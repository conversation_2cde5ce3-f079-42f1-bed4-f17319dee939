import * as std from "std";

export const consts = {
  COMPOSE_DIR: pow.baseDir + "/local-env",
  COMPOSE_BACKEND: pow.baseDir + "/local-env/backend.docker-compose.yml",
  COMPOSE_PAY: pow.baseDir + "/../pay/docker-compose.yml",
  COMPOSE_FRONTEND:
    pow.baseDir + "/local-env/frontend/frontend.docker-compose.yml",
  MANAGE_PY_CMD: [
    "exec",
    "app",
    "sudo",
    "-EHu",
    "app",
    "python",
    "/app/manage.py",
  ],
  MANAGE_PY_NEW_PAY_CMD: [
    "exec",
    "standalone-payapp",
    "sudo",
    "-EHu",
    "app",
    "/opt/app/manage.py",
  ],
};

export function hasBackendDeps() {
  const dockerArgs = [
    ["--version", "You need to install Docker"],
    ["compose", "You need to install Compose plugin for Dock<PERSON>"],
  ];
  let ok = true;
  for (const [arg, msg] of dockerArgs) {
    const cp = pow.spawnSync("docker", [arg], { stdio: "ignore" });
    if (cp.status !== 0) {
      pow.ERROR(msg);
      ok = false;
    }
  }
  return ok;
}

export function hasFrontendDeps() {
  const npmCmd = pow.windows ? "sudo npm" : "npm";
  const deps = [
    ["npm --version", "You need to install Node.js (and npm)"],
    ["yarn --version", `You need to install yarn. Try ${npmCmd} install -g yarn`],
    ["lefthook version", `You need to install lefthook. Try ${npmCmd} install -g lefthook`],
  ];
  let ok = true;
  for (const [name, msg] of deps) {
    if (
      pow.spawnSync(name, [], {
        shell: true,
        stdio: "ignore",
      }).status !== 0
    ) {
      pow.ERROR(msg);
      ok = false;
    }
  }
  return ok;
}

function runCompose(composeFile, args, extraEnv, extraSpawnOpts) {
  if (!hasBackendDeps()) {
    return 1;
  }

  const env = std.getenviron();
  env["COMPOSE_DOCKER_CLI_BUILD"] = "1";
  env["DOCKER_BUILDKIT"] = "1";
  if (extraEnv) {
    for (const key in extraEnv) {
      env[key] = extraEnv[key];
    }
  }
  const envFile = consts.COMPOSE_DIR + "/.env";
  if (pow.fileExists(envFile)) {
    args = [`--env-file=${envFile}`, ...args];
  }
  const opts = _.defaults(extraSpawnOpts || {}, {
    cwd: consts.COMPOSE_DIR,
    env: env,
    stdio: "inherit",
  });
  const cp = pow.spawnSync(
    "docker",
    ["compose", "-f", composeFile, ...args],
    opts
  );
  return cp.status;
}

function printPayNotFoundMsg(compose_file) {
  let msg_no_pay = "\nAyayay, there's no Pay!\n"
    + "Please clone the Pay repository next to the shortlist-platform repo.\n"
    + "You can find it here: https://github.com/ShortlistProject/pay \n"
    + "More info: https://shortlistco.atlassian.net/wiki/spaces/ENG/pages/2551054442/Local+Environments#Checking-out-the-pay-module\n"
  pow.ERROR('\x1b[1;31m' + msg_no_pay + '\x1b[0m\n')
  pow.ERROR('\x1b[1;31m Details: ' + compose_file + " not found" + '\x1b[0m\n');
}

export function runComposeBackend(args, extraEnv, extraSpawnOpts) {
  return runCompose(consts.COMPOSE_BACKEND, args, extraEnv, extraSpawnOpts);
}

export function runComposePay(args, extraEnv, extraSpawnOpts) {
  let compose_file = consts.COMPOSE_PAY
  if (!pow.fileExists(compose_file)) {
    printPayNotFoundMsg(compose_file);
    return
  }
  return runCompose(compose_file, args, extraEnv, extraSpawnOpts);
}

/**
 * Extracts flags and files to validate from stdin or locally changed files.
 * Defaults to changed files (git unstaged files) if no files are passed.
 */
export const extractFilesToValidate = (argv) => {
  const flags = argv.filter(arg => arg.startsWith('-'));
  const files = argv.filter(arg => !arg.startsWith('-'));

  if (files.length !== 0) {
    return {
      flags,
      files,
    }
  }

  const changedFilesCmd = pow.spawnSync(
    "{ git diff --name-only; git diff --cached --name-only; } | tr -d '\\r' | sort -u | grep '^pipeline\/.*\\.py$'",
    [], {
      shell: true,
      stdio: "pipe",
      encoding: "utf-8",
  })
  const changedFiles = changedFilesCmd
      .stdout
      .toString()
      .trim()
      .replaceAll("pipeline/", "")
      .split("\n")
      .filter(Boolean);

  return {
    flags,
    files: changedFiles,
  };
}
