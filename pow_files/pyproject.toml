[tool.ruff]
lint.extend-select = ["E501"]  # E501: enable line length violations check
lint.extend-ignore = ["B006"]  # B006: skip warnings about dict.get() without default value
target-version = "py312"
line-length = 140
indent-width = 4
lint.select = ["E", "F", "UP", "N", "B", "A", "C4", "SIM", "ERA"]

# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "**/migrations/**",
]

[tool.ruff.format]
quote-style = "preserve"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[tool.black]
line-length = 140
extend-exclude = "migrations"
skip-string-normalization = true
