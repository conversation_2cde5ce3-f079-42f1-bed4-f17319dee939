import os
import re
import subprocess
import sys
from pathlib import Path


class Consts:
    POW_DIR = os.path.dirname(__file__)
    PROJECT_DIR = os.path.dirname(POW_DIR)

    FRONTEND_DIR = os.path.join(PROJECT_DIR, "pipeline", "static")
    COMPOSE_DIR = os.path.join(PROJECT_DIR, "local-env")
    PAY_DIR = os.path.join(PROJECT_DIR, '..', 'pay')
    COMPLIANCE_DIR = os.path.join(PROJECT_DIR, '..', 'compliance')
    COMPOSE_COMPLIANCE = os.path.join(COMPLIANCE_DIR, 'pow', 'docker-compose.yml')

    COMPOSE_BACKEND = os.path.join(COMPOSE_DIR, "backend.docker-compose.yml")
    COMPOSE_FRONTEND = os.path.join(COMPOSE_DIR, "frontend", "frontend.docker-compose.yml")
    COMPOSE_SSLPROXY = os.path.join(COMPOSE_DIR, "sslproxy", "sslproxy.docker-compose.yml")
    COMPOSE_PAY = os.path.join(PAY_DIR, "docker-compose.yml")

    REQUIREMENTS_SOURCE_DIR = os.path.join(PROJECT_DIR, "deploy", "requirements")
    REQUIREMENTS_TARGET_DIR = os.path.join(COMPOSE_DIR, "app", "context", "requirements")

    WINDOWS = os.name == "nt"

    # Some commands on Windows need the .cmd/.bat suffix
    YARN_CMD = "yarn.cmd" if WINDOWS else "yarn"

    SSH_ADD_CMD = "C:\\Windows\\System32\\OpenSSH\\ssh-add.exe" if WINDOWS else "ssh-add"

    MANAGE_PY_CMD = ["exec", "app", "sudo", "-EHu", "app", "python", "/app/manage.py"]
    MANAGE_PY_CMD_PAY = ["exec", "standalone-payapp", "sudo", "-EHu", "app", "python", "/app/manage.py"]


class PowPluginShortlist:
    const = Consts()

    @classmethod
    def check_command_exists(cls, cmd):
        try:
            subprocess.run(cmd, capture_output=True, check=True)
        except (OSError, subprocess.CalledProcessError):
            return False
        return True

    @classmethod
    def run_compose(cls, compose_file, args, extra_env=None, **kwargs):
        """Run docker-compose in a new process and return CompletedProcess once it finishes"""
        env = os.environ.copy()
        env["COMPOSE_DOCKER_CLI_BUILD"] = "1"
        env["DOCKER_BUILDKIT"] = "1"
        if extra_env:
            env.update(extra_env)
        env_file = Path(Consts.COMPOSE_DIR) / ".env"
        if env_file.exists():
            args = [f"--env-file={env_file}"] + args

        return subprocess.run(
            ["docker", "compose", "-f", compose_file] + args,
            cwd=Consts.COMPOSE_DIR,
            env=env,
            **kwargs,
        )

    @classmethod
    def run_compose_up(cls, compose_file, args, **kwargs):
        try:
            cls.run_compose(compose_file, ["up"] + args, **kwargs)
        except KeyboardInterrupt:
            sys.stdout.close()
            sys.stderr.close()

    @classmethod
    def verify_env_file(cls):
        docs_pointer = " Consult the local environment docs on Confluence or #engineering-noobs on Slack"
        env_file = Path(Consts.COMPOSE_DIR) / ".env"
        if not env_file.exists():
            raise Exception(f"Env file ({env_file}) not found." + docs_pointer)

        env_contents = env_file.read_text()

        for char_name, forbidden_char in [
            ("space", " "),
            ("quote", '"'),
            ("apostrophee", "'"),
        ]:
            if forbidden_char in env_contents:
                raise Exception(f"Env file ({env_file}) has an unexpected character ({char_name})." + docs_pointer)

        env_lines = env_contents.strip().split("\n")
        defined_keys = [line.split("=")[0] for line in env_lines]

        if "PUBLIC_SCHEMA_DOMAIN" not in defined_keys:
            raise Exception(f"Env file ({env_file}) doesn't define PUBLIC_SCHEMA_DOMAIN." + docs_pointer)

        for recommended_key, missing_feature in [
            ("SHORTLIST_PAY_ADMIN_API_TOKEN", "Shortlist Pay"),
        ]:
            if recommended_key not in defined_keys:
                print(f"Warning: Env file ({env_file}) doesn't define {recommended_key}. {missing_feature} won't work." + docs_pointer)

    @classmethod
    def verify_has_docker_compose(cls):
        for cmd in ["docker", "--version"], ["docker", "compose", "version"]:
            if not cls.check_command_exists(cmd):
                raise Exception("You need to install Docker and Docker Compose")

        # Now check Docker version:
        cp = subprocess.run(["docker", "--version"], capture_output=True, check=True)
        m = re.match(r".*version\s(([0-9]+\.){2,}[0-9]+),", cp.stdout.decode())
        if not m:
            raise Exception("Failed to determine Docker version (required is 20.10.15+)")
        version = m.group(1)
        parts = list(map(int, version.split(".")))
        major = parts[0]
        minor = parts[1]
        patch = parts[2]
        if major < 20:
            raise Exception(f"Docker major version insufficient {version} (required is 20.10.15+)")
        if major == 20 and minor < 10:
            raise Exception(f"Docker minor version insufficient {version} (required is 20.10.15+)")
        if major == 20 and minor == 10 and patch < 15:
            raise Exception(f"Docker patch version insufficient {version} (required is 20.10.15+)")

    @classmethod
    def verify_has_frontend_deps(cls):
        npm_cmd = "npm" if Consts.WINDOWS else "sudo npm"

        for name, cmd, source in [
            ("yarn", [Consts.YARN_CMD, "--version"], "yarn"),
        ]:
            if not cls.check_command_exists(cmd):
                raise Exception(f"You need {name} installed! Try {npm_cmd} install -g {source}")

    @classmethod
    def try_adding_ssh_keys_on_windows(cls):
        if not Consts.WINDOWS:
            return
        try:
            cp = subprocess.run([Consts.SSH_ADD_CMD, "-l"], capture_output=True)
            if cp.returncode == 1:
                # Add all keys from ~/.ssh to the agent
                subprocess.run([Consts.SSH_ADD_CMD])
        except OSError:
            pass

    @classmethod
    def verify_ssh_agent(cls):
        extra_help = ". Try running: pow setup-ssh-agent" if Consts.WINDOWS else ""

        try:
            cp = subprocess.run([Consts.SSH_ADD_CMD, "-l"], capture_output=True)
        except OSError:
            raise Exception("You need to install ssh-agent")

        if cp.returncode == 2:
            if Consts.WINDOWS:
                raise Exception("Fatal error: ssh-agent not running. Please run: pow setup-ssh-agent")
            else:
                raise Exception("Fatal error: ssh-agent not running. Please set up your ssh-agent to start automatically when logging in")

        if cp.returncode == 1:
            # TODO: Explain to the user that they need to authenticate to GitHub via SSH
            extra_help = extra_help or ". To add your ssh keys, run: ssh-add"
            raise Exception(f"No keys added to ssh-agent{extra_help}")

        if cp.returncode != 0:
            raise Exception(f"ssh-add exited abnormally. Exit code was: {cp.returncode}{extra_help}")

    @classmethod
    def set_node_options(cls):
        cp = subprocess.run(["node", "-v"], capture_output=True, check=True)
        m = re.match(r"v(\d+)", cp.stdout.decode())
        if not m:
            raise Exception("Failed to determine Node version")
        version = int(m.group(1))
        if version > 16:
            os.environ["NODE_OPTIONS"] = "--openssl-legacy-provider"

    @classmethod
    def run_frontend_command(cls, command: str):
        cls.verify_has_frontend_deps()
        cls.set_node_options()
        subprocess.run([Consts.YARN_CMD, "install"], cwd=Consts.FRONTEND_DIR)
        subprocess.run([Consts.YARN_CMD, command], cwd=Consts.FRONTEND_DIR)

    @classmethod
    def extract_files_to_validate(cls, args: list):
        """
        Extracts flags and files to validate from stdin or locally changed files.
        Defaults to changed files (git unstaged files) if no files are passed.
        """
        flags = [arg for arg in args if arg.startswith('-')]
        files = [arg for arg in args if not arg.startswith('-')]

        if files:
            return flags, files

        changed_files_cmd = subprocess.run(
            "{ git diff --name-only; git diff --cached --name-only; } | tr -d '\\r' | sort -u | grep '^pipeline\/.*\\.py$'",
            shell=True,
            capture_output=True,
            text=True,
        )
        changed_files = changed_files_cmd.stdout.strip().replace("pipeline/", "").split("\n")
        changed_files = list(filter(None, changed_files))

        return flags, changed_files
