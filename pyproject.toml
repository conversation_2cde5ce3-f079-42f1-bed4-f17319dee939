[project]
name = "shortlist-platform"
version = "1.0"
description = "Worksuite platform"
authors = [
    { name = "engineerning", email = "<EMAIL>" },
    { name = "ops", email = "<EMAIL>" },
]
readme = "README.md"
requires-python = ">= 3.11"

[build-system]
requires = ["poetry-core==2.1.2"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
packages = [{ include = "pipeline" }]

[tool.ruff]
lint.extend-select = ["E501"]  # E501: enable line length violations check
lint.extend-ignore = ["B006"]  # B006: skip warnings about dict.get() without default value
target-version = "py312"
line-length = 140
indent-width = 4
lint.select = ["E", "F", "UP", "N", "B", "A", "C4", "SIM", "ERA"]

# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "**/migrations/**",
    "**/oauth2_provider/**"
]

[tool.ruff.format]
quote-style = "preserve"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[tool.black]
line-length = 140
extend-exclude = "migrations"
skip-string-normalization = true

[tool.poetry.dependencies]
python = ">=3.11,<4"
bleach = { version = "5.0.1", extras = ["css"] }
boto3 = "1.33.13"
celery = "5.3.6"
cryptography = "44.0.0"
csv342 = "1.0.1"
dependency-injector = "4.41.0"
django-autoslug = "1.9.9"
django-braces = "1.15.0"
django-extensions = "3.2.3"
django-filter = "23.1"
django-model-utils = "4.3.1"
django-prefetch = "1.2.3"
django-redis = "5.4.0"
django-storages = "1.14.2"
django-timezone-field = "5.1.0"
django-typed-models = "0.14.0"
django-uuslug = "2.0.0"
django = "4.2.20"
djangorestframework-csv = "1.4.1"
djangorestframework = "3.16.0"
djangorestframework-yaml = "2.0.0"
djangosaml2 = "1.5.6"
drf-spectacular = "0.27.1"
elasticsearch = "6.8.2"
email-reply-parser = "0.5.12"
factory-boy = "3.2.1"
faker = "18.13.0"
flower = "2.0.0"
freezegun = "1.4.0"
gevent = "24.10.3"
googlemaps = "4.10.0"
hellosign-python-sdk = "4.0.0"
html-sanitizer = "1.9.3"
idna = "3.7"
importlib-metadata = "4.13.0"
inflection = "0.5.1"
inscriptis = "2.3.2"
json-rpc = "1.15.0"
kombu = "5.3.6"
lxml = "4.9.4"
mock = "4.0.3"
oauthlib = { extras = ["signedtoken"], version = "^3.2.2" }
parameterized = "0.9.0"
paramiko = "3.3.2"
pgeocode = "0.3.0"
psycogreen = "1.0.2"
psycopg2-binary = "2.9.10"
pyasn1-modules = "0.2.8"
pyasn1 = "0.4.8"
pycountry = "22.3.5"
pycryptodomex = "3.20.0"
pydantic = "2.6.4"
pyjwt = "2.8.0"
pylibmc = "1.6.3"
pymemcache = "4.0.0"
pyopenssl = "24.3.0"
pysftp = "0.2.9"
python-dateutil = "2.8.2"
python-memcached = "1.59"
python-slugify = "6.1.2"
pytimeparse = "1.1.8"
pyyaml = "6.0.1"
redis = "4.5.4"
reportlab = "4.2.5"
requests = "2.31.0"
requests-oauthlib = "1.3.1"
responses = "0.23.3"
rest-condition = "1.0.3"
sentry-sdk = "1.39.2"
slack-sdk = "3.26.2"
strenum = "0.4.15"
tblib = "2.0.0"
ten99policy = "1.1.3"
textparser = "0.24.0"
text-unidecode = "^1.3"
urllib3 = "1.26.18"
us = "3.1.1"
xhtml2pdf = "0.2.16"
setuptools = "66.1.1"
gunicorn = "23"
tornado = "6.4"
asgiref = "3.7.2"
attrs = "23.2.0"
beautifulsoup4 = "4.12.3"
certifi = "2023.11.17"
importlib-resources = "5.10.2"
packaging = "23.2"
pillow = "10.3.0"
pytz = "2023.3.0"
soupsieve = "2.4.1"
sqlparse = "0.4.4"
typing-extensions = "4.7.1"
tzdata = "2024.1"
zipp = "3.15.0"
zope-interface = "6.1.0"
requests-toolbelt = "1.0.0"
mergepythonclient = "1.0.7"
django-otp = "1.3.0"
sendgrid = "6.11.0"
mockito = "^1.5.0"
numpy = "1.26.4"
our-common = { git = "**************:ShortlistProject/our-common.git" }
h11 = "0.16.0"  # lib required indirectly by sentry-sdk and mergepythonclient, upgrading to get rid of critial vulnerability

[tool.poetry.group.dev.dependencies]
django-debug-toolbar = "^3.8.1"
curlify = "^2.2.1"
django-rest-swagger = "^2.2.0"
django-nose = "^1.4.7"
nose-timer = "^1.0.1"
watchdog = { version = "2.2.1", extras = ["watchmedo"] }
black = "^24.10.0"
colorlog = "6.7.0"
markupsafe = "2.1.3"
pathspec = "0.11.0"
platformdirs = "3.0.0"
simplejson = "3.18.3"
pydevd-pycharm = "243.23654.74"
ruff = "^0.9.6"
yamllint = "^1.35.1"

[tool.poetry.requires-plugins]
poetry-plugin-export = ">=1.9"
poetry = "2.1.2"
