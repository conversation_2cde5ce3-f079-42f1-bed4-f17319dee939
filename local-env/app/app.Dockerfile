FROM python:3.11.10-slim-bookworm

# Increment the number below to invalidate docker image cache:
RUN true 1

WORKDIR /app

# Application dependencies:
RUN apt-get update && apt-get install -y curl postgresql-client zip xmlsec1 libpq-dev build-essential git

# Dependencies specific to local environment:
RUN apt-get update && apt-get install -y openssh-server sudo

RUN mkdir -p -m 0600 ~/.ssh && ssh-keyscan github.com >> ~/.ssh/known_hosts

# Install ipython for better Django shell and
# supervisor to manage our Python processes:
RUN pip install ipython supervisor

# Replace the Debian Python:
RUN ln -sf /usr/local/bin/python /usr/bin/python

# Install requirements
COPY context/requirements/ /deploy/requirements/
RUN --mount=type=ssh pip install -r /deploy/requirements/dev.txt
