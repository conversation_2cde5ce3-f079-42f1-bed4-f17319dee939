#!/bin/bash

get_env() {
  env | grep -v ^PWD= | grep -v ^HOME= | sort
}

(
echo "Starting sshd"
mkdir -p /run/sshd
echo PermitUserEnvironment yes >> /etc/ssh/sshd_config
/usr/sbin/sshd

echo "Generating keys of needed"
mkdir -p /ssh-key
if ! [ -e /ssh-key/waffle.pub -a -e /ssh-key/waffle ]; then
  rm -f /ssh-key/waffle.pub /ssh-key/waffle
  ssh-keygen -f /ssh-key/waffle -N ''
fi

echo "Configuring user for ssh access"
install -o app -g app -m 0700 -d /home/<USER>/.ssh
install -o app -g app -m 0600 <(get_env) /home/<USER>/.ssh/environment
install -o app -g app -m 0600 /ssh-key/waffle.pub /home/<USER>/.ssh/authorized_keys
chpasswd <<< app:waffle

echo "Configuring root user for ssh access"
install -o root -g root -m 0700 -d /root/.ssh
install -o root -g root -m 0600 <(get_env) /root/.ssh/environment
install -o root -g root -m 0600 /ssh-key/waffle.pub /root/.ssh/authorized_keys

echo "Verify ssh server is up"
for i in {1..10}; do
  if curl localhost:22 2>/dev/null | grep SSH; then
    ssh_up=yes
    break
  else
    sleep 1
  fi
done

if [ -z "$ssh_up" ]; then
  echo "SSH server is not up"
  exit 1
fi

echo "SSH up. Connection details:

user:  app         password:  waffle
host:  localhost   port:      19323

"
) </dev/null 1>&2

cat /ssh-key/waffle
