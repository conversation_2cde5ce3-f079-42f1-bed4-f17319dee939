#!/bin/bash -xe

env=${1:-"production"}

# Build backend
. venv/bin/activate
cd pipeline
./manage.py clean_pyc --settings=shortlist.settings.deployer --path .
./manage.py collectstatic --no-post-process --noinput --settings=shortlist.settings.deployer
deactivate
cd ..

# Copy backend to distribution
rm -rf dist/src
mkdir -p dist/src
cp -R {pipeline,deploy} dist/src/

# Build frontend
cd dist/src/pipeline/static
npm run-script build -- $env
cd ../../../../

# Prepare archive
rm -rf dist/src/pipeline/static/node_modules # Not needed to run
zip -q -r "shortlist-platform-${env}.zip" dist/
