#!/usr/bin/env python3
"""
Test script to verify that change request notifications are only sent to vendors
when external fields are changed, not when only internal fields are changed.
"""

import os
import sys
import django
from unittest.mock import patch

# Add the pipeline directory to the Python path
sys.path.insert(0, '/mnt/d/code/shortlist/shortlist-platform/pipeline')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shortlist.settings')
django.setup()

from contracts.amendments.application import create_contract_amendment
from contracts.tests.factories import ContractFactory, contract_amendment_data_factory
from contracts.revisions.services import initiate_contract_revision
from events.event_types import AcceptContract
from notifications.tests.test_email_notifications import mock_send_mail
from shortlist.tests.helpers import TenantTestCase
from users.tests.factories import UserFactory


def test_external_field_change_sends_notification():
    """Test that changing external fields sends notification to vendor"""
    print("Testing external field change...")
    
    # Create a live contract
    contract = ContractFactory(live=True)
    user = UserFactory()
    
    # Create amendment with external field change (description is external)
    revision_changes = {'description': 'New description - external field'}
    
    with mock_send_mail() as send_mail_mock:
        contract_amendment = create_contract_amendment(
            contract, 
            contract_amendment_data_factory(contract, revision_changes), 
            user
        )
        
        # Initiate the revision (this triggers the notification logic)
        initiate_contract_revision(contract_amendment.contract_revision)
        
        # Check if vendor notification was sent
        emails = send_mail_mock.emails_for_event(AcceptContract)
        
    print(f"External field change - Emails sent: {len(emails)}")
    if len(emails) > 0:
        print("✓ PASS: Vendor notification sent for external field change")
        return True
    else:
        print("✗ FAIL: No vendor notification sent for external field change")
        return False


def test_internal_field_change_no_notification():
    """Test that changing only internal fields does NOT send notification to vendor"""
    print("Testing internal field change...")
    
    # Create a live contract
    contract = ContractFactory(live=True)
    user = UserFactory()
    
    # Create amendment with only internal field change (name is internal)
    revision_changes = {'name': 'New contract name - internal field'}
    
    with mock_send_mail() as send_mail_mock:
        contract_amendment = create_contract_amendment(
            contract, 
            contract_amendment_data_factory(contract, revision_changes), 
            user
        )
        
        # Initiate the revision (this triggers the notification logic)
        initiate_contract_revision(contract_amendment.contract_revision)
        
        # Check if vendor notification was sent
        emails = send_mail_mock.emails_for_event(AcceptContract)
        
    print(f"Internal field change - Emails sent: {len(emails)}")
    if len(emails) == 0:
        print("✓ PASS: No vendor notification sent for internal field change")
        return True
    else:
        print("✗ FAIL: Vendor notification sent for internal field change (should not happen)")
        return False


def test_mixed_field_change_sends_notification():
    """Test that changing both external and internal fields sends notification to vendor"""
    print("Testing mixed field change...")
    
    # Create a live contract
    contract = ContractFactory(live=True)
    user = UserFactory()
    
    # Create amendment with both external and internal field changes
    revision_changes = {
        'description': 'New description - external field',
        'name': 'New contract name - internal field'
    }
    
    with mock_send_mail() as send_mail_mock:
        contract_amendment = create_contract_amendment(
            contract, 
            contract_amendment_data_factory(contract, revision_changes), 
            user
        )
        
        # Initiate the revision (this triggers the notification logic)
        initiate_contract_revision(contract_amendment.contract_revision)
        
        # Check if vendor notification was sent
        emails = send_mail_mock.emails_for_event(AcceptContract)
        
    print(f"Mixed field change - Emails sent: {len(emails)}")
    if len(emails) > 0:
        print("✓ PASS: Vendor notification sent for mixed field change")
        return True
    else:
        print("✗ FAIL: No vendor notification sent for mixed field change")
        return False


def main():
    """Run all tests"""
    print("Testing change request notification logic...")
    print("=" * 50)
    
    # Set up test environment
    from django.test.utils import setup_test_environment, teardown_test_environment
    from django.db import connection
    from django.core.management.color import no_style
    
    setup_test_environment()
    
    try:
        # Run tests
        test1_pass = test_external_field_change_sends_notification()
        print()
        test2_pass = test_internal_field_change_no_notification()
        print()
        test3_pass = test_mixed_field_change_sends_notification()
        
        print()
        print("=" * 50)
        print("Test Results:")
        print(f"External field change: {'PASS' if test1_pass else 'FAIL'}")
        print(f"Internal field change: {'PASS' if test2_pass else 'FAIL'}")
        print(f"Mixed field change: {'PASS' if test3_pass else 'FAIL'}")
        
        if all([test1_pass, test2_pass, test3_pass]):
            print("\n🎉 All tests PASSED! The fix is working correctly.")
            return 0
        else:
            print("\n❌ Some tests FAILED. The fix needs more work.")
            return 1
            
    finally:
        teardown_test_environment()


if __name__ == '__main__':
    sys.exit(main())
