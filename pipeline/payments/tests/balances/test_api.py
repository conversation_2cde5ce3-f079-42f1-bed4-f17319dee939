from datetime import timedelta
from decimal import Decimal

from django.utils import timezone
from django.utils.timezone import now
from freezegun import freeze_time
from parameterized import parameterized

from clients.features import BANK_DETAILS_MC_FORMAT
from documents.models import Payment
from documents.taxes import round_decimal
from events.event_types import FundingStatementReadyEmailTrigger
from payments.balances.balance_service import (
    calculate_remaining_balance,
    fetch_account_balances_and_calculate,
)
from payments.balances.config import BUFFER_FACTOR
from payments.balances.exchange_rate_service import (
    convert_currency,
    fetch_exchange_rates,
)
from payments.common.model import PaymentsSystemActor
from payments.invoices.actions.model import ActionRequest, InvoiceAction
from payments.invoices.model import InvoiceStatus
from payments.invoices.search.model import SearchQuery
from payments.models import PaymentsConfig
from payments.payments_config.model import FundingMethod
from payments.payments_service import bulk_action
from payments.payruns.payruns_service import scheduled_payruns
from payments.tests.balances.common import prepare_payments, prepare_vendor_with_payout_method_mc
from payments.tests.factories.base import UserTypes
from payments.tests.factories.django import factory_user
from payments.tests.mixins import WithFakePay
from shortlist.tests.helpers import TenantTestCase, features_enabled


class BalancesAPITestCase(WithFakePay, TenantTestCase):
    with_staff_user = True
    api_url = "/api/payments/balances/"
    statement_url = "/api/payments/balances/request_funding_statement/"
    download_statement_url = "/api/payments/balances/download_funding_statement/"

    def setUp(self):
        super().setUp()
        PaymentsConfig.objects.create(funding_method=FundingMethod.CUSTOMER_TRANSFER)

    def _fetch_data(self):
        fetch_exchange_rates()
        fetch_account_balances_and_calculate()

    def _check_response(self, response, scheduled_payments=None, processing_payments=None):
        if not scheduled_payments:
            scheduled_payments = []
        if not processing_payments:
            processing_payments = []
        self.assertEqual(response.status_code, 200)
        response_content = response.json()
        balance_summary = response_content[0]
        account_balance_for_processor = balance_summary["account_balances_for_processor"][0]
        account_balance = account_balance_for_processor["account_balances"][0]
        self.assertEqual(account_balance["scheduled_amount"], sum([p.total_amount for p in scheduled_payments]))
        self.assertEqual(account_balance["processing_amount"], sum([p.total_amount for p in processing_payments]))
        self.assertEqual(
            account_balance["remaining_balance"] + account_balance["scheduled_amount"] + account_balance["processing_amount"],
            account_balance["amount"],
        )

    def _prepare_vendor_with_payout_method_mc(self, payments_processor=None, bank_account_currency="USD"):
        return prepare_vendor_with_payout_method_mc(
            payments_processor=payments_processor or self.mc_processor,
            bank_account_currency=bank_account_currency,
        )

    def test_no_feature_flags(self):
        response = self.api_client.get(self.api_url)
        self.assertEqual(response.status_code, 403)

    @parameterized.expand(
        [
            FundingMethod.PRFA,
            FundingMethod.PULLING,
        ]
    )
    def test_no_funding_method_set_to_customer_transfer(self, funding_method):
        PaymentsConfig.objects.update(funding_method=funding_method)
        response = self.api_client.get(self.api_url)
        self.assertEqual(response.status_code, 403)

    def test_no_payments_config_created(self):
        PaymentsConfig.objects.all().delete()
        response = self.api_client.get(self.api_url)
        self.assertEqual(response.status_code, 403)

    @features_enabled([BANK_DETAILS_MC_FORMAT])
    def test_multiple_payments_configs(self):
        """
        Only newest PaymentsConfig should be used
        """

        # Create second PaymentsConfig with Customer Transfer
        PaymentsConfig.objects.create(funding_method=FundingMethod.CUSTOMER_TRANSFER)
        response = self.api_client.get(self.api_url)
        self.assertEqual(response.status_code, 200)

        # Create third PaymentsConfig but with other Funding Method - expecting PermissionDenied
        PaymentsConfig.objects.create(funding_method=FundingMethod.PULLING)
        response = self.api_client.get(self.api_url)
        self.assertEqual(response.status_code, 403)

        # Create fouth PaymentsConfig with Customer Transfer again - expecting OK
        PaymentsConfig.objects.create(funding_method=FundingMethod.CUSTOMER_TRANSFER)
        response = self.api_client.get(self.api_url)
        self.assertEqual(response.status_code, 200)

    @features_enabled([BANK_DETAILS_MC_FORMAT])
    def test_ok_no_payments(self):
        self._fetch_data()
        response = self.api_client.get(self.api_url)
        self.assertEqual(response.status_code, 200)
        response_content = response.json()
        self.assertTrue(isinstance(response_content, list))
        balance_summary = response_content[0]
        self.assertEqual(balance_summary["processor_name"], "Bank transfer")
        self.assertTrue(isinstance(balance_summary["account_balances_for_processor"], list))
        self.assertEqual(balance_summary["account_balances_for_processor"], [])

    @features_enabled([BANK_DETAILS_MC_FORMAT])
    def test_ok_with_payments_scheduled_for_next_payrun(self):
        self._fetch_data()
        vendor = self._prepare_vendor_with_payout_method_mc()
        nearest_payrun = scheduled_payruns()[0]
        nearest_payrun_date = nearest_payrun.date
        payments = prepare_payments(3, vendor, scheduled_for=nearest_payrun_date)
        calculate_remaining_balance()
        response = self.api_client.get(self.api_url)
        self._check_response(response, payments)

    @features_enabled([BANK_DETAILS_MC_FORMAT])
    def test_ok_with_payments_scheduled_for_two_next_payruns(self):
        self._fetch_data()
        vendor = self._prepare_vendor_with_payout_method_mc()
        payruns = scheduled_payruns()
        nearest_payrun = payruns[0]
        nearest_payrun_date = nearest_payrun.date
        payments_payrun_1 = prepare_payments(3, vendor, scheduled_for=nearest_payrun_date)
        next_one_payrun = payruns[1]
        next_one_payrun_date = next_one_payrun.date
        prepare_payments(6, vendor, scheduled_for=next_one_payrun_date)
        calculate_remaining_balance()
        response = self.api_client.get(self.api_url)
        self._check_response(response, payments_payrun_1)

    @features_enabled([BANK_DETAILS_MC_FORMAT])
    def test_ok_with_payments_scheduled_for_next_payrun_and_offcycle_before_that(self):
        self._fetch_data()
        vendor = self._prepare_vendor_with_payout_method_mc()
        payruns = scheduled_payruns()
        nearest_payrun = payruns[0]
        nearest_payrun_date = nearest_payrun.date
        payments_payrun_1 = prepare_payments(3, vendor, scheduled_for=nearest_payrun_date)
        prepare_payments(6, vendor, scheduled_for=timezone.now())
        calculate_remaining_balance()
        response = self.api_client.get(self.api_url)
        self._check_response(response, payments_payrun_1)

    @features_enabled([BANK_DETAILS_MC_FORMAT])
    def test_ok_with_payments_scheduled_for_next_payrun_and_not_scheduled(self):
        self._fetch_data()
        vendor = self._prepare_vendor_with_payout_method_mc()
        nearest_payrun = scheduled_payruns()[0]
        nearest_payrun_date = nearest_payrun.date
        payments = prepare_payments(3, vendor, scheduled_for=nearest_payrun_date)
        prepare_payments(6, vendor, status=Payment.status_code(Payment.STATUS_NEW))
        calculate_remaining_balance()
        response = self.api_client.get(self.api_url)
        self._check_response(response, payments)

    @features_enabled([BANK_DETAILS_MC_FORMAT])
    def test_ok_with_payments_with_different_currencies_scheduled_for_next_payrun(self):
        self._fetch_data()
        vendor = self._prepare_vendor_with_payout_method_mc()
        nearest_payrun = scheduled_payruns()[0]
        nearest_payrun_date = nearest_payrun.date
        payments_usd = prepare_payments(3, vendor, scheduled_for=nearest_payrun_date)
        payments_eur = prepare_payments(3, vendor, currency="EUR", scheduled_for=nearest_payrun_date)
        calculate_remaining_balance()
        response = self.api_client.get(self.api_url)
        self.assertEqual(response.status_code, 200)
        response_content = response.json()
        balance_summary = response_content[0]
        account_balance_for_processor = balance_summary["account_balances_for_processor"][0]
        account_balance = account_balance_for_processor["account_balances"][0]
        scheduled_amount_in_usd = sum([p.total_amount for p in payments_usd])
        scheduled_amount_in_eur = sum([convert_currency("USD", "EUR", p.total_amount) * BUFFER_FACTOR for p in payments_eur])
        self.assertEqual(
            round_decimal(Decimal(account_balance["scheduled_amount"]), 2),
            round_decimal(Decimal(scheduled_amount_in_usd + scheduled_amount_in_eur), 2),
        )
        self.assertEqual(
            round_decimal(Decimal(account_balance["remaining_balance"] + account_balance["scheduled_amount"]), 2),
            round_decimal(Decimal(account_balance["amount"]), 2),
        )
        self.assertFalse(account_balance["is_scheduled_currency_match"])

    @features_enabled([BANK_DETAILS_MC_FORMAT])
    def test_ok_with_payments_for_vendors_with_different_currencies_scheduled_for_next_payrun(self):
        self._fetch_data()
        vendor_usd = self._prepare_vendor_with_payout_method_mc()
        vendor_eur = self._prepare_vendor_with_payout_method_mc(bank_account_currency="EUR")
        nearest_payrun = scheduled_payruns()[0]
        nearest_payrun_date = nearest_payrun.date
        payments_vendor_usd = prepare_payments(3, vendor_usd, scheduled_for=nearest_payrun_date)
        payments_vendor_eur = prepare_payments(3, vendor_eur, scheduled_for=nearest_payrun_date)
        calculate_remaining_balance()
        response = self.api_client.get(self.api_url)
        self.assertEqual(response.status_code, 200)
        response_content = response.json()
        balance_summary = response_content[0]
        account_balance_for_processor = balance_summary["account_balances_for_processor"][0]
        account_balance = account_balance_for_processor["account_balances"][0]
        scheduled_amount_for_usd_vendor = sum([p.total_amount for p in payments_vendor_usd])
        scheduled_amount_for_eur_vendor = sum([p.total_amount for p in payments_vendor_eur])
        self.assertEqual(
            round_decimal(Decimal(account_balance["scheduled_amount"]), 2),
            round_decimal(Decimal(scheduled_amount_for_usd_vendor + scheduled_amount_for_eur_vendor), 2),
        )
        self.assertEqual(
            round_decimal(Decimal(account_balance["remaining_balance"] + account_balance["scheduled_amount"]), 2),
            round_decimal(Decimal(account_balance["amount"]), 2),
        )
        # Vendor's bank account currency doesn't affect the currency match
        self.assertTrue(account_balance["is_scheduled_currency_match"])

    @features_enabled([BANK_DETAILS_MC_FORMAT])
    def test_ok_updated_data_after_payrun_date(self):
        self._fetch_data()
        with freeze_time("2023-01-09 14:00:00") as frozen_time:
            vendor = self._prepare_vendor_with_payout_method_mc()
            payruns = scheduled_payruns()
            nearest_payrun = payruns[0]
            third_payrun = payruns[2]
            nearest_payrun_date = nearest_payrun.date
            third_payrun_date = third_payrun.date
            payments_first_payrun = prepare_payments(3, vendor, scheduled_for=nearest_payrun_date)
            payments_third_payrun = prepare_payments(3, vendor, scheduled_for=third_payrun_date)
            calculate_remaining_balance()
            response = self.api_client.get(self.api_url)
            self._check_response(response, payments_first_payrun)
            frozen_time.tick(timedelta(days=7))
            fetch_account_balances_and_calculate()
            response = self.api_client.get(self.api_url)
            self._check_response(response, [])
            frozen_time.tick(timedelta(days=7))
            fetch_account_balances_and_calculate()
            response = self.api_client.get(self.api_url)
            self._check_response(response, payments_third_payrun)

    @features_enabled([BANK_DETAILS_MC_FORMAT])
    def test_calculating_after_payment_status_change(self):
        self._fetch_data()
        vendor = self._prepare_vendor_with_payout_method_mc()
        payruns = scheduled_payruns()
        nearest_payrun = payruns[0]
        payments = prepare_payments(3, vendor, status=Payment.STATUS_APPROVED)
        response = self.api_client.get(self.api_url)
        self._check_response(response, [])
        search_query = SearchQuery(filters=[{"status": InvoiceStatus.APPROVED}], size=3)
        with self.captureOnCommitCallbacks(execute=True):
            bulk_action(
                PaymentsSystemActor(),
                search_query,
                ActionRequest(action=InvoiceAction.SCHEDULE, params={"schedule_for": nearest_payrun.date}),
            )
        response = self.api_client.get(self.api_url)
        self._check_response(response, payments)

        search_query = SearchQuery(filters=[{"status": InvoiceStatus.SCHEDULED}], size=3)
        with self.captureOnCommitCallbacks(execute=True):
            bulk_action(
                PaymentsSystemActor(),
                search_query,
                ActionRequest(action=InvoiceAction.UNSCHEDULE),
            )
        response = self.api_client.get(self.api_url)
        self._check_response(response, [])

    @features_enabled([BANK_DETAILS_MC_FORMAT])
    def test_currency_match(self):
        self._fetch_data()
        vendor = self._prepare_vendor_with_payout_method_mc()
        nearest_payrun = scheduled_payruns()[0]
        nearest_payrun_date = nearest_payrun.date
        prepare_payments(3, vendor, scheduled_for=nearest_payrun_date)
        calculate_remaining_balance()
        response = self.api_client.get(self.api_url)
        self.assertEqual(response.status_code, 200)
        response_content = response.json()
        balance_summary = response_content[0]
        account_balance_for_processor = balance_summary["account_balances_for_processor"][0]
        account_balance = account_balance_for_processor["account_balances"][0]
        self.assertTrue(account_balance["is_scheduled_currency_match"])
        vendor_eur = self._prepare_vendor_with_payout_method_mc(bank_account_currency="EUR")
        prepare_payments(1, vendor_eur, scheduled_for=nearest_payrun_date)
        calculate_remaining_balance()
        response = self.api_client.get(self.api_url)
        response_content = response.json()
        balance_summary = response_content[0]
        account_balance_for_processor = balance_summary["account_balances_for_processor"][0]
        account_balance = account_balance_for_processor["account_balances"][0]
        # Vendor's bank account currency doesn't affect the currency match
        self.assertTrue(account_balance["is_scheduled_currency_match"])

    @parameterized.expand(
        [
            (None, 404),
            (UserTypes.STAFF, 200),
            (UserTypes.BUYER_ADMIN, 200),
            (UserTypes.BUYER_REGULAR_PAYMENTS_FULL, 200),
            (UserTypes.BUYER_REGULAR_DEFAULT, 403),
            (UserTypes.BUYER_REGULAR_PAYMENTS_TEAM_RELATED, 403),
            (UserTypes.BUYER_REGULAR_PAYMENTS_RELATED, 403),
            (UserTypes.BUYER_REGULAR_PAYMENTS_BASIC, 403),
            (UserTypes.BUYER_REGULAR_NO_PAYMENTS, 403),
            (UserTypes.GUEST, 403),
            (UserTypes.STAFFING_SUPPLIER, 403),
            (UserTypes.VENDOR, 403),
            (UserTypes.VENDOR_NOT_SHORTLISTED, 403),
        ]
    )
    @features_enabled([BANK_DETAILS_MC_FORMAT])
    def test_permissions(self, user_type, expected_http_status):
        self._fetch_data()
        user = factory_user(user_type) if user_type else None
        if user:
            self.api_client.force_login(user)
        else:
            self.api_client.logout()

        for url in [self.api_url, self.statement_url]:
            response = self.api_client.get(url)
            self.assertEqual(response.status_code, expected_http_status)

    @parameterized.expand(
        [
            (None, 404),
            (UserTypes.STAFF, 404),  # 404 is expected here, because endpoint let user in, but file is not found
            (UserTypes.BUYER_ADMIN, 404),  # 404 is expected here, because endpoint let user in, but file is not found
            (UserTypes.BUYER_REGULAR_PAYMENTS_FULL, 404),  # 404 is expected here, because endpoint let user in, but file is not found
            (UserTypes.BUYER_REGULAR_DEFAULT, 403),
            (UserTypes.BUYER_REGULAR_PAYMENTS_TEAM_RELATED, 403),
            (UserTypes.BUYER_REGULAR_PAYMENTS_RELATED, 403),
            (UserTypes.BUYER_REGULAR_PAYMENTS_BASIC, 403),
            (UserTypes.BUYER_REGULAR_NO_PAYMENTS, 403),
            (UserTypes.GUEST, 403),
            (UserTypes.STAFFING_SUPPLIER, 403),
            (UserTypes.VENDOR, 403),
            (UserTypes.VENDOR_NOT_SHORTLISTED, 403),
        ]
    )
    @features_enabled([BANK_DETAILS_MC_FORMAT])
    def test_permissions_to_download_statement(self, user_type, expected_http_status):
        self._fetch_data()
        user = factory_user(user_type) if user_type else None
        if user:
            self.api_client.force_login(user)
        else:
            self.api_client.logout()

        response = self.api_client.get(self.download_statement_url)
        self.assertEqual(response.status_code, expected_http_status)

    @features_enabled([BANK_DETAILS_MC_FORMAT])
    def test_ok_with_payments_processing_from_last_payrun_and_scheduled_for_next_payrun(self):
        self._fetch_data()
        vendor = self._prepare_vendor_with_payout_method_mc()
        week_ago = now() - timedelta(days=7)
        payruns = scheduled_payruns(date_start=week_ago)
        last_payrun = payruns[0]
        last_payrun_date = last_payrun.date
        nearest_payrun = payruns[1]
        nearest_payrun_date = nearest_payrun.date
        processing_payments = prepare_payments(3, vendor, status=Payment.STATUS_PROCESSING, scheduled_for=last_payrun_date)
        scheduled_payments = prepare_payments(3, vendor, status=Payment.STATUS_SCHEDULED, scheduled_for=nearest_payrun_date)
        calculate_remaining_balance()
        response = self.api_client.get(self.api_url)
        self._check_response(response, scheduled_payments, processing_payments)

    @features_enabled([BANK_DETAILS_MC_FORMAT])
    def test_ok_with_payments_processing_from_two_last_payruns(self):
        self._fetch_data()
        vendor = self._prepare_vendor_with_payout_method_mc()
        two_weeks_ago = now() - timedelta(days=14)
        payruns = scheduled_payruns(date_start=two_weeks_ago)
        two_payrun_ago = payruns[0]
        two_payrun_ago_date = two_payrun_ago.date
        last_payrun = payruns[1]
        last_payrun_date = last_payrun.date
        prepare_payments(3, vendor, status=Payment.STATUS_PROCESSING, scheduled_for=two_payrun_ago_date)  # old payments
        processing_payments = prepare_payments(3, vendor, status=Payment.STATUS_PROCESSING, scheduled_for=last_payrun_date)
        calculate_remaining_balance()
        response = self.api_client.get(self.api_url)
        self._check_response(response, [], processing_payments)

    @features_enabled([BANK_DETAILS_MC_FORMAT])
    def test_ok_with_payments_with_different_currencies_processing_from_last_payrun(self):
        self._fetch_data()
        vendor = self._prepare_vendor_with_payout_method_mc()
        week_ago = now() - timedelta(days=7)
        last_payryun = scheduled_payruns(date_start=week_ago)[0]
        last_payrun_date = last_payryun.date
        payments_usd = prepare_payments(3, vendor, status=Payment.STATUS_PROCESSING, scheduled_for=last_payrun_date)
        payments_eur = prepare_payments(3, vendor, currency="EUR", status=Payment.STATUS_PROCESSING, scheduled_for=last_payrun_date)
        calculate_remaining_balance()
        response = self.api_client.get(self.api_url)
        self.assertEqual(response.status_code, 200)
        response_content = response.json()
        balance_summary = response_content[0]
        account_balance_for_processor = balance_summary["account_balances_for_processor"][0]
        account_balance = account_balance_for_processor["account_balances"][0]
        processing_amount_in_usd = sum([p.total_amount for p in payments_usd])
        processing_amount_in_eur = sum([convert_currency("USD", "EUR", p.total_amount) * BUFFER_FACTOR for p in payments_eur])
        self.assertEqual(
            round_decimal(Decimal(account_balance["processing_amount"]), 2),
            round_decimal(Decimal(processing_amount_in_usd + processing_amount_in_eur), 2),
        )
        self.assertEqual(
            round_decimal(Decimal(account_balance["remaining_balance"] + account_balance["processing_amount"]), 2),
            round_decimal(Decimal(account_balance["amount"]), 2),
        )
        self.assertFalse(account_balance["is_processing_currency_match"])

    def _generate_statement(self):
        self._fetch_data()
        vendor = self._prepare_vendor_with_payout_method_mc()
        week_ago = now() - timedelta(days=7)
        payruns = scheduled_payruns(date_start=week_ago)
        last_payrun = payruns[0]
        last_payrun_date = last_payrun.date
        nearest_payrun = payruns[1]
        nearest_payrun_date = nearest_payrun.date
        prepare_payments(3, vendor, status=Payment.STATUS_PROCESSING, scheduled_for=last_payrun_date)
        prepare_payments(3, vendor, status=Payment.STATUS_SCHEDULED, scheduled_for=nearest_payrun_date)
        calculate_remaining_balance()

        with self.watch_event(FundingStatementReadyEmailTrigger) as events:
            response = self.api_client.get(self.statement_url)
            self.assertEqual(response.status_code, 200)
        return events

    @features_enabled([BANK_DETAILS_MC_FORMAT])
    def test_ok_generate_statement(self):
        events = self._generate_statement()
        self.assertEqual(len(events), 1)
        self.assertEqual(events[0].user.email, self.user.email)
        self.assertEqual(events[0].expiration_date, events[0].export_date + timedelta(7))
        filename_pattern = "?file_name=funding_statement_shared_"
        self.assertIn(filename_pattern, events[0].statement_pdf_url)
        self.assertMailSent(self.user.email, "Funding statement ready")
        self.assertHasSections([":export_date", ":export_time", ":expiration_date", ":statement_pdf_url"], "Funding statement ready")

    @features_enabled([BANK_DETAILS_MC_FORMAT])
    def test_statement_is_able_to_download(self):
        events = self._generate_statement()
        response = self.api_client.get(events[0].statement_pdf_url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-type'], 'application/pdf')

    @parameterized.expand(
        [
            (''),
            (' '),
            ('wrong_pdf.pdf'),
            ('../..'),
            ('/root'),
            ('/'),
            ('======='),
        ]
    )
    @features_enabled([BANK_DETAILS_MC_FORMAT])
    def test_invalid_statement_name(self, pdf_name):
        response = self.api_client.get(f"{self.download_statement_url}?file_name={pdf_name}")
        self.assertEqual(response.status_code, 404)

    @features_enabled([BANK_DETAILS_MC_FORMAT])
    def test_not_existing_pdf_name(self):
        pdf_name = 'funding_statement_shared_2024-01-16_11-12-40_ay6q3190.pdf'
        response = self.api_client.get(f"{self.download_statement_url}?file_name={pdf_name}")
        self.assertEqual(response.status_code, 404)

    @features_enabled([BANK_DETAILS_MC_FORMAT])
    def test_no_parameter(self):
        response = self.api_client.get(self.download_statement_url)
        self.assertEqual(response.status_code, 404)
