import unittest

import responses
from parameterized import parameterized

from clients import features
from integrations.models import ShortlistPayIntegration
from integrations.shortlist_pay_json_rpc_client import ShortlistPayJsonRpcClient
from integrations.tests.test_create_shortlist_pay_integration import (
    PAY_API_ERROR,
    PAY_API_URL,
)
from payments.config import has_payment_processing_enabled
from payments.pay_integration.base import ShortlistPayNotConfigured
from payments.pay_integration.model import PayIntegration, PayOperator
from payments.pay_integration.prod import ShortlistPay
from payments.tests.factories.mocks import factory_tenant
from payments.tests.factories.pay import factory_payment_processor
from shortlist.tests.helpers import TenantTestCase, features_enabled


class TenantPayIntegrationTestCase(TenantTestCase):
    def setUp(self) -> None:
        super().setUp()
        self.facade = ShortlistPay()

    def test_integration_no_flag(self):
        self.assertIsNone(self.facade.integration)

    @features_enabled([features.BANK_DETAILS_MC_FORMAT])
    def test_integration_missing_configuration(self):
        self.assertIsNone(self.facade.integration)

    @features_enabled([features.BANK_DETAILS_MC_FORMAT])
    def test_integration_configuration_disabled(self):
        self._store_configuration(enabled=False)
        self.assertIsNone(self.facade.integration)

    @features_enabled([features.BANK_DETAILS_MC_FORMAT])
    def test_integration_ok(self):
        self._store_configuration()
        self.assertEqual(
            self.facade.integration,
            PayIntegration(api_token="api_token", account_reference="account_reference"),  # noqa: S106
        )

    @features_enabled([features.BANK_DETAILS_MC_FORMAT])
    def test_api_client_configuration_disabled(self):
        self._store_configuration(enabled=False)
        self.assertIsNone(self.facade._api_client)

    @features_enabled([features.BANK_DETAILS_MC_FORMAT])
    def test_api_client_ok(self):
        self._store_configuration()
        self.assertIsInstance(self.facade._api_client, ShortlistPayJsonRpcClient)

    def test_get_payment_processors_no_flag(self):
        self._store_configuration()
        self.assertEqual(self.facade.get_payment_processors(), [])

    @features_enabled([features.BANK_DETAILS_MC_FORMAT])
    def test_get_payment_processors_configuration_disabled(self):
        self._store_configuration(enabled=False)
        self.assertEqual(self.facade.get_payment_processors(), [])

    @parameterized.expand(
        [
            ([],),
            ([factory_payment_processor(PayOperator.MONEYCORP)],),
            ([factory_payment_processor(PayOperator.MONEYCORP), factory_payment_processor(PayOperator.PAYONEER)],),
        ]
    )
    @features_enabled([features.BANK_DETAILS_MC_FORMAT])
    @responses.activate
    def test_get_payment_processors_ok(self, processors):
        self._store_configuration()
        pay_response = responses.add(responses.POST, PAY_API_URL, json={"jsonrpc": "2.0", "result": [p.model_dump() for p in processors]})
        self.facade.get_processors_cache().invalidate()  # We need to invalidate before each test.

        # First usage calls Pay:
        self.assertEqual(self.facade.get_payment_processors(), processors)
        self.assertEqual(pay_response.call_count, 1)
        # Second call makes no additional Pay call due to cache:
        self.assertEqual(self.facade.get_payment_processors(), processors)
        self.assertEqual(pay_response.call_count, 1)

    @features_enabled([features.BANK_DETAILS_MC_FORMAT])
    @responses.activate
    def test_get_payment_processors_error_pay(self):
        self._store_configuration()
        responses.add(responses.POST, PAY_API_URL, json=PAY_API_ERROR)
        self.facade.get_processors_cache().invalidate()  # We need to invalidate before each test.

        with self.assertRaises(ShortlistPayNotConfigured):
            self.facade.get_payment_processors()

    @features_enabled([features.BANK_DETAILS_MC_FORMAT])
    @responses.activate
    def test_get_payment_processor_by_id_ok(self):
        self._store_configuration()
        processor = factory_payment_processor(PayOperator.MONEYCORP)
        responses.add(responses.POST, PAY_API_URL, json={"jsonrpc": "2.0", "result": [processor.model_dump()]})
        self.facade.get_processors_cache().invalidate()  # We need to invalidate before each test.

        self.assertEqual(self.facade.get_payment_processor_by_id(processor.processor_id), processor)

    @features_enabled([features.BANK_DETAILS_MC_FORMAT])
    @responses.activate
    def test_get_payment_processor_by_id_not_found(self):
        self._store_configuration()
        responses.add(responses.POST, PAY_API_URL, json={"jsonrpc": "2.0", "result": None})
        self.facade.get_processors_cache().invalidate()  # We need to invalidate before each test.

        self.assertIsNone(self.facade.get_payment_processor_by_id("APX1"))

    @features_enabled([features.BANK_DETAILS_MC_FORMAT])
    @responses.activate
    def test_get_payment_processor_by_id_error_pay(self):
        self._store_configuration()
        responses.add(responses.POST, PAY_API_URL, json=PAY_API_ERROR)
        self.facade.get_processors_cache().invalidate()  # We need to invalidate before each test.

        self.assertIsNone(self.facade.get_payment_processor_by_id("APX1"))

    @features_enabled([features.BANK_DETAILS_MC_FORMAT])
    @responses.activate
    def test_get_explicit_payment_processor_ok(self):
        self._store_configuration()
        processor_mc = factory_payment_processor(PayOperator.MONEYCORP)
        processor_payoneer = factory_payment_processor(PayOperator.PAYONEER)
        processors = [processor_mc, processor_payoneer]
        responses.add(responses.POST, PAY_API_URL, json={"jsonrpc": "2.0", "result": [p.model_dump() for p in processors]})
        self.facade.get_processors_cache().invalidate()  # We need to invalidate before each test.

        self.assertEqual(self.facade.get_moneycorp_payment_processor(), processor_mc)
        self.assertEqual(self.facade.get_moneycorp_payment_processor_for_processing(), processor_mc)
        self.assertEqual(self.facade.get_payoneer_payment_processor(), processor_payoneer)

    @features_enabled([features.BANK_DETAILS_MC_FORMAT])
    @responses.activate
    def test_get_explicit_payment_processor_no_moneycorp(self):
        self._store_configuration()
        processor_default = factory_payment_processor(PayOperator.DEFAULT)
        processor_payoneer = factory_payment_processor(PayOperator.PAYONEER)
        processors = [processor_default, processor_payoneer]
        responses.add(responses.POST, PAY_API_URL, json={"jsonrpc": "2.0", "result": [p.model_dump() for p in processors]})
        self.facade.get_processors_cache().invalidate()  # We need to invalidate before each test.

        self.assertIsNone(self.facade.get_moneycorp_payment_processor())
        self.assertIsNone(self.facade.get_moneycorp_payment_processor_for_processing())
        self.assertEqual(self.facade.get_payoneer_payment_processor(), processor_payoneer)

    @features_enabled([features.BANK_DETAILS_MC_FORMAT])
    @responses.activate
    def test_get_explicit_payment_processor_no_payoneer(self):
        self._store_configuration()
        processor_default = factory_payment_processor(PayOperator.DEFAULT)
        processor_mc = factory_payment_processor(PayOperator.MONEYCORP)
        processors = [processor_default, processor_mc]
        responses.add(responses.POST, PAY_API_URL, json={"jsonrpc": "2.0", "result": [p.model_dump() for p in processors]})
        self.facade.get_processors_cache().invalidate()  # We need to invalidate before each test.

        self.assertEqual(self.facade.get_moneycorp_payment_processor(), processor_mc)
        self.assertEqual(self.facade.get_moneycorp_payment_processor_for_processing(), processor_mc)
        self.assertIsNone(self.facade.get_payoneer_payment_processor())

    @features_enabled([features.BANK_DETAILS_MC_FORMAT])
    @responses.activate
    def test_get_explicit_payment_processor_moneycorp_disabled(self):
        self._store_configuration()
        processor_default = factory_payment_processor(PayOperator.DEFAULT)
        processor_payoneer = factory_payment_processor(PayOperator.PAYONEER)
        processor_mc = factory_payment_processor(PayOperator.MONEYCORP, beneficiaries_enabled=False, payments_processing_enabled=False)
        processors = [processor_default, processor_payoneer, processor_mc]
        responses.add(responses.POST, PAY_API_URL, json={"jsonrpc": "2.0", "result": [p.model_dump() for p in processors]})
        self.facade.get_processors_cache().invalidate()  # We need to invalidate before each test.

        self.assertIsNone(self.facade.get_moneycorp_payment_processor())
        self.assertIsNone(self.facade.get_moneycorp_payment_processor_for_processing())
        self.assertEqual(self.facade.get_payoneer_payment_processor(), processor_payoneer)

    @features_enabled([features.BANK_DETAILS_MC_FORMAT])
    @responses.activate
    def test_get_explicit_payment_processor_payoneer_disabled(self):
        self._store_configuration()
        processor_default = factory_payment_processor(PayOperator.DEFAULT)
        processor_payoneer = factory_payment_processor(PayOperator.PAYONEER, beneficiaries_enabled=False)
        processor_mc = factory_payment_processor(PayOperator.MONEYCORP)
        processors = [processor_default, processor_payoneer, processor_mc]
        responses.add(responses.POST, PAY_API_URL, json={"jsonrpc": "2.0", "result": [p.model_dump() for p in processors]})
        self.facade.get_processors_cache().invalidate()  # We need to invalidate before each test.

        self.assertEqual(self.facade.get_moneycorp_payment_processor(), processor_mc)
        self.assertEqual(self.facade.get_moneycorp_payment_processor_for_processing(), processor_mc)
        self.assertIsNone(self.facade.get_payoneer_payment_processor())

    @features_enabled([features.BANK_DETAILS_MC_FORMAT])
    @responses.activate
    def test_get_explicit_payment_processor_multiple_moneycorps(self):
        self._store_configuration()
        processor_mc1 = factory_payment_processor(PayOperator.MONEYCORP)
        processor_mc2 = factory_payment_processor(PayOperator.MONEYCORP)
        processors = [processor_mc1, processor_mc2]
        responses.add(responses.POST, PAY_API_URL, json={"jsonrpc": "2.0", "result": [p.model_dump() for p in processors]})
        self.facade.get_processors_cache().invalidate()  # We need to invalidate before each test.

        self.assertIsNone(self.facade.get_moneycorp_payment_processor())
        self.assertIsNone(self.facade.get_moneycorp_payment_processor_for_processing())

    @features_enabled([features.BANK_DETAILS_MC_FORMAT])
    @responses.activate
    def test_get_explicit_payment_processor_multiple_payoneers(self):
        self._store_configuration()
        processor_mc1 = factory_payment_processor(PayOperator.PAYONEER)
        processor_mc2 = factory_payment_processor(PayOperator.PAYONEER)
        processors = [processor_mc1, processor_mc2]
        responses.add(responses.POST, PAY_API_URL, json={"jsonrpc": "2.0", "result": [p.model_dump() for p in processors]})
        self.facade.get_processors_cache().invalidate()  # We need to invalidate before each test.

        self.assertIsNone(self.facade.get_payoneer_payment_processor())

    @features_enabled([features.BANK_DETAILS_MC_FORMAT])
    @responses.activate
    def test_current_payment_processor_ok_moneycorp(self):
        self._store_configuration()
        processor_default = factory_payment_processor(PayOperator.DEFAULT, beneficiaries_enabled=False)
        processor_payoneer = factory_payment_processor(PayOperator.PAYONEER)
        processor_mc = factory_payment_processor(PayOperator.MONEYCORP)
        processors = [processor_default, processor_payoneer, processor_mc]
        responses.add(responses.POST, PAY_API_URL, json={"jsonrpc": "2.0", "result": [p.model_dump() for p in processors]})
        self.facade.get_processors_cache().invalidate()  # We need to invalidate before each test.

        self.assertEqual(self.facade.current_payment_processor, processor_mc)

    @features_enabled([features.BANK_DETAILS_MC_FORMAT])
    @responses.activate
    def test_current_payment_processor_ok_default(self):
        self._store_configuration()
        processor_default = factory_payment_processor(PayOperator.DEFAULT)
        processor_payoneer = factory_payment_processor(PayOperator.PAYONEER)
        processor_mc = factory_payment_processor(PayOperator.MONEYCORP, beneficiaries_enabled=False)
        processors = [processor_default, processor_payoneer, processor_mc]
        responses.add(responses.POST, PAY_API_URL, json={"jsonrpc": "2.0", "result": [p.model_dump() for p in processors]})
        self.facade.get_processors_cache().invalidate()  # We need to invalidate before each test.

        self.assertEqual(self.facade.current_payment_processor, processor_default)

    @features_enabled([features.BANK_DETAILS_MC_FORMAT])
    @responses.activate
    def test_current_payment_processor_ok_only_payoneer(self):
        self._store_configuration()
        processor_default = factory_payment_processor(PayOperator.DEFAULT, beneficiaries_enabled=False)
        processor_payoneer = factory_payment_processor(PayOperator.PAYONEER)
        processor_mc = factory_payment_processor(PayOperator.MONEYCORP, beneficiaries_enabled=False)
        processors = [processor_default, processor_payoneer, processor_mc]
        responses.add(responses.POST, PAY_API_URL, json={"jsonrpc": "2.0", "result": [p.model_dump() for p in processors]})
        self.facade.get_processors_cache().invalidate()  # We need to invalidate before each test.

        self.assertIsNone(self.facade.current_payment_processor)

    def _store_configuration(self, *, enabled=True):
        ShortlistPayIntegration.objects.create(enabled=enabled, api_token="api_token", account_reference="account_reference")  # noqa: S106


class TenantHasPaymentProcessingEnabledTestCase(unittest.TestCase):
    FLAGS_NEEDED = [features.BANK_DETAILS_MC_FORMAT]

    def test_enabled_mc(self):
        tenant = factory_tenant(enabled_features=self.FLAGS_NEEDED)
        tenant.add_payment_processor(factory_payment_processor(PayOperator.MONEYCORP))
        self.assertTrue(has_payment_processing_enabled(tenant))

    def test_enabled_payoneer(self):
        tenant = factory_tenant(enabled_features=self.FLAGS_NEEDED)
        tenant.add_payment_processor(factory_payment_processor(PayOperator.PAYONEER))
        self.assertTrue(has_payment_processing_enabled(tenant))

    def test_enabled_mc_disabled_payoneer(self):
        tenant = factory_tenant(enabled_features=self.FLAGS_NEEDED)
        tenant.add_payment_processor(factory_payment_processor(PayOperator.MONEYCORP))
        tenant.add_payment_processor(factory_payment_processor(PayOperator.PAYONEER, beneficiaries_enabled=False))
        self.assertTrue(has_payment_processing_enabled(tenant))

    def test_enabled_payoneer_disabled_mc(self):
        tenant = factory_tenant(enabled_features=self.FLAGS_NEEDED)
        tenant.add_payment_processor(factory_payment_processor(PayOperator.MONEYCORP, beneficiaries_enabled=False))
        tenant.add_payment_processor(factory_payment_processor(PayOperator.PAYONEER))
        self.assertTrue(has_payment_processing_enabled(tenant))

    def test_enabled_mc_and_payoneer(self):
        tenant = factory_tenant(enabled_features=self.FLAGS_NEEDED)
        tenant.add_payment_processor(factory_payment_processor(PayOperator.MONEYCORP))
        tenant.add_payment_processor(factory_payment_processor(PayOperator.PAYONEER))
        self.assertTrue(has_payment_processing_enabled(tenant))

    def test_enabled_mc_disabled_default(self):
        tenant = factory_tenant(enabled_features=self.FLAGS_NEEDED)
        tenant.add_payment_processor(factory_payment_processor(PayOperator.DEFAULT, beneficiaries_enabled=False))
        tenant.add_payment_processor(factory_payment_processor(PayOperator.MONEYCORP))
        self.assertTrue(has_payment_processing_enabled(tenant))

    def test_disabled_pay_flag_disabled(self):
        tenant = factory_tenant()
        self.assertFalse(has_payment_processing_enabled(tenant))

    def test_disabled_missing_platform_configuration(self):
        tenant = factory_tenant(enabled_features=self.FLAGS_NEEDED, tenant_pay_integration_exists=False)
        self.assertFalse(has_payment_processing_enabled(tenant))

    def test_disabled_only_default_operator(self):
        tenant = factory_tenant(enabled_features=self.FLAGS_NEEDED)
        tenant.add_payment_processor(factory_payment_processor(PayOperator.DEFAULT))
        self.assertFalse(has_payment_processing_enabled(tenant))

    def test_disabled_no_processors(self):
        # This situation shouldn't happen due to Pay restrictions, but...
        tenant = factory_tenant(enabled_features=self.FLAGS_NEEDED)
        self.assertFalse(has_payment_processing_enabled(tenant))

    def test_disabled_moneycorp_was_disabled(self):
        tenant = factory_tenant(enabled_features=self.FLAGS_NEEDED)
        tenant.add_payment_processor(factory_payment_processor(PayOperator.DEFAULT, beneficiaries_enabled=False))
        tenant.add_payment_processor(factory_payment_processor(PayOperator.MONEYCORP, beneficiaries_enabled=False))
        tenant.add_payment_processor(factory_payment_processor(PayOperator.DEFAULT))
        self.assertFalse(has_payment_processing_enabled(tenant))
