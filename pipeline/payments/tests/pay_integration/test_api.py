from parameterized import parameterized

from payments.pay_integration.model import PayOperator
from payments.payout_methods.config import REQUIRED_FEATURE_FLAGS
from payments.tests.factories.base import UserTypes
from payments.tests.factories.django import factory_user
from payments.tests.factories.pay import factory_payment_processor
from payments.tests.mixins import WithFakePay
from shortlist.tests.helpers import TenantTestCase, features_enabled


class PaymentProcessorsAPITestCase(WithFakePay, TenantTestCase):
    with_vendor_user = True
    api_url = "/api/payments/payment_processors/"

    @parameterized.expand(
        [
            (None, 404),
            (UserTypes.BUYER_ADMIN, 200),
            (UserTypes.BUYER_REGULAR_DEFAULT, 200),
            (UserTypes.GUEST, 403),
            (UserTypes.STAFF, 200),
            (UserTypes.STAFFING_SUPPLIER, 200),
            (UserTypes.VENDOR, 200),
            (UserTypes.VENDOR_NOT_SHORTLISTED, 200),
        ]
    )
    @features_enabled(REQUIRED_FEATURE_FLAGS)
    def test_permissions(self, user_type, expected_http_status):
        user = factory_user(user_type) if user_type else None
        if user:
            self.api_client.force_login(user)
        else:
            self.api_client.logout()
        response = self.api_client.get(self.api_url)
        self.assertEqual(response.status_code, expected_http_status)

    def test_no_feature_flags(self):
        response = self.api_client.get(self.api_url)
        self.assertEqual(response.status_code, 403)

    @features_enabled(REQUIRED_FEATURE_FLAGS)
    def test_ok(self):
        response = self.api_client.get(self.api_url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), [{"processor_id": "APX1", "provider": PayOperator.MONEYCORP, "payable_currencies": ["USD"]}])

    @features_enabled(REQUIRED_FEATURE_FLAGS)
    def test_ok_multiple(self):
        self.pay_api_client.config.get_account_processors.return_value = [
            factory_payment_processor(PayOperator.MONEYCORP, processor_id="mc_disabled", beneficiaries_enabled=False).model_dump(),
            factory_payment_processor(PayOperator.MONEYCORP, processor_id="mc_enabled", payable_currencies=["EUR"]).model_dump(),
            factory_payment_processor(PayOperator.PAYONEER, processor_id="payoneer_disabled", beneficiaries_enabled=False).model_dump(),
            factory_payment_processor(PayOperator.PAYONEER, processor_id="payoneer_enabled", payable_currencies=["PLN"]).model_dump(),
        ]
        response = self.api_client.get(self.api_url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.json(),
            [
                {"processor_id": "mc_enabled", "provider": PayOperator.MONEYCORP, "payable_currencies": ["EUR"]},
                {"processor_id": "payoneer_enabled", "provider": PayOperator.PAYONEER, "payable_currencies": ["PLN"]},
            ],
        )
