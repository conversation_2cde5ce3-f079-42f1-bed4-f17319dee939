from unittest import TestCase, mock

from django.utils import timezone
from freezegun import freeze_time
from requests import ConnectionError

from documents.models import Payment
from integrations.json_rpc import RPCException
from payments.mc_payments_processing.service_deliver import (
    deliver_queued_payment_documents_to_moneycorp,
    deliver_single_payment_document_to_moneycorp,
)
from payments.models import MoneyCorpPaymentDocument, MoneyCorpPaymentDocumentType
from payments.pay_integration.model import PayResponse
from payments.tests.mc_payments_processing.repositories import (
    MoneyCorpPaymentDocumentInMemoryRepository,
)


class DeliverServiceTestCase(TestCase):
    def setUp(self) -> None:
        self.repository = MoneyCorpPaymentDocumentInMemoryRepository()
        self.generate_payment_compliance_pdf_mock = mock.patch(
            "payments.mc_payments_processing.service_deliver.generate_payment_compliance_pdf"
        ).start()
        self.generate_payment_compliance_pdf_mock.return_value = b"pdf"
        self.addCleanup(mock.patch.stopall)
        freezer = freeze_time("2024-07-01 12:00:00")
        self.frozen_time = freezer.start()
        self.addCleanup(freezer.stop)
        self.tenant = mock.MagicMock()

    def test_delivery_schedule_nothing(self):
        single_delivery_task = mock.MagicMock()

        result = deliver_queued_payment_documents_to_moneycorp(repository=self.repository, single_delivery_task=single_delivery_task)

        self.assertEqual(result, [])
        single_delivery_task.delay.assert_not_called()

    def test_delivery_schedule_multiple_scheduled(self):
        documents = [
            MoneyCorpPaymentDocument(id=5, oper_account_id=1, oper_payment_id=1),
            MoneyCorpPaymentDocument(id=6, oper_account_id=1, oper_payment_id=2),
            MoneyCorpPaymentDocument(id=7, oper_account_id=1, oper_payment_id=3),
            MoneyCorpPaymentDocument(id=8, oper_account_id=1, oper_payment_id=3, started_at=timezone.now()),
        ]
        self.repository.store(documents)
        single_delivery_task = mock.MagicMock()

        # first run will schedule them:
        result = deliver_queued_payment_documents_to_moneycorp(repository=self.repository, single_delivery_task=single_delivery_task)
        self.assertEqual(result, [5, 6, 7])
        single_delivery_task.delay.assert_has_calls(
            [
                mock.call(5),
                mock.call(6),
                mock.call(7),
            ]
        )
        single_delivery_task.reset_mock()

        # next run won't detect anything new:
        result = deliver_queued_payment_documents_to_moneycorp(repository=self.repository)
        self.assertEqual(result, [])
        single_delivery_task.delay.assert_not_called()

    def test_deliver_single_success(self):
        payment = Payment(id=100)
        self.repository.add_payments([payment])
        document = MoneyCorpPaymentDocument(
            id=5,
            processor_id="APX123456",
            document_type=MoneyCorpPaymentDocumentType.PAYMENT_COMPLIANCE_PDF,
            payment_id=payment.id,
            oper_account_id=********,
            oper_payment_id=************,
        )
        self.repository.store([document])
        self.tenant.shortlist_pay.payments.mc_payment_upload_file.return_value = {
            "status": PayResponse.SUCCESS,
            "attributes": {"id": "123456"},
        }

        result = deliver_single_payment_document_to_moneycorp(document.id, tenant=self.tenant, repository=self.repository)

        self.assertEqual(result, "success")
        document = self.repository.get_single(document.id)
        self.assertTrue(document.success)
        self.assertEqual(document.oper_document_id, "123456")
        self.assertIsNone(document.failed_at)
        self.assertEqual(document.completed_at, timezone.now())
        expected_file_name = "payment_compliance_pdf-id5-aid********-pid************.pdf"
        expected_file_type = "application/pdf"
        self.assertEqual(document.file_metadata, {"name": expected_file_name, "mimetype": expected_file_type})
        self.tenant.shortlist_pay.payments.mc_payment_upload_file.assert_called_once_with(
            payment_processor_id="APX123456",
            payment_id=************,
            file_name=expected_file_name,
            file_type=expected_file_type,
            file_content="cGRm",
        )

    def test_deliver_single_error_already_delivered(self):
        payment = Payment(id=100)
        self.repository.add_payments([payment])
        document = MoneyCorpPaymentDocument(
            id=5,
            processor_id="APX123456",
            document_type=MoneyCorpPaymentDocumentType.PAYMENT_COMPLIANCE_PDF,
            payment_id=payment.id,
            oper_account_id=********,
            oper_payment_id=************,
        )
        document.delivered({}, "123")
        self.repository.store([document])

        result = deliver_single_payment_document_to_moneycorp(document.id, tenant=self.tenant, repository=self.repository)

        self.assertEqual(result, "failed:already-delivered")
        self.tenant.shortlist_pay.payments.mc_payment_upload_file.assert_not_called()

    def test_deliver_single_error_file_upload_error_missing_document_id(self):
        payment = Payment(id=100)
        self.repository.add_payments([payment])
        document = MoneyCorpPaymentDocument(
            id=5,
            processor_id="APX123456",
            document_type=MoneyCorpPaymentDocumentType.PAYMENT_COMPLIANCE_PDF,
            payment_id=payment.id,
            oper_account_id=********,
            oper_payment_id=************,
        )
        self.repository.store([document])
        self.tenant.shortlist_pay.payments.mc_payment_upload_file.return_value = {
            "status": PayResponse.SUCCESS,
        }

        result = deliver_single_payment_document_to_moneycorp(document.id, tenant=self.tenant, repository=self.repository)

        self.assertEqual(result, "failed:delivery-error")
        document = self.repository.get_single(document.id)
        self.assertFalse(document.success)
        self.assertEqual(document.failed_at, timezone.now())
        self.assertEqual(document.failed_reason, "Pay responded without providing uploaded document ID")
        self.assertIsNone(document.completed_at)
        self.assertIsNone(document.oper_document_id)
        self.tenant.shortlist_pay.payments.mc_payment_upload_file.assert_called_once()

    def test_deliver_single_error_file_upload_pay_error(self):
        payment = Payment(id=100)
        self.repository.add_payments([payment])
        document = MoneyCorpPaymentDocument(
            id=5,
            processor_id="APX123456",
            document_type=MoneyCorpPaymentDocumentType.PAYMENT_COMPLIANCE_PDF,
            payment_id=payment.id,
            oper_account_id=********,
            oper_payment_id=************,
        )
        self.repository.store([document])
        pay_response = {
            "status": PayResponse.ERROR,
        }
        self.tenant.shortlist_pay.payments.mc_payment_upload_file.return_value = pay_response

        result = deliver_single_payment_document_to_moneycorp(document.id, tenant=self.tenant, repository=self.repository)

        self.assertEqual(result, "failed:delivery-error")
        document = self.repository.get_single(document.id)
        self.assertFalse(document.success)
        self.assertEqual(document.failed_at, timezone.now())
        self.assertEqual(document.failed_reason, "Pay response status is not success: {'status': PayResponse.ERROR}")
        self.assertIsNone(document.completed_at)
        self.assertIsNone(document.oper_document_id)
        self.tenant.shortlist_pay.payments.mc_payment_upload_file.assert_called_once()

    def test_deliver_single_error_file_upload_rpc_exception(self):
        payment = Payment(id=100)
        self.repository.add_payments([payment])
        document = MoneyCorpPaymentDocument(
            id=5,
            processor_id="APX123456",
            document_type=MoneyCorpPaymentDocumentType.PAYMENT_COMPLIANCE_PDF,
            payment_id=payment.id,
            oper_account_id=********,
            oper_payment_id=************,
        )
        self.repository.store([document])
        errors = {"errors": "error"}
        self.tenant.shortlist_pay.payments.mc_payment_upload_file.side_effect = RPCException(errors)

        result = deliver_single_payment_document_to_moneycorp(document.id, tenant=self.tenant, repository=self.repository)

        self.assertEqual(result, "failed:delivery-error")
        document = self.repository.get_single(document.id)
        self.assertFalse(document.success)
        self.assertEqual(document.failed_at, timezone.now())
        self.assertEqual(document.failed_reason, "RPCException: {'errors': 'error'}")
        self.assertIsNone(document.completed_at)
        self.assertIsNone(document.oper_document_id)
        self.tenant.shortlist_pay.payments.mc_payment_upload_file.assert_called_once()

    def test_deliver_single_error_file_upload_connection_error(self):
        payment = Payment(id=100)
        self.repository.add_payments([payment])
        document = MoneyCorpPaymentDocument(
            id=5,
            processor_id="APX123456",
            document_type=MoneyCorpPaymentDocumentType.PAYMENT_COMPLIANCE_PDF,
            payment_id=payment.id,
            oper_account_id=********,
            oper_payment_id=************,
        )
        self.repository.store([document])
        self.tenant.shortlist_pay.payments.mc_payment_upload_file.side_effect = ConnectionError()

        result = deliver_single_payment_document_to_moneycorp(document.id, tenant=self.tenant, repository=self.repository)

        self.assertEqual(result, "failed:delivery-error")
        document = self.repository.get_single(document.id)
        self.assertFalse(document.success)
        self.assertEqual(document.failed_at, timezone.now())
        self.assertEqual(document.failed_reason, "ConnectionError")
        self.assertIsNone(document.completed_at)
        self.assertIsNone(document.oper_document_id)
        self.tenant.shortlist_pay.payments.mc_payment_upload_file.assert_called_once()

    def test_deliver_single_error_document_not_found(self):
        document = MoneyCorpPaymentDocument(
            id=5,
            processor_id="APX123456",
            document_type=MoneyCorpPaymentDocumentType.PAYMENT_COMPLIANCE_PDF,
            payment_id=1,
            oper_account_id=********,
            oper_payment_id=************,
        )
        self.repository.store([document])

        result = deliver_single_payment_document_to_moneycorp(6, tenant=self.tenant, repository=self.repository)

        self.assertEqual(result, "failed:document-not-found")
        self.tenant.shortlist_pay.payments.mc_payment_upload_file.assert_not_called()

    def test_deliver_single_error_payment_not_found(self):
        document = MoneyCorpPaymentDocument(
            id=5,
            processor_id="APX123456",
            document_type=MoneyCorpPaymentDocumentType.PAYMENT_COMPLIANCE_PDF,
            payment_id=1,
            oper_account_id=********,
            oper_payment_id=************,
        )
        self.repository.store([document])

        result = deliver_single_payment_document_to_moneycorp(document.id, tenant=self.tenant, repository=self.repository)

        self.assertEqual(result, "failed:payment-not-found")
        self.tenant.shortlist_pay.payments.mc_payment_upload_file.assert_not_called()
