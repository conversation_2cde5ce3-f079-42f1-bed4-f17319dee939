from decimal import Decimal

from django.utils import timezone
from parameterized import parameterized
from rest_framework import status

from clients import features
from payments.models import Expense, ExpenseStatus
from payments.tests.expenses.factories import create_expense_category
from payments.tests.expenses.helpers import ExpenseMixin
from payments.tests.factories.base import UserTypes
from payments.tests.factories.django import factory_user
from payments.tests.factories.files import generate_expense_file_path
from payments.tests.factories.onboarding import (
    create_onboarding_workflow,
    assign_workflows_to_vendor,
    mark_workflow_completed_for_vendor,
    remove_workflows_from_vendor,
    mark_workflow_disqualified_for_vendor,
    mark_vendor_as_onboarding_underway,
    mark_vendor_as_fully_onboarded,
)
from shortlist.tests.helpers import TenantTestCase, features_enabled
from tasks.models import Task
from tasks.tests.factories import TaskFactory


class CreateExpenseTestCase(ExpenseMixin, TenantTestCase):
    maxDiff = None
    api_url = "/api/payments/expenses/"

    def setUp(self):
        super().setUp()
        self.vendor_user = factory_user(UserTypes.VENDOR)
        self.expense_category = create_expense_category()
        self.incurred_at = timezone.now()

    @parameterized.expand(
        [
            (UserTypes.BUYER_ADMIN,),
            (UserTypes.BUYER_REGULAR_DEFAULT,),
            (UserTypes.VENDOR,),
        ]
    )
    @features_enabled([features.EXPENSES])
    def test_create(self, user_type):
        user = self.vendor_user if user_type == UserTypes.VENDOR else factory_user(user_type)
        self.api_client.force_login(user)

        approver_user = factory_user(UserTypes.BUYER_REGULAR_EXPENSES_FULL)
        task = TaskFactory(name="Task1", vendor=self.vendor_user.vendor, status=Task.STATUS_ACCEPTED)

        request_data = {
            "name": "Expense1",
            "note": "WKS",
            "amount": Decimal("100"),
            "currency": "USD",
            "incurred_at": self.incurred_at,
            "category": self.expense_category.id,
            "vendor": self.vendor_user.vendor.id,
            "approver": approver_user.id,
            "task": task.id,
            "path": generate_expense_file_path(user=user, filename="samplefile.jpg"),
        }
        response = self.api_client.post(self.api_url, data=request_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        created_expense = Expense.objects.get(expense_id=response.json()["expense_id"])
        self.assertExpenseValues(
            created_expense,
            {
                "name": "Expense1",
                "note": "WKS",
                "amount": Decimal("100"),
                "currency": "USD",
                "incurred_at": self.incurred_at,
                "category": self.expense_category,
                "vendor": self.vendor_user.vendor,
                "approver": approver_user,
                "task": task,
                "created_by": user,
                "status": ExpenseStatus.STATUS_NEW,
                "filename": "samplefile.jpg",
            },
        )

    @features_enabled([features.EXPENSES])
    def test_validate_negative_amount(self):
        user = factory_user(UserTypes.BUYER_ADMIN)
        self.api_client.force_login(user)

        request_data = {
            "name": "Expense1",
            "note": "WKS",
            "amount": Decimal("-100"),
            "currency": "USD",
            "incurred_at": self.incurred_at,
            "category": self.expense_category.id,
            "vendor": self.vendor_user.vendor.id,
        }
        response = self.api_client.post(self.api_url, data=request_data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json(), {'amount': ['Ensure this value is greater than or equal to 0.01.']})

    @features_enabled([features.EXPENSES])
    def test_validate_if_vendor_is_the_same_as_task_vendor(self):
        user = factory_user(UserTypes.BUYER_ADMIN)
        self.api_client.force_login(user)

        task = TaskFactory(name="Task1", vendor=factory_user(UserTypes.VENDOR).vendor, status=Task.STATUS_ACCEPTED)

        request_data = {
            "name": "Expense1",
            "note": "WKS",
            "amount": Decimal("100"),
            "currency": "USD",
            "incurred_at": self.incurred_at,
            "category": self.expense_category.id,
            "vendor": self.vendor_user.vendor.id,
            "task": task.id,
        }
        response = self.api_client.post(self.api_url, data=request_data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json(), {'task': ["Expense partner doesn't match task partner"]})

    @features_enabled([features.EXPENSES])
    def test_validate_zero_decimal_currency(self):
        user = factory_user(UserTypes.BUYER_ADMIN)
        self.api_client.force_login(user)

        request_data = {
            "name": "Expense1",
            "note": "WKS",
            "amount": Decimal("100.01"),
            "currency": "JPY",
            "incurred_at": self.incurred_at,
            "category": self.expense_category.id,
            "vendor": self.vendor_user.vendor.id,
        }
        response = self.api_client.post(self.api_url, data=request_data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(),
            {
                'amount': [
                    'Currency JPY does not allow values with decimal places. '
                    'Please enter an integer value instead of a decimal (e.g. 50 instead 50.25)'
                ]
            },
        )

    @features_enabled([features.EXPENSES])
    def test_validate_wrong_currency(self):
        user = factory_user(UserTypes.BUYER_ADMIN)
        self.api_client.force_login(user)

        request_data = {
            "name": "Expense1",
            "note": "WKS",
            "amount": Decimal("100"),
            "currency": "WKS",
            "incurred_at": self.incurred_at,
            "category": self.expense_category.id,
            "vendor": self.vendor_user.vendor.id,
        }
        response = self.api_client.post(self.api_url, data=request_data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json(), {'currency': ['Invalid currency: WKS']})

    @features_enabled([features.EXPENSES])
    def test_validate_category_not_visible(self):
        user = factory_user(UserTypes.BUYER_ADMIN)
        self.api_client.force_login(user)

        not_visible_category = create_expense_category()
        not_visible_category.visible = False
        not_visible_category.save()

        request_data = {
            "name": "Expense1",
            "note": "WKS",
            "amount": Decimal("100"),
            "currency": "USD",
            "incurred_at": self.incurred_at,
            "category": not_visible_category.id,
            "vendor": self.vendor_user.vendor.id,
        }
        response = self.api_client.post(self.api_url, data=request_data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(),
            {'category': [f'Invalid pk "{not_visible_category.id}" - object does not exist.']},
        )

    @features_enabled([features.EXPENSES])
    def test_validate_required_fields(self):
        user = factory_user(UserTypes.BUYER_ADMIN)
        self.api_client.force_login(user)

        not_visible_category = create_expense_category()
        not_visible_category.visible = False
        not_visible_category.save()

        request_data = {}
        response = self.api_client.post(self.api_url, data=request_data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(),
            {
                'amount': ['This field is required.'],
                'category': ['This field is required.'],
                'incurred_at': ['This field is required.'],
                'name': ['This field is required.'],
                'vendor': ['This field is required.'],
            },
        )

    @features_enabled([features.EXPENSES])
    def test_validate_task_currency(self):
        user = factory_user(UserTypes.BUYER_ADMIN)
        self.api_client.force_login(user)

        task = TaskFactory(name="Task1", vendor=self.vendor_user.vendor, accepted=True)

        request_data = {
            "name": "Expense1",
            "note": "WKS",
            "amount": Decimal("100"),
            "currency": "EUR",
            "incurred_at": self.incurred_at,
            "category": self.expense_category.id,
            "vendor": self.vendor_user.vendor.id,
            "task": task.id,
        }
        response = self.api_client.post(self.api_url, data=request_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json(), {'task': ["Expense currency (EUR) doesn't match task currency (USD)"]})

    @features_enabled([features.EXPENSES])
    def test_validate_task_status(self):
        user = factory_user(UserTypes.BUYER_ADMIN)
        self.api_client.force_login(user)

        task = TaskFactory(name="Task1", vendor=self.vendor_user.vendor, status=Task.STATUS_DRAFT)

        request_data = {
            "name": "Expense1",
            "note": "WKS",
            "amount": Decimal("100"),
            "currency": "USD",
            "incurred_at": self.incurred_at,
            "category": self.expense_category.id,
            "vendor": self.vendor_user.vendor.id,
            "task": task.id,
        }
        response = self.api_client.post(self.api_url, data=request_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json(), {'task': ['Can not add expense to this task']})

    @features_enabled([features.EXPENSES])
    def test_validate_vendor_different_than_logged_vendor(self):
        user = self.vendor_user
        self.api_client.force_login(user)

        request_data = {
            "name": "Expense1",
            "note": "WKS",
            "amount": Decimal("100"),
            "currency": "USD",
            "incurred_at": self.incurred_at,
            "category": self.expense_category.id,
            "vendor": factory_user(UserTypes.VENDOR).vendor.id,
        }
        response = self.api_client.post(self.api_url, data=request_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json(), {'vendor': ["Expense partner doesn't match logged partner"]})

    @features_enabled([features.EXPENSES])
    def test_validate_archived_vendor(self):
        user = factory_user(UserTypes.BUYER_ADMIN)
        self.api_client.force_login(user)

        # Create an archived vendor
        vendor = factory_user(UserTypes.VENDOR).vendor
        vendor.archive()

        request_data = {
            "name": "Expense1",
            "note": "WKS",
            "amount": Decimal("100"),
            "currency": "USD",
            "incurred_at": self.incurred_at,
            "category": self.expense_category.id,
            "vendor": vendor.id,
        }
        response = self.api_client.post(self.api_url, data=request_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json(), {'vendor': ['Cannot assign selected partner']})

    @features_enabled([features.EXPENSES])
    def test_validate_approver_without_permission(self):
        user = factory_user(UserTypes.BUYER_ADMIN)
        self.api_client.force_login(user)

        approver_user = factory_user(UserTypes.BUYER_REGULAR_DEFAULT)

        request_data = {
            "name": "Expense1",
            "note": "WKS",
            "amount": Decimal("100"),
            "currency": "USD",
            "incurred_at": self.incurred_at,
            "category": self.expense_category.id,
            "vendor": self.vendor_user.vendor.id,
            "approver": approver_user.id,
        }
        response = self.api_client.post(self.api_url, data=request_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json(), {'approver': ['User does not have permission to be approver.']})

    @parameterized.expand(
        [
            (UserTypes.BUYER_ADMIN, status.HTTP_201_CREATED),
            (UserTypes.BUYER_REGULAR_DEFAULT, status.HTTP_201_CREATED),
            (UserTypes.VENDOR, status.HTTP_403_FORBIDDEN),
        ]
    )
    @features_enabled([features.EXPENSES, features.EXPENSES_PARTNERS_CANNOT_CREATE])
    def test_permission_check_to_create_with_flag_partners_cannot_create_expense_enabled(self, user_type, expected_status_code):
        user = self.vendor_user if user_type == UserTypes.VENDOR else factory_user(user_type)
        self.api_client.force_login(user)

        request_data = {
            "name": "Expense1",
            "amount": Decimal("100"),
            "currency": "USD",
            "incurred_at": self.incurred_at,
            "category": self.expense_category.id,
            "vendor": self.vendor_user.vendor.id,
        }
        response = self.api_client.post(self.api_url, data=request_data)
        self.assertEqual(response.status_code, expected_status_code)

    @features_enabled([features.EXPENSES])
    def test_fail_create_as_vendor_without_permission(self):
        # Add a test when logged user is vendor without permission expense.create
        # 403 status should be returned
        ...


class CreateExpenseVendorOnboardingCompletionRequirementTestCase(ExpenseMixin, TenantTestCase):
    maxDiff = None
    api_url = "/api/payments/expenses/"

    def setUp(self):
        super().setUp()
        self.vendor_user = factory_user(UserTypes.VENDOR)
        self.expense_category = create_expense_category()
        self.incurred_at = timezone.now()

    @parameterized.expand(
        [
            (UserTypes.BUYER_ADMIN,),
            (UserTypes.BUYER_REGULAR_DEFAULT,),
            (UserTypes.VENDOR,),
        ]
    )
    @features_enabled([features.EXPENSES, features.ONBOARDING, features.ONBOARDING_WORKFLOWS, features.COMPLETE_ONBOARDING])
    def test_ok_as_vendor_with_feature_complete_onboarding_if_workflows_completed(self, user_type):
        """Test that a vendor can create an expense after completing all required onboarding workflows."""
        user = self.vendor_user if user_type == UserTypes.VENDOR else factory_user(user_type)
        self.api_client.force_login(user)

        # Set up the workflow, assign to vendor, and mark as completed
        workflow = create_onboarding_workflow()
        assign_workflows_to_vendor(self.vendor_user.vendor, [workflow])
        mark_workflow_completed_for_vendor(self.vendor_user.vendor, workflow)

        # Verify vendor has completed all workflows
        self.assertTrue(self.vendor_user.vendor.assigned_workflows_completed)

        request_data = {
            "name": "Expense1",
            "amount": Decimal("100"),
            "currency": "USD",
            "incurred_at": self.incurred_at,
            "category": self.expense_category.id,
            "vendor": self.vendor_user.vendor.id,
        }
        response = self.api_client.post(self.api_url, data=request_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    @parameterized.expand(
        [
            (UserTypes.BUYER_ADMIN,),
            (UserTypes.BUYER_REGULAR_DEFAULT,),
            (UserTypes.VENDOR,),
        ]
    )
    @features_enabled([features.EXPENSES, features.ONBOARDING, features.ONBOARDING_WORKFLOWS, features.COMPLETE_ONBOARDING])
    def test_ok_with_feature_complete_onboarding_if_removed_from_workflow(self, user_type):
        """Test that a vendor can create an expense if they've been removed from required onboarding workflows."""
        user = self.vendor_user if user_type == UserTypes.VENDOR else factory_user(user_type)
        self.api_client.force_login(user)

        # Set up the workflow, assign to vendor, then remove from workflow
        workflow = create_onboarding_workflow()
        assign_workflows_to_vendor(self.vendor_user.vendor, [workflow])
        remove_workflows_from_vendor(self.vendor_user.vendor, [workflow])

        # Verify vendor has no required workflows (treated as completed)
        self.assertTrue(self.vendor_user.vendor.assigned_workflows_completed)

        request_data = {
            "name": "Expense1",
            "amount": Decimal("100"),
            "currency": "USD",
            "incurred_at": self.incurred_at,
            "category": self.expense_category.id,
            "vendor": self.vendor_user.vendor.id,
        }
        response = self.api_client.post(self.api_url, data=request_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    @parameterized.expand(
        [
            (UserTypes.BUYER_ADMIN,),
            (UserTypes.BUYER_REGULAR_DEFAULT,),
        ]
    )
    @features_enabled([features.EXPENSES, features.ONBOARDING, features.ONBOARDING_WORKFLOWS, features.COMPLETE_ONBOARDING])
    def test_ok_as_buyer_with_feature_complete_onboarding_if_workflow_not_completed(self, user_type):
        user = factory_user(user_type)
        self.api_client.force_login(user)

        # Set up the workflow and assign to vendor
        workflow = create_onboarding_workflow()
        assign_workflows_to_vendor(self.vendor_user.vendor, [workflow])

        # Verify vendor has incomplete workflows
        self.assertFalse(self.vendor_user.vendor.assigned_workflows_completed)

        request_data = {
            "name": "Expense1",
            "amount": Decimal("100"),
            "currency": "USD",
            "incurred_at": self.incurred_at,
            "category": self.expense_category.id,
            "vendor": self.vendor_user.vendor.id,
        }
        response = self.api_client.post(self.api_url, data=request_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    @features_enabled([features.EXPENSES, features.ONBOARDING, features.ONBOARDING_WORKFLOWS, features.COMPLETE_ONBOARDING])
    def test_fail_as_vendor_with_feature_complete_onboarding_if_workflow_not_completed(self):
        self.api_client.force_login(self.vendor_user)

        # Set up the workflow and assign to vendor
        workflow = create_onboarding_workflow()
        assign_workflows_to_vendor(self.vendor_user.vendor, [workflow])

        # Verify vendor has incomplete workflows
        self.assertFalse(self.vendor_user.vendor.assigned_workflows_completed)

        request_data = {
            "name": "Expense1",
            "amount": Decimal("100"),
            "currency": "USD",
            "incurred_at": self.incurred_at,
            "category": self.expense_category.id,
            "vendor": self.vendor_user.vendor.id,
        }
        response = self.api_client.post(self.api_url, data=request_data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(),
            {"vendor": ["Partner must complete all required onboarding steps before creating expenses."]},
        )

    @features_enabled([features.EXPENSES, features.ONBOARDING, features.ONBOARDING_WORKFLOWS, features.COMPLETE_ONBOARDING])
    def test_fail_as_vendor_with_feature_complete_onboarding_if_workflow_disqualified(self):
        """Test that a vendor cannot create an expense if they've been disqualified from required onboarding workflows."""
        self.api_client.force_login(self.vendor_user)

        # Set up the workflow, assign to vendor, and mark as disqualified
        workflow = create_onboarding_workflow()
        assign_workflows_to_vendor(self.vendor_user.vendor, [workflow])
        mark_workflow_disqualified_for_vendor(self.vendor_user.vendor, workflow)

        # Verify vendor has incomplete workflows due to disqualification
        self.assertFalse(self.vendor_user.vendor.assigned_workflows_completed)

        request_data = {
            "name": "Expense1",
            "amount": Decimal("100"),
            "currency": "USD",
            "incurred_at": self.incurred_at,
            "category": self.expense_category.id,
            "vendor": self.vendor_user.vendor.id,
        }
        response = self.api_client.post(self.api_url, data=request_data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(),
            {"vendor": ["Partner must complete all required onboarding steps before creating expenses."]},
        )


class CreateExpenseVendorMustBeFullyOnboardedValidationTestCase(ExpenseMixin, TenantTestCase):
    maxDiff = None
    api_url = "/api/payments/expenses/"

    def setUp(self):
        super().setUp()
        self.expense_category = create_expense_category()
        self.incurred_at = timezone.now()

    @features_enabled([features.EXPENSES, features.EXPENSES_ONLY_FOR_FULLY_ONBOARDED, features.ONBOARDING, features.ONBOARDING_WORKFLOWS])
    def test_vendor_can_add_expense_if_not_fully_onboarded(self):
        vendor_user = factory_user(UserTypes.VENDOR)
        self.api_client.force_login(vendor_user)

        mark_vendor_as_onboarding_underway(vendor_user.vendor)
        request_data = {
            "name": "Expense1",
            "amount": Decimal("100"),
            "currency": "USD",
            "incurred_at": self.incurred_at,
            "category": self.expense_category.id,
            "vendor": vendor_user.vendor.id,
        }
        response = self.api_client.post(self.api_url, data=request_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    @features_enabled([features.EXPENSES, features.EXPENSES_ONLY_FOR_FULLY_ONBOARDED, features.ONBOARDING, features.ONBOARDING_WORKFLOWS])
    def test_buyer_cant_add_expense_if_vendor_not_fully_onboarded(self):
        vendor_user = factory_user(UserTypes.VENDOR)
        user = factory_user(UserTypes.BUYER_ADMIN)
        self.api_client.force_login(user)

        mark_vendor_as_onboarding_underway(vendor_user.vendor)
        request_data = {
            "name": "Expense1",
            "amount": Decimal("100"),
            "currency": "USD",
            "incurred_at": self.incurred_at,
            "category": self.expense_category.id,
            "vendor": vendor_user.vendor.id,
        }

        response = self.api_client.post(self.api_url, data=request_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json(), {'vendor': ['Partner should be fully onboarded.']})

    @features_enabled([features.EXPENSES, features.ONBOARDING, features.ONBOARDING_WORKFLOWS])
    def test_buyer_can_add_expense_if_vendor_not_fully_onboarded_not_forced_by_flag(self):
        vendor_user = factory_user(UserTypes.VENDOR)
        user = factory_user(UserTypes.BUYER_ADMIN)
        self.api_client.force_login(user)

        mark_vendor_as_onboarding_underway(vendor_user.vendor)
        request_data = {
            "name": "Expense1",
            "amount": Decimal("100"),
            "currency": "USD",
            "incurred_at": self.incurred_at,
            "category": self.expense_category.id,
            "vendor": vendor_user.vendor.id,
        }

        response = self.api_client.post(self.api_url, data=request_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    @features_enabled([features.EXPENSES, features.EXPENSES_ONLY_FOR_FULLY_ONBOARDED, features.ONBOARDING, features.ONBOARDING_WORKFLOWS])
    def test_buyer_can_add_expense_if_vendor_fully_onboarded(self):
        vendor_user = factory_user(UserTypes.VENDOR)
        user = factory_user(UserTypes.BUYER_ADMIN)
        self.api_client.force_login(user)

        mark_vendor_as_fully_onboarded(vendor_user.vendor)
        request_data = {
            "name": "Expense1",
            "amount": Decimal("100"),
            "currency": "USD",
            "incurred_at": self.incurred_at,
            "category": self.expense_category.id,
            "vendor": vendor_user.vendor.id,
        }

        response = self.api_client.post(self.api_url, data=request_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
