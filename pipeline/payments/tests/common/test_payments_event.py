"""Tests for the payments events domain model."""

from datetime import datetime, UTC
from decimal import Decimal
from enum import Enum
from unittest import TestCase, mock
from uuid import UUID

from pydantic import BaseModel, ValidationError

from clients.models import Client
from payments.common.payments_event import PaymentsEvent, Tenant


class FakePayrun:
    """Example test data class."""

    def __init__(self, uid: str, status: str):
        self.uid = uid
        self.status = status


class FakePayrunStartedEvent(PaymentsEvent):
    """Example concrete event class."""

    slug = "payments.payrun.started"
    payrun_id: str
    status: str

    @classmethod
    def from_payrun(cls, payrun: FakePayrun) -> "FakePayrunStartedEvent":
        return cls(payrun_id=payrun.uid, status=payrun.status)


class PaymentMethod(Enum):
    BANK = "bank"
    CARD = "card"


class PaymentDetails(BaseModel):
    """Nested model for payment details."""

    reference: str
    currency: str
    description: str | None = None
    metadata: dict[str, str] = {}
    created_at: datetime


class ComplexPaymentEvent(PaymentsEvent):
    """Test event with complex field types."""

    slug = "payment.complex"
    amount: Decimal
    method: PaymentMethod
    due_date: datetime  # Additional datetime field
    details: PaymentDetails  # Nested Pydantic model


class PaymentsEventTest(TestCase):
    """Tests for PaymentsEvent functionality.

    This test suite verifies:
    1. Basic event properties (metadata, inheritance)
    2. Serialization and deserialization
    3. Field validation and type handling
    4. Error cases and validation messages
    """

    @mock.patch("payments.common.payments_event.connection")
    def test_base_event_has_required_metadata(self, connection_mock):
        """Test that base event class has all required metadata fields."""
        connection_mock.tenant = None
        event = PaymentsEvent()

        self.assertIsInstance(event.event_id, UUID)
        self.assertIsInstance(event.timestamp, datetime)
        self.assertIsInstance(event.version, str)
        self.assertEqual(event.version, "1.0")
        self.assertIsNone(event.tenant)

    @mock.patch("payments.common.payments_event.connection")
    def test_base_event_with_tenant(self, connection_mock):
        """Test that base event class has all required metadata fields."""
        connection_mock.tenant = Client(
            reference="CLL123",
            name="Test",
            domain_prefix="worksuite",
        )
        event = PaymentsEvent()

        self.assertIsInstance(event.event_id, UUID)
        self.assertIsInstance(event.timestamp, datetime)
        self.assertIsInstance(event.version, str)
        self.assertEqual(event.version, "1.0")
        self.assertEqual(event.tenant, Tenant(reference="CLL123", name="Test", domain_prefix="worksuite"))

    def test_custom_event_id(self):
        """Test that we can set a custom event ID if needed."""
        custom_id = UUID('12345678-1234-5678-1234-************')
        event = PaymentsEvent(event_id=custom_id)
        self.assertEqual(event.event_id, custom_id)

    def test_timestamp_is_utc(self):
        """Test that event timestamps are always in UTC."""
        event = PaymentsEvent()
        self.assertEqual(event.timestamp.tzinfo, UTC)

    def test_concrete_event_inherits_metadata(self):
        """Test that concrete event classes inherit metadata fields."""
        payrun = FakePayrun(uid="123", status="started")
        event = FakePayrunStartedEvent.from_payrun(payrun)

        self.assertIsInstance(event.event_id, UUID)
        self.assertIsInstance(event.timestamp, datetime)
        self.assertEqual(event.version, "1.0")
        self.assertEqual(event.slug, "payments.payrun.started")

        # Check business data
        self.assertEqual(event.payrun_id, "123")
        self.assertEqual(event.status, "started")

    def test_event_serialization(self):
        """Test that events can be properly serialized to JSON."""
        payrun = FakePayrun(uid="123", status="started")
        event = FakePayrunStartedEvent.from_payrun(payrun)

        event_dict = event.model_dump(mode='json')

        self.assertIn("event_id", event_dict)
        self.assertIn("timestamp", event_dict)
        self.assertIn("version", event_dict)
        self.assertIn("payrun_id", event_dict)
        self.assertIn("status", event_dict)

        # Check that timestamp is serialized in ISO format with timezone
        self.assertIsInstance(event_dict["timestamp"], str)
        self.assertTrue(
            '+00:00' in event_dict["timestamp"] or event_dict["timestamp"].endswith('Z'), "Timestamp should include timezone information"
        )

        # Verify we can parse it back
        loaded = FakePayrunStartedEvent.model_validate(event_dict)
        self.assertIsInstance(loaded.timestamp, datetime)
        # Compare timezone names instead of instances
        self.assertEqual(str(loaded.timestamp.tzinfo), str(UTC))

    def test_decimal_serialization(self):
        """Test that Decimal fields are properly serialized."""
        event = ComplexPaymentEvent(
            amount=Decimal("123.45"),
            method=PaymentMethod.BANK,
            due_date=datetime(2024, 1, 1, 12, 0, tzinfo=UTC),
            details=PaymentDetails(reference="TEST-REF", currency="USD", created_at=datetime(2024, 1, 1, 12, 0, tzinfo=UTC)),
        )
        event_dict = event.model_dump()

        self.assertEqual(event_dict["amount"], "123.45")

        # Should deserialize back correctly
        loaded = ComplexPaymentEvent.model_validate(event_dict)
        self.assertEqual(loaded.amount, Decimal("123.45"))

    def test_enum_serialization(self):
        """Test that Enum fields are properly serialized."""
        event = ComplexPaymentEvent(
            amount=Decimal("100"),
            method=PaymentMethod.BANK,
            due_date=datetime.now(UTC),
            details=PaymentDetails(reference="TEST-REF", currency="USD", created_at=datetime.now(UTC)),
        )
        event_dict = event.model_dump()

        self.assertEqual(event_dict["method"], "bank")

        # Should deserialize back correctly
        loaded = ComplexPaymentEvent.model_validate(event_dict)
        self.assertEqual(loaded.method, PaymentMethod.BANK)

    def test_validation_rejects_missing_fields(self):
        """Test that validation fails when required fields are missing."""
        with self.assertRaises(ValidationError):
            FakePayrunStartedEvent()

    def test_validation_rejects_wrong_types(self):
        """Test that validation fails when field types are incorrect."""
        with self.assertRaises(ValidationError):
            FakePayrunStartedEvent(payrun_id="123", status=123)  # status should be str

    def test_validation_forbids_extra_fields(self):
        """Test that extra fields are not allowed."""
        with self.assertRaises(ValidationError):
            ComplexPaymentEvent(
                amount=Decimal("100"),
                method=PaymentMethod.BANK,
                due_date=datetime.now(UTC),
                details=PaymentDetails(reference="TEST-REF", currency="USD", created_at=datetime.now(UTC)),
                extra_field="should fail",  # This should raise an error
            )

    def test_complex_event_with_nested_model(self):
        """Test that events with nested Pydantic models work correctly."""
        payment_details = PaymentDetails(
            reference="INV-123",
            currency="USD",
            description="Test payment",
            metadata={"customer_id": "CUST-456"},
            created_at=datetime(2024, 1, 1, 12, 0, tzinfo=UTC),
        )

        event = ComplexPaymentEvent(
            amount=Decimal("123.45"),
            method=PaymentMethod.BANK,
            due_date=datetime(2024, 1, 1, 12, 0, tzinfo=UTC),
            details=payment_details,
        )

        # Test serialization
        event_dict = event.model_dump()
        self.assertIn("details", event_dict)
        self.assertEqual(event_dict["details"]["reference"], "INV-123")
        self.assertEqual(event_dict["details"]["currency"], "USD")
        self.assertEqual(event_dict["details"]["metadata"]["customer_id"], "CUST-456")

        # Test deserialization
        loaded = ComplexPaymentEvent.model_validate(event_dict)
        self.assertEqual(loaded.details.reference, "INV-123")
        self.assertEqual(loaded.details.currency, "USD")
        self.assertEqual(loaded.details.description, "Test payment")
        self.assertEqual(loaded.details.metadata["customer_id"], "CUST-456")
        self.assertEqual(str(loaded.details.created_at.tzinfo), str(UTC))
