from rest_framework import status

from payments.tests.invoices.api.v1.utils import BaseTestCase, buyer_payments_api_list, buyer_payments_api_detail
from documents.tests.factories import create_payments, create_payment


class PaymentsAPIPerformanceTestCase(BaseTestCase):
    with_login_user = False
    with_admin_user = True

    def test_detail(self):
        payment = create_payment()

        with self.assertNumQueries(34):
            response = buyer_payments_api_detail(self.api_client, payment.pk)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_list(self):
        # no payments yet
        with self.assertNumQueries(10):
            response = buyer_payments_api_list(self.api_client)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # +5 payments
        create_payments(5)

        with self.assertNumQueries(30):
            response = buyer_payments_api_list(self.api_client)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # +5 payments
        create_payments(5)

        with self.assertNumQueries(30):
            response = buyer_payments_api_list(self.api_client)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
