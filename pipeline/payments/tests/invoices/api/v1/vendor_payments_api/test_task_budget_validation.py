from decimal import Decimal

from rest_framework.status import HTTP_200_OK, HTTP_201_CREATED

from clients import features
from documents.models import Payment
from payments.tests.invoices.api.v1.vendor_payments_api.utils import (
    BaseTestCase,
    payment_request_data,
    vendor_payments_api_create,
    vendor_payments_api_update,
)


class CreateTestCase(BaseTestCase):
    def test_line_items_ok_limit_feature_disabled_over_limit(self):
        data = payment_request_data(
            self.task,
            total_amount="1200.01",
            total_amount_without_tax="1000.01",
            total_tax="200",
        )
        expected_payment_data = {
            "total_amount": Decimal("1200.01"),
            "total_amount_without_tax": Decimal("1000.01"),
            "total_tax": Decimal("200"),
            "task": self.task,
        }
        enabled_features = []

        response = self._call_api(data, vendor_payments_api_create, enabled_features)

        self.assertEqual(response.status_code, HTTP_201_CREATED)
        self.assertCorrectPaymentCreated(response.data["id"], expected_payment_data)
        self.assertEqual(Payment.objects.count(), 1)

    def test_line_items_ok_limit_not_set(self):
        data = payment_request_data(
            self.task_no_limit,
            total_amount="1200.01",
            total_amount_without_tax="1000.01",
            total_tax="200",
        )
        expected_payment_data = {
            "total_amount": Decimal("1200.01"),
            "total_amount_without_tax": Decimal("1000.01"),
            "total_tax": Decimal("200"),
            "task": self.task_no_limit,
        }
        enabled_features = [
            features.PROJECTS_AND_TASKS_VALIDATING_PAYMENT_AMOUNT_AND_CURRENCY,
        ]

        response = self._call_api(data, vendor_payments_api_create, enabled_features)

        self.assertEqual(response.status_code, HTTP_201_CREATED)
        self.assertCorrectPaymentCreated(response.data["id"], expected_payment_data)
        self.assertEqual(Payment.objects.count(), 1)

    def test_line_items_ok_limit_zero(self):
        data = payment_request_data(
            self.task_limit_zero,
            total_amount="1200.01",
            total_amount_without_tax="1000.01",
            total_tax="200",
        )
        expected_payment_data = {
            "total_amount": Decimal("1200.01"),
            "total_amount_without_tax": Decimal("1000.01"),
            "total_tax": Decimal("200"),
            "task": self.task_limit_zero,
        }
        enabled_features = [
            features.PROJECTS_AND_TASKS_VALIDATING_PAYMENT_AMOUNT_AND_CURRENCY,
        ]

        response = self._call_api(data, vendor_payments_api_create, enabled_features)

        self.assertEqual(response.status_code, HTTP_201_CREATED)
        self.assertCorrectPaymentCreated(response.data["id"], expected_payment_data)
        self.assertEqual(Payment.objects.count(), 1)

    def test_line_items_ok_no_previous_plus_new(self):
        data = payment_request_data(
            self.task,
            total_amount="1200",
            total_amount_without_tax="1000",
            total_tax="200",
        )
        expected_payment_data = {
            "total_amount": Decimal("1200"),
            "total_amount_without_tax": Decimal("1000"),
            "total_tax": Decimal("200"),
            "task": self.task,
        }
        enabled_features = [
            features.PROJECTS_AND_TASKS_VALIDATING_PAYMENT_AMOUNT_AND_CURRENCY,
        ]

        response = self._call_api(data, vendor_payments_api_create, enabled_features)

        self.assertEqual(response.status_code, HTTP_201_CREATED)
        self.assertCorrectPaymentCreated(response.data["id"], expected_payment_data)
        self.assertEqual(Payment.objects.count(), 1)

    def test_line_items_fail_no_previous_plus_new(self):
        data = payment_request_data(
            self.task,
            total_amount="1200.01",
            total_amount_without_tax="1000.01",
            total_tax="200",
        )
        expected_overrun = Decimal("0.01")
        enabled_features = [
            features.PROJECTS_AND_TASKS_VALIDATING_PAYMENT_AMOUNT_AND_CURRENCY,
        ]

        response = self._call_api(data, vendor_payments_api_create, enabled_features)

        self.assertRelatedTaskOverrunValidationError(response, expected_overrun)
        self.assertEqual(Payment.objects.count(), 0)

    def test_line_items_ok_previous_plus_new(self):
        data_request_1_and_2 = payment_request_data(
            self.task,
            total_amount="450.0",
            total_amount_without_tax="400.0",
            total_tax="50",
        )
        expected_payment_data_1_and_2 = {
            "total_amount": Decimal("450"),
            "total_amount_without_tax": Decimal("400"),
            "total_tax": Decimal("50"),
            "task": self.task,
        }
        data_request_3 = payment_request_data(
            self.task,
            total_amount="200",
            total_amount_without_tax="200.0",
            total_tax="0",
        )
        expected_payment_data_3 = {
            "total_amount": Decimal("200"),
            "total_amount_without_tax": Decimal("200"),
            "total_tax": Decimal("0"),
            "task": self.task,
        }
        enabled_features = [
            features.PROJECTS_AND_TASKS_VALIDATING_PAYMENT_AMOUNT_AND_CURRENCY,
        ]

        payment1_response = self._call_api(data_request_1_and_2, vendor_payments_api_create, enabled_features)
        payment2_response = self._call_api(data_request_1_and_2, vendor_payments_api_create, enabled_features)
        payment3_response = self._call_api(data_request_3, vendor_payments_api_create, enabled_features)

        self.assertCorrectPaymentCreated(payment1_response.data["id"], expected_payment_data_1_and_2)
        self.assertCorrectPaymentCreated(payment2_response.data["id"], expected_payment_data_1_and_2)
        self.assertCorrectPaymentCreated(payment3_response.data["id"], expected_payment_data_3)
        self.assertEqual(Payment.objects.count(), 3)

    def test_line_items_fail_previous_plus_new(self):
        data_request_1_and_2 = payment_request_data(
            self.task,
            total_amount="450.0",
            total_amount_without_tax="400.0",
            total_tax="50.0",
        )
        expected_payment_data_1_and_2 = {
            "total_amount": Decimal("450"),
            "total_amount_without_tax": Decimal("400"),
            "total_tax": Decimal("50"),
            "task": self.task,
        }
        data_request_3 = payment_request_data(
            self.task,
            total_amount="250.01",
            total_amount_without_tax="200.01",
            total_tax="50.0",
        )
        expected_overrun = Decimal("0.01")
        enabled_features = [
            features.PROJECTS_AND_TASKS_VALIDATING_PAYMENT_AMOUNT_AND_CURRENCY,
        ]

        payment1_response = self._call_api(data_request_1_and_2, vendor_payments_api_create, enabled_features)
        payment2_response = self._call_api(data_request_1_and_2, vendor_payments_api_create, enabled_features)
        payment3_response = self._call_api(data_request_3, vendor_payments_api_create, enabled_features)

        self.assertCorrectPaymentCreated(payment1_response.data["id"], expected_payment_data_1_and_2)
        self.assertCorrectPaymentCreated(payment2_response.data["id"], expected_payment_data_1_and_2)
        self.assertRelatedTaskOverrunValidationError(payment3_response, expected_overrun)
        self.assertEqual(Payment.objects.count(), 2)


class UpdateTestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.payment = Payment.objects.create(
            vendor=self.vendor,
            total_amount=500,
            total_amount_without_tax=500,
            total_tax=0,
        )

    def test_line_items_ok(self):
        data = payment_request_data(
            self.task,
            total_amount="1200.01",
            total_amount_without_tax="1000",
            total_tax="200.01",
        )
        data["id"] = self.payment.id
        expected_payment_data = {
            "total_amount": Decimal("1200.01"),
            "total_amount_without_tax": Decimal("1000"),
            "total_tax": Decimal("200.01"),
            "task": self.task,
        }
        enabled_features = [
            features.PROJECTS_AND_TASKS_VALIDATING_PAYMENT_AMOUNT_AND_CURRENCY,
        ]

        response = self._call_api(data, vendor_payments_api_update, enabled_features)

        self.assertEqual(response.status_code, HTTP_200_OK)
        self.assertCorrectPaymentCreated(response.data["id"], expected_payment_data)
        self.assertEqual(Payment.objects.count(), 1)

    def test_line_items_fail(self):
        data = payment_request_data(
            self.task,
            total_amount="1200.01",
            total_amount_without_tax="1000.01",
            total_tax="200",
        )
        data["id"] = self.payment.id
        expected_overrun = Decimal("0.01")
        enabled_features = [
            features.PROJECTS_AND_TASKS_VALIDATING_PAYMENT_AMOUNT_AND_CURRENCY,
        ]

        response = self._call_api(data, vendor_payments_api_update, enabled_features)

        self.assertRelatedTaskOverrunValidationError(response, expected_overrun)
        self.assertEqual(Payment.objects.count(), 1)
