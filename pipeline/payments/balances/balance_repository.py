from payments.balances.model import AccountBalance, CalculatedInvoice
from payments.models import OperAccountBalance
from payments.pay_integration.model import PayAccountBalance


class AccountBalanceRepository:
    def _get_base_queryset(self):
        return OperAccountBalance.objects.order_by("-oper_fetch_date", "-id")

    def create(self, account_balance: AccountBalance) -> AccountBalance:
        dj_instance = OperAccountBalance(
            amount=account_balance.amount,
            currency=account_balance.currency,
            processor_id=account_balance.processor_id,
            oper_fetch_date=account_balance.oper_fetch_date,
        )
        dj_instance.save()
        return django_model_to_pydantic_model(dj_instance)

    def update(self, account_balance: AccountBalance) -> AccountBalance:
        dj_instance = OperAccountBalance.objects.get(id=account_balance.oper_account_balance.id)
        dj_instance.scheduled_amount = account_balance.scheduled_amount
        dj_instance.is_scheduled_currency_match = account_balance.is_scheduled_currency_match
        dj_instance.scheduled_invoices = account_balance.model_dump()["scheduled_invoices"]
        dj_instance.processing_amount = account_balance.processing_amount
        dj_instance.is_processing_currency_match = account_balance.is_processing_currency_match
        dj_instance.processing_invoices = account_balance.model_dump()["processing_invoices"]
        dj_instance.amount = account_balance.amount
        dj_instance.oper_fetch_date = account_balance.oper_fetch_date
        dj_instance.save(
            update_fields=[
                "scheduled_amount",
                "is_scheduled_currency_match",
                "scheduled_invoices",
                "processing_amount",
                "is_processing_currency_match",
                "processing_invoices",
                "amount",
                "oper_fetch_date",
                "updated_at",
            ],
        )
        return django_model_to_pydantic_model(dj_instance)

    def get_latest_by_processor_id(self, processor_id: str, currency: str) -> AccountBalance | None:
        dj_instance = self._get_base_queryset().filter(processor_id=processor_id, currency=currency).first()
        if dj_instance is None:
            return None
        return django_model_to_pydantic_model(dj_instance)

    def get_latest_calculated_by_processor_id(self, processor_id: str, currency: str) -> AccountBalance | None:
        dj_instance = self._get_base_queryset().filter(processor_id=processor_id, currency=currency, scheduled_amount__isnull=False).first()
        if dj_instance is None:
            return None
        return django_model_to_pydantic_model(dj_instance)

    def any_account_balance_exists(self) -> bool:
        return self._get_base_queryset().exists()


def django_model_to_pydantic_model(dj_instance: OperAccountBalance) -> AccountBalance:
    scheduled_invoices = []
    if dj_instance.scheduled_invoices:
        for invoice in dj_instance.scheduled_invoices:
            if invoice:
                scheduled_invoices.append(CalculatedInvoice.factory_from_django_model(invoice))
    processing_invoices = []
    if dj_instance.processing_invoices:
        for invoice in dj_instance.processing_invoices:
            if invoice:
                processing_invoices.append(CalculatedInvoice.factory_from_django_model(invoice))
    return AccountBalance(
        oper_account_balance=PayAccountBalance(
            id=dj_instance.id,
            amount=dj_instance.amount,
            currency=dj_instance.currency,
            processor_id=dj_instance.processor_id,
            oper_fetch_date=dj_instance.oper_fetch_date,
            created_at=dj_instance.created_at,
            updated_at=dj_instance.updated_at,
        ),
        scheduled_amount=dj_instance.scheduled_amount,
        is_scheduled_currency_match=dj_instance.is_scheduled_currency_match,
        scheduled_invoices=scheduled_invoices,
        processing_amount=dj_instance.processing_amount,
        is_processing_currency_match=dj_instance.is_processing_currency_match,
        processing_invoices=processing_invoices,
    )
