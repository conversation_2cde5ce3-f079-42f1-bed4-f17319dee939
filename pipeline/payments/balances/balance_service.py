import logging
from collections.abc import Callable
from datetime import timed<PERSON>ta
from decimal import Decimal

from django.db import connection
from django.db.transaction import atomic

from documents.taxes import round_decimal
from payments.balances.balance_repository import AccountBalanceRepository
from payments.balances.config import BUFFER_FACTOR, STATEMENT_STATIC_TEXTS, RECHARGE_DUE_DATE_IN_DAYS
from payments.balances.exceptions import NonExistingExchangeRateError, ExpiredExchangeRateError, ConvertCurrencyError
from payments.balances.exchange_rate_service import convert_currency, convert_currency_and_get_exchange_rate
from payments.balances.model import (
    AccountBalancesForProcessor,
    AccountBalanceSummary,
    CalculatedInvoice,
    AccountBalanceStatementForProcessor,
    ProcessorLabel,
    AccountBalanceStatementSummary,
    ProcessorLabelStyle,
    AccountBalance,
)
from payments.balances.utils import get_scheduled_payruns
from payments.common.model import PaymentsSystemActor
from payments.config import has_funding_method_customer_transfer
from payments.invoices.model import Invoice, InvoiceStatus
from payments.invoices.repositories.factory import factory_repository
from payments.invoices.search.model import SearchQuery
from payments.pay_integration.model import PayAccountBalance
from payments.pay_integration.pay_account_balance_fetcher import PayAccountBalanceFetcher
from payments.pull_syncs.fetchers import FetcherQuery
from payments.pull_syncs.model import (
    ProcessingResult,
    SyncConfig,
    SyncResult,
    SyncResultStatus,
)
from payments.pull_syncs.processors import PullSyncProcessor
from payments.pull_syncs.repository import PullSyncRepository
from payments.pull_syncs.service import PullSync
from shortlist.lock import cache_lock

logger = logging.getLogger(__name__)


class FetchAccountBalancesProcessor(PullSyncProcessor):
    slug = "fetch_account_balances_and_calculate"

    def __init__(self, account_balance_service):
        self.account_balance_service = account_balance_service

    @atomic
    def process(self, records: list[PayAccountBalance]) -> list[ProcessingResult]:
        processed = []
        for oper_account_balance in records:
            existing_balance = self.account_balance_service.get_latest_account_balance_for_processor(
                oper_account_balance.processor_id, oper_account_balance.currency
            )
            if existing_balance and existing_balance.amount == oper_account_balance.amount:
                oper_account_balance.id = existing_balance.oper_account_balance.id
                account_balance = AccountBalance(oper_account_balance=oper_account_balance)
                self.account_balance_service.update(account_balance)
            else:
                account_balance = AccountBalance(oper_account_balance=oper_account_balance)
                self.account_balance_service.create(account_balance)
            single_result = ProcessingResult.success(str(oper_account_balance.model_dump()))
            processed.append(single_result)
        return processed


class FetchAccountBalances(PullSync):
    def get_slug(self) -> str:
        return "fetch-account-balances"

    def get_lock_key(self) -> str:
        return "fetch-account-balances"

    def get_fetcher_query(self, sync_config: SyncConfig) -> FetcherQuery:
        return FetcherQuery(processor_id=self._payment_processor.processor_id, filters={}, sort_by="")

    def execute(self) -> SyncResult:
        payment_processor = self.context["tenant"].shortlist_pay.get_moneycorp_payment_processor()
        if not payment_processor:
            return SyncResult(slug=self.slug, status=SyncResultStatus.DISABLED)
        self._payment_processor = payment_processor
        return super().execute()


def fetch_account_balances_and_calculate(
    *,
    tenant=None,
    repository=None,
    balance_service=None,
    balance_repository=None,
    funding_fetcher=None,
    post_execute_actions: list[Callable[[], None]] = None,
) -> SyncResult | None:
    tenant = tenant or connection.tenant
    if not has_funding_method_customer_transfer(tenant):
        return None
    sync = FetchAccountBalances(
        repository=repository or PullSyncRepository(),
        fetcher=funding_fetcher or PayAccountBalanceFetcher(pay_client=tenant.shortlist_pay),
        context={"tenant": tenant},
        lock=cache_lock,
    )
    account_balance_repository = balance_repository or AccountBalanceRepository()
    account_balance_service = balance_service or AccountBalanceService(account_balance_repository, tenant)
    fetch_account_balances_processor = FetchAccountBalancesProcessor(account_balance_service)
    sync.add_processor(fetch_account_balances_processor)
    execution_result = sync.execute()

    # Edge case - there are no records fetched from external API.
    # This case happen only for new tenants, when MC doesn't create balances on their side yet.
    # In this case we want to create a default entry with 0 USD, but only when we don't have any balance stored yet.
    if not account_balance_service.any_account_balance_exists():
        mc_processor = tenant.shortlist_pay.get_moneycorp_payment_processor()
        if mc_processor:
            zero_account_balance = PayAccountBalance.factory_zero_account_balance(mc_processor.processor_id, "USD")
            account_balance_service.create(AccountBalance(oper_account_balance=zero_account_balance))

    if post_execute_actions is None:
        # Default action
        post_execute_actions = [calculate_remaining_balance]

    for single_action in post_execute_actions:
        single_action()

    return execution_result


class AccountBalanceService:
    def __init__(self, repository: AccountBalanceRepository, tenant=None):
        self.tenant = tenant or connection.tenant
        self.repository = repository

    def create(self, account_balance: AccountBalance) -> AccountBalance:
        instance = self.repository.create(account_balance)
        return instance

    def update(self, account_balance: AccountBalance) -> AccountBalance:
        instance = self.repository.update(account_balance)
        return instance

    def get_latest_account_balance_for_processor(self, processor_id: str, currency: str) -> AccountBalance | None:
        instance = self.repository.get_latest_by_processor_id(processor_id, currency)
        return instance

    def get_latest_calculated_for_processor(self, processor_id: str, currency: str) -> AccountBalance | None:
        calculated = self.repository.get_latest_calculated_by_processor_id(processor_id, currency)
        return calculated

    def any_account_balance_exists(self) -> bool:
        return self.repository.any_account_balance_exists()

    def get_summary_calculated(self, payment_processors_schema: dict[str, dict[str, list[str]]]) -> list[AccountBalanceSummary]:
        payruns = get_scheduled_payruns(self.tenant)
        account_balance_summaries = []
        for processor_name, processor_data in payment_processors_schema.items():
            account_balances_for_processor = []
            for processor_id, currencies in processor_data.items():
                if not self._check_if_there_are_any_invoices(processor_id):
                    continue
                account_balances = []
                for currency in currencies:
                    calculated = self.repository.get_latest_calculated_by_processor_id(processor_id, currency)
                    if calculated:
                        calculated.scheduled_invoices = []  # We don't want to expose details of invoices here
                        calculated.processing_invoices = []  # We don't want to expose details of invoices here
                        account_balances.append(calculated)
                account_balances_for_processor.append(
                    AccountBalancesForProcessor(processor_id=processor_id, account_balances=account_balances)
                )
            account_balance_summaries.append(
                AccountBalanceSummary(
                    processor_name=processor_name,
                    next_payrun_date=payruns[1].date,
                    previous_payrun_date=payruns[0].date,
                    account_balances_for_processor=account_balances_for_processor,
                )
            )
        return account_balance_summaries

    def get_statement_context(self, payment_processors_schema: dict) -> list[AccountBalanceStatementSummary]:
        payruns = get_scheduled_payruns(self.tenant)
        account_balance_summaries = []
        for processor_name, processor_data in payment_processors_schema.items():
            account_balances_for_processor = []
            display_exchange_fluctuations_banner = False
            for processor_id, processor_details in processor_data.items():
                if not self._check_if_there_are_any_invoices(processor_id):
                    continue
                account_balances = []
                recharge_amount_details = []
                for currency in processor_details["currencies"]:
                    calculated = self.repository.get_latest_calculated_by_processor_id(processor_id, currency)
                    if not calculated:
                        continue
                    account_balances.append(calculated)
                    recharge_amount_details.append(
                        ProcessorLabel(
                            label="Necessary Recharge Amount",
                            value=f"{calculated.missing_balance:,} {calculated.currency}",
                            value_style=ProcessorLabelStyle.x_large,
                        )
                    )
                    if calculated.missing_balance == 0:
                        recharge_amount_details.append(ProcessorLabel(label="", value="No Recharge Needed"))
                    else:
                        due_date = payruns[1].date - timedelta(days=RECHARGE_DUE_DATE_IN_DAYS)
                        recharge_amount_details.append(
                            ProcessorLabel(
                                label="Recharge Due Date",
                                value=due_date.strftime("%Y-%m-%d"),
                                value_style=ProcessorLabelStyle.large,
                            )
                        )
                        if processor_details["oper_account"]:
                            recharge_amount_details.append(
                                ProcessorLabel(
                                    label="Quote This Reference In Your Transfer",
                                    value=processor_details["oper_account"].get("account_reference"),
                                    value_style=ProcessorLabelStyle.large,
                                )
                            )
                    if calculated.is_processing_currency_match is False or calculated.is_scheduled_currency_match is False:
                        display_exchange_fluctuations_banner = True
                account_balances_for_processor.append(
                    AccountBalanceStatementForProcessor(
                        processor_id=processor_id,
                        account_balances=account_balances,
                        recharge_amount_details=recharge_amount_details,
                    )
                )
            if display_exchange_fluctuations_banner:
                processor_exchange_fluctuations_banner = STATEMENT_STATIC_TEXTS[processor_name]["processor_exchange_fluctuations_banner"]
            else:
                processor_exchange_fluctuations_banner = None
            account_balance_summaries.append(
                AccountBalanceStatementSummary(
                    processor_name=processor_name,
                    processor_subtitle=STATEMENT_STATIC_TEXTS[processor_name]["processor_subtitle"],
                    processor_exchange_fluctuations_banner=processor_exchange_fluctuations_banner,
                    funding_account_details=STATEMENT_STATIC_TEXTS[processor_name]["funding_account_details"],
                    next_payrun_date=payruns[1].date,
                    previous_payrun_date=payruns[0].date,
                    account_balances_statement_for_processor=account_balances_for_processor,
                )
            )
        return account_balance_summaries

    def calculate_available_balance_by_processor_id(
        self,
        payment_processor_id: str,
        currency: str,
        *,
        store_invoice_data=False,
    ) -> AccountBalance | None:
        balance = self.get_latest_account_balance_for_processor(payment_processor_id, currency)
        if not balance:
            return None

        scheduled_invoices = self._get_invoices_scheduled_for_nearest_payrun(payment_processor_id)
        try:
            scheduled_amount, is_scheduled_currency_match, calculated_scheduled_invoices = self._calculate_invoices_amount(
                scheduled_invoices, currency, store_invoice_data=store_invoice_data
            )
        except ConvertCurrencyError:
            return None
        balance.scheduled_amount = scheduled_amount
        balance.is_scheduled_currency_match = is_scheduled_currency_match
        balance.scheduled_invoices = calculated_scheduled_invoices

        processing_invoices = self._get_invoices_processed_by_latest_payrun(payment_processor_id)
        try:
            processing_amount, is_processing_currency_match, calculated_processing_invoices = self._calculate_invoices_amount(
                processing_invoices, currency, store_invoice_data=store_invoice_data
            )
        except ConvertCurrencyError:
            return None
        balance.processing_amount = processing_amount
        balance.is_processing_currency_match = is_processing_currency_match
        balance.processing_invoices = calculated_processing_invoices
        balance.processing_invoices = calculated_processing_invoices
        self.update(balance)
        return balance

    def _calculate_invoices_amount(
        self,
        invoices: list[Invoice],
        base_currency: str,
        *,
        store_invoice_data: bool = False,
    ) -> tuple[Decimal, bool, list[CalculatedInvoice]]:
        """
        Takes a list of Invoices and a base currency and calculates the summarized value of given Invoices in base currency.
        Returns a tuple which contains
         - that summarized value
         - a flag that indicates wheter any of given Invoices had amount in currency different than the base currency.
         - a list of CalculatedInvoice objects if store_invoice_data parameter was set to True
        """
        calculated_amount = 0
        # Initially is_currency_match is set to True, and if currency check fails later, it will be set to False
        is_currency_match = True
        calculated_invoices = []

        for invoice in invoices:
            exchange_rate = None
            if invoice.currency == base_currency:
                amount_to_add = invoice.amount_with_tax
            else:
                try:
                    if store_invoice_data:
                        amount_to_add, exchange_rate = convert_currency_and_get_exchange_rate(
                            base_currency, invoice.currency, invoice.amount_with_tax
                        )
                    else:
                        amount_to_add = convert_currency(base_currency, invoice.currency, invoice.amount_with_tax)
                except (NonExistingExchangeRateError, ExpiredExchangeRateError):
                    # We skip calculating balance when rate does not exist.
                    raise ConvertCurrencyError()

            currency_matched = self.check_currency_match(base_currency, invoice.currency)

            if store_invoice_data:
                calculated_invoices.append(
                    CalculatedInvoice(
                        invoice_id=invoice.invoice_id,
                        title=invoice.title,
                        partner_name=invoice.vendor.name,
                        partner_id=invoice.vendor.id,
                        original_amount=invoice.amount_with_tax,
                        original_currency=invoice.currency,
                        converted_amount=self.get_invoice_amount_for_funding(amount_to_add, currency_matched),
                        converted_amount_no_buffer=round_decimal(amount_to_add),
                        converted_currency=base_currency,
                        exchange_rate=round_decimal(exchange_rate, decimal_places=4) if exchange_rate else None,
                    )
                )

            if not currency_matched:
                is_currency_match = False  # Once set to False it cannot be set to True again
                amount_to_add = self.get_invoice_amount_for_funding(amount_to_add, False)
            calculated_amount += amount_to_add

        return round_decimal(calculated_amount, 2), is_currency_match, calculated_invoices

    @staticmethod
    def check_currency_match(a: str, b: str) -> bool:
        return a == b

    @staticmethod
    def get_invoice_amount_for_funding(amount: Decimal, currency_match: bool) -> Decimal:
        return round_decimal(amount) if currency_match else round_decimal(amount * BUFFER_FACTOR)

    def _get_invoices_scheduled_for_nearest_payrun(self, payment_processor_id: str) -> list[Invoice]:
        payruns = get_scheduled_payruns(self.tenant)
        search_query = SearchQuery(
            filters=[
                {"status": InvoiceStatus.SCHEDULED},
                {"scheduled_for": payruns[1].date.isoformat()},
                {"account_processor_id": payment_processor_id},
            ],
            size=1000000,
        )
        invoice_repository = factory_repository(PaymentsSystemActor())
        invoices = invoice_repository.search(PaymentsSystemActor(), search_query)
        return invoices

    def _get_invoices_processed_by_latest_payrun(self, payment_processor_id: str) -> list[Invoice]:
        payruns = get_scheduled_payruns(self.tenant)
        search_query = SearchQuery(
            filters=[
                {"status": InvoiceStatus.PROCESSING},
                {"scheduled_for": payruns[0].date.isoformat()},
                {"account_processor_id": payment_processor_id},
            ],
            size=1000000,
        )
        invoice_repository = factory_repository(PaymentsSystemActor())
        invoices = invoice_repository.search(PaymentsSystemActor(), search_query)
        return invoices

    def _check_if_there_are_any_invoices(self, payment_processor_id: str) -> bool:
        # In case when there are no invoices for given account_processor_id we don't want to show calculated balance.
        invoice_repository = factory_repository(PaymentsSystemActor())
        search_query_for_all_invoices = SearchQuery(filters=[{"account_processor_id": payment_processor_id}])
        all_invoices_count = invoice_repository.count(PaymentsSystemActor(), search_query_for_all_invoices)
        return all_invoices_count > 0


def calculate_remaining_balance(
    *,
    tenant=None,
    payment_processor=None,
    account_balance_repository=None,
    currency=None,
    store_invoice_data=False,
):
    tenant = tenant or connection.tenant
    if not has_funding_method_customer_transfer(tenant):
        return None
    processor = payment_processor or tenant.shortlist_pay.get_moneycorp_payment_processor()
    currency = currency or "USD"
    account_balance_service = AccountBalanceService(
        repository=account_balance_repository or AccountBalanceRepository(),
        tenant=tenant,
    )
    account_balance_service.calculate_available_balance_by_processor_id(
        processor.processor_id,
        currency,
        store_invoice_data=store_invoice_data,
    )


def calculate_remaining_balance_and_store_invoice_data(
    *,
    tenant=None,
    payment_processor=None,
    account_balance_repository=None,
    currency=None,
):
    calculate_remaining_balance(
        tenant=tenant,
        payment_processor=payment_processor,
        account_balance_repository=account_balance_repository,
        currency=currency,
        store_invoice_data=True,
    )
