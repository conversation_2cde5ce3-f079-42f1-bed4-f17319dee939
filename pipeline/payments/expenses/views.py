from django.db import connection
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.exceptions import NotFound
from rest_framework.response import Response
from rest_framework.viewsets import ViewSet

from clients import features
from payments.common.api.serializers import ExpenseSerializer, ExpenseCategorySerializer, ExpenseApproverSerializer
from payments.common.model import PaymentsActor
from payments.expenses.actions import actions_service
from payments.expenses.permissions import (
    has_expense_global_perm_drf,
    ExpensePermissionActions,
    VendorCanCreateExpensePermission,
    BuyerCanCreateExpensePermission,
    VendorCanUpdateExpensePermission,
    BuyerCanUpdateExpensePermission,
)
from payments.expenses.service import ExpenseService
from payments.models import ExpenseCategory, Expense
from shortlist.helpers import global_link
from shortlist.permissions import <PERSON><PERSON><PERSON>er, IsVendor, TenantFeaturesEnabled, Permission
from shortlist.utils import serve_file, filepicker_policy
from users.models import User


class ExpensesViewSet(ViewSet):
    permission_classes = [
        (IsBuyer | IsVendor)
        & TenantFeaturesEnabled(features.EXPENSES)
        & has_expense_global_perm_drf(ExpensePermissionActions.VIEW)
        & has_expense_global_perm_drf(ExpensePermissionActions.LIST)
    ]
    lookup_field = "expense_id"

    def get_permissions(self):
        extra_permissions = []
        if self.action == 'create':
            extra_permissions.append(VendorCanCreateExpensePermission)
            extra_permissions.append(BuyerCanCreateExpensePermission)
        if self.action == 'partial_update':
            extra_permissions.append(VendorCanUpdateExpensePermission)
            extra_permissions.append(BuyerCanUpdateExpensePermission)
        return [permission() for permission in self.permission_classes + extra_permissions]

    def get_payments_actor(self):
        if not hasattr(self, "_payments_actor"):
            self._payments_actor = PaymentsActor(user=self.request.user)
        return self._payments_actor

    def get_object(self, request=None, expense_id=None):
        expense = ExpenseService().get(self.get_payments_actor(), expense_id=expense_id)
        if not expense:
            raise NotFound(detail=f"Expense {expense_id} not found")
        return expense

    def retrieve(self, request, expense_id=None):
        expense = self.get_object(request, expense_id)
        actions_availability = actions_service.get_expenses_actions_availability(connection.tenant, self.get_payments_actor(), [expense])
        result = ExpenseSerializer(expense, context={"actions_availability": actions_availability})
        return Response(result.data)

    def create(self, request, *args, **kwargs):
        payments_actor = self.get_payments_actor()

        expense = ExpenseService().create(
            payments_actor,
            request.data,
            context={"request": self.request},
        )

        if not expense:
            return Response(status=status.HTTP_403_FORBIDDEN)

        actions_availability = actions_service.get_expenses_actions_availability(connection.tenant, self.get_payments_actor(), [expense])

        return Response(
            ExpenseSerializer(expense, context={"actions_availability": actions_availability}).data,
            status=status.HTTP_201_CREATED,
        )

    def partial_update(self, request, *args, **kwargs):
        payments_actor = self.get_payments_actor()

        expense_id = kwargs[self.lookup_field]

        updated_expense = ExpenseService().update(
            payments_actor,
            expense_id,
            request.data,
            context={"request": self.request},
        )

        if not updated_expense:
            return Response(status=status.HTTP_403_FORBIDDEN)

        actions_availability = actions_service.get_expenses_actions_availability(
            connection.tenant, self.get_payments_actor(), [updated_expense]
        )

        return Response(
            ExpenseSerializer(updated_expense, context={"actions_availability": actions_availability}).data,
            status=status.HTTP_200_OK,
        )

    @action(detail=True, methods=["get"])
    def attachment(self, request, expense_id=None):
        expense = self.get_object(request, expense_id=expense_id)
        return serve_file(expense.filename, expense.file, inline='inline' in request.query_params)

    @global_link(methods=["get"])
    def categories(self, request):
        expense_categories = ExpenseCategory.objects.all()
        return Response(ExpenseCategorySerializer(expense_categories, many=True).data)

    @global_link(methods=["get"])
    def approvers(self, request, *args, **kwargs):
        users = User.objects.all_users_with_permission(
            Permission.buyer('expense', 'set approved'), is_active=True, deleted=False, is_staff=False
        )
        return Response(ExpenseApproverSerializer(users, many=True).data)

    @global_link(methods=["get"])
    def upload_file_config(self, request):
        policy = filepicker_policy(Expense.get_upload_path())
        return Response(policy)
