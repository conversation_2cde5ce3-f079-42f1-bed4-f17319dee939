import os
from datetime import date
from io import BytesIO
from random import randrange
from tempfile import NamedTemporaryFile

import pysftp
from django.conf import settings

from events.event_types import MoneyCorpSFTPTestFailed


class MoneyCorpSFTPError(Exception):
    pass


def check_sftp_connection_moneycorp(raise_on_error=False):
    sftp_config = settings.MONEYCORP_ENVIRONMENT.get("sftp", {})
    enabled = sftp_config.get("enabled")
    if not enabled:
        return False

    hostname = sftp_config["host"]
    username = sftp_config["username"]
    password = sftp_config["password"]
    host_key = sftp_config["host_key"]
    directory = sftp_config.get("directory") or "/"

    with NamedTemporaryFile(delete=False) as knownhosts:
        knownhosts.write(f"{hostname} ssh-rsa {host_key}".encode())
    cnopts = pysftp.CnOpts(knownhosts=knownhosts.name)
    os.unlink(knownhosts.name)

    current_date = date.today().strftime("%Y%m%d")
    remote_dir = directory + f"shortlist-connection-test-{settings.PUBLIC_SCHEMA_DOMAIN}-{current_date}-{randrange(1e5, 9e5)}"
    remote_path = remote_dir + "/test.sp"
    result = True
    msg = ""

    try:
        with pysftp.Connection(hostname, username=username, password=password, cnopts=cnopts) as sftp:
            try:
                sftp.mkdir(remote_dir)
            except Exception as e:
                result = False
                msg = str(e)
            else:
                try:
                    sftp.putfo(BytesIO(b"test"), remote_path)
                except Exception as e:
                    result = False
                    msg = str(e)
                try:
                    sftp.rmdir(remote_dir)
                except Exception as e:
                    result = False
                    # Persist the first message
                    msg = msg or str(e)
    except Exception as e:
        result = False
        msg = str(e)

    if not result:
        MoneyCorpSFTPTestFailed(message=msg)
        if raise_on_error:
            raise MoneyCorpSFTPError(msg)

    return result
