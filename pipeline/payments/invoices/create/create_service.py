import logging
from collections.abc import Callable

from django.db import transaction

from budgets.tracking.api import validate_add_invoices_spending
from payments.common.model import InvoiceVendor, PaymentsActor, ServiceResultStatus, RawTask
from payments.invoices.actions import actions_service
from payments.invoices.actions.model import ActionRequest, InvoiceAction
from payments.invoices.audit_integration import save_audit_log
from payments.invoices.create.factories import factory_invoice_to_create
from payments.invoices.create.model import BulkCreateResults, CreateInvoiceResult
from payments.invoices.events_integration import (
    default_external_integrations,
    skip_external_integrations,
)
from payments.invoices.internal_integrations import default_internal_integrations
from payments.invoices.model import (
    Event,
    Invoice,
    InvoiceStatus,
    PaymentCreatedEvent,
)
from payments.invoices.reference_integration import generate_invoice_id
from payments.invoices.repositories.exceptions import NotFound, RepositoryError
from payments.invoices.repositories.factory import factory_repository
from payments.invoices.repositories.tasks import InvoiceRelatedTaskRepository
from payments.vendor_payable.repository import InvoiceVendorRepository

logger = logging.getLogger(__name__)


def validate_invoices(tenant, payments_actor: PaymentsActor, invoice_create_requests: list[Invoice]) -> BulkCreateResults:
    tasks_repository = InvoiceRelatedTaskRepository()
    vendors_repository = InvoiceVendorRepository()

    results: dict[int, CreateInvoiceResult] = {}

    prefetched_vendors: dict[int, InvoiceVendor] = {
        invoice_vendor.id: invoice_vendor
        for invoice_vendor in vendors_repository.get_by_ids([i.vendor.id for i in invoice_create_requests])
    }
    prefetched_related_tasks: dict[int, RawTask] = {
        related_task.id: related_task
        for related_task in tasks_repository.get_by_ids([i.related_task.id for i in invoice_create_requests if i.related_task])
    }

    invoices_to_be_approved: list[Invoice] = []
    invoices_to_be_approved_map_to_create: dict[str, int] = {}
    for idx, invoice in enumerate(invoice_create_requests):
        to_be_approved = invoice.status == InvoiceStatus.APPROVED
        validate_new_invoice_result = factory_invoice_to_create(
            payments_actor,
            invoice,
            tenant=tenant,
            invoice_id=str(idx),  # For validation purposes we don't need to generate SuperID
            vendor=prefetched_vendors[invoice.vendor.id],
            related_task=prefetched_related_tasks[invoice.related_task.id] if invoice.related_task else None,
        )
        if validate_new_invoice_result.status != ServiceResultStatus.SUCCESS:
            results[idx] = validate_new_invoice_result
            continue

        if to_be_approved:
            invoices_to_be_approved.append(validate_new_invoice_result.invoice)
            invoices_to_be_approved_map_to_create[validate_new_invoice_result.invoice.invoice_id] = idx
        else:
            results[idx] = validate_new_invoice_result

    # OK, we're done with validation for new invoices, now check if actions can be applied too.
    # For that we need much more related information about Invoice,
    # and it'll be harder to perform, but we can try...
    if invoices_to_be_approved:
        budgets_spending_validation = validate_add_invoices_spending(invoices_to_be_approved)
        for result in budgets_spending_validation.results:
            idx = invoices_to_be_approved_map_to_create[result.invoice.value.invoice_id]
            if result.is_success:
                results[idx] = CreateInvoiceResult.success(result.invoice.value)
            else:
                results[idx] = CreateInvoiceResult.error({"non_field_errors": result.invoice.errors_dump()})

    bulk_create_results = BulkCreateResults(actor=payments_actor)
    # Add results in the same order as they were passed within `invoice_create_requests`:
    bulk_create_results.add_results([v for _, v in sorted(results.items())])
    return bulk_create_results


def create_invoice(
    tenant,
    payments_actor: PaymentsActor,
    invoice: Invoice,
    *,
    invoice_id_factory: Callable[[], str] = None,
    internal_integrations: Callable[[Event], None] | None = None,
    external_integrations: Callable[[Event], None] | None = None,
    invoice_repository=None,
    vendor_repository=None,
) -> CreateInvoiceResult:
    """Create invoice."""
    if invoice_repository is None:
        invoice_repository = factory_repository(payments_actor)
    if vendor_repository is None:
        vendor_repository = InvoiceVendorRepository()
    if internal_integrations is None:
        internal_integrations = default_internal_integrations
    if external_integrations is None:
        external_integrations = default_external_integrations
    if invoice_id_factory is None:
        invoice_id_factory = generate_invoice_id
    tasks_repository = InvoiceRelatedTaskRepository()

    # Resolve dependencies:
    invoice_vendor = vendor_repository.get_by_id(invoice.vendor.id)
    if not invoice_vendor:
        return CreateInvoiceResult.error({"vendor": [{"type": "invalid_vendor"}]})

    related_task = tasks_repository.get_by_id(invoice.related_task.id) if invoice.related_task else None

    validation_result = factory_invoice_to_create(
        payments_actor,
        invoice,
        tenant=tenant,
        invoice_id=invoice_id_factory(),
        vendor=invoice_vendor,
        related_task=related_task,
    )
    if validation_result.status != ServiceResultStatus.SUCCESS:
        return validation_result

    invoice = validation_result.invoice

    payment_to_approve = False
    if invoice.status == InvoiceStatus.APPROVED:
        payment_to_approve = True

    domain_events: list[Event] = []
    with transaction.atomic():
        try:
            invoice = invoice_repository.create(payments_actor, invoice)
        except NotFound:
            # We assume here that person who can't access created invoice
            # can't create it also, so performing rollback here.
            # This should be changed when we introduce better permission checks
            # into this service.
            transaction.set_rollback(True)
            return CreateInvoiceResult.error({"non_field_errors": [{"type": "no_permission"}]})
        except RepositoryError:
            logger.exception("invoices.create.create_service:repository_error")
            transaction.set_rollback(True)
            return CreateInvoiceResult.error({"non_field_errors": {"type": "data_consistency_error"}})
        save_audit_log(payments_actor, [PaymentCreatedEvent(invoice=invoice, actor=payments_actor)])
        if payment_to_approve:
            action_result = actions_service.single_action(
                tenant,
                payments_actor,
                invoice,
                ActionRequest(action=InvoiceAction.APPROVE),
                external_integrations=skip_external_integrations,
            )
            if action_result.status == ServiceResultStatus.SUCCESS:
                invoice = invoice_repository.get(payments_actor, invoice.id)
                created_event = PaymentCreatedEvent(invoice=invoice, actor=payments_actor)
                domain_events.append(created_event)
                if action_result.domain_events:
                    domain_events.extend(action_result.domain_events)
            else:
                transaction.set_rollback(True)
                return CreateInvoiceResult.error({"non_field_errors": action_result.errors})
        else:
            created_event = PaymentCreatedEvent(invoice=invoice, actor=payments_actor)
            domain_events.append(created_event)

    transaction.on_commit(lambda: internal_integrations(domain_events))
    transaction.on_commit(lambda: external_integrations(domain_events))
    return CreateInvoiceResult.success(invoice)
