from collections import defaultdict
from decimal import Decimal

from django.db import connection
from django.db.models import Case, Sum, When
from rest_framework import serializers

from clients import features
from documents.models import Payment
from payments.common.api.pydantic_drf import ROOT_KEY_DRF
from payments.common.model import PayoutMethodPublic
from payments.config import has_payment_processing_enabled
from payments.invoices.model.invoice_calculator import calculate_line_item_amounts
from payments.invoices.specifications import is_triggered_by_contracts_or_timesheets
from shortlist.currencies import tenant_currency, ZERO_DECIMAL_CURRENCY_LIST
from shortlist.permissions import Permission


class TaskBudgetValidator:
    requires_context = True
    OVERRUN_ERROR = "Invoice subtotal exceeds {} budget by {:.2f} {}"

    def __call__(self, attrs, serializer_field):
        instance = getattr(serializer_field, "instance", None)
        request_user = serializer_field.context["request"].user

        if not connection.tenant.has_features(features.PROJECTS_AND_TASKS_VALIDATING_PAYMENT_AMOUNT_AND_CURRENCY):
            return

        related_task = attrs.get("related_task", None)
        if not related_task:
            return
        if not related_task.budget_total:
            return

        if request_user.has_perm(Permission.buyer('task', 'exceed budget')):
            return

        total_amount = attrs.get('total_amount_without_tax', 0)

        payments = Payment.objects.filter(related_task=related_task).exclude(status=Payment.status_code(Payment.STATUS_REJECTED))
        if instance and instance.pk is not None:
            payments = payments.exclude(pk=instance.pk)

        budget_used = (
            payments.aggregate(
                total_amount=Sum(
                    Case(
                        When(total_amount_without_tax__isnull=True, then='total_amount'),
                        When(total_amount_without_tax__isnull=False, then='total_amount_without_tax'),
                        default=Decimal(0),
                    ),
                ),
            )['total_amount']
            or 0
        )
        budget_overrun = total_amount + budget_used - related_task.budget_total
        if budget_overrun > 0:
            raise serializers.ValidationError(
                {
                    'related_task': self.OVERRUN_ERROR.format(
                        connection.tenant.tenant_terms['task'],
                        budget_overrun,
                        related_task.currency,
                    ),
                },
            )


class CompatValidator:
    """Base validator class to allow to customize exception class with validation errors.

    Can be used either with django or drf ValidationError's."""

    def __init__(self, *, exc_class=None, non_field_error_key=None):
        if exc_class is None:
            exc_class = serializers.ValidationError
        if non_field_error_key is None:
            non_field_error_key = ROOT_KEY_DRF
        self.exc_class = exc_class
        self.non_field_error_key = non_field_error_key


REASON_BLOCKED_BY_TRIGGERED_BY = "blocked by trigger"
REASON_BLOCKED_BY_LINKED_TIMESHEETS = "blocked by linked timesheet"
REASON_BAD_INVOICE_STATUS = "bad status"

FIELD_NOT_EDITABLE_ERROR_MSG = "Value cannot be changed"
LINE_ITEMS_ADDING_NOT_ALLOWED = "Adding items to invoice is not allowed"
INVOICE_REMOVING_NOT_ALLOWED = "Removing invoice is not allowed"


class InvoiceTriggeredByValidator(CompatValidator):
    requires_context = True

    def __call__(self, request_data, serializer_field, instance=None):
        payment = instance or getattr(serializer_field, "instance", None)
        if not payment:
            return
        if not is_triggered_by_contracts_or_timesheets(payment):
            return
        errors = validate_fields_not_editable(
            payment,
            request_data,
            ["vendor", "total_amount", "total_amount_without_tax", "total_tax", "currency", "related_task"],
            reason=REASON_BLOCKED_BY_TRIGGERED_BY,
        )
        if errors:
            raise self.exc_class(errors)


class LineItemTriggeredByValidator(CompatValidator):
    requires_context = True
    not_editable_fields = ("quantity", "unit_price", "tax_percent", "tax_label")

    def __call__(self, request_data, serializer, instance=None):
        work_item = instance or getattr(serializer, "instance", None)

        payment = request_data.get("payment") or (work_item and work_item.payment) or (serializer and serializer.context.get("payment"))
        if not payment:
            return
        if not is_triggered_by_contracts_or_timesheets(payment):
            return
        reason = REASON_BLOCKED_BY_TRIGGERED_BY
        if not work_item:
            raise self.exc_class(f"{LINE_ITEMS_ADDING_NOT_ALLOWED} ({reason})")
        errors = validate_fields_not_editable(
            work_item,
            request_data,
            self.not_editable_fields,
            reason=reason,
        )
        if errors:
            raise self.exc_class(errors)


class LineItemTriggeredByPublicAPIValidator(LineItemTriggeredByValidator):
    not_editable_fields = ("quantity", ("unit_price", "price"), "tax_percent", "tax_label")


class InvoiceValidationError:
    CURRENCY_NOT_SUPPORTED_FOR_TENANT = "Invalid currency: {currency}"
    PAYOUT_METHOD_PAYMENT_PROCESSOR_REMOVED = (
        "This invoice can't be paid with the currently selected Payout Method. Please update Primary Payout Method."
    )
    PAYOUT_METHOD_CURRENCY_NOT_PAYABLE = (
        "This invoice can't be paid with the currently selected Payout Method. "
        "Please switch the currency to {payable_currencies} or change Primary Payout Method."
    )


class InvoicePayableCurrencyValidator(CompatValidator):
    requires_context = True

    def __call__(self, request_data, serializer_field, instance=None):
        invoice = instance or getattr(serializer_field, "instance", None)

        errors = validate_currency_is_payable(
            request_data.get("vendor", invoice.vendor if invoice else None),
            request_data.get("currency", invoice.currency if invoice else None),
            tenant=serializer_field.context.get("tenant") if serializer_field else None,
        )

        if errors:
            # Map errors for django compatibility:
            errors_map = {
                "payout_method_processor_removed": lambda error: InvoiceValidationError.PAYOUT_METHOD_PAYMENT_PROCESSOR_REMOVED,
                "payout_method_currency_not_payable": lambda error: InvoiceValidationError.PAYOUT_METHOD_CURRENCY_NOT_PAYABLE.format(
                    payable_currencies=', '.join(error["payable_currencies"])
                ),
            }
            currency_errors = [errors_map.get(error["type"])(error) for error in errors]
            raise self.exc_class({"currency": currency_errors})


def validate_currency_is_payable(vendor, currency: str, *, tenant=None) -> list[dict]:
    if tenant is None:
        tenant = connection.tenant
    if not currency:
        # There are some edge cases where currency is not provided by frontend.
        # In that case default currency is used.
        return []
    if not vendor:
        # In case vendor not yet established, do not validate.
        return []
    if not has_payment_processing_enabled(tenant):
        return []
    primary_payout_method = vendor.primary_payout_method
    if not primary_payout_method:
        # In case vendor has no payout method yet, we allow to add this Invoice
        # even with bad currency. It will be validated later during scheduling & processing.
        return []
    # account_processor can be either from VendorBankDetailsShortlistPay class or PayoutMethodPublic,
    # so we need to grab processor accordingly:
    if isinstance(primary_payout_method, PayoutMethodPublic):
        processor_id = primary_payout_method.account_processor.processor_id
    else:
        processor_id = primary_payout_method.account_processor["processor_id"]
    processor = tenant.shortlist_pay.get_payment_processor_by_id(processor_id)
    if processor is None:
        return [{"type": "payout_method_processor_removed"}]

    if not tenant.shortlist_pay.is_payable_currency(processor.processor_id, currency):
        return [{"type": "payout_method_currency_not_payable", "payable_currencies": processor.payable_currencies}]

    return []


def validate_fields_not_editable(instance, request_data, fields, reason=None) -> dict | None:
    errors = defaultdict(list)
    for field in fields:
        if isinstance(field, tuple):
            field, errors_key = field
        else:
            field, errors_key = field, field
        is_field_changed = field in request_data and getattr(instance, field) != request_data[field]
        if is_field_changed:
            if reason:
                errors[errors_key].append(f"{FIELD_NOT_EDITABLE_ERROR_MSG} ({reason})")
            else:
                errors[errors_key].append(f"{FIELD_NOT_EDITABLE_ERROR_MSG}")
    return errors if errors else None


def validate_can_remove_payment(payment, allowed_statuses=None):
    if allowed_statuses is None:
        allowed_statuses = (
            Payment.status_code(Payment.STATUS_NEW),
            Payment.status_code(Payment.STATUS_REJECTED),
        )
    if payment.status not in allowed_statuses:
        raise serializers.ValidationError({ROOT_KEY_DRF: [f"{INVOICE_REMOVING_NOT_ALLOWED} ({REASON_BAD_INVOICE_STATUS})"]})
    if is_triggered_by_contracts_or_timesheets(payment):
        raise serializers.ValidationError({ROOT_KEY_DRF: [f"{INVOICE_REMOVING_NOT_ALLOWED} ({REASON_BLOCKED_BY_TRIGGERED_BY})"]})
    if payment.tasktimesheetperiod_set.exists():
        raise serializers.ValidationError(
            {ROOT_KEY_DRF: [f"{INVOICE_REMOVING_NOT_ALLOWED} ({REASON_BLOCKED_BY_LINKED_TIMESHEETS})"]},
        )


def _validate_invoice_total_amount(
    payment,
    *,
    exclude_line_item_id: int | None = None,
    additional_amount: Decimal | None = None,
    exc_class=None,
    non_field_error_key=None,
) -> None:
    """
    Validates that the total invoice amount remains greater than zero.
    Can be used for both deletion and update/create validation.

    Args:
        payment: The payment instance to validate
        exclude_line_item_id: Optional ID of line item to exclude from total calculation (for deletion)
        additional_amount: Optional amount to add to total (for create/update)

    Raises:
        ValidationError: If removing/updating the line item would make total amount zero or negative
    """
    if exc_class is None:
        exc_class = serializers.ValidationError
    if non_field_error_key is None:
        non_field_error_key = ROOT_KEY_DRF
    total_amount = Decimal("0")
    for work_item in payment.work_items.all():
        if exclude_line_item_id and work_item.id == exclude_line_item_id:
            continue
        total_amount += work_item.amount_with_tax

    if additional_amount is not None:
        total_amount += additional_amount

    if total_amount <= Decimal("0"):
        raise exc_class({non_field_error_key: ["Total invoice amount must be greater than 0"]})


def validate_can_remove_line_item(line_item):
    validate_can_remove_payment(line_item.payment)
    _validate_invoice_total_amount(line_item.payment, exclude_line_item_id=line_item.id)


def validate_line_item_deduction(
    *, unit_price: Decimal, quantity: Decimal, deduction_is_percentage: bool, deduction_value: Decimal, tenant=None
) -> list[dict]:
    if tenant is None:
        tenant = connection.tenant

    if not deduction_value:
        # There's no need to validate as there's no deduction provided.
        return []

    if not tenant.has_features(features.PAYMENTS_LINE_ITEMS_DEDUCTIONS):
        return [{"type": "deduction_not_allowed"}]

    if deduction_value < Decimal(0):
        return [{"type": "deduction_value_negative"}]

    base_amount = calculate_line_item_amounts({"quantity": quantity, "unit_price": unit_price}).base_amount

    if deduction_is_percentage in (None, ""):
        # In case client provided non-boolean value, we assume that it's percentage by default.
        deduction_is_percentage = True

    if deduction_is_percentage:
        if deduction_value >= Decimal(100):
            return [{"type": "deduction_value_percentage_too_large"}]
    else:
        if deduction_value >= base_amount:
            return [{"type": "deduction_value_too_large"}]

    return []


class LineItemDeductionValidator(CompatValidator):
    requires_context = True

    def __call__(self, request_data, serializer_field, instance=None):
        work_item = instance or getattr(serializer_field, "instance", None)

        errors = validate_line_item_deduction(
            quantity=request_data.get("quantity", work_item.quantity if work_item else None),
            unit_price=request_data.get("unit_price", work_item.unit_price if work_item else None),
            deduction_is_percentage=request_data.get("deduction_is_percentage", work_item.deduction_is_percentage if work_item else None),
            deduction_value=request_data.get("deduction_value", work_item.deduction_value if work_item else None),
            tenant=serializer_field.context.get("tenant") if serializer_field else None,
        )

        if errors:
            # Map errors for django compatibility:
            errors_map = {
                "deduction_not_allowed": lambda error: "Deduction value is not allowed",
                "deduction_value_negative": lambda error: "Deduction value can't be lower than 0",
                "deduction_value_percentage_too_large": lambda error: "Deduction value can't be equal or larger than 100",
                "deduction_value_too_large": lambda error: "Deduction value can't be larger than or equal to item amount",
            }
            deduction_value_errors = [errors_map.get(error["type"])(error) for error in errors]
            raise self.exc_class({"deduction_value": deduction_value_errors})


class TotalAmountValidator(CompatValidator):
    """Validates that the total invoice amount remains greater than zero after adding/updating a work item."""

    requires_context = True

    def __call__(self, request_data, serializer, instance=None):
        if not request_data:
            return

        current_work_item = instance or getattr(serializer, "instance", None)
        payment = (
            request_data.get("payment")
            or (current_work_item and current_work_item.payment)
            or (serializer and serializer.context.get("payment"))
        )
        if not payment:
            return

        # Calculate amount of the current work item
        current_amount_with_tax = calculate_line_item_amounts(
            {
                "quantity": request_data.get("quantity", current_work_item.quantity if current_work_item else None),
                "unit_price": request_data.get("unit_price", current_work_item.unit_price if current_work_item else None),
                "tax_percent": request_data.get("tax_percent", current_work_item.tax_percent if current_work_item else None),
                "deduction_is_percentage": request_data.get(
                    "deduction_is_percentage", current_work_item.deduction_is_percentage if current_work_item else None
                ),
                "deduction_value": request_data.get("deduction_value", current_work_item.deduction_value if current_work_item else None),
            },
        ).amount_with_tax

        _validate_invoice_total_amount(
            payment,
            exclude_line_item_id=current_work_item.id if current_work_item else None,
            additional_amount=current_amount_with_tax,
            exc_class=self.exc_class,
            non_field_error_key=self.non_field_error_key,
        )


class ZeroDecimalCurrencyValidator:
    requires_context = True
    MONEY_FIELDS = {
        'amount',
        'amount_with_tax',
        'price',
        'tax_amount',
        'total_amount',
        'total_amount_without_tax',
        'total_tax',
        'unit_price',
    }

    def __call__(self, attrs, serializer_field):
        self.errors = {}
        currency = attrs.get('currency') or serializer_field.context.get('currency') or tenant_currency()
        for field_name in self.MONEY_FIELDS:
            if field_name not in attrs:
                continue

            field_value = attrs.get(field_name)
            self._validate_zero_currency(currency, field_name, field_value)
        if self.errors:
            raise serializers.ValidationError(self.errors)

    def _validate_zero_currency(self, currency, field_name, value):
        if currency in ZERO_DECIMAL_CURRENCY_LIST and int(value) != value:
            self.errors.setdefault(field_name, []).append(
                f'Currency {currency} does not allow values with decimal places.'
                ' Please enter an integer value instead of a decimal (e.g. 50 instead 50.25)'
            )
