from dataclasses import dataclass
from django.db.models import Index
from pydantic import BaseModel, Field

from compat.choices import TextChoices
from payments.expenses.model import Expense
from payments.invoices.model import Invoice


def get_default_sort():
    return {"created_at": "desc"}


class SearchQuery(BaseModel):
    filters: list[dict] = Field(default_factory=list)
    sort: dict[str, str] | None = Field(default_factory=get_default_sort)
    query: str | None = None
    page: int = 0
    size: int = 25
    terms: bool = False
    aggregations: bool = False
    hits: bool = True
    total: bool = True


@dataclass(frozen=True)
class SearchResults:
    hits: list[Invoice] | list[Expense]
    total: int | None
    size: int
    page: int
    query: str
    filters: list
    terms: dict
    aggregations: dict


class SortDirection(TextChoices):
    ASC = "asc"
    DESC = "desc"


@dataclass(frozen=True)
class SortField:
    name: str
    direction: list[SortDirection]
    field: str | None = None
    arbiter: str | None = None
    external: bool = False

    @property
    def storage_field(self) -> str:
        if self.field:
            return self.field
        return self.name


@dataclass(frozen=True)
class SearchConfig:
    sorting: list[SortField]

    def get_sorting_field_config(self, field: str, direction: SortDirection) -> SortField | None:
        found_by_name = [f for f in self.sorting if f.name == field]
        if len(found_by_name) != 1:
            return
        sort_field_config = found_by_name[0]
        if direction not in sort_field_config.direction:
            return
        return sort_field_config

    @property
    def django_indexes(self) -> list[Index]:
        def get_index_declaration(field: str, direction: SortDirection, arbiter: str | None = None):
            fields = [f"{'-' if direction == SortDirection.DESC else ''}{field}"]
            if arbiter:
                fields.append(arbiter)
            return Index(fields=fields)

        indexes = []
        for option in self.sorting:
            if option.external:
                # Do not create index, it's handled by external table
                continue
            if not option.arbiter:
                # If there's no arbiter, we don't need two indexes to be created in ASC/DESC order.
                # One will be sufficient and it should be used by DB engine.
                indexes.append(get_index_declaration(option.storage_field, option.direction[0]))
            else:
                for direction in option.direction:
                    indexes.append(get_index_declaration(option.storage_field, direction, option.arbiter))
        return indexes
