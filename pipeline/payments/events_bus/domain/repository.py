"""Repository interfaces for the events delivery domain."""

from abc import ABC, abstractmethod
from uuid import UUID

from payments.events_bus.domain.model import DeliveryAttempt, Integration


class DeliveryAttemptRepository(ABC):
    """Repository interface for delivery attempts."""

    @abstractmethod
    def store_attempts_for_event(
        self, event_id: UUID, event_type: str, event_data: dict, integrations: list[Integration]
    ) -> list[DeliveryAttempt]:
        """Store delivery attempts for multiple integrations for the same event"""
        pass

    @abstractmethod
    def get_not_delivered_attempts(self, integration: Integration) -> list[DeliveryAttempt]:
        """Get attempts that haven't been delivered to the integration yet"""
        pass

    @abstractmethod
    def update_attempts(self, attempts: list[DeliveryAttempt]) -> None:
        """Update delivery attempts"""
        pass

    @abstractmethod
    def get_attempt(self, attempt_id: int) -> DeliveryAttempt | None:
        """Get a single attempt by its ID"""
        pass
