# Generated by Django 4.2.14 on 2024-08-08 07:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payments', '0008_rename_oper_sync_date_operaccountbalance_oper_fetch_date'),
    ]

    operations = [
        migrations.CreateModel(
            name='OperExchangeRate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('base_currency', models.CharField(max_length=10)),
                ('terms_currency', models.CharField(max_length=10)),
                ('buy_rate', models.DecimalField(decimal_places=10, max_digits=19)),
                ('sell_rate', models.DecimalField(decimal_places=10, max_digits=19)),
                ('spread_included', models.BooleanField()),
                ('source', models.CharField(max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTime<PERSON>ield(auto_now=True)),
            ],
        ),
    ]
