# Generated by Django 3.1.14 on 2023-01-24 10:19

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name='PaymentExportData',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                (
                    'name',
                    models.CharField(
                        choices=[
                            ('pdf_top_left', 'Invoice PDF - Top Left Section'),
                            ('pdf_top_right', 'Invoice PDF - Top Right Section'),
                            ('pdf_bottom_1', 'Invoice PDF - Bottom Section 1'),
                            ('pdf_bottom_2', 'Invoice PDF - Bottom Section 2'),
                            ('custom_export', 'Custom Export'),
                        ],
                        max_length=100,
                        unique=True,
                    ),
                ),
                ('display_name', models.CharField(blank=True, max_length=100, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='PaymentExportField',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                (
                    'column_name',
                    models.CharField(
                        blank=True, help_text='Provide own name for the field or leave empty to use default', max_length=100, null=True
                    ),
                ),
                (
                    'default_value',
                    models.CharField(blank=True, help_text='Default value to display if the field has no data', max_length=100, null=True),
                ),
                (
                    'hide_if_empty',
                    models.BooleanField(default=False, help_text='If True, a field will be hidden instead of showing "default value"'),
                ),
                ('mapping', models.CharField(max_length=100)),
                ('order', models.SmallIntegerField(default=0)),
                (
                    'export',
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fields', to='payments.paymentexportdata'),
                ),
            ],
            options={
                'ordering': ('export', 'order', 'id'),
            },
        ),
    ]
