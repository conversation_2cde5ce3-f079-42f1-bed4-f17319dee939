# Generated by Django 4.2.14 on 2024-09-18 06:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payments', '0010_alter_operexchangerate_unique_together'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentsConfig',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                (
                    'funding_method',
                    models.CharField(
                        choices=[('prfa', 'PRFA'), ('customer_transfer', 'Customer Transfer'), ('pulling', 'Pulling Funding')],
                        max_length=32,
                    ),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ('-created_at',),
            },
        ),
    ]
