from collections import defaultdict
from decimal import Decimal

from django.db import models
from django.db.transaction import atomic
from django.utils import timezone
from django.utils.encoding import force_bytes

from shortlist.utils import SimpleDictReader

from documents.models import Payment, PaymentBulkActionMaster
from documents.tasks import (
    payments_back_to_processing,
    payments_mark_as_processing_manually,
    payments_to_in_flight,
    trigger_payment_status_updates,
)


class BulkPaymentUpdateError(Exception):
    retryable = False


class BulkPaymentUpdateErrorRetryable(BulkPaymentUpdateError):
    retryable = True


class BulkPaymentUpdate:
    ACTION_MARK_AS_PAID = 'mark_as_paid'
    ACTION_MARK_AS_PROCESSING_MANUALLY = 'mark_as_processing_manually'
    ACTION_MOVE_TO_IN_FLIGHT = 'move_to_in_flight'
    ACTION_MOVE_BACK_TO_PROCESSING = 'move_back_to_processing'
    ACTION_UPDATE_EXTERNAL_IDENTIFIERS = 'update_external_identifiers'

    ACTIONS = {
        ACTION_MARK_AS_PAID,
        ACTION_MARK_AS_PROCESSING_MANUALLY,
        ACTION_MOVE_TO_IN_FLIGHT,
        ACTION_MOVE_BACK_TO_PROCESSING,
        ACTION_UPDATE_EXTERNAL_IDENTIFIERS,
    }

    ALLOWED_PAYMENT_STATUSES_FOR_ACTION = {
        ACTION_MARK_AS_PAID: [Payment.STATUS_APPROVED, Payment.STATUS_SCHEDULED],
        ACTION_MARK_AS_PROCESSING_MANUALLY: [Payment.STATUS_APPROVED, Payment.STATUS_SCHEDULED],
        ACTION_MOVE_TO_IN_FLIGHT: [Payment.STATUS_PROCESSING],
        ACTION_MOVE_BACK_TO_PROCESSING: [Payment.STATUS_IN_FLIGHT],
        ACTION_UPDATE_EXTERNAL_IDENTIFIERS: [Payment.STATUS_PAID],
    }

    # Generate one Celery task to update this many payments
    BATCH_SIZE = 1000

    @classmethod
    def _fetch_and_validate_payments_for_action(cls, ids, action, payments_amount=None):
        db_payments = Payment.objects.filter(id__in=ids)
        db_payment_ids = list(map(str, db_payments.values_list('id', flat=True)))
        missing_in_db = set(map(str, ids)).difference(set(db_payment_ids))
        if len(missing_in_db):
            missing_in_db_str = ', '.join(missing_in_db)
            raise BulkPaymentUpdateError(f'Could not find the following payment IDs in the DB: {missing_in_db_str}')

        # Find any payments with disallowed status
        allowed_statuses = cls.ALLOWED_PAYMENT_STATUSES_FOR_ACTION[action]
        disallowed_payments_by_status = db_payments.exclude(status__in=list(map(Payment.status_code, allowed_statuses)))
        disallowed_payment_by_status = disallowed_payments_by_status.first()
        if disallowed_payment_by_status:
            more = disallowed_payments_by_status.count() - 1
            msg = 'Wrong payment status for payment #{} \n action: {} \n status: {} \n allowed: {} {}'.format(
                disallowed_payment_by_status.id,
                action,
                disallowed_payment_by_status.status_name,
                ', '.join(allowed_statuses),
                f'\n (and {more} more payments with disallowed statuses)' if more else '',
            )
            raise BulkPaymentUpdateError(msg)

        # Check for overwriting existing ext ids
        if action == cls.ACTION_UPDATE_EXTERNAL_IDENTIFIERS:
            disallowed_payments_by_external_id = db_payments.exclude(payment_ext_identifier__isnull=True).exclude(payment_ext_identifier='')
            disallowed_payment_by_external_id = disallowed_payments_by_external_id.first()
            if disallowed_payment_by_external_id:
                more = disallowed_payments_by_external_id.count() - 1
                msg = 'Payment #{} has an external identifier set already. Refusing to overwrite it {}'.format(
                    disallowed_payment_by_external_id.id,
                    f'\n (and {more} more payments with external identifiers set)' if more else '',
                )
                raise BulkPaymentUpdateError(msg)

        if payments_amount is not None:
            # Check if the total amount matches
            if payments_amount != db_payments.aggregate(total_amount=models.Sum('total_amount'))['total_amount']:
                raise BulkPaymentUpdateError("Total amount doesn't match")

        return db_payments

    @classmethod
    def create_bulk_update_master_object(cls, action, csv_content):
        if action not in cls.ACTIONS:
            raise BulkPaymentUpdateError('Not a supported action')

        return PaymentBulkActionMaster.objects.create(
            action=action,
            batch_size=cls.BATCH_SIZE,
            file_contents=csv_content,
            payments_count=len(csv_content.strip().splitlines()) - 1,
        )

    @classmethod
    def create_bulk_update_batch_object(cls, master, batch_number):
        try:
            return cls._create_bulk_update_batch_object(master, batch_number)
        except BulkPaymentUpdateError as ex:
            # user error: report to user, but don't fail the Celery task
            master.create_error(
                batch_number=batch_number,
                message=str(ex),
            )
            return None
        except Exception:
            # code error: fail the Celery task and report to user without
            # giving away the exception message
            master.create_error(
                batch_number=batch_number,
                message='Unexpected error. Please check the Celery task',
            )
            raise

    @classmethod
    def _create_bulk_update_batch_object(cls, master, batch_number):
        batch_start = (batch_number - 1) * master.batch_size
        batch_end = batch_number * master.batch_size
        try:
            reader = SimpleDictReader(force_bytes(master.file_contents).splitlines())
            csv_items = list(reader)[batch_start:batch_end]
            csv_item_ids = [item.get('shortlistpaymentid') or item.get('payment id') or item.get('id') for item in csv_items]
            csv_items_by_id = dict(zip(csv_item_ids, csv_items, strict=False))
        except Exception as e:
            raise BulkPaymentUpdateError(f'Cannot parse the supplied CSV file.\n{str(e)}')

        # Error out if a new line is used in any field, this freaks out our sanity-check that relies on
        # number of lines in the file (minus the header line) matching the number of payments to process
        for csv_item in csv_items:
            for column_name, field_value in csv_item.items():
                if '\n' in field_value:
                    raise BulkPaymentUpdateError(
                        f'This CSV file has a column named "{column_name.capitalize()}" that contains multi-line values. '
                        'This is not supported. Please remove the column and re-upload the file'
                    )

        if all(item_id is None for item_id in csv_item_ids):
            raise BulkPaymentUpdateError(
                'Bad CSV file: payment ID not specified for any payment. '
                + 'Looks like the column may be missing (or is named incorrectly) in the CSV file'
            )

        if any(not item_id for item_id in csv_item_ids):
            raise BulkPaymentUpdateError('Bad CSV file: payment ID not specified for at least one payment')

        if len(set(csv_item_ids)) != len(csv_item_ids):
            raise BulkPaymentUpdateError('Bad CSV file: payment IDs not unique across the file')

        db_payments = cls._fetch_and_validate_payments_for_action(csv_item_ids, master.action)

        payments_ext_identifiers = []
        db_payment_ids = []
        for db_payment in db_payments:
            id = db_payment.id
            db_payment_ids.append(id)
            csv_item = csv_items_by_id.get(str(id), {})

            csv_total_amount = csv_item.get('moneycorppaymentamount') or csv_item.get('base amount') or -1
            try:
                csv_total_amount_decimal = Decimal(csv_total_amount)
            except:
                msg = f'Bad CSV file: trouble parsing the amount of payment #{id} as a decimal value.'
                msg += f' Expecting a number formatted like 12345.67, but instead got {csv_total_amount}'
                raise BulkPaymentUpdateError(msg)

            payment_from_csv = Payment(
                id=id,
                currency=csv_item.get('moneycorppaymentcurrency') or csv_item.get('base currency'),
                total_amount=csv_total_amount_decimal,
                vendor_id=csv_item.get('shortlistvendorid') or csv_item.get('sl id'),
                # created_at=csv_item.get('date created'),
            )
            cls._validate_payments_match(db_payment, payment_from_csv)

            if master.action in [cls.ACTION_MOVE_TO_IN_FLIGHT, cls.ACTION_MARK_AS_PAID, cls.ACTION_UPDATE_EXTERNAL_IDENTIFIERS]:
                # It's OK for ext_id to be an empty string, but it cannot be missing completely
                ext_id_candidates = [v for v in [csv_item.get('moneycorppaymentid', None), csv_item.get('mc rid', None)] if v is not None]
                if not ext_id_candidates:
                    raise BulkPaymentUpdateError('Bad CSV file: missing column with payment external indentifiers')

                ext_id = ext_id_candidates[0]
                payments_ext_identifiers.append(ext_id)

                if not ext_id and master.action == cls.ACTION_UPDATE_EXTERNAL_IDENTIFIERS:
                    raise BulkPaymentUpdateError(f'Bad CSV file: missing payment external indentifier for payment #{id}')

        payments_amount = db_payments.aggregate(total_amount=models.Sum('total_amount'))['total_amount']
        payments_count = len(db_payments)

        return master.batches.create(
            batch_number=batch_number,
            payment_ids=db_payment_ids,
            payments_amount=payments_amount,
            payments_count=payments_count,
            payments_ext_identifiers=payments_ext_identifiers or None,
        )

    @classmethod
    def validate_bulk_update(cls, master):
        if master.error:
            raise BulkPaymentUpdateError(master.error.message)

        if not master.all_batches_prepared:
            raise BulkPaymentUpdateErrorRetryable(
                f'The action is split into {master.number_of_batches} batches (of up to {master.batch_size} payments each). '
                'We are still waiting for some batches to validate in the background before continuing'
            )

        batches = master.batches.all()

        batches_payments_count = sum(batch.payments_count for batch in batches)
        if batches_payments_count != master.payments_count:
            raise BulkPaymentUpdateError(
                "Detected improper splitting: counts of payments in batches don't sum up to the original payment count"
            )

        payment_ids = set()
        for batch in batches:
            payment_ids = payment_ids.union(batch.payment_ids)

        if len(payment_ids) != master.payments_count:
            raise BulkPaymentUpdateError(
                "Detected improper splitting: counts of unique payments in batches don't sum up to the original payment count"
            )

    @classmethod
    def execute_bulk_update_batch(cls, batch):
        try:
            cls._execute_bulk_update_batch(batch)
        except Exception as ex:
            batch.result = False
            batch.save()
            batch.master.create_error(
                batch_number=batch.batch_number,
                message=str(ex),
            )
            raise
        else:
            batch.result = True
            batch.save()

    @classmethod
    def _execute_bulk_update_batch(cls, batch):
        payments_ids_per_status = defaultdict(list)
        trigger_status_updates = True

        action = batch.master.action

        with atomic():
            payments = cls._fetch_and_validate_payments_for_action(batch.payment_ids, action, batch.payments_amount)

            for payment_id, old_status in payments.values_list('id', 'status'):
                payments_ids_per_status[old_status].append(payment_id)

            if action == cls.ACTION_MARK_AS_PAID:
                cls._update_external_identifiers(batch)
                payments.update(status=Payment.status_code(Payment.STATUS_PAID), paid_date=timezone.now(), paid_day=timezone.now())
            elif action == cls.ACTION_MOVE_TO_IN_FLIGHT:
                cls._update_external_identifiers(batch)
                payments_to_in_flight(payments)
            elif action == cls.ACTION_MOVE_BACK_TO_PROCESSING:
                payments_back_to_processing(payments)
            elif action == cls.ACTION_MARK_AS_PROCESSING_MANUALLY:
                payments_mark_as_processing_manually(payments, batch.master.created_by)
            elif action == cls.ACTION_UPDATE_EXTERNAL_IDENTIFIERS:
                cls._update_external_identifiers(batch)
                trigger_status_updates = False
            else:
                raise BulkPaymentUpdateError('Invalid action for bulk payment update')

        if trigger_status_updates:
            for old_status, payment_ids in payments_ids_per_status.items():
                trigger_payment_status_updates.delay(payment_ids, Payment.get_display_status(old_status))

    @classmethod
    def _update_external_identifiers(cls, batch):
        for payment_id, payment_ext_identifier in zip(batch.payment_ids, batch.payments_ext_identifiers, strict=False):
            Payment.objects.filter(id=payment_id).update(payment_ext_identifier=payment_ext_identifier)

    @staticmethod
    def _validate_payments_match(in_db, in_csv):
        def error(field):
            msg = f'Mismatching payment {field} for payment #{in_db.id},\n in database: {getattr(in_db, field)},\n in CSV: {getattr(in_csv, field)}'
            if getattr(in_csv, field) is None:
                msg += '. Looks like the column may be missing (or is named incorrectly) in the CSV file'

            return BulkPaymentUpdateError(msg)

        if in_db.currency != in_csv.currency:
            raise error('currency')

        if in_db.total_amount != in_csv.total_amount:
            raise error('total_amount')

        if str(in_db.vendor_id) != str(in_csv.vendor_id):
            raise error('vendor_id')

        # TODO: add created date
