from django.db import connection

from payments.payruns.payrun_dates import PayrunDatesService
from payments.payruns.model import ScheduledPayrun


def scheduled_payruns(*, date_start=None) -> list[ScheduledPayrun]:
    """Get list of ScheduledPayrun's for a tenant."""
    dates = PayrunDatesService.from_tenant(connection.tenant, date_start=date_start).get_nearest_dates()
    return [ScheduledPayrun(date) for date in dates]
