import random
import uuid
from typing import Any

import factory
from faker import Faker

from django.core.exceptions import ValidationError
from django.db import connection

from preferences.models import (
    CustomField,
    CustomFieldsTemplate,
    validate_custom_fields_data,
)
from shortlist.factories import ShortlistDjangoModelFactory
from users.models import User
from users.tests.factories import UserFactory

fake = Faker()

DEFAULT_CUSTOM_FIELDS = [
    {'type': CustomField.TYPE_TEXT_LINE},
    {'type': CustomField.TYPE_NUMBER},
    {'type': CustomField.TYPE_BOOLEAN},
    {'type': CustomField.TYPE_DATE},
    {'type': CustomField.TYPE_CHOICE},
    {'type': CustomField.TYPE_SELECT},
]


def custom_field_choice_factory(cf: CustomField) -> list[str]:
    return (
        [fake.random_element(cf.choices)]
        if cf.max_len == 1
        else list(fake.random_elements(elements=cf.choices, unique=True, length=random.randint(1, min(len(cf.choices), 10))))
    )


def custom_field_user_factory(cf: CustomField) -> list[int]:
    user_ids = User.objects.filter(buyer_id__isnull=False, vendor_id__isnull=True).values_list('id', flat=True)
    if user_ids:
        return [fake.random_element(user_ids)]

    return [UserFactory().id]


CustomFieldValueFactory = {
    CustomField.TYPE_TEXT_LINE: lambda cf: fake.sentence(),
    CustomField.TYPE_NUMBER: lambda cf: fake.random_int(),
    CustomField.TYPE_BOOLEAN: lambda cf: fake.boolean(),
    CustomField.TYPE_DATE: lambda cf: fake.date(),
    CustomField.TYPE_CHOICE: custom_field_choice_factory,
    CustomField.TYPE_SELECT: lambda cf: fake.random_element(cf.choices),
    CustomField.TYPE_USER: custom_field_user_factory,
}


def get_custom_field_value(custom_field):
    fake_method = CustomFieldValueFactory.get(custom_field.type)
    if not fake_method:
        return fake.sentence()

    return fake_method(custom_field)


def get_custom_field_values(custom_fields_template):
    return {
        str(cf.source_field_id if cf.source_field_id else cf.pk): get_custom_field_value(cf)
        for cf in custom_fields_template.template_fields.all()
    }


def get_custom_fields_data(custom_fields_template, custom_fields_data: dict[int, Any] | None = None):
    custom_fields_values = get_custom_field_values(custom_fields_template)
    if custom_fields_data:
        custom_fields_values.update({str(k): v for k, v in custom_fields_data.items()})

    validated_custom_fields_data, errors = validate_custom_fields_data(custom_fields_template.template_fields.all(), custom_fields_values)
    if errors:
        raise ValidationError(errors)
    return validated_custom_fields_data


def generate_custom_fields(obj, create, extracted, **kwargs):
    if not create:
        return

    fields_kwargs = (extracted or {}).get('template_fields') or DEFAULT_CUSTOM_FIELDS
    custom_fields = []
    for field_kwargs in fields_kwargs:
        field_properties = field_kwargs.copy()

        if 'id' in field_properties:
            field_properties.update(get_global_custom_field_properties(field_properties['id']))
            field_properties['source_field_id'] = field_properties.pop('id')

        if 'create_propagators' in kwargs:
            field_properties.update(propagator={})

        custom_fields.append(CustomFieldFactory(template=obj, **field_properties))

    return custom_fields


def get_global_custom_field_properties(custom_field_id):
    field_values = CustomField.objects.filter(id=custom_field_id, template__model__isnull=False).values().first()
    if not field_values:
        raise Exception(f"Global Custom Field {custom_field_id} not found")

    fields = {
        k: v
        for k, v in field_values.items()
        if k in ['id', 'label', 'short_label', 'description', 'tooltip', 'type', 'mandatory', 'visible_to_vendors', 'choices', 'max_len']
    }
    return fields


def generate_custom_field_choices(self, count=3):
    if self.type in (CustomField.TYPE_CHOICE, CustomField.TYPE_SELECT):
        return [fake.sentence() for _ in range(count)]

    return None


class CustomFieldsTemplateFactory(ShortlistDjangoModelFactory):
    class Meta:
        model = 'preferences.CustomFieldsTemplate'

    name = factory.LazyAttribute(lambda o: f"{o.name_prefix}-{uuid.uuid4()}")
    post = factory.PostGeneration(generate_custom_fields)

    class Params:
        name_prefix = 'customfieldstemplate'

    @factory.post_generation
    def post_create(self, create, extracted, **kwargs):
        custom_fields_template: CustomFieldsTemplate = self
        if not create:
            return

        if custom_fields_template.model:
            try:
                # invalidate cached global custom fields templates if already loaded
                # so that when a test create a new one it gets loaded by this property
                del connection.tenant.cache.default_custom_field_templates
            except AttributeError:
                pass


class CustomFieldFactory(ShortlistDjangoModelFactory):
    class Meta:
        model = 'preferences.CustomField'

    template = factory.SubFactory(CustomFieldsTemplateFactory)
    type = factory.Faker("random_element", elements=[cf['type'] for cf in DEFAULT_CUSTOM_FIELDS])
    order = factory.Sequence(lambda n: n)
    label = factory.Faker("sentence", nb_words=4)
    visible_to_vendors = True
    mandatory = True
    choices = factory.LazyAttribute(lambda o: generate_custom_field_choices(o))

    @classmethod
    def dict(cls, global_field_id=None, **kwargs):
        params = {'template': None}
        if global_field_id:
            params.update(get_global_custom_field_properties(global_field_id))

        if kwargs:
            params.update(kwargs)

        data = super().dict(**params)
        return data


class CustomFieldDataFactory(factory.DictFactory):
    class Params:
        template = None

    correct_data = factory.LazyAttribute(lambda o: get_custom_field_values(o.template))


class CustomRateTypeFactory(ShortlistDjangoModelFactory):
    class Meta:
        model = 'preferences.CustomRateType'

    name = factory.Faker("word")
    label = factory.Faker("word")
    units_label = factory.Faker("word")
    enabled_for_tenant = True
    enabled_by_buyer = True
