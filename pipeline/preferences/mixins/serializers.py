from preferences.models import RestrictedData
from shortlist.current_user import get_current_request


class RestrictedDataSerializerMixin:
    def __new__(cls, *args, **kwargs):
        obj = super().__new__(cls, *args, **kwargs)
        if not hasattr(cls, '_restricted_fields'):
            if hasattr(obj, 'child'):
                obj._restricted_fields = obj.child._restricted_fields
            else:
                sources = obj.sources_for_fields()

                models = {v['model'] for v in sources.values()}
                fields = {v['attribute'] for v in sources.values()}.union(set(obj._declared_fields), set(obj.Meta.fields))

                obj._restricted_fields = RestrictedData.objects.for_models(*models).filter(field__in=fields)
        return obj

    def get_fields(self):
        # TODO: (p.<PERSON><PERSON><PERSON><PERSON>) does not work
        fields = super().get_fields()
        if self._restricted_fields:
            request = get_current_request()
            sources = self.sources_for_fields()
            main_model_name = RestrictedData.get_app_model_name(self.Meta.model)

            filtered_fields = {}
            for field in fields:
                if field in sources:
                    model_name = RestrictedData.get_app_model_name(sources[field]['model'])
                    attribute = sources[field]['attribute']
                else:
                    model_name = main_model_name
                    attribute = field

                has_access = True
                for restricted_field in self._restricted_fields:
                    if restricted_field.model_name == model_name and restricted_field.field == attribute:
                        if not restricted_field.has_permission(request):
                            has_access = False
                if has_access:
                    filtered_fields[field] = fields[field]
            return filtered_fields
        return fields

    @classmethod
    def sources_for_fields(cls):
        if not hasattr(cls, '_sources_for_fields'):
            cls._sources_for_fields = {
                field_name: cls.source_for_field(cls.Meta.model, field_name, field) for field_name, field in cls._declared_fields.items()
            }
        return cls._sources_for_fields

    @classmethod
    def source_for_field(cls, model, field_name, field):
        if field.source:
            for component in field.source.split('.'):
                previous_model = model
                if hasattr(model, component):
                    related_model = getattr(model, component)
                    if (
                        hasattr(related_model, 'field')
                        and hasattr(related_model.field, 'remote_field')
                        and related_model.field.remote_field
                    ):
                        model = related_model.field.remote_field.model
                else:
                    return {'model': previous_model, 'attribute': component}
        return {'model': model, 'attribute': field_name}
