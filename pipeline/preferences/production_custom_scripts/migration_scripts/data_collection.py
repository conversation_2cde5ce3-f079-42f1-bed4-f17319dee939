from django.db.models.signals import post_save
from documents.models import RequestedDocument
from onboarding.models import (
    OnBoardingStage,
    OnBoardingStagesForVendor,
    CustomFieldsTemplateToOnBoardingStagesForVendor,
    OnBoardingStageAttachment,
)
from onboarding.tasks import recalculate_onboarding_completeness
from preferences.models import CustomField, CustomVendorField, CustomFieldsTemplate, custom_field_post_save
from shortlist.utils import generate_token, guess_mimetype


LOG_LEVEL = 1  # lvl 1 - log stage changes, lvl 2 - log also Vendor data migration
RECALCULATE_VENDORS = False

static_dict = {
    "working_hours": {"label": 'Working hours', "propagator": {"profile_field": "working_hours"}, "type": CustomField.TYPE_WORKING_HOURS},
    "services": {"label": 'Skills and services', "propagator": {"profile_field": "services"}, "type": CustomField.TYPE_SERVICES},
    "introduction": {"label": 'Description', "propagator": {"profile_field": "introduction"}, "type": CustomField.TYPE_TEXT_AREA},
    "rates": {"label": 'Rates', "propagator": {"profile_field": "custom_rates_with_currency"}, "type": CustomField.TYPE_RATES},
    "location": {"label": 'Location', "propagator": {"profile_field": "location_dict"}, "type": CustomField.TYPE_LOCATION},
    "phone_number": {
        "label": 'Mobile phone number',
        "propagator": {"profile_field": "phone_number"},
        "type": CustomField.TYPE_PHONE_NUMBER,
    },
    "profile_photo": {
        "label": 'Profile photo',
        "propagator": {"profile_field": "profile_photo"},
        "type": CustomField.TYPE_PROFILE_PHOTO,
        "getattr": "logo",
    },
    "links:dribbble": {"label": 'Dribbble', "propagator": {"profile_field": "links", "type": "dribbble"}, "type": CustomField.TYPE_LINKS},
    "links:behance": {"label": 'Behance', "propagator": {"profile_field": "links", "type": "behance"}, "type": CustomField.TYPE_LINKS},
    "links:website": {"label": 'Website', "propagator": {"profile_field": "links", "type": "website"}, "type": CustomField.TYPE_LINKS},
    "links:facebook": {"label": 'Facebook', "propagator": {"profile_field": "links", "type": "facebook"}, "type": CustomField.TYPE_LINKS},
    "links:youtube": {"label": 'Youtube', "propagator": {"profile_field": "links", "type": "youtube"}, "type": CustomField.TYPE_LINKS},
    "links": {"label": 'Linkedin', "propagator": {"profile_field": "links", "type": "linkedin"}, "type": CustomField.TYPE_LINKS},
    "linkedin": {"label": 'Linkedin', "propagator": {"profile_field": "links", "type": "linkedin"}, "type": CustomField.TYPE_LINKS},
    "links:twitter": {"label": 'Twitter', "propagator": {"profile_field": "links", "type": "twitter"}, "type": CustomField.TYPE_LINKS},
    "links:vimeo": {"label": 'Vimeo', "propagator": {"profile_field": "links", "type": "vimeo"}, "type": CustomField.TYPE_LINKS},
    "links:instagram": {
        "label": 'Instagram',
        "propagator": {"profile_field": "links", "type": "instagram"},
        "type": CustomField.TYPE_LINKS,
    },
    "links:blog": {"label": 'Blog', "propagator": {"profile_field": "links", "type": "blog"}, "type": CustomField.TYPE_LINKS},
    "links:pinterest": {
        "label": 'Pinterest',
        "propagator": {"profile_field": "links", "type": "pinterest"},
        "type": CustomField.TYPE_LINKS,
    },
    "links:tiktok": {"label": 'Tiktok', "propagator": {"profile_field": "links", "type": "tiktok"}, "type": CustomField.TYPE_LINKS},
    "links:imdb": {"label": 'Imdb', "propagator": {"profile_field": "links", "type": "imdb"}, "type": CustomField.TYPE_LINKS},
}

ids_dict = dict()
req_documents = dict()
recalculate_vendors = set()

post_save.disconnect(custom_field_post_save, CustomField)


def log(msg, level=1):
    if level <= LOG_LEVEL:
        print(msg, file=out)


for stage in OnBoardingStage.objects.filter(stage_type=OnBoardingStage.STAGE_DATA_COLLECTION):
    matching_dict = static_dict.copy()

    log(f"OnBoardingStage ID: {stage.id} Workflow ID: {stage.workflow_id}")

    if not stage.custom_fields_template_id:
        stage.custom_fields_template = CustomFieldsTemplate.objects.create(name=f"onboardingstage-{generate_token()}")
        log(f"Generated CustomFieldsTemplate: {stage.custom_fields_template.id}")

    i = 0
    for i, field in enumerate(stage.vendor_profile_fields):
        label = None
        if field.startswith('custom_'):
            cvf_id = field[7:]
            cvf = CustomVendorField.objects.filter(id=cvf_id).first()
            if cvf:
                propagator = {"custom_vendor_field": cvf_id}
                label = cvf.name
                cf_type = cvf.type
                if cvf.choices:
                    choices = list(set(cvf.choices))  # get rid of duplicates
                else:
                    choices = None
                visible_to_vendors = cvf.visible_to_vendors
                log(f"CVF ID: {cvf_id} selected to migrate")
            else:
                log(f"CVF ID: {cvf_id} NOT FOUND")
        elif field in matching_dict:
            matched_field = matching_dict[field]
            propagator = matched_field['propagator']
            label = matched_field['label']
            cf_type = matched_field['type']
            choices = None
            visible_to_vendors = True
            log(f"Found matching {field}")
        else:
            log(f"Found FIELD which isn't in matching dict and isn't CVF!!! {field}")
        if label:
            cf = CustomField.objects.create(
                label=label,
                choices=choices,
                type=cf_type,
                order=i,
                mandatory=field in stage.vendor_profile_fields_mandatory,
                visible_to_vendors=visible_to_vendors,
                template_id=stage.custom_fields_template_id,
            )
            cf.propagator = propagator
            cf.save()
            ids_dict[cf.id] = {"field": field}
            log(f"Created CF ID: {cf.id}")

    for req_document in stage.requested_documents.all():
        i = i + 1
        cf = CustomField.objects.create(
            label=req_document.name,
            type=CustomField.TYPE_FILES,
            order=i,
            mandatory=req_document.mandatory,
            visible_to_vendors=req_document.visible_for_vendor,
            template_id=stage.custom_fields_template_id,
        )
        cf.propagator = {"custom_field": cf.id, "requested_document": req_document.id}
        cf.save()
        req_documents[req_document.id] = {"field": cf.id}
        log(f"Requested Dcoument ID: {req_document.id} have now created CF ID: {cf.id}")

    OnBoardingStage.objects.filter(id=stage.id).update(
        stage_type=OnBoardingStage.STAGE_REQUEST_DATA,
        custom_fields_template=stage.custom_fields_template,
        vendor_profile_fields=[],
        vendor_profile_fields_mandatory=[],
    )
    log("Stage updated")

    # migrate data
    stages_for_vendors = OnBoardingStagesForVendor.objects.filter(onboarding_template=stage).select_related('onboarding_template', 'vendor')
    log(f"Found {stages_for_vendors.count()} OnBoardingStagesForVendor")
    for stage_for_vendor in stages_for_vendors:
        log(f"Vendor ID: {stage_for_vendor.vendor_id} OnBoardingStagesForVendor {stage_for_vendor.id}", level=3)
        custom_fields_data = dict()
        custom_fields_template = stage_for_vendor.onboarding_template.custom_fields_template
        vendor = stage_for_vendor.vendor
        for cf_field in custom_fields_template.fields_for_serializer:
            cf_id = int(cf_field.id)
            if cf_id in ids_dict:
                field = ids_dict.get(cf_id)["field"]
                if field.startswith("custom_"):
                    if vendor.custom_fields:
                        cvf_id = field[7:]
                        cvf_value = vendor.custom_fields.get(cvf_id)
                        if cvf_value:
                            custom_fields_data[str(cf_id)] = cvf_value
                            log(f"Setting {str(cf_id)} value: {cvf_value}", level=3)
                        else:
                            log(f"NO VALUE {str(cf_id)}", level=3)
                else:
                    get_attr = static_dict.get(field).get("getattr") or static_dict[field].get("propagator").get("profile_field")
                    if get_attr and get_attr != "links":
                        value = getattr(vendor, get_attr)
                        if cf_field.type == CustomField.TYPE_NUMBER and value:
                            value = int(value)
                        custom_fields_data[str(cf_id)] = value
                        log(f"Setting {str(cf_id)} value: {value}", level=3)
                    elif get_attr == "links":
                        if vendor.links:
                            link_type = static_dict[field].get("propagator").get("type")
                            if link_type:
                                custom_fields_data[str(cf_id)] = {link_type: vendor.links.get(link_type)}
                                log(f"Setting {str(cf_id)} value: {link_type}: {vendor.links.get(link_type)}", level=3)
                        else:
                            log("NO Links!!!", level=3)
        vendor_documents = RequestedDocument.objects.filter(
            vendor=vendor, template_id__in=stage.requested_documents.values_list('id', flat=True)
        )
        for vendor_document in vendor_documents:
            cf_id = req_documents[vendor_document.template_id].get("field")
            if cf_id:
                attachment = OnBoardingStageAttachment.objects.create(
                    file=vendor_document.file,
                    filename=vendor_document.filename or vendor_document.file.name,
                    vendor=vendor,
                    custom_field_id=int(cf_id),
                    mimetype=guess_mimetype(vendor_document.filename or vendor_document.file.name),
                    uploaded_by=vendor_document.created_by,
                    stage=stage_for_vendor,
                )
                custom_fields_data[str(cf_id)] = attachment.id
                log(f"Vendor_document {vendor_document.id} set to ID: {str(cf_id)} with value: {attachment.id}", level=3)
            else:
                log(f"Vendor_document {vendor_document.id} not present!", level=3)
        log(f"OnBoardingStagesForVendor {stage_for_vendor.id} will get:", level=2)
        log(custom_fields_data, level=2)
        OnBoardingStagesForVendor.objects.filter(id=stage_for_vendor.id).update(custom_fields_data=custom_fields_data)
        recalculate_vendors.add(vendor.id)
        CustomFieldsTemplateToOnBoardingStagesForVendor.objects.get_or_create(
            template_id=stage.custom_fields_template_id, stage_for_vendor_id=stage_for_vendor.id
        )

    log("\n\nNext stage\n\n")

post_save.disconnect(custom_field_post_save, CustomField)

if RECALCULATE_VENDORS:
    log(f"Recalculating Vendors {recalculate_vendors}")
    recalculate_onboarding_completeness.delay(recalculate_vendors, source=['Migrating DataCollection Stages'])
