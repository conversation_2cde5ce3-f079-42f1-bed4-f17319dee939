# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2021-10-21 08:46
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('preferences', '0036_auto_20210926_2000'),
    ]

    operations = [
        migrations.AlterField(
            model_name='tenantterm',
            name='term',
            field=models.CharField(choices=[('invoice', 'invoice'), ('invoices', 'invoices'), ('Invoice', 'Invoice'), ('Invoices', 'Invoices'), ('project', 'project'), ('projects', 'projects'), ('Project', 'Project'), ('Projects', 'Projects'), ('request', 'request'), ('requests', 'requests'), ('Request', 'Request'), ('Requests', 'Requests'), ('task', 'task'), ('tasks', 'tasks'), ('Task', 'Task'), ('Tasks', 'Tasks'), ('Tasks & Projects', 'Tasks & Projects'), ('partner', 'partner'), ('partners', 'partners'), ('Partner', 'Partner'), ('Partners', 'Partners'), ('group', 'group'), ('groups', 'groups'), ('Group', 'Group'), ('Groups', 'Groups'), ('talent pool', 'talent pool'), ('talent pools', 'talent pools'), ('Talent Pool', 'Talent Pool'), ('Talent Pools', 'Talent Pools'), ('Campaign', 'Campaign'), ('Campaigns', 'Campaigns'), ('campaign', 'campaign'), ('campaigns', 'campaigns'), ('My Shortlist', 'My Shortlist'), ('Shortlist', 'Shortlist'), ('Shortlist Support', 'Shortlist Support'), ('Worksuite', 'Worksuite'), ('Other Vendor', 'Other Vendor'), ('RFP', 'RFP'), ('RFI', 'RFI'), ('Assignment', 'Assignment'), ('assignment', 'assignment'), ('Affiliations', 'Affiliations'), ('onboarding', 'onboarding'), ('Onboarding', 'Onboarding'), ('Finish onboarding', 'Finish onboarding'), ('Expense name', 'Expense name'), ('Expense', 'Expense'), ('Expenses', 'Expenses'), ('expense', 'expense'), ('expenses', 'expenses'), ('Upload expense file', 'Upload expense file'), ('Add new expense', 'Add new expense'), ('Working hours preference', 'Working hours preference'), ('Working hours', 'Working hours'), ('tag', 'tag'), ('tags', 'tags'), ('Tag', 'Tag'), ('Tags', 'Tags'), ('payment', 'payment'), ('payments', 'payments'), ('Payment', 'Payment'), ('Payments', 'Payments'), ('Payment due date', 'Payment due date'), ('Payrun', 'Payrun'), ('payrun', 'payrun'), ('Payruns', 'Payruns'), ('payruns', 'payruns'), ('additional information', 'additional information'), ('Additional information', 'Additional information'), ('cover letter', 'cover letter'), ('Cover letter', 'Cover letter'), ('marketplace', 'marketplace'), ('Marketplace', 'Marketplace'), ('job opening', 'job opening'), ('job openings', 'job openings'), ('Job opening', 'Job opening'), ('Job openings', 'Job openings'), ('candidate', 'candidate'), ('candidates', 'candidates'), ('Candidate', 'Candidate'), ('Candidates', 'Candidates'), ('applicant', 'applicant'), ('applicants', 'applicants'), ('Applicant', 'Applicant'), ('Applicants', 'Applicants'), ('job', 'job'), ('Job', 'Job'), ('job title', 'job title'), ('Job title', 'Job title'), ('stage', 'stage'), ('Stage', 'Stage'), ('stages', 'stages'), ('Stages', 'Stages'), ('favorite', 'favorite'), ('favorites', 'favorites'), ('Favorite', 'Favorite'), ('Favorites', 'Favorites'), ('Accept invitation', 'Accept invitation'), ('Description', 'Description'), ('Messages', 'Messages'), ('Team Board', 'Team Board'), ('Internal note', 'Internal note'), ('Quality', 'Quality'), ('Delivery', 'Delivery'), ('Value for money', 'Value for money'), ('staffing supplier', 'staffing supplier'), ('staffing suppliers', 'staffing suppliers'), ('Staffing supplier', 'Staffing supplier'), ('Staffing suppliers', 'Staffing suppliers'), ('worker', 'worker'), ('workers', 'workers'), ('Worker', 'Worker'), ('Workers', 'Workers'), ('terms-and-conditions-contractors', 'terms-and-conditions-contractors'), ('terms-and-conditions-contractors-custom-checkbox', 'terms-and-conditions-contractors-custom-checkbox'), ('Job Portal Apply Button', 'Job Portal Apply Button'), ('tax', 'tax'), ('Tax', 'Tax'), ('Category', 'Category'), ('Price', 'Price'), ('Amount', 'Amount'), ('Item', 'Item'), ('fee', 'fee'), ('fees', 'fees'), ('Name / Company', 'Name / Company'), ('Job opening Unique ID', 'Job opening Unique ID'), ('Task Unique ID', 'Task Unique ID'), ('Payment Unique ID', 'Payment Unique ID'), ('Send_Invoice', 'Send_Invoice'), ('Payment_Line_Items_Item', 'Payment_Line_Items_Item'), ('Portfolio_Project_name', 'Portfolio_Project_name'), ('Portfolio_Project_description', 'Portfolio_Project_description')], max_length=100, primary_key=True, serialize=False),
        ),
    ]
