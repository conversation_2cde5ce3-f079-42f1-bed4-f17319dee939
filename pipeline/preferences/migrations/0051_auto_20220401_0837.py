# -*- coding: utf-8 -*-
# Generated by Django 1.11.29 on 2022-04-01 08:37
from __future__ import unicode_literals

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('preferences', '0050_profanityfilterlog_blocked'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='emailconfig',
            name='footer_text',
        ),
        migrations.RemoveField(
            model_name='emailconfig',
            name='footer_text_color',
        ),
        migrations.RemoveField(
            model_name='emailconfig',
            name='logo',
        ),
        migrations.RemoveField(
            model_name='emailconfig',
            name='text_color',
        ),
        migrations.RemoveField(
            model_name='emailconfig',
            name='theme_color',
        ),
    ]
