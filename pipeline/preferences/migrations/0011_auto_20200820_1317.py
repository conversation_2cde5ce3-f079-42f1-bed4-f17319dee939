# -*- coding: utf-8 -*-
# Generated by Django 1.9.13 on 2020-08-20 13:17
from __future__ import unicode_literals

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('vendors', '0006_vendorcompliancestatus'),
        ('preferences', '0010_auto_20200817_1301'),
    ]

    operations = [
        migrations.CreateModel(
            name='ComplianceRule',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rule_type', models.CharField(choices=[('agreement', 'agreement'), ('bank_details', 'bank_details'), ('custom_vendor_field', 'custom_vendor_field'), ('requested_document', 'requested_document')], max_length=255)),
                ('object_id', models.PositiveIntegerField(null=True)),
                ('vendor_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='compliance_rules', to='vendors.VendorType')),
            ],
        ),
        migrations.AlterField(
            model_name='emailconfig',
            name='branding',
            field=models.CharField(choices=[(b'10thmanagement', b'10thmanagement'), (b'amcstudio', b'amcstudio'), (b'atvantage', b'atvantage'), (b'betterup', b'betterup'), (b'civitas', b'civitas'), (b'correlate', b'correlate'), (b'cxc', b'cxc'), (b'cxc-capgemini', b'cxc-capgemini'), (b'cxc-relx', b'cxc-relx'), (b'cxcglobal', b'cxcglobal'), (b'default', b'default'), (b'disney', b'disney'), (b'esa', b'esa'), (b'excellentia', b'excellentia'), (b'ferguson', b'ferguson'), (b'flosports', b'flosports'), (b'flybyfun', b'flybyfun'), (b'ge', b'ge'), (b'growthcollective', b'growthcollective'), (b'hays', b'hays'), (b'hays-dlg', b'hays-dlg'), (b'hays-honeywell', b'hays-honeywell'), (b'hays-nxp', b'hays-nxp'), (b'hutchinson', b'hutchinson'), (b'icf', b'icf'), (b'ideasunited', b'ideasunited'), (b'inusual', b'inusual'), (b'itn', b'itn'), (b'jpg', b'jpg'), (b'just99', b'just99'), (b'keylime', b'keylime'), (b'likewise', b'likewise'), (b'locomotus', b'locomotus'), (b'loreal', b'loreal'), (b'mccarthy', b'mccarthy'), (b'movidiam', b'movidiam'), (b'mpca', b'mpca'), (b'mykludo', b'mykludo'), (b'oars', b'oars'), (b'oliver', b'oliver'), (b'peroptyx', b'peroptyx'), (b'prodigious', b'prodigious'), (b'prodigious-lancome', b'prodigious-lancome'), (b'rellium', b'rellium'), (b'roadtrippers', b'roadtrippers'), (b'rr', b'rr'), (b'sephora', b'sephora'), (b'shiftgig', b'shiftgig'), (b'skyscanner', b'skyscanner'), (b'solomongroup', b'solomongroup'), (b'spartanrace', b'spartanrace'), (b'sumodigital', b'sumodigital'), (b'superbeast', b'superbeast'), (b'test', b'test'), (b'therapyworks', b'therapyworks'), (b'toastwedding', b'toastwedding'), (b'tyt', b'tyt'), (b'usertribe', b'usertribe'), (b'verifi', b'verifi'), (b'weareteachers', b'weareteachers'), (b'wlt', b'wlt'), (b'wpp', b'wpp'), (b'zypsy', b'zypsy')], default='default', max_length=100),
        ),
    ]
