# Generated by Django 3.2.16 on 2023-02-03 17:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('preferences', '0067_auto_20230118_1004'),
    ]

    operations = [
        migrations.AlterField(
            model_name='tenantterm',
            name='term',
            field=models.CharField(choices=[('Accept invitation', 'Accept invitation'), ('Add & Invite to', 'Add & Invite to'), ('Add new', 'Add new'), ('Add new expense', 'Add new expense'), ('additional information', 'additional information'), ('Additional information', 'Additional information'), ('Affiliations', 'Affiliations'), ('All statuses', 'All statuses'), ('Amount', 'Amount'), ('applicant', 'applicant'), ('Applicant', 'Applicant'), ('applicants', 'applicants'), ('Applicants', 'Applicants'), ('Apply', 'Apply'), ('Assignment', 'Assignment'), ('assignment', 'assignment'), ('Availability not set', 'Availability not set'), ('Available part-time', 'Available part-time'), ('Available soon', 'Available soon'), ('Basic info', 'Basic info'), ('Campaign', 'Campaign'), ('campaign', 'campaign'), ('Campaigns', 'Campaigns'), ('campaigns', 'campaigns'), ('candidate', 'candidate'), ('Candidate', 'Candidate'), ('candidates', 'candidates'), ('Candidates', 'Candidates'), ('Category', 'Category'), ('Change password', 'Change password'), ('cover letter', 'cover letter'), ('Cover letter', 'Cover letter'), ('Dashboard', 'Dashboard'), ('Date format', 'Date format'), ('Decline', 'Decline'), ('Declined', 'Declined'), ('Delivery', 'Delivery'), ('Description', 'Description'), ('disqualified', 'disqualified'), ('Disqualified', 'Disqualified'), ('disqualify', 'disqualify'), ('Disqualify', 'Disqualify'), ('E-mail', 'E-mail'), ('Edit profile', 'Edit profile'), ('Email content', 'Email content'), ('Email message', 'Email message'), ('Email title', 'Email title'), ('Entities', 'Entities'), ('entities', 'entities'), ('Entity', 'Entity'), ('entity', 'entity'), ('Expense', 'Expense'), ('expense', 'expense'), ('Expense name', 'Expense name'), ('Expenses', 'Expenses'), ('expenses', 'expenses'), ('Expiry', 'Expiry'), ('favorite', 'favorite'), ('Favorite', 'Favorite'), ('favorites', 'favorites'), ('Favorites', 'Favorites'), ('fee', 'fee'), ('fees', 'fees'), ('Files', 'Files'), ('Filter by', 'Filter by'), ('Finish onboarding', 'Finish onboarding'), ('First name', 'First name'), ('Get invite link', 'Get invite link'), ('group', 'group'), ('Group', 'Group'), ('groups', 'groups'), ('Groups', 'Groups'), ('Hint: You can insert variables using the "#" symbol', 'Hint: You can insert variables using the "#" symbol'), ('Internal note', 'Internal note'), ('Invite', 'Invite'), ('invoice', 'invoice'), ('Invoice', 'Invoice'), ('invoices', 'invoices'), ('Invoices', 'Invoices'), ('Item', 'Item'), ('job', 'job'), ('Job', 'Job'), ('Job board', 'Job board'), ('job opening', 'job opening'), ('Job opening', 'Job opening'), ('Job opening Unique ID', 'Job opening Unique ID'), ('job openings', 'job openings'), ('Job openings', 'Job openings'), ('Job Portal Apply Button', 'Job Portal Apply Button'), ('job title', 'job title'), ('Job title', 'Job title'), ('Last name', 'Last name'), ('Manage invite links', 'Manage invite links'), ('marketplace', 'marketplace'), ('Marketplace', 'Marketplace'), ('Messages', 'Messages'), ('My Shortlist', 'My Shortlist'), ('Name / Company', 'Name / Company'), ('Newsletter settings', 'Newsletter settings'), ('Not available', 'Not available'), ('onboarding', 'onboarding'), ('Onboarding', 'Onboarding'), ('Other Vendor', 'Other Vendor'), ('Partly available', 'Partly available'), ('partner', 'partner'), ('Partner', 'Partner'), ('partners', 'partners'), ('Partners', 'Partners'), ('payment', 'payment'), ('Payment', 'Payment'), ('Payment due date', 'Payment due date'), ('Payment Unique ID', 'Payment Unique ID'), ('Payment_Line_Items_Item', 'Payment_Line_Items_Item'), ('payments', 'payments'), ('Payments', 'Payments'), ('Payrun', 'Payrun'), ('payrun', 'payrun'), ('Payruns', 'Payruns'), ('payruns', 'payruns'), ('Personal time zone', 'Personal time zone'), ('Portfolio_Project_description', 'Portfolio_Project_description'), ('Portfolio_Project_name', 'Portfolio_Project_name'), ('Price', 'Price'), ('Profile', 'Profile'), ('Profile picture', 'Profile picture'), ('project', 'project'), ('Project', 'Project'), ('projects', 'projects'), ('Projects', 'Projects'), ('Quality', 'Quality'), ('request', 'request'), ('Request', 'Request'), ('Requested Documents', 'Requested Documents'), ('requests', 'requests'), ('Requests', 'Requests'), ('RFI', 'RFI'), ('RFP', 'RFP'), ('Send_Invoice', 'Send_Invoice'), ('Settings', 'Settings'), ('Shortlist', 'Shortlist'), ('Shortlist Support', 'Shortlist Support'), ('staffing supplier', 'staffing supplier'), ('Staffing supplier', 'Staffing supplier'), ('staffing suppliers', 'staffing suppliers'), ('Staffing suppliers', 'Staffing suppliers'), ('stage', 'stage'), ('Stage', 'Stage'), ('stages', 'stages'), ('Stages', 'Stages'), ('tag', 'tag'), ('Tag', 'Tag'), ('tags', 'tags'), ('Tags', 'Tags'), ('talent pool', 'talent pool'), ('Talent Pool', 'Talent Pool'), ('talent pools', 'talent pools'), ('Talent Pools', 'Talent Pools'), ('task', 'task'), ('Task', 'Task'), ('Task Unique ID', 'Task Unique ID'), ('tasks', 'tasks'), ('Tasks', 'Tasks'), ('Tasks & Projects', 'Tasks & Projects'), ('tax', 'tax'), ('Tax', 'Tax'), ('Team Board', 'Team Board'), ('terms-and-conditions-contractors', 'terms-and-conditions-contractors'), ('terms-and-conditions-contractors-custom-checkbox', 'terms-and-conditions-contractors-custom-checkbox'), ('Time format', 'Time format'), ('To do list', 'To do list'), ('Upload expense file', 'Upload expense file'), ('Value for money', 'Value for money'), ('worker', 'worker'), ('Worker', 'Worker'), ('workers', 'workers'), ('Workers', 'Workers'), ('Working hours', 'Working hours'), ('Working hours preference', 'Working hours preference')], max_length=150, primary_key=True, serialize=False),
        ),
    ]
