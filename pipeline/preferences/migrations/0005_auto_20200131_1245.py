# -*- coding: utf-8 -*-
# Generated by Django 1.9 on 2020-01-31 12:45
from __future__ import unicode_literals

from django.db import migrations, models
import shortlist.db_fields


class Migration(migrations.Migration):

    dependencies = [
        ('preferences', '0004_auto_20200124_1535'),
    ]

    operations = [
        migrations.AlterField(
            model_name='dashboardwidget',
            name='params',
            field=shortlist.db_fields.JSONField(default={}),
        ),
        migrations.AlterField(
            model_name='emailconfig',
            name='branding',
            field=models.CharField(choices=[(b'10thmanagement', b'10thmanagement'), (b'atvantage', b'atvantage'), (b'betterup', b'betterup'), (b'civitas', b'civitas'), (b'correlate', b'correlate'), (b'cxc', b'cxc'), (b'cxc-capgemini', b'cxc-capgemini'), (b'cxc-relx', b'cxc-relx'), (b'cxcglobal', b'cxcglobal'), (b'default', b'default'), (b'disney', b'disney'), (b'esa', b'esa'), (b'excellentia', b'excellentia'), (b'ferguson', b'ferguson'), (b'flosports', b'flosports'), (b'ge', b'ge'), (b'hays', b'hays'), (b'hays-dlg', b'hays-dlg'), (b'hays-honeywell', b'hays-honeywell'), (b'hays-nxp', b'hays-nxp'), (b'hutchinson', b'hutchinson'), (b'icf', b'icf'), (b'ideasunited', b'ideasunited'), (b'inusual', b'inusual'), (b'itn', b'itn'), (b'jpg', b'jpg'), (b'just99', b'just99'), (b'keylime', b'keylime'), (b'loreal', b'loreal'), (b'mccarthy', b'mccarthy'), (b'movidiam', b'movidiam'), (b'mpca', b'mpca'), (b'mykludo', b'mykludo'), (b'oars', b'oars'), (b'oliver', b'oliver'), (b'peroptyx', b'peroptyx'), (b'prodigious', b'prodigious'), (b'prodigious-lancome', b'prodigious-lancome'), (b'rellium', b'rellium'), (b'roadtrippers', b'roadtrippers'), (b'rr', b'rr'), (b'sephora', b'sephora'), (b'shiftgig', b'shiftgig'), (b'spartanrace', b'spartanrace'), (b'test', b'test'), (b'therapyworks', b'therapyworks'), (b'toastwedding', b'toastwedding'), (b'tyt', b'tyt'), (b'usertribe', b'usertribe'), (b'wlt', b'wlt'), (b'wpp', b'wpp'), (b'zypsy', b'zypsy')], default='default', max_length=100),
        ),
    ]
