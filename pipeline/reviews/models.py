import hashlib
import hmac
from decimal import Decimal

from django.apps import apps
from django.db import models, connection, transaction, IntegrityError
from django.db.models import Avg
from django.utils.crypto import constant_time_compare
from django.core.exceptions import ObjectDoesNotExist
from django.utils.encoding import force_bytes
from django.utils.timezone import now
from django.db.models.signals import post_save, post_delete

from shortlist.db_fields import <PERSON><PERSON><PERSON><PERSON>
from shortlist.search import indexer
from shortlist.nulls_last import NullsLastQueryManager
from shortlist.track_tasks import add_task_to_batch
from shortlist.utils import generate_token
from vendors.models.vendor import are_updates_blocked, _block_update_vendor_ids

TAG_QUERY = """
SELECT "reviews_reviewtag"."tag", COUNT("reviews_reviewtag"."tag") AS "number"
FROM "reviews_reviewtag"
INNER JOIN "reviews_review"
  ON  "reviews_reviewtag"."review_id" = "reviews_review"."id"
WHERE "reviews_review"."recommended" = %s
  AND "reviews_review"."vendor_id" = %s
GROUP BY "reviews_reviewtag"."tag"
"""

PROJECT_COUNT = """
SELECT COUNT(DISTINCT project_id) AS c
FROM "reviews_review"
WHERE "reviews_review"."vendor_id" = %s
"""  # required due to NotImplementedError: aggregate() + distinct(fields) not implemented.


def get_rows(sql, *params):
    cursor = connection.cursor()
    try:
        cursor.execute(sql, params)
        rows = cursor.fetchall()
    finally:
        cursor.close()
    return rows


class Review(models.Model):
    actor = models.ForeignKey(
        'users.Actor', related_name='reviews', null=True, on_delete=models.CASCADE
    )  # null True because of migration problem
    vendor = models.ForeignKey('vendors.Vendor', related_name='reviews', on_delete=models.CASCADE)
    project = models.ForeignKey('projects.Project', on_delete=models.SET_NULL, null=True, related_name='reviews', blank=True, default=None)
    task = models.ForeignKey('tasks.Task', on_delete=models.SET_NULL, null=True, related_name='reviews', blank=True, default=None)
    created = models.DateTimeField(auto_now_add=True, db_index=True)
    contents = models.TextField(default='', blank=True)
    recommended = models.BooleanField(db_index=True, default=False)
    score_quality = models.DecimalField(max_digits=2, decimal_places=1, db_index=True, null=True, blank=True)
    score_delivery = models.DecimalField(max_digits=2, decimal_places=1, db_index=True, null=True, blank=True)
    score_value_for_money = models.DecimalField(max_digits=2, decimal_places=1, db_index=True, null=True, blank=True)

    def _get_tags(self):
        return list(self.raw_tags.all().values_list('tag', flat=True).order_by('id'))

    def _set_tags(self, value):
        assert isinstance(value, list), "Tags should be a list of names"
        if self.id is not None:
            old_set = set(self.tags)
            new_set = set(value)
            if old_set != new_set:
                # keep order
                to_add = [tag for tag in value if tag not in old_set]
                to_remove = old_set - new_set
                self.raw_tags.bulk_create([ReviewTag(review=self, tag=tag) for tag in to_add])
                self.raw_tags.filter(tag__in=to_remove).delete()
                update_vendor_scores([self.vendor.id])
        else:
            # new instance, we don't have id yet - postpone creating tags until review is saved
            self._tags = value

    tags = property(_get_tags, _set_tags)

    class Meta:
        ordering = ("-id",)


class ReviewTag(models.Model):
    review = models.ForeignKey(Review, related_name='raw_tags', on_delete=models.CASCADE)
    tag = models.TextField(db_index=True)

    class Meta:
        unique_together = ('review', 'tag')


class VendorScoreBase(models.Model):
    class Meta:
        abstract = True

    DICT_FIELDS = (
        'recommended_yes',
        'recommended_no',
        'recommended_net',
        'review_count',
        'score_quality',
        'score_delivery',
        'score_value_for_money',
        'score_average',
        'recommended_ratio',
    )

    recommended_yes = models.IntegerField(default=0)
    recommended_no = models.IntegerField(default=0)
    recommended_net = models.IntegerField(default=0)
    review_count = models.IntegerField(default=0)
    score_quality = models.DecimalField(max_digits=2, decimal_places=1, db_index=True, null=True)
    score_delivery = models.DecimalField(max_digits=2, decimal_places=1, db_index=True, null=True)
    score_value_for_money = models.DecimalField(max_digits=2, decimal_places=1, db_index=True, null=True)
    score_average = models.DecimalField(max_digits=2, decimal_places=1, db_index=True, null=True)
    recommended_ratio = models.DecimalField(max_digits=3, decimal_places=2, null=True)

    @property
    def as_dict(self):
        return {k: getattr(self, k) for k in self.DICT_FIELDS}

    def reviews(self):
        raise NotImplementedError()

    def calculate_and_save(self):
        self.calculate()
        with transaction.atomic():  # SHOR-4644
            exists = self.__class__.objects.filter(pk=self.pk).exists()
            if exists:
                self.save(force_update=True)
            else:
                # might still run into race condition here SHOR-7775
                try:
                    with transaction.atomic(savepoint=True):
                        self.save(force_insert=True)
                except IntegrityError:
                    self.save(force_update=True)
        return self

    def calculate(self):
        reviews = self.reviews()
        positive_reviews = reviews.filter(recommended=True)
        negative_reviews = reviews.filter(recommended=False)

        self.recommended_yes = positive_reviews.count()
        self.recommended_no = negative_reviews.count()
        self.review_count = self.recommended_yes + self.recommended_no
        self.recommended_net = self.recommended_yes - self.recommended_no
        scores = []
        if self.review_count:
            self.recommended_ratio = float(self.recommended_yes) / float(self.review_count)
            result = reviews.aggregate(
                score_quality=Avg('score_quality'), score_delivery=Avg('score_delivery'), score_value_for_money=Avg('score_value_for_money')
            )
            for k, v in result.items():
                setattr(self, k, v)
                if v is not None:
                    scores.append(v)
        else:
            self.recommended_ratio = None
        self.score_average = sum(scores) / len(scores) if scores else None
        return self


class VendorScore(VendorScoreBase):
    vendor = models.OneToOneField('vendors.Vendor', related_name='raw_score', primary_key=True, on_delete=models.CASCADE)
    project_count = models.IntegerField(default=0)
    skills = JSONField(null=True)

    # satisfaction ration is recommended ratio but only if rewiew is conneted to any project
    satisfaction_ratio = models.DecimalField(max_digits=3, decimal_places=2, null=True)

    DICT_FIELDS = VendorScoreBase.DICT_FIELDS + ('project_count', 'skills', 'satisfaction_ratio')

    objects = NullsLastQueryManager()

    def calculate(self):
        super().calculate()

        skills = {name: 0 for name in (self.vendor.services or ())}
        if self.review_count:
            for value, recommended in [(-1, False), (1, True)]:
                rows = get_rows(TAG_QUERY, recommended, self.vendor.id)
                for tag, number in rows:
                    skills[tag] = skills.get(tag, 0) + value * number
            self.project_count = get_rows(PROJECT_COUNT, self.vendor.id)[0][0] or 0
        else:
            self.project_count = 0
        self.skills = skills

        satisfaction_qs = self.reviews().exclude(project=None)
        positive_satisfaction = satisfaction_qs.filter(recommended=True).count()
        negative_satisfaction = satisfaction_qs.filter(recommended=False).count()
        satisfaction_count = positive_satisfaction + negative_satisfaction
        if satisfaction_count:
            self.satisfaction_ratio = Decimal(positive_satisfaction) / Decimal(satisfaction_count)
        else:
            self.satisfaction_ratio = None

    def reviews(self):
        return Review.objects.filter(vendor=self.vendor)


class VendorScorePerProject(VendorScoreBase):
    project_vendor = models.OneToOneField(
        'projects.ProjectVendors', related_name='review_score', on_delete=models.CASCADE, primary_key=True
    )

    def reviews(self):
        return Review.objects.filter(vendor=self.vendor, project=self.project_vendor.project)

    @property
    def vendor(self):
        return self.project_vendor.vendor


def update_vendor_scores(vendor_ids):
    from vendors.models import Vendor

    vendors = Vendor.objects.all()
    if vendor_ids:
        vendors = vendors.filter(id__in=set(vendor_ids).difference(_block_update_vendor_ids))
    if vendors:
        for vendor in vendors:
            vendor_score, _ = VendorScore.objects.get_or_create(vendor=vendor)
            vendor_score.calculate_and_save()
        add_task_to_batch(indexer.update_vendors, [v.pk for v in vendors])


def force_vendor_scores_per_project(project, vendor):
    for pv in apps.get_model('projects', 'ProjectVendors').objects.filter(project=project, vendor=vendor):
        obj, _ = VendorScorePerProject.objects.get_or_create(project_vendor=pv)
        obj.calculate_and_save()


def update_vendor_scores_per_project(review):
    if are_updates_blocked(review.vendor.id):
        return
    for project in (review.project, getattr(review, 'old_project', None)):
        if project is not None:
            force_vendor_scores_per_project(project=project, vendor=review.vendor)


def review_post_save(sender, instance, raw=False, created=False, **kwargs):
    if raw:  # from fixture
        return
    if created:
        tags = getattr(instance, '_tags', None)
        if tags:
            instance.tags = tags
    update_vendor_scores([instance.vendor.id])
    update_vendor_scores_per_project(instance)


def review_post_delete(sender, instance, **kwargs):
    update_vendor_scores([instance.vendor.id])
    update_vendor_scores_per_project(instance)


post_save.connect(review_post_save, sender=Review, dispatch_uid="review_post_save.post_save")
post_delete.connect(review_post_delete, sender=Review, dispatch_uid="review_post_delete.post_delete")


class FeedbackRequest(models.Model):
    requested_by = models.ForeignKey('users.User', on_delete=models.CASCADE)
    requested_on = models.DateField(auto_now_add=True)
    expires = models.DateTimeField(null=True)
    invite_to_shortlist = models.BooleanField(default=False)
    custom_message = models.TextField(null=True)
    vendors = models.ManyToManyField('vendors.Vendor', related_name='feedback_request_vendors+')
    actors = models.ManyToManyField('users.Actor', related_name='feedback_request_actors+')
    key_salt = models.CharField(max_length=100, default=generate_token)
    task = models.ForeignKey('tasks.Task', on_delete=models.SET_NULL, null=True, related_name='feedback_requests', blank=True, default=None)

    def get_key_for(self, vendor, actor):
        data = f"{self.id}_{vendor.id}_{actor.id}"
        result = hmac.new(force_bytes(self.key_salt), force_bytes(data), hashlib.sha256).hexdigest()
        return result

    def verify_key_for(self, key, vendor_id, actor_id):
        try:
            vendor = self.vendors.get(pk=vendor_id)
            actor = self.actors.get(pk=actor_id)
        except ObjectDoesNotExist:
            return False, None, None
        signature = self.get_key_for(vendor, actor)
        return constant_time_compare(signature, key), vendor, actor

    def already_completed(self, vendor, actor):
        return self.fulfilled.filter(actor=actor, vendor=vendor).exists()

    @property
    def expired(self):
        return self.expires is not None and self.expires < now()

    def get_url_params_for(self, vendor, actor):
        return f"feedback_request={self.id}&actor={actor.id}&vendor={vendor.id}&key={self.get_key_for(vendor, actor)}"

    def pending_vendors_for_actor(self, actor):
        if actor not in self.actors.all():
            return ()

        fulfilled = [x.vendor for x in self.fulfilled.filter(actor=actor)]
        return set(self.vendors.all()).difference(fulfilled)


class FeedbackRequestFulfilled(models.Model):
    request = models.ForeignKey('reviews.FeedbackRequest', related_name='fulfilled', on_delete=models.CASCADE)
    vendor = models.ForeignKey('vendors.Vendor', related_name='+', on_delete=models.CASCADE)
    actor = models.ForeignKey('users.Actor', related_name='+', on_delete=models.CASCADE)
    review = models.ForeignKey('reviews.Review', related_name='+', null=True, on_delete=models.SET_NULL)
    timestamp = models.DateTimeField(auto_now_add=True)
