"""
API overview - for buyer:

POST        /api/reviews - create a new review
GET         /api/reviews - get some reviews
 filter by: page (number), author (slug), project (slug), vendor (slug), recommended (True/False)
 default sort is id descending (last reviews first)
 possible orderings: id, vendor, project, any of the scores: 'score_quality', 'score_delivery', 'score_value_for_money'
GET         /api/reviews/{id} - details of a single review
PUT/PATCH   /api/reviews/{id} - edit a single review (author only)
DELETE      /api/reviews/{id} - delete a single review (author only)

Examples:

Going through all reviews starting with last ones:
  GET /api/reviews/?page=1, then GET /api/reviews/?page=1 etc.
First page of reviews for a specific vendor with  3.0 <= quality score <= 4.0:
  GET /api/reviews/?page=1&vendor=slug&score_quality_0=3.0&score_quality_1=4.0


"""

import os.path

from django.db import connection
from django.db.models import Q
from rest_framework import viewsets, status
from rest_framework.exceptions import PermissionDenied

from rest_framework.response import Response
from rest_framework.views import APIView
import django_filters

from reviews.serializers import VendorScoreSerializer, VendorScoreListSerializer, VendorScoreListCSVSerializer
from shortlist.current_user import get_current_user
from shortlist.filters import ContainFilter, FilterSet
from shortlist.permissions import Permission, TypedPermissionMixin
from shortlist.pagination import OptionalPagination
from shortlist.helpers import global_action
from shortlist.utils import serve_file_contents
from tasks.models import Task
from tasks.serializers import TaskMinimalSerializer
from vendors.models import Vendor, VendorGroup
from vendors.serializers import VendorMinimalSerializer, VendorForVendorMinimalSerializer
from users.models import Actor, User
from events.event_types import VendorFeedbackRequested

from .models import Review, VendorScore, ReviewTag, FeedbackRequest, FeedbackRequestFulfilled
from .serializers import ReviewSerializer, FeedbackRequestCreateSerializer

SCORES = ('score_quality', 'score_delivery', 'score_value_for_money')


class ReviewFilterSet(FilterSet):
    vendor = django_filters.CharFilter(field_name="vendor__slug")
    project = django_filters.CharFilter(field_name="project__slug")
    task = django_filters.CharFilter(field_name="task_id")
    author = django_filters.CharFilter(field_name="actor__user__slug")
    score_quality = django_filters.RangeFilter(field_name="score_quality")
    score_delivery = django_filters.RangeFilter(field_name="score_delivery")
    score_value_for_money = django_filters.RangeFilter(field_name="score_value_for_money")

    class Meta:
        model = Review
        fields = ('vendor', 'author', 'project', 'recommended')
        order_by = ('id', 'vendor', 'project') + SCORES


class VendorScoresFilterSet(FilterSet):
    skill = ContainFilter(field_name='vendor__services')
    group = django_filters.CharFilter(field_name='vendor__groups__slug')

    class Meta:
        model = VendorScore
        fields = ('vendor',)
        order_by = (
            'vendor',
            'score_average',
            'satisfaction_ratio',
            'review_count',
        ) + SCORES


class ReviewViewSet(TypedPermissionMixin, viewsets.ModelViewSet):
    permission_app = 'buyer'
    permission_type = 'review'
    model = Review
    serializer_class = ReviewSerializer
    filterset_class = ReviewFilterSet
    pagination_class = OptionalPagination

    def get_queryset(self):
        return Review.objects.all().select_related('actor', 'project', 'vendor', 'task')

    def filter_queryset(self, queryset):
        queryset = super().filter_queryset(queryset)
        user = self.request.user
        if not user.has_perm(Permission.buyer('task', 'list')):
            if user.has_perm(Permission.buyer('task', 'list', 'task manager')):
                queryset = queryset.filter(
                    Q(task__isnull=True) | Q(task__managers=user) | Q(task__milestone__managers=user) | Q(actor=user.actor)
                )
            else:
                queryset = queryset.filter(Q(task__isnull=True) | Q(actor=user.actor))
        return queryset

    def update(self, request, *args, **kwargs):
        obj = self.get_object()
        if self.request.user != obj.actor.user:
            raise PermissionDenied("Only author can update a review")
        obj.old_project = obj.project
        created = False
        serializer = self.get_serializer(obj, data=request.data, partial=kwargs.pop('partial', False))
        if serializer.is_valid():
            self.perform_update(serializer)
            return Response(serializer.data, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def perform_create(self, serializer):
        save_kwargs = {'force_update': True}
        if not serializer.validated_data.get('actor'):
            save_kwargs['actor'] = Actor.objects.for_user(self.request.user)

        instance = serializer.save(**save_kwargs)
        assert instance.actor is not None

    def perform_update(self, serializer):
        self.perform_create(serializer)

    @global_action(permission_classes=[Permission.buyer('review', 'create')])
    def request_feedback(self, request, *args, **kwargs):
        serializer = FeedbackRequestCreateSerializer(data=request.data)
        if serializer.is_valid():
            invite_to_shortlist = serializer.data.get('invite_to_shortlist', False)
            if invite_to_shortlist and not Permission.buyer('team', 'create').has_permission(request, self):
                return Response(
                    f"Not allowed to invite to {connection.tenant.tenant_terms['Shortlist']}",
                    status=status.HTTP_403_FORBIDDEN,
                )
            vendors = list(Vendor.objects.filter(archived=False, slug__in=(serializer.data.get('vendors', []))))
            if not vendors:
                return Response(
                    f"Must specify at least 1 {connection.tenant.tenant_terms['partner']} to this user.",
                    status=status.HTTP_400_BAD_REQUEST,
                )
            actors = set()
            for slug_or_email in serializer.data['users']:
                if '@' in slug_or_email:
                    user = User.objects.filter(email__iexact=slug_or_email).first()
                    if user:
                        actor = user.actor
                    else:
                        actor = Actor.objects.for_external(slug_or_email)
                else:
                    user = User.objects.filter(slug=slug_or_email).first()
                    if not user:
                        return Response(f"Unknown user {slug_or_email}", status=status.HTTP_400_BAD_REQUEST)
                    actor = user.actor
                actors.add(actor)

            fr = FeedbackRequest.objects.create(
                requested_by=request.user,
                expires=serializer.data.get('expires'),
                custom_message=serializer.data['custom_message'],
                invite_to_shortlist=invite_to_shortlist,
                task=Task.objects.filter(id=serializer.data.get('task')).first(),
            )
            fr.actors.add(*actors)
            fr.vendors.add(*vendors)
            for actor in actors:
                VendorFeedbackRequested(feedback_request=fr, actor=actor, vendors=vendors, user=actor.user)
            return Response()
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class VendorScoresViewSet(viewsets.ReadOnlyModelViewSet):
    permission_classes = [Permission.buyer('review', 'list')]
    lookup_field = 'vendor__slug'
    lookup_url_kwarg = 'slug'

    model = VendorScore
    queryset = VendorScore.objects.all()

    filterset_class = VendorScoresFilterSet

    def post(self, request, *args, **kwargs):
        # workaround for too many vendor's slugs to pass in QUERY_PARAMS
        return self.get(request, *args, **kwargs)

    def is_list_view(self):
        return not bool(self.kwargs)

    def get_queryset(self):
        qs = super().get_queryset()
        if self.is_list_view():
            return qs.exclude(review_count=0).select_related('vendor')
        return qs

    def filter_queryset(self, queryset):
        if 'slug' in self.request.data:
            vendor_slugs = self.request.data['slug']
        else:
            vendor_slugs = self.request.query_params.getlist('slug', None)
        if vendor_slugs:
            return queryset.filter(vendor__slug__in=vendor_slugs)
        else:
            return queryset

    def get_serializer_class(self):
        if self.is_list_view():
            if self.request.query_params.get('format') == 'csv':
                return VendorScoreListCSVSerializer
            return VendorScoreListSerializer
        return VendorScoreSerializer


class VendorScoresFilterOptions(APIView):
    permission_classes = [Permission.buyer('review', 'list')]

    def get(self, request):
        vendors_with_reviews = Vendor.objects.filter(id__in=Review.objects.values_list('vendor__id'))
        return Response(
            {
                'services': ReviewTag.objects.distinct().exclude(review__isnull=True).order_by('tag').values_list('tag', flat=True),
                'groups': VendorGroup.visible.filter(vendors__in=vendors_with_reviews).distinct().values('id', 'name'),
            }
        )


def load_icon(filename):
    dir = os.path.dirname(os.path.dirname(os.path.join(__file__)))
    path = os.path.join(dir, 'static', 'images', filename)
    with open(path, "rb") as f:
        return f.read()


class PublicFeedbackRequestViewSet(viewsets.ViewSet):
    permission_classes = []
    icon_done = load_icon('feedback_done.png')
    icon_waiting = load_icon('feedback_waiting.png')
    icon_error = load_icon('feedback_error.png')

    def check_params(self, request):
        params = request.query_params
        try:
            self.fr = fr = FeedbackRequest.objects.filter(id=params['feedback_request']).first()
            if fr is not None:
                verified, self.vendor, self.actor = fr.verify_key_for(str(params['key']), params['vendor'], params['actor'])
                if not verified:
                    return Response('Invalid key parameter.', status=status.HTTP_403_FORBIDDEN)
        except LookupError:
            fr = None
        if fr is None:
            return Response('Invalid link.', status=status.HTTP_400_BAD_REQUEST)
        if fr.expired:
            return Response('Link has expired.', status=status.HTTP_400_BAD_REQUEST)

    def icon(self, request, *args, **kwargs):
        """Return checkmark image to show whether this feedback request has already been submitted"""
        error_response = self.check_params(request)
        if error_response is None:
            if self.fr.already_completed(self.vendor, self.actor):
                result = self.icon_done
            else:
                result = self.icon_waiting
        else:
            result = self.icon_error
        return serve_file_contents(file_name=None, content=result, content_type="image/png", inline=True)

    def check(self, request, *args, **kwargs):
        """Verify key and return vendor data to the frontend"""
        error_response = self.check_params(request)
        if error_response is not None:
            return error_response

        current_user = get_current_user()
        vendor_serializer = VendorForVendorMinimalSerializer if current_user and current_user.vendor else VendorMinimalSerializer
        result = {
            'vendor': vendor_serializer(self.vendor).data,
            'task': TaskMinimalSerializer(self.fr.task, fields=('name',)).data,
            'invite_to_shortlist': self.fr.invite_to_shortlist and self.actor.user is None,
            'submitted': self.fr.already_completed(self.vendor, self.actor),
            'custom_message': self.fr.custom_message,
            'full_name': self.actor.name or '',
        }
        result.update(self.other_requests())
        return Response(result)

    def other_requests(self):
        pending_vendors = self.fr.pending_vendors_for_actor(self.actor)
        return {
            'other_requests': [self.fr.get_url_params_for(vendor, self.actor) for vendor in pending_vendors if vendor != self.vendor],
        }

    def submit(self, request, *args, **kwargs):
        """Submit the feedback request"""
        error_response = self.check_params(request)
        if error_response is not None:
            return error_response
        data = request.data
        data['vendor'] = self.vendor.slug
        if self.fr.task:
            data['task'] = self.fr.task.pk
        serializer = ReviewSerializer(data=data)
        if serializer.is_valid():
            full_name = data.get('full_name')
            if full_name and self.actor.user is None:
                self.actor.external_name = full_name
                self.actor.save()

            instance = serializer.save(actor=self.actor, force_insert=True)
            self.fr.fulfilled.add(FeedbackRequestFulfilled(vendor=self.vendor, actor=self.actor, review=instance), bulk=False)
            result = self.other_requests()
            result['invite_to_shortlist'] = self.fr.invite_to_shortlist and (self.actor.user is None or not self.actor.user.is_active)
            return Response(result, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def use_invite(self, request, *args, **kwargs):
        error_response = self.check_params(request)
        if error_response is not None:
            return error_response
        if not self.fr.invite_to_shortlist:
            return Response(status=status.HTTP_400_BAD_REQUEST, data="Not invited")
        if self.actor.user is None:
            user = self.actor.convert_to_buyer(invited_by=self.fr.requested_by)
        else:
            if self.actor.user.is_active:
                return Response(status=status.HTTP_400_BAD_REQUEST, data="Already registered")
            user = self.actor.user
        return Response({'invite_url': user.get_team_invite_url()})
