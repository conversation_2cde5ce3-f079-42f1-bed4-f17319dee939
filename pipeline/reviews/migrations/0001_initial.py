# -*- coding: utf-8 -*-
# Generated by Django 1.9 on 2020-01-17 18:18
from __future__ import unicode_literals

from django.db import migrations, models
import shortlist.utils


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='FeedbackRequest',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('requested_on', models.DateField(auto_now_add=True)),
                ('expires', models.DateTimeField(null=True)),
                ('invite_to_shortlist', models.BooleanField(default=False)),
                ('custom_message', models.TextField(null=True)),
                ('key_salt', models.CharField(default=shortlist.utils.generate_token, max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name='FeedbackRequestFulfilled',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Review',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('contents', models.TextField()),
                ('recommended', models.BooleanField(db_index=True, default=False)),
                ('score_quality', models.DecimalField(blank=True, db_index=True, decimal_places=1, max_digits=2, null=True)),
                ('score_delivery', models.DecimalField(blank=True, db_index=True, decimal_places=1, max_digits=2, null=True)),
                ('score_value_for_money', models.DecimalField(blank=True, db_index=True, decimal_places=1, max_digits=2, null=True)),
            ],
            options={
                'ordering': ('-id',),
            },
        ),
        migrations.CreateModel(
            name='ReviewTag',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tag', models.TextField(db_index=True)),
            ],
        ),
    ]
