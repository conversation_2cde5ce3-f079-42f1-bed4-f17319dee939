from django.db.models import Exists, OuterRef, QuerySet

from onboarding.models import OnBoardingStage, OnBoardingWorkflow


def is_compliance_workflow(workflow: OnBoardingWorkflow | dict) -> bool:
    """Check if a workflow contains a worker classification stage.

    Args:
        workflow: Either an OnBoardingWorkflow instance or a dictionary representation of a workflow

    Returns:
        bool: True if the workflow contains a worker classification stage, False otherwise
    """
    if isinstance(workflow, dict):
        has_worker_classification_stage = any(
            stage_data.get('stage_type') == OnBoardingStage.STAGE_WORKER_CLASSIFICATION for stage_data in workflow.get('stages', [])
        )
    else:
        has_worker_classification_stage = OnBoardingStage.objects.filter(
            workflow=workflow, stage_type=OnBoardingStage.STAGE_WORKER_CLASSIFICATION
        ).exists()

    return has_worker_classification_stage


def is_compliance_stage(stage: OnBoardingStage | dict) -> bool:
    """Check if a stage is a worker classification stage.

    Args:
        stage: Either an OnBoardingStage instance or a dictionary representation of a stage

    Returns:
        bool: True if the stage is a worker classification stage, False otherwise
    """
    if isinstance(stage, dict):
        stage_type = stage.get('stage_type')
    else:
        stage_type = stage.stage_type

    return stage_type == OnBoardingStage.STAGE_WORKER_CLASSIFICATION


def filter_workflows_by_compliance(queryset: QuerySet, with_worker_classification_stage: bool) -> QuerySet:
    """Filter workflows based on whether they contain a worker classification stage.

    Args:
        queryset: The base queryset of OnBoardingWorkflow objects to filter
        with_worker_classification_stage: If True, return workflows with a worker classification stage.
            If False, return workflows without a worker classification stage.

    Returns:
        QuerySet: Filtered queryset of OnBoardingWorkflow objects
    """
    return queryset.annotate(
        has_worker_classification_stage=Exists(
            OnBoardingStage.objects.filter(workflow_id=OuterRef('id'), stage_type=OnBoardingStage.STAGE_WORKER_CLASSIFICATION)
        )
    ).filter(has_worker_classification_stage=with_worker_classification_stage)
