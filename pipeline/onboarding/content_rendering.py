from django.db import connection
from django.utils.timezone import now
from rest_framework import serializers

from shortlist.content_rendering import render_html_content, render_text_content
from vendors.field_mapping import get_mapped_field_values_for_content_rendering


def get_content_rendering_context(vendor=None, workflow_for_vendor=None):
    from events.attributes import expand_attributes
    from events.base import get_global_params
    from notifications.emails import get_default_content_attributes

    context = dict(timestamp=now())

    if connection.tenant:
        context.update(get_global_params(connection.tenant))

    if vendor:
        context['executor'] = vendor

    if workflow_for_vendor and workflow_for_vendor.current_stage:
        context['stage'] = workflow_for_vendor.current_stage

    expand_attributes(context)

    context = get_default_content_attributes(vendor, context)
    if workflow_for_vendor:
        context.update(get_mapped_field_values_for_content_rendering(vendor, workflow_for_vendor))

    return context


class RenderTextContentField(serializers.ReadOnlyField):
    """
    A read-only field that returns the text rendered with available mapped fields and their values obtained
    from the serialization context.
    """

    def to_representation(self, value):
        if value:
            return render_text_content(
                value, get_content_rendering_context(self.context.get('vendor'), self.context.get('workflow_for_vendor'))
            )

        return value


class RenderHtmlContentField(serializers.ReadOnlyField):
    """
    A read-only field that returns the HTML rendered with available mapped fields and their values obtained
    from the serialization context.
    """

    def to_representation(self, value):
        if value:
            return render_html_content(
                value, get_content_rendering_context(self.context.get('vendor'), self.context.get('workflow_for_vendor'))
            )

        return value
