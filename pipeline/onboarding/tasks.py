from datetime import datetime, timedelta
from logging import getLogger

from django.conf import settings
from django.db import connection
from django.db.models import Count, OuterRef, Q, Subquery
from django.db.models.functions import Coalesce
from django.utils import timezone

from clients import features
from events.event_types import BuyerSentDocumentForSignature, OnBoardingTemplateEdited
from notifications.base import dont_send_notifications
from shortlist.celery import app
from shortlist.current_user import set_user_in_task
from shortlist.search import indexer
from shortlist.utils import save_prepared_file_to_s3
from users.models import User

logger = getLogger(__name__)


def recalculate_onboarding_completeness_current_workflow(workflow_id: int, vendor_id: int, context_id: str | None = None):
    from django.db import connection

    from onboarding.completeness import OnBoardingCompleteness
    from vendors.models.vendor import filter_vendor_ids_by_feature

    vendor_ids = set(filter_vendor_ids_by_feature([vendor_id], features.ONBOARDING))
    if vendor_ids and connection.tenant.has_features(features.ONBOARDING_WORKFLOWS):
        completeness_service = OnBoardingCompleteness()
        completeness_service.recalculate([vendor_id], workflow_id, context_id)


@app.task()
def recalculate_onboarding_completeness(
    vendor_ids: list[int],
    notify: bool = True,
    requested_by_id: int | None = None,
    workflow_id: int | None = None,
    context_id: str | None = None,
    *,
    source: list[str] | None = None,
):
    from django.db import connection

    from onboarding.models import OnBoardingStagesForVendor, OnBoardingWorkflow
    from shortlist.current_user import get_current_user_id
    from shortlist.track_tasks import add_task_to_batch
    from vendors.models.vendor import filter_vendor_ids_by_feature

    from .completeness import OnBoardingCompleteness

    logger.debug("Recalculate onboarding completeness in workflow=%s for vendors=%s source=%s", workflow_id, vendor_ids, source)

    if not vendor_ids:
        return

    source = source or []
    source.append('recalculate_onboarding_completeness')

    vendor_ids = set(filter_vendor_ids_by_feature(vendor_ids, features.ONBOARDING))
    if vendor_ids and connection.tenant.has_features(features.ONBOARDING_WORKFLOWS):
        proceed_stages = set()
        completeness_service = OnBoardingCompleteness()
        results, vendor_completed_stages = completeness_service.recalculate(vendor_ids, workflow_id, context_id)

        while vendor_completed_stages != proceed_stages:
            to_proceed = vendor_completed_stages.difference(proceed_stages)
            source.append('recalculate_loop')
            vendors_to_run_post_actions = OnBoardingWorkflow.proceed_vendors_with_workflow(
                [{'vendor': stage[0], 'template_id': stage[1], 'context': stage[2]} for stage in to_proceed], notify, source=source
            )
            if vendors_to_run_post_actions:
                OnBoardingStagesForVendor.objects.post_apply_template_actions(
                    (requested_by_id or get_current_user_id()), vendors_to_run_post_actions, context_id
                )

            proceed_stages = vendor_completed_stages
            results, vendor_completed_stages = completeness_service.recalculate(vendor_ids, workflow_id, context_id)

    if vendor_ids:
        add_task_to_batch(indexer.update_vendors, list(vendor_ids))


@app.task()
def regenerate_dynamic_managers_for_stage_task(stage_id: int):
    from onboarding.models import OnBoardingStage, create_dynamic_managers_for_stage
    from openings.models import JobOpening

    stage = OnBoardingStage.objects.filter(pk=stage_id).first()

    if stage and stage.workflow:
        openings = JobOpening.objects.filter(onboarding_workflow=stage.workflow)
        for opening in openings:
            create_dynamic_managers_for_stage(stage, opening)


@app.task()
def apply_onboarding_templates_post_actions(
    requested_by_id: int,
    vendor_ids: list[int],
    notify: bool = True,
    *,
    recalculate_completeness: bool = True,
    source: list[str] | None = None,
    context_id: str | None = None,
):
    from onboarding.models import OnBoardingStagesForVendor
    from vendors.models.vendor import filter_vendor_ids_by_feature

    source = source or []
    source.append('apply_onboarding_templates_post_actions')

    vendor_ids = filter_vendor_ids_by_feature(vendor_ids, features.ONBOARDING)
    if vendor_ids:
        OnBoardingStagesForVendor.objects.post_apply_template_actions(requested_by_id, vendor_ids, context_id)
        if recalculate_completeness:
            recalculate_onboarding_completeness(
                list(vendor_ids), notify=notify, requested_by_id=requested_by_id, source=source, context_id=context_id
            )


@app.task()
def apply_onboarding_templates_post_actions_on_stage_update(requested_by_id: int, vendor_ids: list[int], notify: bool = True):
    with dont_send_notifications([BuyerSentDocumentForSignature]):
        apply_onboarding_templates_post_actions(
            requested_by_id, vendor_ids, notify, source=['apply_onboarding_templates_post_actions_on_stage_update']
        )


@app.task(trail=False)
def reminder_spawner(vendor_id: int, workflow_id: int, stage_id: int, stage_for_vendor_id: int | None = None):
    # Not active Vendors should never get a reminder. We don't filter them out here because this task itself shouldn't
    # be executed if Vendor isn't active
    from preferences.models import ETAEntry, OnBoardingReminders

    eta_entries = []
    for reminder in OnBoardingReminders.objects.all():
        start_time = timezone.now() + timedelta(seconds=reminder.get_seconds())
        eta_entries.append(
            ETAEntry(
                type=ETAEntry.ONBOARDING_REMINDER,
                start_time=start_time,
                celery_task_data={
                    'vendor_id': vendor_id,
                    'workflow_id': workflow_id,
                    'stage_id': stage_id,
                    'stage_for_vendor_id': stage_for_vendor_id,
                },
            )
        )

    if eta_entries:
        ETAEntry.objects.bulk_create(eta_entries)

    if settings.TEST_MODE:
        for eta_entry in eta_entries:
            eta_entry.run_eta_task()


@app.task(trail=False)
def remind_about_finishing_onboarding_stage(
    vendor_id: int, workflow_id: int, stage_id: int, stage_for_vendor_id: int | None = None, eta_entry_id: int | None = None
):
    from events.event_types import VendorEnteredStage
    from onboarding.models import (
        OnBoardingStage,
        OnBoardingStagesForVendor,
        OnBoardingWorkflowsForVendor,
    )
    from openings.models import JobOpening
    from preferences.models import ETAEntry
    from vendors.models import Vendor

    eta_entry = None
    if eta_entry_id:
        eta_entry = ETAEntry.objects.filter(id=eta_entry_id, type=ETAEntry.ONBOARDING_REMINDER).first()

    stages_for_vendor = OnBoardingStagesForVendor.objects.filter(vendor=vendor_id, workflow=workflow_id, onboarding_template=stage_id)
    if stage_for_vendor_id:
        stages_for_vendor = stages_for_vendor.filter(pk=stage_for_vendor_id)

    stage_for_vendor = stages_for_vendor.first()
    if not stage_for_vendor:
        if eta_entry:
            eta_entry.save_message("stage_for_vendor not found")
        return

    if stage_for_vendor.context and stage_for_vendor.context.context_type == stage_for_vendor.context.CONTEXT_TYPE_JOB_OPENING:
        job_opening = stage_for_vendor.context.context_object
        if job_opening and job_opening.status == JobOpening.DISABLED:
            if eta_entry:
                eta_entry.save_message("JobOpening is disabled")
            return

    if stage_for_vendor.progress < 100 and not stage_for_vendor.vendor_completed:
        # reminder is only for vendor
        vendor = Vendor.objects.filter(id=vendor_id).first()
        vendor_workflow = OnBoardingWorkflowsForVendor.objects.filter(
            workflow_id=workflow_id, vendor=vendor, context=stage_for_vendor.context
        ).first()
        if vendor_workflow.disqualified:
            if eta_entry:
                eta_entry.save_message("Workflow disqualified")
        else:
            VendorEnteredStage(vendor_workflow.pk, OnBoardingStage.objects.filter(id=stage_id).first(), vendor, reminder_for_vendor=True)
            if eta_entry:
                eta_entry.save_message(f"VendorEnteredStage sent for Vendor ID: {vendor.id}")
    elif eta_entry:
        eta_entry.save_message("Progress is 100% or vendor_completed=True")


@app.task(bind=True, max_retries=5, trail=False)
def retry_process_action_stage(self, vendor_ids: list[int], stage_id: int, context_name: str):
    from random import randint

    from events.event_types import OnBoardingActionStageRetrySucceeded
    from onboarding.models import (
        OnBoardingContext,
        OnBoardingStage,
        OnBoardingStagesForVendor,
    )

    stage = OnBoardingStage.objects.get(pk=stage_id)
    context = OnBoardingContext.objects.get(name=context_name)

    failed_action, error_msg = OnBoardingStagesForVendor.objects.execute_actions_for_stage(
        vendor_ids, stage, context, source=['retry_process_action_stage']
    )

    if failed_action:
        delay_sec = (self.request.retries + 1) * 10 * 60 + (randint(5, 10) * 60)
        self.retry(countdown=delay_sec)
    else:
        OnBoardingActionStageRetrySucceeded(vendor_ids=", ".join(str(v) for v in vendor_ids), stage_id=stage_id)
        # run recalculate to move Vendor to next stage
        recalculate_onboarding_completeness(vendor_ids, notify=True, source=['retry_process_action_stage'], context_id=context.name)


@app.task(queue=settings.QUEUE_BATCH)
def export_onboarding_actions_for_buyer_task(workflow_pk: int, date_start: datetime | None = None, date_end: datetime | None = None):
    from onboarding.models import OnBoardingWorkflow, onboarding_actions_csv_content

    workflow = OnBoardingWorkflow.objects.get(pk=workflow_pk)
    csv_contents = onboarding_actions_csv_content(workflow, date_start, date_end)
    save_prepared_file_to_s3(csv_contents, "text/csv")


@app.task(queue=settings.QUEUE_BATCH)
def export_onboarding_to_pdf_task(context):
    from onboarding.onboarding_summary import render_vendor_onboarding_summary_pdf

    content = render_vendor_onboarding_summary_pdf(context)
    save_prepared_file_to_s3(content.getvalue(), "application/pdf")


@app.task()
def lock_completed_stages(tenant_id: int, lock: bool):
    from django.db import connection

    from clients.models import Client
    from onboarding.models import OnBoardingStagesForVendor

    if connection.tenant:
        actual_tenant = Client.tenants.get(pk=connection.tenant.pk)
    else:
        actual_tenant = Client.objects.first().get_public()
    connection.set_tenant(Client.tenants.get(pk=tenant_id))
    if lock:
        OnBoardingStagesForVendor.objects.filter(Q(progress=100) | Q(vendor_completed=True)).update(is_locked=True)
    else:
        OnBoardingStagesForVendor.objects.update(is_locked=False)
    connection.set_tenant(actual_tenant)


@app.task()
def notify_vendors_about_stage_changes(vendor_ids: list[int], stage_id: int, context_name: str, executor_id: int | None):
    if executor_id:
        set_user_in_task(User.objects.get(id=executor_id))

    OnBoardingTemplateEdited(vendor_ids=vendor_ids, template_id=stage_id, context_name=context_name)


@app.task()
def reset_vendors_with_empty_stage(workflow_id: int, vendor_ids: list[int] | None = None, *, recalculate_completeness=False, source=None):
    from onboarding.models import (
        OnBoardingStagesForVendor,
        OnBoardingWorkflow,
        OnBoardingWorkflowsForVendor,
    )

    source = source or []
    source.append('reset_vendors_with_empty_stage')

    workflow = OnBoardingWorkflow.objects.get(id=workflow_id)
    vendor_workflows = OnBoardingWorkflowsForVendor.objects.filter(workflow=workflow, current_stage=None).select_related('workflow')
    if vendor_ids:
        vendor_workflows = vendor_workflows.filter(vendor_id__in=vendor_ids)

    reset_vendor_ids = []
    if connection.tenant.has_features(features.ONBOARDING_STAGE_LOCKING):
        # in Stage locking we can't move to first stage because it is already locked
        for vendor_workflow in list(vendor_workflows):
            try:
                next_stage = vendor_workflow.next_stage()
                if next_stage:
                    vendor_workflow.move_to_stage(
                        next_stage,
                        recalculate_completeness=recalculate_completeness,
                        source=[*source, 'reset_vendors_with_empty_stage_1'],
                    )
                    reset_vendor_ids.append(vendor_workflow.vendor_id)
                else:
                    next_stage = (
                        OnBoardingStagesForVendor.objects.filter(workflow_for_vendor=vendor_workflow, context=vendor_workflow.context)
                        .order_by('created_at')
                        .last()
                    )
                    if next_stage:
                        vendor_workflow.move_to_stage(
                            next_stage.onboarding_template,
                            lock_and_hold=False,
                            recalculate_completeness=recalculate_completeness,
                            source=[*source, 'reset_vendors_with_empty_stage_2'],
                        )
                        reset_vendor_ids.append(vendor_workflow.vendor_id)
            except Exception:
                logger.exception("Error when moving vendor to workflow stage", extra={'tenant_name': connection.tenant.name})

    else:
        first_stage = workflow.get_first_stage()
        if first_stage:
            for vendor_workflow in list(vendor_workflows):
                try:
                    vendor_workflow.move_to_stage(
                        first_stage,
                        recalculate_completeness=recalculate_completeness,
                        source=[*source, 'reset_vendors_with_empty_stage_3'],
                    )
                    reset_vendor_ids.append(vendor_workflow.vendor_id)
                except Exception:
                    logger.exception("Error when moving vendor to workflow stage", extra={'tenant_name': connection.tenant.name})

    return reset_vendor_ids


def recalculate_workflow_async(
    vendor_ids: list[int], /, workflow_id: int, *, reset_empty_stages=False, recalculate_completeness=True, source=None
):
    source = source or []
    source.append('recalculate_workflow_async')

    queue = settings.QUEUE_BATCH if len(vendor_ids) > 100 else settings.QUEUE_DEFAULT
    task = None

    if reset_empty_stages:
        task = reset_vendors_with_empty_stage.s(
            workflow_id=workflow_id, vendor_ids=vendor_ids, recalculate_completeness=False, source=source
        ).set(queue=settings.QUEUE_DEFAULT)

    if recalculate_completeness:
        if task:
            if vendor_ids:
                task = task | recalculate_onboarding_completeness.si(vendor_ids=vendor_ids, source=source).set(queue=queue)
            else:
                task = task | recalculate_onboarding_completeness.s(source=source).set(queue=queue)
        else:
            task = recalculate_onboarding_completeness.s(vendor_ids=vendor_ids, source=source).set(queue=queue)

    if task:
        task.apply_async()


@app.task()
def update_workflow_vendors_count(workflow_ids: list[int]):
    """Update the vendors count for the specified workflows."""
    from onboarding.models import OnBoardingWorkflow, OnBoardingWorkflowsForVendor

    OnBoardingWorkflow.objects.filter(id__in=workflow_ids).update(
        vendors_count=Coalesce(
            Subquery(
                OnBoardingWorkflowsForVendor.objects.values('workflow_id')
                .filter(workflow_id=OuterRef('id'))
                .filter(Q(vendor__email_verified=True) & Q(vendor__archived=False))
                .annotate(vendors_count=Count('vendor'))
                .values('vendors_count')[:1],
            ),
            0,
        )
    )


@app.task()
def update_stage_vendors_count(stage_ids: list[int]):
    """Update the vendors count for the specified stages."""
    from onboarding.models import OnBoardingStage, OnBoardingStagesForVendor

    OnBoardingStage.objects.filter(id__in=stage_ids).update(
        vendors_count=Coalesce(
            Subquery(
                OnBoardingStagesForVendor.objects.values('onboarding_template_id')
                .filter(onboarding_template_id=OuterRef('id'))
                .filter(Q(vendor__email_verified=True) & Q(vendor__archived=False))
                .annotate(vendors_count=Count('vendor'))
                .values('vendors_count')[:1],
            ),
            0,
        )
    )
