# -*- coding: utf-8 -*-
# Generated by Django 1.9.13 on 2020-03-13 15:56
from __future__ import unicode_literals

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import shortlist.current_user
import shortlist.db_fields


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('onboarding', '0005_auto_20200206_0812'),
    ]

    operations = [
        migrations.CreateModel(
            name='InformationStageConfiguration',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('jobopening', 'jobopening'), ('tasktemplate', 'tasktemplate'), ('onboardingstage', 'onboardingstage')], max_length=255)),
                ('name', models.CharField(max_length=255)),
                ('order', models.IntegerField()),
            ],
            options={
                'ordering': ('order',),
            },
        ),
        migrations.CreateModel(
            name='OnBoardingInformationStageSnapshot',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('data', shortlist.db_fields.JSONField(default=dict)),
                ('created_by', models.ForeignKey(default=shortlist.current_user.get_current_user, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('stage_for_vendor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='snapshots', to='onboarding.OnBoardingStagesForVendor')),
            ],
        ),
        migrations.AddField(
            model_name='onboardingstage',
            name='proceed_button_label',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='onboardingdynamicmanagers',
            name='onboarding_managers',
            field=models.ManyToManyField(blank=True, null=True, related_name='_onboardingdynamicmanagers_onboarding_managers_+', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='onboardingstage',
            name='stage_type',
            field=models.CharField(blank=True, choices=[('request_data', 'request_data'), ('data_collection', 'data_collection'), ('answer_questions', 'answer_questions'), ('request_documents', 'request_documents'), ('sign_agreements', 'sign_agreements'), ('bank_details', 'bank_details'), ('tax_information', 'tax_information'), ('portfolio', 'portfolio'), ('interview', 'interview'), ('action', 'action'), ('docebo', 'docebo'), ('information', 'information')], default=None, max_length=17, null=True),
        ),
        migrations.AddField(
            model_name='informationstageconfiguration',
            name='onboarding_stage',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='information_stage_config', to='onboarding.OnBoardingStage'),
        ),
    ]
