from django.conf import settings
from rest_framework import status

from clients import features
from onboarding.models import OnBoardingTemplateMixIn, OnBoardingStage
from onboarding.tests.utils import OnBoardingTestCase
from preferences.models import CustomField
from shortlist.tests.helpers import features_enabled


class ProfilePhotoCustomFieldConfigurationTest(OnBoardingTestCase):
    with_admin_user = True

    @features_enabled([features.ONBOARDING_WORKFLOWS])
    def test_onboarding_stage_available_fields_contains_profile_photo(self):
        available_fields = self.check_response(
            status.HTTP_200_OK, self.api_client.get(self.api_buyer_onboarding_template_available_fields)
        ).data

        self.assertIn(CustomField.TYPE_PROFILE_PHOTO, available_fields)
        self.assertIn(CustomField.TYPE_PROFILE_PHOTO, OnBoardingStage.available_fields())

    @features_enabled([features.ONBOARDING_WORKFLOWS])
    def test_add_profile_photo_field_to_request_data_stage(self):
        workflow = self.create_ow()
        stage_data = self._create_stage(
            workflow=workflow.id,
            stage_type=OnBoardingTemplateMixIn.STAGE_REQUEST_DATA,
            custom_fields_template=dict(
                template_fields=[
                    dict(type=CustomField.TYPE_PROFILE_PHOTO, propagator={"profile_field": "profile_photo"}),
                ]
            ),
        )
        profile_photo_cf_data = stage_data["custom_fields_template"]["template_fields"][0]

        self.assertEqual(profile_photo_cf_data["type"], CustomField.TYPE_PROFILE_PHOTO)
        self.assertEqual(profile_photo_cf_data["propagator"]["profile_field"], "profile_photo")

        profile_photo_cf = CustomField.objects.get(id=profile_photo_cf_data["id"])
        self.assertEqual(profile_photo_cf.type, CustomField.TYPE_PROFILE_PHOTO)
        self.assertEqual(profile_photo_cf.vendor_field_propagator.profile_field, "profile_photo")


class ProfilePhotoCustomFieldTest(OnBoardingTestCase):
    with_admin_user = True

    def setUp(self):
        super().setUp()

        self.workflow = self.create_ow()
        stage_data = self._create_stage(
            workflow=self.workflow.id,
            stage_type=OnBoardingTemplateMixIn.STAGE_REQUEST_DATA,
            custom_fields_template=dict(
                template_fields=[
                    dict(type=CustomField.TYPE_PROFILE_PHOTO, propagator={"profile_field": "profile_photo"}),
                ]
            ),
        )
        self.stage = OnBoardingStage.objects.get(id=stage_data["id"])
        self.profile_photo_cf_id = stage_data["custom_fields_template"]["template_fields"][0]["id"]
        self.vendor = self._create_vendor()
        self.vendor_workflow = self._add_vendor_to_workflow(self.workflow, self.vendor)
        self.vendor_stage = self._get_vendor_stage(self.vendor_workflow, self.stage)

    def _get_vendor_logo_path(self, vendor):
        return "//{domain}/{bucket}/{path}".format(
            domain=settings.AWS_STORAGE_VALID_DOMAINS[settings.AWS_DEFAULT_REGION],
            bucket=settings.AWS_STORAGE_TENANT_LOGOS_NAME,
            path=self.tenant.storage_path('vendor-logo', vendor.slug),
        )

    @features_enabled([features.ONBOARDING_WORKFLOWS])
    def test_set_profile_logo_path_on_request_data_stage(self):
        logo_path = f"{self._get_vendor_logo_path(self.vendor)}test.jpg"

        self._submit_vendor_stage(self.vendor_stage, custom_fields={self.profile_photo_cf_id: logo_path})

        self.vendor.refresh_from_db()
        self.vendor_stage.refresh_from_db()
        self.assertEqual(self.vendor.logo, logo_path)
        self.assertEqual(self.vendor_stage.custom_fields_data, {str(self.profile_photo_cf_id): logo_path})

    @features_enabled([features.ONBOARDING_WORKFLOWS])
    def test_set_invalid_profile_logo_path_on_request_data_stage(self):
        logo_path = "invalid-path/test.jpg"

        with self.as_vendor():
            data = self.check_response(
                status.HTTP_400_BAD_REQUEST,
                self.api_client.patch(
                    self.api_vendor_stage_data.format(vendor_stage_id=self.vendor_stage.pk),
                    data={'custom_fields': {self.profile_photo_cf_id: logo_path}},
                ),
            ).data

        self.assertEqual(data[self.profile_photo_cf_id][0].code, 'invalid')

    @features_enabled([features.ONBOARDING_WORKFLOWS])
    def test_render_profile_photo_in_onboarding_summary(self):
        logo_path = f"{self._get_vendor_logo_path(self.vendor)}test.jpg"
        self._submit_vendor_stage(self.vendor_stage, custom_fields={self.profile_photo_cf_id: logo_path})

        onboarding_summary_html = self._render_onboarding_summary_html(self.vendor_workflow)

        self.assertIn(f'<img src="{logo_path}"', onboarding_summary_html)
