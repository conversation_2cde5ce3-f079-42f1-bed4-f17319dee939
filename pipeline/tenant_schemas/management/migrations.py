from django.conf import settings
from django.core.management import CommandError
from django.db import connection
from django.db.migrations.executor import MigrationExecutor


def available_apps(apps):
    apps_names = get_app_labels(apps)
    executor = MigrationExecutor(connection)
    return [app for app in apps_names if app in executor.loader.migrated_apps]


def get_app_labels(apps):
    labels = []
    for app in apps:
        app = app.split('.')
        labels.append(app[app.index('apps') - 1] if len(app) > 1 and 'apps' in app else app[-1])
    return labels


def migrate(apps, progress_callback=None, fake=False, fake_initial=False, raise_on_pending=False):
    app_labels = get_app_labels(apps)
    executor = MigrationExecutor(connection, progress_callback=progress_callback)
    targets = executor.loader.graph.leaf_nodes()
    migrations = executor.migration_plan(targets)
    pending_targets = [
        (migration.app_label, migration.name) for migration, status in migrations if migration.app_label in app_labels and not status
    ]
    if raise_on_pending and pending_targets:
        raise CommandError('There are pending migrations to run.')
    executor.migrate(pending_targets, fake=fake, fake_initial=fake_initial)


def migrate_tenant(tenant, progress_callback=None, fake=False, fake_initial=False, raise_on_pending=False):
    connection.set_tenant(tenant, include_public=False)
    apps = settings.TENANT_APPS
    migrate(apps, progress_callback=progress_callback, fake=fake, fake_initial=fake_initial, raise_on_pending=raise_on_pending)
