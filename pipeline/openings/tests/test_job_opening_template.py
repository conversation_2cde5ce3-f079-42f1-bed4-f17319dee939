from rest_framework import status

from clients import features
from openings.models import JobOpening, JobOpeningTemplate
from preferences.models import CustomFieldsTemplate, CustomField
from shortlist.tests.helpers import TenantTestCase


class JobOpeningTemplatesTest(TenantTestCase):
    with_staff_user = True

    openings_url = '/api/job_openings/'
    templates_url = '/api/job_opening_templates/'

    def test_create_jo_from_template(self):
        description = self.factory.sentence()
        jo_name = self.factory.sentence()
        template_data = {
            "name": self.factory.sentence(),
            "job_opening_name_field": "name",
            "fields": [
                {"field": "name", "hidden": False, "disabled": False, "value": self.factory.word(), "order": 1},
                {"field": "description", "hidden": False, "disabled": True, "value": description, "order": 2},
            ],
            "task_template_fields": {
                "name": "",
                "date_start": "",
                "date_end": "",
                "vendor": "",
                "custom_fields_templates": [],
                "custom_fields": [],
                "files": [],
                "skills": [],
                "status": "draft",
                "id": None,
                "work_dates": [],
                "managers": [],
                "budget_rate_type": 1,
            },
            "task_template_fields_config": [],
        }

        response = self.check_response(status.HTTP_201_CREATED, self.api_client.post(self.templates_url, template_data))
        jo_data = {"name": jo_name, "job_opening_template": response.data.get('id'), "description": "wrong value"}
        created = self.check_response(status.HTTP_201_CREATED, self.api_client.post(self.openings_url, jo_data))

        jo = JobOpening.objects.get(pk=created.data.get('id'))
        self.assertEqual(jo.name, jo_name)
        self.assertEqual(jo.description, description)

    def test_create_jo_ignore_name_field(self):
        # name field is applied only on frontend
        description_name = self.factory.sentence()
        ui_description_name = self.factory.sentence()
        template_name = self.factory.sentence()

        template_data = {
            "name": self.factory.sentence(),
            "job_opening_name_field": "description",
            "fields": [
                {"field": "name", "hidden": False, "disabled": False, "value": template_name, "order": 1},
                {"field": "description", "hidden": False, "disabled": False, "value": description_name, "order": 2},
            ],
            "task_template_fields": {
                "name": "",
                "date_start": "",
                "date_end": "",
                "vendor": "",
                "custom_fields_templates": [],
                "custom_fields": [],
                "files": [],
                "skills": [],
                "status": "draft",
                "id": None,
                "work_dates": [],
                "managers": [],
                "budget_rate_type": 1,
            },
            "task_template_fields_config": [],
        }

        response = self.check_response(status.HTTP_201_CREATED, self.api_client.post(self.templates_url, template_data))
        jo_data = {"job_opening_template": response.data.get('id'), "description": ui_description_name}
        created = self.check_response(status.HTTP_201_CREATED, self.api_client.post(self.openings_url, jo_data))

        jo = JobOpening.objects.get(pk=created.data.get('id'))
        self.assertEqual(jo.name, template_name)

    def _create_global_custom_template_fields(self):
        gt = CustomFieldsTemplate.objects.create(name=self.factory.sentence(), model=CustomFieldsTemplate.JOB_OPENING_MODEL)
        f1 = CustomField.objects.create(
            template=gt,
            label=self.factory.sentence(),
            type=CustomField.TYPE_CHOICE,
            mandatory=True,
            visible_to_vendors=True,
            order=1,
            choices=["a", "b", "c"],
        )
        f2 = CustomField.objects.create(
            template=gt, label=self.factory.sentence(), type=CustomField.TYPE_TEXT_LINE, mandatory=True, visible_to_vendors=False, order=2
        )
        gt.update_field_count()
        return gt, f1, f2

    def test_custom_fields_template_validation(self):
        gt, f1, f2 = self._create_global_custom_template_fields()
        description_name = self.factory.sentence()
        template_name = self.factory.sentence()

        with self.features_enabled(features.MARKETPLACE):
            data = {
                "name": self.factory.name(),
                "job_title": None,
                "description": self.factory.sentence(),
                "location": "",
                "onboarding_workflow": None,
                "status": "draft",
                "archived": False,
                "custom_fields": {},
                "custom_fields_templates": [],
            }
            response = self.check_response(status.HTTP_400_BAD_REQUEST, self.api_client.post("/api/job_openings/", data=data))
            self.assertEqual(
                response.data,
                {f1.pk: [f"{f1.label}: This field is required"], f2.pk: [f"{f2.label}: This field is required"]},
            )
            jot = JobOpeningTemplate.objects.create(
                name=self.factory.sentence(),
                job_opening_name_field="description",
                fields=[
                    {"field": "name", "hidden": False, "disabled": False, "value": template_name, "order": 1},
                    {"field": "description", "hidden": False, "disabled": False, "value": description_name, "order": 2},
                    {"field": f"custom_{f1.pk}", "hidden": True, "disabled": False, "value": None, "order": 3},
                    {"field": f"custom_{f2.pk}", "hidden": False, "disabled": True, "value": "Awesome value", "order": 4},
                ],
                task_template_fields={},
                task_template_fields_config=[],
            )
            data['job_opening_template'] = jot.id
            self.check_response(status.HTTP_201_CREATED, self.api_client.post("/api/job_openings/", data=data))

    def test_create_invalid_job_opening_template(self):
        gt, f1, f2 = self._create_global_custom_template_fields()
        description = self.factory.sentence()
        jo_name = self.factory.sentence()
        template_data = {
            "name": self.factory.sentence(),
            "job_opening_name_field": "name",
            "fields": [
                {"field": "name", "hidden": False, "disabled": True, "value": jo_name, "order": 1},
                {"field": "description", "hidden": False, "disabled": False, "value": description, "order": 2},
                {"field": f"custom_{f1.pk}", "hidden": True, "disabled": True, "value": None, "order": 3},
                {"field": f"custom_{f2.pk}", "hidden": False, "disabled": True, "value": None, "order": 4},
            ],
            "task_template_fields": {},
            "task_template_fields_config": [],
        }

        response = self.check_response(status.HTTP_400_BAD_REQUEST, self.api_client.post(self.templates_url, template_data))
        self.assertEqual(
            response.data,
            {f2.pk: [f'{f2.label}: If field is supposed to be disabled You have to provide value for it']},
        )

    def test_jo_name_validation_in_job_opening_template(self):
        gt, f1, f2 = self._create_global_custom_template_fields()
        description = self.factory.sentence()
        jo_name = self.factory.sentence()
        template_data = {
            "name": self.factory.sentence(),
            "job_opening_name_field": f"custom_{f1.pk}",
            "fields": [
                {"field": "name", "hidden": False, "disabled": True, "value": jo_name, "order": 1},
                {"field": "description", "hidden": False, "disabled": True, "value": description, "order": 2},
                {"field": f"custom_{f1.pk}", "hidden": False, "disabled": True, "value": None, "order": 3},
            ],
            "task_template_fields": {},
            "task_template_fields_config": [],
        }
        response = self.check_response(status.HTTP_400_BAD_REQUEST, self.api_client.post(self.templates_url, template_data))
        self.assertEqual(
            response.data,
            {
                'fields': {f'custom_{f1.pk}': ['If name is supposed to be hidden or disabled You have to provide value for it']},
                f1.pk: [f'{f1.label}: If field is supposed to be disabled You have to provide value for it'],
            },
        )

        template_data['fields'][2]['disabled'] = False
        template_data['fields'][2]['hidden'] = True

        response = self.check_response(status.HTTP_400_BAD_REQUEST, self.api_client.post(self.templates_url, template_data))
        self.assertEqual(
            response.data,
            {'fields': {f'custom_{f1.pk}': ['If name is supposed to be hidden or disabled You have to provide value for it']}},
        )
        template_data['fields'][2]['value'] = "Awesome JobOpening name"
        self.check_response(status.HTTP_201_CREATED, self.api_client.post(self.templates_url, template_data))
