from django.db import connection, transaction
from django.db.models.query_utils import Q
from django.http import Http404
from django.shortcuts import get_object_or_404
from django.utils.functional import cached_property
from django.utils.timezone import now
from rest_framework import status
from rest_framework.decorators import action, permission_classes
from rest_framework.generics import RetrieveAPIView
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ModelViewSet, ReadOnlyModelViewSet

from clients import features
from documents.models import RequestedDocument
from events.event_types import VendorRegistrationCompleted, VendorSignedUp
from onboarding.models import (
    OnBoardingContext,
    OnBoardingStagesForVendor,
    OnBoardingWorkflowsForVendor,
)
from onboarding.serializers import OnBoardingTemplateSerializerReadOnlyOS3
from onboarding.tasks import apply_onboarding_templates_post_actions
from openings.filters import (
    CandidateFilterSet,
    JobOpeningAttachmentsFilterSet,
    JobOpeningFilterSet,
)
from openings.models import (
    Candidate,
    DistributionTemplate,
    HasCandidates,
    JobOpening,
    JobOpeningAttachment,
    JobOpeningBulkImport,
    JobOpeningBulkImportConfig,
    JobOpeningSupplier,
    JobOpeningTemplate,
)
from openings.serializers import (
    CandidateSerializer,
    CandidateStatusSerializer,
    DistributionTemplateSerializer,
    JobOpeningAttachmentPublicSerializer,
    JobOpeningAttachmentSerializer,
    JobOpeningListSerializer,
    JobOpeningPublicListSerializer,
    JobOpeningPublicListWithStartDateSerializer,
    JobOpeningPublicSerializer,
    JobOpeningSerializer,
    JobOpeningTemplateSerializer,
    JobOpeningWithVendorWorkflowSerializer,
    SupplierSerializer,
)
from openings.services.reserialize_data import overwrite_with_job_opening_template
from openings.tasks import update_job_opening_counts
from shortlist import auth
from shortlist.cache_utils import invalidate_custom_cache_page
from shortlist.current_user import get_current_user
from shortlist.helpers import global_action, global_link
from shortlist.pagination import OptionalPagination
from shortlist.permissions import (
    IsSupplier,
    IsUser,
    IsVendor,
    Or,
    Permission,
    TenantFeaturesEnabled,
    TypedPermissionMixin,
    check_permission,
)
from shortlist.search import indexer
from shortlist.serializers import limit_fields
from shortlist.track_tasks import add_task_to_batch, start_task_tracking
from shortlist.utils import filepicker_policy, get_ip, serve_file
from shortlist.views import BulkImportViewSet
from vendors.models import Vendor, VendorGroup, VendorSignUp, VendorType
from vendors.serializers.vendor_serializers import VendorForVendorSerializer


class JobOpeningsBuyerViewSet(TypedPermissionMixin, ModelViewSet):
    permission_app = 'buyer'
    permission_type = 'job opening'
    permission_classes = [TenantFeaturesEnabled(features.MARKETPLACE)]
    queryset = JobOpening.objects.select_related('created_by', 'onboarding_workflow').all()
    serializer_class = JobOpeningSerializer
    pagination_class = OptionalPagination
    filterset_class = JobOpeningFilterSet

    def get_serializer_class(self):
        if self.action == 'list':
            requested_fields = self.request.query_params.get('fields', '').split(',')
            fields = set(requested_fields).intersection(JobOpeningListSerializer.Meta.fields) or JobOpeningListSerializer.Meta.fields
            return limit_fields(JobOpeningListSerializer, fields)
        else:
            return self.serializer_class

    def get_serializer_context(self):
        context = super().get_serializer_context()
        # Note: query params are passed as string, so we have to explicitly cast it to boolean
        context['validate_ten99p_fields'] = self.request.query_params.get('validate_ten99p') == "true"
        return context

    def get_queryset(self):
        return self.queryset.filtered_by_permission()

    def filter_queryset(self, queryset):
        current_user = get_current_user()

        if self.request.query_params.get('my_jobs_only', None) and current_user:
            queryset = queryset.filter(created_by=current_user)
        if self.action == 'list' and not self.request.query_params.get('include_archived', None):
            queryset = queryset.exclude(archived=True)

        return super().filter_queryset(queryset)

    def create(self, request, *args, **kwargs):
        data = request.data
        data, candidates, suppliers = overwrite_with_job_opening_template(data)
        serializer = JobOpeningSerializer(data=data)
        if serializer.is_valid():
            self.object = serializer.create(serializer.validated_data)
            JobOpeningSupplier.objects.bulk_set([self.object.id], suppliers)
            Candidate.objects.bulk_set([self.object.id], candidates, Candidate.INVITED)
            new = JobOpeningSerializer(instance=self.object)
            headers = self.get_success_headers(new.data)
            return Response(new.data, status=status.HTTP_201_CREATED, headers=headers)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, *args, **kwargs):
        start_task_tracking()

        try:
            self.object = self.get_object()
        except Http404:
            self.object = None

        if (
            self.object.status in JobOpening.DRAFT_STATUSES
            and request.data.get('status', JobOpening.DRAFT) not in JobOpening.DRAFT_STATUSES
        ):
            check_permission(Permission.buyer('job opening', 'publish'), request, self)

        if self.object.status == JobOpening.PUBLISHED and not self.object.add_partners_disabled:
            groups_to_add = request.data.get('vendor_groups_to_invite', [])
            self.object.add_vendors_from_groups([], groups_to_add)
            emails_to_add = request.data.get('vendor_emails_to_add', [])
            self.object.add_vendors_from_emails(emails_to_add)

        return super().update(request, *args, **kwargs)

    @action(
        methods=['POST'],
        detail=True,
        permission_classes=[
            TenantFeaturesEnabled(features.MARKETPLACE),
            Or(Permission.buyer('job opening', 'edit'), Permission.buyer('job opening', 'publish')),
        ],
    )
    def set_status(self, request, *args, **kwargs):
        try:
            self.object = self.get_object()
        except Http404:
            return Response(status=status.HTTP_404_NOT_FOUND)

        status_to_set = request.data.get('status')
        if not status_to_set:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        if self.object.status in JobOpening.DRAFT_STATUSES and status_to_set not in JobOpening.DRAFT_STATUSES:
            check_permission(Permission.buyer('job opening', 'publish'), request, self)

        if status_to_set in [JobOpening.PUBLISHED, JobOpening.COMPLETED, JobOpening.DISABLED]:
            invalidate_custom_cache_page(key_postfix='/api/public/job_openings/')

        start_task_tracking()

        serializer = self.get_serializer(self.object, data={'status': status_to_set}, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def _set_archived(self, value):
        obj = self.get_object()
        if obj.archived != value:
            obj.archived = value
            obj.save(update_fields=['archived'])
        serializer = self.get_serializer(obj)
        return Response(serializer.data)

    @action(detail=True, serializer_class=CandidateSerializer)
    def candidates(self, request, *args, **kwargs):
        try:
            obj = self.get_object()
        except Http404:
            obj = None

        vendor_slug = request.query_params.get('vendor_slug', None)
        query = obj.candidates.select_related('vendor', 'job_opening__onboarding_workflow').filter(vendor__archived=False)
        if vendor_slug:
            query = query.filter(vendor__slug=vendor_slug)

        serializer = self.get_serializer(instance=query, many=True)
        return Response(serializer.data)

    @action(
        methods=['PATCH'],
        detail=True,
        serializer_class=CandidateSerializer,
        permission_classes=[TenantFeaturesEnabled(features.MARKETPLACE)],
    )
    def candidate_status(self, request, *args, **kwargs):
        start_task_tracking()
        try:
            obj = self.get_object()
        except Http404:
            obj = None
        vendor_slug = request.query_params.get('vendor_slug', None)
        candidate = get_object_or_404(obj.candidates, vendor__slug=vendor_slug)
        serializer = self.get_serializer(instance=candidate, data=self.request.data.copy(), many=False)
        if serializer.is_valid():
            serializer.save()
            add_task_to_batch(indexer.index_job_applications, [candidate.id])
            return Response(serializer.data)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, serializer_class=SupplierSerializer)
    def suppliers(self, request, *args, **kwargs):
        try:
            obj = self.get_object()
        except Http404:
            obj = None
        vendor_slug = request.query_params.get('vendor_slug', None)
        query = obj.suppliers.select_related('vendor').filter(vendor__email_verified=True, vendor__archived=False)
        if vendor_slug:
            query = query.filter(vendor__slug=vendor_slug)
        serializer = self.get_serializer(instance=query, many=True)
        return Response(serializer.data)

    @action(
        methods=['POST'],
        detail=True,
        permission_classes=[TenantFeaturesEnabled(features.MARKETPLACE), Permission.buyer('job opening', 'create')],
    )
    def duplicate(self, request, *args, **kwargs):
        new_job_opening = JobOpening.objects.duplicate(self.get_object(), duplicated_manually=True)
        serializer = self.get_serializer(new_job_opening)
        return Response(serializer.data)

    @action(
        methods=['POST'],
        detail=True,
        permission_classes=[TenantFeaturesEnabled(features.MARKETPLACE), Permission.buyer('job opening', 'edit')],
    )
    def archive(self, request, *args, **kwargs):
        return self._set_archived(True)

    @action(
        methods=['POST'],
        detail=True,
        permission_classes=[TenantFeaturesEnabled(features.MARKETPLACE), Permission.buyer('job opening', 'edit')],
    )
    def unarchive(self, request, *args, **kwargs):
        return self._set_archived(False)

    @action(
        methods=['GET', 'POST', 'DELETE'],
        detail=True,
        permission_classes=[TenantFeaturesEnabled(features.MARKETPLACE), Permission.buyer('job opening', 'edit')],
    )
    def sharing(self, request, *args, **kwargs):
        job_opening = self.get_object()
        if job_opening.invite_link_disabled:
            return Response("Sharing this JobOpening is disallowed", status=status.HTTP_400_BAD_REQUEST)
        if job_opening.vendors_number is not None:
            return Response("Can't share JobOpenings with limited vendors", status=status.HTTP_400_BAD_REQUEST)

        if request.method == 'POST':
            job_opening.set_key()
            return Response(status=status.HTTP_201_CREATED, data=job_opening.key)

        if request.method == 'DELETE':
            job_opening.clear_key()
            return Response(status=status.HTTP_204_NO_CONTENT)

        return Response(job_opening.key)

    @global_link()
    def upload_file_config(self, request, **kwargs):
        return Response(filepicker_policy(JobOpeningAttachment.get_upload_path()))

    @action(methods=['POST'], detail=True, permission_classes=[Permission.buyer('job opening', 'view', Permission.ANY)])
    def request_approval(self, request, **kwargs):
        job_opening = self.get_object()
        requested = job_opening.request_approval()
        if requested:
            return Response(status=status.HTTP_200_OK)
        return Response(status=status.HTTP_400_BAD_REQUEST, data="Only DRAFT Job opening with job approvers can be requested for approval")

    @global_action(methods=['POST'], permission_classes=[Permission.buyer('job opening', 'assign', Permission.ANY)])
    def bulk_set(self, request, *args, **kwargs):
        start_task_tracking()
        vendor_slugs = request.data.get('vendors', [])
        group_slugs = request.data.get('groups', [])
        vendor_ids = Vendor.objects.filter(slug__in=vendor_slugs, vendor_type__is_supplier=False).values_list('id', flat=True)
        supplier_ids = Vendor.objects.filter(slug__in=vendor_slugs, vendor_type__is_supplier=True).values_list('id', flat=True)
        if vendor_ids or supplier_ids or group_slugs:
            unset_all = request.data.get('unset_all', [])
            if unset_all:
                if JobOpening.objects.filter(id__in=unset_all, add_partners_disabled=True).exists():
                    return Response("Remove candidates/suppliers for those JobOpenings is disallowed", status=status.HTTP_400_BAD_REQUEST)
                if JobOpening.objects.filter(status__in=[JobOpening.COMPLETED], id__in=unset_all).exists():
                    return Response("Remove candidates/suppliers is not supported for this status", status=status.HTTP_400_BAD_REQUEST)
                if group_slugs:
                    return Response("Remove groups from multiple JobOpenings is not supported", status=status.HTTP_400_BAD_REQUEST)

                context_ids = OnBoardingContext.objects.filter(app_label=JobOpening._meta.app_label, object_id__in=unset_all).values_list(
                    'name', flat=True
                )
                if supplier_ids:
                    JobOpeningSupplier.objects.filter(vendor__in=supplier_ids, job_opening__in=unset_all).delete()
                    Candidate.objects.filter(vendor__parent_id__in=supplier_ids, job_opening__in=unset_all).delete()
                    if context_ids:
                        OnBoardingStagesForVendor.objects.filter(context__in=context_ids, vendor__in=supplier_ids).delete()
                        OnBoardingWorkflowsForVendor.objects.filter(context__in=context_ids, vendor__in=supplier_ids).delete()
                if vendor_ids:
                    Candidate.objects.filter(vendor__in=vendor_ids, job_opening__in=unset_all).delete()
                    if context_ids:
                        OnBoardingStagesForVendor.objects.filter(context__in=context_ids, vendor__in=vendor_ids).delete()
                        OnBoardingWorkflowsForVendor.objects.filter(context__in=context_ids, vendor__in=vendor_ids).delete()
                return Response(status=status.HTTP_200_OK)
            set_all = JobOpening.objects.filter(
                status__in=[JobOpening.DRAFT, JobOpening.PENDING, JobOpening.PUBLISHED], pk__in=request.data.get('set_all', [])
            ).exclude(pk__in=unset_all)
            if set_all:
                if JobOpening.objects.filter(id__in=set_all, add_partners_disabled=True).exists():
                    return Response("Add candidates/suppliers for those JobOpenings is disallowed", status=status.HTTP_400_BAD_REQUEST)
                set_all_ids = set_all.values_list('id', flat=True)
                JobOpeningSupplier.objects.bulk_set(set_all_ids, supplier_ids)
                Candidate.objects.bulk_set(set_all_ids, vendor_ids, Candidate.INVITED)
                vendor_groups = VendorGroup.objects.filter(slug__in=group_slugs, type=VendorGroup.TYPE_PUBLIC)
                if vendor_groups:
                    for job_opening in set_all:
                        if job_opening.status in JobOpening.DRAFT_STATUSES:
                            job_opening.vendor_groups_to_invite.add(*vendor_groups)
                        else:
                            job_opening.add_vendors_from_groups(vendor_groups)
                return Response(status=status.HTTP_200_OK)
        return Response("Invalid vendors", status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, *args, **kwargs):
        try:
            return super().destroy(request, *args, **kwargs)
        except HasCandidates:
            return Response(status=status.HTTP_400_BAD_REQUEST, data="Cannot delete a job opening which has candidates. Archive it instead")


class JobOpeningAttachmentForBuyerViewSet(TypedPermissionMixin, ModelViewSet):
    permission_app = 'buyer'
    permission_type = 'job opening'
    permission_classes = [TenantFeaturesEnabled(features.MARKETPLACE)]
    queryset = JobOpeningAttachment.objects.all()
    serializer_class = JobOpeningAttachmentSerializer
    filterset_class = JobOpeningAttachmentsFilterSet

    def filter_queryset(self, queryset):
        current_user = get_current_user()
        queryset = super().filter_queryset(queryset)
        if current_user.has_perm(Permission.buyer('job opening', 'view', Permission.ALL)):
            return queryset
        job_openings = JobOpening.objects.filtered_by_permission().values_list('id')
        return queryset.filter(job_opening_id__in=job_openings)

    @action(
        detail=True,
        permission_classes=[TenantFeaturesEnabled(features.MARKETPLACE), Permission.buyer('job opening', 'view', Permission.ANY)],
    )
    def download(self, request, *args, **kwargs):
        obj = self.get_object()
        return serve_file(obj.file_name, obj.file, inline='inline' in request.query_params)


class JobOpeningAttachmentForVendorViewSet(TypedPermissionMixin, ReadOnlyModelViewSet):
    permission_app = 'vendor'
    permission_type = 'job opening'
    permission_classes = [TenantFeaturesEnabled(features.MARKETPLACE)]
    queryset = JobOpeningAttachment.objects.all()
    serializer_class = JobOpeningAttachmentSerializer
    filterset_class = JobOpeningAttachmentsFilterSet

    def filter_queryset(self, queryset):
        vendor = self.request.user.vendor
        queryset = queryset.filter(
            Q(job_opening__candidates__vendor=vendor) | Q(job_opening__suppliers__vendor=vendor),
            job_opening__status=JobOpening.PUBLISHED,
            job_opening__archived=False,
        )
        return super().filter_queryset(queryset)

    @action(detail=True, permission_classes=[TenantFeaturesEnabled(features.MARKETPLACE), Permission.vendor('job opening', 'view')])
    def download(self, request, *args, **kwargs):
        obj = self.get_object()
        return serve_file(obj.file_name, obj.file, inline='inline' in request.query_params)


class JobOpeningsPublicViewSet(ReadOnlyModelViewSet):
    permission_classes = [TenantFeaturesEnabled(features.MARKETPLACE)]
    serializer_class = JobOpeningPublicListSerializer
    queryset = JobOpening.objects.filter(status=JobOpening.PUBLISHED).exclude(archived=True)
    lookup_field = 'slug'

    def filter_queryset(self, queryset):
        slug = self.kwargs.get('slug', None)
        key = self.request.query_params.get('key', None)
        current_user = get_current_user()
        if current_user and current_user.has_perm(Permission.buyer('job opening', 'view', Permission.ANY)):
            # buyer can preview public JO, but draft JO still won't be listed
            queryset = queryset.filter(type=JobOpening.TYPE_PUBLIC)
            if slug:
                # if slug is given buyer is doing preview of drat JO
                queryset = JobOpening.objects.filtered_by_permission()
        else:
            if not slug or not key:
                queryset = queryset.filter(type=JobOpening.TYPE_PUBLIC)
            else:
                queryset = queryset.filter(Q(type=JobOpening.TYPE_PUBLIC) | Q(type=JobOpening.TYPE_INVITE_ONLY, key=key, slug=slug))

        if connection.tenant.has_features(features.PROJECTS_AND_TASKS_TASK_TEMPLATES_IN_JOB_OPENINGS):
            queryset = queryset.select_related('task_template')

        return queryset

    def get_serializer_class(self):
        if self.action == 'list':
            if connection.tenant.has_features(features.PROJECTS_AND_TASKS_TASK_TEMPLATES_IN_JOB_OPENINGS):
                return JobOpeningPublicListWithStartDateSerializer
            else:
                return JobOpeningPublicListSerializer
        else:
            return JobOpeningPublicSerializer


class CandidateStatusViewSet(ReadOnlyModelViewSet):
    permission_classes = [TenantFeaturesEnabled(features.MARKETPLACE), IsVendor]
    model = Candidate
    serializer_class = CandidateStatusSerializer
    filterset_class = CandidateFilterSet
    paginate_by = 10
    queryset = Candidate.objects.filter(
        job_opening__status__in=[JobOpening.PUBLISHED, JobOpening.COMPLETED, JobOpening.DISABLED],
        job_opening__archived=False,
    )
    lookup_field = 'job_opening_id'

    def filter_queryset(self, queryset):
        return super().filter_queryset(queryset.filter(vendor_id=self.request.user.vendor_id))

    @action(detail=True, methods=['PATCH'])
    def status(self, request, *args, **kwargs):
        start_task_tracking()
        job_opening = self.get_object().job_opening
        candidate = job_opening.candidates.get(vendor=self.request.user.vendor)
        serializer = self.get_serializer(instance=candidate, data=request.data.copy(), many=False)
        if serializer.is_valid():
            serializer.save()
            add_task_to_batch(indexer.index_job_applications, [candidate.id])
            return Response(serializer.data)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class JobOpeningForInformationStageView(RetrieveAPIView):
    permission_classes = [TenantFeaturesEnabled(features.MARKETPLACE), IsUser]
    serializer_class = JobOpeningPublicSerializer
    queryset = JobOpening.objects.filter(status__in=[JobOpening.PUBLISHED, JobOpening.COMPLETED]).exclude(archived=True)

    def get_object(self):
        job_opening = super().get_object()
        if (
            self.request.user.is_vendor
            and not job_opening.candidates.filter(vendor=self.request.user.vendor).exists()
            and not job_opening.suppliers.filter(vendor=self.request.user.vendor).exists()
        ):
            return None
        return job_opening

    def filter_queryset(self, queryset):
        queryset = super().filter_queryset(queryset)
        if not self.request.user.is_vendor:
            queryset = queryset.filtered_by_permission()
        return queryset


class JobOpeningForVendorView(RetrieveAPIView):
    permission_classes = [TenantFeaturesEnabled(features.MARKETPLACE), IsVendor]
    serializer_class = JobOpeningPublicSerializer
    queryset = JobOpening.objects.filter(status__in=[JobOpening.PUBLISHED, JobOpening.COMPLETED, JobOpening.DISABLED]).exclude(
        archived=True
    )

    def get_object(self):
        job_opening = super().get_object()
        if (
            job_opening.job_board_visible_for_all_partners
            or job_opening.job_board_groups.filter(vendors__id=self.request.user.vendor_id).exists()
            or job_opening.candidates.filter(vendor=self.request.user.vendor).exists()
            or job_opening.suppliers.filter(vendor=self.request.user.vendor).exists()
        ):
            return job_opening
        return None


class JobOpeningAttachmentPublicViewSet(ReadOnlyModelViewSet):
    permission_classes = [TenantFeaturesEnabled(features.MARKETPLACE)]
    queryset = (
        JobOpeningAttachment.objects.select_related('job_opening', 'custom_field')
        .filter(job_opening__status=JobOpening.PUBLISHED)
        .exclude(job_opening__archived=True)
        .filter(custom_field__visible_to_vendors=True)
    )
    serializer_class = JobOpeningAttachmentPublicSerializer

    def filter_queryset(self, queryset):
        slug = self.request.query_params.get('job_opening', None)
        key = self.request.query_params.get('key', None)
        current_user = get_current_user()
        if current_user and current_user.has_perm(Permission.buyer('job opening', 'view', Permission.ANY)):
            # view for buyer
            if slug:
                queryset = JobOpeningAttachment.objects.select_related('job_opening', 'custom_field').filter(job_opening__slug=slug)
            else:
                queryset = JobOpeningAttachment.objects.select_related('job_opening', 'custom_field')
            if not current_user.has_perm(Permission.buyer('job opening', 'view', Permission.ALL)):
                job_openings = JobOpening.objects.filtered_by_permission().values_list('id')
                queryset = queryset.filter(job_opening_id__in=job_openings)
        else:
            # view for vendor
            if not slug or not key:
                queryset = queryset.filter(job_opening__type=JobOpening.TYPE_PUBLIC)
                if slug:
                    queryset = queryset.filter(job_opening__slug=slug)
            else:
                queryset = queryset.filter(job_opening__slug=slug).filter(
                    Q(job_opening__type=JobOpening.TYPE_PUBLIC) | Q(job_opening__type=JobOpening.TYPE_INVITE_ONLY, job_opening__key=key)
                )
        return queryset

    @action(detail=True, permission_classes=[TenantFeaturesEnabled(features.MARKETPLACE)])
    def download(self, request, *args, **kwargs):
        obj = self.get_object()
        current_user = get_current_user()
        if not (current_user and current_user.has_perm(Permission.buyer('job opening', 'view', Permission.ANY))):
            opening = obj.job_opening
            if opening and opening.type == JobOpening.TYPE_INVITE_ONLY:
                key = self.request.query_params.get('key', None)
                if not opening.key or key != opening.key:
                    raise Http404()
        return serve_file(obj.file_name, obj.file, inline='inline' in request.query_params)


"""
GET api/public/job_opening_registration/<job_opening_id>/
returns:
{
    "requested_documents": [
    ], # - requested documents template list
    "onboarding_stage": {
    }, # - onboarding stage template data,
    "custom_vendor_fields": [
    ] # - custom fields data (to display in onboarding stage template)
}



POST api/public/job_opening_registration/<job_opening_slug>/

should be in following format:
{
                'vendor_data': {
                    'first_name': 'Tester',
                    'last_name': 'Candidate',
                    'email': 'email',
                    'custom_fields': {}, # (same format as normally)

                    # other fields from vendor API /api/vendors/<vendor_slug>/' will work as well
                },
}

"""


class JobOpeningRegistrationPublicApiView(APIView):
    permission_classes = [TenantFeaturesEnabled(features.MARKETPLACE)]

    def opening(self):
        current_user = get_current_user()
        if current_user and current_user.has_perm(Permission.buyer('job opening', 'view', Permission.ANY)):
            opening = get_object_or_404(JobOpening.objects.filtered_by_permission(), slug=self.kwargs.get('opening_slug'))
        else:
            opening = get_object_or_404(JobOpening, slug=self.kwargs.get('opening_slug'), status=JobOpening.PUBLISHED, archived=False)
            if opening.type == JobOpening.TYPE_INVITE_ONLY:
                key = self.request.query_params.get('key', None)
                if not opening.key or key != opening.key:
                    raise Http404()
        return opening

    def get(self, request, *args, **kwargs):
        opening = self.opening()

        if opening.onboarding_workflow:
            onboarding_stage = opening.onboarding_workflow.get_first_stage()
        else:
            onboarding_stage = None

        response = {
            'registration_flow': opening.registration_flow,
            'workflow': opening.onboarding_workflow.pk if opening.onboarding_workflow else None,
            'onboarding_stage': OnBoardingTemplateSerializerReadOnlyOS3(onboarding_stage).data if onboarding_stage else None,
        }

        return Response(response)

    def _handle_onboarding(self, opening, vendor):
        if not opening.onboarding_workflow:
            return False
        first_stage = opening.onboarding_workflow.get_first_stage()

        if not first_stage:
            return False

        OnBoardingWorkflowsForVendor.objects.bulk_set(
            [vendor.pk],
            [opening.onboarding_workflow.pk],
            [],
            replace_all=True,
            onboarding_workflows_post_actions=False,
            context=opening.onboarding_context,
        )
        apply_onboarding_templates_post_actions(None, [vendor.pk])
        return True

    def _handle_email_exists(self, vendor_email, opening):
        vendor = Vendor.objects.filter(email__iexact=vendor_email).first()
        vendor_signup = VendorSignUp.objects.filter(vendor=vendor).first()

        if vendor:
            if vendor.email_verified or not vendor_signup:
                VendorRegistrationCompleted(vendor, executor=vendor.first_contact, job_opening=opening)
            else:
                VendorSignedUp(vendor_signup=vendor_signup, executor=vendor.first_contact, redirect_to='/dashboard/')

        return Response({'flow': auth.REGISTRATION_FLOW_CONFIRM_EMAIL})

    def post(self, request, *args, **kwargs):
        opening = self.opening()
        registration_flow = opening.registration_flow

        vendor_data = request.data.get('vendor_data')
        redirect_to = request.data.get('redirect_to', None)

        email = vendor_data.get('email')
        first_name = vendor_data.get('first_name')
        last_name = vendor_data.get('last_name')
        password = vendor_data.get('password')

        serializer = VendorForVendorSerializer(None, data=vendor_data, partial=True)
        if not serializer.is_valid():
            # validates email and other fields to save
            if 'email' in serializer.errors and 'exists' in serializer.errors['email'][0]:
                return self._handle_email_exists(email, opening)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        # create vendor
        time_now = now()
        last_active = time_now
        is_active = True

        if registration_flow == auth.REGISTRATION_FLOW_CONFIRM_EMAIL:
            last_active = None
            is_active = False

        args = {
            'first_name': first_name.strip()[:50],
            'last_name': last_name.strip()[:50],
            'name': "{} {}".format(first_name or "", last_name or "").strip()[:100],
            'last_active': last_active,
            'vendor_type': VendorType.objects.enabled_or_default('candidate'),
            'invited_at': time_now,
            'email_verified': False,
            'terms_accepted': time_now if connection.tenant.requires_accept_custom_terms_by_vendor else None,
        }

        start_task_tracking()
        with transaction.atomic():
            vendor, created = Vendor.objects.get_or_create(email=email, defaults=args)
            if created:
                serializer = VendorForVendorSerializer(vendor, data=vendor_data, partial=True)
                if serializer.is_valid():
                    vendor = serializer.save(force_update=True)
                user = vendor.first_contact
                user.is_active = is_active
                user.last_active = last_active
                if password:
                    user.set_password(password)
                user.save()

                candidate = Candidate.objects.create(job_opening=opening, vendor=vendor, ip_address=get_ip(), status=Candidate.APPLIED)
                vendor_signup, created = VendorSignUp.objects.get_or_create(vendor=vendor)
                if created:
                    VendorSignedUp(vendor_signup=vendor_signup, executor=user, redirect_to=redirect_to)

                self._handle_onboarding(opening, vendor)

                if registration_flow == auth.REGISTRATION_FLOW_SIMPLE:
                    request.session.set_expiry(connection.tenant.security_settings.session_timeout)
                    user.backend = 'shortlist.auth.UserBackend'
                    auth.shortlist_login(request, user)

                add_task_to_batch(update_job_opening_counts, [opening.id])

        if created:
            add_task_to_batch(indexer.update_vendors, [vendor.pk])
            add_task_to_batch(indexer.update_job_applications, [candidate.pk])
            add_task_to_batch(indexer.update_job_openings, [opening.pk])

            return Response({'vendor': serializer.data, 'flow': registration_flow})

        return Response({'flow': registration_flow})


class JobOpeningRegistrationUploadConfigPublicApiView(APIView):
    permission_classes = [TenantFeaturesEnabled(features.MARKETPLACE)]

    def get(self, request, *args, **kwargs):
        policy = filepicker_policy(RequestedDocument.get_upload_path())
        return Response(policy)


class VendorJobOpeningsApiView(APIView):
    permission_classes = [TenantFeaturesEnabled(features.MARKETPLACE), Permission.buyer('vendor', 'view')]

    @cached_property
    def vendor(self):
        return get_object_or_404(Vendor, slug=self.kwargs.get('vendor_slug'))

    def get_permissions_object(self):
        return self.vendor

    def get(self, request, *args, **kwargs):
        openings = JobOpening.objects.filtered_by_permission().prefetch_related(
            'candidates', 'suppliers', 'onboarding_workflow', 'created_by', 'job_approvers'
        )
        if self.vendor.is_supplier:
            openings = openings.filter(suppliers__vendor=self.vendor)
        else:
            openings = openings.filter(candidates__vendor=self.vendor)
        return Response(JobOpeningWithVendorWorkflowSerializer(openings, many=True, context={'vendor': self.vendor}).data)


class JobOpeningApplyVendorApiView(APIView):
    permission_classes = [TenantFeaturesEnabled(features.MARKETPLACE), Permission.vendor('job opening', 'signup')]

    @cached_property
    def opening(self):
        opening = get_object_or_404(JobOpening, slug=self.kwargs.get('opening_slug'), status=JobOpening.PUBLISHED, archived=False)
        vendor_id = self.request.user.vendor_id
        if opening.type == JobOpening.TYPE_INVITE_ONLY:
            key = self.request.query_params.get('key', None)
            if (not opening.key or key != opening.key) and (
                not opening.job_board_visible_for_all_partners and not opening.job_board_groups.filter(vendors__id=vendor_id).exists()
            ):
                raise Http404()
        return opening

    def _vendor_applied(self, vendor, opening):
        if vendor.is_supplier:
            return JobOpeningSupplier.objects.filter(job_opening=opening, vendor=vendor).first()
        else:
            return Candidate.objects.filter(job_opening=opening, vendor=vendor).first()

    def get(self, request, *args, **kwargs):
        opening = self.opening
        vendor = request.user.vendor
        existing = self._vendor_applied(vendor, opening)

        if not vendor.is_supplier and opening.onboarding_workflow:
            onboarding_stage = opening.onboarding_workflow.get_first_stage()
        else:
            onboarding_stage = None

        response = {
            'registration_flow': 'simple',  # just for front-end compatibility
            'workflow': opening.onboarding_workflow.pk if opening.onboarding_workflow else None,
            'onboarding_stage': OnBoardingTemplateSerializerReadOnlyOS3(onboarding_stage).data if onboarding_stage else None,
            'can_apply': False if existing else True,
        }

        return Response(response)

    def post(self, request, *args, **kwargs):
        vendor = request.user.vendor
        opening = self.opening
        existing = self._vendor_applied(vendor, opening)

        response = {
            'vendor': VendorForVendorSerializer(vendor).data,
            'flow': 'simple',  # just for front-end compatibility,
            'vendor_applied': False if existing else True,
            'workflow': opening.onboarding_workflow.pk if opening.onboarding_workflow else None,
        }

        if existing:
            return Response(response)

        if vendor.is_supplier:
            JobOpeningSupplier.objects.bulk_set([opening.pk], [vendor.pk])
        else:
            Candidate.objects.bulk_set([opening.pk], [vendor.pk], Candidate.APPLIED)
        return Response(response)


class JobOpeningTemplatesViewSet(TypedPermissionMixin, ModelViewSet):
    permission_app = 'buyer'
    permission_type = 'job opening'
    queryset = JobOpeningTemplate.objects.all()
    serializer_class = JobOpeningTemplateSerializer

    @permission_classes([TenantFeaturesEnabled(features.MARKETPLACE), Permission.staff('job opening', 'create')])
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @permission_classes([TenantFeaturesEnabled(features.MARKETPLACE), Permission.staff('job opening', 'edit')])
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @permission_classes([TenantFeaturesEnabled(features.MARKETPLACE), Permission.staff('job opening', 'delete')])
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)


class JobOpeningsSupplierViewSet(APIView):
    permission_classes = [TenantFeaturesEnabled(features.MARKETPLACE), IsSupplier]

    def post(self, request, *args, **kwargs):
        job_opening_id = kwargs.get('job_opening_id')
        if not JobOpeningSupplier.objects.filter(
            job_opening__status=JobOpening.PUBLISHED, job_opening_id=job_opening_id, vendor_id=request.user.vendor_id
        ).exists():
            raise Http404()
        start_task_tracking()
        vendor_slugs = request.data.get('vendors', [])
        vendor_ids = Vendor.objects.filter(parent_id=request.user.vendor_id, slug__in=vendor_slugs).values_list('id', flat=True)
        if vendor_ids:
            Candidate.objects.bulk_set([job_opening_id], vendor_ids, Candidate.INVITED)
            return Response(status=status.HTTP_200_OK)
        return Response("Invalid vendors", status=status.HTTP_400_BAD_REQUEST)


class JobOpeningBulkAPIViewSet(BulkImportViewSet):
    permission_classes = [TenantFeaturesEnabled(features.MARKETPLACE), Permission.buyer('job opening', 'create')]
    queryset = JobOpeningBulkImport.objects.all()
    model_name = 'jobopeningbulkimport'
    config_model = JobOpeningBulkImportConfig
    model = JobOpeningBulkImport


class DistributionTemplatesViewSet(TypedPermissionMixin, ModelViewSet):
    permission_app = 'buyer'
    permission_type = 'job opening'
    model = DistributionTemplate
    serializer_class = DistributionTemplateSerializer

    def get_queryset(self):
        if self.action == 'list':
            return DistributionTemplate.objects.all()
        else:
            return DistributionTemplate.objects.prefetch_related(
                'job_board_groups', 'prefill_vendor_groups', 'prefill_vendors', 'prefill_vendors__vendor_type', 'prefill_vendors__parent'
            )

    def get_serializer_class(self):
        if self.action == 'list':
            return limit_fields(DistributionTemplateSerializer, ('id', 'name'))
        else:
            return self.serializer_class

    @permission_classes([TenantFeaturesEnabled(features.MARKETPLACE), Permission.staff('job opening', 'create')])
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @permission_classes([TenantFeaturesEnabled(features.MARKETPLACE), Permission.staff('job opening', 'edit')])
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @permission_classes([TenantFeaturesEnabled(features.MARKETPLACE), Permission.staff('job opening', 'delete')])
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)
