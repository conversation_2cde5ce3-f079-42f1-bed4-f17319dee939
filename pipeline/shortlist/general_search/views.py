import json
import time
import io
from django.db import connection

from django.db.models import Q
from django.core.handlers.wsgi import WSGIRequest
from rest_framework.response import Response
from rest_framework.views import APIView

from clients import features
from deals.models import SignRequest
from deals.views.deals import SignRequestsViewSetForBuyer
from documents.models import Document
from documents.views import DocumentForUserViewSet
from shortlist.permissions import Permission
from shortlist.search.views import JobOpeningSearchView, VendorSearchView
from tasks.models import TaskGroup, Task
from tasks.views import TaskForBuyerViewSet, TaskGroupForBuyerViewSet
from .serializers import MinimalTaskSerializer, MinimalTaskGroupSerializer, MinimalSignRequestSerializer, MinimalDocumentSerializer


class GeneralSearchView(APIView):
    permission_classes = [Permission.buyer('vendor', 'list')]

    DEFAULT_SIZE = 5

    def post(self, request, *args, **kwargs):
        """example_search = {"query": "v", "sort": null, "filters": [], "page": 0, "terms": false, "size": 5}"""

        user = request.user
        size = request.data.get('size', self.DEFAULT_SIZE)
        use_views = request.data.get('use_views', False)
        query = str(request.data.get('query', 'test'))
        start = time.time()
        search_vendors = self.get_wsgi_request(
            'POST', '/api/search/vendors', {"query": query, "sort": None, "filters": [], "page": 0, "terms": False, "size": size}
        )
        vendors_view = VendorSearchView.as_view()
        vendors_view.filter_out_fields = ['documents']
        vendors = vendors_view(search_vendors).data
        vendors_end = time.time()
        vendors_request = float("%.3f" % (vendors_end - start))

        if connection.tenant.has_features(features.PROJECTS_AND_TASKS):
            if use_views:
                search_tasks = self.get_wsgi_request('GET', f'/api/tasks/?page_size={size}&search={query}&ordering=-updated_at', {})
                tasks = TaskForBuyerViewSet.as_view({'get': 'list'})(search_tasks).data
            else:
                search_tasks = (
                    Task.objects.filtered_by_permission('list')
                    .filter(Q(name__icontains=query) | Q(description__icontains=query))
                    .order_by('updated_at')
                )
                tasks = {"results": MinimalTaskSerializer(search_tasks[:size], many=True).data, 'count': search_tasks.count()}
        else:
            tasks = {"results": [], "count": 0}
        tasks_end = time.time()
        tasks_request = float("%.3f" % (tasks_end - vendors_end))

        if connection.tenant.has_features(features.PROJECTS_AND_TASKS):
            if use_views:
                search_groups = self.get_wsgi_request('GET', f'/api/task_groups/?page_size={size}&search={query}&ordering=-updated_at', {})
                task_groups = TaskGroupForBuyerViewSet.as_view({'get': 'list'})(search_groups).data
            else:
                search_groups = (
                    TaskGroup.objects.filtered_by_permission('list')
                    .filter(Q(name__icontains=query) | Q(description__icontains=query))
                    .order_by('updated_at')
                    .distinct('updated_at')
                )
                task_groups = {"results": MinimalTaskGroupSerializer(search_groups[:size], many=True).data, 'count': search_groups.count()}
        else:
            task_groups = {"results": [], "count": 0}
        groups_end = time.time()
        groups_request = float("%.3f" % (groups_end - tasks_end))

        search_openings = self.get_wsgi_request(
            'POST', '/api/search/job_openings', {"query": query, "sort": None, "filters": [], "page": 0, "terms": False, "size": size}
        )
        job_openings = JobOpeningSearchView.as_view()(search_openings).data
        openings_end = time.time()
        openings_request = float("%.3f" % (openings_end - groups_end))

        if user.has_perm(Permission.buyer('document', 'view', Permission.ALL)):
            if use_views:
                search_files = self.get_wsgi_request('GET', f'/api/documents/?page_size={size}&search={query}&ordering=-created_at', {})
                files = DocumentForUserViewSet.as_view({'get': 'list'})(search_files).data
            else:
                search_files = Document.objects.filter(
                    vendor__email_verified=True, vendor__archived=False, filename__icontains=query
                ).order_by('created_at')
                files = {"results": MinimalDocumentSerializer(search_files[:size], many=True).data, 'count': search_files.count()}
        else:
            files = dict()
        files_end = time.time()
        files_request = float("%.3f" % (files_end - tasks_end))

        if user.has_perm(Permission.buyer('agreement', 'view', Permission.ALL)):
            if use_views:
                search_agreements = self.get_wsgi_request(
                    'GET', f'/api/agreements/requests/?page_size={size}&search={query}&fields=id,name', {}
                )
                agreements = SignRequestsViewSetForBuyer.as_view({'get': 'list'})(search_agreements).data
            else:
                search_agreements = SignRequest.objects.filter(name__icontains=query).order_by('created')
                agreements = {
                    'results': MinimalSignRequestSerializer(search_agreements[:size], many=True).data,
                    'count': search_agreements.count(),
                }
        else:
            agreements = dict()
        agreements_end = time.time()
        agreements_request = float("%.3f" % (agreements_end - openings_end))

        result = {
            'hits': {
                # 'contracts': contracts.get('results'),
                'vendors': vendors.get('hits'),
                'tasks': tasks.get('results'),
                'task_groups': task_groups.get('results'),
                'job_openings': job_openings.get('hits'),
                'files': files.get('results'),
                'agreements': agreements.get('results'),
            },
            'page': 0,
            'query': query,
            'size': size,
            'sort': None,
            'terms': {
                'vendors': vendors.get('total', 0),
                'tasks': tasks.get('count', 0),
                'task_groups': task_groups.get('count', 0),
                'job_openings': job_openings.get('total', 0),
                'files': files.get('count', 0),
                'agreements': agreements.get('count', 0),
            },
            'timers': {
                'vendors_request': vendors_request,
                'tasks_request': tasks_request,
                'groups_request': groups_request,
                'openings_request': openings_request,
                'agreements_request': agreements_request,
                'files_request': files_request,
            },
        }
        return Response(result)

    def get_wsgi_request(self, method, path, data):
        """Construct a fake request(WSGIRequest) object"""
        query_params = path.split('?')
        environ = self.request._request.environ.copy()
        data = json.dumps(data, ensure_ascii=False).encode()
        environ.update(
            {
                'REQUEST_METHOD': method,
                'QUERY_STRING': query_params[1] if len(query_params) > 1 else '',
                'PATH_INFO': path,
                'CONTENT_LENGTH': len(data),
                'wsgi.input': io.BytesIO(data),
            }
        )
        req = WSGIRequest(environ)
        req.user = self.request.user
        req._dont_enforce_csrf_checks = True
        return req
