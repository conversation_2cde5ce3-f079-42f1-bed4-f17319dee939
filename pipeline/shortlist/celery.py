import os

from celery.utils.log import get_task_logger

from shortlist.search import IndexingFailed
from shortlist.tenant import tenant_name
from tenant_schemas_celery.task import SkipTask

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shortlist.settings.local')

from celery.schedules import maybe_schedule, schedstate
from celery.signals import worker_init
from celery import Task
from django.db import connection
from django.conf import settings

from tenant_schemas_celery.app import CeleryApp


def before_send(event, hint):
    if 'exc_info' in hint:
        _, exc_value, _ = hint['exc_info']
        if isinstance(exc_value, (IndexingFailed, SkipTask)):
            return None

    try:
        tags = event.setdefault('tags', {})
        tags['tenant_name'] = tenant_name()
    except Exception:
        pass

    return event


def setup_sentry(*args, **kwargs):
    if settings.SENTRY_DSN is not None:
        import sentry_sdk
        from sentry_sdk.integrations.celery import CeleryIntegration

        sentry_sdk.init(
            dsn=settings.SENTRY_DSN,
            release=settings.RELEASE_VERSION,
            integrations=[CeleryIntegration(monitor_beat_tasks=True)],
            before_send=before_send,
        )


worker_init.connect(setup_sentry)


def is_gevent_monkey_patched():
    try:
        from gevent import monkey
    except ImportError:
        return False
    else:
        return bool(monkey.saved)


if is_gevent_monkey_patched():
    import psycogreen.gevent

    psycogreen.gevent.patch_psycopg()


class TenantPeriodicTask(Task):
    abstract = True

    def run(self, *args, **kwargs):
        from django.apps import apps

        task_name = self.__class__.__name__
        logger = get_task_logger(task_name)
        logger.info('Running task %s', task_name)
        for client in apps.get_model('clients', 'Client').tenants.all():
            kw = kwargs.copy() if kwargs else {}
            kw['_schema_name'] = client.schema_name
            try:
                connection.set_tenant(client)
                self.tenant_task()
            except Exception:  # noqa
                logger.exception('Error running tenant task %s for tenant %s', task_name, client.domain_url, exc_info=True)

    def tenant_task(self):
        raise NotImplementedError()


class JoinedSchedule:
    def __init__(self, parts):
        self.parts = [maybe_schedule(x) for x in parts]

    def is_due(self, last_run_at):
        """Returns tuple of two items `(is_due, next_time_to_run)`,
        where next time to run is in seconds.

        See :meth:`celery.schedules.schedule.is_due` for more information.

        """
        states = [x.is_due(last_run_at) for x in self.parts]
        due = any(x[0] for x in states)
        mins = [x[1] for x in states if x[1]]
        rem = min(mins) if mins else None
        return schedstate(due, rem)


def in_task():
    from celery import current_task

    return current_task and current_task.request.id is not None


def current_task_id():
    from celery import current_task

    if current_task:
        return current_task.request.id


app = CeleryApp(strict_typing=False)
app.config_from_object('django.conf:settings', namespace='CELERY')

app.autodiscover_tasks(lambda: settings.INSTALLED_APPS)
