import _thread
import base64
import calendar
import collections
import datetime
import enum
import hashlib
import hmac
import json
import logging
import mimetypes
import os
import pickle
import posixpath
import random
import re
import string
import time
import uuid
from urllib.error import HTTPError

import binascii
import boto3
import csv342 as csv
import math
import pgeocode
import pytz
import uuslug
import yaml.representer
from botocore.client import Config
from botocore.exceptions import ClientError
from django.conf import settings
from django.core.cache import cache
from django.core.files.storage import default_storage
from django.db import connection, models
from django.http.response import HttpResponse, StreamingHttpResponse, HttpResponseNotFound, HttpResponseRedirect
from django.urls import reverse
from django.utils.deprecation import MiddlewareMixin
from django.utils.encoding import force_bytes as enc_force_bytes, force_bytes, force_str
from django.utils.functional import wraps
from django.utils.timezone import now
from django.utils.translation import trans_real
from rest_framework.fields import FileField
from rest_framework.negotiation import BaseContentNegotiation

from . import rfc6266
from .current_user import get_current_request, get_current_user
from .db_fields import PipelineJSONEncoder
from .memcache_key import sanitize_memcached_key

logger = logging.getLogger()

MAX_IP_LENGTH = 45  # INET6_ADDRSTRLEN - 1
KEY_ALPHABET = string.ascii_lowercase + string.digits


def get_ip():
    """Assumes we're running behind AWS Elastic Load Balancer"""
    request = get_current_request()
    if request is not None and hasattr(request, 'META'):
        ip = str(request.META.get('HTTP_X_CLIENT_IP', '').strip())
        if not ip:
            ip = str(request.META.get('HTTP_X_FORWARDED_FOR', '').strip())
        if not ip:
            ip = request.META.get('REMOTE_ADDR', '').strip()
        return ip[:MAX_IP_LENGTH]
    return ""


class FormatError(Exception):
    pass


class ExtendedFileField(FileField):
    def to_representation(self, value):
        if value:
            try:
                return {
                    'file': {'url': ''.join([settings.MEDIA_URL, value.name]), 'size': value.size},
                    'name': os.path.basename(value.name),
                }
            except OSError:
                return None


def ensure_json_dict(value):
    """
    this is silly function to ensure string ( or unicode object ) will be converted to
    python dict.
    This is a little workaround for strange bug on development server
    :param value: string or loaded json dict
    :return: json dict
    """
    if isinstance(value, dict):
        return value
    try:
        return json.loads(value)
    except:
        return {}


def filepicker_policy(directory, timeout=3600, bucket=settings.AWS_STORAGE_BUCKET_NAME, max_size=None):
    """
    Return policy for file picker according to this docs:
    https://www.filepicker.com/documentation/security/create_policy
    We generate this policy each time user opens file picker widget and wants to upload
    a file.
    As an additional parameters we send AWS S3 bucket name ( depends on current env type )
    and bucket file path.
    """
    expiry = int(time.time() + timeout)
    policyDict = {'expiry': expiry, 'call': ['pick', 'store', 'convert', 'stat'], 'container': bucket, 'path': directory}
    if max_size:
        policyDict['maxSize'] = max_size
    json_policy = json.dumps(policyDict)

    policy = base64.urlsafe_b64encode(force_bytes(json_policy))
    signature = hmac.new(force_bytes(settings.FILEPICKER_SECRET), policy, hashlib.sha256).hexdigest()
    return {
        'policy': policy,
        'signature': signature,
        'bucket': bucket,
        'upload_path': directory,
        'domain': settings.AWS_STORAGE_VALID_DOMAINS[settings.AWS_DEFAULT_REGION],
        'region': settings.AWS_DEFAULT_REGION,
    }


def validate_upload_path(prefix, path):
    normalized = posixpath.normpath(path)
    if normalized and normalized.startswith(prefix) and len(normalized) > len(prefix):
        return True
    else:
        logger.warning(f"validate_upload_path failed, {prefix}, {path}, {normalized}")


def upload_path_validator(path_value: str, prefix_method: callable) -> bool:
    prefix = prefix_method()
    return validate_upload_path(prefix, path_value) or False


def boto3_client(service, **kwargs):
    options = dict(
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
        region_name=settings.AWS_DEFAULT_REGION,
    )
    options.update(kwargs)
    return boto3.client(service, **options)


def boto3_resource(service, **kwargs):
    options = dict(
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
        region_name=settings.AWS_DEFAULT_REGION,
    )
    options.update(kwargs)
    session = boto3.Session(**options)
    return session.resource(service)


def s3_presigned_url(file_name, bucket_name, key_name, content_type=None, inline=False):
    params = []
    disposition = 'inline' if inline else 'attachment'
    if file_name is not None:
        disposition = rfc6266.build_header(file_name, disposition)
    params.append(("ResponseContentDisposition", force_str(disposition)))
    if content_type is None:
        content_type = guess_mimetype(file_name)
    if content_type:
        params.append(("ResponseContentType", content_type))
    params.extend(
        [
            ('Bucket', bucket_name),
            ('Key', key_name),
        ]
    )

    # Get the service client with sigv4 configured
    s3 = boto3_client('s3', config=Config(signature_version='s3v4'))  # TODO: cache client?
    url = s3.generate_presigned_url(
        ClientMethod='get_object',
        Params=collections.OrderedDict(sorted(params)),  # keep keys sorted alphabetically for signature checking
        ExpiresIn=settings.AWS_S3_PRESIGNED_EXPIRE,
    )
    return url


def s3_file_exists(bucket_name, key_name):
    s3 = boto3_client('s3', config=Config(signature_version='s3v4'))
    response = s3.list_objects_v2(Bucket=bucket_name, Prefix=key_name, MaxKeys=1)
    return response.get('KeyCount', 0) > 0


def serve_file(file_name, file_field, content_type=None, inline=False):
    if file_field is None:
        return HttpResponseNotFound()
    if content_type is None:
        content_type = guess_mimetype(file_name)
    if content_type not in settings.INLINE_MIME_TYPES:
        # Prevent most file types from being served inline, as a security precaution
        inline = False
    try:
        if settings.AWS_S3_PRESIGNED_URLS:
            obj = file_field.file.obj
            return serve_presigned_url(file_name, obj.bucket_name, obj.key, content_type, inline)
        file_chunks = file_field.file.chunks()
        return serve_file_contents(file_name, file_chunks, content_type, inline)
    except (ValueError, OSError):
        # no file after all
        return HttpResponseNotFound()
    except ClientError as e:
        # if there is no access ClientError is thrown
        if e.response['Error']['Code'] in ('403', '404'):
            return HttpResponseNotFound()
        else:
            raise


def serve_s3_file(file_name, bucket_name, file_path, content_type=None, inline=False):
    if content_type is None:
        content_type = guess_mimetype(file_name)
    if content_type not in settings.INLINE_MIME_TYPES:
        # Prevent most file types from being served inline, as a security precaution
        inline = False

    if settings.AWS_S3_PRESIGNED_URLS:
        try:
            return serve_presigned_url(file_name, bucket_name, file_path, content_type)
        except ClientError as e:
            # if there is no access ClientError is thrown
            if e.response['Error']['Code'] in ('403', '404'):
                return HttpResponseNotFound()
            else:
                raise
    else:
        try:
            with default_storage.open(file_path, 'rb') as f:
                return serve_file_contents(file_name, f.read(), content_type, inline)
        except (ValueError, OSError):
            # no file after all
            return HttpResponseNotFound()


def serve_presigned_url(file_name, bucket_name, key_name, content_type=None, inline=False):
    url = s3_presigned_url(file_name, bucket_name, key_name, content_type, inline)
    return HttpResponseRedirect(url)


def guess_mimetype(file_name):
    content_type = "application/octet-stream"
    return (mimetypes.guess_type(file_name)[0] or content_type) if file_name is not None else content_type


def serve_file_contents(file_name, content, content_type=None, inline=False):
    if content_type is None:
        content_type = guess_mimetype(file_name)

    if isinstance(content, (bytes, str)):
        response = HttpResponse(content, content_type=content_type)
        response['Content-Length'] = len(content)
    else:
        response = StreamingHttpResponse(content, content_type=content_type)
    disposition = 'inline' if inline else 'attachment'
    if file_name is not None:
        disposition = rfc6266.build_header(file_name, disposition)
    response['Content-Disposition'] = disposition
    return response


def max_len(choices):
    return max(len(x if isinstance(x, str) else x[0]) for x in choices)


def make_choices(*args, force_bytes=False, sort=False):
    if force_bytes:
        args = list(map(enc_force_bytes, args))
    choices = list(zip(args, args, strict=False))
    if sort:
        choices.sort()
    return choices


def make_choices_from_enums(*enums):
    choices = []
    for _enum in enums:
        for tag in _enum:
            choices.append((tag.value, tag.name))
    return choices


def generate_key(length=8):
    return ''.join(random.choice(KEY_ALPHABET) for _ in range(length))


def generate_token():
    return uuid.uuid4().hex


def rand_x_digit_code(x=4):
    return "".join(random.choice(string.digits) for i in range(x))


def slugify(text, **kwargs):
    return uuslug.slugify(text, **kwargs) or binascii.b2a_hex(os.urandom(3))


class HeaderedList(list):
    def __init__(self, values, header):
        list.__init__(self, values)
        self.header = header


yaml.representer.SafeRepresenter.add_representer(
    HeaderedList, lambda dumper, data: dumper.represent_sequence('tag:yaml.org,2002:seq', data)
)

STILL_VALID_SUFFIX = b':st'


def cached(cache_key, timeout=None, soft_timeout=None):
    """
    Caching decorator.
    Similar to one from django-cache-utils, but simplified and uses explicit cache keys
    Can be applied to function, method or classmethod.
    Supports invalidation for exact parameter
    set. Cache keys are human-readable because they are constructed from
    callable's full name and arguments and then sanitized to make
    memcached happy.

    Wrapped callable gets `invalidate` methods. Call `invalidate` with
    same arguments as function and the result for these arguments will be
    invalidated.
    """
    if soft_timeout is not None:
        assert timeout is not None
        assert timeout > soft_timeout

    def _log(key: str, cache_hit: bool):
        logger.info("cached_get_value cached_hit=%s key=%s", cache_hit, str(key))

    def _cached(func):
        if callable(cache_key):
            static_key = None

            def key_func(*args, **kwargs):
                return sanitize_memcached_key(cache_key(*args, **kwargs))

        else:
            static_key = sanitize_memcached_key(cache_key)
            key_func = None

        if soft_timeout is None:

            @wraps(func)
            def wrapper(*args, **kwargs):
                # try to get the value from cache
                key = static_key or key_func(*args, **kwargs)
                value = cache.get(key)
                cache_hit = True

                # in case of cache miss recalculate the value and put it to the cache
                if value is None:
                    value = func(*args, **kwargs)

                    try:
                        cache.set(key, value, timeout)
                    except pickle.PicklingError:
                        pass
                    cache_hit = False

                _log(key, cache_hit)
                return value

        else:

            def calc_and_store(key, args, kwargs):
                value = func(*args, **kwargs)
                cache.set(key, value, timeout)
                cache.set(key + STILL_VALID_SUFFIX, True, soft_timeout)
                return value

            @wraps(func)
            def wrapper(*args, **kwargs):
                # try to get the value from cache
                key = static_key or key_func(*args, **kwargs)
                d = cache.get_many([key, key + STILL_VALID_SUFFIX])
                value = d.get(key)
                # in case of cache miss recalculate the value and put it to the cache
                if value is None:
                    _log(key, cache_hit=False)
                    return calc_and_store(key, args, kwargs)
                elif (key + STILL_VALID_SUFFIX) not in d:
                    # cache hit, but let's recalculate in background for next time
                    cache.set(key + STILL_VALID_SUFFIX, True, soft_timeout)  # make sure we won't swarm
                    _thread.start_new_thread(calc_and_store, (key, args, kwargs))

                _log(key, cache_hit=True)
                return value

        def invalidate(*args, **kwargs):
            """invalidates cache result for function called with passed arguments"""
            key = static_key or key_func(*args, **kwargs)
            cache.delete(key)

        def is_cached(*args, **kwargs):
            key = static_key or key_func(*args, **kwargs)
            return cache.get(key) is not None

        wrapper.is_cached = is_cached
        wrapper.invalidate = invalidate
        return wrapper

    return _cached


def cache_func_tenant_and_pks(name, version=1):
    """Simple cache key function to be used with tenant-specific functions taking model instances (PrimaryKeyS) as parameters"""

    def key_func(*args):
        parts = [connection.tenant.domain_url if connection.tenant else "", name, str(version)]
        parts.extend(str(x.pk) for x in args)
        return "-".join(parts)

    return key_func


def cache_func_tenant(name, version=1):
    """Simple cache key function to be used with tenant-specific functions taking ordinal values as parameters"""

    def key_func(*args):
        parts = [connection.tenant.domain_url if connection.tenant else "", name, str(version)]
        parts.extend(str(x) for x in args)
        return "-".join(parts)

    return key_func


def cache_const_tenant(name, version=1):
    """Simple cache key function to be used with tenant-specific functions taking no parameters"""

    def key_func():
        parts = [connection.tenant.domain_url if connection.tenant else "", name, str(version)]
        return "-".join(parts)

    return key_func


def cache_const_env(name, version=1):
    """Simple cache key function to be used with global functions taking no parameters"""
    parts = [settings.PUBLIC_SCHEMA_DOMAIN, name, str(version)]
    return "-".join(parts)


def cache_func_env(name, version=1):
    """Simple cache key function to be used with global functions taking some parameters"""

    def key_func(*args):
        parts = [settings.PUBLIC_SCHEMA_DOMAIN, name, str(version)]
        parts.extend(str(x) for x in args)
        return "-".join(parts)

    return key_func


def detect_language_from_request(request=None):
    language_code = settings.LANGUAGE_CODE
    if request is None:
        request = get_current_request()
        if request is None:
            return language_code
    in_request = getattr(request, 'LANGUAGE_CODE', None)
    if in_request:
        return in_request
    # have to make our own loop, because function in trans_real insists on checking for existence .mo files,
    # which we don't care about
    supported = dict(settings.LANGUAGES)
    accepted = trans_real.parse_accept_lang_header(request.META.get('HTTP_ACCEPT_LANGUAGE', ''))
    for accept_lang, unused in accepted:
        prefix = accept_lang.split('-')[0]
        if prefix in supported:
            language_code = prefix
            break
    request.LANGUAGE_CODE = language_code
    return language_code


class AnonymousVendor:
    def to_representation(self):
        name = connection.tenant.tenant_terms['Other Vendor']
        return {
            'slug': None,
            'first_name': '',
            'last_name': '',
            'email': '',
            'full_name': name,
            'avatar_color': '#d6d6d6',
            'vendor': True,
            'company_name': name,
            'id': None,
        }


ANONYMOUS_VENDOR = AnonymousVendor()


class AnonymousSupport:
    email = '<EMAIL>'
    should_send_email = True

    @property
    def first_name(self):
        try:
            return self.full_name.split(' ')[0]
        except Exception:
            return ''

    @property
    def last_name(self):
        try:
            return self.full_name.split(' ')[1]
        except Exception:
            return ''

    @property
    def initials(self):
        return f"{self.first_name[:1]} {self.last_name[:1]}".strip()

    @property
    def full_name(self):
        return connection.tenant.tenant_terms['Shortlist Support']

    def to_representation(self):
        return {
            'slug': None,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'email': self.email,
            'full_name': self.full_name,
            'initials': self.initials,
            'avatar_color': '#d6d6d6',
            'vendor': False,
            'company_name': '',
            'id': None,
            'is_staff': True,
            'profile_picture_path': settings.SHORTLIST_USER_LOGO,
        }


ANONYMOUS_SUPPORT = AnonymousSupport()

SYSTEM_ACTOR = {
    'full_name': 'System',
    'profile_picture_path': settings.SHORTLIST_USER_LOGO,
}


class ZipPart:
    def __init__(self, file_name, bytes=None, url=None, timestamp=None):
        self.bytes = bytes
        self.url = url
        self.timestamp = timestamp
        self.file_name = file_name


def split_in_batches(iterable, batch_size):
    """Optimized for real lists and QuerySets, but will work with any iterable"""
    fast = isinstance(iterable, (list, models.query.QuerySet))
    if fast:
        start = 0
    else:
        iterator = iter(iterable)
    go = True
    while go:
        try:
            if fast:
                batch = list(iterable[start : start + batch_size])
                start += batch_size
            else:
                batch = []
                for i in range(batch_size):
                    batch.append(next(iterator))
        except StopIteration:
            go = False
        if batch:
            yield batch
        else:
            go = False


class Kms:
    """Wrapper for Amazon Key Management Service client"""

    def __init__(self, use_base64):
        aws_settings = {'region_name': settings.AWS_DEFAULT_REGION}
        aws_settings.update(settings.BANK_DETAILS_KEY_SETTINGS)
        self.client = boto3.client('kms', **aws_settings)  # not boto3_client (config settings) on purpose
        self.use_base64 = use_base64

    def encrypt(self, key_id: str, data: str) -> str:
        response = self.client.encrypt(KeyId=key_id, Plaintext=data)
        result = response['CiphertextBlob']
        if self.use_base64:
            result = base64.b64encode(result)
        return force_str(result)

    def decrypt(self, data: str) -> str:
        if self.use_base64:
            data = base64.b64decode(data)
        response = self.client.decrypt(CiphertextBlob=data)
        result = response['Plaintext']
        return force_str(result)

    def re_encrypt(self, key_id, data) -> str:
        if self.use_base64:
            data = base64.b64decode(data)
        response = self.client.re_encrypt(DestinationKeyId=key_id, CiphertextBlob=data)
        result = response['CiphertextBlob']
        if self.use_base64:
            result = base64.b64encode(result)
        return force_str(result)


def current_user_timezone():
    user = get_current_user()
    return connection.tenant.timezone if user is None else user.timezone


def convert_time_tz(value, from_tz=None, to_tz=None):
    """
    Convert time values (only) from one timezone to another.
    from_tz: default is tenant's timezone
    to_tz: default is current user's timezone
    This is needed as default datetime.time does not support timedelta arithmetic.
    We assume time is for today in case daylight time saving matters.
    """
    if value is not None:
        from_tz = from_tz or connection.tenant.timezone
        to_tz = to_tz or current_user_timezone()
        if from_tz == to_tz:
            return value
        dt = from_tz.localize(datetime.datetime.combine(datetime.date.today(), value))
        dt = to_tz.normalize(dt)
        return dt.time()


def update_subitems(obj, field_name, raw_field_name, value, parent_field_name, model_field_name='name'):
    """
    Remember about the post_save handler if you are using this method.
    """
    if obj.id is not None:
        old_set = set(getattr(obj, field_name))
        new_set = set(value)
        if old_set != new_set:
            # keep order
            for x in value:
                if x not in old_set:
                    getattr(obj, raw_field_name).get_or_create(**{parent_field_name: obj, model_field_name: x})
            getattr(obj, raw_field_name).filter(**{model_field_name + '__in': old_set - new_set}).delete()
    else:
        # new instance, we don't have id yet - postpone creating until object is saved
        # requires appropriate post_save handler
        setattr(obj, '_' + field_name, value)


def q_combine(conditions, connector=models.Q.OR):
    if conditions:
        if len(conditions) == 1:
            return next(iter(conditions))
        else:
            q = models.Q()
            q.connector = connector
            for c in conditions:
                q.add(c, connector)
            return q


def is_boto_not_found(exc):
    """Check boto3 ClientError and return whether it is a 404 not found error"""
    error_code = int(exc.response['Error']['Code'])
    return error_code == 404


def serialized_model_data(obj, mapping):
    data = {}
    for field in mapping.get('fields', []):
        data[field] = getattr(obj, field)
    for field in mapping.get('fields_as_str', []):
        data[field] = str(getattr(obj, field))
    for field in mapping.get('foreign', []):
        data[field] = getattr(obj, field).pk if getattr(obj, field) else None
    for field in mapping.get('m2m', []):
        if isinstance(getattr(obj, field), list):
            data[field] = [item.id for item in getattr(obj, field)]
        else:
            data[field] = list(getattr(obj, field).all().values_list('id', flat=True))

    return data


class DisableCsrfMiddleware(MiddlewareMixin):
    """
    This middleware disables all of CSRF checks made by Django framework.
    It can be useful to test things manually doing some POST curls for example, straight from command line.
    THIS IS FOR TESTING PURPOSES ONLY!
    DO NOT ENABLED IT ON PRODUCTION ENVIRONMENTS.
    """

    def process_request(self, request):
        request._dont_enforce_csrf_checks = True


def dict_diff(a, b):
    """
    Find keys that have different values in 2 dicts.
    Doesn't work with nested dicts.
    """
    all_keys = set(a.keys()).union(list(b.keys()))
    return {key for key in all_keys if a.get(key) != b.get(key)}


def replace_with_tenant_terms(text):
    terms = connection.tenant.tenant_terms
    for term in terms:
        text = text.replace('[' + term + ']', terms[term])
    return text


def strip_ws(s):
    return s.strip() if s else ""


def strip_ws_and_quotes(s):
    return s.strip().strip('"\'') if s else ""


def add_newlines_generator(f):
    try:
        for x in f:
            v = x.decode('utf-8') if isinstance(x, bytes) else x
            if v.endswith('\n'):
                yield v
            else:
                yield v + '\n'
    except UnicodeDecodeError as e:
        raise FormatError(['Only UTF-8 format file is allowed.', str(e)])


class SimpleDictReaderException(Exception):
    pass


class SimpleDictReader:
    def __init__(self, f, dialect="excel", add_newlines=True, lowercase=True, strip_quotes=True, **kwargs):
        self.line_counter = 1
        if add_newlines:
            f = add_newlines_generator(f)
        self.reader = csv.reader(f, dialect, **kwargs)
        self.strip_quotes = strip_quotes
        try:
            if lowercase:
                self.fieldnames = [(x or "").strip().lower() for x in next(self.reader)]
            else:
                self.fieldnames = [(x or "").strip() for x in next(self.reader)]
        except FormatError as e:
            e.args[0][0] = e.args[0][0].format(line_counter=self.line_counter)
            raise SimpleDictReaderException(e.args)
        except StopIteration:
            pass

    def __iter__(self):
        return self

    def __next__(self):
        self.line_counter += 1
        try:
            row = next(self.reader)
            while not row:
                row = next(self.reader)
        except FormatError as e:
            e.args[0][0] = e.args[0][0].format(line_counter=self.line_counter)
            raise SimpleDictReaderException(e.args)

        f = strip_ws_and_quotes if self.strip_quotes else strip_ws
        return dict(zip(self.fieldnames, map(f, row), strict=False))


def chunks(l, n):
    """Yield successive n-sized chunks from l."""
    for i in range(0, len(l), n):
        yield l[i : i + n]


def make_months():
    result = {calendar.month_name[i].lower(): i for i in range(1, 13)}
    result.update({calendar.month_abbr[i].lower(): i for i in range(1, 13)})
    return result


MONTHS = make_months()

DATE_REGEXPES = [
    re.compile(r'(?P<year>\d{4})-(?P<month>\d{1,2})-(?P<day>\d{1,2})$'),
    re.compile(r'(?P<day>\d{1,2})-(?P<month>\d{1,2})-(?P<year>\d{4})$'),
    re.compile(r'(?P<year>\d{4})\.(?P<month>\d{1,2})\.(?P<day>\d{1,2})$'),
    re.compile(r'(?P<day>\d{1,2})\.(?P<month>\d{1,2})\.(?P<year>\d{4})$'),
    re.compile(r'(?P<month>\d{1,2})/(?P<day>\d{1,2})/(?P<year>\d{4})$'),
    re.compile(r'(?P<day>\d{1,2})-(?P<month_name>%s)-(?P<year>\d{4})$' % ("|".join(list(MONTHS.keys()))), re.IGNORECASE),
    re.compile(r'(?P<day>\d{1,2}) (?P<month_name>%s) (?P<year>\d{4})$' % ("|".join(list(MONTHS.keys()))), re.IGNORECASE),
    # CF date values stored in ES formatted using shortlist.search.custom_fields_search.py.CUSTOM_FIELDS_DATE_FORMAT
    re.compile(r'(?P<year>\d{4})-(?P<month>\d{1,2})-(?P<day>\d{1,2})T00:00:00$'),
]


def parse_date(value):
    """
    Parses a string and return a datetime.date.

    Raises ValueError if the input is well formatted but not a valid date, or input is not empty and not well formatted.

    Modified from dateparse.parse_date. Let's play a guessing game if it's DMY or MDY :(
    """
    if value:
        for regexp in DATE_REGEXPES:
            match = regexp.match(value)
            if match:
                kw = dict(match.groupdict())
                if 'month_name' in kw:
                    kw['month'] = MONTHS[kw.pop('month_name').lower()]
                kw = {k: int(v) for k, v in kw.items()}
                return datetime.date(**kw)
        raise ValueError(f'Unrecognized date value: {value}')


def validate_phone_number(phone_number):
    """TODO: switch to regex"""
    return phone_number and phone_number.startswith('+')


def s3_key_for_prepared_file(task_id=None):
    if task_id is None:
        from .celery import current_task_id

        task_id = current_task_id()
    if task_id:
        return f"{connection.tenant.domain_url}/prepared/{task_id}"


def prepared_file_response(task_id, filename):
    return HttpResponseRedirect(reverse("prepared-file", kwargs=dict(task_id=task_id, filename=filename)))


def save_prepared_file_to_s3(content, content_type, custom_path=None):
    if custom_path:
        s3_key = custom_path
    else:
        s3_key = s3_key_for_prepared_file()
    s3 = boto3_client("s3")
    s3.put_object(
        Bucket=settings.AWS_BUCKET_TEMPORARY,
        Key=s3_key,
        Body=content,
        ContentType=content_type,
    )


def save_prepared_file_from_disk_to_s3(file_path):
    s3_key = s3_key_for_prepared_file()
    s3 = boto3_resource("s3")
    s3.meta.client.upload_file(
        Filename=file_path,
        Bucket=settings.AWS_BUCKET_TEMPORARY,
        Key=s3_key,
    )


def delete_directory_from_s3(bucket, prefix):
    if not prefix.endswith("/"):
        raise ValueError("Path should end with /")
    if len(prefix.strip("/").split("/")) < 3:
        raise ValueError("Removing main directories is not allowed")
    s3 = boto3_resource("s3")
    bucket = s3.Bucket(bucket)
    bucket.objects.filter(Prefix=prefix).delete()


MIDNIGHT = datetime.time(0, 0, 0)


def date_to_datetime(date, to_tz=pytz.UTC):
    """Convert datetime.date assumed to be midnight in users's local timezone to UTC datetime.datetime"""
    if date:
        from_tz = current_user_timezone()
        dt = from_tz.localize(datetime.datetime.combine(date, MIDNIGHT))
        dt = to_tz.normalize(dt)
        return dt


class IgnoreClientContentNegotiation(BaseContentNegotiation):
    def select_parser(self, request, parsers):
        """
        Select the first parser in the `.parser_classes` list.
        """
        return parsers[0]

    def select_renderer(self, request, renderers, format_suffix):
        """
        Select the first renderer in the `.renderer_classes` list.
        """
        return (renderers[0], renderers[0].media_type)


def need_zip_output(view):
    return view.kwargs.get('format', view.request.query_params.get('format', 'zip')) != 'csv'  # bare CSV otherwise


class ListWithShortRepr(list):
    def __repr__(self):
        return f"[{len(self)} items]"


def zip_or_csv_response(zip_output, file_name, response_generator):
    if zip_output:
        from .tasks import prepare_zip_with_downloads

        zip_parts = ListWithShortRepr(response_generator)
        return prepared_file_response(
            prepare_zip_with_downloads.delay(zip_parts),
            file_name + ".zip",
        )
    else:
        return serve_file_contents(file_name + ".csv", next(response_generator))


def date_for_export(value, to_tz):
    if value is None:
        return ''
    if isinstance(value, datetime.datetime):
        value = value.astimezone(to_tz)
    return value.strftime("%Y-%m-%d")


def datetime_for_export(value, to_tz):
    if isinstance(value, datetime.datetime):
        value = value.astimezone(to_tz)
        return value.strftime("%Y-%m-%d %H:%M:%S")
    return ''


def add_username_for_internal_note(value):
    user = get_current_user()
    date = now()

    return PipelineJSONEncoder(sort_keys=True).encode(
        {'user_slug': user.slug, 'user_name': user.full_name, 'timestamp': date, 'note': value}
    )


def dict_by_pk(objects):
    return {obj.pk: obj for obj in objects}


def dict_by_attr(objects, attr):
    return {getattr(obj, attr): obj for obj in objects}


def postal_code_to_geo_location(country_code, postal_code):
    """
    Postal codes for 83 countries are supported.
    Supported countries list: https://download.geonames.org/export/zip/
    """
    try:
        nomi = pgeocode.Nominatim(country=country_code)
    except (ValueError, HTTPError):
        return None

    location = nomi.query_postal_code(str(postal_code))
    return location if not math.isnan(location.latitude) and not math.isnan(location.longitude) else None


def parts_paginator(total_count, number_of_parts=1, part_number_to_generate=1, batch_size=250):
    """
    Example usage: We have a queryset of 1000 rows.
    We want to split it into 4 parts. If the batch_size == 1000/4, we will get:
    1) for part 1: [[0, 250]]
    2) for part 4: [[750, 1000]]
    We can also create smaller batches for each part, if the batch_size is for example 100, we will get:
    1) for part 1: [[0, 100], [100, 200], [200, 250]]
    2) for part 4: [[750, 850], [850, 950], [950, 1000]]
    :param total_count:
    :param number_of_parts:
    :param part_number_to_generate:
    :param batch_size:
    :return: list
    """
    page_size = max(int(int(total_count) / int(number_of_parts)), 1)
    total_start_number = (part_number_to_generate - 1) * page_size
    reminder = max(total_count - number_of_parts * page_size, 0)
    total_end_number = total_start_number + page_size if page_size > 1 else max(total_count, 1)

    if part_number_to_generate == number_of_parts:
        total_end_number += reminder

    parts = []
    start_number = total_start_number
    end_number = total_start_number + batch_size if batch_size < page_size else total_end_number
    parts.append([start_number, end_number])
    if end_number < total_end_number:
        while True:
            start_number = end_number
            end_number = min(start_number + batch_size, total_end_number)
            parts.append([start_number, end_number])
            if end_number >= total_end_number:
                break

    return parts


class Choices(str, enum.Enum):
    @classmethod
    def choices(cls) -> tuple:
        return tuple((x.name, x.value) for x in cls)

    @classmethod
    def names(cls) -> tuple:
        return tuple(x.name for x in cls)

    def __str__(self):
        return self.value


def is_time_difference_less_than(datetime1: datetime.datetime, datetime2: datetime.datetime, hours: int = 1) -> (bool, datetime.timedelta):
    return abs(datetime2 - datetime1) < datetime.timedelta(hours=hours)


def first(iterable, default=None):
    for element in iterable:
        if element:
            return element

    return default
