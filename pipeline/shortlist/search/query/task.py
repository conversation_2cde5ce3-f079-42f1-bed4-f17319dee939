from django.db import connection
from django.db.models import Q

from clients import features
from shortlist.search.query.base import FilterQueryBuilder, SearchQueryBuilder, SortBuilder, CustomFieldsAggregationQueryBuilderMixin
from shortlist.search.utils import has_filter
from tasks.models import Task


class TaskQueryBuilder(SearchQueryBuilder):
    TEXT_FIELDS = [
        'contract.name',
        'description',
        'managers.full_name',
        'name',
        'task_group.name',
        'teams.name',
        'vendor.full_name',
    ]


class TaskQueryBuilderForVendor(SearchQueryBuilder):
    TEXT_FIELDS = [
        'contract.name',
        'description',
        'name',
        'task_group.name',
    ]


class TaskAggregationQueryBuilder(CustomFieldsAggregationQueryBuilderMixin):
    TERMS_FIELDS = [
        'budget_rate_type.id',
        'budget_total',
        'budget_rate_per_time_unit',
        'created_by.id',
        'currency.term',
        'date_end',
        'date_start',
        'managers.id',
        'status',
        'task_group.id',
        'teams.id',
        'vendor.id',
    ]

    def __init__(self, custom_fields=None):
        super().__init__(custom_fields=custom_fields)
        if connection.tenant.has_features(features.CONTRACTS):
            self.fields.append('contract.id')


class TaskAggregationQueryBuilderForVendor(TaskAggregationQueryBuilder):
    pass


class TaskFilterQueryBuilder(FilterQueryBuilder):
    FILTERS_MAP = {
        'budget_rate_type': 'budget_rate_type.id',
        'budget_rate_per_time_unit': ['budget_rate_per_time_unit'],
        'budget_total': ['budget_total'],
        'contract': 'contract.id',
        'created_by': 'created_by.id',
        'id': 'id',
        'currency': 'currency.term',
        'date_end': ['date_end'],
        'date_start': ['date_start'],
        'job_opening': 'job_opening.id',
        'managers': 'managers.id',
        'milestone_managers': 'milestone_managers.id',
        'status': 'status',
        'task_group': 'task_group.id',
        'teams': 'teams.id',
        'vendor': 'vendor.id',
    }

    def build(self):
        self._exclude_archived_tasks_by_default()
        self._filter_by_payments()
        return super().build()

    def _exclude_archived_tasks_by_default(self):
        if not has_filter(self.filters, 'status'):
            self.filters.append({'not': [{'status': 'archived'}]})

    def _filter_by_payments(self):
        query = Q()
        for filter_item in list(self.filters):
            if "payments_count" in filter_item:
                self.filters.remove(filter_item)
                count_range = filter_item["payments_count"].get("range") or {}
                count_gte = count_range.get("gte")
                count_lte = count_range.get("lte")
                if count_gte is not None:
                    query &= Q(payments_count__gte=count_gte)
                if count_lte is not None:
                    query &= Q(payments_count__lte=count_lte)

            if "payments_amount" in filter_item:
                self.filters.remove(filter_item)
                amount_range = filter_item["payments_amount"].get("range") or {}
                amount_gte = amount_range.get("gte")
                amount_lte = amount_range.get("lte")
                if amount_gte is not None:
                    query &= Q(payments_amount__gte=amount_gte)
                if amount_lte is not None:
                    query &= Q(payments_amount__lte=amount_lte)

        if query:
            task_ids = (
                Task.objects.annotate_not_rejected_payments_with_currency_according_to_task().filter(query).values_list("id", flat=True)
            )
            self.filters.append({'id': list(task_ids)})

        return self.filters


class TaskFilterQueryBuilderForVendor(TaskFilterQueryBuilder):
    FILTERS_MAP = {
        'budget_rate_type': 'budget_rate_type.id',
        'budget_rate_per_time_unit': ['budget_rate_per_time_unit'],
        'contract': 'contract.id',
        'currency': 'currency.term',
        'budget_total': ['budget_total'],
        'date_end': ['date_end'],
        'date_start': ['date_start'],
        'id': 'id',
        'status': 'status',
        'task_group': 'task_group.id',
        'vendor': 'vendor.id',
    }

    def build(self):
        self._exclude_archived_tasks_by_default()
        return super().build()


class TaskQuerySortBuilder(SortBuilder):
    SORT_MAP = {
        'date_end': {'asc': [{'date_end': 'asc'}], 'desc': [{'date_end': 'desc'}], 'default': 'desc'},
        'date_start': {'asc': [{'date_start': 'asc'}], 'desc': [{'date_start': 'desc'}], 'default': 'desc'},
        'name': {'asc': [{'name.sort': 'asc'}], 'desc': [{'name.sort': 'desc'}], 'default': 'asc'},
        'status': {'asc': [{'status': 'asc'}], 'desc': [{'status': 'desc'}], 'default': 'desc'},
        'updated_at': {'asc': [{'updated_at': 'asc'}], 'desc': [{'updated_at': 'desc'}], 'default': 'desc'},
    }
