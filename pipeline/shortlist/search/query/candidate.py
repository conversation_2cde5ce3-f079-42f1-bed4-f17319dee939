from shortlist.search.query.base import SearchQueryBuilder, FilterQueryBuilder, SortBuilder


class CandidateForVendorQueryBuilder(SearchQueryBuilder):
    TEXT_FIELDS = [
        'job_opening.name',
    ]


class CandidateQueryBuilder(SearchQueryBuilder):
    TEXT_FIELDS = [
        'vendor.name',
    ]


class CandidateAggregationQueryBuilder:
    TERMS_FIELDS = [
        'job_opening.location.term',
        'vendor.parent_id',
        'status',
    ]

    def __init__(self, custom_fields=None):
        pass

    def build(self):
        return {field: {'terms': {'field': field, 'size': 1000}} for field in self.TERMS_FIELDS}


class CandidateFilterQueryBuilder(FilterQueryBuilder):
    FILTERS_MAP = {
        'job_opening.location': 'job_opening.location.term',
        'status': 'status',
        'vendor.id': 'vendor.id',
        'vendor.parent_id': 'vendor.parent_id',
        'job_opening.id': 'job_opening.id',
    }


class CandidateQuerySortBuilder(SortBuilder):
    SORT_MAP = {
        'job_opening.created_at': {
            'asc': [{'job_opening.created_at': 'asc'}],
            'desc': [{'job_opening.created_at': 'desc'}],
            'default': 'desc',
        },
        'status': {'asc': [{'status': 'asc'}], 'desc': [{'status': 'desc'}], 'default': 'desc'},
    }
