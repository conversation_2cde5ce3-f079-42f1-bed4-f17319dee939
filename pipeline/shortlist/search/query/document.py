class DocumentQueryBuilder:
    def __init__(self, include=None):
        self.include = include or []

    def build(self):
        return {'bool': {'should': [self._prepare_section(token) for token in self.include]}} if self.include else {}

    def _prepare_section(self, words):
        if isinstance(words, list):
            query = ' '.join(words)
            is_phrase = True
        else:
            query = words
            is_phrase = False

        return {'bool': {'should': self._prepare_content_fields(query, is_phrase)}}

    def _prepare_content_fields(self, query, is_phrase):
        if is_phrase:
            return [{'match_phrase_prefix': {'content': query}}]
        return [{'match': {'content': {'query': query}}}, {'prefix': {'content': query}}]
