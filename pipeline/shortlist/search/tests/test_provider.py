from clients import features
from preferences.models import CustomVendor<PERSON>ield, ComplianceRule
from shortlist.search import custom_fields_search
from shortlist.search.configuration.vendor import VendorDataProvider
from shortlist.tests.helpers import TenantTestCase
from vendors.models import VendorType


class TestVendorDataProvider(TenantTestCase):
    def _unique_name(self):
        return f"{self.factory.word()}{self.factory.word()}"

    def _prepare_custom_fields(self):
        available_custom_fields = (
            CustomVendorField.objects.create(name=self._unique_name(), type=CustomVendorField.TYPE_NUMBER),
            CustomVendorField.objects.create(name=self._unique_name(), type=CustomVendorField.TYPE_BOOLEAN),
            CustomVendorField.objects.create(name=self._unique_name(), type=CustomVendorField.TYPE_TEXT_LINE),
            CustomVendorField.objects.create(name=self._unique_name(), type=CustomVendorField.TYPE_CHOICE, choices=['a', ' b', 'c']),
            CustomVendorField.objects.create(name=self._unique_name(), type=CustomVendorField.TYPE_NOTE),
        )
        freelancer = VendorType.objects.get(name="freelancer")
        freelancer.custom_fields.add(*available_custom_fields)

        return available_custom_fields

    def test_empty_custom_fields_data(self):
        first, second, third, fourth, fifth = self._prepare_custom_fields()
        vendor = self._get_vendor(custom_fields={first.id: None, second.id: None, third.id: '', fourth.id: None})

        result = VendorDataProvider().get(vendor.pk)

        self.assertEqual(
            result,
            {
                **result,
                **{
                    custom_fields_search.to_search_key(first): None,
                    custom_fields_search.to_search_key(second): None,
                    custom_fields_search.to_search_key(third): None,
                    custom_fields_search.to_search_key(fourth): None,
                },
            },
        )

    def test_text_normalization(self):
        first, second, third, fourth, fifth = self._prepare_custom_fields()
        vendor = self._get_vendor(custom_fields={third.id: ' Testing spaces around      ', fourth.id: ['a', ' b']})

        result = VendorDataProvider().get(vendor.pk)

        self.assertEqual(
            result,
            {
                **result,
                **{
                    custom_fields_search.to_search_key(third): 'Testing spaces around',
                    custom_fields_search.to_search_key(fourth): ['a', 'b'],
                },
            },
        )

    def test_wrong_custom_fields_data_treated_as_empty(self):
        first, second, third, fourth, fifth = self._prepare_custom_fields()
        vendor = self._get_vendor(custom_fields={first.id: self.factory.word(), second.id: self.factory.word(), fourth.id: ['d']})

        result = VendorDataProvider().get(vendor.pk)

        self.assertEqual(
            result,
            {
                **result,
                **{
                    custom_fields_search.to_search_key(first): None,
                    custom_fields_search.to_search_key(second): None,
                    custom_fields_search.to_search_key(fourth): None,
                },
            },
        )

    def test_valid_custom_fields_data(self):
        first, second, third, fourth, fifth = self._prepare_custom_fields()
        value = self.factory.sentence()
        vendor = self._get_vendor(
            custom_fields={
                first.id: 1,
                second.id: 1,
                third.id: value,
                fourth.id: ['c'],
                fifth.id: '{"note": "new note for a new vendor", "timestamp": "", "user_name": "test", "user_slug": "test"}',
            }
        )

        result = VendorDataProvider().get(vendor.pk)

        self.assertEqual(
            result,
            {
                **result,
                **{
                    custom_fields_search.to_search_key(first): 1,
                    custom_fields_search.to_search_key(second): True,
                    custom_fields_search.to_search_key(third): value,
                    custom_fields_search.to_search_key(fourth): ['c'],
                    custom_fields_search.to_search_key(fifth): "new note for a new vendor",
                },
            },
        )

    def test_not_existing_custom_field(self):
        vendor = self._get_vendor(custom_fields={99999: self.factory.word()})

        result = VendorDataProvider().get(vendor.pk)

        self.assertNotIn("custom_99999", result.keys())

    def test_empty_score(self):
        vendor = self._get_vendor()

        result = VendorDataProvider().get(vendor.pk)

        self.assertEqual(
            {
                'project_count': 0,
                'recommended_net': 0,
                'recommended_no': 0,
                'recommended_yes': 0,
                'review_count': 0,
                'score_quality': 0.0,
                'score_delivery': 0.0,
                'score_value_for_money': 0.0,
                'score_average': 0.0,
                'recommended_ratio': 0.0,
                'satisfaction_ratio': 0.0,
            },
            result.get('score'),
        )

    def test_invalid_note_treated_as_empty(self):
        first, second, third, fourth, fifth = self._prepare_custom_fields()
        vendor = self._get_vendor(custom_fields={fifth.id: "test"})

        result = VendorDataProvider().get(vendor.pk)

        self.assertEqual(result, {**result, custom_fields_search.to_search_key(fifth): None})

    def test_vendor_with_parent(self):
        parent = self._get_staffing_supplier()
        worker_vendor_type = VendorType.objects.create(name='worker', is_worker=True)
        vendor = self._get_vendor(parent=parent, vendor_type=worker_vendor_type)

        result = VendorDataProvider().get(vendor.pk)

        self.assertEqual(result, {**result, 'parent': {'slug': parent.slug, 'full_name': parent.full_name, 'id': parent.id}})

    def test_vendor_compliance(self):
        default_vt = VendorType.objects.get_default()
        ComplianceRule.objects.create(vendor_type=default_vt, rule_type=ComplianceRule.BANK_DETAILS)
        vendor = self._get_vendor()

        result = VendorDataProvider().get(vendor.pk)

        self.assertEqual(result, {**result, 'compliance': 'not-compliant'})

    def test_vendor_documents(self):
        with self.feature_set(features.VENDOR_SEARCH_INCLUDES_DOCUMENTS, True):
            vendor = self._get_vendor()

            result = VendorDataProvider().get(vendor.pk)
            self.assertNotIn('documents', list(result.keys()))

            result = VendorDataProvider().get(vendor.pk, fields=['full_name'])
            self.assertNotIn('documents', list(result.keys()))

            result = VendorDataProvider().get(vendor.pk, fields=['documents'])
            self.assertIn('documents', list(result.keys()))

            result = VendorDataProvider().get(vendor.pk, fields=[VendorDataProvider.ALL_FIELDS_KEY])
            self.assertIn('documents', list(result.keys()))
