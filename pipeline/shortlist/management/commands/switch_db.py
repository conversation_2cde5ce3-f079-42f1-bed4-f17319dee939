"""
Change Django settings to use another database.
If new database does not exist, clone current one and then switch.

For cloning, only PostgreSQL is supported and pg_dump and psql must be installed.

This should be faster and more robust than dumpdata/loaddate or dumpscript/runscript.

dbname parameter is optional. If not provided, current git branch name will be used after normalization
(removing non alphanumeric, converting to lowercase).
"""

import os
import re
import shutil
import subprocess
import tempfile

from django.conf import settings
from django.core.management.base import BaseCommand

from shortlist.management.postgres_common import create_db, db_exists, drop_db, pg_run_process


def get_git_branch():
    return subprocess.check_output("git symbolic-ref --short HEAD".split())


def normalize(s):
    return re.sub(r"\W", "", s).lower()


CLONE_FILTER = re.compile(b"^(--|CREATE|COMMENT ON) EXTENSION")
BYTES_BUFFER_SIZE = 100000000  # ~ 100mb


def pg_clone(old, new):
    tmpdir = tempfile.mkdtemp()
    try:
        raw_path = os.path.join(tmpdir, f"{old}.raw.sql")
        dump_path = os.path.join(tmpdir, f"{old}.sql")
        pg_run_process(old, ["pg_dump", "-x", "-f", raw_path])

        with open(dump_path, "wb") as dump_file:
            with open(raw_path, "rb") as raw_file:
                lines = raw_file.readlines(BYTES_BUFFER_SIZE)
                while lines:
                    dump_file.writelines([line for line in lines if line and not CLONE_FILTER.search(line)])
                    lines = raw_file.readlines(BYTES_BUFFER_SIZE)

        pg_run_process(new, ["psql", "-f", dump_path])
    finally:
        shutil.rmtree(tmpdir)


class Command(BaseCommand):
    help = __doc__.strip()

    def add_arguments(self, parser):
        parser.add_argument('dbname', nargs="?", help='Database name')
        parser.add_argument('--keep-settings', action='store_true', dest='keep_settings', default=False, help="Don't touch settings")
        parser.add_argument(
            '--use-settings',
            action='store_true',
            dest='use_settings',
            default=False,
            help="Use current database from settings as target. Implies --keep-settings",
        )
        parser.add_argument(
            '--drop', action='store_true', dest='drop', default=False, help="Forcefully drop target database if it exists first"
        )
        parser.add_argument(
            '--fresh', action='store_true', dest='fresh', default=False, help="Don't clone, create a fresh database instead"
        )

    def get_db_name(self, dbname=None):
        if dbname is None:
            dbname = get_git_branch()
        return normalize(dbname)

    def handle(self, *args, **options):
        current = settings.DATABASES['default']['NAME']
        if options.get('use_settings'):
            new = current
        else:
            new = self.get_db_name(options.get('dbname'))
        exists = db_exists(new)
        if exists and options['drop']:
            self.stdout.write(f"Dropping database {new}")
            drop_db(new)
            exists = False
        if not exists:
            create_db(new)
            if options['fresh']:
                # create a fresh db
                self.stdout.write(f"Creating fresh database {new}")
                from django.db import connection
                from clients.models import create_public_tenant

                connection.close()
                connection.settings_dict["NAME"] = new
                create_public_tenant()
            else:
                self.stdout.write(f"Cloning database {current} to {new}")
                pg_clone(current, new)
        if not options['keep_settings'] and not options['use_settings']:
            settings_module = options['settings'] or os.environ['DJANGO_SETTINGS_MODULE']
            settings_path = settings_module.replace('.', '/') + '.py'
            self.stdout.write(f"Switching to database {new} in file {settings_path}")
            with open(settings_path) as f:
                old_contents = f.read()
            new_contents = re.sub(r"(DATABASES[^{]+\{[^}]+'NAME':)[^,]+", r"\1 '%s'" % new, old_contents)
            with open(settings_path, "w") as f:
                f.write(new_contents)
