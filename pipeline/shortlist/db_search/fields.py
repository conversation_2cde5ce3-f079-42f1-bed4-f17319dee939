from django.db import models

from shortlist.db_search.base import CUSTOM_FIELDS_DATA_FIELD
from shortlist.db_search.sql import table_column_name


def custom_field_data_as_json(
    field_id: int | str, *, related_model: type[models.Model] | None = None, through_field: str | None = None
) -> str:
    """
    Generates a SQL expression to access a custom field's data as JSON.

    This function creates a SQL string that extracts a specific custom field's
    data from the custom fields JSON structure, returning it in JSON format.

    Args:
        field_id: The ID of the custom field to access.
        related_model: Optional related model to use for accessing the field.
        through_field: Optional field name for related model access.

    Returns:
        str: A SQL expression string that extracts the custom field data as JSON.

    Example:
        >>> json_expr = custom_field_data_as_json(123, related_model=Vendor)
        >>> # Returns SQL like: ("vendor_table"."custom_fields_data"->'123')
    """
    try:
        field_id_int = int(field_id)
    except (TypeError, ValueError) as ex:
        raise ValueError("field_id must be an int‐convertible value") from ex

    return f"({custom_fields_data_table_column_name(related_model=related_model, through_field=through_field)}->'{field_id_int}')"


def custom_field_data_as_text(
    field_id: int | str, *, related_model: type[models.Model] | None = None, through_field: str | None = None
) -> str:
    """
    Generates a SQL expression to access a custom field's data as text.

    This function creates a SQL string that extracts a specific custom field's
    data from the custom fields JSON structure, returning it as text.

    Args:
        field_id: The ID of the custom field to access.
        related_model: Optional related model to use for accessing the field.
        through_field: Optional field name for related model access.

    Returns:
        str: A SQL expression string that extracts the custom field data as text.

    Example:
        >>> text_expr = custom_field_data_as_text(123, related_model=Vendor)
        >>> # Returns SQL like: ("vendor_table"."custom_fields_data"->>'123')
    """
    try:
        field_id_int = int(field_id)
    except (TypeError, ValueError) as ex:
        raise ValueError("field_id must be an int‐convertible value") from ex

    return f"({custom_fields_data_table_column_name(related_model=related_model, through_field=through_field)}->>'{field_id_int}')"


def custom_fields_data_table_column_name(*, related_model: type[models.Model] | None = None, through_field: str | None = None) -> str:
    """
    Generates the fully qualified column name for the custom fields data.

    This function creates a properly formatted column reference for the custom fields
    data field, optionally including table name or through field prefix.

    Args:
        related_model: Optional related model to use for accessing the field.
        through_field: Optional field name for related model access.

    Returns:
        str: The fully qualified column name for the custom fields data.

    Example:
        >>> column_name = custom_fields_data_table_column_name(related_model=Vendor)
        >>> # Returns: '"vendor_table"."custom_fields_data"'
        >>> column_name = custom_fields_data_table_column_name(through_field='vendor')
        >>> # Returns: 'vendor__custom_fields_data'
    """
    return table_column_name(CUSTOM_FIELDS_DATA_FIELD, related_model=related_model, through_field=through_field)
