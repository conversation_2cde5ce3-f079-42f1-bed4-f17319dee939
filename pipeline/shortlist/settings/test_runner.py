from django.test.runner import <PERSON>ver<PERSON>unner

from integrations.tests.helpers import FakeGoogleMapsClient


def sort_key(case):
    return str(case.__class__), case._testMethodName


class ShortlistTestRunner(DiscoverRunner):
    def setup_test_environment(self, **kwargs):
        import googlemaps

        super().setup_test_environment(**kwargs)
        googlemaps.Client = FakeGoogleMapsClient

    def run_suite(self, suite, **kwargs):
        suite._tests.sort(key=sort_key)
        return super().run_suite(suite, **kwargs)
