from unittest import TestCase

from parameterized import parameterized

from shortlist.serializers import HtmlContentField


class SharedSerializersTests(TestCase):
    @parameterized.expand(
        [
            ("foo", "foo"),
            ("Lorem <strong>ipsum</strong>", "Lorem <strong>ipsum</strong>"),
            ("Lorem <a href=\"https://example.com\">ipsum</a>", "Lorem <a href=\"https://example.com\">ipsum</a>"),
            ("Lorem <u>ipsum</u> <script>alert(\"!\");</script>", "Lorem <u>ipsum</u> &lt;script&gt;alert(\"!\");&lt;/script&gt;"),
        ]
    )
    def test_html_content_field(self, text, expected_output):
        self.assertEqual(HtmlContentField().to_internal_value(text), expected_output)
