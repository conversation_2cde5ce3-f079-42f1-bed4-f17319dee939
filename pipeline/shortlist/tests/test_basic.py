import datetime
import pytz


from django.db import connection
from django.test import TestCase
from faker import Factory
from unittest import mock

from tenant_schemas.utils import schema_exists, get_public_schema_name, create_current_schema_mock

from clients.models import Client
from shortlist.utils import convert_time_tz


class BaseTenantTest(TestCase):
    def setUp(self):
        self.factory = Factory.create()
        self.factory.seed(1)  # get repeatable results
        connection.set_schema_to_public()

    def tearDown(self):
        connection.set_schema_to_public()

    def test_public_client_exists(self):
        self.assertTrue(schema_exists(get_public_schema_name()))

    def test_client_creating(self):
        company_name = self.factory.name()
        schema_name = 'p_{}'.format('current')
        self.assertFalse(Client.objects.filter(name=company_name).exists())
        with mock.patch('clients.models.Client.create_schema', side_effect=create_current_schema_mock) as create_schema_mock:
            client = Client(domain_prefix='current', name=company_name, schema_name=schema_name)
            client.save()

        create_schema_mock.assert_called_once_with(check_if_exists=True, verbosity=mock.ANY)
        self.assertTrue(Client.objects.filter(name=company_name).exists())
        self.assertTrue(schema_exists(client.schema_name))

    @mock.patch('clients.models.Client.create_schema', mock.Mock())
    def test_tenant_change(self):
        company_name = self.factory.name()
        domain_prefix = 'test'
        schema_name = 'p_{}'.format('test')
        self.assertFalse(Client.objects.filter(name=company_name).exists())
        client = Client(domain_prefix='test', name=company_name, schema_name=schema_name)
        client.save()
        connection.set_tenant(client)
        current_tenant = connection.tenant
        self.assertEqual(domain_prefix, current_tenant.domain_prefix)
        self.assertEqual(schema_name, current_tenant.schema_name)

    def test_timezone_convert(self):
        from_tz = pytz.timezone('US/Pacific')
        to_tz = pytz.timezone('US/Eastern')
        original = datetime.time(5)
        converted = convert_time_tz(original, from_tz, to_tz)
        self.assertEqual(converted.hour, 8, "There should be a 3 hours offset between US/Eastern and US/Pacific")
