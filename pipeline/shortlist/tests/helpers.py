import copy
import logging
import random
import string
from contextlib import contextmanager
from datetime import date, timedelta, UTC
from functools import partial, wraps
from itertools import chain
from typing import TypeVar
from unittest import mock

import pycountry
import requests
import responses
from faker import Factory, Generator
from faker.config import PROVIDERS
from faker.providers import date_time

from django.core import mail
from django.core.exceptions import ObjectDoesNotExist
from django.db import connection, connections, reset_queries, transaction
from django.db.utils import DEFAULT_DB_ALIAS
from django.test import TestCase
from django.test.utils import CaptureQueriesContext
from django.utils.timezone import now

from rest_framework import ISO_8601
from rest_framework import status as http_status
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.response import Response
from rest_framework.settings import api_settings
from rest_framework.test import APIClient

from clients.models import Client, generate_schema_name
from documents.models import RequestedDocument, RequestedDocumentTemplate
from documents.services.requested_documents import create_requested_documents
from events.base import EventBase

# turn one events based emails
from notifications import emails  # noqa: F401, isort:skip
from onboarding.models import (
    OnBoardingStage,
    OnBoardingStagesForVendor,
    OnBoardingWorkflow,
    OnBoardingWorkflowsForVendor,
    VendorOnBoardingStageForm,
    VendorOnBoardingStageFormResponse,
)
from payments.tests.payout_methods.factories import create_payout_method
from projects.models import Project, ProjectVendors, ProjectVersion
from shortlist import current_user
from shortlist.mail import mail_outbox
from shortlist.permissions import Permission
from tasks.models import Milestone, MilestoneForm, TaskTimeSheet, TaskTimeSheetPeriod
from tenant_schemas.utils import schema_exists
from users.models import CustomPermission, User, UserRole
from vendors.models import Vendor, VendorGroup, VendorType

logging.basicConfig(level=logging.CRITICAL)

E = TypeVar("E", bound=EventBase)


class ExternalAPICallNotAllowedError(Exception):
    def __init__(self, *args, **kwargs):
        self.url = self.__extract_url(*args, **kwargs)
        super().__init__(
            f"External API call blocked: {self.url}. This action is not allowed in tests. Use responses or mock.patch to mock external API calls."
        )

    @staticmethod
    def __extract_url(*args, **kwargs):
        url = kwargs.get("url")

        if url is not None:
            return url

        # Handle usage of socket library
        if args and isinstance(args[0], tuple):
            return f"{args[0][0]}:{args[0][1]}"

        # Handle usage of requests library
        if args and isinstance(args[0], str):
            return args[0]

        return "[UNKNOWN URL]"


def datetime_format(value):
    """
    Copied from rest_framework.fields.DateField#to_representation
    """
    if not value:
        return None

    output_format = api_settings.DATETIME_FORMAT

    if output_format is None or isinstance(value, str):
        return value

    if output_format.lower() == ISO_8601:
        value = value.isoformat()
        if value.endswith('+00:00'):
            value = value[:-6] + 'Z'
        return value
    return value.strftime(output_format)


def valid_email_factory(original_factory):
    from shortlist.validators import EMAIL_VALIDATORS, ValidationError

    def valid_email():
        while True:
            email = original_factory()
            try:
                for v in EMAIL_VALIDATORS:
                    v(email)
            except ValidationError:
                pass
            else:
                return email

    return valid_email


class DateTimeProvider(date_time.Provider):
    def date_time(self, **kwargs):
        return super().date_time().replace(tzinfo=UTC)

    def date_time_ad(self, **kwargs):
        return super().date_time_ad().replace(tzinfo=UTC)

    def date_time_between(self, start_date='-30y', end_date='now', **kwargs):
        return super().date_time_between(start_date, end_date).replace(tzinfo=UTC)


def generate_unique(what, number=None):
    previous = set()
    value = what()
    while number is None or len(previous) < number:
        while value in previous:
            value = what()
        yield value
        previous.add(value)


def get_next(iterator):
    return iterator.__next__


class BaseTestCase(TestCase):
    FACTORY_TESTS_SEED = 1

    @classmethod
    def setUpClass(cls):
        cls._delete_directory_from_s3_mock = mock.patch('vendors.models.vendor.delete_directory_from_s3')
        cls._delete_directory_from_s3_mock.start()

        original_requests_send = requests.Session.send

        def mock_requests_calls(session, request, **kwargs):
            if responses.mock and responses.mock.registered():
                # Delegate to the original send method if the request is mocked by `responses`
                # `responses` mocks API calls on lower `requests` level
                return original_requests_send(session, request, **kwargs)
            raise ExternalAPICallNotAllowedError(request.url)

        def mock_socket_calls(address, **kwargs):
            raise ExternalAPICallNotAllowedError(address, **kwargs)

        cls._request_mock = mock.patch('requests.Session.send', side_effect=mock_requests_calls, autospec=True)
        cls._connect_mock = mock.patch('socket.socket.connect', side_effect=mock_socket_calls)
        cls._urllib_mock = mock.patch('urllib.request.urlopen', side_effect=ExternalAPICallNotAllowedError)
        cls._connect_mock.start()
        cls._request_mock.start()
        cls._urllib_mock.start()

        super().setUpClass()

    @classmethod
    def tearDownClass(cls):
        super().tearDownClass()
        cls._delete_directory_from_s3_mock.stop()
        cls._connect_mock.stop()
        cls._request_mock.stop()
        cls._urllib_mock.stop()

    def tearDown(self):
        super().tearDown()
        self.clearMails()

    generator = Generator()
    generator.seed(seed=FACTORY_TESTS_SEED)
    factory = Factory.create(providers=list(set(PROVIDERS) - {'date_time'}), generator=generator)
    factory.add_provider(DateTimeProvider)
    factory.email = get_next(generate_unique(valid_email_factory(factory.email)))
    factory.external_id = get_next(generate_unique(factory.word))
    factory.payment_amount = partial(factory.pydecimal, positive=True, max_value=999_999_999_999, right_digits=2)

    def check_response(self, expected_code, response, message=None):
        default_message = "Expected {}, instead got {}:\n\n{}\n\n{}"
        if isinstance(expected_code, int):
            if response.status_code != expected_code:
                message = default_message.format(expected_code, response.status_code, response, getattr(response, 'data', None))
            self.assertEqual(response.status_code, expected_code, message)
        else:
            if response.status_code not in expected_code:
                message = default_message.format(expected_code, response.status_code, response, "")
            self.assertIn(response.status_code, expected_code, message)

        return response

    @contextmanager
    def watch_event(self, event_cls: type[E]):
        triggered_events = []
        watcher = triggered_events.append
        try:
            event_cls.watch(watcher)
            yield triggered_events
        finally:
            event_cls.unwatch(watcher)

    @contextmanager
    def watch_events(self, event_classes: list[type[E]]):
        triggered_events = []
        watcher = triggered_events.append

        for event_cls in event_classes:
            event_cls.watch(watcher)

        try:
            yield triggered_events
        finally:
            for event_cls in event_classes:
                event_cls.unwatch(watcher)

    def _get_message_template_name(self, message):
        return message.unique_args.get("template_name")

    def _get_message_subs(self, message):
        return message.substitutions

    def _get_message_sections(self, message):
        return message.sections

    def _get_mails(self, template_name=None):
        for message in mail_outbox:
            if template_name is None or self._get_message_template_name(message) == template_name:
                yield message

    def clearMails(self):
        del mail_outbox[:]

    def assertMailSent(self, to=None, template_name=None, message=None, times=1):
        if message is None:
            message = "{} should have been sent {} times".format(template_name if template_name else 'email', times)
        sent = [chain.from_iterable(m.recipients for m in self._get_mails(template_name))]
        if to:
            sent_copy = list(sent)
            sent = []
            for recipients in sent_copy:
                for recipient in recipients:
                    if recipient == to:
                        sent.append(recipient)
        self.assertEqual(len(sent), times, message)

    def assertMailNotSent(self, to=None, template_name=None, message=None):
        if message is None:
            message = "{} should not have been sent".format(template_name if template_name else 'email')
        if to:
            sent = list(chain.from_iterable(m.recipients for m in self._get_mails(template_name)))
            email_sent = False
            for recipients in sent:
                for recipient in recipients:
                    if recipient == to:
                        email_sent = True
            self.assertFalse(email_sent, message)
        else:
            self.assertFalse(bool(set(self._get_mails(template_name))), message)

    def assertHasSubs(self, subs, template_name=None, not_empty_only=True):
        for message in self._get_mails(template_name):
            message_subs = self._get_message_subs(message)
            for substitutions in message_subs:
                if not_empty_only:
                    substitutions = {k: v for k, v in substitutions.items() if v}
                self.assertLessEqual(
                    set(subs),
                    set(substitutions.keys()),
                    f"email {template_name} do not have all required subs {sorted(subs)} in {sorted(substitutions.keys())}. Missing ones: {set(subs) - set(substitutions.keys())}",
                )

    def assertHasSections(self, sections, template_name=None, not_empty_only=True):
        for message in self._get_mails(template_name):
            message_sections = self._get_message_sections(message)
            if not_empty_only:
                sections = {k: v for k, v in message_sections.items() if v}
            self.assertLessEqual(
                set(sections),
                set(message_sections.keys()),
                f"email {template_name} do not have all required subs {sorted(sections)} in {sorted(message_sections.keys())}. Missing ones: {set(sections) - set(message_sections.keys())}",
            )


def features_enabled(features_to_enable, features_to_disable=None):
    def wrapper(f):
        @wraps(f)
        def wrapped(self, *args, **kwargs):
            tenant = connection.tenant
            old_features = tenant.features

            for feature in features_to_enable:
                tenant.switch_feature(feature, True)

            if features_to_disable:
                for feature in features_to_disable:
                    tenant.switch_feature(feature, False)

            tenant.save()

            try:
                f(self, *args, **kwargs)
            finally:
                tenant.features = old_features
                tenant.save()

        return wrapped

    return wrapper


def tenant_timezone(tz):
    def wrapper(f):
        @wraps(f)
        def wrapped(self, *args, **kwargs):
            tenant = connection.tenant
            old_timezone = tenant.timezone
            tenant.timezone = tz
            tenant.save()

            try:
                f(self, *args, **kwargs)
            finally:
                tenant.timezone = old_timezone
                tenant.save()

        return wrapped

    return wrapper


def create_api_client(http_host):
    api_client = APIClient()
    api_client.default_format = 'json'
    api_client.credentials(HTTP_HOST=http_host)
    return api_client


class TenantTestCase(BaseTestCase):
    """
    Basic class for testing pipeline.
    """

    create_user = True
    with_login_user = False
    with_admin_user = False
    with_guest_user = False
    with_vendor_user = False
    with_supplier_user = False
    with_staff_user = False
    vendor_user_invited = True
    requires_real_transactions = False  # use methods from TransactionTestCase if True
    user = None
    shared_tenants = {}
    shared_tenant_name = "shared"
    required_tenant_name = "test1"
    permissions = None

    @classmethod
    def setUpClass(cls):
        cls.setup_tenant()
        super().setUpClass()

    def setUp(self):
        super().setUp()
        current_user.storage.request = None
        self.api_client = create_api_client(self.tenant.domain_url)
        self.setup_user()
        connection.tenant.cache.invalidate()

    def run_commit_hooks(self):
        """
        Fake transaction commit to run delayed on_commit functions
        :return:
        """
        for db_name in reversed(self._databases_names()):
            with mock.patch('django.db.backends.base.base.BaseDatabaseWrapper.validate_no_atomic_block', lambda a: False):
                transaction.get_connection(using=db_name).run_and_clear_commit_hooks()

    @staticmethod
    def dummy_middleware(request):
        """
        A dummy middleware method for testing purposes.
        Returns an empty HTTP response.
        """
        return Response()

    def create_password(self, length=10, special_chars=True, digits=True, upper_case=True, lower_case=True):
        if not digits:
            return self.factory.password(length, special_chars, digits, upper_case, lower_case)
        while True:
            password = self.factory.password(length, special_chars, digits, upper_case, lower_case)
            if set(password) & set(string.digits) and set(password) & set(string.ascii_letters):
                return password

    def setup_user(self):
        assert not (self.with_vendor_user and self.with_login_user), "with_vendor_user and with_login_user are mutually exclusive"
        assert not (self.with_vendor_user and self.with_supplier_user), "vendor and supplier are exclusive"
        assert not (self.with_login_user and self.with_supplier_user), "login_user and supplier are exclusive"
        if self.create_user and self.user is None:
            self.user = self._get_user(is_admin=self.with_admin_user, is_staff=self.with_staff_user, permissions=self.permissions)
        if self.with_admin_user or self.with_login_user or self.with_staff_user:
            self.api_client.login(email=self.user.email, password=self.user_password)
        elif self.with_vendor_user:
            self._get_vendor(able_to_login=True, invited=self.vendor_user_invited)
        elif self.with_supplier_user:
            self._get_staffing_supplier(able_to_login=True, invited=self.vendor_user_invited)
        elif self.with_guest_user:
            self._get_guest(able_to_login=True)

        def set_current_request(request=None):
            if request:
                current_user.storage.request = request
            else:
                if self.with_vendor_user or self.with_supplier_user:
                    if getattr(self, 'vendor', None) is None:
                        if self.with_supplier_user:
                            self._get_staffing_supplier(able_to_login=True, invited=self.vendor_user_invited)
                        else:
                            self._get_vendor(able_to_login=True, invited=self.vendor_user_invited)
                    self.api_client.login(email=self.vendor.email, password=self.vendor_password)
                    current_user.storage = mock.MagicMock()
                    current_user.storage.request.user = self.vendor.first_contact
                elif self.with_guest_user:
                    if getattr(self, 'guest', None) is None:
                        self._get_guest(able_to_login=True)
                    self.api_client.force_login(self.guest)
                    current_user.storage = mock.MagicMock()
                    current_user.storage.request.user = self.guest
                else:
                    current_user.storage = mock.MagicMock()
                    current_user.storage.request.user = self.user

        set_current_request()
        current_user.set_current_request = set_current_request

    @contextmanager
    def as_user(self, user=None):
        old_api_client = None
        if user:
            old_api_client = self.api_client
            self.api_client = create_api_client(self.tenant.domain_url)
        else:
            user = self.user

        try:
            self.api_client.login(email=user.email, password=user._test_password)
            with mock.patch.object(current_user, 'storage') as storage:
                storage.request.user = user
                yield
                self.api_client.logout()
        finally:
            if old_api_client:
                self.api_client = old_api_client

    @contextmanager
    def as_guest(self):
        if getattr(self, 'guest', None) is None:
            self._get_guest(able_to_login=True)

        self.api_client.login(email=self.guest.email, password=self.user_password)
        with mock.patch.object(current_user, 'storage') as storage:
            storage.request.user = self.user
            yield
            self.api_client.logout()

    @contextmanager
    def as_vendor(self, vendor=None):
        if vendor:
            vendor_password = vendor._test_password
        elif getattr(self, 'vendor', None) is None:
            vendor = self._get_vendor(able_to_login=True)
            vendor_password = self.vendor_password
        else:
            vendor = self.vendor
            vendor_password = self.vendor_password

        self.api_client.login(email=vendor.email, password=vendor_password)
        with mock.patch.object(current_user, 'storage') as storage:
            storage.request.user = vendor.first_contact
            yield
            if self.user is not None:
                self.api_client.login(email=self.user.email, password=self.user_password)

    @property
    def recipients(self):
        """All addresses we sent the emails to"""
        return set(chain.from_iterable([msg.to for msg in mail.outbox]))

    def _fixture_setup(self):
        if not self.requires_real_transactions:
            super()._fixture_setup()
        else:
            self.__class__.setup_tenant()

    def _fixture_teardown(self):
        if not self.requires_real_transactions:
            super()._fixture_teardown()

    def _pre_setup(self):
        super()._pre_setup()
        connection.set_tenant(self.tenant)

    def _post_teardown(self):
        super()._post_teardown()

        if self.requires_real_transactions:
            self.destroy_tenant()

    @classmethod
    def _enter_atomics(cls):
        if not cls.requires_real_transactions:
            return super()._enter_atomics()

        return {}

    @classmethod
    def _rollback_atomics(cls, atomics):
        if not cls.requires_real_transactions:
            super()._rollback_atomics(atomics)

    @contextmanager
    def limit_query_count(self, num, **kwargs):
        """
        Context manager that can be used inside test case to make sure we're not
        running large number of SQL queries, for example due to Django ORM misconfiguration.

        Example:
        with self.limit_query_count(300):
            # do something that touches database
            # will fail if it does more than 300 queries

        By default, test will also fail if it does no queries at all, as that is most
        likely coding error. If 0 queries is fine, pass zero_ok=True to this function.
        using kwargs can be used to watch queries against non-default database.
        """
        using = kwargs.pop("using", DEFAULT_DB_ALIAS)
        conn = connections[using]

        reset_queries()
        with CaptureQueriesContext(conn) as context:
            yield
            if len(context) > num:
                self.fail(f"{len(context)} queries executed, no more than {num} expected")
            if len(context) == 0 and not kwargs.get('zero_ok', False):
                # this most likely means a coding error in test case
                self.fail("no queries executed in LimitQueryCount")

    @classmethod
    def delete_existing_tenant(cls, schema_name):
        try:
            tenant = Client.objects.get(domain_prefix=schema_name)
            tenant.delete()
        except ObjectDoesNotExist:
            db_schema_name = generate_schema_name(schema_name)
            if schema_exists(db_schema_name):
                cursor = connection.cursor()
                cursor.execute("DROP SCHEMA %s CASCADE" % db_schema_name)
                cursor.close()

    @classmethod
    def create_tenant(cls, schema_name):
        connection.set_schema_to_public()

        cls.delete_existing_tenant(schema_name)

        tenant = Client(
            domain_prefix=schema_name,
            schema_name=generate_schema_name(schema_name),
            name=schema_name,
            owner_email=cls.factory.email(),
            current_plan='internal',
        )
        tenant.save()
        return tenant

    @classmethod
    def setup_tenant(cls):
        schema_name = cls.required_tenant_name if cls.requires_real_transactions else cls.shared_tenant_name
        if cls.requires_real_transactions or schema_name not in cls.shared_tenants:
            cls.tenant = cls.create_tenant(schema_name)
            if not cls.requires_real_transactions:
                cls.shared_tenants[schema_name] = cls.tenant
            connection.set_tenant(cls.tenant)
            cls.tenant.cache.invalidate()
            return True
        else:
            cls.tenant = cls.shared_tenants[schema_name]
            connection.set_tenant(cls.tenant)
            cls.tenant.cache.invalidate()
            return False

    def destroy_tenant(self):
        connection.set_schema_to_public()
        self.tenant.delete()

    def _get_project(self, **kwargs):
        from projects import VERSIONED_FIELDS

        assert self.factory, 'Test class has to have factory attr'
        assert self.user, 'Test class has to have user class'
        name = kwargs.pop('name', None) or self.factory.sentence()[:20]
        version_kwargs = {'description': self.factory.text(), 'latest': True}
        for key in VERSIONED_FIELDS.intersection(list(kwargs.keys())):
            version_kwargs[key] = kwargs.pop(key)
        project = Project.objects.create(name=name, created_by=self.user, **kwargs)
        version = ProjectVersion.objects.create(project=project, **version_kwargs)
        project.latest_version = version
        project.save()
        if project.status != project.STATUS_DRAFT:
            project.publish()
        return project

    def _get_full_project(self, **kwargs) -> Project:
        if 'vendor' in kwargs:
            vendor = kwargs.pop('vendor')
        else:
            vendor = None
        vendor_count = kwargs.pop('vendor_count', 1)

        kwargs.setdefault('submission_deadline', now() + timedelta(days=7))
        project = self._get_project(**kwargs)

        if not vendor:
            for _ in range(0, vendor_count):
                vendor = Vendor()
                vendor.introduction = self.factory.text()
                vendor.name = self.factory.name()
                vendor.first_name = self.factory.first_name()
                vendor.email = self.factory.email()
                vendor.save()
                vendor_project = ProjectVendors()
                vendor_project.project = project
                vendor_project.vendor = vendor
                vendor_project.save()
        else:
            vendor_project = ProjectVendors()
            vendor_project.project = project
            vendor_project.vendor = vendor
            vendor_project.save()

        return project

    def _get_user(self, is_admin=False, is_active=True, is_staff=False, email=None, password=None, permissions=None, **kwargs) -> User:
        self.user_password = password or self.create_password()
        user = User.objects.create(
            email=email or self.factory.email(),
            first_name=kwargs.pop("first_name", None) or self.factory.first_name(),
            last_name=kwargs.pop("last_name", None) or self.factory.last_name(),
            is_active=is_active,
            is_staff=is_staff,
            team=kwargs.pop('team', None),
            external_id=kwargs.pop("external_id", None),
        )
        user.convert_to_buyer()
        user.set_password(self.user_password)
        for key, value in kwargs.items():
            setattr(user, key, value)
        user.save()
        if is_admin and not is_staff:
            user.make_admin()
        user = User.objects.get(id=user.id)
        user._test_password = self.user_password

        if permissions is not None:
            role_grant = user.role_grants.get()
            role = role_grant.role
            for permission in permissions:
                if isinstance(permission, Permission):
                    CustomPermission.objects.create(
                        role=role,
                        app=permission.app,
                        object_types=list(permission.types),
                        actions=list(permission.actions),
                    )

        return user

    def _get_guest(self, able_to_login=False, is_active=True, email=None, password=None, **kwargs):
        self.user_password = password or self.create_password()
        user = User.objects.create(
            email=email or self.factory.email(),
            first_name=self.factory.first_name(),
            last_name=self.factory.last_name(),
            is_active=is_active,
        )
        user.set_password(self.user_password)
        for key, value in kwargs.items():
            setattr(user, key, value)
        user.role_names = ['guest']
        user.save()

        if able_to_login and not user.is_active:
            user.is_active = True
            user.save()

        self.guest = User.objects.guests().get(id=user.id)
        return self.guest

    def _get_normal_users(self, count):
        return [self._get_user() for _ in range(count)]

    def _random_country(self):
        """
        Use this to generate random countries, as ones in faker don't match 100% those in pycountry (see SHOR-190).
        """
        return random.choice(pycountry.countries.objects).name

    def _get_vendor(self, able_to_login=False, **kwargs) -> Vendor:
        vendor = self._new_vendor(able_to_login=able_to_login, **kwargs)
        self.vendor = vendor
        if able_to_login:
            self.vendor_password = vendor.first_contact._test_password

        return vendor

    def _new_vendor(self, able_to_login=False, **kwargs) -> Vendor:
        assert 'shortlisted' not in kwargs
        parent = None
        email = kwargs.pop('email', self.factory.email())
        email_verified = kwargs.pop('email_verified', True)
        name = kwargs.pop('name', self.factory.name())
        external_id = kwargs.pop('external_id', None)
        invited = kwargs.pop('invited', True)
        bank_details = kwargs.pop('bank_details', None)
        vendor_type = VendorType.objects.enabled_or_default(kwargs.pop('vendor_type', ''))
        if vendor_type.is_worker:
            parent = kwargs.pop('parent')

        if invited:
            invited_at = last_active = now()
        else:
            invited_at = last_active = None

        user = getattr(self, 'user', None)
        vendor = Vendor.objects.create(
            email=email,
            introduction=self.factory.text(),
            name=name,
            first_name=self.factory.first_name(),
            last_name=self.factory.last_name(),
            invited_by=user if user and user.email else None,  # no guarantee we have self.user
            vendor_type=vendor_type,
            parent=parent,
            email_verified=email_verified,
            invited_at=invited_at,
            last_active=last_active,
            **kwargs,
        )

        if external_id:
            vendor.external_id = external_id

        if able_to_login:
            self._activate_vendor(vendor)

        if bank_details:
            if 'attributes' in bank_details:
                # TODO how to stop depending on connection.tenant.domain_url?
                bank_details['attributes'].update({'recipientReference': f'{connection.tenant.domain_url}:{vendor.pk}'})
                create_payout_method(vendor.id, schema_data=bank_details["attributes"], processor=getattr(self, "mc_processor", None))
            else:
                vendor.bank_details = bank_details

        return vendor

    def _get_staffing_supplier(self, able_to_login=False, **kwargs):
        kwargs.setdefault('vendor_type', VendorType.objects.get_or_create(name="supplier", label="Staffing Supplier", is_supplier=True)[0])
        return self._get_vendor(able_to_login, **kwargs)

    def _activate_vendor(self, vendor):
        user = vendor.first_contact
        if not user.is_active:
            user.is_active = True
            user.first_name = vendor.first_name
            user.last_name = vendor.last_name
            self.vendor_password = self.create_password()
            user.set_password(self.vendor_password)
            user.save()
            user._test_password = self.vendor_password
            vendor._test_password = self.vendor_password
            return user

    def _get_vendors(self, count, **kwargs):
        return [self._get_vendor(**kwargs) for _ in range(count)]

    def _get_vendor_group(self, **kwargs):
        assert hasattr(self, '_get_user'), 'You have to use PipelineUserTestHelper first'
        created_by = kwargs.get('created_by', self._get_user())
        vendor_group = VendorGroup(name=f"{self.factory.name()}{self.factory.name()}", created_by=created_by)
        vendor_group.save()
        vendors = kwargs.get('vendors')
        vendors_number = kwargs.get('vendors_number', random.randint(1, 10))

        if not vendors:
            for _ in range(vendors_number):
                vendor_group.vendors.add(self._get_vendor())
        else:
            vendor_group.vendors.add(*vendors)
        return vendor_group

    def _get_vendor_groups(self, count, **kwargs):
        return [self._get_vendor_group(**kwargs) for _ in range(count)]

    @contextmanager
    def feature_set(self, feature, enabled=True):
        tenant = connection.tenant
        old_status = tenant.has_features(feature)

        tenant.switch_feature(feature, enabled)
        tenant.save()

        try:
            yield
        finally:
            tenant.switch_feature(feature, old_status)
            tenant.save()

    @contextmanager
    def features_enabled(self, *features):
        tenant = connection.tenant
        old_features = tenant.features

        for feature in features:
            tenant.switch_feature(feature, True)
        tenant.save()

        try:
            yield
        finally:
            tenant.features = old_features
            tenant.save()

    @contextmanager
    def switch_on_features(self, *features):
        tenant = connection.tenant
        for feature in features:
            tenant.switch_feature(feature, True)
        tenant.save()

    @contextmanager
    def switch_off_features(self, *features):
        tenant = connection.tenant
        for feature in features:
            tenant.switch_feature(feature, False)
        tenant.save()

    @staticmethod
    @contextmanager
    def roles_enabled(role_name, includes=None, excludes=None):
        user_role = UserRole.objects.get(name=role_name)
        old_includes = copy.copy(user_role.includes)
        if includes:
            user_role.includes = includes
        if excludes:
            for item in excludes:
                if item in user_role.includes:
                    user_role.includes.remove(item)
        user_role.save()

        try:
            yield
        finally:
            user_role.includes = old_includes
            user_role.save()

    @contextmanager
    def user_roles_enabled(self, user, includes=None, excludes=None):
        old_includes = user.role_names
        if excludes:
            user.remove_roles(*excludes)
        if includes:
            user.add_roles(*includes)
        user.refresh_from_db()
        try:
            yield
        finally:
            user.role_grants.all().delete()
            user.add_roles(*old_includes)
            user.refresh_from_db()

    # tasks mixin
    def get_task_group_data(self, **kwargs):
        import factory

        from tasks.tests.factories import TaskGroupFactory

        return factory.build(dict, FACTORY_CLASS=TaskGroupFactory, **kwargs)

    def get_task_data(self, **kwargs):
        import factory

        from tasks.tests.factories import TaskFactory

        if "date_start" not in kwargs:
            kwargs["date_start"] = None

        if "date_end" not in kwargs:
            kwargs["date_end"] = None

        return factory.build(dict, FACTORY_CLASS=TaskFactory, **kwargs)

    def get_task_milestone_data(self, **kwargs):
        milestone = {'name': self.factory.sentence(), 'description': self.factory.sentence(), 'milestone_order': random.randint(1, 1000)}
        milestone.update(**kwargs)
        return milestone

    def create_task_milestone(self, **kwargs):
        return Milestone.objects.create(**self.get_task_milestone_data(**kwargs))

    def get_task_milestone_form_data(self, **kwargs):
        milestone_form = {'contents': [{'key': f'textarea-{random.randint(1, 1000)}'}]}
        milestone_form.update(**kwargs)
        return milestone_form

    def create_task_milestone_form(self, **kwargs):
        return MilestoneForm.objects.create(**self.get_task_milestone_form_data(**kwargs))

    def get_task_timesheet_data(self, **kwargs):
        task_timesheet = {
            'date_start': '2022-04-04',
            'date_end': '2022-04-08',
            'minutes_per_day': {
                '2022-04-04': {'minutes': 50, 'note': 'A note'},
                '2022-04-05': 70,
                '2022-04-06': 65,
                '2022-04-07': 80,
                '2022-04-08': 95,
            },
            'minutes_total': 370,
        }
        task_timesheet.update(**kwargs)
        return task_timesheet

    def create_task_timesheet(self, **kwargs):
        timesheet_data = self.get_task_timesheet_data(**kwargs)
        task_timesheet = TaskTimeSheetPeriod.objects.create(**timesheet_data)
        TaskTimeSheet.objects.bulk_create(
            [
                TaskTimeSheet(
                    task=task_timesheet.task,
                    vendor=task_timesheet.vendor,
                    date_worked=date_worked,
                    minutes_worked=data.get("minutes") if isinstance(data, dict) else data,
                    note=data.get("note") if isinstance(data, dict) else None,
                )
                for date_worked, data in timesheet_data["minutes_per_day"].items()
            ]
        )
        return task_timesheet


class APITestCaseMixin:
    def api_get(self, path, status=http_status.HTTP_200_OK, data=None):
        return self.check_response(status, self.api_client.get(path, data=data)).data

    def api_post(self, path, status=http_status.HTTP_201_CREATED, data=None):
        return self.check_response(status, self.api_client.post(path, data=data)).data

    def api_put(self, path, status=http_status.HTTP_200_OK, data=None):
        return self.check_response(status, self.api_client.put(path, data=data)).data

    def api_patch(self, path, status=http_status.HTTP_200_OK, data=None):
        return self.check_response(status, self.api_client.patch(path, data=data)).data

    def api_delete(self, path, status=http_status.HTTP_204_NO_CONTENT):
        return self.check_response(status, self.api_client.delete(path)).data


def to_json(serializer):
    return JSONRenderer().render(serializer.data)


def generate_upload_path(model, factory):
    return model.get_upload_path() + f'{factory.word()}_{factory.word()}'


class RequestedDocumentsTemplateDataMixIn:
    def get_requested_document_template(self, minimal=True, **updates):
        data = {
            'name': self.factory.name(),
            'type': random.choice(['license', 'certificate', 'insurance', 'other document']),
        }
        if not minimal:
            data['description'] = self.factory.text(60)
            data['expiration_date_required'] = True

        data.update(**updates)
        return data


class SimpleOnBoardingOperationsTestCase(TenantTestCase, RequestedDocumentsTemplateDataMixIn):
    """
    OT - onboarding template
    RDT - requested document template
    RD - requested document
    OW - onboarding workflow
    """

    def get_ot_data(self, **kwargs):
        ot = {'name': self.factory.sentence()[:75], 'description': self.factory.sentence()}
        ot.update(**kwargs)
        return ot

    def get_ow_data(self, **kwargs):
        ow = {
            'name': self.factory.sentence()[:75],
            'description': self.factory.sentence(),
            'description_widget': self.factory.sentence()[:250],
        }
        ow.update(**kwargs)
        return ow

    def get_questionnaire_form_elem(self, question_key="description-1", question_type="textarea", mandatory=True):
        return {"key": question_key, "type": question_type, "mandatory": mandatory}

    def get_questionnaire_form(self, elems=None, **kwargs):
        return [elems if elems else self.get_questionnaire_form_elem(**kwargs)]

    def create_ow(self, **kwargs):
        return OnBoardingWorkflow.objects.create(**self.get_ow_data(**kwargs))

    def create_ot_questionnaire(self, ot, form_data):
        return VendorOnBoardingStageForm.objects.create(onboarding_template=ot, contents=form_data)

    def create_ot_questionnaire_response(self, questionnaire, vendor, keys_to_fill):
        data = {key_to_fill: self.factory.sentence() for key_to_fill in keys_to_fill}
        return VendorOnBoardingStageFormResponse.objects.create(form=questionnaire, vendor=vendor, data=data)

    def get_ot(self, *rtds, **kwargs):
        ot = OnBoardingStage(**self.get_ot_data(**kwargs))
        ot.save()
        if rtds:
            ot.requested_documents.add(*rtds)
            ot.save()
        return ot

    def get_rdt(self, **kwargs):
        return RequestedDocumentTemplate.objects.create(**self.get_requested_document_template(minimal=False, **kwargs))

    def create_rds(self, rdts, vendors):
        create_requested_documents([v.id for v in vendors], [rdt.id for rdt in rdts])

    def create_rd(self, rdt, vendor):
        return self.create_rds([rdt], [vendor])

    def expire_rd(self, rd):
        rd.expiration_date = date.today() - timedelta(days=2)
        rd.save()

    def get_rd(self, vendor, rdt=None):
        kwargs = dict(vendor=vendor)
        if rdt:
            kwargs['template'] = rdt
        return RequestedDocument.objects.filter(**kwargs).first()

    def apply_ot(self, ot, vendors, operation='set_all'):
        self.api_client.post('/api/onboarding/templates/bulk_set/', {operation: [ot.pk], 'vendors': [v.slug for v in vendors]})

    def unapply_ot(self, ot, vendors):
        self.apply_ot(ot, vendors, operation='unset_all')

    def upload_rd(self, rd):
        rd.file = generate_upload_path(RequestedDocument, self.factory)
        rd.save()

    def unupload_rd(self, rd):
        rd.file = None
        rd.save()

    def switch_verification(self, rdt, status=True):
        rdt.verification_required = status
        rdt.save()

    def enable_verification(self, rdt):
        self.switch_verification(rdt, True)

    def disable_verification(self, rdt):
        self.switch_verification(rdt, False)

    def remove_rdts_from_ot(self, ot):
        self.api_client.patch(f'/api/onboarding/templates/{ot.pk}/', {'requested_documents': []})

    def assert_rd_status(self, pk, status):
        self.assertEqual(RequestedDocument.objects.get(pk=pk).status_name, status)

    def has_workflow(self, workflow, vendor):
        return OnBoardingWorkflowsForVendor.objects.filter(workflow=workflow, vendor=vendor).exists()

    def has_stage(self, stage, vendor):
        return OnBoardingStagesForVendor.objects.filter(onboarding_template=stage, vendor=vendor).exists()

    def _get_ot_for_vendor(self, vendor):
        return {template.onboarding_template_id: template for template in OnBoardingStagesForVendor.objects.filter(vendor=vendor)}


@contextmanager
def temporarily_disconnect_signal(signal, receiver, sender=None, dispatch_uid=None):
    """
    Context manager to temporarily disconnect a Django signal receiver.
    Usage:
        with temporarily_disconnect_signal(post_save, my_handler, sender=MyModel):
            ...
    """
    signal.disconnect(receiver, sender=sender, dispatch_uid=dispatch_uid)
    try:
        yield
    finally:
        signal.connect(receiver, sender=sender, dispatch_uid=dispatch_uid)
