import copy
import datetime
import hashlib
import io
import itertools
import re
import uuid
from collections import OrderedDict


import bleach
import csv342 as csv
import requests
from bleach.sanitizer import ALLOWED_ATTRIBUTES
from django.conf import settings
from django.core.exceptions import ObjectDoesNotExist, ValidationError
from django.core.validators import MaxLengthValidator
from django.db import connection
from django.db.models import QuerySet
from django.utils.encoding import force_bytes, force_str, smart_str
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _
from requests import ConnectionError
from rest_framework import renderers, serializers
from rest_framework.fields import Field, empty

from clients import features
from shortlist.current_user import get_current_user
from shortlist.db_fields import MAX_PATH_LENGTH
from shortlist.fields import ProfanityFilterValidator
from shortlist.utils import (
    ANONYMOUS_SUPPORT,
    ANONYMOUS_VENDOR,
    HeaderedList,
    boto3_resource,
    convert_time_tz,
    validate_upload_path,
)
from shortlist.validators import EMAIL_VALIDATORS


class ListAnonymizer(serializers.ListSerializer):
    def update(self, instance, validated_data):
        raise NotImplementedError()

    def to_representation(self, data):
        iterable = super().to_representation(data)
        return [item for item in iterable if item is not None and item.get('slug')]


class Anonymizer(serializers.ModelSerializer):
    """
    1. Make sure vendors can only get information about their own vendor objects or users.
    Other vendors will be anonymous (single fields) or removed from lists (when many=True).
    This applies to serialization only, deserialization is unaffected.
    2. Make sure all non-staff users will see staff user (eg. in activity stream) as Shortlist Support.

    Inherit from this instead of from serializers.ModelSerializer (cannot make
    this a mixin because rest framework base classes for serializers don't call super properly).
    """

    _drop_email_feature = True
    _anonymize = True

    class Meta:
        list_serializer_class = ListAnonymizer

    def to_representation(self, obj):
        if not self._anonymize:
            return super().to_representation(obj)

        if obj is not None:
            current_user = get_current_user()
            if getattr(obj, 'is_staff', False) and not getattr(current_user, 'is_staff', False):
                return ANONYMOUS_SUPPORT.to_representation()
            elif current_user is not None and getattr(current_user, 'is_supplier', False):
                if (
                    current_user.vendor != obj
                    and (getattr(obj, 'vendor', False) and current_user.vendor != obj.vendor.parent)
                    and getattr(obj, 'email', None) != current_user.email
                ):
                    return ANONYMOUS_VENDOR.to_representation()
            elif current_user is not None and getattr(current_user, 'is_vendor', False):
                if current_user.vendor != obj:
                    email = getattr(obj, 'email', None)
                    if email is not None:
                        if (
                            getattr(self, '_show_group_managers', False)
                            and current_user.vendor.shared_groups_access
                            and obj.id in current_user.vendor.vendor_type.shared_group_managers_list
                        ):
                            # Allow view Vendor data If requesting user have access to SHARED GROUPS and
                            # Vendor is manager at any group shared with his VendorType
                            pass
                        elif email != current_user.email and (getattr(obj, 'is_vendor', False) or hasattr(obj, 'is_company')):
                            return ANONYMOUS_VENDOR.to_representation()
            result = super().to_representation(obj)
            if (
                self._drop_email_feature
                and connection.tenant.has_features(features.BUYER_EMAILS_HIDDEN)
                and not getattr(current_user, 'is_staff', False)
                and getattr(obj, 'is_buyer', False)
            ):
                result['email'] = ''
            return result


class EmptyFieldsCleanerMixin:
    clean_fields = []

    @staticmethod
    def _clean_up(data, fields):
        return {key: value for key, value in data.items() if key not in fields or value != ''}

    def to_internal_value(self, data):
        return super().to_internal_value(self._clean_up(data, self.clean_fields))


class DynamicSerializerFieldsMixin:
    def __init__(self, *args, **kwargs):
        fields = kwargs.pop('fields', None)
        exclude = kwargs.pop('exclude', ())
        super().__init__(*args, **kwargs)
        if fields:
            allowed = set(fields)
            existing = set(self.fields.keys())
            exclude = (existing.difference(allowed)).union(exclude)
        for field_name in exclude:
            try:
                self.fields.pop(field_name)
            except KeyError:
                raise KeyError(f"{self.__class__} does not have a field named {field_name}")


def limit_fields(serializer_class, fields):
    """
    Take a serializer class and construct another serializer class based on the
    original one with fields limited to the ones passed in the second argument

    This is similar to DynamicSerializerFieldsMixin, but it's easier to use in views
    as you don't need to modify the serializers and it can work with paginated API
    views as well as non-paginated ones.

    Example (in your view):

    ```
    def get_serializer_class(self):
        # Limit fields returned by the serializer to only vendor and context
        return limit_fields(self.serializer_class, ['vendor', 'context'])
    ```
    """

    class LimitedFieldsSerializer(serializer_class):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            if fields:
                allowed = set(fields)
                existing = set(self.fields.keys())
                exclude = existing.difference(allowed)
                for field_name in exclude:
                    try:
                        self.fields.pop(field_name)
                    except KeyError:
                        raise KeyError(f'limit_fields: {serializer_class} does not have a field named {field_name}')

    return LimitedFieldsSerializer


class FromSlugMixin:
    slug_queryset = None
    slug_field = 'slug'

    def get_value(self, dictionary):
        if self.read_only:
            return empty
        value = super().get_value(dictionary)
        if value is empty:
            if self.default is not None and self.default is not empty and not self.partial:
                # Note: partial updates shouldn't set defaults
                value = copy.deepcopy(self.default)
            else:
                if self.required:
                    raise ValidationError(self.error_messages['required'])

        if value in (None, '', empty):
            return None
        return value

    def to_internal_value(self, data):
        if self.slug_queryset is not None and isinstance(data, ((str,), int)):
            try:
                return self.slug_queryset.get(**{self.slug_field: data})
            except ObjectDoesNotExist:
                raise ValidationError(_("Object with %s=%s does not exist.") % (self.slug_field, smart_str(data)))
            except (TypeError, ValueError):
                raise ValidationError(_('Invalid value.'))
        return super().to_internal_value(data)


class ActorFromEmailOrSlugMixin(FromSlugMixin):
    """
    Allow to pass list of objects like:
    [{'slug': 'user-slug'}, {'email': '<EMAIL>'}, {'slug': 'another-user-slug'}]
    If there's email property passed - it will return Actor for it.
    Later it check for a slug and search through all users (buyers, admins, vendors).

    Possible updates - add parameters to search (like `exclude_vendors`) or search for email value in all users before
    we return Actor with TYPE_EXTERNAL_EMAIL
    """

    def to_internal_value(self, data):
        if isinstance(data, dict):
            if data.get('email'):
                email = data.get('email')
                for validator in EMAIL_VALIDATORS:
                    validator(email)
                from users.models import Actor

                return Actor.objects.get_or_create(
                    external=email, defaults={'actor_type': Actor.TYPE_EXTERNAL_EMAIL, 'external_name': data.get('name', None) or email}
                )[0]
            elif self.slug_queryset and data.get('slug'):
                data = data.get('slug')
                # we don't search directly over Actors because not all User objects have related Actor object
                result = super().to_internal_value(data)
                return result.actor
        raise ValidationError(_('Invalid value.'))


class ConstSerializer(serializers.ReadOnlyField):
    """A field that always returns the same value."""

    def __init__(self, value):
        self.value = value
        super().__init__()

    def to_representation(self, value):
        return self.value

    def get_attribute(self, obj):
        return self.value


class LocalizedTimeField(serializers.ReadOnlyField):
    """A field that returns time value converted to user's timezone and formatted using their time settings"""

    DEFAULT_TIME_FORMAT = "%H:%M"

    def get_attribute(self, instance):
        data = super().get_attribute(instance)

        if data or data == datetime.time(0, 0, 0):  # workaround, this issue got fixed in Python 3.5 (https://bugs.python.org/issue13936)
            time = convert_time_tz(data)
            user = get_current_user()
            return time.strftime(self.DEFAULT_TIME_FORMAT if user is None else user.python_time_format)

    def to_internal_value(self, data):
        if data or data == datetime.time(0, 0, 0):  # workaround, this issue got fixed in Python 3.5 (https://bugs.python.org/issue13936)
            time = convert_time_tz(data)
            user = get_current_user()
            return time.strftime(self.DEFAULT_TIME_FORMAT if user is None else user.python_time_format)
        else:
            return None


class FilepathSerializer(serializers.Field):
    """Serializer for file uploads done by Filepicker into our S3 buckets."""

    use_files = False
    write_only = True  # doesn't work with rest framework 2.3.9 unfortunately

    default_error_messages = {
        'required': 'File is required.',
        'invalid': 'Invalid upload.',
    }

    def __init__(self, path_prefix=None, check_if_exists=True, allow_updates=False, **kwargs):
        super().__init__(**kwargs)
        self.path_prefix = path_prefix
        self.check_if_exists = check_if_exists
        self.allow_updates = allow_updates
        self.validators.append(MaxLengthValidator(MAX_PATH_LENGTH))

    def get_value(self, dictionary):
        if getattr(self.root, 'partial', False) and not self.allow_updates:
            return
        return super().get_value(dictionary)

    def to_internal_value(self, value):
        if value is not None and not isinstance(value, str):
            self.fail('invalid')
        if value or self.required or self.allow_updates:
            if self.path_prefix:
                if callable(self.path_prefix):
                    if self.parent.instance is None:
                        prefix = self.path_prefix(None, serializer=self.parent)
                    else:
                        prefix = self.path_prefix(self.parent.instance)
                else:
                    prefix = self.path_prefix
                if not validate_upload_path(prefix, value):
                    self.fail('invalid')
        return value

    def to_representation(self, value):
        return value.name if value else None


class DictSlugRelatedField(serializers.RelatedField):
    """
    Serialization will return dict of scalar values from the model, limited on attributes list.
    Deserialization will load data from string or {slug_field: string} and match it to a unique field on the target.
    Similar to SlugRelatedField from rest framework.
    """

    read_only = False

    default_error_messages = {
        'does_not_exist': _("Object with %s=%s does not exist."),
        'invalid': _('Invalid value.'),
    }

    def __init__(self, attributes, *args, **kwargs):
        self.slug_field = kwargs.pop('slug_field', 'slug')
        self.read_attributes = attributes
        super().__init__(*args, **kwargs)

    def to_internal_value(self, data):
        if self.queryset is None:
            raise Exception('Writable related fields must include a `queryset` argument')

        if isinstance(data, dict):
            data = data.get(self.slug_field)
        try:
            return self.queryset.get(**{self.slug_field: data})
        except ObjectDoesNotExist:
            raise ValidationError(self.error_messages['does_not_exist'] % (self.slug_field, smart_str(data)))
        except (TypeError, ValueError):
            msg = self.error_messages['invalid']
            raise ValidationError(msg)

    def to_representation(self, value, pk=False):
        if pk:
            return value.id  # when pk is true return a string or string-convertible value
        return {attr: getattr(value, attr) for attr in self.read_attributes}

    def get_choices(self, cutoff=None):
        queryset = self.get_queryset()
        if queryset is None:
            # Ensure that field.choices returns something sensible
            # even when accessed with a read-only field.
            return {}

        if cutoff is not None:
            queryset = queryset[:cutoff]
        return OrderedDict([(self.to_representation(item, pk=True), self.display_value(item)) for item in queryset])


class FlatteningSerializerMixin:
    _fields_to_flatten = ()

    def to_internal_value(self, data):
        if data:
            for field in self._fields_to_flatten:
                d = {}
                for name in self.fields[field].fields.keys():
                    if name in data:
                        d[name] = data.pop(name)
                data[field] = d
        result = super().to_internal_value(data)
        return result

    def to_representation(self, obj):
        result = super().to_representation(obj)
        for field in self._fields_to_flatten:
            nested = result.pop(field, None) or {}
            for key, value in nested.items():
                result[key] = value
        return result

    def is_valid(self):
        result = super().is_valid()
        if not result:
            # flatten errors
            for field in self._fields_to_flatten:
                nested = self.errors.pop(field, None) or ()
                self.errors.update(nested)
        return result


class WritableSerializerMethodField(Field):
    """Field that reads like SerializerMethodField, but allows setting the value using 'source' too"""

    def __init__(self, read_method, **kwargs):
        self.read_method = read_method
        super().__init__(**kwargs)

    def to_internal_value(self, data):
        return data

    def get_attribute(self, instance):
        value = getattr(self.parent, self.read_method)(instance)
        return self.to_representation(value)

    def to_representation(self, value):
        return value


# Renderers


class PlainTextRenderer(renderers.BaseRenderer):
    media_type = 'text/plain'
    format = 'txt'
    charset = 'utf-8'

    def render(self, data, media_type=None, renderer_context=None):
        if isinstance(data, list):
            data = "\n".join([str(x) for x in data])
        return str(data).encode(self.charset)


SUSPICIOUS_CSV_VALUE = re.compile(r'^[=+\-@].*[^\d]')


def quote_csv_injection(value):
    value = force_str(value).strip()
    if value and SUSPICIOUS_CSV_VALUE.match(value):
        return "'" + value
    return value


class CSVRenderer(renderers.BaseRenderer):
    """
    Rest framework's built in CSV Renderer does a lot of unnecessary magic.
    Let's keep it simple.
    Renderer which serializes to CSV
    """

    media_type = 'text/csv'
    format = 'csv'

    def render(self, data, media_type=None, renderer_context=None):
        """
        Renders serialized *data* into CSV. For a dictionary:
        """
        if not data:
            return b''

        if isinstance(data, dict):
            data = [data]
        if not isinstance(data, HeaderedList):
            try:
                data = HeaderedList(data, sorted(data[0].keys()))
            except Exception:
                # not a list of dicts - might be an error message
                return str(data)

        csv_buffer = io.StringIO()
        header_fields = list(data.header.keys()) if isinstance(data.header, dict) else data.header
        header_row = data.header if isinstance(data.header, dict) else dict(zip(data.header, data.header, strict=False))
        csv_writer = csv.DictWriter(csv_buffer, header_fields, extrasaction='ignore')

        for row in itertools.chain([header_row], data):
            # Assume that strings should be encoded as UTF-8
            csv_writer.writerow({key: self.quote_value(value) for key, value in row.items()})

        return csv_buffer.getvalue()

    def quote_value(self, value):
        if isinstance(value, (list, tuple)):
            return ",".join(self.quote_value(x) for x in value)
        elif value is None:
            return ""

        return quote_csv_injection(value)


class SemicolonArrayField(serializers.Field):
    def to_representation(self, value):
        if value:
            return ";".join(value)
        return ""


class NestedSerializerMixin:
    def get_instance_from_parent(self):
        return getattr(self.parent.instance, self.source, None)

    def get_instance(self, data: dict | None = None):
        if self.instance is not None:
            return self.instance
        return self.get_instance_from_parent() if self.parent else None


class BlankDecimalField(serializers.DecimalField):
    def to_internal_value(self, data):
        if data == '':
            return None
        return super().to_internal_value(data)


class DecimalStringField(serializers.DecimalField):
    def __init__(self, **kwargs):
        super().__init__(coerce_to_string=True, **kwargs)

    def to_internal_value(self, data):
        if not isinstance(data, str) and not isinstance(data, str):
            raise ValidationError("Not a valid string.")
        return super().to_internal_value(data)


class ProfanityFilterSerializerMixin:
    profanity_filter_field_names = []
    profanity_filter_field_types = (serializers.CharField, serializers.ListField, serializers.DictField)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.profanity_filter_field_names:
            fields = [field for field_name, field in self.fields.items() if field_name in self.profanity_filter_field_names]
        elif self.profanity_filter_field_types:
            fields = [field for field_name, field in self.fields.items() if isinstance(field, self.profanity_filter_field_types)]

        for field in fields:
            field.validators.append(ProfanityFilterValidator())


class UrlToS3FileField(FilepathSerializer):
    URL_REGEXP = re.compile(r"^((http|https)\:\/\/)[a-zA-Z0-9\.\/\?\:@\-_=#]+\.([a-zA-Z]){2,6}([a-zA-Z0-9\.\&\/\?\:@\-_=#])*$")

    @cached_property
    def s3_bucket(self):
        s3 = boto3_resource('s3')
        return s3.Bucket(settings.AWS_STORAGE_BUCKET_NAME)

    def to_internal_value(self, data):
        if isinstance(data, str) and self.URL_REGEXP.match(data):
            try:
                response = requests.get(data)
            except ConnectionError:
                raise ValidationError(f"Could not download file from URL {data}")

            if response.status_code != 200:
                raise ValidationError(f"Could not download file from URL {data}")

            upload_path = f"{self.path_prefix}{self._get_file_name(response, data)}"
            extras = {}
            if "Content-Type" in response.headers:
                extras['ContentType'] = response.headers["Content-Type"]
            self.s3_bucket.put_object(Body=response.content, Key=upload_path, **extras)

            data = upload_path
        return super().to_internal_value(data)

    def _get_file_name(self, response, url):
        if "Content-Disposition" in response.headers:
            name = re.findall("filename=(.+)", response.headers["Content-Disposition"])[0]
        else:
            name = url.split("/")[-1]

        hash_prefix = hashlib.md5(force_bytes(uuid.uuid4())).hexdigest()[:20]
        return f"{hash_prefix}_{name}"


class HtmlContentField(serializers.CharField):
    allowed_tags = ["a", "b", "strong", "i", "em", "u", "ul", "ol", "li", "br", "p", "span"]
    allowed_attributes = {**ALLOWED_ATTRIBUTES, **{"li": ["data-list"]}}

    def to_internal_value(self, data):
        return bleach.clean(super().to_internal_value(data), tags=self.allowed_tags, attributes=self.allowed_attributes)


class UpdatableModelListSerializer(serializers.ListSerializer):
    ordered = False

    def create(self, validated_data):
        return [self.child.create(dict(**attrs, order=order) if self.ordered else attrs) for order, attrs in enumerate(validated_data)]

    def update(self, instance, validated_data):
        items = {item.id: item for item in instance}

        updated_items = {}
        for order, item_data in enumerate(validated_data):
            item_id = item_data.pop('id', None)
            if item_id is None:
                item = self.child.create(dict(**item_data, order=order) if self.ordered else item_data)
                updated_items[item.id] = item
            else:
                item = items.get(item_id)
                if item is None:
                    raise serializers.ValidationError(f"Invalid pk {item_id} - {self.child.Meta.model.__name__} does not exist")

                updated_items[item.id] = self.child.update(item, dict(**item_data, order=order) if self.ordered else item_data)

        for item_id, item in items.items():
            if item_id not in updated_items:
                item.delete()

        return updated_items


class PatchReadOnlyModelSerializer(serializers.ModelSerializer):
    class Meta:
        patch_read_only_fields = []

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        is_many = isinstance(self.instance, (list, tuple, QuerySet))
        if not is_many:
            self.__set_patch_read_only_fields()

    def __set_patch_read_only_fields(self):
        if self.instance and self.instance.pk:
            for field_name, field in self.fields.items():
                if field_name in self.Meta.patch_read_only_fields:
                    field.read_only = True
