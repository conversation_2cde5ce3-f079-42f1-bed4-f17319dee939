import datetime
import os
import os.path
import shlex
import shutil
import subprocess
import tempfile
from datetime import timedelta
from decimal import Decimal

from celery.utils.log import get_task_logger
from django.conf import settings
from django.contrib.auth.hashers import UNUSABLE_PASSWORD_PREFIX
from django.contrib.auth.tokens import default_token_generator
from django.core.serializers.json import Serializer
from django.db import connection
from django.db.models import Q
from django.utils.encoding import smart_str, force_bytes
from django.utils.timezone import now

from clients import features
from clients.models import UserEmail
from events.event_types import SendSecureLinkRequest, UserPasswordExpiring
from preferences.models import ProfanityFilterLog
from shortlist.celery import app, TenantPeriodicTask
from users.models import User
from .models import SecureLinkRequestLog
from .utils import save_prepared_file_from_disk_to_s3, boto3_resource

REQUEST_TIME_LIMIT = timedelta(minutes=5)


@app.task
def oom_debug_task():
    import time

    store = []
    multiply = 250000
    max_iterations = 5000
    i = 0

    while i < max_iterations:
        store.append("text" * multiply)
        time.sleep(0.1)
        i = i + 1

    return len(store), i


@app.task
def send_secure_links(email, ip_address, redirect_to=None):
    from users.models import User
    from vendors.models import Vendor

    email = email.lower()
    tenant = connection.tenant
    if tenant is not None and tenant.public:
        tenant = None
    connection.set_schema_to_public()

    try:
        previous = SecureLinkRequestLog.objects.filter(
            tenant=tenant, request_processed=True, email=email, request_at__gt=now() - REQUEST_TIME_LIMIT
        ).exists()

        if previous:
            SecureLinkRequestLog.objects.create(
                tenant=tenant, tenants_matched=None, request_processed=False, email=email, ip_address=ip_address
            )
            return

        if tenant is None or tenant.public:
            # look for user in all tenants where we have that email
            user_email = UserEmail.objects.filter(address=email).first()
            if user_email is None:
                tenants = ()
            else:
                tenants = list(user_email.tenants.all())
        else:
            # check current tenant only
            tenants = [tenant]
        login_links = {}  # tenant name: login link
        for tenant in tenants:
            # filter out tenants without features.SECURE_LOGIN_LINK
            if tenant.has_features(features.SECURE_LOGIN_LINK):
                connection.set_tenant(tenant)
                user = (
                    User.objects.filter(email=email, deleted=False, secure_login_links=True)
                    .exclude(vendor__isnull=False, vendor__archived=True)
                    .first()
                )
                if user is not None and (user.vendor is None or user.vendor.status != Vendor.STATUS_NOT_INVITED):
                    get_access_url_kwargs = {'redirect_to': redirect_to} if redirect_to else {}
                    login_links[tenant] = user.get_access_url(**get_access_url_kwargs)
        # log attempt, including number of matched users
        connection.set_schema_to_public()

        SecureLinkRequestLog.objects.create(
            tenant=tenant, tenants_matched=len(login_links), request_processed=True, email=email, ip_address=ip_address
        )

        if login_links:
            # only send email if accounts found
            if len(login_links) == 1:
                connection.set_tenant(next(iter(login_links.keys())))  # branding, etc, for single tenant match
            login_links = sorted((tenant.name, link) for (tenant, link) in login_links.items())
            SendSecureLinkRequest(email=email, login_links=login_links)

    finally:
        if tenant is None:
            connection.set_schema_to_public()
        else:
            connection.set_tenant(tenant)


@app.task(queue=settings.QUEUE_BATCH)
def perform_bulk_import(app_label, model_name, obj_id, simulate):
    from django.apps import apps

    bulk_import = apps.get_model(app_label, model_name).objects.get(id=obj_id)
    bulk_import.run(simulate)


PROTECTED_TYPES = (int,) + (type(None), float, Decimal, datetime.datetime, datetime.date, datetime.time, list, dict)


class SisenseSerializer(Serializer):
    def __init__(self, domain_url):
        self.domain_url = domain_url

    def get_dump_object(self, obj):
        result = dict(self._current)
        result.update(
            {
                "pk": smart_str(obj._get_pk_val(), strings_only=True),
                "model": smart_str(obj._meta),
                "tenant": self.domain_url,
            }
        )
        return result

    def handle_field(self, obj, field):
        value = field.value_from_object(obj)
        # Protected types (i.e., primitives like None, numbers, dates,
        # and Decimals) are passed through as is. All other values are
        # converted to string first.
        if isinstance(value, PROTECTED_TYPES):
            self._current[field.name] = value
        else:
            self._current[field.name] = field.value_to_string(obj)


IGNORE_APPS = {
    'auth',
    'clients',
    'contenttypes',
    'sessions',
    'shared',
    'preferences',
    'apis',
    'oauth2_provider',
    'admin',
    'events',
    'shortlist',
    'tenant_creation',
}

IGNORE_TABLES = {
    'users_passwordhistory',
    'users_onetimetoken',
    'users_logintoken',
    'vendors_vendorbulkimport',
    'vendors_vendorbulkmessage',
    'tasks_taskbulkimport',
    'tasks_taskbulkimportconfig',
    'tasks_taskbulkimportmessage',
    'integrations_hellosignoverride',
    'integrations_saml',
    'vendors_vendorbankdetails',
}


def get_export_queries():
    from django.apps import apps

    schema_name = connection.tenant.schema_name
    domain_url = connection.tenant.domain_url

    done = set()

    for model in apps.get_models():
        # Do something with your model here
        db_table = model._meta.db_table
        app = model._meta.app_label
        if app in IGNORE_APPS or db_table in IGNORE_TABLES or db_table in done:
            continue
        done.add(db_table)
        full_table_name = f"{schema_name}.{model._meta.db_table}"
        yield db_table + '.csv', f"SELECT '{domain_url}' as tenant, * FROM {full_table_name}"

    yield 'clients.csv', f"SELECT '{domain_url}' as tenant, * FROM public.clients_client WHERE id={connection.tenant.id}"

    # TODO: vendor custom fields


@app.task(queue=settings.QUEUE_BATCH)
def csv_export(directory, save_to_s3=False, delete_after=False):
    from shortlist.management.postgres_common import pg_run_process

    db_name = 'slave' if 'slave' in settings.DATABASES else 'default'

    domain_url = connection.tenant.domain_url
    tenant_dir = os.path.join(directory, domain_url)

    print(f"Exporting tenant {domain_url} to {tenant_dir}")
    if os.path.exists(tenant_dir):
        shutil.rmtree(tenant_dir, ignore_errors=False)
    os.makedirs(tenant_dir)

    try:
        files = {}

        for filename, query in sorted(get_export_queries()):
            filepath = os.path.join(tenant_dir, filename)
            files[filename] = filepath
            print(f"  Saving data to {filepath}")

            with open(filepath, "wb") as f:
                if isinstance(query, str):
                    psql_command = f"\\copy ({query}) TO STDOUT WITH CSV HEADER"
                    pg_run_process(settings.DATABASES[db_name]['NAME'], ["psql", "-c", psql_command], stdout=f)

        if save_to_s3:
            print("  Copying files to S3 bucket")
            s3 = boto3_resource('s3')
            bucket = s3.Bucket(settings.AWS_BUCKET_SISENSE)
            for filename, filepath in sorted(files.items()):
                bucket.upload_file(Filename=filepath, Key=f"current/{domain_url}/{filename}")

    finally:
        if delete_after:
            print("  Deleting temporary directory")
            shutil.rmtree(tenant_dir, ignore_errors=True)


@app.task(queue=settings.QUEUE_BATCH)
def prepare_zip_with_downloads(zip_parts):
    tmpdir_prefix = f"prepare_zip_with_downloads-{connection.tenant.domain_prefix}-"
    tmpdir = tempfile.mkdtemp(prefix=tmpdir_prefix)
    zipdir = os.path.join(tmpdir, "to_zip")
    zipfile = os.path.join(tmpdir, "archive.zip")
    os.makedirs(zipdir)

    # Materialize the generated parts (the CSV file)
    for part in zip_parts:
        if part.bytes:
            with open(os.path.join(zipdir, part.file_name), "wb") as f:
                f.write(part.bytes)

    # Now, download the remaining files in parallel using curl and xargs
    xargs_input = "\n".join(f"--output {shlex.quote(part.file_name)} {shlex.quote(part.url)}" for part in zip_parts if part.url)
    sp = subprocess.Popen(
        ["xargs", "-L1", "-P8", "curl", "--silent", "--create-dirs", "--fail"],
        cwd=zipdir,
        stdin=subprocess.PIPE,
    )
    sp.communicate(force_bytes(xargs_input))
    sp.wait()

    # Set the access and modification times
    for part in zip_parts:
        if part.timestamp:
            unix_time = int(part.timestamp.strftime("%s"))
            try:
                os.utime(
                    os.path.join(zipdir, part.file_name),
                    (unix_time, unix_time),
                )
            except OSError:
                pass

    # Put all files to a single ZIP archive
    # -0 means the files are not compressed, so the operation is relatively fast
    # Omitting -0 would result in only a ~10-15% smaller download
    sp = subprocess.call(["zip", "-0", "-r", shlex.quote(zipfile), "."], cwd=zipdir)
    shutil.rmtree(zipdir, ignore_errors=True)

    save_prepared_file_from_disk_to_s3(zipfile)
    shutil.rmtree(tmpdir, ignore_errors=True)


class TrimProfanityFilterLogEntries(TenantPeriodicTask):
    def tenant_task(self):
        logger = get_task_logger(self.__class__.__name__)
        tenant = connection.tenant
        if tenant.has_features(features.PROFANITY_FILTER):
            filter_date = datetime.date.today() - timedelta(days=90)
            logger.info(f"[tenant: {tenant.name}] trimming profanity filter log to last 90 days, deleting entries older than {filter_date}")
            ProfanityFilterLog.objects.filter(created_at__date__lt=filter_date).delete()


class RemindUsersAboutExpiringPasswords(TenantPeriodicTask):
    def tenant_task(self):
        logger = get_task_logger(self.__class__.__name__)
        tenant = connection.tenant
        security_settings = tenant.security_settings
        if security_settings.password_rotation_period > 5:
            logger.info(f"[tenant: {tenant.name}] reminding users about expiring passwords in the 5 days.")

            rotation_period_with_offset = security_settings.password_rotation_period - 5
            notification_date = now().date() - timedelta(days=rotation_period_with_offset)

            # Where the last password change was exactly rotation_period_with_offset days ago
            # Note: `Q(password__startswith=UNUSABLE_PASSWORD_PREFIX) | Q(password__isnull=True)` <=> `not user.has_usable_password()`
            # it is done due to better performance by DB query instead relying on model method
            users = User.objects.filter(deleted=False, password_last_changed_at__date=notification_date).exclude(
                Q(vendor__archived=True) | Q(password__startswith=UNUSABLE_PASSWORD_PREFIX) | Q(password__isnull=True)
            )

            for user in users:
                password_reset_url = (
                    f"{connection.tenant.root_url}password_reset/{user.id}/?token={default_token_generator.make_token(user)}"
                )
                UserPasswordExpiring(user=user, period=security_settings.password_rotation_period, password_reset_url=password_reset_url)


class InvalidateExpiredUserPasswords(TenantPeriodicTask):
    def tenant_task(self):
        logger = get_task_logger(self.__class__.__name__)
        tenant = connection.tenant
        security_settings = tenant.security_settings
        if security_settings.password_rotation_period > 0:
            logger.info(f"[tenant: {tenant.name}] invalidate expired user passwords.")

            invalidation_date = now().date() - timedelta(days=security_settings.password_rotation_period)

            # Where the last password change was security_settings.password_rotation_period days ago or earlier
            # Note: `Q(password__startswith=UNUSABLE_PASSWORD_PREFIX) | Q(password__isnull=True)` <=> `not user.has_usable_password()`
            # it is done due to better performance by DB query instead relying on model method
            users = User.objects.filter(deleted=False, password_last_changed_at__date__lte=invalidation_date).exclude(
                Q(vendor__archived=True) | Q(password__startswith=UNUSABLE_PASSWORD_PREFIX) | Q(password__isnull=True)
            )

            for user in users:
                user.set_unusable_password()
                user.password_expired = True
                user.save()
                user.audit_model.objects.add_password_expired(user.email)


app.register_task(InvalidateExpiredUserPasswords())
app.register_task(RemindUsersAboutExpiringPasswords())
app.register_task(TrimProfanityFilterLogEntries())


DEBOUNCER_FUNCTION_MAP = {}


@app.task()
def debouncer_delayed_function(func_id, *args, **kwargs):
    """
    Execute the function after a delay.

    :param func_id: The identifier of the function to be executed.
    :param args: Arguments for the function.
    :param kwargs: Keyword arguments for the function.
    """
    func = DEBOUNCER_FUNCTION_MAP.get(func_id)
    if func:
        func(*args, **kwargs)
