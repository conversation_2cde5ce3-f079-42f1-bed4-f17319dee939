"""
WSGI config for pipeline project.

This module contains the WSGI application used by Django's development server
and any production WSGI deployments. It should expose a module-level variable
named ``application``. Django's ``runserver`` and ``runfcgi`` commands discover
this application via the ``WSGI_APPLICATION`` setting.

Usually you will have the standard Django WSGI application here, but it also
might make sense to replace the whole Django WSGI application with a custom one
that later delegates to the Django one. For example, you could introduce WSGI
middleware here, or combine a Django application with an application of another
framework.

"""

import os

from shortlist.signals_tracker import apply_tracker_to_signals
from shortlist.tenant import tenant_name

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shortlist.settings.local')


def before_send(event, hint):
    try:
        tags = event.setdefault('tags', {})
        tags['tenant_name'] = tenant_name()
    except Exception:
        pass

    return event


sentry_dsn = os.environ.get('SENTRY_DSN', None)
if sentry_dsn is not None:
    import sentry_sdk
    from sentry_sdk.integrations.django import DjangoIntegration

    sentry_sdk.init(
        dsn=sentry_dsn,
        release=os.environ.get('RELEASE_VERSION', None),
        integrations=[DjangoIntegration()],
        send_default_pii=True,
        traces_sample_rate=float(os.environ.get('SENTRY_TRACE_RATE', 0.0)),
        profiles_sample_rate=float(os.environ.get('SENTRY_PROFILE_RATE', 0.0)),
        before_send=before_send,
    )
    print("Sentry initialized")

# This application object is used by any WSGI server configured to use this
# file. This includes Django's development server, if the WSGI_APPLICATION
# setting points here.
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()

apply_tracker_to_signals()
