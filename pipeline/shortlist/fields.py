import requests
import sentry_sdk.api
from botocore.exceptions import ClientError

from django import forms
from django.conf import settings
from django.core import validators
from django.forms.widgets import SelectMultiple
from django.db import models, connection
from rest_framework.exceptions import ValidationError

from clients import features
from shortlist.current_user import get_current_user
from shortlist.utils import boto3_client, guess_mimetype


class MultipleAnyStringField(forms.Field):
    """
    Simple and silly field for proper django_filter validation of custom
    search key words.
    """

    widget = SelectMultiple

    def validate(self, values):
        # field is always valid for random value
        pass


def validate_aws_storage_path(paths, bucket=settings.AWS_STORAGE_BUCKET_NAME):
    def _inner(value):
        path = connection.tenant.storage_path(*paths)
        domain = settings.AWS_STORAGE_VALID_DOMAINS[settings.AWS_DEFAULT_REGION]
        correct_path = f'//{domain}/{bucket}/{path}'
        if not value.startswith(correct_path):
            raise validators.ValidationError(f'Wrong S3 path, it should start with {correct_path}, got {value} instead')

    return _inner


def validate_absolute_url(value):
    if value:
        try:
            value.encode('ascii')
        except Exception:
            raise validators.ValidationError('Url is not ASCII')
        if settings.CHECK_EXTERNAL_URLS:
            if value.startswith('//'):
                value = f'https:{value}'
            try:
                if requests.request('HEAD', value).status_code == 200:
                    return
            except requests.RequestException:
                pass
            raise validators.ValidationError('Url is not correct')


class ImageProfanityValidatorMixin:
    def __init__(self, bucket):
        self.bucket = bucket
        self.reasons = {}

    def __call__(self, image):
        raise NotImplementedError

    def _get_object_name(self, value):
        domain = settings.AWS_STORAGE_VALID_DOMAINS[settings.AWS_DEFAULT_REGION]
        bucket_path = f'//{domain}/{self.bucket}/'
        return value.replace(bucket_path, '')


class ImageConfidenceValidator(ImageProfanityValidatorMixin):
    def __call__(self, image):
        labels = self.get_labels(image)
        if labels and not self.validate_confidence(labels):
            self.reasons = {'confidence_labels': labels}
            raise ValidationError('This image contains inappropriate content.')

    def get_labels(self, value):
        client = boto3_client('rekognition')
        name = self._get_object_name(value)
        try:
            response = client.detect_moderation_labels(Image={'S3Object': {'Bucket': self.bucket, 'Name': name}})
        except ClientError as exc:
            sentry_sdk.capture_exception(exc)
            return []
        return response.get('ModerationLabels', [])

    @staticmethod
    def validate_confidence(labels):
        from preferences.models import ProfanityFilterModerationCategory, PROFANITY_FILTER_DEFAULT_THRESHOLD

        thresholds = ProfanityFilterModerationCategory.all_thresholds()
        for label in labels:
            if label['Confidence'] > thresholds.get(label['Name'], PROFANITY_FILTER_DEFAULT_THRESHOLD):
                return False

        return True


class ImageTextValidator(ImageProfanityValidatorMixin):
    def __call__(self, image):
        detected_text = self.detect_text(image)
        if detected_text:
            blocked_phrases = self.validate_text(detected_text)
            if blocked_phrases:
                self.reasons = {'blocked_phrases': blocked_phrases}
                raise ValidationError('This image contains inappropriate words or phrases.')

    def detect_text(self, image):
        client = boto3_client('rekognition')
        name = self._get_object_name(image)
        try:
            response = client.detect_text(Image={'S3Object': {'Bucket': self.bucket, 'Name': name}})
        except ClientError as exc:
            sentry_sdk.capture_exception(exc)
            return []
        return response.get('TextDetections', [])

    @staticmethod
    def validate_text(texts):
        from preferences.models import ProfanityFilterBlockList, PROFANITY_FILTER_DEFAULT_THRESHOLD

        joined_text = ' '.join(t['DetectedText'] for t in texts if t['Confidence'] >= PROFANITY_FILTER_DEFAULT_THRESHOLD)
        return ProfanityFilterBlockList.find_blocked_phrases(joined_text)


class ImageContentValidator:
    IMAGE_MIMETYPES = {
        'image/gif',
        'image/png',
        'image/jpeg',
    }

    VALIDATORS = [
        ImageConfidenceValidator,
        ImageTextValidator,
    ]

    def __init__(self, bucket=None):
        self._validators = [validator_class(bucket) for validator_class in self.VALIDATORS]

    def __call__(self, value):
        if not connection.tenant.has_features(features.PROFANITY_FILTER):
            return

        if guess_mimetype(value) in self.IMAGE_MIMETYPES:
            from preferences.tasks import store_event_in_profanity_filter_log

            for validator in self._validators:
                try:
                    validator(value)
                except ValidationError as exc:
                    content_blocked = True
                    raise exc
                else:
                    content_blocked = False
                finally:
                    user = get_current_user()
                    user_id = user.id if user else None
                    store_event_in_profanity_filter_log.delay(
                        user_id=user_id, object_type='image', object_content=value, reasons=validator.reasons, blocked=content_blocked
                    )


class ProfanityFilterValidator:
    default_message = 'Disallowed string in form field. Please update and try again.'

    def __init__(self, message=None):
        self.message = message or self.default_message

    def __call__(self, value):
        if connection.tenant.has_features(features.PROFANITY_FILTER):
            if isinstance(value, list):
                errors = []
                for list_element in value:
                    current_element_errors = []
                    if not self.is_valid(list_element):
                        current_element_errors.append(self.message)

                    errors.append(current_element_errors)

                if any(errors):
                    raise ValidationError(errors)

            elif isinstance(value, dict):
                errors = {}
                for dict_key, dict_value in value.items():
                    if not self.is_valid(dict_value):
                        errors[dict_key] = [self.message]
                if errors:
                    raise ValidationError(errors)

            elif not self.is_valid(value):
                raise ValidationError(self.message)

    def is_valid(self, value):
        from preferences.models import ProfanityFilterBlockList

        if isinstance(value, dict):
            for dict_key, dict_value in value.items():
                if not self.is_valid(dict_value):
                    return False

        elif isinstance(value, str):
            blocked_phrases = ProfanityFilterBlockList.find_blocked_phrases(value)
            if blocked_phrases:
                ProfanityFilterBlockList.store_profanity_filter_log_event(value, {'blocked_phrases': blocked_phrases})
                return False
        return True


def validate_startswith(startswith):
    def _inner(value):
        if not value.startswith(startswith):
            raise validators.ValidationError(f'value should starts with {startswith}')

    return _inner


class AbsoluteURLField(models.CharField):
    default_validators = [validate_absolute_url]
