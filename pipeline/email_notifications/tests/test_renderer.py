from django.test import SimpleTestCase

from email_notifications.renderer import DO_NOT_RENDER, Template<PERSON><PERSON><PERSON>
from shortlist.mail import sort_dict_by_longest_key

EXAMPLE_CONTENT = "{% if test_var == 'foo' %}test_var equals foo{% else %}test_var does not equal foo{% endif %}"


class TestRenderer(SimpleTestCase):
    def test_render_notification(self):
        properties = {
            "subject": "Login [link] reset - :business_name's Worksuite",
            "button_1_text": "New Login [Link]",
            "secondary_content": """<p>Or paste the [Link] below into the navigation field in your [browser]:</p>
                <p>:login_link<p> :login_link_button""",
            "primary_content": "Bar",
        }

        # Initialize the TemplateRenderer with the properties
        renderer = TemplateRenderer(properties)

        # Test rendering subject
        self.assertEqual("Login [link] reset - :business_name's Worksuite", renderer.render_property("subject"))
        self.assertEqual(
            "Login hiperlink reset - Foo's Worksuite",
            renderer.render_template_with_substitutions(
                {':business_name': 'Foo', '[link]': 'hiperlink'}, renderer.render_property("subject")
            ),
        )

        # Provide context data to replace placeholders
        html = renderer.render()

        self.assertIn("<div>Bar</div>", html)
        self.assertIn("New Login [Link]", html)
        self.assertIn("<p>:login_link<p>", html)
        self.assertIn("<div><p>Or paste the [Link] below into the navigation field in your [browser]:</p>", html)

        context_data = {
            ':login_link': 'https://example.com',
            ':login_link_button': '<a href="https://example.com">example.com</a>',
            '[browser]': 'Chrome',
            '[Link]': 'HIPERLINK',
        }
        context_data = sort_dict_by_longest_key(context_data)
        html = renderer.render_template_with_substitutions(context_data, html, no_escape={':login_link_button'})

        # Test that the rendered HTML contains the expected output
        self.assertIn("<div>Bar</div>", html)  # Check for `primary_content`
        self.assertIn("New Login HIPERLINK", html)  # Update assertion to match rendered output
        self.assertIn("<p>https://example.com<p>", html)  # Check for `:login_link` replacement
        self.assertIn('<a href="https://example.com">example.com</a>', html)  # Check for `:login_link_button` replacement
        self.assertIn("<div><p>Or paste the HIPERLINK below into the navigation field in your Chrome:</p>", html)

    def test_render_notification_with_handlebars_without_providing_needed_variable(self):
        renderer = TemplateRenderer(properties={"secondary_content": EXAMPLE_CONTENT})

        html = renderer.render()
        self.assertIn(f"<div>{EXAMPLE_CONTENT}</div>", html)

    def test_render_notification_with_handlebars(self):
        renderer = TemplateRenderer(properties={"secondary_content": EXAMPLE_CONTENT})

        html = renderer.render(context_data={":test_var": "foo"})
        self.assertIn("<div>test_var equals foo</div>", html)

        html = renderer.render(context_data={":test_var": "bar"})
        self.assertIn("<div>test_var does not equal foo</div>", html)

    def test_do_not_render_selected_properties(self):
        renderer = TemplateRenderer(properties={"button_1_text": DO_NOT_RENDER})

        html = renderer.render(context_data={":test_var": "foo"})
        self.assertNotIn("New Login Link", html)
        self.assertNotIn("object at 0x", html)  # this is how the DO_NOT_RENDER object could be rendered as a string

    def test_render_template_with_substitutions(self):
        context_data = {
            '!some_text': 'lorem ipsum',
            '!some_html': '<b>test</b><script>alert("test")</script>',
            '!some_list': ['one', 'two', 'three'],
            '!some_dict': {'a': 'one', 'b': 'two', 'c': 'three'},
        }
        html_text = (
            "<p>{{ !some_text }}</p>"
            "{{ !some_html }}"
            "<ul>"
            "{% for item in !some_list %}"
            "<li>{{ item }}</li>"
            "{% endfor %}"
            "</ul>"
            "<ul>"
            "{% for key, value in !some_dict.items %}"
            "<li>{{ key }}: {{ value }}</li>"
            "{% endfor %}"
            "</ul>"
        )
        result = TemplateRenderer.render_template_with_substitutions(context_data, html_text)
        self.assertEqual(
            result,
            (
                '<p>lorem ipsum</p>'
                '&lt;b&gt;test&lt;/b&gt;&lt;script&gt;alert(&quot;test&quot;)&lt;/script&gt;'
                '<ul><li>one</li><li>two</li><li>three</li></ul>'
                '<ul><li>a: one</li><li>b: two</li><li>c: three</li></ul>'
            ),
        )
