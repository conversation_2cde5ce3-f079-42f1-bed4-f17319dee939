from django.core.exceptions import ObjectDoesNotExist
from rest_framework import serializers

from projects.mixins import UnreadActivitiesMixin
from projects.models import ProjectVendors
from projects.serializers.project_files_serializers import OffersFilesSerializer, ProjectFileForVendorSerializer
from reviews.serializers import VendorScoreSerializer
from vendors.serializers import VendorSerializer
from users.serializers import SimpleUserSerializer, MinimalUserSeralizer
from shortlist.serializers import DynamicSerializerFieldsMixin, ConstSerializer, BlankDecimalField
from tasks.serializers import TaskMinimalSerializer


class ProjectVendorSerializer(serializers.ModelSerializer):
    project_vendor_id = serializers.ReadOnlyField(source='id')
    id = serializers.ReadOnlyField(source='vendor.id')
    email = serializers.ReadOnlyField(source='vendor.email')
    name = serializers.ReadOnlyField(source='vendor.name')
    slug = serializers.ReadOnlyField(source='vendor.slug')
    status = serializers.ReadOnlyField()
    proposal_files = OffersFilesSerializer(source='files', many=True, required=False)

    class Meta:
        model = ProjectVendors
        fields = (
            'project_vendor_id',
            'id',
            'email',
            'name',
            'slug',
            'seen',
            'accepted',
            'status',
            'proposal_amount',
            'proposal_at',
            'proposal_comment',
            'awarded',
            'proposal_files',
        )
        read_only_fields = ('proposal_at',)


class PartialProjectForVendorSerializer(serializers.ModelSerializer):
    archived = serializers.ReadOnlyField(source='project.archived')
    slug = serializers.ReadOnlyField(source='project.slug')
    name = serializers.ReadOnlyField(source='project.name')
    id = serializers.ReadOnlyField(source='project.id')
    budget_type = serializers.ReadOnlyField(source='project.published_version.budget_type')
    budget_price = serializers.ReadOnlyField(source='project.published_version.budget_price')
    currency = serializers.ReadOnlyField(source='project.published_version.currency')
    status = serializers.ReadOnlyField(source='project.status')
    proposal_amount = serializers.ReadOnlyField()
    proposal_items = serializers.ReadOnlyField()
    project_status = serializers.ReadOnlyField()
    awarded = serializers.ReadOnlyField()
    proposal_at = serializers.DateTimeField()
    submission_deadline = serializers.DateTimeField(source='project.published_version.submission_deadline')
    updated_at = serializers.DateTimeField()
    closed_at = serializers.DateTimeField(source='project.closed_at')
    awarded_at = serializers.DateTimeField(source='project.awarded_at')
    created_at = serializers.DateTimeField(source='project.created_at')
    created_by = MinimalUserSeralizer(source='project.created_by')
    tags = serializers.ReadOnlyField(source='project.tags')
    average_score = serializers.SerializerMethodField()
    project_type = serializers.CharField(source='project.project_type')
    open_to_world = serializers.BooleanField(source='project.open_to_world')
    open_to_shortlist = serializers.BooleanField(source='project.open_to_shortlist')
    work_category = serializers.CharField(source='project.work_category')
    job_type = serializers.CharField(source='project.job_type')

    class Meta:
        model = ProjectVendors
        fields = (
            'archived',
            'slug',
            'name',
            'id',
            'budget_type',
            'budget_price',
            'currency',
            'status',
            'project_status',
            'awarded',
            'proposal_at',
            'submission_deadline',
            'updated_at',
            'created_by',
            'proposal_amount',
            'closed_at',
            'awarded_at',
            'created_at',
            'tags',
            'average_score',
            'job_type',
            'proposal_items',
            'project_type',
            'open_to_shortlist',
            'open_to_world',
            'work_category',
        )

    def get_average_score(self, obj):
        try:
            return obj.review_score.score_average
        except ObjectDoesNotExist:
            pass


class ProjectVendorsSerializer(serializers.ModelSerializer):
    project_vendor_id = serializers.ReadOnlyField(source="id")
    id = serializers.ReadOnlyField(source="vendor.id")
    slug = serializers.ReadOnlyField(source="vendor.slug")
    email = serializers.ReadOnlyField(source="vendor.email")
    name = serializers.ReadOnlyField(source="vendor.name")
    availability = serializers.ReadOnlyField(source="vendor.availability")
    available_from = serializers.ReadOnlyField(source="vendor.available_from")
    has_requested_document_expired = serializers.ReadOnlyField(source="vendor.has_requested_document_expired")
    logo = serializers.ReadOnlyField(source="vendor.logo")
    vendor_type = serializers.ReadOnlyField(source="vendor.vendor_type.name")
    is_approved = serializers.ReadOnlyField(source="vendor.is_approved")
    last_active = serializers.ReadOnlyField(source="vendor.last_active")
    shortlisted = serializers.ReadOnlyField(source="vendor.shortlisted")
    has_sign_request = serializers.ReadOnlyField(source="vendor.has_sign_request")
    vendor_scores = VendorScoreSerializer(source="vendor.raw_score")
    linked_tasks = TaskMinimalSerializer(many=True)
    proposal_files = OffersFilesSerializer(source="files", many=True)
    questions = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    onboarded = serializers.CharField(source="vendor.onboarding_status", read_only=True)

    class Meta:
        model = ProjectVendors
        fields = (
            "project_vendor_id",
            "id",
            "slug",
            "email",
            "name",
            "availability",
            "available_from",
            "vendor_type",
            "has_requested_document_expired",
            "logo",
            "is_approved",
            "last_active",
            "shortlisted",
            "has_sign_request",
            "vendor_scores",
            "linked_tasks",
            "proposal_files",
            "questions",
            "reject_reason",
            "accepted",
            "status",
            "proposal_amount",
            "proposal_comment",
            "proposal_items",
            "proposal_at",
            "proposal_changed_at",
            "awarded",
            "inactive",
            "invitation_sent",
            "temporary",
            "hidden",
            "accepted_at",
            "onboarded",
        )

    def get_questions(self, obj):
        return obj.questions.exists()

    def get_status(self, obj):
        if obj.vendor and obj.vendor.archived:
            return obj.vendor.status
        else:
            return obj.status


class ProjectVendorSerializerNoLinkedTasks(ProjectVendorsSerializer):
    class Meta(ProjectVendorsSerializer.Meta):
        exclude = ('linked_tasks',)


class MainProjectVendorsSerializer(serializers.Serializer):
    type = ConstSerializer("partner")
    data = ProjectVendorsSerializer(source="*")


class MainProjectVendorsSerializerNoLinkedTasks(serializers.Serializer):
    type = ConstSerializer("partner")
    data = ProjectVendorSerializerNoLinkedTasks(source="*")


class PartialProjectVendorSerializer(serializers.ModelSerializer):
    vendor = VendorSerializer(
        fields=(
            'id',
            'slug',
            'email',
            'name',
            'last_active',
            'public_id',
            'logo',
            'vendor_type',
            'shortlisted',
            'has_sign_request',
            'status',
            'has_requested_document_expired',
            'onboarded',
        )
    )
    proposal_files = OffersFilesSerializer(source='files.visible', many=True)
    linked_tasks = TaskMinimalSerializer(many=True, read_only=True)

    class Meta:
        model = ProjectVendors
        fields = (
            'id',
            'accepted',
            'project_status',
            'proposal_amount',
            'proposal_at',
            'proposal_comment',
            'awarded',
            'inactive',
            'proposal_files',
            'invitation_sent',
            'temporary',
            'vendor',
            'linked_tasks',
        )


class PartialProjectVendorSerializerNoLinkedTasks(PartialProjectVendorSerializer):
    class Meta(PartialProjectVendorSerializer.Meta):
        exclude = ('linked_tasks',)


class ProjectForVendorSerializer(DynamicSerializerFieldsMixin, UnreadActivitiesMixin, serializers.ModelSerializer):
    archived = serializers.BooleanField(source='project.archived', read_only=True)
    awarded = serializers.BooleanField(read_only=True)
    days_left = serializers.CharField(source='project.days_left', read_only=True)
    id = serializers.ReadOnlyField(source='project.id')
    budget_type = serializers.ReadOnlyField(source='project.published_version.budget_type')
    budget_price = serializers.ReadOnlyField(source='project.published_version.budget_price')
    budget_price_range_lower = serializers.ReadOnlyField(source='project.published_version.budget_price_range_lower')
    budget_price_range_upper = serializers.ReadOnlyField(source='project.published_version.budget_price_range_upper')
    budget_range_name = serializers.ReadOnlyField(source='project.published_version.budget_range_name')
    budget_rate = serializers.ReadOnlyField(source='project.published_version.budget_rate')
    budget_rate_range_lower = serializers.ReadOnlyField(source='project.published_version.budget_rate_range_lower')
    budget_rate_range_upper = serializers.ReadOnlyField(source='project.published_version.budget_rate_range_upper')
    budget_duration = serializers.ReadOnlyField(source='project.published_version.budget_duration')
    budget_items = serializers.ReadOnlyField(source='project.published_version.budget_items')
    currency = serializers.CharField(source='project.published_version.currency', read_only=True)
    name = serializers.CharField(source='project.name', read_only=True)
    slug = serializers.CharField(source='project.current_slug_with_id', read_only=True)
    original_slug = serializers.CharField(source='project.slug', read_only=True)
    description = serializers.CharField(source='project.published_version.description', read_only=True)
    status = serializers.CharField(source='project_status', read_only=True)
    project_status = serializers.CharField(source='project.status', read_only=True)
    project_type = serializers.CharField(source='project.project_type', read_only=True)
    skills = serializers.ReadOnlyField(source='project.skills')

    acceptance_deadline = serializers.DateTimeField(
        label='Invitation acceptance date', source='project.published_version.acceptance_deadline', read_only=True
    )
    submission_deadline = serializers.DateTimeField(
        label='Proposal Due Date', source='project.published_version.submission_deadline', read_only=True
    )
    closed_at = serializers.DateTimeField(label='Closed', source='project.closed_at', read_only=True)
    files = ProjectFileForVendorSerializer(
        label='Files', source='project.published_version.files_for_vendor', many=True, required=False, read_only=True
    )
    proposal_files = OffersFilesSerializer(source='files', many=True, required=False, read_only=True)
    created_by = SimpleUserSerializer(source='project.created_by', label='Created by', read_only=True)

    proposal_amount = serializers.DecimalField(
        max_digits=30, decimal_places=2, error_messages={'max_digits': 'This value is too large (max %s digits)'}
    )
    proposal_items = serializers.ReadOnlyField()
    response_form_id = serializers.SerializerMethodField()
    has_unread_activities = serializers.SerializerMethodField()
    has_unread_messages = serializers.SerializerMethodField()
    question_count = serializers.IntegerField(source='project.question_count', read_only=True)

    comment = serializers.CharField()
    completion_deadline = serializers.DateTimeField(source='project.published_version.completion_deadline', read_only=True)

    class Meta:
        model = ProjectVendors
        fields = (
            'id',
            'acceptance_deadline',
            'accepted',
            'accepted_at',
            'archived',
            'awarded',
            'budget_duration',
            'budget_items',
            'budget_price',
            'budget_price_range_lower',
            'budget_price_range_upper',
            'budget_range_name',
            'budget_rate',
            'budget_rate_range_lower',
            'budget_rate_range_upper',
            'budget_type',
            'closed_at',
            'comment',
            'completion_deadline',
            'created_by',
            'currency',
            'days_left',
            'description',
            'files',
            'has_unread_activities',
            'has_unread_messages',
            'hidden',
            'inactive',
            'invitation_sent',
            'name',
            'project_status',
            'project_type',
            'proposal_amount',
            'proposal_at',
            'proposal_changed_at',
            'proposal_comment',
            'proposal_files',
            'proposal_items',
            'question_count',
            'reject_reason',
            'response_form_id',
            'seen',
            'skills',
            'slug',
            'status',
            'submission_deadline',
            'temporary',
            'updated_at',
            'original_slug',
        )
        read_only_fields = ('proposal_at', 'temporary')

    def get_project_from_object(self, obj):
        return obj.project

    def get_response_form_id(self, obj):
        if obj.get_project_form_response() is not None:
            return obj.get_project_form_response().pk
        return ''

    def validate(self, attrs):
        attrs = super().validate(attrs)

        if not attrs.get('proposal_amount'):
            raise serializers.ValidationError({'proposal_amount': 'Proposal amount has to be filled.'})

        return attrs


class ProposalSerializer(serializers.ModelSerializer):
    proposal_amount = BlankDecimalField(max_digits=30, decimal_places=2, coerce_to_string=False, required=False, allow_null=True)
    proposal_items = serializers.ReadOnlyField(required=False)
    proposal_comment = serializers.CharField(required=False, allow_null=True)

    class Meta:
        model = ProjectVendors
        fields = ('proposal_amount', 'proposal_items', 'proposal_comment')
