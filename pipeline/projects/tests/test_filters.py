from projects.filters import ProjectFilterSet
from projects.models import Project
from shortlist.tests.helpers import TenantTestCase


class ProjectsFiltersTest(TenantTestCase):
    def setUp(self):
        super().setUp()
        for status in Project.STATUSES:
            self._get_project(status=status[0])

    def tearDown(self):
        Project.objects.all().delete()
        super().tearDown()

    def test_project_filter_set(self):
        """ProjectFilterSet test"""
        for status in Project.STATUSES:
            projects = ProjectFilterSet({'status': status[0]})
            self.assertEqual(projects.qs.count(), 1)

    def test_project_filter_set_with_empty_filter(self):
        """
        ProjectFilterSet test with empty filter value
        """
        projects = ProjectFilterSet({'status': ''})
        self.assertEqual(projects.qs.count(), Project.objects.count())

    def test_projects_user_filter(self):
        """
        ProjectFilterSet test filtering by user slug
        """
        slug = self.user.slug
        projects = ProjectFilterSet({'user': slug})
        self.assertEqual(projects.qs.count(), Project.objects.count())
