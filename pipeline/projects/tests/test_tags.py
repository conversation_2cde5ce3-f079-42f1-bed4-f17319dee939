from rest_framework import status

from ._utils import BaseProjectTest
from projects.models import Project, ProjectTag
from shortlist.tests.helpers import generate_unique

TAGS_ALL = "/api/project_tags/"


class TagTests(BaseProjectTest):
    with_login_user = True

    def test_all_tags(self):
        project1 = self._get_project()
        ProjectTag.objects.create(project=project1, name="App")
        ProjectTag.objects.create(project=project1, name="Web")
        project2 = self._get_project()
        ProjectTag.objects.create(project=project2, name="App")
        tags = self.check_response(status.HTTP_200_OK, self.api_client.get(TAGS_ALL)).data
        self.assertEqual(tags[0], "App", "'App' should be first as it's in 2 project")
        self.assertEqual(tags[1], "Web", "'Web' should be second as it's in 2 project")

    def test_getting_from_project(self):
        project = self._get_project()
        tag1 = ProjectTag.objects.create(project=project, name=self.factory.name())
        tag2 = ProjectTag.objects.create(project=project, name=self.factory.name())
        response = self.check_response(status.HTTP_200_OK, self.api_client.get(f"/api/projects/{project.slug}/"))
        self.assertIn("tags", response.data, "Response data should contain tags")
        tags = response.data["tags"]
        self.assertEqual(tags, [tag1.name, tag2.name], "Project tags should be returned in creation order")
        tag1.delete()
        tag3 = ProjectTag.objects.create(project=project, name=self.factory.name())
        response = self.check_response(status.HTTP_200_OK, self.api_client.get(f"/api/projects/{project.slug}/"))
        self.assertIn("tags", response.data, "Response data should contain tags")
        tags = response.data["tags"]
        self.assertEqual(tags, [tag2.name, tag3.name], "Project tags should be returned in creation order")

    def test_setting_in_project(self):
        project = self._get_full_project()
        response = self.check_response(status.HTTP_200_OK, self.api_client.get(f"/api/projects/{project.slug}/"))
        self.assertIn("tags", response.data, "Response data should contain tags")
        self.assertEqual(response.data["tags"], [], "Tags should be empty before settings")

        tags = ["tag 1", "tag 2", "tag 3"]

        request = self._get_project_request(project, dict(tags=tags))
        self.check_response(status.HTTP_200_OK, self.api_client.patch(f"/api/projects/{project.slug}/", request))

        self.assertEqual(project.tags, tags, "Tags should have been set")

    def test_create_project_with_tags(self):
        """SHOR-1654"""
        request = {
            "description": self.factory.sentence(),
            "files": [],
            "name": self.factory.name(),
            "status": Project.STATUS_DRAFT,
            "team": [],
            "tags": list(generate_unique(self.factory.word, 2)),
            "vendors": [],
        }
        r = self.check_response(status.HTTP_201_CREATED, self.api_client.post("/api/projects/", data=request))
        self.assertEqual(r.data["tags"], request["tags"])

    def test_duplicate_tags(self):
        request = {
            "description": self.factory.sentence(),
            "files": [],
            "name": self.factory.name(),
            "status": Project.STATUS_DRAFT,
            "team": [],
            "tags": ["1", "2", "duplicated", "3", "duplicated"],
            "vendors": [],
        }
        r = self.check_response(status.HTTP_201_CREATED, self.api_client.post("/api/projects/", data=request))
        self.assertEqual(set(r.data["tags"]), set(request["tags"]))
