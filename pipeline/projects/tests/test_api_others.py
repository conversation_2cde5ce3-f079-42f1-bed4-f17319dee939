from contextlib import contextmanager

from unittest.mock import patch
from rest_framework import status

from projects.models import Project, ProjectVendors
from shortlist import current_user
from shortlist.tests.helpers import TenantTestCase, create_api_client
from ._utils import BaseProjectTest

from unittest import skip


class APINotLoggedInTest(BaseProjectTest):
    def test_get_project_list(self):
        self.check_response(status.HTTP_404_NOT_FOUND, self.client.get("/api/projects/"))

    def test_random_slug_access(self):
        self.check_response(status.HTTP_404_NOT_FOUND, self.client.get(f"/api/projects/{self.factory.slug()}/"))

    def test_project_actions(self):
        actions = ["send", "close", "award"]
        project = self._get_project()
        # TODO fix AttributeError: 'ProjectForVendorViewSet' object has no attribute 'send'
        for action in actions:
            self.check_response(status.HTTP_404_NOT_FOUND, self.client.post(f"/api/projects/{project.slug}/{action}/", {}))

    def test_project_update(self):
        project = self._get_project()
        self.check_response(status.HTTP_404_NOT_FOUND, self.client.put(f"/api/projects/{project.slug}/"))

    def get_vendors_navigation_list(self):
        """SHOR-5767"""
        url = "/api/v/projects/navigation_list/"
        self.check_response(status.HTTP_403_FORBIDDEN, self.api_client.get(url))


class VendorVisibility(BaseProjectTest):
    """Make sure vendors cannot access information about other vendors"""

    with_login_user = True

    def setUp(self):
        super().setUp()

        self.project = self._get_full_project(status=Project.STATUS_OPEN)
        self.project_url = f"/api/projects/{self.project.slug}/"
        self.vendors = {}
        # add two more vendors
        for i in range(2):
            self._get_vendor(able_to_login=True)
            self.vendors[self.vendor] = self.vendor_password
            pv = ProjectVendors.objects.create(project=self.project, vendor=self.vendor, order=i + 1)
            pv.accept()
        # post a question from buyer
        response = self.check_response(
            status.HTTP_201_CREATED,
            self.api_client.post("/api/project_questions/", {"content": self.factory.text(), "project": self.project.pk}),
        )
        with self.as_vendor(next(iter(self.vendors))) as vendor_client:
            # post a response from vendor
            self.check_response(
                status.HTTP_201_CREATED,
                vendor_client.post(
                    "/api/project_questions/", {"content": self.factory.text(), "project": self.project.pk, "parent": response.data["id"]}
                ),
            )

    @contextmanager
    def as_vendor(self, vendor):
        vendor_api_client = create_api_client(self.tenant.domain_url)
        vendor_api_client.login(email=vendor.email, password=self.vendors[vendor])
        with patch.object(current_user, "storage") as mock:
            mock.request.user = self.user
            yield vendor_api_client
            vendor_api_client.logout()

    def check_vendor_pov(self, vendor, question):
        related_vendors = question["related_vendors"]
        self.assertEqual(len(related_vendors), 1, "There should be just 1 one vendor in the related_vendors")
        self.assertEqual(related_vendors[0]["email"], vendor.email)
        self.assertEqual(related_vendors[0]["full_name"], vendor.full_name)
        if question["user"]["vendor"]:
            self.assertIn(question["user"]["slug"], (None, vendor.slug))
        else:
            self.assertNotEqual(question["user"]["email"], "", "Buyer user should not have been filtered")
        for answer in question["related_question"]["answers"]:
            self.assertTrue(answer["user"]["vendor"])
            self.assertIn(answer["user"]["company_name"], ("Other Vendor", vendor.name))

    def check_user_pov(self, question):
        related_vendors = question["related_vendors"]
        if related_vendors:
            self.assertEqual(len(related_vendors), len(self.vendors) + 1, "All vendors should in the related_vendors from user's POV")
            for answer in question["related_question"]["answers"]:
                self.assertTrue(answer["user"]["vendor"])
                self.assertNotEqual(answer["user"]["company_name"], "Other Vendor")

    @skip("Random failures")
    def test_activity_feed(self):
        """related_vendors for activities should not contain information about other vendors"""
        for vendor in self.vendors:
            with self.as_vendor(vendor) as vendor_client:
                data = self.check_response(status.HTTP_200_OK, vendor_client.get("/api/activities/")).data
                for activity in data:
                    self.check_vendor_pov(vendor, activity)
        # user should get all the data
        data = self.check_response(status.HTTP_200_OK, self.api_client.get("/api/activities/")).data

        for activity in data:
            self.check_user_pov(activity)

    @skip("Random failures")
    def test_questions(self):
        """question data should not contain information about other vendors"""
        for vendor in self.vendors:
            with self.as_vendor(vendor) as vendor_client:
                url = self.project_url + "questions/"
                data = self.check_response(status.HTTP_200_OK, vendor_client.get(url)).data
                for question in data:
                    self.check_vendor_pov(vendor, question)

        # user should get all the data
        data = self.check_response(status.HTTP_200_OK, self.api_client.get(self.project_url + "questions/")).data
        for question in data:
            self.check_user_pov(question)


class StaffUserTests(TenantTestCase):
    with_staff_user = True

    def get_vendors_navigation_list(self):
        """SHOR-5767"""
        url = "/api/v/projects/navigation_list/"
        self.check_response(status.HTTP_403_FORBIDDEN, self.api_client.get(url))
