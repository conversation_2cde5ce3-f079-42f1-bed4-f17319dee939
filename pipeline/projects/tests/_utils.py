from unittest import mock


from django.core.files.base import ContentFile
from rest_framework import serializers, status

from shortlist import current_user
from shortlist.tests.helpers import TenantTestCase

from projects.models import ProjectVendors


class BaseProjectTest(TenantTestCase):
    @staticmethod
    def _format_datetime(_datetime):
        # TODO maybe we should move this function to more common place
        if _datetime:
            date_time_field = serializers.DateTimeField()
            return date_time_field.to_representation(_datetime)
        return None

    def _get_project_request(self, project, fields):
        return fields

    def add_file_to_project(self, project, file_name, content):
        pf = project.files.create(file_name=file_name, file=project.upload_dir() + file_name)
        pf.file.save(pf.file.name, ContentFile(content))
        project.latest_version.files.add(pf)
        return pf

    def send_message(self, as_vendor=False, **kwargs):
        if as_vendor:
            self.api_client.login(email=self.vendor.email, password=self.vendor_password)
            current_user.storage = mock.MagicMock()
            current_user.storage.request.user = self.vendor.first_contact
        project_question = {
            'content': self.factory.text(),
            'project': self.project.pk,
        }
        project_question.update(**kwargs)

        return self.check_response(status.HTTP_201_CREATED, self.api_client.post('/api/project_questions/', project_question))

    def add_to_project(self, vendor, accept=True, offer=None):
        pv = ProjectVendors.objects.get_or_create(project=self.project, vendor=vendor)[0]
        if accept:
            pv.accept()
            if offer:
                pv.update_proposal(offer)
        return pv
