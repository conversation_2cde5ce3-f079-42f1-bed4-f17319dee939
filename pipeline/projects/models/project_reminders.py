from django.db import models
from shortlist.utils import make_choices


class ProjectReminder(models.Model):
    INVITATION_BYUER = "Invitation Due Date - Buyer"
    SUBMISSION_VENDOR = "Submission Due Date - Vendor"

    project = models.ForeignKey("projects.Project", related_name="reminders", on_delete=models.CASCADE)
    event = models.CharField(choices=make_choices(INVITATION_BYUER, SUBMISSION_VENDOR), max_length=100, db_index=True)
    when = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ("project", "event")
        app_label = "projects"
