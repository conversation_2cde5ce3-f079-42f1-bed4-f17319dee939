from rest_framework.generics import get_object_or_404
from rest_framework.response import Response
from rest_framework import status


from activities.models import ProjectActivity
from activities.serializers import ActivitySerializer
from projects.helpers import get_id_or_slug
from projects.models import ProjectVendors
from projects.views import ProjectQuestionsViewSet
from shortlist.permissions import IsUser


class ProjectProposalQuestionsView(ProjectQuestionsViewSet):
    permission_classes = [IsUser]
    create_info_notification = False
    create_project_notification = False
    create_team_notification = False

    def get_queryset(self):
        qs = super(ProjectQuestionsViewSet, self).get_queryset()
        project_id, project_slug = get_id_or_slug(self.kwargs.get('project_slug'))
        if project_id:
            qs = qs.filter(
                project__id=project_id,
                proposal__project__id=project_id,
                proposal__vendor__slug=self.kwargs.get('vendor_slug'),
            )
        else:
            qs = qs.filter(
                project__slug=project_slug,
                proposal__project__slug=project_slug,
                proposal__vendor__slug=self.kwargs.get('vendor_slug'),
            )
        if self.request.method == 'GET':
            qs = qs.filter(parent__isnull=True)
        return qs

    def set_request_data(self):
        super().set_request_data()
        project_id, project_slug = get_id_or_slug(self.kwargs.get('project_slug'))
        if project_id:
            proposal = get_object_or_404(ProjectVendors, project__id=project_id, vendor__slug=self.kwargs.get('vendor_slug'))
        else:
            proposal = get_object_or_404(ProjectVendors, project__slug=project_slug, vendor__slug=self.kwargs.get('vendor_slug'))
        self.request._full_data.update(
            {
                'proposal': proposal.pk,
            }
        )

    def perform_create(self, serializer):
        self.instance = serializer.save()

    def create(self, request, *args, **kwargs):
        response = super().create(request, *args, **kwargs)
        # mark message as unread
        obj = self.instance
        # should create notification only for replays
        if obj and not obj.proposal and obj.user.is_vendor():
            # this should always work since we have only one level parent <-> child relation
            proposal = obj.parent.proposal
            # find all users related to this question / response
            # creator, team members + vendor
            users = proposal.project.all_team
            # add only new users
            obj.unread_by_users.add(*users.difference(set(obj.unread_by_users.all())))
        return response

    def update(self, request, *args, **kwargs):
        """
        trick for update unread messages
        to mark discussion as read you have to send POST request with read: true
        update should come from with parent question id
        """
        if 'read' not in request.data and not request.data.get('read'):
            return Response(status=status.HTTP_400_BAD_REQUEST)
        # mark as read
        obj = self.get_object()
        user = request.user
        user.unread_project_proposal_messages.filter(parent=obj).delete()
        return Response(status=status.HTTP_200_OK)

    def list(self, request, *args, **kwargs):
        """Returns list of activity connected only with this proposal"""
        project_id, project_slug = get_id_or_slug(self.kwargs.get('project_slug'))
        if project_id:
            activities = ProjectActivity.objects.filter(
                related_question_project__proposal__vendor__slug=kwargs.get('vendor_slug'),
                related_question_project__proposal__project__id=project_id,
                related_question_project__parent__isnull=True,
                activity_type='ProjectQuestionAsk',
            )
        else:
            activities = ProjectActivity.objects.filter(
                related_question_project__proposal__vendor__slug=kwargs.get('vendor_slug'),
                related_question_project__proposal__project__slug=project_slug,
                related_question_project__parent__isnull=True,
                activity_type='ProjectQuestionAsk',
            )
        activities = activities.prefetch_related('related_question_project__answers__unread_by_users')
        context = self.get_serializer_context()
        return Response(ActivitySerializer(activities, many=True, context=context).data)
