from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.fields import empty
from rest_framework.response import Response
from events.event_types import ProjectUpdated
from shortlist.permissions import Permission, TypedPermissionMixin
from preferences.models import CustomFieldsTemplate
from projects.serializers.project_template_serializers import ProjectTemplateSerializer


class ProjectTemplatesViewSet(TypedPermissionMixin, viewsets.ModelViewSet):
    permission_app = 'buyer'
    permission_type = 'project template'
    serializer_class = ProjectTemplateSerializer
    queryset = CustomFieldsTemplate.objects.filter(model__isnull=True).exclude_autogenerated_templates()

    def get_serializer(self, instance=None, data=empty, many=None, partial=False):
        serializer_class = self.get_serializer_class()
        context = self.get_serializer_context()
        kwargs = {'exclude': ('template_fields',)} if many else {}
        return serializer_class(instance, data=data, many=many, partial=partial, context=context, **kwargs)

    @action(detail=True, permission_classes=[Permission.buyer('project template', 'edit')])
    def inform_vendors(self, request, *args, **kwargs):
        inform_partners = request.data.get('inform_partners', False)
        if inform_partners:
            project_template = self.get_object()
            for template_to_project in project_template.raw_projects.filter(
                project__published_version_id__isnull=False,
                project__deleted=False,
            ):
                ProjectUpdated(project=template_to_project.project, change_list={'description': "new"}, inform_partners=inform_partners)
        return Response(status=status.HTTP_200_OK)
