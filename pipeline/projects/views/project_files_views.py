from django.http import HttpResponseNotFound
from django.shortcuts import get_object_or_404

from rest_framework.views import APIView

from projects.models import ProposalFile, ProjectFile

from shortlist.utils import serve_file
from shortlist.permissions import IsUser


class ProjectFileView(APIView):
    permission_classes = [IsUser]
    file_model = {'project': ProjectFile, 'project-proposal': ProposalFile}

    def get(self, request, type, file_id):
        # check for file read permission
        file_model_class = self.file_model.get(type)
        file_obj = get_object_or_404(file_model_class, pk=file_id)
        user = request.user
        if file_obj.is_available_for_user(user):
            return serve_file(file_obj.file_name, file_obj.file)
        return HttpResponseNotFound()


class PublicFileView(APIView):
    permission_classes = []

    def get(self, request, file_id, key):
        file_obj = get_object_or_404(ProjectFile, pk=file_id)

        if file_obj.is_public(key):
            return serve_file(file_obj.file_name, file_obj.file)
        return HttpResponseNotFound()
