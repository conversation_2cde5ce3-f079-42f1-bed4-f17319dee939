from users.permissions import TENANT_BITMASK


class ActivityPlace:
    """
    Silly class to describe activity places in system
    """

    DASHBOARD = 1 << 1
    PROJECT = 1 << 2
    VENDOR_PROFILE = 1 << 3
    USER_PROFILE = 1 << 4
    VENDOR_ACCOUNT = 1 << 5


class BaseActivity:
    """
    A base class for subclassing in specific activities.
    """

    message = ''
    permission_required = TENANT_BITMASK
    model = None

    show_on = []
    visible_for_vendor = False

    def prepare_activity(self, related_obj, user, **kwargs):
        return {
            'activity_type': self.__class__.__name__,
            'user_id': user.id if user is not None else None,
            'related_obj_id': related_obj.id,
            'related_obj_slug': getattr(related_obj, 'slug', ''),
            'related_obj_name': related_obj.name_for_activities,
            'related_obj_type': related_obj.type_for_activities,
            'permission_required': self.permission_required,
            'show_in_vendor_profile': self._get_visibility_for(ActivityPlace.VENDOR_PROFILE),
            'show_in_user_profile': self._get_visibility_for(ActivityPlace.USER_PROFILE),
            'show_in_dashboard': self._get_visibility_for(ActivityPlace.DASHBOARD),
            'show_in_project': self._get_visibility_for(ActivityPlace.PROJECT),
            'visible_for_vendor': self.visible_for_vendor,
        }

    def set_additional_values(self, activity, **kwargs):
        return

    def _get_visibility_for(self, activity_place):
        """
        Resolves if activity should be showed in activity_place
        :param activity_place:
        :return: bool
        """
        return activity_place in self.show_on


class ActivityWithRelatedVendorsMixin:
    """ """

    def set_additional_values(self, activity, **kwargs):
        if 'related_vendors' in kwargs:
            activity.related_vendors.add(*kwargs['related_vendors'])
            activity.save()
