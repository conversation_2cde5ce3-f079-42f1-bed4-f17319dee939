from shortlist.tests.helpers import TenantTestCase
from tasks import sdk as tasks_sdk
from mockito import mock, unstub, when

from tasks.models import TaskGroup
from tasks.services.tasks.command.create_task_group import CreateTaskGroupCommand
from shortlist.result import Result


class TaskGroupCreationSDKTestCase(TenantTestCase):
    def tearDown(self):
        unstub()
        super().tearDown()

    def test_create_task_group(self):
        task_group_data = {"name": "Task Group name"}
        expected_result = Result.success(mock({"id": 1}, spec=TaskGroup))
        when(CreateTaskGroupCommand).execute(task_group_data).thenReturn(expected_result)

        result = tasks_sdk.creation.create_task_group(task_group_data)
        self.assertEqual(result.value, expected_result.value)

    def test_create_task_group_wrong_payload(self):
        task_group_data = {}
        expected_result = Result.fail("Task Group data is not valid")
        when(CreateTaskGroupCommand).execute(task_group_data).thenReturn(expected_result)

        result = tasks_sdk.creation.create_task_group(task_group_data)
        self.assertEqual(result.errors, expected_result.errors)
