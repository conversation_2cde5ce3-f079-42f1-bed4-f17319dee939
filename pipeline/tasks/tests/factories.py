from datetime import date, timedelta

import factory
from django.utils import timezone
from faker import Faker

from shortlist.current_user import get_current_user
from shortlist.factories import ShortlistDjangoModelFactory, ObjectOrNoneFactory
from tasks.models import TaskTemplate
from vendors.factories import VendorFactory


fake = Faker()


class CustomRateTypeFactory(ShortlistDjangoModelFactory):
    class Meta:
        model = "preferences.CustomRateType"
        django_get_or_create = ("name",)

    name = "hourly"


class TaskGroupFactory(ShortlistDjangoModelFactory):
    class Meta:
        model = "tasks.TaskGroup"

    class Params:
        with_dates = factory.Trait(
            date_start=factory.Faker("date_between", start_date="today", end_date="today"),
            date_end=factory.Faker("future_date"),
        )

    name = factory.Sequence(lambda n: f"Task Group {n}")
    description = factory.Faker("sentence")
    status = "draft"

    @factory.post_generation
    def teams(self, create: bool, teams: list) -> None:
        if create and teams:
            self.teams.add(*teams)

    @factory.post_generation
    def members(self, create: bool, members: list) -> None:
        if not create:
            return
        if members:
            self.members.add(*members)

    @factory.post_generation
    def _set_members_to_created_by_only_if_given(self, create, extracted, **kwargs):
        if create and self.created_by is not None and self.created_by != get_current_user():
            self.members.set([self.created_by])


class TimesheetTaskConfigFactory(ShortlistDjangoModelFactory):
    class Meta:
        model = "timesheet.TimesheetTaskConfig"

    config = factory.Dict(
        {
            "name": factory.Faker("word"),
            "week_start_day": "monday",
            "mode": "week",
            "description": factory.Faker("text"),
            "entry_types": [],
            "meta": {
                "schema_version": 1,
                "id": None,
                "created_at": f"{factory.Faker('date')}",
            },
        }
    )


class TaskFactory(ShortlistDjangoModelFactory):
    class Meta:
        model = "tasks.Task"

    class Params:
        with_weekly = factory.Trait(
            timesheettaskconfig=factory.RelatedFactory(
                TimesheetTaskConfigFactory,
                factory_related_name="task",
                config__mode="week",
            )
        )
        with_monthly = factory.Trait(
            timesheettaskconfig=factory.RelatedFactory(
                TimesheetTaskConfigFactory,
                factory_related_name="task",
                config__mode="month",
            )
        )

        draft = factory.Trait(status="draft", live_at=None)
        pending = factory.Trait(
            status="pending",
            live_at=factory.LazyFunction(timezone.now),
            vendor=factory.SubFactory(VendorFactory),
        )
        accepted = factory.Trait(
            status="accepted",
            live_at=factory.LazyFunction(timezone.now),
            vendor=factory.SubFactory(VendorFactory),
        )
        completed = factory.Trait(
            status="completed",
            live_at=factory.LazyFunction(timezone.now),
            vendor=factory.SubFactory(VendorFactory),
        )
        rejected = factory.Trait(
            status="rejected",
            live_at=factory.LazyFunction(timezone.now),
            vendor=factory.SubFactory(VendorFactory),
        )
        canceled = factory.Trait(
            status="canceled",
            live_at=factory.LazyFunction(timezone.now),
            vendor=factory.SubFactory(VendorFactory),
        )
        archived = factory.Trait(
            status="archived",
            live_at=factory.LazyFunction(timezone.now),
            vendor=factory.SubFactory(VendorFactory),
        )
        under_review = factory.Trait(
            status="under_review",
            live_at=factory.LazyFunction(timezone.now),
            vendor=factory.SubFactory(VendorFactory),
        )

    name = factory.Sequence(lambda n: f"Task {n}")
    budget_rate_type = ObjectOrNoneFactory(factory.SubFactory(CustomRateTypeFactory))
    description = factory.Faker("sentence")
    status = "draft"
    task_group = factory.SubFactory(TaskGroupFactory)

    @factory.post_generation
    def teams(self, create: bool, teams: list) -> None:
        if create and teams:
            self.teams.add(*teams)

    @factory.post_generation
    def timesheets_approvers(self, create: bool, timesheets_approvers: list) -> None:
        if create and timesheets_approvers:
            self.timesheets_approvers.add(*timesheets_approvers)

    @factory.post_generation
    def _set_managers_to_created_by_only_if_given(self, create, extracted, **kwargs):
        if create and self.created_by is not None and self.created_by != get_current_user():
            self.managers.set([self.created_by])

    @classmethod
    def dict(cls, **kwargs):
        if "task_group" not in kwargs:
            kwargs["task_group"] = TaskGroupFactory().id

        if "budget_rate_type" not in kwargs:
            kwargs["budget_rate_type"] = CustomRateTypeFactory().id

        return super().dict(**kwargs)


class TaskMilestoneFactory(ShortlistDjangoModelFactory):
    class Meta:
        model = "tasks.Milestone"

    name = factory.Sequence(lambda n: f"Milestone {n}")
    milestone_order = 0


class TaskTimeSheetFactory(ShortlistDjangoModelFactory):
    class Meta:
        model = "tasks.TaskTimeSheet"

    task = factory.SubFactory(TaskFactory)
    date_worked = factory.LazyFunction(timezone.datetime.today)
    minutes_worked = 0


def _generate_timesheet_date_start(timesheet):
    if timesheet.task and timesheet.task.date_start:
        return timesheet.task.date_start
    previous_timesheet = timesheet.task.tasktimesheetperiod_set.order_by("-date_start").first()
    if previous_timesheet:
        return previous_timesheet.date_end + timezone.timedelta(days=1)
    return timezone.datetime.today()


class TaskTimeSheetPeriodFactory(ShortlistDjangoModelFactory):
    class Meta:
        model = "tasks.TaskTimeSheetPeriod"

    class Params:
        duration = "weekly"

    status = "unsubmitted"
    task = factory.SubFactory(TaskFactory)
    date_start = factory.LazyAttribute(lambda o: _generate_timesheet_date_start(o))
    date_end = factory.LazyAttribute(lambda o: o.date_start + timezone.timedelta(days=7 if o.duration == "weekly" else 1))
    minutes_total = factory.Faker("random_int", max=60 * 7 * 24)


class TaskTemplateFactory(ShortlistDjangoModelFactory):
    class Meta:
        model = TaskTemplate

    name = factory.Faker("word")
    template_name = factory.Faker("word")
    description = factory.Faker("sentence")
    budget_type = "total"
    budget_total = factory.Faker("pydecimal", min_value=100, max_value=1000)
    date_start = factory.LazyFunction(lambda: date.today() + timedelta(days=2))
    date_end = factory.LazyFunction(lambda: date.today() + timedelta(days=10))
    task_group = factory.SubFactory(TaskGroupFactory)
