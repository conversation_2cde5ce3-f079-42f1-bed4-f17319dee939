from datetime import date

from shortlist.tests.helpers import TenantTestCase
from tasks.models import TaskGroup
from tasks.services.tasks.command.create_task_group import (
    CreateTaskGroupCommand,
    TaskGroupDataValidator,
)


class TestCreateTaskGroupCommand(TenantTestCase):
    def setUp(self):
        super().setUp()
        self.validator = TaskGroupDataValidator
        self.command = CreateTaskGroupCommand(validator=self.validator)

    def test_execute_successful_creation(self):
        # given
        task_group_data = {
            "name": "Test Task Group",
            "description": "This is a test task group",
            "date_start": date(2023, 5, 1),
            "date_end": date(2023, 5, 31),
            "currency": "PLN",
        }

        # when
        result = self.command.execute(task_group_data)

        # then
        self.assertTrue(result.is_successful)
        task_group = result.value
        self.assertIsInstance(task_group, TaskGroup)
        self.assertEqual(task_group.name, task_group_data["name"])
        self.assertEqual(task_group.description, task_group_data["description"])
        self.assertEqual(task_group.date_start, task_group_data["date_start"])
        self.assertEqual(task_group.date_end, task_group_data["date_end"])
        self.assertEqual(task_group.currency, task_group_data["currency"])

    def test_execute_validation_failure(self):
        # given
        task_group_data = {
            "name": "",
            "description": "This is a test task group",
            "date_start": date(2023, 5, 1),
            "date_end": date(2023, 5, 31),
            "currency": "USD",
        }

        # when
        result = self.command.execute(task_group_data)

        # then
        self.assertTrue(result.is_failure)

    def test_execute_missing_fields(self):
        # given
        task_group_data = {
            "name": "Test Task Group",
            "description": "This is a test task group",
        }

        # when
        result = self.command.execute(task_group_data)

        # then
        self.assertTrue(result.is_failure)

    def test_execute_invalid_data_types(self):
        # given
        task_group_data = {
            "name": 123,
            "description": 456,
            "date_start": "2023-05-01",
            "date_end": "2023-05-31",
        }

        # when
        result = self.command.execute(task_group_data)

        # then
        self.assertTrue(result.is_failure)
