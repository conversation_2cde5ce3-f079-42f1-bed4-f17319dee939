from typing import Any

from shortlist.pydantic_utils.pydantic_drf import validation_error_as_fail
from shortlist.result import Result
from tasks.models import Task
from tasks.services.tasks.command.validate_task import TaskDataRequest, TaskDataValidator, ValidateTaskCommand
from tasks.services.tasks.data_model import BudgetType, Name, PositiveDecimal, TaskManager, TimesheetTemplateType
from users.models import User


class UpdateTaskDataRequest(TaskDataRequest):
    name: Name | None = None
    description: str | None = None
    budget_rate_type: BudgetType | None = None
    budget_rate_per_time_unit: PositiveDecimal | None = None
    budget_time_units_worked: PositiveDecimal | None = None
    budget_total: PositiveDecimal | None = None
    timesheet_template: TimesheetTemplateType | None = None
    managers: list[TaskManager] | None = None
    custom_fields_data: dict | None = None


class UpdateTaskDataValidator(TaskDataValidator):
    request_class = UpdateTaskDataRequest


class UpdateTaskCommand:
    def __init__(self, validator: type[UpdateTaskDataValidator], task: Task):
        self.validator = validator
        self.task = task

    @validation_error_as_fail
    def execute(self, task_data: UpdateTaskDataRequest | dict[str, Any]) -> Result:
        validate_command = ValidateTaskCommand(validator=self.validator, task=self.task)
        validation_result = validate_command.execute(task_data)
        if validation_result.is_failure:
            return validation_result

        task_data = validation_result.value

        # Get the original dict data to check which fields were actually provided
        original_data = {}
        if isinstance(task_data, dict):
            original_data = task_data.copy()
        else:
            # If it's a Pydantic model, get the fields that were explicitly set
            original_data = task_data.dict(exclude_unset=True)

        # Update fields that were explicitly provided in the request
        update_fields = []

        if 'name' in original_data:
            self.task.name = task_data.name
            update_fields.append('name')

        if 'description' in original_data:
            self.task.description = task_data.description
            update_fields.append('description')

        if 'date_start' in original_data:
            self.task.date_start = task_data.date_start
            update_fields.append('date_start')

        if 'date_end' in original_data:
            self.task.date_end = task_data.date_end
            update_fields.append('date_end')

        if 'budget_rate_type' in original_data:
            self.task.budget_rate_type_id = task_data.budget_rate_type
            update_fields.append('budget_rate_type_id')

        if 'budget_rate_per_time_unit' in original_data:
            self.task.budget_rate_per_time_unit = task_data.budget_rate_per_time_unit
            update_fields.append('budget_rate_per_time_unit')

        if 'budget_time_units_worked' in original_data:
            self.task.budget_time_units_worked = task_data.budget_time_units_worked
            update_fields.append('budget_time_units_worked')

        if 'budget_total' in original_data:
            self.task.budget_total = task_data.budget_total
            update_fields.append('budget_total')

        if 'custom_fields_data' in original_data:
            self.task.custom_fields_data = task_data.custom_fields_data
            update_fields.append('custom_fields_data')

        if update_fields:
            self.task.save(update_fields=update_fields)

        if 'managers' in original_data:
            set_task_managers(self.task, task_data.managers)

        return Result.success(value=self.task)


class ValidateUpdateTaskCommand:
    def __init__(self, validator: type[UpdateTaskDataValidator], task: Task):
        self.validator = validator
        self.task = task

    def execute(self, task_data: UpdateTaskDataRequest | dict[str, Any]) -> Result:
        return self.validator.validate(task_data)


def set_task_managers(task: Task, managers: list[TaskManager]) -> None:
    if not managers:
        task.managers.clear()
    else:
        task.managers.set(User.objects.filter(id__in={m.id for m in managers}))
