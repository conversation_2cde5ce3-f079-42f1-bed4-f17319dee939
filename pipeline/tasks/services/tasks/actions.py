from typing import TYPE_CHECKING

from tasks.services.actions_registry import ActionsRegistry, Action
from tasks.services.tasks import checks
from shortlist.result import Result


if TYPE_CHECKING:
    from tasks.models import Task
    from vendors.models import Vendor

registry = ActionsRegistry()

registry.add(Action(action_name="edit", availability_checks=[checks.check_can_edit], available_for=["buyer"]))


def get_task_actions_available(task: "Task", role: str) -> set[Action]:
    return registry.get_available_actions(task=task, role=role)


@registry.action(checks.check_can_make_live, available_for=["buyer"])
def make_live(task: "Task") -> Result:
    # TODO: we will implement these and the following actions one by one in successive iterations
    ...


@registry.action(checks.check_can_remove, available_for=["buyer"])
def remove(task: "Task") -> Result: ...


@registry.action(checks.check_can_edit_partner, available_for=["buyer"])
def edit_partner(task: "Task", vendor: "Vendor") -> Result: ...


@registry.action(checks.check_can_duplicate, available_for=["buyer"])
def duplicate(task: "Task") -> Result: ...


@registry.action(checks.check_can_complete, available_for=["buyer"])
def complete(task: "Task") -> Result: ...


@registry.action(checks.check_can_cancel, available_for=["buyer"])
def cancel(task: "Task") -> Result: ...


@registry.action(checks.check_can_add_invoice, available_for=["buyer", "vendor"])
def add_invoice(task: "Task") -> Result: ...


@registry.action(checks.check_can_reset, available_for=["buyer"])
def reset(task: "Task") -> Result: ...


@registry.action(checks.check_can_archive, available_for=["buyer"])
def archive(task: "Task") -> Result: ...


@registry.action(checks.check_can_reset_back, available_for=["buyer"])
def reset_back(task: "Task") -> Result: ...


@registry.action(checks.check_can_accept, available_for=["vendor"])
def accept(task: "Task") -> Result: ...


@registry.action(checks.check_can_reject, available_for=["vendor"])
def reject(task: "Task") -> Result: ...


@registry.action(checks.check_can_vendor_mark_as_completed, available_for=["vendor"])
def vendor_mark_as_completed(task: "Task") -> Result: ...
