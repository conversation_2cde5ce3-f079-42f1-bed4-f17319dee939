import decimal
import logging
from typing import Any

from django.db import connection

from clients import features
from documents.models import Payment
from documents.taxes import round_decimal
from events.event_types import BuyerAddedPaymentForTask
from payments.common.model import PaymentsActor, ServiceResultStatus
from payments.payments_service import approve_invoices, reject_invoices
from preferences.models import WorkType
from shortlist.content_rendering import render_text_content
from shortlist.current_user import get_current_user
from shortlist.permissions import Permission
from tasks import content_rendering
from tasks.models import TaskTimeSheetPeriod
from tasks.validators import (
    WorkItemRequiredValidator,
    validate_can_add_payment_to_timesheet,
)

logger = logging.getLogger(__name__)


def generating_payments_is_enabled():
    return connection.tenant.has_features(features.PAYMENTS)


def user_can_generate_payments(user):
    return generating_payments_is_enabled() and user.has_perm(Permission.buyer('payment', 'create'))


class TimesheetPaymentsService:
    def __init__(self):
        self.strategy = TimesheetPaymentsWithLineItemsStrategy

    @staticmethod
    def can_generate_payment(timesheet: TaskTimeSheetPeriod):
        if (
            not validate_can_add_payment_to_timesheet(timesheet, raise_exception=False)
            or timesheet.payments.exclude(status=Payment.status_code(Payment.STATUS_REJECTED)).exists()
            or (connection.tenant.has_features(features.PAYMENTS_ONLY_FOR_FULLY_ONBOARDED) and not timesheet.vendor.is_fully_onboarded())
            or timesheet.vendor.vendor_type.is_worker
        ):
            return False

        return True

    def generate_payment(self, timesheet: TaskTimeSheetPeriod, payment_data: dict[str, Any]) -> Payment | None:
        if not self.can_generate_payment(timesheet):
            return
        data = payment_data.copy()
        number_template = data['number']
        data['number'] = self.generate_payment_number(number_template, timesheet, payment_data)
        data['assigned_buyer'] = self.payment_approver
        data['hours_worked'] = decimal.Decimal(timesheet.minutes_total / 60)

        budget_rate_per_time_unit = data.pop('budget_rate_per_time_unit', None)
        if budget_rate_per_time_unit:
            data['amount_to_invoice'] = round_decimal(budget_rate_per_time_unit * decimal.Decimal(timesheet.minutes_total / 60))
            data['hourly_rate'] = budget_rate_per_time_unit

        elif timesheet.task.has_hourly_budget_type():
            data['hourly_rate'] = timesheet.task.budget_rate_per_time_unit

        payment = self.strategy.generate_payment(timesheet, data)
        if payment:
            BuyerAddedPaymentForTask(vendor=timesheet.vendor, payment=payment, task=timesheet.task)

        return payment

    @staticmethod
    def approve_invoices(user, invoice_ids: list[str]) -> None:
        bulk_action_result = approve_invoices(PaymentsActor(user=user), invoice_ids)
        if bulk_action_result.status != ServiceResultStatus.SUCCESS:
            # Those situations shouldn't normally happen, but they are possible
            # at least for race conditions. Collect them for better visibility and to see scale.
            # For now they are reported as errors, but maybe that will be to verbose.
            for action_result in bulk_action_result.errors:
                logger.error(
                    "Invoice meant to be approved, but failed due to validation errors. Invoice id: %s.",
                    action_result.invoice_id,
                )

    @staticmethod
    def reject_invoices(user, invoice_ids: list[str], rejection_reason: str) -> None:
        bulk_action_result = reject_invoices(PaymentsActor(user=user), invoice_ids, reason=rejection_reason)
        if bulk_action_result.status != ServiceResultStatus.SUCCESS:
            # Those situations shouldn't normally happen, but they are possible
            # at least for race conditions. Collect them for better visibility and to see scale.
            # For now they are reported as errors, but maybe that will be to verbose.
            for action_result in bulk_action_result.errors:
                logger.error(
                    "Invoice meant to be rejected, but failed due to validation errors. Invoice id: %s.",
                    action_result.invoice_id,
                )

    @staticmethod
    def generate_payment_number(template: str, timesheet: TaskTimeSheetPeriod, payment_data: dict[str, Any]) -> str:
        if not content_rendering.is_rendering_required(template):
            return template

        def update_context_with_custom_fields_data(data):
            if 'custom_fields_data' in data:
                context.update({f'template_field.{cf_id}': value for cf_id, value in data['custom_fields_data'].items()})

        context = content_rendering.get_context_for_timesheet(timesheet)
        update_context_with_custom_fields_data(payment_data)
        update_context_with_custom_fields_data(payment_data.get('work_item') or {})
        return render_text_content(template, context).strip()[:100]

    def get_work_item_validator(self):
        return self.strategy.work_item_validator_class()

    @property
    def payment_approver(self):
        if connection.tenant.has_features(features.PAYMENTS_ASSIGNED_BUYER):
            return get_current_user()


class TimesheetPaymentsWithLineItemsStrategy:
    work_item_validator_class = WorkItemRequiredValidator

    @classmethod
    def generate_payment(cls, timesheet: TaskTimeSheetPeriod, payment_data: dict[str, Any]) -> Payment | None:
        amount_to_invoice = payment_data.pop('amount_to_invoice', timesheet.get_total_amount())
        hours_worked = payment_data.get('hours_worked')
        hourly_rate = payment_data.get('hourly_rate')
        if amount_to_invoice > 0:
            currency = timesheet.get_currency_for_invoice()
            payment = timesheet.payments.create(
                assigned_buyer=payment_data['assigned_buyer'],
                currency=currency,
                custom_fields_data=payment_data.get('custom_fields_data'),
                number=payment_data['number'],
                related_task=timesheet.task,
                total_amount=0,
                total_amount_without_tax=0,
                vendor=timesheet.vendor,
            )
            work_item = payment_data['work_item']
            payment.work_items.create(
                display_name=f"Timesheet {timesheet.date_start.strftime('%Y-%m-%d')} - {timesheet.date_end.strftime('%Y-%m-%d')}",
                amount=amount_to_invoice,
                currency=currency,
                vendor=timesheet.vendor,
                work_type=WorkType.default_work_type(),
                quantity=hours_worked,
                unit_price=round_decimal(hourly_rate) if hourly_rate else None,
                **work_item,
            )
            return payment
