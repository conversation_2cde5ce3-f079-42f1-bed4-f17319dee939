# Generated by Django 3.1.14 on 2023-01-12 09:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0033_onetimetoken_expiration'),
    ]

    operations = [
        migrations.AlterField(
            model_name='userpreference',
            name='key',
            field=models.CharField(choices=[('DATA_TABLE_ALL_TASKS', 'DataTable All tasks'), ('DATA_TABLE_TASKS_IN_PROJECT', 'DataTable Tasks in projects'), ('DATA_TABLE_TASK_GROUPS', 'DataTable Tasks groups'), ('DATA_TABLE_TASKS_IN_VENDOR_PROFILE', 'DataTable Tasks in Vendor profile'), ('DATA_TABLE_TASKS_IN_JOB_OPENINGS', 'DataTable Tasks in Job Openings'), ('SEARCH_GRID_CONFIG', 'SearchGrid')], db_index=True, max_length=200),
        ),
    ]
