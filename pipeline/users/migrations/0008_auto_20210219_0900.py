# -*- coding: utf-8 -*-
# Generated by Django 1.9.13 on 2021-02-19 09:00
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0007_userrestoresubscription'),
    ]

    operations = [
        migrations.AlterField(
            model_name='actor',
            name='actor_type',
            field=models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5), (6, 6)], db_index=True),
        ),
        migrations.AlterField(
            model_name='custompermission',
            name='condition',
            field=models.CharField(choices=[(b'all', b'all'), (b'any', b'any'), (b'assigned to task from group', b'assigned to task from group'), (b'assigned to task', b'assigned to task'), (b'author', b'author'), (b'document creator', b'document creator'), (b'member and draft', b'member and draft'), (b'milestone approver', b'milestone approver'), (b'onboarding template manager', b'onboarding template manager'), (b'open to team', b'open to team'), (b'project member and draft', b'project member and draft'), (b'project member', b'project member'), (b'related invoices', b'related invoices'), (b'review author', b'review author'), (b'task group member', b'task group member'), (b'task manager', b'task manager'), (b'timesheets approver', b'timesheets approver'), (b'this user in custom field', b'this user in custom field'), (b'this user', b'this user'), (b'this vendor', b'this vendor'), (b'vendor feature bank details', b'vendor feature bank details'), (b'vendor feature tax information', b'vendor feature tax information'), (b'vendor in project', b'vendor in project'), (b'vendor relationship manager', b'vendor relationship manager'), (b'vendor marketplace manager', b'vendor marketplace manager'), (b'vendor supplier', b'vendor supplier')], default='all', max_length=100),
        ),
    ]
