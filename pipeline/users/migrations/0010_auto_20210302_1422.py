# -*- coding: utf-8 -*-
# Generated by Django 1.9.13 on 2021-03-02 14:22
from __future__ import unicode_literals

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0009_auto_20210219_0906'),
    ]

    operations = [
        migrations.AlterField(
            model_name='userrolesetting',
            name='setting',
            field=models.CharField(choices=[(b'redirect_to_distraction_free_mode', b'Redirect to Distraction-Free mode'), (b'hide_onboarding_tab', b'Hide OnBoarding tab'), (b'hide_show_only_my_stages', b"Hide 'Show only my stages' checkbox"), (b'hide_job_opening_location', b'Hide Location in Job Openings'), (b'hide_job_opening_approvers', b'Hide Approvers in Job Openings'), (b'hide_job_opening_description', b'Hide Description in Job Openings'), (b'hide_job_opening_custom_fields', b'Hide custom fields in Job Openings'), (b'hide_profile_introduction', b'Hide profile introduction'), (b'hide_profile_titles', b'Hide profile titles'), (b'hide_profile_skills', b'Hide profile skills'), (b'hide_profile_rate_section', b'Hide profile rate section'), (b'hide_profile_cover_photo', b'Hide profile cover photo'), (b'hide_profile_phone', b'Hide profile phone'), (b'hide_profile_location', b'Hide profile location'), (b'hide_profile_working_hours', b'Hide profile working hours'), (b'hide_profile_facebook', b'Hide profile facebook link'), (b'hide_profile_twitter', b'Hide profile twitter link'), (b'hide_profile_linkedin', b'Hide profile linkedin link'), (b'hide_profile_vimeo', b'Hide profile vimeo link'), (b'hide_profile_youtube', b'Hide profile youtube link'), (b'hide_profile_behance', b'Hide profile behance link'), (b'hide_profile_dribbble', b'Hide profile dribbble link'), (b'hide_profile_instagram', b'Hide profile instagram link'), (b'hide_profile_pinterest', b'Hide profile pinterest link'), (b'hide_profile_blog', b'Hide profile blog link'), (b'hide_profile_website', b'Hide profile website link')], max_length=150),
        ),
    ]
