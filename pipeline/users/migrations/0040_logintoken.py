# Generated by Django 3.2.19 on 2023-09-11 14:33

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import shortlist.utils


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0039_alter_userpreference_key'),
    ]

    operations = [
        migrations.CreateModel(
            name='LoginToken',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.CharField(default=shortlist.utils.generate_token, max_length=100, unique=True)),
                ('expiration', models.DateTimeField()),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='login_tokens', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
