from shortlist.tests.helpers import TenantTestCase
from users.models import User
from users.service.guest import create_guest, get_guest_by_email, is_email_available, GuestUserCreateDTO
from users.tests.factories import create_user


class GuestServiceTest(TenantTestCase):
    def setUp(self):
        super().setUp()
        self.email = "<EMAIL>"
        self.first_name = "Test"
        self.last_name = "User"

    def test_create_guest_success(self):
        # given
        data = GuestUserCreateDTO(email=self.email, first_name=self.first_name, last_name=self.last_name)

        # when
        guest = create_guest(data).unwrap()

        # then
        self.assertEqual(guest.email, self.email)
        self.assertEqual(guest.first_name, self.first_name)
        self.assertEqual(guest.last_name, self.last_name)
        self.assertTrue(guest.is_guest)

        # verify user was created in database
        db_user = User.objects.get(email=self.email)
        self.assertEqual(db_user.email, self.email)
        self.assertEqual(db_user.first_name, self.first_name)
        self.assertEqual(db_user.last_name, self.last_name)
        self.assertTrue(db_user.is_guest)

    def test_create_guest_with_existing_email_fails(self):
        # given
        User.objects.create(email=self.email, first_name="Existing", last_name="User")
        data = GuestUserCreateDTO(email=self.email, first_name=self.first_name, last_name=self.last_name)

        # when
        result = create_guest(data)

        # then
        self.assertFalse(result.is_successful)
        self.assertEqual(result.errors, ["User with this email already exists"])

    def test_get_guest_by_email_success(self):
        # given
        create_guest(dict(email=self.email, first_name=self.first_name, last_name=self.last_name))

        # when
        guest = get_guest_by_email(self.email)

        # then
        self.assertIsNotNone(guest)
        self.assertEqual(guest.email, self.email)
        self.assertEqual(guest.first_name, self.first_name)
        self.assertEqual(guest.last_name, self.last_name)
        self.assertTrue(guest.is_guest)

    def test_get_guest_by_email_not_found(self):
        # when
        guest = get_guest_by_email("<EMAIL>")

        # then
        self.assertIsNone(guest)

    def test_get_guest_by_email_not_guest(self):
        # given
        create_user("vendor", email=self.email, first_name=self.first_name, last_name=self.last_name)

        # when
        guest = get_guest_by_email(self.email)

        # then
        self.assertIsNone(guest)

    def test_is_email_available_for_guest_new_email(self):
        # when
        is_available = is_email_available(self.email)

        # then
        self.assertTrue(is_available)

    def test_is_email_available_for_guest_existing_guest(self):
        # given
        create_guest(dict(email=self.email, first_name=self.first_name, last_name=self.last_name))

        # when
        is_available = is_email_available(self.email)

        # then
        self.assertFalse(is_available)
