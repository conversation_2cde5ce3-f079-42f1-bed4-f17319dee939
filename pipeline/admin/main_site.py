import logging

from django.conf import settings
from django.core.management import call_command
from django.db import connection
from django.db.models import Sum
from django.http import HttpResponse, HttpResponseRedirect
from django.views.generic import CreateView, ListView, RedirectView, UpdateView, View
from django.views.generic.detail import BaseDetailView, SingleObjectMixin
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from admin.expand import tasks
from admin.forms import ClientAddForm, ClientDomainFormSet, ClientEditForm
from clients import features
from clients.models import Client, UserEmail
from clients.serializers import ExportSerializer, NewsletterSerializer
from clients.tasks import UpdateClient
from shortlist.track_tasks import start_task_tracking
from shortlist.utils import HeaderedList

# Authentication for classes here handled in admin/main_urls.py
from tenant_schemas.urlresolvers import reverse, reverse_lazy

logger = logging.Logger("admin")


class FeaturesHint:
    def __init__(self):
        self.always_on = settings.FEATURES_FORCED_ON
        self.always_off = settings.FEATURES_FORCED_OFF

    def lines(self):
        result = []
        for state, f in [("on", self.always_on), ("off", self.always_off)]:
            if f:
                result.append("Always {}: {}".format(state, ", ".join(sorted(features.DESCRIPTIONS[x] for x in f))))
        return result

    def __str__(self):
        return "<br>".join(self.lines())


class AdminTenantsListView(ListView):
    template_name = "main_admin/tenants_list.html"
    columns = [
        ("Reference", "reference", ""),
        ("Company", "name", ""),
        ("Action", None, ""),
        ("Status", "active", "Active/Inactive"),
        ("Domain", "domain_prefix", ""),
        ("Owner", "owner_full_name", "Owner name and email"),
        ("CS", "customer_success", "Customer success person"),
        ("Current plan", "current_plan", ""),
        ("Users", "all_users_count", "Registered buyer count"),
        ("Active users", "active_users_count", "Buyers active in last week"),
        ("Partners", "all_vendors_count", "All partner count"),
        ("Registered partners", "registered_vendors_count", "Registered partner count"),
        ("Active partners", "active_vendors_count", "Partners active in last week"),
        ("Projects", "projects_total", "Non deleted projects"),
        ("Last active", "client_activity__last_active", "Last activity, including staff"),
        ("Member since", "member_since", ""),
        ("Currency", "currency", ""),
        ("Features", None, FeaturesHint()),
    ]
    default_order = "-client_activity__last_active"

    def get_order(self):
        return self.request.session.get("admin_list_order", self.default_order)

    def set_order(self, value):
        self.request.session["admin_list_order"] = value

    list_order = property(get_order, set_order)

    def get_at_worksuite_hidden(self):
        return self.request.session.get("at_worksuite_hidden", False)

    def set_at_worksuite_hidden(self, value):
        self.request.session["at_worksuite_hidden"] = value

    set_at_worksuite_hidden = property(get_at_worksuite_hidden, set_at_worksuite_hidden)

    def get_show_tenants_to_clone(self):
        return self.request.session.get("show_tenants_to_clone", False)

    def set_show_tenants_to_clone(self, value):
        self.request.session["show_tenants_to_clone"] = value

    show_tenants_to_clone = property(get_show_tenants_to_clone, set_show_tenants_to_clone)

    def get_queryset(self):
        order = self.list_order
        queryset = Client.tenants
        if self.set_at_worksuite_hidden:
            queryset = queryset.exclude(owner_email__iendswith="@worksuite.com")
        if self.show_tenants_to_clone:
            queryset = queryset.filter(allow_clone=True)
        if order:
            try:
                queryset = queryset.order_by(order)
            except Exception:
                # clear invalid ordering
                self.list_order = None
        return queryset

    def get_context_data(self, **kwargs):
        ctx = super().get_context_data()
        qs = self.get_queryset()

        counts = dict(
            qs.aggregate(
                all_users=Sum("all_users_count"),
                active_users=Sum("active_users_count"),
                all_vendors=Sum("all_vendors_count"),
                registered_vendors=Sum("registered_vendors_count"),
                active_vendors=Sum("active_vendors_count"),
                projects=Sum("projects_total"),
            )
        )
        counts["all_companies"] = qs.count()
        counts["active_companies"] = qs.filter(active=True).count()
        counts["inactive_companies"] = qs.count() - counts["active_companies"]
        counts["inactive_users"] = (counts.get("all_users") or 0) - (counts.get("active_users") or 0)

        totals = {
            "Company": counts["all_companies"],
            "Status": "{}/{}".format(counts["active_companies"], counts["inactive_companies"]),
            "Users": counts["all_users"],
            "Active users": counts["active_users"],
            "Partners": counts["all_vendors"],
            "Registered partners": counts["registered_vendors"],
            "Active partners": counts["active_vendors"],
            "Projects": counts["projects"],
        }

        columns = []
        order = self.list_order

        def marked(f, desc):
            if f is not None:
                if desc:
                    f = "-" + f
                if f == order:
                    return "active-sort"
            return ""

        for column, field, hint in self.columns:
            columns.append(
                [
                    column,  # column header
                    f"({totals[column]})" if column in totals else "",
                    field is not None,  # show sorting arrows?
                    field or "",  # field name
                    marked(field, False),  # up arrow active?
                    marked(field, True),  # down arrow active
                    hint,
                ]
            )
        ctx["columns"] = columns
        ctx["at_worksuite_hidden"] = self.set_at_worksuite_hidden
        ctx["show_tenants_to_clone"] = self.show_tenants_to_clone
        return ctx

    def post(self, request, *args, **kwargs):
        try:
            self.set_at_worksuite_hidden = request.POST["at_worksuite_hidden"] == "on"
        except KeyError:
            pass
        try:
            self.show_tenants_to_clone = request.POST["show_tenants_to_clone"] == "on"
        except KeyError:
            pass
        try:
            self.list_order = request.POST["admin_list_order"]
        except KeyError:
            pass
        return self.get(request, *args, **kwargs)


class AdminTenantsDelete(BaseDetailView):
    queryset = Client.tenants.all()

    def get(self, request, *args, **kwargs):
        start_task_tracking()
        obj = self.get_object()
        tasks.tenant_delete.delay(obj.domain_prefix)
        return HttpResponse("Delete started", status=status.HTTP_202_ACCEPTED)


class TenantViewMixin:
    template_name = "main_admin/tenant_edit.html"
    model = Client

    def get_context_data(self, **kwargs):
        result = super().get_context_data(**kwargs)
        result["client_domains_formset"] = (
            kwargs['inline_formset'] if 'inline_formset' in kwargs else ClientDomainFormSet(instance=self.object)
        )
        result["feature_hints"] = FeaturesHint()
        result["action"] = self.button_name
        return result

    def get_success_url(self):
        return "{}#domain-{}".format(reverse("tenants"), self.object.domain_prefix)


class AdminTenantEditView(TenantViewMixin, UpdateView):
    button_name = "Update"
    form_class = ClientEditForm

    def post(self, request, *args, **kwargs):
        self.object = self.get_object()
        form = self.get_form()
        domains_formset = ClientDomainFormSet(data=self.request.POST, instance=self.object)
        if form.is_valid() and domains_formset.is_valid():
            return self._form_valid(form, domains_formset)
        else:
            return self._form_invalid(form, domains_formset)

    def _form_valid(self, form, inline_formset):
        # quick solution to get edits in server logs
        logger.info("TENANT EDIT by %s, saved tenant %s: %s", self.request.user.email, self.object.domain_url, form.data)
        result = super().form_valid(form)
        domains = inline_formset.save()
        for domain in domains:
            domain.client = self.object
            domain.save()
        return result

    def _form_invalid(self, form, inline_formset):
        return self.render_to_response(self.get_context_data(form=form, inline_formset=inline_formset))


class AdminTenantsAddView(TenantViewMixin, CreateView):
    button_name = "Create"
    form_class = ClientAddForm

    def post(self, request, *args, **kwargs):
        self.object = None
        form = self.get_form()
        domains_formset = ClientDomainFormSet(data=self.request.POST)
        if form.is_valid() and domains_formset.is_valid():
            return self._form_valid(form, domains_formset)
        else:
            return self._form_invalid(form, domains_formset)

    def _form_valid(self, form, inline_formset):
        # quick solution to get edits in server logs
        logger.info(("TENANT ADD by %s, saved tenant %s", self.request.user.email, form.data))
        result = super().form_valid(form)
        domains = inline_formset.save(commit=False)
        for domain in domains:
            domain.client = self.object
            domain.save()
        return result

    def _form_invalid(self, form, inline_formset):
        return self.render_to_response(self.get_context_data(form=form, inline_formset=inline_formset))


class AdminTenantsRefreshView(RedirectView):
    url = reverse_lazy("tenants")

    def get(self, request, *args, **kwargs):
        update_client = UpdateClient()
        update_client.run()
        return super().get(request, *args, **kwargs)


class AdminTenantsBackup(BaseDetailView):
    queryset = Client.tenants.all()

    def get(self, request, *args, **kwargs):
        start_task_tracking()
        obj = self.get_object()
        tasks.tenant_backup.delay(obj.domain_prefix)
        return HttpResponse("Backup started", status=status.HTTP_202_ACCEPTED)


class AdminTenantsClone(SingleObjectMixin, View):
    queryset = Client.tenants.filter(allow_clone=True)

    def post(self, request, *args, **kwargs):
        obj = self.get_object()
        new_tenant_prefix = request.POST.get('prefix')
        call_command("tenant_clone", obj.domain_prefix, new_tenant_prefix)
        return HttpResponse("Tenant cloned successfully", status=status.HTTP_200_OK)


class AdminTenantRestore(ListView):
    template_name = "main_admin/tenant_restore.html"

    def get_queryset(self):
        tenant = self.request.GET["tenant"]
        from shortlist.backups import Backups

        backups = Backups().list_backups(tenant=tenant)
        if backups:
            backups = [{"name": s, "display_name": s.replace("/" + tenant, "")} for s in backups]
            backups.reverse()
        else:
            backups = []
        return backups

    def get_context_data(self, **kwargs):
        context = super().get_context_data(tenant=self.request.GET["tenant"])
        return context


class FeaturesForAllView(ListView):
    template_name = "main_admin/features_for_all.html"

    def get_queryset(self):
        return [
            {
                "name": key,
                "description": description,
                "can_enable_for": Client.tenants.with_feature_setting(key, False).count(),
                "can_disable_for": Client.tenants.with_feature_setting(key, True).count(),
                "forced": self.is_forced(key),
            }
            for key, description in features.SORTED_DESCRIPTIONS
        ]

    def is_forced(self, feature):
        if feature in settings.FEATURES_FORCED_ON:
            return "Always on"
        elif feature in settings.FEATURES_FORCED_OFF:
            return "Always off"

    def post(self, request, *args, **kwargs):
        data = set(request.POST.keys())
        data.discard("csrfmiddlewaretoken")
        assert len(data) == 1
        feature, toggle = data.pop().rsplit("_", 1)
        assert feature in list(features.DESCRIPTIONS.keys())
        self.switch_feature(feature, toggle == "enable")
        return HttpResponseRedirect(reverse("tenants-features-for-all"))

    def switch_feature(self, feature, enable):
        for client in Client.tenants.with_feature_setting(feature, not enable):
            client.switch_feature(feature, enable)
            client.save()


class AdminTenantStartRestore(APIView):
    # handled by check_staff_user
    authentication_classes = []
    permission_classes = []
    success_message = (
        "Success: Restore completed.\n"
        + "Following features are automatically disabled on restore if they were enabled before: {}".format(
            ", ".join(features.DESCRIPTIONS[x] for x in features.DISABLE_ON_RESTORE)
        )
    )

    def get(self, request, *args, **kwargs):
        backup = request.GET["backup"]
        start_task_tracking()
        tasks.tenant_restore.delay(backup=backup)
        return HttpResponse(self.success_message, status=status.HTTP_202_ACCEPTED)


class AdminTenantsExport(APIView):
    # handled by check_staff_user
    authentication_classes = []
    permission_classes = []

    def get(self, request, *args, **kwargs):
        clients = Client.tenants.all().order_by("name")
        serializer = ExportSerializer(instance=clients, many=True)
        data = HeaderedList(serializer.data, list(ExportSerializer.Meta.fields))
        return Response(data)


class NewsletterSubscribers(APIView):
    # handled by check_staff_user
    authentication_classes = []
    permission_classes = []

    def get(self, request, *args, **kwargs):
        emails = UserEmail.objects.filter(accepted_newsletter=True).order_by("address")
        serializer = NewsletterSerializer(instance=emails, many=True)
        data = HeaderedList(serializer.data, list(NewsletterSerializer.Meta.fields))
        return Response(data)


class _TenantInfoView(APIView):
    # handled by check_staff_user
    authentication_classes = []
    permission_classes = []
    tenant = None

    def get_tenant(self, request, *args, **kwargs):
        return Client.objects.get(id=int(kwargs["tenant_id"]))

    def get(self, request, *args, **kwargs):
        self.tenant = self.get_tenant(request, *args, **kwargs)
        connection.set_tenant(self.tenant)
        return Response(self.get_data())


class TenantActiveUsers(_TenantInfoView):
    def get_data(self):
        from users.models import User
        from users.serializers import BaseUserSerializer

        qs = User.objects.buyers().active_in_last_7_days().order_by("email")
        serializer = BaseUserSerializer(instance=qs, many=True, fields=("email", "full_name"))
        return serializer.data


class TenantActiveVendors(_TenantInfoView):
    def get_data(self):
        from users.models import User
        from vendors.serializers import VendorMinimalSerializer

        qs = User.objects.vendors().active_in_last_7_days().order_by("email")
        serializer = VendorMinimalSerializer(instance=[x.vendor for x in qs], many=True, fields=("email", "full_name"))
        return serializer.data
