from datetime import timed<PERSON><PERSON>
from unittest import mock

from django.conf import settings
from django.contrib.auth.models import AnonymousUser
from django.db import connection
from django.utils.http import int_to_base36
from freezegun import freeze_time
from parameterized import parameterized
from rest_framework import status

from admin.tests import AdminTestCase
from clients import features
from clients.models import Client
from shortlist.models import StaffUser
from shortlist.tests.helpers import create_api_client
from users.models import User


class AdminAPIStaffTests(AdminTestCase):
    def setUp(self):
        super().setUp()
        self.email = self.factory.email()
        StaffUser.objects.filter(email__iexact=self.email).delete()
        connection.set_tenant(self.tenant)
        User.objects.filter(email__iexact=self.email).delete()
        connection.set_schema_to_public()

    def get_user_token(self, email):
        connection.set_tenant(self.tenant)
        user = User.objects.filter(email__iexact=email).first()
        ott = user.one_time_tokens.all().first()
        connection.set_schema_to_public()
        return ott

    def test_link_for_existing_staff(self):
        StaffUser.objects.create(email=self.email, is_staff=True, is_active=True)

        response = self.check_response(
            status.HTTP_200_OK, self.api_client.post('/api/create_staff_link/', data={'email': self.email, 'client_id': self.tenant.pk})
        )

        self.assertIn(self.get_user_token(self.email).token, response.data.get('uri'))

    def test_link_for_new_staff(self):
        first_name = self.factory.first_name()
        last_name = self.factory.last_name()

        response = self.check_response(
            status.HTTP_200_OK,
            self.api_client.post(
                '/api/create_staff_link/',
                data={
                    'email': self.email,
                    'client_id': self.tenant.pk,
                    'first_name': first_name,
                    'last_name': last_name,
                },
            ),
        )

        su = StaffUser.objects.filter(email__iexact=self.email).first()
        self.assertIsNotNone(su)
        self.assertEqual(su.first_name, first_name)
        self.assertEqual(su.last_name, last_name)
        self.assertIn(self.get_user_token(self.email).token, response.data.get('uri'))

    def test_link_for_new_staff_missing_names(self):
        self.check_response(
            status.HTTP_400_BAD_REQUEST,
            self.api_client.post(
                '/api/create_staff_link/',
                data={
                    'email': self.email,
                    'client_id': self.tenant.pk,
                },
            ),
        )

    def test_onetime_login_with_link(self):
        response = self.check_response(
            status.HTTP_200_OK,
            self.api_client.post(
                '/api/create_staff_link/',
                data={
                    'email': self.email,
                    'client_id': self.tenant.pk,
                    'first_name': self.factory.first_name(),
                    'last_name': self.factory.last_name(),
                },
            ),
        )

        login_link_path = response.data.get('uri')
        first_try = self.check_response(status.HTTP_302_FOUND, create_api_client(self.tenant.domain_url).get(login_link_path))
        self.assertEqual(first_try.url, '/')
        self.assertEqual(first_try.wsgi_request.user.email, self.email)
        self.assertTrue(first_try.wsgi_request.user.is_staff)

        second_try = self.check_response(status.HTTP_302_FOUND, create_api_client(self.tenant.domain_url).get(login_link_path))
        user = User.objects.filter(email__iexact=self.email).first()
        self.assertEqual(second_try.url, f'/request-new-token/?redirect_to=&uid={int_to_base36(user.id)}')
        self.assertEqual(type(second_try.wsgi_request.user), AnonymousUser)

    def test_staff_login_to_client_with_no_staff_access(self):
        self.tenant.allow_staff_access = False
        self.tenant.save()
        response = self.check_response(
            status.HTTP_200_OK,
            self.api_client.post(
                '/api/create_staff_link/',
                data={
                    'email': self.email,
                    'client_id': self.tenant.pk,
                    'first_name': self.factory.first_name(),
                    'last_name': self.factory.last_name(),
                },
            ),
        )
        login_link_path = response.data.get('uri')
        self.check_response(status.HTTP_403_FORBIDDEN, create_api_client(self.tenant.domain_url).get(login_link_path))
        self.tenant.allow_staff_access = True
        self.tenant.save()
        self.check_response(status.HTTP_302_FOUND, create_api_client(self.tenant.domain_url).get(login_link_path))

    def test_link_expiration(self):
        with freeze_time("2023-01-09 14:00:00") as frozen_time:
            response = self.check_response(
                status.HTTP_200_OK,
                self.api_client.post(
                    '/api/create_staff_link/',
                    data={
                        'email': self.email,
                        'client_id': self.tenant.pk,
                        'first_name': self.factory.first_name(),
                        'last_name': self.factory.last_name(),
                    },
                ),
            )
            frozen_time.tick(timedelta(minutes=5))

            login_link_path = response.data.get('uri')
            login = self.check_response(status.HTTP_302_FOUND, create_api_client(self.tenant.domain_url).get(login_link_path))
            user = User.objects.filter(email__iexact=self.email).first()
            self.assertEqual(login.url, f'/request-new-token/?redirect_to=&uid={int_to_base36(user.id)}')
            self.assertEqual(type(login.wsgi_request.user), AnonymousUser)

    def test_link_expiration_custom_ttl(self):
        with freeze_time("2023-11-20 13:00:00") as frozen_time:
            response = self.check_response(
                status.HTTP_200_OK,
                self.api_client.post(
                    '/api/create_staff_link/',
                    data={
                        'email': self.email,
                        'client_id': self.tenant.pk,
                        'first_name': self.factory.first_name(),
                        'last_name': self.factory.last_name(),
                        'ttl': 400,
                    },
                ),
            )
            frozen_time.tick(timedelta(minutes=5))

            login_link_path = response.data.get('uri')
            first_try = self.check_response(status.HTTP_302_FOUND, create_api_client(self.tenant.domain_url).get(login_link_path))
            self.assertEqual(first_try.url, '/')
            self.assertEqual(first_try.wsgi_request.user.email, self.email)
            self.assertTrue(first_try.wsgi_request.user.is_staff)

    def test_link_expiration_max_ttl(self):
        with freeze_time("2023-11-20 13:00:00") as frozen_time:
            response = self.check_response(
                status.HTTP_200_OK,
                self.api_client.post(
                    '/api/create_staff_link/',
                    data={
                        'email': self.email,
                        'client_id': self.tenant.pk,
                        'first_name': self.factory.first_name(),
                        'last_name': self.factory.last_name(),
                        'ttl': settings.STAFF_LINK_TTL_MAX_SECONDS + 10,
                    },
                ),
            )
            frozen_time.tick(timedelta(seconds=settings.STAFF_LINK_TTL_MAX_SECONDS + 5))

            login_link_path = response.data.get('uri')
            login = self.check_response(status.HTTP_302_FOUND, create_api_client(self.tenant.domain_url).get(login_link_path))
            user = User.objects.filter(email__iexact=self.email).first()
            self.assertEqual(login.url, f'/request-new-token/?redirect_to=&uid={int_to_base36(user.id)}')
            self.assertEqual(type(login.wsgi_request.user), AnonymousUser)

    def test_new_link_after_expired(self):
        with freeze_time("2023-01-09 14:00:00") as frozen_time:
            self.check_response(
                status.HTTP_200_OK,
                self.api_client.post(
                    '/api/create_staff_link/',
                    data={
                        'email': self.email,
                        'client_id': self.tenant.pk,
                        'first_name': self.factory.first_name(),
                        'last_name': self.factory.last_name(),
                    },
                ),
            )
            frozen_time.tick(timedelta(minutes=5))

            self.check_response(
                status.HTTP_200_OK,
                self.api_client.post(
                    '/api/create_staff_link/',
                    data={
                        'email': self.email,
                        'client_id': self.tenant.pk,
                        'first_name': self.factory.first_name(),
                        'last_name': self.factory.last_name(),
                    },
                ),
            )

    def test_previous_link_gets_retired(self):
        first_link = self.check_response(
            status.HTTP_200_OK,
            self.api_client.post(
                '/api/create_staff_link/',
                data={
                    'email': self.email,
                    'client_id': self.tenant.pk,
                    'first_name': self.factory.first_name(),
                    'last_name': self.factory.last_name(),
                },
            ),
        )

        second_link = self.check_response(
            status.HTTP_200_OK,
            self.api_client.post(
                '/api/create_staff_link/',
                data={
                    'email': self.email,
                    'client_id': self.tenant.pk,
                    'first_name': self.factory.first_name(),
                    'last_name': self.factory.last_name(),
                },
            ),
        )

        first_try = self.check_response(status.HTTP_302_FOUND, create_api_client(self.tenant.domain_url).get(first_link.data.get('uri')))
        user = User.objects.filter(email__iexact=self.email).first()
        self.assertEqual(first_try.url, f'/request-new-token/?redirect_to=&uid={int_to_base36(user.id)}')
        self.assertEqual(type(first_try.wsgi_request.user), AnonymousUser)

        second_try = self.check_response(status.HTTP_302_FOUND, create_api_client(self.tenant.domain_url).get(second_link.data.get('uri')))
        self.assertEqual(second_try.url, '/')
        self.assertEqual(second_try.wsgi_request.user.email, self.email)
        self.assertTrue(second_try.wsgi_request.user.is_staff)


class AdminAPITenantsTests(AdminTestCase):
    tenant_api = "/api/tenants/"
    tenant_domains_api = "/api/tenants/{}/set_domains/"

    @mock.patch('clients.models.Client.create_schema', mock.Mock())
    def test_listing_tenants(self):
        tenant = dict(domain_prefix="a0", name="azero", schema_name="a_a0")
        Client.objects.create(**tenant)

        response = self.check_response(status.HTTP_200_OK, self.api_client.get(self.tenant_api))
        self.assertEqual(
            dict(
                domain_prefix=response.data[1].get('domain_prefix'),
                name=response.data[1].get('name'),
                schema_name=response.data[1].get('schema_name'),
            ),
            tenant,
        )

    def test_missing_data(self):
        response = self.check_response(status.HTTP_400_BAD_REQUEST, self.api_client.post(self.tenant_api, data=dict(name="t1")))
        self.assertEqual(response.data, dict(current_plan=['This field is required.'], domain_prefix=['This field is required.']))

    @mock.patch('clients.models.Client.create_schema', mock.Mock())
    def test_update_tenant(self):
        tenant = Client.objects.create(domain_prefix="t0", name="zero", schema_name="t_t0")

        self.assertEqual(tenant.current_plan, '')
        response = self.check_response(
            status.HTTP_200_OK,
            self.api_client.patch(f"{self.tenant_api}{tenant.pk}/", data=dict(current_plan="demo")),
        )
        self.assertEqual(response.data.get("current_plan"), "demo")

    @mock.patch('clients.models.Client.create_schema', mock.Mock())
    @mock.patch('clients.models.Client.setup_first_user')
    def test_create_tenant_with_user(self, setup_first_user_mock):
        StaffUser.objects.create(email="<EMAIL>")
        tenant = dict(
            name="one",
            domain_prefix="t1",
            current_plan="internal",
            owner_email="<EMAIL>",
            owner_full_name="Test User",
            country="Poland",
            customer_success="<EMAIL>",
        )

        response = self.check_response(status.HTTP_201_CREATED, self.api_client.post(self.tenant_api, data=tenant))

        del tenant['country']  # country is write-only field
        self.assertEqual(
            dict(
                domain_prefix=response.data.get('domain_prefix'),
                name=response.data.get('name'),
                current_plan=response.data.get('current_plan'),
                owner_email=response.data.get('owner_email'),
                owner_full_name=response.data.get('owner_full_name'),
                customer_success=response.data.get('customer_success'),
            ),
            tenant,
        )

        setup_first_user_mock.assert_called_once_with(
            {
                'name': 'one',
                'current_plan': 'internal',
                'owner_full_name': 'Test User',
                'owner_email': '<EMAIL>',
                'domain_prefix': 't1',
                'customer_success': '<EMAIL>',
                'country': 'Poland',
            }
        )

    def test_setup_first_user(self):
        client = Client.objects.first()
        client.setup_first_user(
            {
                'name': 'one',
                'current_plan': 'internal',
                'owner_full_name': 'Test User',
                'owner_email': '<EMAIL>',
                'domain_prefix': 't1',
                'customer_success': '<EMAIL>',
                'country': 'Poland',
            }
        )

        self.assertTrue(User.objects.filter(email='<EMAIL>').exists())

    def test_tenant_set_domain(self):
        client = Client.objects.first()
        self.check_response(
            status.HTTP_200_OK, self.api_client.post(self.tenant_domains_api.format(client.pk), data=dict(domains=['test_domain']))
        )

        self.assertEqual(['test_domain'], [d.domain for d in client.client_domains.all()])

    def test_tenant_set_domains_with_default(self):
        client = Client.objects.first()
        self.check_response(
            status.HTTP_200_OK,
            self.api_client.post(self.tenant_domains_api.format(client.pk), data=dict(domains=['test1', 'test2'], default_domain='test2')),
        )

        self.assertEqual({'test1', 'test2'}, {d.domain for d in client.client_domains.all()})
        self.assertEqual('test2', client.default_domain)

    def test_tenant_set_domains_with_default_not_in_domains(self):
        client = Client.objects.first()
        response = self.check_response(
            status.HTTP_400_BAD_REQUEST,
            self.api_client.post(self.tenant_domains_api.format(client.pk), data=dict(domains=['test1', 'test2'], default_domain='test3')),
        )

        self.assertEqual(str(response.data.get('default_domain')), 'Default domain must be one of the domains')

    def test_tenant_set_domains_override_existing_domains(self):
        client = Client.objects.first()
        client.client_domains.create(domain='test1')

        self.check_response(
            status.HTTP_200_OK,
            self.api_client.post(self.tenant_domains_api.format(client.pk), data=dict(domains=['test2', 'test3'])),
        )

        self.assertEqual({'test2', 'test3'}, {d.domain for d in client.client_domains.all()})

    def test_tenant_set_domains_removal(self):
        client = Client.objects.first()
        client.client_domains.create(domain='test1')
        client.client_domains.create(domain='test2')

        self.check_response(
            status.HTTP_200_OK,
            self.api_client.post(self.tenant_domains_api.format(client.pk), data=dict(domains=[])),
        )

        self.assertEqual([], [d.domain for d in client.client_domains.all()])

    def test_tenant_set_domains_empty(self):
        client = Client.objects.first()

        response = self.check_response(
            status.HTTP_400_BAD_REQUEST,
            self.api_client.post(self.tenant_domains_api.format(client.pk), data={}),
        )

        self.assertEqual(str(response.data.get('domains')), 'This field is required')

    @parameterized.expand(
        [
            ({}, status.HTTP_202_ACCEPTED, False),
            ({"reset": None}, status.HTTP_202_ACCEPTED, False),
            ({"reset": "true"}, status.HTTP_202_ACCEPTED, True),
            ({"reset": True}, status.HTTP_202_ACCEPTED, True),
            ({"reset": "false"}, status.HTTP_202_ACCEPTED, False),
            ({"reset": "BAD!"}, status.HTTP_400_BAD_REQUEST, None),
        ]
    )
    @mock.patch('clients.models.Client.create_schema', mock.Mock())
    @mock.patch('integrations.tasks.create_shortlist_pay_integration_if_needed')
    def test_setup_pay(self, request_data, expected_status, expected_force_recreate, setup_pay_task_mock):
        tenant = Client.objects.create(domain_prefix="t0", name="zero", schema_name="t_t0")

        self.check_response(expected_status, self.api_client.post(f"{self.tenant_api}{tenant.id}/setup_pay/", data=request_data))
        if expected_force_recreate is None:
            setup_pay_task_mock.delay.assert_not_called()
        else:
            setup_pay_task_mock.delay.assert_called_once_with(tenant.id, force_recreate=expected_force_recreate)

    def test_update_tenant_features_with_conflicts(self):
        client = Client.objects.first()

        conflicting_features = [features.CONTRACTS, features.CONTRACTS_SOON]
        response = self.check_response(
            status.HTTP_400_BAD_REQUEST, self.api_client.patch(f"{self.tenant_api}{client.pk}/", data=dict(features=conflicting_features))
        )
        self.assertEqual(response.data.get('features').get('conflicts'), features.check_feature_conflicts(conflicting_features))

        non_conflicting_features = [features.TASKS_TIME_SHEET, features.CONTRACTS]
        response = self.check_response(
            status.HTTP_200_OK, self.api_client.patch(f"{self.tenant_api}{client.pk}/", data=dict(features=non_conflicting_features))
        )
        self.assertEqual(set(response.data.get('features')), set(non_conflicting_features))
