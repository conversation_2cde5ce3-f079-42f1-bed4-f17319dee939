from dashboard.models import DashboardAnnouncement
from dashboard.tests.factories import DashboardAnnouncementFactory
from shortlist.tests.helpers import TenantTestCase
from vendors.tests.factories import VendorGroupFactory


class DashboardAnnouncementPoliciesTests(TenantTestCase):
    def test_deleting_vendor_group_sets_dashboard_announcement_as_draft(self):
        # given
        vendor_group = VendorGroupFactory()
        dashboard_announcement = DashboardAnnouncementFactory(vendor_group=vendor_group, on_feed=True)
        # when
        vendor_group.delete()
        # then
        dashboard_announcement.refresh_from_db()
        self.assertEqual(dashboard_announcement.status, DashboardAnnouncement.Status.DRAFT.value)
        self.assertIsNone(dashboard_announcement.vendor_group_id)
