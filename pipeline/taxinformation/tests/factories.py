from enum import Enum


import us
from faker import Factory

from shortlist.views import CANADA_PROVINCES
from taxinformation import tax_classifications
from taxinformation.models import TaxInformation, TaxInformationRequirementPostpone
from taxinformation.tax_information_for_bank_details import get_grace_period_end_date
from taxinformation.values import OTHER_COUNTRIES

factory = Factory.create()


class TaxType:
    BUSINESS = 'business'
    INDIVIDUAL = 'individual'


class TaxCategory(Enum):
    US_BUSINESS = (TaxType.BUSINESS, 'United States')
    US_INDIVIDUAL = (TaxType.INDIVIDUAL, 'United States')
    CANADA_BUSINESS = (TaxType.BUSINESS, 'Canada')
    CANADA_INDIVIDUAL = (TaxType.INDIVIDUAL, 'Canada')
    OTHER_BUSINESS = (TaxType.BUSINESS, 'Other')
    OTHER_INDIVIDUAL = (TaxType.INDIVIDUAL, 'Other')


class IdNumberPattern:
    US_SSN = factory.bothify('###-##-####')
    US_EIN = factory.bothify('##-#######')
    US_TI = factory.bothify('### ####')
    CANADA_EIN = factory.bothify('#########-##-####')
    CANADA_SIN = factory.bothify('### ### ###')
    CANADA_TI = factory.bothify('### ####')
    OTHER_SSN = factory.bothify('### ### ###')
    OTHER_EIN = factory.bothify('##-#######')
    OTHER_TI = factory.bothify('??###########')
    NATIONAL_ID = factory.bothify('###')


def get_us_data():
    return {
        'country': 'United States',
        'residential_country': 'United States',
        'state': factory.random_element({s.abbr for s in us.STATES}),
        'postal_code': factory.postcode(),
        'tax_classification': factory.random_element(tax_classifications.onlyForUs()),
        'w9_consent': True,
        'w9_signed_by': factory.last_name(),
        'w9_signed_at': factory.date_time().isoformat(),
    }


def get_canada_data():
    return {
        'country': 'Canada',
        'residential_country': 'Canada',
        'state': factory.random_element(list(CANADA_PROVINCES.keys())),
        'postal_code': factory.bothify('?#? #?#'),
    }


def get_other_country_data():
    random_country = factory.random_element(OTHER_COUNTRIES)
    return {
        'country': random_country,
        'residential_country': random_country,
        'state': factory.city(),
        'postal_code': factory.postcode(),
        'tax_identifier': IdNumberPattern.OTHER_TI,
    }


def get_business_data():
    return {
        'type': 'business',
        'company_name': factory.name(),
        'employer_identyfication_number': IdNumberPattern.US_EIN,
    }


def get_individual_data(tax_category):
    data = {
        'type': 'individual',
        'first_name': factory.first_name(),
        'last_name': factory.last_name(),
        'national_id': IdNumberPattern.NATIONAL_ID,
    }
    if tax_category == TaxCategory.CANADA_INDIVIDUAL:
        data['national_id'] = IdNumberPattern.CANADA_SIN
        # Not best-looking trick, but allowes to correctly create a TaxInformation object programmatically
        data['social_security_number'] = data['national_id']
    elif tax_category == TaxCategory.US_INDIVIDUAL:
        data['tax_identifier'] = IdNumberPattern.US_SSN
        # Same as above
        data['social_security_number'] = data['tax_identifier']
    else:
        data['tax_identifier'] = IdNumberPattern.OTHER_TI

    return data


def correct_tax_data(tax_category: TaxCategory, **updates):
    data = {'address': factory.address(), 'city': factory.city(), 'w9_consent': True}

    if tax_category in (TaxCategory.US_BUSINESS, TaxCategory.US_INDIVIDUAL):
        data.update(get_us_data())
        if tax_category == TaxCategory.US_INDIVIDUAL:
            data['tax_classification'] = 'individual'
    elif tax_category in (TaxCategory.CANADA_BUSINESS, TaxCategory.CANADA_INDIVIDUAL):
        data.update(get_canada_data())
    elif tax_category in (TaxCategory.OTHER_BUSINESS, TaxCategory.OTHER_INDIVIDUAL):
        data.update(get_other_country_data())

    if tax_category in (TaxCategory.US_BUSINESS, TaxCategory.CANADA_BUSINESS, TaxCategory.OTHER_BUSINESS):
        data.update(get_business_data())
        if tax_category == TaxCategory.CANADA_BUSINESS:
            data['employer_identyfication_number'] = IdNumberPattern.CANADA_EIN
    elif tax_category in (TaxCategory.US_INDIVIDUAL, TaxCategory.CANADA_INDIVIDUAL, TaxCategory.OTHER_INDIVIDUAL):
        data.update(get_individual_data(tax_category))

    data.update(**updates)
    return data


def create_tax_information_for_vendor(vendor, tax_category: TaxCategory | None = None):
    if tax_category is None:
        tax_category = TaxCategory.US_INDIVIDUAL
    tax, _ = TaxInformation.objects.get_or_create(vendor=vendor, defaults=correct_tax_data(tax_category, vendor=vendor))
    return tax


def create_requirement_postpone_for_vendor(vendor):
    postpone = TaxInformationRequirementPostpone.objects.create(vendor=vendor, requirement_starts_at=get_grace_period_end_date())
    return postpone
