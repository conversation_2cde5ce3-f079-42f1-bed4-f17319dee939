{"name": "shortlist-platform", "version": "0.1.0", "private": true, "repository": "https://github.com/ShortlistProject/shortlist-platform", "license": "UNLICENSED", "workspaces": ["packages/*"], "scripts": {"serve": "yarn prepare:local && concurrently \"vite --mode development\" \"webpack --watch --mode development\" \"node ./scripts/build-sass.js --watch\"", "serve:with-react": "concurrently \"yarn serve\" \"yarn workspace shortlist-react run esbuild-watch\"", "serve:dist": "yarn build:assets && vite", "build:react": "yarn workspace shortlist-react run esbuild", "build:assets": "yarn clean && yarn build:react && node ./scripts/build-ngtemplates.js && node ./scripts/build-sass.js production && node ./scripts/build-angular.js production && yarn webpack --mode production && node ./scripts/copy-files.js dist,snippets && node ./scripts/update-html-references.js", "build:production": "node ./scripts/replace.js production && node ./scripts/setup-env.js production && yarn build:assets", "build:development": "node ./scripts/replace.js development && node ./scripts/setup-env.js development && yarn build:assets", "build:testing": "node ./scripts/replace.js production && node ./scripts/setup-env.js testing && yarn build:assets", "clean": "rimraf --glob .tmp dist/* packages/*/dist/**/*", "prepare:local": "yarn clean && yarn build:react && node ./scripts/replace.js development && node ./scripts/setup-env.js && node ./scripts/build-angular.js && node ./scripts/copy-files.js tmp,snippets,icons,img", "storybook": "yarn workspace shortlist-react run storybook", "test": "yarn workspace shortlist-react run test", "vitest": "NODE_ENV=test vitest", "typecheck": "yarn workspace shortlist-react run typecheck", "typecheck-watch": "yarn workspace shortlist-react run typecheck-watch"}, "dependencies": {"@sentry/browser": "^6.10.0", "@sentry/integrations": "^6.10.0", "@uirouter/angularjs": "~0.3.1", "@veriff/incontext-sdk": "1.1.0", "angular": "~1.5.5", "angular-animate": "~1.5.5", "angular-bindonce": "pasvaz/bindonce#0.3.3", "angular-content-editable": "^1.2.3", "angular-contenteditable": "^0.3.9", "angular-cookies": "~1.5.5", "angular-deferred-bootstrap": "~0.1.7", "angular-drag-and-drop-lists": "^2.1.0", "angular-elastic": "monospaced/angular-elastic#v2.4.0", "angular-ellipsis": "dibari/angular-ellipsis#0.1.6", "angular-input-masks": "assisrafael/bower-angular-input-masks#1.2.5", "angular-load": "~0.3.0", "angular-masonry": "^0.17.0", "angular-recaptcha": "~3.0.4", "angular-resource": "~1.5.5", "angular-sanitize": "~1.5.5", "angular-switcher": "~0.2.6", "angular-translate": "~2.8.1", "angular-ui-bootstrap": "link:./lib/angular-ui-bootstrap", "bootstrap-sass": "~3.4.3", "bootstrap-tags": "ShortlistProject/bootstrap-tags#1.2.7", "checklist-model": "~0.6.0", "core-js": "^2.4.1", "dompurify": "^2.3.0", "esbuild": "^0.24.0", "esbuild-plugin-inline-css": "^0.0.1", "esbuild-plugin-svgr": "^3.1.0", "filestack-js": "3.24.1", "hellosign-embedded": "^2.9.0", "inputmask": "^3.3.7", "intersection-observer": "^0.11.0", "jquery": "~2.1.1", "js-cookie": "~1.5.1", "memoizerific": "^1.11.3", "ment.io": "ShortlistProject/ment.io#0.9.25", "moment": "~2.29.1", "moment-timezone-all": "~0.5.5", "ng-content-editable": "ShortlistProject/ng-contenteditable#20643ece23661c04001b05205d6663ca9e0b7997", "ng-focus-if": "~1.0.2", "ng-idle": "~1.3.2", "ng-infinite-scroll": "~1.3.0", "ngclipboard": "^1.1.1", "ngmap": "^1.18.4", "ngprogress": "VictorBjelkholm/ngProgress#v1.1.3", "react": "18.3.1", "react-dom": "18.3.1", "react-error-boundary": "^4.0.13", "rx": "^4.1.0", "underscore": "^1.13.6"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@biomejs/biome": "^1.9.4", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "babel-loader": "^9.2.1", "babel-plugin-angularjs-annotate": "^0.10.0", "concurrently": "^9.1.2", "esbuild-sass-plugin": "^3.3.1", "eslint": "^8.57.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react-hooks": "^4.6.2", "html-loader": "^1.3.2", "html-minifier-terser": "^7.2.0", "http-proxy-middleware": "^3.0.5", "jquery.easing": "~1.3.1", "lefthook": "^1.7.1", "resize-observer-polyfill": "^1.5.1", "rimraf": "^6.0.1", "sass": "^1.47.0", "serve-static": "^1.13.2", "typescript": "^5.5.4", "vite": "^6.2.6", "webpack": "^5.0.0", "webpack-cli": "^5.1.4"}, "engines": {"node": ">=0.12.0"}}