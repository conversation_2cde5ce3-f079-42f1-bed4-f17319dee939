<div class="item-header row"
     tabindex="-1"
     ng-click="selectItem($event)"
     key-bind="saveChanges()"
     key="13"
     ng-class="{'selected': elementInFocus, 'inactive': elementInFocus == false}">

    <response-form-item-user-ui-elements></response-form-item-user-ui-elements>
    <div class="content" ng-hide="elementInFocus">
        {{ item.templateOptions.label }}
    </div>

    <div class="edit" ng-hide="!elementInFocus">
        <div class="col-md-1 icon">
            <i class="icon-section-title"></i>
        </div>
        <div class="col-md-8">
            <div ng-class="{'has-error': errors[item.key].length}"
                 class="form-group">
                <div class="icon-header icon small"></div>
                <input class="item-label form-control"
                       ng-model="item.templateOptions.label"
                       ng-disabled="!elementInFocus"
                       placeholder="{{ item.templateOptions.placeholder }}"/>
                <ul class="errorlist" ng-show="errors[item.key].length">
                    <li ng-repeat="error in errors[item.key]">{{ error.message }}</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="controls controls-header" ng-hide="elementInFocus">
        <a class="icon" ng-click="" href="" ng-show="!editingDisabled">
            <span class="glyphicon glyphicon-pencil"></span>
        </a>
        <a class="icon" ng-click="removeItem(item.id, $event)" href=""
           ng-show="allowedToRemove() && !editingDisabled">
            <span class="glyphicon glyphicon-remove"></span>
        </a>
    </div>
    <div class="row">
        <div class="col-md-offset-1 col-md-10">
            <response-form-item-user-tools></response-form-item-user-tools>
        </div>
    </div>

</div>
