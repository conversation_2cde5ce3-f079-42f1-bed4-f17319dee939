<div class="search-project" protractor-id="search">
    <div class="search-wrapper">
        <div class="input-wrapper"
             dropdown-navigation
             collection="results"
             select="selectCurrent"
             cancel="cancel"
             dropdown-dynamic-position>

            <input type="text"
                   class="form-control"
                   ng-model="searchText"
                   ng-change="searchProjects()"
                   protractor-id="input"
                   placeholder="{{ 'SHORTLIST_MODAL_ADD_TO_PROJECT_SEARCH_PLACEHOLDER' | translate }}"
                   focus-if="!project"
                   focus-delay="300">

            <div class="search-widget" ng-show="searchText.length >= 1">
                <ul ng-show="searchText.length >= 1">
                    <li ng-repeat="item in results"
                        ng-click="selectCurrent(item)"
                        ng-if="results.length"
                        ng-class="{'active': item.active}"
                        protractor-id="result">
                        <a href>
                            <span ng-class="item.status"
                                  class="status pull-left">{{ item.status }}</span>{{ item.name }}
                        </a>
                    </li>
                    <li class="empty-results"
                        ng-if="results.length === 0">
                        <a>{{ 'SHORTLIST_MODAL_ADD_TO_PROJECT_SEARCH_NO_RESULTS' | translate }}</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
