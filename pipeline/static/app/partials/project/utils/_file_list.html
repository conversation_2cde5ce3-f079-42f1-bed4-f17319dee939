<ul class="list-unstyled" ng-class="getClass()">
    <li ng-repeat="file in filteredFiles track by $index"
        class="uploaded-file-wrapper">
        <a target="_blank"
           rel="noopener"
           title="{{ file.name }}"
           href="{{ file.file_url }}"
           class="file">
            <i class="file-list__icon"
               ng-class="file.name | fileNameToIcon"></i>{{ file.name }}
        </a>
    </li>
</ul>
