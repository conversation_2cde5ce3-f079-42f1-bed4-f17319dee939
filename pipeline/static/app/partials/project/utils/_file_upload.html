<div class="file-widget">
    <div>
        <div class="file-upload-component"
             file-drop="fileDropHandler(files)">
            <span>
                <i class="material-icons">cloud_upload</i> {{ 'FILE_UPLOAD_COMPONENT_UPLOAD_DEFAULT_CTA' | translate }}
                <a ng-click="onFileSelect()" href>{{ 'FILE_UPLOAD_COMPONENT_UPLOAD' | translate }}</a>
            </span>
        </div>
        <div class="container-fluid">
            <div class="uploaded-file-wrapper row">
                <div class="uploaded-file"
                     ng-repeat="file in filteredFiles | reverse track by $index"
                     ng-class="{'errors': file.errors}">
                        <a class="link"
                           href="{{ file.file_url }}"
                           target="_blank">
                            <div class="icon-file"></div>
                            <span class="file-name">{{ file.name }}</span>
                        </a>
                        <i class="icon-file-close"
                           ng-click="removeFile(file)"
                           ng-hide="file.errors"></i>
                </div>
            </div>
        </div>
        <div class="clearfix"></div>
    </div>
</div>
