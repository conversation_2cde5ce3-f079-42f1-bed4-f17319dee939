<div class="v3-modal modal-content project-awarding">

    <div class="modal-header">
        <a href class="close" ng-click="dismiss()" aria-hidden="true"><span class="icon-cross-icon"></span></a>
        <h1 class="modal-title">
            {{ 'PROJECT_AWARD_MODAL_HEADER' | translate }}
        </h1>
    </div>

    <div class="modal-body">
        <div ng-bind-html="projectAwardCopy"></div>
        <div class="total-fee clearfix" ng-show="showProposalFee">
            <label class="pull-left">
                {{ 'PROJECT_AWARD_MODAL_TOTAL_FEE' | translate }}
            </label>
            <input class="pull-left item-label form-control"
                   ng-model="totalProposalFee"
                   ui-number-mask="0"
                   maxlength="19"
                   type="text">
            <span class="pull-left">{{ project.currency }}</span>
        </div>
    </div>

    <div class="modal-footer">
        <div class="pull-left">
            <button type="button"
                    class="btn-modal btn-primary"
                    data-dismiss="modal"
                    ng-click="award()">
                <span class="btn-inner-text">
                    {{ 'PROJECT_AWARD_MODAL_AWARD' | translate }}
                </span>
            </button>
            <button type="button"
                    class="btn-modal btn-secondary transparent"
                    data-dismiss="modal"
                    ng-if="project.project_type != 'assignment'"
                    ng-click="award(true)">
                <span class="btn-inner-text">
                    {{ 'PROJECT_AWARD_MODAL_AWARD_AND_CLOSE' | translate }}
                </span>
            </button>
        </div>
        <div ng-class="project.project_type != 'assignment' ? 'pull-right' : 'pull-left'">
            <a ng-click="dismiss()" class="btn-modal-cancel" href>
                {{ 'PROJECT_AWARD_MODAL_CANCEL' | translate }}
            </a>
        </div>
    </div>

</div>




