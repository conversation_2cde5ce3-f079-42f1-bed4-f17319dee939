<div class="row">
    <div class="col-md-7">

        <div class="notification-box" ng-show="!addNewVendorsToProject">
            {{ 'PROJECT_UPDATE_MODAL_PLEASE_REVIEW' | translate }}:
        </div>

        <div class="budget-widget">
            <div class="name">
                {{ project.name }}
                <changed-label field-name="name"></changed-label>
            </div>
            <div class="value description">
                <div class="description-changed">
                    <changed-label field-name="description"></changed-label>
                </div>
                {{ project.description | truncate: 100 }}
            </div>

            <div class="horizontal-spacer"></div>

            <span ng-if="project.project_type == 'rfp'">
                <ng-include src="'/partials/project/modals/update-inner/budget.html'"></ng-include>
                <ng-include src="'/partials/project/modals/update-inner/deadlines.html'"></ng-include>
            </span>

            <span ng-if="project.project_type == 'rfi'">
                <ng-include src="'/partials/project/modals/update-inner/deadlines.html'"></ng-include>
            </span>

            <span ng-if="project.project_type == 'assignment'">
                <ng-include src="'/partials/project/modals/update-inner/budget.html'"></ng-include>
                <ng-include src="'/partials/project/modals/update-inner/completion-deadline.html'"></ng-include>
            </span>

        </div>
    </div>
    <div class="col-md-5">

        <div class="invitation-wrapper-spacer"
             ng-show="vendorsTemporaryAdded.length || vendorsPending.length">

            <p class="invitation-list-header">
                {{ 'PROJECT_UPDATE_MODAL_INVITATION_WILL_BE_SENT_TO' | translate }}:
            </p>

            <div class="invitation-wrapper"
                 ng-class="{'pending': vendorsPending.length, 'not-pending': vendorsTemporaryAdded.length}">

                <div ng-show="vendorsTemporaryAdded.length">
                    <ul class="invitation-list">
                        <li ng-repeat="vendor in vendorsTemporaryAdded">
                            {{ vendor.name || vendor.email }}
                            <span class="label label-info label-new"
                                  ng-hide="!addNewVendorsToProject || !vendor.isNewToProject">new</span>
                        </li>
                    </ul>
                </div>

                <div ng-show="vendorsPending.length">
                    <ul class="invitation-list">
                        <li class="pending-users" ng-repeat="vendor in vendorsPending">
                            <i class="material-icons">query_builder</i>
                            {{ vendor.name || vendor.email }}
                        </li>
                    </ul>
                </div>
            </div>

        </div>

        <div class="inform-partners">
            <checkbox ng-model="fields.informPartners"
                      ng-checked="fields.informPartners"
                      ng-show="changes.fields.length"
                      data-label="{{ 'PROJECT_UPDATE_MODAL_INFORM_PARTNERS' | translate }}">
            </checkbox>
        </div>

        <div ng-show="fields.informPartners && changes.fields.length && vendors.length"
             class="updated-notifications">

            <div class="inform-invitation-list">
                <span ng-repeat="vendor in vendors"
                      ng-show="vendor.status != 'rejected'">
                    {{ vendor.name }}{{$last ? '' : ', '}}
                </span>
            </div>

            <p class="invitation-list-footer">
                {{ 'PROJECT_UPDATE_MODAL_WILL_BE_NOTIFIED' | translate }}
            </p>

        </div>

    </div>
</div>
