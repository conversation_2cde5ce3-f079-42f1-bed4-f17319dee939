<div>
    <top-action-bar title="{{ 'VENDOR_DASHBOARD_ACTIVITY' | translate }}"></top-action-bar>

    <div class="container">
        <div class="row">
            <div class="col-md-8">
                <div class="activities">
                    <div class="updates discussion">
                        <post-message-form on-submit="submitPrivateMessageToBuyer(message)"
                                           user="$root.user"
                                           placeholder="{{ messagePlaceholder | unescapeHtml }}"
                                           submit-text="{{ 'VENDOR_DASHBOARD_POST_NEW_MESSAGE_SUBMIT' | translate }}">
                        </post-message-form>

                        <div bindonce="activity"
                             ng-repeat="activity in activities.items">
                            <display-activity
                                    data-activity="activity"
                                    data-current-user="$root.user"
                                    data-can-answer="true">
                            </display-activity>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12 text-center">
            <button ng-click="pagination.getMoreResults()"
                    ng-if="pagination.hasNextPage"
                    class="load-more btn-secondary transparent">
                <span class="btn-inner-text">
                    {{ 'NOTIFICATIONS_PAGE_LOAD_MORE' | translate }}
                </span>
            </button>
            <p ng-show="!pagination.hasNextPage" class="no-more">
                {{ 'NOTIFICATIONS_NO_MORE' | translate }}
            </p>
        </div>
    </div>
</div>
