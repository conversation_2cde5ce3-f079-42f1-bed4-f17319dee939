<div class="subheader-message type-alert" ng-show="!vendor.last_active">
    <div class="container">
        <span class="icon icon-alert-white"></span>
        <span class="message"
              ng-if="vendor.invited_at && vendor.accepted_by && $root.userHasAccess($root.accessLevels.admin)">
            Oops! Looks like {{vendor.name}} has not yet accepted your invitation to your Shortlist.
            <resend-invitation data-vendor="vendor"
                               data-title="Resend to {{vendor.email}}">
            </resend-invitation>
        </span>
        <span class="message"
              ng-if="(!vendor.invited_at || !vendor.accepted_by) && $root.userHasAccess($root.accessLevels.admin)">
            Oops! Looks like {{vendor.name}} has not yet been invited to your Shortlist
            <a class="btn-second"
               ng-click="inviteToShortlist()"
               href>
                {{ 'PARTNER_DETAILS_INVITE_TO_SHORTLIST_LABEL' | translate }}
            </a>
            <a class="btn-second"
               ng-click="delete()"
               href>
                {{ 'PARTNER_DETAILS_DELETE_PARTNER_LABEL' | translate }}
            </a>
        </span>
        <!-- AccessLevels.user would display the message for both an admin and user role -->
        <span ng-if="user.role.title == 'user'">
            {{vendor.name}} has not yet registered to your Shortlist
        </span>
    </div>
</div>

<!-- SUBHEADER -->
<div class="v2 subheader subheader-vendor-details" ng-class="{'vendor-not-activated': !vendor.last_active}">
    <div class="container">
        <div class="row">
            <div class="subheader-main-wrapper col-md-7">
                <a ui-sref="app.partners.shortlist"
                   class="go-back"
                   href>
                    <i class="material-icons">arrow_back</i>
                </a>

                <icon-vendor data-item="vendor"></icon-vendor>

                <div class="title-wrapper">
                    <div class="title clearfix" ng-class="{'inactive': !vendor.last_active}">
                        <div class="pull-left">{{ vendor.name || vendor.email }}</div>
                        <span ng-if="review_scores.score_average != null">
                            <div class="pull-left star-rating-container">
                                <star-rating model="review_scores.score_average"
                                             read-only="true"></star-rating>
                            </div>
                            <div class="pull-left review-count">
                                ({{ review_scores.review_count }})
                            </div>
                        </span>
                    </div>
                </div>
            </div>

            <div class="col-md-5 text-right"
                 ng-if="$root.userHasAccess($root.accessLevels.admin) || $root.userHasAccess($root.accessLevels.staff)">

                <div class="dropdown">
                    <a ng-click="addToProject()"
                       ng-if="$root.userHasAccess($root.accessLevels.user) && vendor.status != 'not invited'"
                       class="btn-first btn" href>
                        {{ 'PARTNER_DETAILS_ADD_TO_PROJECT_LABEL' | translate}}
                    </a>
                    <a ng-click="inviteToShortlist()"
                       ng-if="$root.userHasAccess($root.accessLevels.admin) && vendor.status == 'not invited'"
                       class="btn-first btn" href>
                        {{ 'PARTNER_DETAILS_INVITE_TO_SHORTLIST_LABEL' | translate }}
                    </a>
                </div>

                <div class="dropdown-actions dropdown">
                    <a class="btn-actions-dots btn-actions btn" href data-toggle="dropdown">
                        <ul class="indicator-dots">
                            <li></li>
                            <li></li>
                            <li></li>
                        </ul>
                    </a>
                    <ul class="dropdown-menu" role="menu">
                        <li ng-if="$root.userHasAccess($root.accessLevels.user) && vendor.status == 'not invited'">
                            <a role="button"
                               ng-click="addToProject()"
                               href>
                                {{ 'PARTNER_DETAILS_ADD_TO_PROJECT_LABEL' | translate}}
                            </a>
                        </li>
                        <li ng-if="vendor.awarded_count == 0">
                            <a role="button" ng-click="delete()" href>
                                {{ 'PARTNER_DETAILS_REMOVE_PARTNER_LABEL' | translate}}
                            </a>
                        </li>
                        <li>
                            <a role="button" ng-click="resetLoginLink()"
                               ng-if="$root.userHasAccess($root.accessLevels.staff)" href>
                                {{ 'PARTNER_DETAILS_RESET_LOGIN_LINK_LABEL' | translate}}
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container content-container">
    <div class="row">
        <div class="col-md-9">
            <!-- MAIN PART OF THE VIEW -->
            <ul class="vendor-details-tabs nav nav-tabs">

                <li ui-sref-active="active">
                    <a ui-sref="app.partner-details.profile({slug: vendor.slug})">
                        {{ 'PARTNERS_TABS_SUMMARY' | translate }}
                    </a>
                </li>

                <li ui-sref-active="active">
                    <a ui-sref="app.partner-details.projects({slug: vendor.slug})">
                        {{ 'PARTNERS_TABS_PROJECTS' | translate }}
                    </a>
                </li>

                <li ui-sref-active="active">
                    <a ui-sref="app.partner-details.updates({slug: vendor.slug})">
                        {{ 'PARTNERS_TABS_ACTIVITY' | translate }}
                    </a>
                </li>

                <li ui-sref-active="active">
                    <a ui-sref="app.partner-details.agreements({slug: vendor.slug})">
                        {{ 'PARTNERS_TABS_DOCUMENTS' | translate }}
                    </a>
                </li>

                <li ui-sref-active="active">
                    <a ui-sref="app.partner-details.profile({slug: vendor.slug})">
                        {{ 'PARTNERS_TABS_DETAILS' | translate }}
                    </a>
                </li>

            </ul>

            <div class="partner-details-wrapper">
                <ui-view/>
            </div>
        </div>

        <!-- RIGHT PANEL -->
        <div class="vendor-details-right-panel col-md-3">

            <section class="info clearfix" ng-show="review_scores.top_services.length">
                <div class="title">{{ 'PARTNERS_RIGHT_PANEL_TOP_SERVICES' | translate }}</div>

                <div ng-repeat="service in review_scores.top_services track by $index"
                     title="{{ service.name }}"
                     class="service top-service pull-left">
                    {{ service.name }}
                </div>
            </section>

            <section class="info clearfix" ng-show="review_scores.services.length">
                <div class="title">{{ 'PARTNERS_RIGHT_PANEL_SERVICES' | translate }}</div>

                <div class="clearfix">
                    <div ng-repeat="service in review_scores.services track by $index"
                         title="{{ service.name }}"
                         class="service pull-left">
                        {{ service.name }}
                    </div>
                    <div ng-repeat="service in review_scores.hidden_services track by $index"
                         ng-show="showHiddenServices"
                         title="{{ service.name }}"
                         class="service pull-left">
                        {{ service.name }}
                    </div>
                </div>
                <div ng-show="review_scores.hidden_services.length">
                    <a ng-click="showHiddenServices = true"
                       ng-hide="showHiddenServices"
                       href>
                        +{{ review_scores.hidden_services.length }} {{ 'PARTNERS_RIGHT_PANEL_N_MORE' | translate }}
                    </a>
                    <a ng-click="showHiddenServices = false"
                       ng-show="showHiddenServices"
                       href>
                        hide
                    </a>
                </div>
            </section>

            <section class="info">
                <div class="title">{{ 'PARTNERS_RIGHT_PANEL_PROJECTS' | translate }}</div>

                <ul class="total-contracted list-unstyled">
                    <li>
                        <span class="value">{{ review_scores.awarded_count }}</span>&nbsp;
                    </li>
                </ul>
            </section>

            <hr class="dark" ng-show="worked_with.length || vendor.groups.length">

            <div ng-if="worked_with.length" class="worked-with">

                <section class="info clearfix">
                    <div class="title">Worked with</div>

                    <div ng-repeat="vendor in worked_with track by $index"
                         class="vendor pull-left">
                        <avatar-widget data-user="vendor" data-size="33"></avatar-widget>
                    </div>
                </section>

                <hr class="dark">

            </div>

            <section class="info" ng-if="vendor.groups.length">
                <div class="title">Groups</div>

                <div class="group-list">
                    <groups data-groups="vendor.groups"></groups>
                </div>
            </section>

        </div>
    </div>
</div>
