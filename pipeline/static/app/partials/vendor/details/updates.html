<div class="activities">
  <div class="updates">
      <div ng-show="vendor.last_active && vendor.status != 'not invited'">
          <form role="form"
                class="form-new-message"
                ng-submit="submitQuestion(new_question, 'Vendor')">
              <textarea class="form-control"
                        ng-model="new_question.content"
                        ng-attr-placeholder="{{ 'PARTNER_DETAILS_NEW_PRIVATE_MESSAGE_PLACEHOLDER' | translate }}"
                        msd-elastic>
              </textarea>

              <ul class="errorlist" ng-if='errors.description'>
                  <li ng-repeat="error in errors.content">{{ error }}</li>
              </ul>

              <button type="submit"
                      class="btn-submit-form"
                      ng-disabled="!new_question.content">
                  Submit
              </button>
          </form>
      </div>

      <div ng-show="!vendor.last_active">
          This profile is not active yet
      </div>

      <div class="avatar-wrapper"
           bindonce="activity"
           ng-repeat="activity in activities.items">
          <display-activity
              data-activity="activity"
              data-current-obj="vendor"
              data-current-obj-type="'Vendor'"
              data-current-user="user"
              data-can-answer="true">
          </display-activity>
      </div>
  </div>
</div>
