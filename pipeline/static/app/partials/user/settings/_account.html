<div class="container account-settings">
    <div class="row">
        <div class="col-md-10 col-md-offset-1">
            <div class="profile-edit-form">

                <h1 class="shortlist-heading-h4-section account-settings__first-header">
                    {{ 'SETTINGS_ACCOUNT_HEADER' | translate }}
                </h1>
                <div class="block-tile block-tile__merged">

                    <div class="account-settings__field">
                        <div class="account-settings__label">
                            {{ 'SETTINGS_ACCOUNT_TENANT_COMPANY_NAME' | translate }}
                        </div>
                        <div class="account-settings__value">
                            <edit-name initial-name="tenant.name"
                                       editing-enabled="true"
                                       update-function="updateName"
                                       editing-name="isEditingTenantName"
                                       template="edit_company_name">
                            </edit-name>
                        </div>
                    </div>

                    <div class="account-settings__field">
                        <div class="account-settings__label">
                            {{ 'SETTINGS_ACCOUNT_TENANT_WEBSITE' | translate }}
                        </div>
                        <div class="account-settings__value">
                            <span class="value"
                                  tooltip="{{ tenant.domain_url }}"
                                  tooltip-placement="left">
                                {{ tenant.domain_url }}
                            </span>
                        </div>
                    </div>

                    <div class="account-settings__field">
                        <div class="account-settings__label">
                            {{ 'SETTINGS_ACCOUNT_TENANT_MEMBER_SINCE' | translate }}
                        </div>
                        <div class="account-settings__value">
                            {{ tenant.member_since | date }}
                        </div>
                    </div>

                </div>

                <h1 class="shortlist-heading-h4-section">
                    {{ 'SETTINGS_ACCOUNT_LOCALE' | translate }}
                </h1>
                <div class="block-tile block-tile__merged">

                    <div class="account-settings__label">
                        {{ 'SETTINGS_ACCOUNT_TIMEZONE' | translate }}
                    </div>
                    <div class="row account-settings__field">
                        <div class="account-settings__value col-md-4">
                            <profile-time-zone-select
                                data-tenant="tenant"
                                auto-save="true"
                                is-disabled="!isTimezoneChangeAllowed"></profile-time-zone-select>
                        </div>
                        <div class="col-md-8">
                            {{ 'SETTINGS_ACCOUNT_TIMEZONE_INFO' | translate }}

                            <div class="account-settings__margin-top-10"
                                 ng-if="!isTimezoneChangeAllowed"
                                 translate="SETTINGS_ACCOUNT_TIMEZONE_DISABLED_INFO"
                                 translate-compile>
                            </div>
                        </div>
                    </div>

                    <div class="account-settings__label">
                        {{ 'SETTINGS_ACCOUNT_FIRST_DAY' | translate }}
                    </div>
                    <div class="row account-settings__field">
                        <div class="account-settings__value col-md-4">
                            <select ng-model="firstDay"
                                    ng-change="firstDayChanged($event)"
                                    ng-disabled="isSaving || !isFirstDayChangeAllowed"
                                    class="form-control">
                                <option ng-repeat="(key,name) in firstDayOptions"
                                        ng-selected="key == firstDay"
                                        value="{{key}}">
                                    {{name}}
                                </option>
                            </select>
                        </div>
                        <div class="col-md-8"
                             ng-if="isFirstDayChangeAllowed === false"
                             translate="SETTINGS_ACCOUNT_FIRST_DAY_NOTIFICATION_COPY"
                             translate-compile>
                        </div>
                    </div>

                    <div class="account-settings__label">
                        {{ 'SETTINGS_ACCOUNT_DEFAULT_DATE_FORMAT' | translate }}
                    </div>
                    <div class="row account-settings__field">
                        <div class="account-settings__value col-md-4">
                            <select ng-model="defaultDateFormat"
                                    ng-disabled="isSaving"
                                    ng-change="updateDefaultDateFormat(defaultDateFormat)"
                                    ng-options="item.key as item.name for item in defaultDateFormatOptions"
                                    class="form-control">
                            </select>
                        </div>
                    </div>

                    <div class="account-settings__label">
                        {{ 'SETTINGS_ACCOUNT_DEFAULT_TIME_FORMAT' | translate }}
                    </div>
                    <div class="row account-settings__field">
                        <div class="account-settings__value col-md-4">
                            <select ng-model="defaultTimeFormat"
                                    ng-disabled="isSaving"
                                    ng-change="updateDefaultTimeFormat(defaultTimeFormat)"
                                    ng-options="item.key as item.name for item in defaultTimeFormatOptions"
                                    class="form-control">
                            </select>
                        </div>
                    </div>

                    <div class="account-settings__label">
                        {{ 'SETTINGS_ACCOUNT_DISTANCE_UNITS' | translate }}
                    </div>
                    <div class="row account-settings__field">
                        <div class="account-settings__value col-md-4">
                            <select ng-model="defaultDistanceUnit"
                                    ng-disabled="isSaving"
                                    ng-change="updateDefaultDistanceUnit(defaultDistanceUnit)"
                                    class="form-control">
                                <option ng-repeat="(key,name) in defaultDistanceUnitOptions"
                                        ng-selected="key == defaultDistanceUnit"
                                        value="{{key}}">
                                    {{name}}
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div ng-if="$root.tenantHasFeature('payments')">
                    <h1 class="shortlist-heading-h4-section">
                        {{ 'SETTINGS_ACCOUNT_INVOICE_HEADER' | translate }}
                    </h1>
                    <div class="block-tile block-tile__merged">

                        {{ 'SETTINGS_ACCOUNT_INVOICE_DESCRIPTION' | translate }}

                        <div class="account-settings__invoice-emails" ng-if="invoiceEmails.length">
                            <div class="account-settings__invoice-email row"
                                 title="{{ email.email }}"
                                 ng-repeat="email in invoiceEmails track by $index">
                                 <div class="col-sm-5 pr-1" ng-if="entities.length > 1">
                                    {{ getInvoiceEntityName(email) }}
                                </div>
                                <div ng-class="{'col-sm-6 pl-1': entities.length > 1, 'col-sm-11': entities.length <= 1}">
                                    {{ email.email }}
                                </div>
                                <div class="col-sm-1 account-settings__invoice-email-remove">
                                    <a ng-click="removeInvoiceEmail(email.id)">
                                     <i class="material-icons">clear</i>
                                 </a></div>
                            </div>
                        </div>

                        <div class="account-settings__invoice-email-add clearfix">

                           <p> {{ 'SETTINGS_ACCOUNT_INVOICE_ADD_NEW' | translate }}</p>

                            <form class="row" ng-submit="addInvoiceEmail(invoice)">
                               <div class="col-sm-5 pr-1" ng-if="entities.length > 1">
                                    <select class="form-control"
                                    ng-class="{'has-error': invoiceEntityError}"
                                    ng-options="entity as entity.name for entity in invoiceEntities track by entity.id"
                                    ng-model="invoice.entity">
                                        <option value="">
                                            {{ 'SETTINGS_ACCOUNT_INVOICE_SELECT_ENTITY' | translate }}
                                        </option>
                                    </select>
                               </div>
                               <div ng-class="{'col-sm-5 pl-1 pr-1': entities.length > 1, 'col-sm-10': entities.length <= 1}">
                                    <input class="form-control"
                                           ng-class="{'has-error': invoiceEmailError}"
                                           placeholder="{{ 'type e-mail' | translate }}"
                                           ng-model="invoice.email">
                                    <ul class="errorlist" ng-show="invoiceEmailError">
                                          <li>{{ 'Invalid email address' | translate }}</li>
                                    </ul>
                                    <ul class="errorlist" ng-show="invoiceEntityError">
                                       <li ng-repeat="error in invoiceEntityError">
                                           {{ error }}
                                       </li>
                                    </ul>
                               </div>
                               <div class="col-sm-2 pl-1">
                                    <button class="btn-modal btn-primary">
                                        <span class="btn-inner-text">
                                            {{ 'SETTINGS_ACCOUNT_INVOICE_ADD' | translate }}
                                        </span>
                                    </button>
                               </div>
                            </form>

                        </div>

                    </div>
                </div>

                <h1 class="shortlist-heading-h4-section">
                    {{ 'SETTINGS_ACCOUNT_SIGNUP_HEADER' | translate }}
                </h1>
                <div class="block-tile block-tile__merged">

                    <div class="account-settings__update-signup clearfix">

                        <input class="form-control pull-left"
                               placeholder="{{ signupHeaderDefault | unescapeHtml }}"
                               ng-model="signup_header">

                        <button ng-click="updateSignupSettings()"
                                class="btn-modal btn-primary pull-left">
                            <span class="btn-inner-text">
                                {{ 'SETTINGS_ACCOUNT_SIGNUP_UPDATE' | translate }}
                            </span>
                        </button>

                    </div>

                </div>

            </div>
        </div>
    </div>
</div>
