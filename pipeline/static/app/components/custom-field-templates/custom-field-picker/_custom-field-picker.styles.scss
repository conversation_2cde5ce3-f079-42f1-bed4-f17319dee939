.custom-field-picker {

    &__profile-question {
        display: block;
        width: 25%;
        float: left;
        padding: 12px 10px;
        color: $shortlist-blue-1;
        font-size: 12px;
        font-weight: 600;
        border-radius: 4px;
        text-align: center;

        i, .icon-holder {
            display: block;
            font-size: 36px;
            padding-bottom: 5px;
        }

        &:hover {
            background-color: $shortlist-blue-4;
            cursor: pointer;
        }
    }

    &__profile-field {
        display: block;
        width: 48%;
        float: left;
        padding: 12px 10px;
        color: $shortlist-blue-1;
        font-size: 14px;
        font-weight: 600;
        border-radius: 4px;

        &:nth-child(odd) {
            margin-right: 4%;
        }

        i {
            margin-right: 5px;
        }

        &:hover {

            &:not(.custom-field-picker__profile-field--disabled) {
                background-color: $shortlist-blue-4;
                cursor: pointer;
            }
        }

        &--disabled {
            color: $shortlist-grey-3;


            &.btn-select-document:hover {
                color: $shortlist-grey-3;
            }
        }
    }

    .btn-new-document-request {
        font-size: 13px;
        font-weight: 600;
        height: 50px;
        padding-top: 10px;

        &:hover {
            &:not(.custom-field-picker__profile-field--disabled) {
                background-color: $shortlist-blue-4;
                cursor: pointer;
            }
        }
    }

    .btn-select-document {
        font-size: 14px;
        font-weight: 600;
        height: 50px;
        padding-top: 5px;
        padding-bottom: 5px;

        display: flex;
        justify-content: center;
        flex-direction: column;

        i {
            position: absolute;
            font-size: 40px;
            margin-right: 10px;
            float: left;
        }

        span {
            display: inline-block;
            max-height: 50px;
            overflow: hidden;
            margin-left: 35px;
        }

        &:hover {
            &:not(.custom-field-picker__profile-field--disabled) {
                background-color: $shortlist-blue-4;
                cursor: pointer;
            }
        }
    }

    &__content-text-area {

        .icon-holder {
            padding-bottom: 0;

            i {
                font-size: 53px;
                padding-bottom: 1px;
                margin-top: -8px;
            }
        }
    }

    &__content-video {

        .icon-holder {
            padding-bottom: 0;

            i {
                font-size: 56px;
                padding-bottom: 0;
                margin-top: -10px;
            }
        }
    }

    &__content-pdf {

        .icon-holder {
            padding-bottom: 0;

            i {
                font-size: 48px;
                padding-bottom: 3px;
                margin-top: -5px;
            }
        }
    }

    &__inner {
        border-radius: 4px;
        position: relative;
        background: white;
        border: 1px solid $shortlist-grey-3;
        padding-top: 5px;

        &:after, &:before {
            bottom: 100%;
            left: 50%;
            border: solid transparent;
            content: " ";
            height: 0;
            width: 0;
            position: absolute;
            pointer-events: none;
        }

        &:after {
            border-color: rgba(255, 0, 0, 0);
            border-bottom-color: white;
            border-width: 10px;
            margin-left: -10px;
        }

        &:before {
            border-color: rgba(0, 0, 0, 0);
	        border-bottom-color: $shortlist-grey-3;
            border-width: 11px;
            margin-left: -11px;
        }
    }

    &__tab-content {
        margin: 20px;

        &:after {
            content: "";
            display: table;
            clear: both;
        }
    }

    h1 {
        text-align: center;
        margin: 20px 0;
    }

    .tabbed-navbar {

        .container {
            width: auto;
            padding: 0;

            ul {
                text-align: center;
            }

            li {
                float: none;
                display: inline-block;
            }
        }
    }
}
