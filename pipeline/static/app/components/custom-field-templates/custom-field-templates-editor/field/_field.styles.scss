.custom-field-templates-editor-field {

    &__content-text-area {
        @include line-clamp($font-size: 14px, $line-height: 1.5, $lines-to-show: 1);
    }

    &__video-label {
        font-size: 14px;
        overflow-x: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 470px;
        display: inline-block;

        &.has-thumbnail {
            padding-left: 45px;
        }
    }

    &__video-thumbnail {
        position: absolute;
        left: -10px;
        top: -10px;
        height: 65px;
        width: 100px;

        .video-thumbnail-from-url {
            width: 100%;
            height: 100%;
            background-repeat: no-repeat;
            background-position: center center;
            height: 65px;
            background-size: cover;
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
        }
    }

    &--no-edit {

        .custom-field-templates-editor-field {

            &__details {
                cursor: unset;
            }
        }
    }

    &--workflow {

        .visible-to-vendors-label, .vendors-checkbox, .help-tooltip {
            display: none !important;
        }

        .extra-info-wrapper {

            .required {

                span:not(:last-child) {

                    &::after {
                        content: ', ';
                    }
                }
            }

            .required-hover {
                display: none;
            }
        }

        &:hover {

            .extra-info-wrapper {

                .required {
                    display: none;
                }

                .required-hover {
                    display: block;
                }
            }
        }
    }

    .other-option-field {
        padding-right: 125px;

        &-info {
            position: absolute;
            right: 300px;
            font-size: 14px;
            font-style: italic;
            margin-top: -3px;
            color: $shortlist-grey-2;
            z-index: 9999;

            &--workflow {
                right: 55px;
            }
        }
    }

    .icon-wrapper {
        width: 45px;
        height: 45px;
        line-height: 45px;
        font-size: 28px;
        text-align: center;

        &.icon-font-size-40 {
            font-size: 40px;
        }
    }

    &__details,
    &__editor {
        display: flex;
        padding: 20px 0;
        border-bottom: 1px $shortlist-grey-3 solid;
    }

    &__details {
        align-items: center;
        cursor: pointer;
        position: relative;

        .name-wrapper {
            width: 495px;
            padding: 0 20px;

            label {
                display: block;
                margin-bottom: 0;
                font-weight: 600;
            }

            .type {
                font-size: 12px;
                font-weight: 600;
                color: $shortlist-grey-2;
            }
        }

        .icon-wrapper {

            .icon-Article {
                font-size: 42px;
                margin-top: 1px;
                margin-left: 5px;
            }

            .icon-Video {
                font-size: 42px;
                margin-top: 1px;
                margin-left: 5px;
            }

            .icon-DocumentPDF {
                display: block;
                font-size: 42px;
                margin-top: 2px;
                margin-left: 5px;
            }
        }

        .choices-wrapper {
            ul {
                margin-top: 10px;
                padding-left: 0;
                list-style: none;
            }

            li {
                display: flex;
                width: 100%;
                align-items: center;
                justify-content: left;
                margin-bottom: 5px;
            }

            .material-icons {
                color: $shortlist-grey-2;
                margin-right: 5px;
            }

            .choice {
                font-weight: 600;
            }
        }

        .extra-info-wrapper {
            margin-left: auto;
            white-space: nowrap;
            width: 150px;
            text-align: right;

            .required {
                font-size: 12px;
                font-style: italic;
                color: $shortlist-grey-2;
            }
        }

        .actions-wrapper {
            margin-left: 20px;

            .remove {
                position: relative;
                top: 2px;
                color: $shortlist-grey-2;
            }
        }

        .item-reorder {
            position: absolute;
            display: none;
            left: 0;
            top: 0;
            height: 100%;

            .move-item {
                position: absolute;
                color: $shortlist-grey-2;
                left: 5px;
            }

            .move-up {
                top: 0;
            }

            .move-down {
                bottom: 0;
            }
        }

        &:hover {
            .item-reorder {
                display: block;
            }
        }
    }

    &__editor {
        padding: 40px 0;

        .form-wrapper {
            width: 100%;
            padding-right: 200px;
            margin-left: 20px;

            .form-control {
                width: 100%;
            }

            .buttons {
                margin-top: 40px;
            }

            .answers {
                margin-top: 15px;

                &__choices {
                    font-size: 14px;
                }

                .form-group {
                    display: flex;
                    align-items: center;
                }

                .material-icons {
                    margin-right: 5px;
                    color: $shortlist-blue-3;
                }

                .remove {
                    color: $shortlist-grey-2;
                    margin-left: 5px;
                }
            }

            .checkboxes {
                margin-top: 10px;

                .checkbox {
                    display: inline-block;
                    margin-right: 20px;

                    &.vendors-checkbox {
                        margin-right: 0;
                    }
                }
            }
        }
    }

    .icon-date, .icon-toggle {
        font-size: 34px;
    }
}
