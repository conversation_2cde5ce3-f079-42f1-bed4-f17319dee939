'use strict';

const youtubeVideoUrl = $sce =>
    youtubeVideoId => $sce.trustAsResourceUrl(`https://www.youtube.com/embed/${youtubeVideoId}?rel=0&amp;controls=0&amp;showinfo=0&autoplay=0`)
;

const vimeoVideoUrl = ($sce) => 
    vimeoVideoId => $sce.trustAsResourceUrl(`https://player.vimeo.com/video/${vimeoVideoId}`)
;

youtubeVideoUrl.$inject = ['$sce'];
vimeoVideoUrl.$inject = ['$sce'];

angular.module('pipeline.portfolio')
.filter('youtubeVideoUrl', youtubeVideoUrl)
.filter('vimeoVideoUrl', vimeoVideoUrl);
