export default class {

    constructor($state, portfolioService) {
        'ngInject';

        if ((this.isEditMode == null)) {
            this.isEditMode = false;
        }

        this.firstNotAudiofiles = this.project.items.filter(item => item.type !== 'audio');

        this.getThumbnailImagePath = () => {
            if (this.firstNotAudiofiles.length > 0) {
                if (this.firstNotAudiofiles[0].url) {
                    return this.firstNotAudiofiles[0].url;
                } else if (this.firstNotAudiofiles[0].type === 'youtube') {
                    return portfolioService.getYoutubeThumbnailById(this.firstNotAudiofiles[0].video_id);
                }
            }

            return '';
        };

        this.hasOnlyAudio = () => {
            return this.firstNotAudiofiles.length === 0;
        };

        this.showPortfolioProject = (loadProject = false) => {
            return portfolioService.portfolioProjectDetailsModal(this.project, loadProject);
        };

        this.editPortfolioProject = () => {
            return portfolioService.editPortfolioProjectModal(this.project, this.vendor);
        };

        this.deletePortfolioProject = () => {
            return portfolioService.deletePortfolioProjectModal(this.vendor, this.project);
        };

        this.getVendorProfileUrl = () => {
            return $state.href('app.partner-details.profile', {
                slug: this.vendor.slug
            });
        };

        this.goToVendorProfile = (event) => {
            $state.go('app.partner-details.profile', {
                slug: this.vendor.slug
            });
            event.stopPropagation();
        };
    }
}
