'use strict';

angular.module("pipeline.portfolio").component('portfolioProjectList', {
    templateUrl: '/components/portfolio/components/portfolio-project-list/portfolio-project-list.html',
    bindings: {
        vendor: '<',
        isEditMode: '<',
        portfolio: '<?',
        onEvent: '&?'
    },
    controller($rootScope, _, portfolioService) {
        'ngInject';

        const events = [];

        this.$onInit = () => {

            this.isLoading = false;
            this.items = [];
            this.featuredProject = false;

            if (this.portfolio != null) {
                this.items = portfolioService.orderPortfolioProjects(this.portfolio);
                this.orderFeaturedProject();
            } else {
                this.getItems();
            }

            events.push($rootScope.$on('portfolioProjectAdded', () => {
                return this.reloadItems();
            })
            );

            return events.push($rootScope.$on('portfolioProjectDeleted', () => {
                return this.reloadItems();
            })
            );
        };

        this.$onDestroy = () => Array.from(events).map((event) => event());

        this.reloadItems = function() {
            this.items = [];
            return this.getItems();
        };

        this.getItems = () => {
            if (this.isLoading === true) { return false; }
            this.isLoading = true;
            this.featuredProject = false;
            return portfolioService.getProjectsForVendor(this.vendor)
            .then(data => {
                this.items = this.items.concat(data);
                if (this.onEvent) {
                    this.onEvent({
                        event: 'PORTFOLIO_PROJECT_LIST.ITEMS_LOADED',
                        data: { items: this.items
                    }
                    });
                }
                return this.orderFeaturedProject();
        }).finally(() => {
                return this.isLoading = false;
            });
        };

        this.orderFeaturedProject = function() {
            if (this.featuredProject = _.find(this.items, {order: 0})) {
                return this.items = _.without(this.items, this.featuredProject);
            }
        };

        this.addPortfolioProject = () => {
            return portfolioService.addPortfolioProjectModal(this.vendor);
        };


    }
}).component('videoThumbnail', {
    template: '<img class="video-thumbnail video-thumbnail--{{ $ctrl.videoType }}" ng-src="{{ $ctrl.videoThumbnailSrc }}" ng-if="$ctrl.videoThumbnailSrc">',
    bindings: {
        videoId: '<',
        videoType: '<',
    },
    controller(portfolioService) {
        'ngInject';

        this.videoThumbnailSrc = false;

        const initVideoThumbnail = () => {
            if (this.videoType === 'youtube') {
                return this.videoThumbnailSrc = portfolioService.getYoutubeThumbnailById(this.videoId);
            } else if (this.videoType === 'vimeo') {
                return portfolioService.getVimeoThumbnailById(this.videoId)
                .then(imageSrc => {
                    return this.videoThumbnailSrc = imageSrc;
                });
            }
        };

        this.$onChanges = () => initVideoThumbnail();

        this.$onInit = () => initVideoThumbnail();

    }
}).component('videoFromUrl', {
    template: `<span ng-if="$ctrl.item">
        <iframe ng-if="$ctrl.item.type === 'youtube'"
                width="{{ $ctrl.width }}"
                height="{{ $ctrl.height }}"
                ng-src="{{ $ctrl.item.video_id | youtubeVideoUrl }}"
                frameborder="0"
                allowfullscreen="0"></iframe>
        <iframe ng-if="$ctrl.item.type === 'vimeo'"
                src="{{ $ctrl.item.video_id | vimeoVideoUrl  }}"
                width="{{ $ctrl.width }}"
                height="{{ $ctrl.height}}"
                frameborder="0"
                webkitallowfullscreen="0"
                mozallowfullscreen="0"
                allowfullscreen="0"></iframe>
        <video ng-if="$ctrl.item.type === 'html'"
               width="{{ $ctrl.width }}"
               height="{{ $ctrl.height}}"
               src="{{ $ctrl.videoUrl }}"
               on-video-play="$ctrl.onPlay()"
               controls
               controlslist="nodownload noremoteplayback disablepictureinpicture" />
    </span>`,
    bindings: {
        videoUrl: '<',
        width: '=?',
        height: '=?',
        onPlay: '&'
    },
    controller($scope, portfolioService) {
        'ngInject';

        this.$onInit = () => {
            if (!this.width) {
                this.width = 560;
            }
            if (!this.height) {
                this.height = Math.ceil(this.width / 1.777);
            }
            const videoData = portfolioService.detectVideoTypeAndId(this.videoUrl);
            if (videoData) {
                this.item = videoData;
            }
        };
    }
});
