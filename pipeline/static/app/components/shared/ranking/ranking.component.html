<div class="ranking-dropdown dropdown"
     ng-class="$ctrl.getStyleClasses()"
     ng-if="$ctrl.vendorRank && $ctrl.ranks.length">

    <a data-toggle="dropdown"
       href>

        <span class="ranking__label">
            <span ng-if="$ctrl.vendorRank.rank != 'unranked'">
                <icon name="'icon-rank-' + $ctrl.vendorRank.icon"></icon>
            </span>
            <span ng-if="$ctrl.vendorRank.rank == 'unranked'">
                <span class="icon-rank-unranked icon-rank-blue ranking__icon"></span>
            </span>
        </span>
    </a>

    <ul class="dropdown-menu" role="menu" ng-if="$ctrl.editingEnabled">
        <li ng-repeat="rank in $ctrl.ranks track by $index">
            <a ng-click="$ctrl.updateRank(rank.rank)" href>
                <icon name="'icon-rank-' + rank.icon"></icon>
                {{ rank.label }}
            </a>
        </li>
        <li>
            <a ng-click="$ctrl.updateRank()" href>
                {{ 'RANK_RANKS_UNRANKED' | translate }}
            </a>
        </li>
    </ul>

</div>
