import promiseService from "../../scripts/services/promise";

class externalSignService extends promiseService {
    constructor($q, $http) {
        'ngInject'
        super();
        this.$q = $q;
        this.$http = $http;
    }

    checkToken(token) {
       return this.getPromise(this.$http.get(`/api/public/agreements/${token}/sign_config/`));
    }

    postSignedRequest(token) {
        return this.$http.post(`/api/public/agreements/${token}/signed/`);
    }
}

export default externalSignService
