<div class="container documents-list vendor">

    <div class="documents-list__search-settings">
        <div class="documents-list__filters">

            <span class="documents-list__search-settings__caption">
                {{ 'DOCUMENTS_TABLE_FILTER_BY' | translate }}
            </span>

            <!-- Filter by Status -->
            <div class="documents-list__search-settings__item"
                 dropdown-multi-select-close="filtersShown.status">

                <a ng-click="toggleFilterList('status')"
                   ng-class="{'active': filtersShown.status}"
                   class="documents-list__filter-toggle"
                   href>
                    <i class="material-icons">play_arrow</i>
                    <span>{{ getActiveStatusFilter() }}</span>
                </a>
                <div class="documents-list__filter-dropdown left-aligned"
                     ng-show="filtersShown.status">
                    <dropdown-multi-select options="filterByStatus.options"
                                           events="filterByStatus.events"
                                           extra-settings="filterByStatus.extraSettings"
                                           selected-model="filterByStatus.selectedModel">
                    </dropdown-multi-select>
                </div>
            </div>

            <!-- Filter by Type -->
            <div class="documents-list__search-settings__item"
                 dropdown-multi-select-close="filtersShown.type">
                <a ng-click="toggleFilterList('type')"
                   ng-class="{'active': filtersShown.type}"
                   class="documents-list__filter-toggle"
                   href>
                    <i class="material-icons">play_arrow</i>
                    <span>{{ getActiveTypeFilter() }}</span>
                </a>
                <div class="documents-list__filter-dropdown left-aligned"
                     ng-show="filtersShown.type">
                    <dropdown-multi-select options="filterByType.options"
                                           events="filterByType.events"
                                           extra-settings="filterByType.extraSettings"
                                           selected-model="filterByType.selectedModel">
                    </dropdown-multi-select>
                </div>
            </div>
        </div>
    </div>

    <div class="document-list__search">
        <search-keyword on-changed="onSearchChanged(value)"
                        placeholder="searchPlaceholder">
        </search-keyword>
    </div>
    <document-list
            data-documents="documents"
            data-refresh="refreshList"
            data-custom-template="Vendor"
            data-is-infinite-scroll="true">
    </document-list>
    <r-zero-state
        ng-if="!documents.length"
        header="'DOCUMENTS_EMPTY' | translate"
        icon="'Documents'"
    ></r-zero-state>
</div>

