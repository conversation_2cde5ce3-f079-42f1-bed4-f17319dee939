'use strict';

const vendorItem = {
    bindings: {
        vendor: '<',
        group: '<',
        actions: '<',
        onSelect: '&',
        getVendorDetailsHref: '&?',
        options: '<?'
    },
    templateUrl: "/components/partners/vendor-items/vendor-item.template.html",
    controller($rootScope, $scope, $state, $stateParams, $translate, _, userPermissionsService) {
        'ngInject';

        this.isVendorStarRatingEnabled = userPermissionsService.isVendorStarRatingEnabled();

        const defaultOptions = {
            isCheckboxEnabled: true,
            showMoreOptions: true,
            showAddToGroup: true
        };

        const { actions } = this;

        this.$onInit = () => {
            this.options = _.extend(defaultOptions, this.options || {});
            this.tenantHasFeature = $rootScope.tenantHasFeature;
            this.hasAccessToReviews = userPermissionsService.hasAccessToReviews(this.vendor);
            $scope.isFavorite = false;

            if (this.vendor.favorited_by) {
                return $scope.isFavorite = this.vendor.favorited_by.includes($rootScope.user.id);
            }
        };

        this.generateVendorDetailsUrl = function (vendor) {
            let back = '';
            let backId = '';

            if (this.getVendorDetailsHref) {
                return this.getVendorDetailsHref(vendor);
            }

            if ($state.current.name === 'app.marketplace.candidates') {
                back = 'marketplace';
            } else if ($state.current.name === 'app.partners.shortlist.favorites') {
                back = 'favorites';
            } else if ($state.current.name === 'app.partners.group-details') {
                back = 'group';
                backId = $stateParams.slug;
            }

            return $state.href('app.partner-details.profile', { slug: vendor.private_slug, back, backId });
        };

        this.addToFavorites = vendor =>
            actions.addToFavorites(vendor).then(() => $scope.isFavorite = true).catch(function () {
                const msg = $translate.instant('VENDOR_FAVORITE_STATUS_CHANGE');
                return $rootScope.alertUser(msg, 'alert-danger');
            })
            ;


        this.removeFromFavorites = vendor =>
            actions.removeFromFavorites(vendor).then(() => $scope.isFavorite = false).catch(function () {
                const msg = $translate.instant('VENDOR_FAVORITE_STATUS_CHANGE');
                return $rootScope.alertUser(msg, 'alert-danger');
            })
            ;

    }
};

angular.module('pipeline.partners').component('vendorItem', vendorItem);
