.vendor-notes {
    position: relative;
    clear: both;
    padding: 0;
    margin-bottom: 30px;

    &__textarea {
        min-height: 80px;
        overflow-y: scroll !important;
    }

    .note-empty {
        padding: 0 60px;
        line-height: 65px;
        min-height: 65px;
        display: flex;
        align-items: center;

        .add-note-copy {
            font-size: 16px;
            line-height: 20px;
            margin-left: 20px;
            margin-right: 20px;
        }

        .btn-secondary {
            margin-left: auto;

            &:hover {
                background-color: $note-tile-border-color !important;
                color: $yellow-3;
            }
        }
    }

    .note-content {
        padding: 30px;

        p {
            word-break: break-word;
            word-wrap: break-word;
            overflow-wrap: break-word;
            font-size: 14px;
            line-height: 20px;
            color: $note-tile-color;
            margin-bottom: 0 !important;
        }

        .edit-note-trigger {
            opacity: 0;
            position: absolute;
            z-index: 2;
            right: 12px;
            top: 12px;
            color: $shortlist-grey-2;
        }
    }

    &:hover:not(.vendor-notes--disabled-edit) {
        background-color: $note-tile-background-color-hover;
        cursor: pointer;

        .note-content {
            .edit-note-trigger {
                opacity: 1;
            }
        }
    }

    &.in-edit-mode {
        padding: 20px 20px 30px 20px;

        .form-control {
            padding: 10px 20px;
            font-size: 14px;
            color: $shortlist-grey-1;
            line-height: 20px;
        }

        .actions {
            display: flex;
            align-items: center;
            overflow: hidden;
            margin-top: 20px;

            .btn-modal-cancel {
                padding-left: 0;
                padding-right: 0;
            }

            .remove-note {
                font-size: 11px;
                line-height: 13px;
                margin-left: auto;
                font-weight: 800;

                .icon-remove-icon {
                    font-size: 14px;
                    margin-right: 2px;
                    vertical-align: text-top;
                }
            }
        }
    }
}

.icon-lock-icon.private-note {
    margin-left: 5px;
    color: $shortlist-grey-2;
}

.tooltip > * {
    white-space: pre-wrap;
}
