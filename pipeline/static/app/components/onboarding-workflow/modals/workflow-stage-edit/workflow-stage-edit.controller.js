import { OnboardingWorkflowStage } from '../../onboarding-workflow.classes';

class WorkflowStageEditController {
    constructor($scope, $translate, $modalInstance, stage, onboardingWorkflow, onboardingService, integrations, workflowCategory) {
        'ngInject';

        Object.assign(this, {
            $translate,
            $modalInstance,
            stage,
            onboardingService,
            integrations,
            workflowCategory,
        });
        this.stageTypeLabel = this.$translate.instant(`ONBOARDING_WORKFLOW_STAGE_TYPE.${this.stage.stage_type}`);
        this.stage = this.onboardingService.normalize(this.stage);
        this.onboardingWorkflow = onboardingWorkflow;
        this.errors = {};
        this.errorElementId = null;
        this.saveErrorTimestamp = null;
        this.saveButtonClicked = false;
        this.triggerRequestDataModalClose = 0;

        this.stageHeader = 'ONBOARDING_WORKFLOW_STAGE_HEADER';
        if (this.stage.is_internal) {
            this.stageHeader = 'ONBOARDING_WORKFLOW_INTERNAL_STAGE_HEADER';
            this.stageTypeLabel = this.stageTypeLabel.toLowerCase();
        }

        // live validation of stage
        ['name', 'vendor_profile_fields', 'responseForm', 'agreements', 'requested_documents'].forEach((vField) => {
            $scope.$watch(
                () => ['name', 'responseForm'].indexOf(vField) >= 0 ? this.stage[vField] : this.stage[vField].length,
                () => {
                    if (this.saveButtonClicked) {
                        this.validateStage(false);
                    }
                },
            );
        });

        $scope.$on('modal.closing', () => {
            this.triggerRequestDataModalClose++;
        });
    }

    getStageName(value) {
        this.stage.name = value;
    }

    save() {
        this.saveButtonClicked = true;

        if (this.validateStage()) {
            if (!this.stage.showDescription) {
                this.stage.description = '';
            }

            if (!this.stage._showDisqualifyButton) {
                this.stage.disqualify_button_label = '';
            }

            if (!this.stage._showProceedMessage) {
                this.stage.manual_proceed_message = '';
                this.stage.auto_proceed_message = '';
            }

            if (!this.stage._redirectPartner) {
                this.stage.external_site_url = '';
                this.stage.external_site_name = '';
            }

            this.$modalInstance.close({
                action: this.stage.id ? 'edited' : 'added',
                stage: new OnboardingWorkflowStage(this.stage),
            });
        }
    }

    validateStage(scrollTo = true) {
        this.errors = {};
        this.stage = new OnboardingWorkflowStage(this.stage);
        const errors = this.stage.validate();
        if (errors.length) {
            this.errors = this.normalizeErrors(errors, scrollTo);
            return false;
        }
        return true;
    }

    cancel() {
        this.$modalInstance.dismiss();
    }

    normalizeErrors(errors = [], scrollTo = true) {
        const normalized = {};
        this.errorElementId = null;
        errors.forEach((error, index) => {
            if ((index === 0) && scrollTo) {
                this.errorElementId = `#form-element-stage-${error.field}`;
                this.saveErrorTimestamp = new Date().getTime();
            }
            normalized[error.field] = [ this.$translate.instant(error.message) ];
        });
        return normalized;
    }
}

export default WorkflowStageEditController;
