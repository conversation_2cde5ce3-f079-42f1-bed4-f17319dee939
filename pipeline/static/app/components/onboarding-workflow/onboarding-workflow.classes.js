export class OnboardingWorkflowEvents {

    static PARTNERS_ADDED_TO_WORKFLOW = 'ONBOARDING_WORKFLOW_EVENTS.PARTNERS_ADDED_TO_WORKFLOW';
    static PARTNERS_MOVED_TO_STAGE = 'ONBOARDING_WORKFLOW_EVENTS.PARTNERS_MOVED_TO_STAGE';
    static REFRESH_ONBOARDING_WORKFLOW_DETAILS = 'REFRESH_ONBOARDING_WORKFLOW_DETAILS';

    static ONBOARDING_STAGE_VIEW_STAGE_DATA_LOAD_COMPLETED = 'ONBOARDING_STAGE_VIEW_STAGE_DATA_LOAD_COMPLETED';
    static ONBOARDING_STAGE_VIEW_STAGE_DATA_LOAD_FAILED = 'ONBOARDING_STAGE_VIEW_STAGE_DATA_LOAD_FAILED';
}

export class OnboardingWorkflowVendor {

    constructor(data) {
        data && Object.assign(this, data);
        this.normalize();
    }

    normalize() {
        this.progress = this.progress || 0;
        this.stage = this.stage || null;
        this.workflow = this.workflow || null;
        this.checked = this.checked || false;
        this.workflow_completed = this.completed || this.workflow_completed || false;
        this.workflow_disqualified = this.disqualified || this.workflow_disqualified || false;
    }

    getActionsForVendorInStage(WorkflowStageService, jobOpening = null) {
        return WorkflowStageService.getActionsForVendorInStage(
            this,
            this.workflow,
            this.stage,
            this.progress,
            'all',
            jobOpening
        );
    }
}

export class OnboardingWorkflow {

    static CATEGORY_CONTRACT = 'contract';

    constructor(data) {
        data && Object.assign(this, data);
        this.normalize();
    }

    normalize() {
        this.name = this.name || '';
        this.stages_count = this.stages_count || 0;
        this.vendors_count = this.vendors_count || 0;
        this.vendors_in_progress_count = this.vendors_in_progress_count || 0;
        this.stages = this.stages || [];
        this.available_for_inviting_new_partners = this.available_for_inviting_new_partners !== true;
        this.disqualified = this.disqualified || 0;
        this.category = this.category || null;
    }

    /**
     * Checks for errors in object
     * @returns {Array} Array of errors
     */
    validate() {
        let errors = [];

        if (this.name.trim().length < 1) {
            errors.push('name');
        }
        if (this.stages.length < 1) {
            errors.push('stages');
        }

        return errors;
    }
}

export class OnboardingWorkflowStage {

    static TYPE_REQUEST_DATA = 'request_data';
    static TYPE_SIGN_AGREEMENTS = 'sign_agreements';
    static TYPE_BANK_DETAILS = 'bank_details';
    static TYPE_TAX_INFORMATION = 'tax_information';
    static TYPE_PORTFOLIO = 'portfolio';
    static TYPE_INTERVIEW = 'interview';
    static TYPE_ACTION = 'action';
    static TYPE_INFORMATION = 'information';
    static TYPE_BACKGROUND_CHECK = 'background_check';
    static TYPE_PAYCASSO_ENROLMENT = 'paycasso_enrolment';
    static TYPE_VERIFF = 'veriff';
    static TYPE_TEN99P = 'ten99p';

    constructor(data) {
        data && Object.assign(this, data);
        this.normalize();
    }

    normalize() {
        this.name = this.name || '';
        this.stage_type = this.stage_type || null;
        this.vendors_count = this.vendors_count || 0;
        this.vendors_current_count = this.vendors_current_count || 0;
        this.current_progress = this.current_progress || 0;
        this.custom_email_message = this.custom_email_message || '';
        this.message_on_completion = this.message_on_completion || '';
        this.completed_internally = this.completed_internally || false;
        this.auto_proceed_to_next_stage = this.auto_proceed_to_next_stage || false;
        this.responseForm = this.responseForm || false;
        this.isNew = this.isNew || false;
        this.auto_proceed_message = this.auto_proceed_message || '';
        this.manual_proceed_message = this.manual_proceed_message || '';
        this.calendar_link = this.calendar_link || '';
        this.is_internal = this.is_internal || false;
        this.actions = this.actions || [];
        this.next_stage_id = this.next_stage_id || null;
        this.notifications = this.notifications || null;
        this.custom_fields_template = this.custom_fields_template || {
            template_fields: [],
        };
        this.course = this.course || 0;
        this.dynamic_managers = this.dynamic_managers || [];
        this.external_site_url = this.external_site_url || '';
        this.external_site_name = this.external_site_name || '';
        this.proceed_button_label = this.proceed_button_label || '';
        this.disqualify_button_label = this.disqualify_button_label || '';
        this.information_stage_config = this.information_stage_config || [];
        this.checker = this.checker || null;
        this.checker_status = this.checker_status || null;
        this.send_default_notifications = this.send_default_notifications === undefined ? true : this.send_default_notifications;

        // in UI use only, not saved to API
        this._showProceedMessage = this._showProceedMessage || false;
        this._redirectPartner = this._redirectPartner || false;
        this._showDisqualifyButton = this._showDisqualifyButton !== undefined ? this._showDisqualifyButton : !!this.disqualify_button_label;
    }

    /**
     * Checks for errors in object
     * @returns {Array} Array of errors
     */
    validate() {
        let errors = [];

        // stage name
        if (this.name.trim().length < 1) {
            errors.push({
                field: 'name',
                message: 'ONBOARDING_WORKFLOW_STAGE_VALIDATION_STAGE_NAME_EMPTY',
            });
        } else if (this.name.trim().length > 80) {
            errors.push({
                field: 'name',
                message: 'ONBOARDING_WORKFLOW_STAGE_VALIDATION_STAGE_NAME_LENGTH',
            });
        }

        // stage request data
        if (this.stage_type === OnboardingWorkflowStage.TYPE_REQUEST_DATA) {
            if (this.custom_fields_template.template_fields.length === 0) {
                //alert('validation error...')
                errors.push({
                    field: 'stage_questions',
                    message: 'ONBOARDING_WORKFLOW_STAGE_VALIDATION_DATA_FIELD',
                });
            }
        }

        // external url validation if selected
        if (this._redirectPartner) {
            if (this.external_site_url.trim().length < 1) {
                errors.push({
                    field: 'external_site_url',
                    message: 'ONBOARDING_WORKFLOW_STAGE_VALIDATION_EXTERNAL_SITE_EMPTY',
                });
            } else {
                // regex taken from https://gist.github.com/dperini/729294 (Diego Perini)
                const regex = new RegExp(
                    '^' +
                    // protocol identifier (optional)
                    // short syntax // still required
                    '(?:(?:(?:https?|ftp):)?\\/\\/)' +
                    // user:pass BasicAuth (optional)
                    '(?:\\S+(?::\\S*)?@)?' +
                    '(?:' +
                    // IP address exclusion
                    // private & local networks
                    '(?!(?:10|127)(?:\\.\\d{1,3}){3})' +
                    '(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})' +
                    '(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})' +
                    // IP address dotted notation octets
                    // excludes loopback network 0.0.0.0
                    // excludes reserved space >= *********
                    // excludes network & broadcast addresses
                    // (first & last IP address of each class)
                    '(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])' +
                    '(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}' +
                    '(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))' +
                    '|' +
                    // host & domain names, may end with dot
                    // can be replaced by a shortest alternative
                    // (?![-_])(?:[-\\w\\u00a1-\\uffff]{0,63}[^-_]\\.)+
                    '(?:' +
                    '(?:' +
                    '[a-z0-9\\u00a1-\\uffff]' +
                    '[a-z0-9\\u00a1-\\uffff_-]{0,62}' +
                    ')?' +
                    '[a-z0-9\\u00a1-\\uffff]\\.' +
                    ')+' +
                    // TLD identifier name, may end with dot
                    '(?:[a-z\\u00a1-\\uffff]{2,}\\.?)' +
                    ')' +
                    // port number (optional)
                    '(?::\\d{2,5})?' +
                    // resource path (optional)
                    '(?:[/?#]\\S*)?' +
                    '$', 'i'
                );

                if (!this.external_site_url.match(regex)) {
                    errors.push({
                        field: 'external_site_url',
                        message: 'ONBOARDING_WORKFLOW_STAGE_VALIDATION_EXTERNAL_SITE_URL',
                    });
                }
            }
        }

        // stage interview
        if (this.stage_type === OnboardingWorkflowStage.TYPE_INTERVIEW) {
            if (this.calendar_link.length <= 0) {
                errors.push({
                    field: 'calendar_link',
                    message: 'ONBOARDING_WORKFLOW_STAGE_VALIDATION_CALENDAR_LINK',
                });
            }
            if (!this.validateURL(this.calendar_link)) {
                errors.push({
                    field: 'calendar_link',
                    message: 'ONBOARDING_WORKFLOW_STAGE_VALIDATION_CALENDAR_LINK_URL',
                });
            }
        }

        // information stage
        if (this.stage_type === OnboardingWorkflowStage.TYPE_INFORMATION) {
            if (this.proceed_button_label.trim().length < 1) {
                errors.push({
                    field: 'proceed_button_label',
                    message: 'ONBOARDING_WORKFLOW_STAGE_VALIDATION_PROCEED_BUTTON_LABEL_EMPTY',
                });
            } else if (this.proceed_button_label.trim().length > 100) {
                errors.push({
                    field: 'proceed_button_label',
                    message: 'ONBOARDING_WORKFLOW_STAGE_VALIDATION_PROCEED_BUTTON_LABEL_LENGTH',
                });
            }
            if (this._showDisqualifyButton) {
                if (!this.disqualify_button_label || this.disqualify_button_label.trim().length < 1) {
                    errors.push({
                        field: 'disqualify_button_label',
                        message: 'ONBOARDING_WORKFLOW_STAGE_VALIDATION_DISQUALIFY_BUTTON_LABEL_EMPTY'
                    })
                } else if (this.disqualify_button_label.trim().length > 100) {
                    errors.push({
                        field: 'disqualify_button_label',
                        message: 'ONBOARDING_WORKFLOW_STAGE_VALIDATION_DISQUALIFY_BUTTON_LABEL_LENGTH'
                    })
                }
            }
            if (!this.information_stage_config || !this.information_stage_config.length) {
                errors.push({
                    field: 'information_stage_config',
                    message: 'ONBOARDING_WORKFLOW_STAGE_VALIDATION_DATA_FIELD',
                });
            }
        }

        // sign agreements
        if (this.stage_type === OnboardingWorkflowStage.TYPE_SIGN_AGREEMENTS) {
            if (this.agreements.length <= 0) {
                errors.push({
                    field: 'agreements',
                    message: 'ONBOARDING_WORKFLOW_STAGE_VALIDATION_AGREEMENT',
                })
            }
        }

        // action stage
        if (this.stage_type === OnboardingWorkflowStage.TYPE_ACTION) {
            if (!this.actions.length) {
                errors.push({
                    field: 'no-action-added',
                    message: 'ONBOARDING_WORKFLOW_STAGE_VALIDATION_NO_ACTION'
                });
            } else {
                this.actions.forEach((action) => {
                    if (action.action_name === 'change_vendor_type') {
                        if (!action.action_params.vendor_type || !action.action_params.vendor_type.length) {
                            errors.push({
                                field: `action-${action.action_name}`,
                                message: 'ONBOARDING_WORKFLOW_STAGE_VALIDATION_ACTION_VENDOR_TYPE'
                            });
                        }
                    } else if (action.action_name === 'set_manager') {
                        if (!action.action_params.manager || !action.action_params.manager.length) {
                            errors.push({
                                field: `action-${action.action_name}`,
                                message: 'ONBOARDING_WORKFLOW_STAGE_VALIDATION_ACTION_VENDOR_MANAGER'
                            });
                        }
                    } else if (action.action_name === 'add_to_group') {
                        if (!action.action_params.group || !action.action_params.group.length) {
                            errors.push({
                                field: `action-${action.action_name}`,
                                message: 'ONBOARDING_WORKFLOW_STAGE_VALIDATION_ACTION_VENDOR_GROUP'
                            });
                        }
                    } else if (action.action_name === 'remove_from_group') {
                        if (!action.action_params.group || !action.action_params.group.length) {
                            errors.push({
                                field: `action-${action.action_name}`,
                                message: 'ONBOARDING_WORKFLOW_STAGE_VALIDATION_ACTION_VENDOR_GROUP'
                            });
                        }
                    } else if (action.action_name === 'link_to_service') {
                        if (!action.action_params.service || !action.action_params.service.length) {
                            errors.push({
                                field: `action-${action.action_name}`,
                                message: 'ONBOARDING_WORKFLOW_STAGE_VALIDATION_ACTION_VENDOR_LINK'
                            });
                        }
                    } else if (action.action_name === 'apply_workflow') {
                        if (!action.action_params.workflow_id || !action.action_params.workflow_id.length) {
                            errors.push({
                                field: `action-${action.action_name}`,
                                message: 'ONBOARDING_WORKFLOW_STAGE_VALIDATION_ACTION_APPLY_WORKFLOW'
                            });
                        }
                    } else if (action.action_name === 'checkr_continuous_check') {
                        if (!action.action_params.package_type || !action.action_params.package_type.length) {
                            errors.push({
                                field: `action-${action.action_name}`,
                                message: 'ONBOARDING_CHECKR_VALIDATION_ACTION_APPLY_WORKFLOW'
                            });
                        }
                    }
                });
            }
        }

        // checkr screening integration
        if (this.stage_type === OnboardingWorkflowStage.TYPE_BACKGROUND_CHECK && !this.checker) {
            errors.push({
                field: 'checker',
                message: 'ONBOARDING_WORKFLOW_STAGE_VALIDATION_CHECKER',
            });
        }

        return errors;
    }

    validateURL(url = '') {
        const pattern = /(ftp|http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/;
        return pattern.test(url);
    }
}

export class OnboardingWorkflowStageNotification {
    static SEND_CONDITION_STAGE_COMPLETED = 'stage_completed';
    static SEND_CONDITION_ENTERED_STAGE = 'entered_stage';
    static SEND_CONDITION_VENDOR_COMPLETED = 'vendor_completed';
    static SEND_CONDITION_VENDOR_DISQUALIFIED = 'vendor_disqualified';
    static SEND_CONDITION_SEND_MANUALLY = 'send_manually';

    constructor(data = {}, contentVariables = []) {
        data && Object.assign(this, data);
        this.normalize(contentVariables);
    }

    normalize(contentVariables = []) {
        this.isNew = this.isNew || false;
        this.id = this.id || null;
        this.name = this.name || '';
        this.recipients = this.recipients || [];
        this.dynamic_recipients = this.dynamic_recipients || [];
        this.send_to_vendor = this.send_to_vendor || false;
        this.send_to_stage_managers = this.send_to_stage_managers || false;
        this.subject = this.subject || '';
        this.content = this.content || '';
        this.button_label = this.button_label || '';
        this.send_condition = this.send_condition || {
            onboarding_stage: null,
            condition: OnboardingWorkflowStageNotification.SEND_CONDITION_STAGE_COMPLETED,
        };
        this._status = this._status || 'not-changed'; // ['not-changed', 'added', 'changed', 'deleted']
        this.attach_onboarding_summary = this.attach_onboarding_summary || false;
    }

    /**
     * Checks for errors in object
     * @returns {Array} Array of errors
     */
    validate() {
        let errors = [];

        if (this.name.trim().length < 1) {
            errors.push({
                field: 'name',
                message: 'ONBOARDING_STAGE_NOTIFICATION_VALIDATION_NAME_EMPTY',
            });
        }

        if (!(this.recipients.length || this.send_to_vendor || this.send_to_stage_managers || this.dynamic_recipients.length)) {
            errors.push({
                field: 'recipients',
                message: 'ONBOARDING_STAGE_NOTIFICATION_VALIDATION_RECIPIENTS_EMPTY',
            });
        }

        if (this.subject.trim().length < 1) {
            errors.push({
                field: 'subject',
                message: 'ONBOARDING_STAGE_NOTIFICATION_VALIDATION_SUBJECT_EMPTY',
            });
        }

        if (this.content.length < 1) {
            errors.push({
                field: 'content',
                message: 'ONBOARDING_STAGE_NOTIFICATION_VALIDATION_CONTENT_EMPTY',
            });
        }

        return errors;
    }

    getIcon() {
        return OnboardingWorkflowStageNotification.getIconForSendCondition(this.send_condition.condition);
    }

    static getIconForSendCondition(type) {
        const icons = {};
        icons[OnboardingWorkflowStageNotification.SEND_CONDITION_STAGE_COMPLETED] = 'Medium.StageCompleteIcon';
        icons[OnboardingWorkflowStageNotification.SEND_CONDITION_ENTERED_STAGE] = 'Medium.StageEnterIcon';
        icons[OnboardingWorkflowStageNotification.SEND_CONDITION_VENDOR_COMPLETED] = 'Medium.StageExitIcon';
        icons[OnboardingWorkflowStageNotification.SEND_CONDITION_VENDOR_DISQUALIFIED] = 'Medium.StageExitIcon';
        icons[OnboardingWorkflowStageNotification.SEND_CONDITION_SEND_MANUALLY] = 'Medium.StageExitIcon';
        return icons[type];
    }
}

export default OnboardingWorkflow;
