<div class="vendor-background-check clearfix">
    <a
        ng-if="$ctrl.backgroundCheckReport.status === 'not_started'"
        ng-click="$ctrl.openModal()"
        class="shortlist__list-3 shortlist__list-flex"
        href
    >
        <i class="icon-Background-Screening"></i>
        <div class="vendor-background-check__report shortlist__column--main">
            <div class="vendor-background-check__report--name">
                {{ $ctrl.backgroundCheckReport.checker_name }}
            </div>
        </div>
        <div class="vendor-background-check__cta shortlist__column--right">
            {{ 'BACKGROUND_SCREENING__VENDOR__START_SCREENING' | translate }}
        </div>
    </a>

    <div
        ng-if="$ctrl.backgroundCheckReport.status !== 'not_started'"
        class="shortlist__list-1 shortlist__list-flex"
    >
        <i class="icon-Background-Screening"></i>
        <div class="vendor-background-check__report shortlist__column--main">
            <div class="vendor-background-check__report--name">
                {{ $ctrl.backgroundCheckReport.checker_name }}
            </div>
        </div>
        <div class="vendor-background-check__status" ng-if="!$ctrl.isLoading">
            <status
                status="$ctrl.backgroundCheckReport.status"
                type="screening"
            ></status>
        </div>
        <div ng-if="$ctrl.isLoading" class="loader">
            <loader></loader>
        </div>
    </div>
</div>
