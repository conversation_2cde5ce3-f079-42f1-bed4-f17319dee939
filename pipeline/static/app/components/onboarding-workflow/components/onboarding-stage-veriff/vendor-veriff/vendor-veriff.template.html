<div class="vendor-veriff clearfix">
    <a
        ng-if="$ctrl.veriffVerification.doable"
        ng-click="$ctrl.openModal()"
        class="shortlist__list-3 shortlist__list-flex"
        href
    >
        <i class="icon-Veriff"></i>
        <div class="vendor-veriff__verification shortlist__column--main">
            <div class="vendor-veriff__verification--veriff_type">
                <span ng-if="$ctrl.veriffVerification.veriff_type">{{ ('ONBOARDING_WORKFLOW_VERIFF_DOCUMENT_TYPE.' + $ctrl.veriffVerification.veriff_type) | translate }}</span>
                <span ng-if="!$ctrl.veriffVerification.veriff_type">{{ 'ONBOARDING_WORKFLOW_VERIFF_DOCUMENT_TYPE_ALL' | translate }}</span>
            </div>
        </div>
        <div class="vendor-veriff__cta shortlist__column--right">
            <span ng-if="$ctrl.veriffVerification.status == 'resubmission_requested'">{{ 'VERIFF__VENDOR__RESTART_VERIFICATION' | translate }}</span>
            <span ng-if="$ctrl.veriffVerification.status != 'resubmission_requested'">{{ 'VERIFF__VENDOR__START_VERIFICATION' | translate }}</span>
        </div>
    </a>

    <div
        ng-if="!$ctrl.veriffVerification.doable"
        class="shortlist__list-1 shortlist__list-flex"
    >
        <i class="icon-Veriff"></i>
        <div class="vendor-veriff__verification shortlist__column--main">
            <div class="vendor-veriff__verification--veriff_type">
                <span ng-if="$ctrl.veriffVerification.veriff_type">{{ ('ONBOARDING_WORKFLOW_VERIFF_DOCUMENT_TYPE.' + $ctrl.veriffVerification.veriff_type) | translate }}</span>
                <span ng-if="!$ctrl.veriffVerification.veriff_type">{{ 'ONBOARDING_WORKFLOW_VERIFF_DOCUMENT_TYPE_ALL' | translate }}</span>
            </div>
        </div>
        <div class="vendor-veriff__status" ng-if="!$ctrl.isLoading">
            <status
                status="$ctrl.veriffVerification.status"
                type="veriff"
            ></status>
        </div>
        <div ng-if="$ctrl.isLoading" class="loader">
            <loader></loader>
        </div>
    </div>

</div>
