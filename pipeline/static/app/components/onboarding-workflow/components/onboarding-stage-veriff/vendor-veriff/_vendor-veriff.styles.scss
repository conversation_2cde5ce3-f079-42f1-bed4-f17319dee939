.vendor-veriff {

    h4 {
        border-bottom: none;
    }

    .icon-Veriff {
        font-size: 40px;
        color: $shortlist-blue-1;
        float: left;
        margin-right: 10px;
        &:before {
            content: "\eaad";
        }
    }

    .shortlist__list-3, .shortlist__list-1 {
        border-radius: 4px;
        padding: 10px;
    }

    .shortlist__list-1 {
        cursor: default;
    }

    .loader {
        width: 70px;
        padding-top: 10px;
    }

    &__status {
        padding-top: 10px;
    }

    &__cta, &_cta:visited {
        line-height: 40px;
        font-weight: 600;
        color: $shortlist-blue-1;
    }

    &__verification {
        width: 100%;
        line-height: 40px;

        &--veriff_type {
            margin-right: 10px;
            font-weight: 600;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        &--processing {
            font-size: 13px;
            font-style: italic;
            color: $shortlist-grey-2;
            float: right;

            .indicator-dots {
                margin-right: 5px;
                li {
                    width: 7px;
                    height: 7px;
                }

            }
        }

        .status {
            float: left;
        }
    }
}
