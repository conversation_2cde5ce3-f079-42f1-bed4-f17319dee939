export default class {

    constructor($state, $modal, $translate, $scope) {
        'ngInject';

        Object.assign(this, {
            $state,
            $modal,
        });

        this.mandatoryText = '';

        $scope.$watch(
            () => this.document,
            () => {
                if (this.document && this.document.template && this.document.template.type) {
                    this.mandatoryText = $translate.instant(`ONBOARDING_REQUESTED_DOC_TYPES.${this.document.template.type || this.document.type}`) || '';
                }
            }
        );
    }

    $onInit() {
        if (angular.isFunction(this.onVisibleCheck)) {
            this.onVisibleCheck();
        }
    }
}
