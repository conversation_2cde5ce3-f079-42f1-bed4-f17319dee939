.onboarding-stage-paycasso-enrolment {
    span, p, a {
        white-space: normal;
    }

    &.loader {
        margin-top: 20px;
    }

    &__video-custom-field {
        margin: 10px auto;
        text-align: center;

        video {
            max-width: 100%;
        }
    }

    .icon-Background-Screening {
        font-size: 40px;
        color: $shortlist-blue-1;
        float: left;
        margin-right: 10px;
    }

    .shortlist__list-3, .shortlist__list-1 {
        border-radius: 4px;
        padding: 10px;
    }

    .shortlist__list-1 {
        cursor: default;
    }

    &__status {
        max-width: 50%;
        padding-top: 10px;
        white-space: normal;

        &.nowrap {
            white-space: nowrap;
        }
    }

    &__cta, &_cta:visited {
        line-height: 40px;
        font-weight: 600;
        color: $shortlist-blue-1;
    }

    &__form {
        flex-direction: column;
        font-size: 0.9em;
        margin-top: 30px;

        &.shortlist__list-1,
        &.shortlist__list-3 {
            padding: 0;
        }

        &.form-error {
            border-color: $pink-1;
        }
    }

    &__header-row {
        display: flex;
        padding: 10px;
        width: 100%;
    }

    &__details {
        width: 100%;
        line-height: 40px;

        &--name {
            margin-right: 10px;
            font-weight: 600;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    &__status {
        color: $shortlist-grey-2;
        font-style: italic;
        float: left;

        &.verified,
        &.error {
            color: $shortlist-green;
            position: relative;

            i {
                left: -26px;
                top: 7px;
                position: absolute;
            }
        }

        &.error {
            color: $pink-1;

            i {
                left: -20px;
                top: 12px;
            }
        }
    }

    &__loader {
        border-top: 1px solid $shortlist-grey-3;
        padding: 30px;
        width: 100%
    }

    &__error-message {
        border-top: 1px solid $shortlist-grey-3;
        padding: 30px;
        width: 100%;

        p {
            color: $pink-1;
            text-align: center;
        }
    }

    &__steps {
        border-top: 1px solid $shortlist-grey-3;
        padding: 30px;
        width: 100%;

        .loader {
            color: $shortlist-grey-2;
            font-style: italic;
            width: 70px;

            ul {
              margin-right: 10px;
            }
        }

        &-section {
            margin-bottom: 20px;

            p {
                white-space: normal;
            }

            &:last-of-type {
                margin-bottom: 0;
            }
        }

        &-hint {
            color: $shortlist-grey-2;
            font-size: 0.9em;
            margin-top: 10px;
        }

        &-phone {
            color: $black;
        }

        &-enrolment,
        &-verification {
            font-size: 1em;
            margin-top: 10px;

            > div {
                padding-left: 20px;
                position: relative;
            }

            .verified {
                color: $shortlist-green;
                font-size: 14px;
                font-style: italic;
            }

            .failed {
                color: $pink-1;
                font-style: italic;
            }

            .failed-attempt {
                color: $shortlist-grey-2;
                font-style: italic;
            }

            i {
                font-size: 1.25em;
                left: 0;
                position: absolute;
                top: 2px;
            }
        }

        &-verification_button {
            margin-top: 10px;
        }
    }
}
