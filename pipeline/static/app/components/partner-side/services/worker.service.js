import Worker from '../classes/worker.class';

export default class WorkerService {
    constructor($http) {
        'ngInject';
        
        Object.assign(this, {
            $http,
        });
        
        this.api = '/api/s/vendors/';
    }
    
    
    getWorkers() {
        return this.$http.get(this.api).then(
            res => res.data.map(
                w => new Worker(w)
            )
        );
    }
    
    getWorkerData(slug = null) {
        return this.$http.get(`${this.api}${slug}`)
            .then(({ data }) => new Worker(data));
    }
    
    createWorkers(vendors) {
        return this.$http.post(this.api, { vendors });
    }
}
