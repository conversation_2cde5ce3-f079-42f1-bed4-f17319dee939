<div ng-if="$ctrl.searchClass"
     class="job-opening-details__description job-opening-suppliers"
     ng-class="{'job-opening-details__description--without-side': $ctrl.jobOpening.public_portal_disabled && $ctrl.jobOpening.invite_link_disabled}">
    
    <search
        search-class="$ctrl.searchClass"
        search-grid-configuration="$ctrl.searchGridConfiguration">
    </search>

    <div ng-if="$ctrl.canAddCandidate()"
         class="job-opening-candidates__actions">

        <a ng-click="$ctrl.openAddCandidateModal()">
            <i class="icon-Add"></i> {{ 'Add more' | translate }}
        </a>
    </div>

    <div ng-if="!$ctrl.jobOpening.public_portal_disabled || !$ctrl.jobOpening.invite_link_disabled" 
         class="job-opening-details__side-info">
        
        <div ng-if="!$ctrl.jobOpening.public_portal_disabled"
             class="job-opening-details__side-info__section">
            <p class="job-opening-details__side-info__section__heading">
                {{ 'JOB_OPENING_ADD_CANDIDATES_VISIBILITY_HEADER' | translate }}
            </p>
            <p class="job-opening-details__side-info__section__copy">
                {{ 'JOB_OPENING_ADD_CANDIDATES_VISIBILITY_LEAD' | translate }}
            </p>
            <job-opening-visibility
                job-opening="$ctrl.jobOpening">
            </job-opening-visibility>
        </div>
    
        <div ng-if="!$ctrl.jobOpening.invite_link_disabled"
             class="job-opening-details__side-info__section">
            <p class="job-opening-details__side-info__section__heading">
                {{ 'JOB_OPENING_ADD_CANDIDATES_LINK_SHARING_HEADER' | translate }}
            </p>
            <p ng-if="$ctrl.jobOpening.status === 'draft'"
               class="job-opening-details__side-info__section__copy">
                {{ 'JOB_OPENING_ADD_CANDIDATES_LINK_SHARING_LEAD' | translate }}
            </p>
            <p ng-if="$ctrl.jobOpening.status !== 'draft'"
               class="job-opening-details__side-info__section__copy">
                {{ 'JOB_OPENING_ADD_CANDIDATES_LINK_SHARING_LEAD_LIVE' | translate }}
            </p>
            <job-opening-link-sharing
                job-opening="$ctrl.jobOpening">
            </job-opening-link-sharing>
        </div>
        
    </div>
</div>
