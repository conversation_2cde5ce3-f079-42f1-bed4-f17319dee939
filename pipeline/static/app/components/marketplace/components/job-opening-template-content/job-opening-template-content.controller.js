export default class JobOpeningTemplateContentController {
    constructor() {
        'ngInject';
    }

    $onInit() {
        this.pickerDisplayed = false;
    }

    showPicker() {
        this.pickerDisplayed = true;
    }
    
    hidePicker() {
        this.pickerDisplayed = false;
    }
    
    addContentField(fieldType) {
        this.onAdd({
            fieldType,
        });
        this.hidePicker();
    }
};
