export default class {

    constructor($state, $http, $stateParams, $rootScope, $translate, $window, _, diffService, onboardingService,
                onboardingWorkflowService, MarketplaceService, userAuth, userPermissionsService) {
        'ngInject';

        Object.assign(this, {
            $state,
            $http,
            $stateParams,
            $rootScope,
            $translate,
            $window,
            _,
            diffService,
            onboardingService,
            onboardingWorkflowService,
            MarketplaceService,
            userAuth,
            userPermissionsService,
        });
    }

    $onInit() {
        this.isLoading = true;
        this.publicJobOpening = null;

        if (this.$rootScope.user) {

            // Redirect logged users
            if (this.$rootScope.is_user) {
                return this.$state.go(this.userAuth.getDefaultRoute());
            }
            return this.$state.go('app.public-job-openings-details', {
                slug: this.$stateParams.slug
            });
        }

        this.MarketplaceService.getPublicJobOpeningBySlug(this.$stateParams.slug, this.$stateParams.key)
            .then((res) => {
                this.isLoading = false;
                this.publicJobOpening = res;
                this.vendorData = {
                    email: '',
                    first_name: '',
                    last_name: '',
                    terms: false,
                };
            })
            .catch((err) => {
                if (err && err.status && (err.status === 404)) {
                    this.$state.go('404');
                }
            });
    }

    onVendorCodeSubmitForJobOpening(vendorData, code) {
        return this.$http.post(`/api/public/confirm_email/`, {
            code: code,
            vendor_email: vendorData.email,
        }).then(() => {
            let params = {
                jo: this.publicJobOpening.id,
                fromEmailVerification: true,
            };
            if (!this.$rootScope.tenantHasFeature('vendor_signup_simplified')) {
                // we have to pass `signup` in this step
                params.signup = true;
                params.dfmode = true;
            }
            return params;
        });
    }

    addVendorFromJobOpening(vendorData) {
        return this.$http.post(`/api/public/job_opening_registration/${this.$stateParams.slug}/?key=${this.$stateParams.key}`, {
            vendor_data: vendorData,
            redirect_to: `/confirm-email/?jo=${this.publicJobOpening.id}`,
        }).then((res) => {
            return [
                res,
                {...res.data, resolveUserHomeUrlParams: { jo: this.publicJobOpening.id, fromEmailVerification: true }}
            ];
        });
    }

    onGoToGoogleAuth() {
        this.$window.location = this.loginOptions.google_oauth_url + '&invitation=job_opening-' + this.$stateParams.slug + (this.$stateParams.key ? ('.' + this.$stateParams.key) : '');
    };
}
