export default class {
    constructor($state, onboardingWorkflowService, MarketplaceService) {
        'ngInject';

        Object.assign(this, {
            $state,
            onboardingWorkflowService,
            MarketplaceService,
        });

        this.workflow = null;
    }
    
    $onInit() {
        if (this.jobOpening.onboarding_workflow) {
            this.onboardingWorkflowService.getWorkflowById(
                    this.jobOpening.onboarding_workflow,
                    false,
                    { job_opening: this.jobOpening.id }
                )
                .then((workflow) => {
                    this.workflow = workflow;
                });
        }
    }

    isStatusDraft() {
        return this.MarketplaceService.isJobOpeningStatusDraftOrPending(this.jobOpening.status);
    }
}
