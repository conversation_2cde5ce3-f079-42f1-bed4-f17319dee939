const marketplaceModule = angular.module('pipeline.marketplace', [
    'ui.router'
]);

import marketplaceConfig from './marketplace.config';
import MarketplaceService from './marketplace.service';

import marketplaceState from './states/marketplace-state/marketplace-state.component';
import vendorJobOpeningState from './states/vendor-job-opening-state/vendor-job-opening-state.component';
import vendorJobBoardItemState from './states/vendor-job-board-item-state/vendor-job-board-item-state.component';
import jobOpeningFormState from './states/job-opening-form-state/job-opening-form-state.component';
import jobOpeningDetailsState from './states/job-opening-details-state/job-opening-details-state.component';

import candidateNotification from './components/candidate-notification/candidate-notification.component';
import jobOpeningListForVendor from './components/job-opening-list-for-vendor/job-opening-list-for-vendor.component';
import jobOpeningEdit from './components/job-opening-edit/job-opening-edit.component';
import jobOpeningDetails from './components/job-opening-details/job-opening-details.component';
import jobOpeningSummary from './components/job-opening-summary/job-opening-summary.component';
import jobOpeningCandidates from './components/job-opening-candidates/job-opening-candidates.component';
import jobOpeningSuppliers from './components/job-opening-suppliers/job-opening-suppliers.component';
import jobOpeningJobBoards from './components/job-opening-job-boards/job-opening-job-boards.component';
import jobOpeningMessageBoard from './components/job-opening-message-board/job-opening-message-board.component';
import jobOpeningTasks from './components/job-opening-tasks/job-opening-tasks.component';
import jobOpeningAddCandidates from './components/job-opening-add-candidates/job-opening-add-candidates.component';
import jobOpeningWorkflow from './components/job-opening-workflow/job-opening-workflow.component';
import jobOpeningVisibility from './components/job-opening-visibility/job-opening-visibility.component';
import jobOpeningLinkSharing from './components/job-opening-link-sharing/job-opening-link-sharing.component';

import vendorJobOpeningDetails from './components/vendor-job-opening-details/vendor-job-opening-details.component';

import publicJobOpeningDetails from './components/public-job-opening-details/public-job-opening-details.component';
import publicJobOpeningApply from './components/public-job-opening-apply/public-job-opening-apply.component';
import jobOpeningInformation from './components/job-opening-information/job-opening-information.component';
import jobOpeningTemplates from './components/job-opening-templates/job-opening-templates.component';
import distributionTemplates from './components/distribution-templates/distribution-templates.component';
import distributionTemplateEdit from './components/distribution-template-edit/distribution-template-edit.component';
import jobOpeningTemplateContent from './components/job-opening-template-content/job-opening-template-content.component';

import { SearchService, SearchUtilsService } from '../../scripts/services/search.service';

marketplaceModule
    .config(marketplaceConfig)
    .service('MarketplaceService', MarketplaceService)
    .service('SearchService', SearchService)
    .service('SearchUtilsService', SearchUtilsService)

    .component('marketplaceState', marketplaceState)
    .component('vendorJobOpeningState', vendorJobOpeningState)
    .component('vendorJobBoardItemState', vendorJobBoardItemState)
    .component('jobOpeningFormState', jobOpeningFormState)
    .component('jobOpeningDetailsState', jobOpeningDetailsState)

    .component('candidateNotification', candidateNotification)
    .component('jobOpeningListForVendor', jobOpeningListForVendor)
    .component('jobOpeningEdit', jobOpeningEdit)
    .component('jobOpeningDetails', jobOpeningDetails)
    .component('jobOpeningSummary', jobOpeningSummary)
    .component('jobOpeningCandidates', jobOpeningCandidates)
    .component('jobOpeningSuppliers', jobOpeningSuppliers)
    .component('jobOpeningJobBoards', jobOpeningJobBoards)
    .component('jobOpeningMessageBoard', jobOpeningMessageBoard)
    .component('jobOpeningTasks', jobOpeningTasks)
    .component('jobOpeningAddCandidates', jobOpeningAddCandidates)
    .component('jobOpeningWorkflow', jobOpeningWorkflow)
    .component('jobOpeningVisibility', jobOpeningVisibility)
    .component('jobOpeningLinkSharing', jobOpeningLinkSharing)
    .component('publicJobOpeningDetails', publicJobOpeningDetails)
    .component('publicJobOpeningApply', publicJobOpeningApply)
    .component('jobOpeningInformation', jobOpeningInformation)
    .component('jobOpeningTemplates', jobOpeningTemplates)
    .component('distributionTemplates', distributionTemplates)
    .component('distributionTemplateEdit', distributionTemplateEdit)
    .component('jobOpeningTemplateContent', jobOpeningTemplateContent)

    .component('vendorJobOpeningDetails', vendorJobOpeningDetails)

export default marketplaceModule;
