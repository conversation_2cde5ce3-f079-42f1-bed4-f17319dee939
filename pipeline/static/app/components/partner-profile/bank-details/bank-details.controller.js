'use strict';

const BankDetailsController = function ($scope, $q, $http, $timeout, $translate, _, bankAccountFormats,
                                        PartnerBankDetailsService) {

    let key;

    PartnerBankDetailsService.getBankDetails(this.user.vendor.slug)
        .then((bankDetails) => {
            this.bankDetails = bankDetails;
            if (this.bankDetails.fields && this.bankDetails.fields.uk_account_type) {
                this.accountFormatFields = PartnerBankDetailsService.getAccountFormatFields(
                    bankDetails.country,
                    bankDetails.fields.uk_account_type
                );
            } else {
                this.accountFormatFields = PartnerBankDetailsService.getAccountFormatFields(bankDetails.country);
            }
            this.accountFormatInfo = PartnerBankDetailsService.getAccountFormatInfo(bankDetails.country);

            this.model = this.bankDetails.fields;

            this.country = this.bankDetails.country;

            this.loading = false;

            this.countries = [];

            this.submitted = false;

            this.inEditMode = _.values(this.model).join('').length === 0;

            if (this.hideSaveButton) {
                this.inEditMode = true;
            }

            this.errors = [];

            this.getBankDetailsType = PartnerBankDetailsService.getBankDetailsType;

            for (key in bankAccountFormats) {
                const val = bankAccountFormats[key];
                this.countries.push({
                    key,
                    name: val.name
                });
            }

            if (this.getBankDetailsType() === 'shortlist') {
                $scope.$watch((() => {
                    return this.triggerSaveAction;
                }), val => {
                    if (val && (val > 0)) {
                        return this.setBankDetails();
                    }
                });
            }

        });

    this.reloadCountryFields = () => {
        this.accountFormatFields = PartnerBankDetailsService.getAccountFormatFields(this.country, this.model.uk_account_type);
        this.accountFormatInfo = PartnerBankDetailsService.getAccountFormatInfo(this.country);

        if (!this.model.uk_account_type) {
            return this.model.uk_account_type = this.accountFormatFields[0].options[0].value;
        }
    };

    this.setBankDetails = function () {
        this.submitted = true;
        const valid = !$scope.form.$invalid;

        if (this.onSave) {
            this.onSave({
                valid
            });
        }

        if (!valid) {
            return;
        }

        this.loading = true;
        const data = {
            country: this.country,
            fields: this.model
        };

        return PartnerBankDetailsService.setBankDetails(this.user.vendor.slug, data)
            .finally(() => {
                this.loading = false;
                this.submitted = false;
                return $timeout(() => {
                        return this.inEditMode = false;
                    }
                    , 250);
            });
    };

    this.editBankDetails = function () {
        return this.inEditMode = true;
    };

    this.showFieldValue = field => {
        if (field.mask && this.model[field.key]) {
            if (field.mask === "showLastDigits") {
                if (this.model[field.key].length <= 4) {
                    return this.maskCharactersAndShowOnlyLast(this.model[field.key], 0);
                } else if ((this.model[field.key].length > 4) && (this.model[field.key].length <= 8)) {
                    return this.maskCharactersAndShowOnlyLast(this.model[field.key], 2);
                } else {
                    return this.maskCharactersAndShowOnlyLast(this.model[field.key], 4);
                }
            } else {
                return field.mask;
            }
        } else {
            if (this.model[field.key]) {
                return this.model[field.key];
            } else {
                return $translate.instant("BANK_ACCOUNT_NOT_SET");
            }
        }
    };

    this.maskCharactersAndShowOnlyLast = (str, lastChars) => {
        let last = "";
        if (lastChars > 0) {
            last = str.slice(lastChars * -1);
        }
        const first = str.substring(0, str.length - lastChars);
        return first.replace(new RegExp(".", "g"), "*") + last;
    };

};

BankDetailsController.$inject = [
    '$scope',
    '$q',
    '$http',
    '$timeout',
    '$translate',
    '_',
    'bankAccountFormats',
    'PartnerBankDetailsService'
];

angular.module('pipeline.partner-profile').controller('BankDetailsController', BankDetailsController);
