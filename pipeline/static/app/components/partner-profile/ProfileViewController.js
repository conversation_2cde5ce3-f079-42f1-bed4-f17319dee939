'use strict';

const stateNameToTitle = {
    'app.company-profile.personal-settings': 'USER_NAV_VENDOR_PROFILE',
    'app.company-profile.account-details': 'TITLE_SETTINGS_ACCOUNT_BARE',
    'app.company-profile.tax-information': 'TITLE_SETTINGS_TAX_INFORMATION',
    'app.company-profile.1099-tax-filing': 'TITLE_SETTINGS_TAX_FILLING',
    'app.company-profile.bank-details': 'TITLE_SETTINGS_BANK_DETAILS',
    'app.company-profile.payout-methods': 'TITLE_SETTINGS_PAYOUT_METHODS',
    'app.company-profile.settings': 'TITLE_SETTINGS',
    'app.company-profile.portfolio': 'TITLE_SETTINGS_PORTFOLIO',
};

angular.module('pipeline.partner-profile').controller('vendorProfileViewController',
    function ($scope, $rootScope, $state, employeesOptions, vendorExternalLinks, userPermissionsService, vendor) {
        $scope.vendor = vendor.data;
        $scope.vendorExternalLinks = vendorExternalLinks;

        $scope.displayTaxInformation = (
            userPermissionsService.checkSectionAccess('vendorTax') &&
            $rootScope.tenantHasFeature(`tax_information/${$scope.vendor.vendor_type}`)
        );
        $scope.displayPortfolio = $rootScope.tenantHasFeature(`vendor_portfolios/${$scope.vendor.vendor_type}`);

        $scope.$state = $state;
        $scope.employeesOptions = employeesOptions;
        $scope.hasAccessToReviews = userPermissionsService.hasAccessToReviews($scope.vendor)

        $scope.getTitle = () => stateNameToTitle[$state.current.name] || 'TITLE_SETTINGS';

        return $scope.update = () => $rootScope.$broadcast('updateProfile');
    }
);
