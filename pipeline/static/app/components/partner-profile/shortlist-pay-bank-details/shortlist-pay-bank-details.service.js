'use strict';

export default class ShortlistPayBankDetailsService {
    constructor($http, $translate, $rootScope, bankDetailsStatesPerCountry, defaultRuleSet, ruleSetFieldsNamesMap) {
        'ngInject';

        Object.assign(this, {
            $http,
            $translate,
            $rootScope,
            bankDetailsStatesPerCountry,
            defaultRuleSet,
            ruleSetFieldsNamesMap,
        });

        this.defaultCountry = 'US';
        this.defaultCurrency = 'USD';
        this.defaultRecipientCountry = 'US';
        this.helpContentUrl = 'https://shortlist-pay-bank-details-helpful-tips.s3.amazonaws.com';
    }

    getHelpContentUrl(country, currency) {
        return `${this.helpContentUrl}/${country}/${currency}.html`;
    }

    getBankDetailsRules(slug, country, currency, rule_type) {
        const params = { country };
        if (currency) {
            params.currency = currency;
        }
        if (rule_type) {
            params.rule_type = rule_type;
        }
        return this.$http.get(`/api/vendors/${slug}/bank_details_requirements/`, { params });
    }

    getPayableCurrencies(slug) {
        return this.$http.get(`/api/payments/payout_methods/?vendor=${slug}`)
            .then(({data}) => {
                const primaryPayoutMethod = data.find((payoutMethod) => payoutMethod.is_primary);
                return this.$http.get('/api/payments/payment_processors/')
                    .then(({data}) => {
                        const processor = data.find((processor) => processor.processor_id === primaryPayoutMethod?.account_processor?.processor_id);
                        return processor?.payable_currencies ?? [];
                    });
            });
    }

    getBankAccountProcessor() {
        return this.$http.get('/api/payments/payment_processors/')
            .then(({data}) => {
                const bankAccountProcessors = data.filter((processor) => ['MoneyCorpOperator', 'DefaultOperator'].includes(processor.provider));
                if (bankAccountProcessors.length === 0) {
                    return null;
                }
                if (bankAccountProcessors.length === 1) {
                    return bankAccountProcessors[0];
                } else {
                    throw new Error("Invalid tenant configuration");
                }
            });
    }

    setBankDetails(slug, data) {
        return this.getBankAccountProcessor()
            .then((accountProcessor) => {
                return this.$http.post(`/api/payments/payout_methods/`, {
                    account_processor_id: accountProcessor.processor_id,
                    schema_data: data
                });
            });
    }

    getBankDetailsPayoutMethod(slug) {
        return this.$http.get(`/api/payments/payout_methods/?vendor=${slug}`)
            .then(({data}) => {
                const bankDetailsMethod = data.filter((payoutMethod) => ['MoneyCorpOperator', 'DefaultOperator'].includes(payoutMethod.account_processor.provider));
                if (bankDetailsMethod.length === 1) {
                    return bankDetailsMethod[0];
                }
                return null;
            });
    }

    getBankDetails(slug) {
        return this.getBankDetailsPayoutMethod(slug).then((payoutMethod) => {
            if (payoutMethod !== null && payoutMethod.schema_data && Object.keys(payoutMethod.schema_data).length !== 0) {
                return payoutMethod.schema_data;
            }
            return this.getDefaultModel();
        });
    }

    getBankDetailsShortlistPayStatus(slug) {
        return this.getBankDetailsPayoutMethod(slug).then((method) => {
           return method !== null ? method.status : 'empty';
        });
    }

    isPayable(status) {
        return ['active', 'payable'].includes(status);
    }

    isModelDataEmpty(data) {
        return Object.values({...data, ...{bankAccountCountry: '', bankAccountCurrency: '', ruleType: ''}})
            .every((v) => !String(v).trim().length);
    }

    validateBankDetails(ruleSet, data) {
        let errors = {};
        ruleSet.fields.map((field) => {
            const value = data[field.fieldName] ? data[field.fieldName].trim() : data[field.fieldName];
            if (field.condition === 'mandatory' && !value) {
                errors[field.fieldName] = [this.$translate.instant('FIELD_ERROR.required')];
            } else if (value && !value.match(field.regex)) {
                errors[field.fieldName] = [this.$translate.instant('FIELD_ERROR.invalid')];
            }
        });
        return errors;
    }

    updateModelDefaults(fields, data) {
        let updated_data = {'ruleType': data.ruleType || ''};
        Object.keys(fields).map(section_name => {
            return fields[section_name].map((field) => {
                updated_data[field.fieldName] = data[field.fieldName] || '';
            });
        });
        return updated_data;
    }

    getDefaultModel() {
        return {
            bankAccountCountry: this.defaultCountry,
            bankAccountCurrency: this.defaultCurrency,
            recipientCountry: this.defaultRecipientCountry,
        };
    }

    getBankDetailsStatesPerCountry() {
        return this.bankDetailsStatesPerCountry;
    }

    getFieldName(field) {
        if (field.label) {
            return field.label;
        } else if (field.fieldType) {
            return field.fieldType;
        }
        return this.$translate.instant(`BANK_ACCOUNT_FIELD_LABEL.${field.fieldName}`);
    }

    translateRuleTypeChoice(choiceName) {
        return this.$translate.instant(`BANK_ACCOUNT_FIELD_RULE_TYPE_CHOICE.${choiceName}`);
    }

    getDefaultRuleSet() {
        return this.defaultRuleSet;
    }

    getRuleSetFieldsNamesMap() {
        return this.ruleSetFieldsNamesMap;
    }
}

angular.module('pipeline.partner-profile').service('ShortlistPayBankDetailsService', ShortlistPayBankDetailsService);
