'use strict';

angular.module("pipeline").component('inputManagers', {
    templateUrl: '/components/onboarding/components/input-managers/input-managers.html',
    bindings: {
        label: '<',
        labelInfo: '<',
        managers: '=',
        isOptional: '<?'
    },
    controller($q, _, userUtils) {
        'ngInject';

        this.availableUsers = [];
        this.model = {users: {}};

        const findMe = cont => {
            return cont.id === this.manager.id;
        };

        this.$onInit = function() {
            return userUtils.getUsers().then(res => {
                this.availableUsers = res;
                for (this.manager of Array.from(this.managers)) {
                    if (this.availableUsers.findIndex(findMe) === -1) {
                        this.availableUsers.push(this.manager);
                    }
                }
                return reIndexManagers();
            });
        };

        var reIndexManagers = () => {
            return this.model.users = _.chain(this.managers)
            .map(manager => _.findWhere(this.availableUsers, {slug: manager.slug}).full_name)
            .indexBy()
            .value();
        };

        this.managerAdd = manager => {
            if (!_.findWhere(this.managers, {full_name: manager})) {
                return this.managers.push(_.findWhere(this.availableUsers, {full_name: manager}));
            }
        };

        this.managerRemove = function(manager) {
            this.managers = _.without(this.managers, manager);
            if (manager.deleted) {
                const index = this.availableUsers.indexOf(manager);
                this.availableUsers.splice(index, 1);
            }
            return reIndexManagers();
        };

        this.searchUsers = input => {
            const userModel = Object.keys(this.model.users);
            return $q.when(_.pluck(
                _.filter(this.availableUsers, user =>
                    (user.full_name !== '') &&
                        (user.full_name.toLowerCase().indexOf(input.toLowerCase()) !== -1) &&
                        (userModel.indexOf(user.full_name) === -1)
                )
            , 'full_name')
            );
        };

    }
}
);
