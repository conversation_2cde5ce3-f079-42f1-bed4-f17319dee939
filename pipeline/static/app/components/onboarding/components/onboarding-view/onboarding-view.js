'use strict';

angular.module("pipeline.onboarding").component('onboardingView', {
    templateUrl: '/components/onboarding/components/onboarding-view/onboarding-view.html',
    bindings: {
        template: '<',
        vendorData: '<',
        workflowVendor: '<',
        vendorCustomFields: '<',
        templateData: '<?',
    },
    controller($rootScope, _, onboardingService, agreementsUtils, WorkflowStageService, onboardingWorkflowService, userPermissionsService) {
        'ngInject';

        this.$onInit = function () {
            this.orderedCustomFields = [];
            this.agreementsUtils = agreementsUtils;
            let templateDataPromise = Promise.resolve(this.templateData);

            if (!this.templateData) {
                templateDataPromise = onboardingService.getVendorTemplateForVendorByTemplateId(
                    this.workflowVendor.id,
                    this.vendorData.slug,
                );
            }

            templateDataPromise.then((templateData) => {
                this.templateData = templateData;
                this.vendorProfileFieldsMandatory = this.templateData.template.vendor_profile_fields_mandatory;

                if (this.templateData.forms && (this.templateData.forms[0] !== null)) { this.questionaire = this.templateData.forms[0]; }
                this.vendorResponse = { data: { init: true } };
                if (this.templateData.responses && (this.templateData.responses[0] !== null)) { this.vendorResponse = this.templateData.responses[0]; }
                this.isQuestionnaireResponseValid = true;
                this.vendorData.selectedServices = this.vendorData.services;
                this.progress = this.templateData.progress;

                if (this.templateData.template.agreements && this.templateData.template.agreements.length) {
                    agreementsUtils.mapAgreementsWithContext(
                        this.templateData.template.agreements,
                        (this.templateData.context.name !== 'default') ? this.templateData.context.name : null,
                        agreementsUtils.getAgreementRequests({ vendor: this.vendorData.slug }),
                    ).then((agreements) => {
                        this.templateData.template.agreements = agreements;
                    });
                }

                if (
                    angular.isArray(this.templateData.template.requested_documents)
                    &&
                    this.templateData.template.requested_documents.length
                ) {
                    onboardingService.getOrderedRequestedDocumentsForVendor(
                        this.vendorData.slug,
                        this.templateData.template.requested_documents,
                        WorkflowStageService.useSavingByStaffingSupplier(this.vendorData.slug)
                    ).then((orderedRequestedDocuments) => {
                        this.templateData.template.requested_documents = orderedRequestedDocuments;
                    });
                }

                for (let section of Array.from(this.vendorCustomFields)) {
                    this.orderedCustomFields = this.orderedCustomFields.concat(section.customFields);
                }

                const customFieldInOnboarding = [];
                _.map(this.templateData.template.vendor_profile_fields, field => {
                    if (_.findWhere(this.orderedCustomFields, { field_key: field })) {
                        customFieldInOnboarding.push(field);
                        return this.templateData.template.vendor_profile_fields = _.without(this.templateData.template.vendor_profile_fields, field);
                    }
                });

                this.orderedCustomFields = _.filter(this.orderedCustomFields, field => Array.from(customFieldInOnboarding).includes(field.field_key));

                const { vendorData } = this;
                let filteredCustomFields = this.orderedCustomFields.filter(field => field.vendor_types.includes(vendorData.vendor_type));
                let customFieldPrefix = 'custom_';

                if (templateData.template.stage_type === 'request_data') {
                    let reqestedDataPromise = null;
                    if ($rootScope.is_user) {
                        reqestedDataPromise = onboardingService.getVendorRequestDataForVendorByTemplateId(this.templateData.id, this.vendorData.slug);
                    } else {
                        reqestedDataPromise = onboardingService.getVendorRequestDataForVendorByTemplateIdByVendor(
                            this.templateData.id,
                            WorkflowStageService.useSavingByStaffingSupplier(this.vendorData.slug)
                        );
                    }

                    reqestedDataPromise.then((requestData) => {
                        customFieldPrefix = 'custom_vendor_field_';

                        filteredCustomFields = [
                            ...angular.copy(templateData.template.custom_fields_template.template_fields),
                        ].map((i) => {
                            if (i.mandatory) {
                                this.vendorProfileFieldsMandatory.push(onboardingService._getFieldKey(i.id));
                            }
                            return onboardingService._normalizeField(i, this.vendorData.vendor_type);
                        });
                        requestData.custom_fields.forEach((i) => this.vendorData.custom_fields[i.id] = i.value);

                        // these are needed to get all "files" type fields
                        const [allCustomFieldsInStage] =
                            onboardingService.normalizeFieldsForRequestDataStage(
                                angular.copy(this.templateData.template.custom_fields_template.template_fields),
                                this.vendorData.vendor_type
                            );

                        WorkflowStageService.loadUploadedFiles(
                            this.templateData.id,
                            !$rootScope.is_user,
                            WorkflowStageService.useSavingByStaffingSupplier(this.vendorData.slug)
                        ).then(({ data }) => {

                            // get "files" type fields data
                            allCustomFieldsInStage
                                .filter((field) => field.type === 'files')
                                .forEach((field) => {
                                    const uploadedFile = data.filter(
                                        file => (file.id === parseInt(this.vendorData.custom_fields[field.id], 10)) && !file.is_deleted
                                    );
                                    this.vendorData.custom_fields[field.id] = uploadedFile[0] ? uploadedFile[0] : null;
                                });

                            [
                                this.orderedCustomFields,
                                this.uploadedFiles,
                                this.vendorData.custom_fields,
                            ] = onboardingWorkflowService.normalizeRequestedFiles(
                                allCustomFieldsInStage,
                                this.vendorData.custom_fields,
                                this.templateData,
                                data,
                                this.templateData.template.requested_documents,
                            );

                            // convert custom fields when files are ready
                            this.orderedCustomFields = onboardingService.convertCustomFieldsToOnboardingFormat(
                                filteredCustomFields,
                                this.vendorData.custom_fields
                            );

                            // mark proper fields as mandatory
                            this.orderedCustomFields.form.contents = this.orderedCustomFields.form.contents.map((field) => {
                                field.mandatory = this.vendorProfileFieldsMandatory.indexOf(`${customFieldPrefix}${field.id}`) >= 0;
                                return field;
                            });
                        });
                    });
                } else {
                    this.orderedCustomFields = onboardingService.convertCustomFieldsToOnboardingFormat(
                        filteredCustomFields,
                        this.vendorData.custom_fields
                    );
                    // mark proper fields as mandatory // TODO duplicated here -> will be moved to service with above in the future
                    this.orderedCustomFields.form.contents = this.orderedCustomFields.form.contents.map((field) => {
                        field.mandatory = this.vendorProfileFieldsMandatory.indexOf(`${customFieldPrefix}${field.id}`) >= 0;
                        return field;
                    });
                }

            });
        };

        this.getDownloadRequestedDocumentUrl = function (document, inline) {
            if (inline == null) { inline = false; }
            if (document && document.filename && !document.isNew) {
                return onboardingService.downloadRequestedDocumentUrl(document, inline, null, this.vendorData.slug);
            }
        };

    }
}
);
