'use strict';

const pageNotificationsController = function(notificationsService) {

    this.$onInit = function() {
        this.page = 1;
        this.lastPage = false;
        this.lastBatchLength = 20;
        return notificationsService.markAsRead();
    };

    this.loadMore = function() {
        /*
            Detect the last batch of results. Either: (assume 20 is the default batch length)
            1. On page one, less than 20 results displayed, meaning there are no more
            2. The current batch is less than 20 results - also the last one to pull from the server.
        */
        if (((this.page === 1) && (this.notifications.length < 20)) ||
        ((this.lastBatchLength > 0) && (this.lastBatchLength < 20))) {
            return this.lastPage = true;
        // There are more notifications to fetch
        } else {
            this.page += 1;
            this.isLoading = true;
            return notificationsService.getNotifications(this.page)
            .then(response => {
                // Fetched some notifications
                if (response.length) {
                    this.lastBatchLength = response.length;
                    return this.notifications = this.notifications.concat(response);
                // Request came in empty
                } else {
                    return this.lastPage = true;
                }
        }).finally(() => {
                return this.isLoading = false;
            });
        }
    };

};

pageNotificationsController.$inject = [
    'notificationsService'
];

angular.module('pipeline').controller('pageNotificationsController', pageNotificationsController);
