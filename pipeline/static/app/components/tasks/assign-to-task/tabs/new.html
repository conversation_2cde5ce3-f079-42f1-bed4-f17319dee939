<ng-form name="$ctrl.formModel[0]"
         novalidate="">
    <div class="assign-to-task__field">
        <input-label label="'PROJECT_ASSIGN_TO_TASK_TASK_NAME_LABEL' | translate">

        </input-label>
        <input class="form-control"
               type="text"
               ng-required="true"
               name="task_name"
               ng-model="$ctrl.model[0].task_name"
               ng-attr-maxlength="100"
               placeholder="{{ 'PROJECT_ASSIGN_TO_TASK_TASK_NAME_PLACEHOLDER' | translate }}"
               ng-class="{
                   'has-error': $ctrl.formModel[0].submitted && !$ctrl.formModel[0].task_name.$valid
               }" />
        <div class="input-text-widget__counter">
            <span>
                {{ $ctrl.model[0].task_name.length || 0  }} / 100
            </span>
        </div>
        <div ng-if="$ctrl.formModel[0].submitted && !$ctrl.formModel[0].task_name.$valid"
             class="assign-to-task__field__error">
            {{ 'PROJECT_ASSIGN_TO_TASK_TASK_NAME_REQUIRED_ERROR' | translate }}
        </div>
    </div>
    <div class="assign-to-task__field autocomplete-field">
        <input-label label="'PROJECT_ASSIGN_TO_TASK_PROJECT_LABEL' | translate">

        </input-label>
        <div ng-if="!$ctrl.task_group.id && !$ctrl.task_group.name">
            <input class="form-control"
                   type="text"
                   name="query[0]"
                   ng-model="$ctrl.query[0]"
                   ng-required="true"
                   placeholder="{{ 'PROJECT_ASSIGN_TO_TASK_PROJECT_PLACEHOLDER' | translate }}"
                   ng-class="{
                       'has-error': $ctrl.formModel[0].submitted && !$ctrl.formModel[0].query[0].$valid
                   }"
                   typeahead="task as task for task in $ctrl.getProjects($viewValue)"
                   typeahead-on-select="$ctrl.select($item, 'project')"
                   typeahead-template-url="/components/tasks/assign-to-task/typeahead/project.html" />
            <div ng-if="$ctrl.formModel[0].submitted && (!$ctrl.formModel[0].query[0].$valid && !$ctrl.formModel[0].querySelected[0].$valid)"
                 class="assign-to-task__field__error">
                {{ 'PROJECT_ASSIGN_TO_TASK_PROJECT_REQUIRED_ERROR' | translate }}
            </div>
        </div>
        <input ng-hide="true"
               type="text"
               ng-model="$ctrl.querySelected[0]"
               ng-required="true"/>
        <div ng-if="$ctrl.task_group.name || $ctrl.task_group.id"
             class="selected-task">
            <div class="tasks__list-item project-selected">
                <div ng-if="$ctrl.task_group.new"
                     class="tasks__list-item__create">
                    {{ 'PROJECT_ASSIGN_TO_TASK_NEW_PROJECT' | translate }}:
                </div>
                <div ng-if="$ctrl.task_group.status"
                     class="tasks__list-item__status">
                <span ng-class="$ctrl.task_group.status">
                    {{ $ctrl.task_group.status }}
                </span>
                </div>
                <div class="tasks__list-item__details">
                <span class="tasks__list-item__name">
                    {{ $ctrl.task_group.name }}
                </span>
                </div>
                <div class="tasks__list-item__actions">
                <span class="dropdown dropdown-actions">
                    <a ng-click="$ctrl.remove('project')"
                       href="">
                        <i class="glyphicon glyphicon-remove">

                        </i>
                    </a>
                </span>
                </div>
            </div>
        </div>
    </div>
    <div class="assign-to-task__field description-field">
        <input-label label="'PROJECT_ASSIGN_TO_TASK_COPY_DESCRIPTION_LABEL' | translate">

        </input-label>
        <div class="description-content">
            <switcher ng-if="!$ctrl.hideCopyDescription"
                      ng-model="$ctrl.model[0].copy_description"
                      true-label=""
                      false-label=""
                      ng-change="$ctrl.switchDescription(!$ctrl.model[0].copy_description)">

            </switcher>
            <p ng-if="$ctrl.type === 'rfi'"
               class="switch-label">
                {{ 'PROJECT_ASSIGN_RFI_TO_TASK_COPY_DESCRIPTION_SWITCH' | translate }}
            </p>
            <p ng-if="$ctrl.type === 'rfp'"
               class="switch-label">
                {{ 'PROJECT_ASSIGN_RFP_TO_TASK_COPY_DESCRIPTION_SWITCH' | translate }}
            </p>
            <p ng-if="$ctrl.type === 'assignment'"
               class="switch-label">
                {{ 'PROJECT_ASSIGN_ASSIGNMENT_TO_TASK_COPY_DESCRIPTION_SWITCH' | translate }}
            </p>
            <p ng-if="$ctrl.type === 'jobopening'"
               class="switch-label">
                {{ 'MARKETPLACE_CANDIDATES_ASSIGN_TO_TASK_COPY_DESCRIPTION' | translate }}
            </p>
        </div>
        <input-textarea ng-if="!$ctrl.model[0].copy_description || $ctrl.hideCopyDescription"
                        initial-value="$ctrl.description"
                        on-change="$ctrl.onChangeField(value, 'task_description')"
                        placeholder="'PROJECT_ASSIGN_TO_TASK_COPY_DESCRIPTION_PLACEHOLDER' | translate"
                        rows="4">

        </input-textarea>
    </div>
</ng-form>
