.resource-gantt {
    position: relative;

    table, th, td {
        border: 1px solid $shortlist-grey-3;
        background-color: $white;
    }

    &__sticky {
        padding: 0 10px;
        white-space: nowrap;
    }

    .sticky-wrapper {
        overflow-x: scroll;
    }

    .sticky-header-column {
        pointer-events: none;
        z-index: 100;
        position: absolute;
        background: $white;
        left: 0;
    }

    &__header {

        &__month {
            font-size: 14px;
            font-weight: 600;
            padding: 15px 5px;
        }

        &__day {
            min-width: 50px;
            height: 50px;
            font-weight: 600;
            font-size: 12px;
            padding: 0 5px;
        }
    }

    &__day {
        position: relative;
        min-width: 50px;
        height: 50px;

        .tooltip-inner {

            div {
                text-align: left;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
            }
        }

        &-inner {
            position: absolute;
            height: 100%;
            width: 100%;
            z-index: 1;
        }

        &__task-count {
            position: absolute;
            right: 0;
            top: 0;
            font-size: 10px;
            padding: 2px 5px;
            background: $pink-1;
            font-weight: 600;
            color: $white;
        }
    }

    &__task {
        position: absolute;
        bottom: 5px;
        top: 5px;
        right: 0;
        left: 0;

        &--last {
            border-radius: 0 5px 5px 0;
            right: 5px;
        }

        &--first {
            border-radius: 5px 0 0 5px;
            left: 5px;
        }

        &--open,
        &--ongoing {
            background-color: $green-1 !important;
        }

        &--pending {
            background-color: $orange-1 !important;
        }

        &--draft {
            background-color: $shortlist-grey-2 !important;
        }

        &--awarded, &--accepted {
            background-color: $purple-1 !important;
        }

        &--under_review {
            background-color: $color-status-under-review !important;
        }

        &--archived {
            background-color: $yellow-1 !important;
        }

        &--evaluating {
            background-color: $lightblue-1 !important;
        }

        &--closed, &--rejected, &--canceled {
            background-color: $pink-1 !important;
        }
    }
}
