export default class TaskTemplatesController {
    constructor($state, tasksService) {
        'ngInject';

        Object.assign(this, {
            $state,
            tasksService,
        });

        this.taskTemplates = [];
        this.isLoading = true;
    }

    $onInit() {
        this.loadTemplatesList();
    }

    loadTemplatesList() {
        this.isLoading = true;
        this.tasksService.getTaskTemplates({without_job_opening_templates: true})
            .then((templates) => {
                this.taskTemplates = templates.sort((a, b) => a.name.localeCompare(b.name));
            })
            .finally(() => {
                this.isLoading = false;
            })
    }

    openTemplateDeleteModal(id) {
        this.tasksService.deleteTaskTemplate(id)
            .then(() => {
                this.loadTemplatesList();
            });
    }

    editTaskTemplate(id) {
        this.$state.go('app.tasks.create-task', { template_id: id, edit_template: true });
    }
}
