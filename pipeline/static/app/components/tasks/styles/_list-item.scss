& {
    display: flex;
    align-items: center;
    height: 85px;
    cursor: pointer;
    position: relative;

    &__anchor {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        padding: 10px 15px;
    }

    &__status {
        min-width: $item-status-width;
        width: $item-status-width;

        span {
            @include project-status(right);
            left: -15px;
        }
    }

    &__project-members {
        width: 145px;
    }

    &__details {
        width: 100%;
    }

    &__budget {
        min-width: 150px;
        width: 150px;

        .task-group-budget {
            display: block;
        }

        .not-set {
            color: $shortlist-grey-2;
            font-style: italic;
            font-size: 13px;
        }
    }

    &__fee {
        min-width: 150px;
        width: 150px;
        color: $shortlist-grey-1;

        .not-set {
            color: $shortlist-grey-2;
            font-style: italic;
            font-size: 13px;
        }
    }

    &__partners {
        min-width: 95px;
        width: 95px;
    }

    &__task-managers {
        min-width: 95px;
        width: 95px;
    }

    &__name {
        font-weight: 600;
        color: $shortlist-blue-1;
    }

    &__info {
        color: $shortlist-grey-2;
        margin-top: 10px;
        font-size: 12px;
        position: relative;
        font-weight: 600;

        > span:last-child {

            &::before {
                color: $shortlist-grey-3;
                content: '●';
                padding: 0 8px;
            }
        }

        > span:not(:nth-last-child(-n+2)) {
            margin-right: 12px;
        }

        i {
            font-size: 14px;
            vertical-align: middle;

            &.icon-Task {
                font-size: 24px;
                display: inline-block;
                padding-bottom: 1px;
                padding-right: 2px;
            }

            &.icon-messages-icon {
                font-size: 14px;
            }
        }

        &__task-group-name {
            display: none;
        }

        &__schedule {

            i {
                font-size: 18px;
                display: inline-block;
                padding-bottom: 1px;
            }
        }

        &__messages {

            &--unread {
                color: $shortlist-coral-dark;
            }
        }

        &__updated {
            font-weight: normal;
            font-style: italic;
        }
    }

    &__toolbar {
        margin-left: auto;
        min-width: 26px;
        width: 26px;
    }

    &__avatar-placeholder {
        @include avatar-blank(35px);

        @include shortlist-icon-xpath {
            font-size: 28px;
        }
    }
}

