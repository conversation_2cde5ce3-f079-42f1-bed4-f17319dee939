<div class="task-fee-preview">
    <div ng-if="$ctrl.task.budget_total && !$ctrl.showRateOnly" class="task-fee-preview__budget-total">
        <strong class="total">
            {{ $ctrl.task.budget_total | formatCurrencyInText }}
            {{ $ctrl.task.currency }}
        </strong>
    </div>
    <div ng-if="$ctrl.task.budget_rate_type !== null" class="task-fee-preview__hours">
        <span>
            {{ $ctrl.task.budget_rate_per_time_unit | formatCurrencyInText }}
            {{ $ctrl.task.currency }}
        </span>

        <span>/</span>
        <span>
            {{ $ctrl.getCustomRateUnit($ctrl.task.budget_rate_type, true) }}
        </span>
    </div>
    <div ng-if="$ctrl.task.budget_rate_type !== null && !$ctrl.showRateOnly" class="task-fee-preview__days">
        <span ng-if="$ctrl.task.budget_time_units_worked">
            <span>
                {{ $ctrl.task.budget_time_units_worked }} {{ $ctrl.getCustomRateUnit($ctrl.task.budget_rate_type) }}
            </span>
        </span>
    </div>
</div>
