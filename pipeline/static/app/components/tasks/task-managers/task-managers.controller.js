
class taskManagersController {
    constructor($rootScope, $scope, $filter, partnerApprovalService, taskManagersService, tasksService, _) {
        'ngInject'
        this.$rootScope = $rootScope;
        this.$scope = $scope;
        this.$filter = $filter;
        this._ = _;
        this.partnerApprovalService = partnerApprovalService;
        this.taskManagersService = taskManagersService;
        this.tasksService = tasksService;

        this.buyerEmailsAreHidden = $rootScope.tenantHasFeature('buyer_emails_hidden');
        this.managers = [];
        this.$scope.isPopoverOpen = false;
        this.$scope.$watch('isPopoverOpen', (isOpen) => {
            if (isOpen) {
                this.initialManagersCache = this._.clone(this.initialManagers);
            } else {
                this.managers = this._.clone(this.initialManagersCache);
            }
        });
    }

    $onInit() {
        this.isLoaded = false;
        this.taskManagersService.get(this.task.id, this.showTimesheetApprovers).then((res) => {
            this.initialManagers = this._.clone(res);
            this.managers = this._.clone(res);
            this.isLoaded = true;
        });

        if (!this.preview) {
            this.partnerApprovalService.getTeamMembers({}, true).then((res) => {
                this.users = res;
            });
        }
    }

    isEditable() {
        return this.task.can_edit && !this.showTimesheetApprovers;
    }

    onAdd(manager) {
        this.managers.push(manager[0]);

    }

    onRemove(manager) {
        let index = this._.findIndex(this.managers, (managerObj) => {
            return managerObj.slug === manager[0].slug;
        });

        if (index >= 0) {
            this.managers.splice(index, 1);
        }
    }

    save() {
        let slugs = [];

        this._.each(this.managers, function(manager) {
            slugs.push(manager.slug);
        });

        this.taskManagersService.save({
            id: this.task.id,
            set_all: slugs
        }).then(() => {
            this._notify();
            this.$scope.isPopoverOpen = false;
            this.initialManagers = this.managers;
            this.initialManagersCache = this.managers;

            //Check regular user is still manager
            if (!this.$rootScope.user.is_admin) {
                let index = this._.findIndex(this.managers, (managerObj) => {
                    return managerObj.slug === this.$rootScope.user.slug;
                });

                if (index === -1) {
                    this.task.can_edit = (this.task.created_by.id === this.$rootScope.user.id);
                }
            }
        })
    }

    _notify() {
        if (this._managersAreChanged()) {
            this.tasksService.notifyApi(this.task.id);
        }
    }

    _managersAreChanged() {
        let identical = true;

        if (this.managers.length != this.initialManagers.length) {
            return true;
        }

        _.each(this.managers, (manager) => {
            if (identical) {
                let index = this._.findIndex(this.initialManagers, (initManager) => {
                    return manager.slug === initManager.slug;
                });

                identical = (index >= 0);
            }
        });

        return !identical;
    }
}

export default taskManagersController
