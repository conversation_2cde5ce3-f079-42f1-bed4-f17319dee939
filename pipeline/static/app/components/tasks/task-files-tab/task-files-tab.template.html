<div class="tasks-files-tab">
    <h1 class="pull-left">
        {{ 'TASKS_FILES_TAB_HEADER' | translate }}
    </h1>
    <div class="button-container pull-right" ng-if="$ctrl.canUploadNewFile()">
        <a ng-click="$ctrl.addNewFile()"
           class="add-new-document"
           href>
            <i class="pull-left material-icons ng-scope">cloud_upload</i>
            {{ 'TASKS_FILES_UPLOAD_NEW_FILE' | translate }}
        </a>
    </div>

    <table class="agreement-listing document-listing tasks-files-tab__list"
           ng-if="$ctrl.taskFiles.length">
        <thead>
            <tr>
                <th ng-click="$ctrl.toggleSorting('name')"
                    class="table__header-status sortable"
                    ng-class="$ctrl.sortingClass('name')"> {{ 'TASKS_TASK_FILES_TAB_NAME_HEADER' | translate }}
                </th>

                <th ng-click="$ctrl.toggleSorting('uploaded_at')"
                    class="table__header-date tasks-files-tab__list__created_at sortable"
                    ng-class="$ctrl.sortingClass('uploaded_at')"> {{ 'TASKS_TASK_FILES_TAB_UPLOADED_HEADER' | translate }}
                </th>

                <th ng-click="$ctrl.toggleSorting('author')"
                    class="table__header-date sortable tasks-files-tab__list__uploaded_by"
                    ng-class="$ctrl.sortingClass('author')"> {{ 'TASKS_TASK_FILES_TAB_UPLOADED_BY_HEADER' | translate }}
                </th>

                <th class="table__header-actions tasks-files-tab__list__extra-actions"></th>
            </tr>
        </thead>
        <tbody>
            <tr ng-repeat="file in $ctrl.taskFiles track by $index">
                <td class="tasks-files-tab__list__name">
                    <a href="{{ $ctrl.downloadLink(file) }}" target="_blank">
                        {{ file.name }}
                    </a>
                </td>
                <td class="tasks-files-tab__list__created_at">
                    {{ file.uploaded_at | date:"/Y/M/d" }}
                </td>
                <td class="tasks-files-tab__list__uploaded_by">
                    <avatar ng-if="file.uploaded_by_user"
                        size="35"
                        user="file.uploaded_by_user">
                    </avatar>
                    <avatar ng-if="file.uploaded_by_vendor"
                        size="35"
                        user="file.uploaded_by_vendor">
                    </avatar>
                </td>
                <td class="tasks-files-tab__list__extra-actions">
                    <div ng-if="file.can_delete">
                        <div class="dropdown-actions dropdown">
                            <a class="material-icons" data-toggle="dropdown" href>more_vert</a>
                            <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                <li>
                                    <a ng-click="$ctrl.removeFile(file)" href>
                                        {{ 'AGREEMENTS_TABLE_ACTION_DELETE' | translate }}
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>

    <div class="payment-list__no-payments tasks-files-tab__list__no-files"
         ng-if="!$ctrl.task.files_count">
        <div class="v-align">
            <div class="v-align-wrapper ng-scope">
                <div class="vendor-task-info info ng-binding">
                    {{ 'TASKS_FILES_NO_FILE_UPLOADED' | translate }}
                </div>
            </div>
        </div>
    </div>
</div>
