<div class="task-notification-box"
     ng-if="['rejected', 'canceled'].includes($ctrl.task.status)">

    <notification-info-box
            ng-if="$ctrl.task.status == 'rejected'"
            type="warn">
        <div ng-if="$ctrl.task.rejection_reason">
            {{ 'TASKS_REJECTION_INFO_BOX_HEADER' | translate }} {{ 'TASKS_REJECTION_INFO_BOX_WITH_REASON' | translate }}<br>
            {{ $ctrl.task.rejection_reason }}
        </div>
        <div ng-if="!$ctrl.task.rejection_reason">
            {{ 'TASKS_REJECTION_INFO_BOX_HEADER' | translate }} {{ 'TASKS_REJECTION_INFO_BOX_NO_REASON' | translate }}
        </div>
    </notification-info-box>

    <notification-info-box
            ng-if="$ctrl.task.status == 'canceled' && !$root.is_user"
            type="warn">
        {{ 'TASKS_CANCELED_INFO_BOX' | translate }}
    </notification-info-box>

</div>
