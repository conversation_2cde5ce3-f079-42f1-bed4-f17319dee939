<div class="task-milestones-edit-list clearfix">

    <div ng-if="$ctrl.getMilestonesList($ctrl.milestones).length === 0"
         class="milestones-empty-state">
        {{ 'TASK_MILESTONE_LIST_ZERO_STATE' | translate }}
    </div>

    <div ng-if="$ctrl.getMilestonesList($ctrl.milestones).length > 0"
         class="clearfix">

        <ul class="task-milestones">
            <li class="task-milestones__row task-milestones__row__headers clearfix">
                <div class="task-milestones__row--managers">{{ 'TASK_MILESTONE_MANAGERS_HEADER' | translate }}</div>
                <div class="task-milestones__row--fee">{{ 'TASK_MILESTONE_FEE_HEADER' | translate }}</div>
            </li>
            <li ng-repeat="milestone in $ctrl.getMilestonesList($ctrl.milestones) track by $index"
                ng-click="$event.originalEvent.dropdown || $ctrl.editMilestone(milestone)"
                class="task-milestones__row clearfix">

                <div class="task-milestones__row--index"ng-click="$ctrl.editMilestone(milestone)">
                    {{ $index + 1 }}
                </div>

                <div class="task-milestones__row--name">
                    {{ milestone.name }}
                </div>

                <div class="task-milestones__row--managers">
                    <r-logo-group 
                        users="milestone.managers"
                        size="35"
                        visible-items="3">
                    ></r-logo-group>
                </div>

                <div class="task-milestones__row--fee">
                    {{ $ctrl.parseFee(milestone.fee_percent) }}%
                </div>

                <div class="task-milestones__row--actions">
                    <div class="dropdown-actions dropdown"
                         ng-click="$event.originalEvent.dropdown = true">
                        <a class="material-icons" data-toggle="dropdown" href>more_vert</a>
                        <ul class="dropdown-menu dropdown-menu-right"
                            role="menu">
                            <li>
                                <a ng-click="$ctrl.editMilestone(milestone)" href>
                                    {{ 'TASK_MILESTONE_EDIT_BTN' |translate }}
                                </a>
                            </li>
                            <li>
                                <a ng-click="$ctrl.removeMilestone(milestone)" href>
                                    {{ 'TASK_MILESTONE_DELETE_BTN' | translate }}
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="btn-sort">
                    <a class="btn-sort-up"
                       ng-click="$ctrl.sortUp($index, $event)"
                       ng-show="!$first"
                       href>
                        <i class="material-icons">
                            keyboard_arrow_up
                        </i>
                    </a>
                    <a class="btn-sort-down"
                       ng-click="$ctrl.sortDown($index, $event)"
                       ng-show="!$last"
                       href>
                        <i class="material-icons">
                            keyboard_arrow_down
                        </i>
                    </a>
                </div>

            </li>
        </ul>

    </div>

    <a class="onboarding-template-edit__add-field"
       ng-click="$ctrl.createMilestone()"
       href>
        <i class="icon-Add"></i> {{ 'TASK_MILESTONE_ADD_BTN' | translate }}
    </a>

</div>
