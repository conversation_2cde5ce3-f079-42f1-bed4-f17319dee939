.task-milestones-edit-list {
    .milestones-empty-state {
        text-align: center;
        border: solid 1px $shortlist-grey-3;
        padding: 30px;
        font-style: italic;
        font-size: 12px;
        color: $shortlist-grey-2;
        margin-bottom: 20px;
        border-radius: 3px;
    }

    .task-milestones {
        margin: 0;
        padding: 0;
        list-style-type: none;
        cursor: pointer;

        &__row {
            border-radius: 5px;
            border: 1px solid $shortlist-grey-3;
            padding: 20px;
            margin-bottom: 20px;
            position: relative;

            > div {
                top: 20px;
                &.task-milestones__row--managers {
                    top: 15px;
                }
            }

            &--index {
                position: relative;
                font-weight: 700;
                font-size: 18px;
                margin-left: 0;
                width: 30px;
                top: 0 !important;
            }

            &--name {
                position: absolute;
                left: 50px;
            }

            &--managers {
                position: absolute;
                width: 100px;
                right: 90px;
            }

            &--fee {
                position: absolute;
                width: 40px;
                right: 43px;
            }

            &--actions {
                position: absolute;
                right: 10px;
                width: 20px;
                color: $shortlist-grey-2;
                top: 20px;

                a {
                    color: $shortlist-grey-2;
                }
            }

            &__headers {
                border: none;
                padding: 0;
                color: $shortlist-grey-2;
                top: -45px;
                margin-bottom: 0;

                .task-milestones__row--managers {
                    right: 95px;
                    top: 22px !important;
                }

                .task-milestones__row--fee {
                    right: 45px;
                }
            }
        }

        .btn-sort {
            position: absolute;
            right: -5px;
            top: 0;

            &-up {
                top: 5px;
            }

            &-down {
                bottom: -65px;
            }

            i {
                font-size: 26px;
            }

            a {
                color: $shortlist-grey-3;
                position: absolute;

                &:hover {
                    color: $shortlist-grey-2;
                }
            }
        }
    }
}
