class TaskMilestone {

    static STATUS_NOT_COMPLETED = 'not_completed';
    static STATUS_SUBMITTED = 'submitted';
    static STATUS_REJECTED = 'rejected';
    static STATUS_APPROVED = 'approved';
    static STATUS_PAID = 'paid';

    constructor(data) {
        data && Object.assign(this, data);
        this.normalize();

        this.maxFeePercent = 100;
    }


    normalize() {
        this.name = this.name || '';
        this.description = this.description || '';
        this.fee_percent = this.fee_percent || 0;
        this.milestone_order = this.milestone_order || 0;
        this.managers = this.managers || [];
    }


    setMaxFeePercent(maxFee = 100) {
        this.maxFeePercent = maxFee;
    }


    validate($translate = null) {
        const errors = {
            name: false,
            fee_percent: false,
            managers: false,
        };

        if (!this.name || (this.name.trim() === '')) {
            errors.name = [$translate.instant('TASK_MILESTONE_VALIDATION_NAME')];
        }

        this.fee_percent = Math.round(parseFloat(this.fee_percent) * 100) / 100;

        if (!Number.isFinite(this.fee_percent)) {
            errors.fee_percent = [$translate.instant('TASK_MILESTONE_VALIDATION_FEE')];
        }

        if (this.fee_percent && ((this.fee_percent < 0) || (this.fee_percent > this.maxFeePercent) || (this.maxFeePercent < 0))) {
            const errMessage = this.maxFeePercent === 0 ?
                $translate.instant('TASK_MILESTONE_VALIDATION_FEE_MINIMAL') :
                $translate.instant('TASK_MILESTONE_VALIDATION_FEE_RANGE', {maxFee: this.maxFeePercent});
            errors.fee_percent = Array.isArray(errors.fee_percent) ?
                errors.fee_percent.push(errMessage) : [errMessage];
        }

        return errors;
    }
}

export default TaskMilestone;
