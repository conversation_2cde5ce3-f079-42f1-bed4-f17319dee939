class taskListingService {
    constructor() {
        'ngInject'
    }

    getDropdownConfig(type, options, onItemSelect, onDeselectAll) {
        return {
            options: options,
            events: {
                onItemSelect: (item) => onItemSelect(item, type),
                onDeselectAll: (item) => onDeselectAll(item, type)
            },
            extraSettings: {
                enableSearch: false,
                selectionLimit: 1,
                alwaysOpen: true,
                closeOnBlur: true
            },
            selectedModel: []
        }
    }
}

export default taskListingService
