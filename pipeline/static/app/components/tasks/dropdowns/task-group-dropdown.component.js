import {
    defaultTaskGroupState
} from '../config';

export default {
    bindings: {
        taskGroup: '<',
        workflow: '<?',
        redirectToDetails: '<?'
    },
    template: `
        <div class="dropdown" ng-mouseover="$ctrl.getWorkflow()">
            <a class="btn-actions-dots btn-actions btn"
               href
               data-toggle="dropdown">
                <i class="icon-more-icon"></i>
            </a>
            <ul class="dropdown-menu dropdown-menu-right" ng-if="$ctrl.workflow">
                <li ng-if="$ctrl.hasAction('edit')">
                    <a ng-click="$ctrl.editTaskGroup()"
                       class="menu-item"
                       href>
                        {{ 'TASKS_TASK_GROUP_VIEW_ACTIONS_EDIT' | translate }}
                    </a>
                </li>
                <li ng-if="$ctrl.hasAction('duplicate')">
                    <a ng-click="$ctrl.duplicateTaskGroup()"
                       class="menu-item"
                       href>
                        {{ 'TASKS_TASK_GROUP_VIEW_ACTIONS_DUPLICATE' | translate }}
                    </a>
                </li>
                <li ng-if="$ctrl.hasAction('all_live')">
                    <a ng-click="$ctrl.makeAllLive()"
                       class="menu-item"
                       href>
                        {{ 'TASKS_TASK_GROUP_VIEW_ACTIONS_MAKE_ALL_LIVE' | translate }}
                    </a>
                </li>
                <li ng-if="$ctrl.hasAction('complete_live_tasks')">
                    <a ng-click="$ctrl.makeAllLiveCompleted()"
                       class="menu-item"
                       href>
                        {{ 'TASKS_TASK_GROUP_VIEW_ACTIONS_COMPLETE_LIVE_TASKS' | translate }}
                    </a>
                </li>
                <li ng-if="$ctrl.hasAction('remove_task_group')">
                    <a ng-click="$ctrl.removeTaskGroup()"
                       class="menu-item"
                       href>
                        {{ 'TASKS_TASK_GROUP_VIEW_ACTIONS_REMOVE' | translate }}
                    </a>
                </li>
                <li ng-if="$ctrl.hasAction('archive_task_group')">
                    <a ng-click="$ctrl.archiveTaskGroup()"
                       class="menu-item"
                       href>
                        {{ 'TASKS_TASK_GROUP_VIEW_ACTIONS_ARCHIVE' | translate }}
                    </a>
                </li>
                <li ng-if="!$ctrl.hasAnyAction()">
                    <em class="empty-info">{{ 'TASKS_TASK_GROUP_VIEW_ACTIONS_NONE' | translate }}</em>
                </li>
            </ul>
        </div>
    `,
    controller($state, taskGroupsService) {
        'ngInject';

        this.getWorkflow = () => {
            if(!this.workflow && !this.gettingWorkflow && this.taskGroup && this.taskGroup.id) {
                this.gettingWorkflow = true;
                taskGroupsService.getWorkflow(this.taskGroup.id)
                .then((workflow) => {
                    this.workflow = workflow;
                })
                .finally(() => this.gettingWorkflow = false);
            }
        };

        this.redirect = () => {
            if(this.redirectToDetails) {
                taskGroupsService.goToTaskGroup(this.taskGroup.id);
            } else {
                $state.go($state.current, {}, {reload: true});
            }
        };

        this.hasAnyAction = () => {
            return this.hasAction('edit')
                || this.hasAction('duplicate')
                || this.hasAction('all_live')
                || this.hasAction('remove_task_group')
                || this.hasAction('archive_task_group');
        };

        this.hasAction = (action) => {
            return taskGroupsService.hasAction(this.workflow, action);
        };

        this.editTaskGroup = () => {
            taskGroupsService.editTaskGroup(this.taskGroup.id);
        };

        this.duplicateTaskGroup = () => {
            taskGroupsService.duplicateTaskGroup(this.taskGroup.id, this.taskGroup.name).then((variables) => {
                $state.go(defaultTaskGroupState, {
                    id: variables.response.result
                });
            });
        };

        this.makeAllLive = () => {
            taskGroupsService.makeAllLive(this.taskGroup.id)
            .then(this.redirect);
        };

        this.makeAllLiveCompleted = () => {
            taskGroupsService.makeAllLiveCompleted(this.taskGroup.id)
            .then(this.redirect);
        };

        this.removeTaskGroup = () => {
            taskGroupsService.removeTaskGroup(this.taskGroup.id)
            .then(() => $state.go('app.tasks.task-groups', {}, {reload: true}));
        };

        this.archiveTaskGroup = () => {
            taskGroupsService.archiveTaskGroup(this.taskGroup.id)
            .then(this.redirect);
        }
    }
}
