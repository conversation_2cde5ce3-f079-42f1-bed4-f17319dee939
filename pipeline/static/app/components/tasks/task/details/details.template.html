<div class="tasks__task__details">
    <div class="tasks__task__details__info">

        <!-- Task description -->
        <div class="pre-line ws-body-large quill-list-preview"
             ng-bind-html="$ctrl.task.description | replaceDataListWithClass | linkysWithHtml"></div>
        <div class="empty-value"
             ng-if="!$ctrl.task.description">
            {{ 'TASKS_TASK_GROUPS_NO_DESCRIPTION' | translate }}
        </div>

        <!-- Task skills -->
        <div class="tasks__task__details__skills">
            <project-skills-required skills="$ctrl.task.skills"></project-skills-required>
        </div>

        <!-- Task files -->
        <div class="tasks__task__details__files">
            <task-files
                data-files="$ctrl.taskFiles"
                data-read-only="true">
            </task-files>
        </div>

        <!-- Task custom fields -->
        <div class="tasks__task__details__custom-fields"
             ng-if="$ctrl.taskCustomFields.length">
            <project-custom-field
                    related-item="$ctrl.task"
                    field="field"
                    value="field.value"
                    read-only="true"
                    ng-repeat="field in $ctrl.taskCustomFields">
            </project-custom-field>
        </div>

        <!-- Task milestones -->
        <task-milestones-list
                show-header="true"
                container-class="'tasks__task__details__milestones'"
                task="$ctrl.task">
        </task-milestones-list>

        <!-- Task standard timesheets -->
        <div ng-if="$ctrl.hasAccessToStandardTimeSheets()"
             class="tasks__task__details__timesheet">
            <h1>
                {{ 'TIMESHEETS_TIMESHEET_LABEL' | translate }}
            </h1>
            <r-timesheets-components-timesheet-list
                task="$ctrl.task"
            ></r-timesheets-components-timesheet-list>
        </div>


        <!-- Task extended timesheets -->
        <div ng-if="$ctrl.hasAccessToExtendedTimeSheets() && $ctrl.timesheetConfig"
             class="tasks__task__details__timesheet">
            <h1>
                {{ 'TIMESHEETS_TIMESHEET_LABEL' | translate }}
            </h1>
            <task-extended-timesheet
                ng-if="$ctrl.timesheetConfig.periods.length > 0"
                task="$ctrl.task"
                timesheet-config="$ctrl.timesheetConfig"
            ></task-extended-timesheet>

            <div class="vendor-task-info"
                 ng-if="$ctrl.timesheetConfig.periods.length === 0">
                {{ 'TIMESHEETS_TIMESHEET_NO_TO_FILL' | translate }}
            </div>
        </div>


        <!-- Task invoices -->
        <div ng-if="$ctrl.hasAccessToPayments()"
            class="tasks__task__details__invoices">
            <r-invoices-list task="$ctrl.task" on-click-show-more="$ctrl.goToInvoicesTab"></r-invoices-list>
        </div>

    </div>
    <div class="tasks__task__details__sidebar">
        <task-details-sidebar task="$ctrl.task"
                              workflow="$ctrl.workflow">
        </task-details-sidebar>
    </div>
</div>
