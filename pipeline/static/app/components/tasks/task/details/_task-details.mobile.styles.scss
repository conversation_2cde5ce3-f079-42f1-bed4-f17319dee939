@media (max-width: $screen-md) {

    .tasks__task__details {
        display: flex;
        flex-direction: column-reverse;

        &__info {
            padding-right: 0;
        }

        &__sidebar {
            padding-left: 0;
            border-left: 0;
        }

        &__timesheet {

            .task-weekly-timesheet__week {

                &-row {
                    padding-right: 0;
                    display: flex;
                    flex-direction: column;

                    .input-calendar {

                        &__days {
                            border-right: 0;
                        }

                        &__day {

                            input {
                                padding: 6px;
                            }
                        }

                        &__weekdays {
                            border-right: 0;
                            margin-top: 10px;
                        }

                        &__header {
                            position: absolute;
                            top: 75px;
                            left: 0;
                            right: 0;
                            text-align: left;

                            &__month {
                                margin-left: 0;
                            }
                        }
                    }
                }

                &-total {
                    padding-top: 65px;
                    width: 100%;
                    position: inherit;
                    right: auto;
                    bottom: auto;
                }
            }
        }
    }
}
