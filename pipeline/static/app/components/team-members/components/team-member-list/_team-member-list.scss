.team-member-list {

    &__inactive-team-members {
        position: relative;
        background: rgba(70, 89, 182, 0.05);
        border: 1px solid rgba(70, 89, 182, 0.25);
        border-radius: 2px;
        padding: 20px 40px;
        margin-top: 30px;
        min-height: 100px;

        &__copy {
            margin-left: 120px;
            margin-right: 175px;
            padding-right: 20px;
            padding-top: 4px;

            h4, p {
                opacity: 0.55;
                color: $shortlist-blue-1;
            }

            p {
                font-size: 14px;
            }
        }

        &__button {
            border-left: 1px solid rgba(70, 89, 182, 0.25);
            padding: 15px 0 15px 40px;
            position: absolute;
            top: 20px;
            right: 40px;

            .btn-secondary {
                opacity: 0.4;

                .btn-inner-text {
                    padding: 10px 25px;
                }
            }
        }

        .icon-findusers {
            position: absolute;
            font-size: 58px;
            left: 70px;
        }
    }

    &__headers {
        font-weight: 600;
        color: $shortlist-grey-2;

        &__team-mate {
            float: left;
            width: 120px;
        }

        &__team {
            float: right;
            width: 145px;
        }

        &__role {
            float: right;
            width: 135px;
        }
    }

    &__search {
        position: relative;
        margin-top: 30px;
        margin-bottom: 10px;
        padding-right: 170px;

        .sort-dropdown {
            position: absolute;
            right: 10px;
            bottom: 0;
        }

        &__search-icon {
            position: absolute;
            top: 12px;
            left: 10px;
            color: $shortlist-grey-2;
        }

        &__search-clear {
            position: absolute;
            top: 15px;
            left: 350px;
            color: $shortlist-grey-2;
        }

        input {
            padding-left: 36px;
            background: transparent;
            border: 0;
            width: 345px;

            &:focus, &:hover, &:visited {
                border: 0;
                background: transparent;
            }
        }
    }

    &__team-members {
        position: relative;
        height: 83px;

        &:not(:last-child) {
            border-bottom: 1px solid $shortlist-grey-3;
        }
    }

    &__team-member {
        padding: 23px 315px 23px 50px;
        text-overflow: ellipsis;
        overflow-x: hidden;
        white-space: nowrap;

        &__avatar {
            position: absolute;
            top: 20px;
            left: 0;
        }

        &__email {
            font-size: 12px;
            color: rgba(80, 89, 128, 0.5);
        }

        &__name, &__email {
            font-weight: 600;
            text-overflow: ellipsis;
            overflow-x: hidden;
            white-space: nowrap;
        }

        &__team {
            position: absolute;
            top: 20px;
            right: 145px;

            select {
                width: 135px;
            }
        }

        &__role {
            position: absolute;
            top: 20px;
            right: 0;

            select {
                width: 135px;
            }
        }

        &__edit-permissions {
            position: absolute;
            top: 35px;
            right: 300px;
        }
    }

    .block-tile {
        padding: 20px 50px;
    }

    .loader {
        margin-top: 100px;
    }
}
