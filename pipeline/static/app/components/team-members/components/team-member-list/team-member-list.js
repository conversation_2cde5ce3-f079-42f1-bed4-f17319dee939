'use strict';

angular.module('pipeline').component('teamMemberList', {
    templateUrl: '/components/team-members/components/team-member-list/team-member-list.html',
    controller($rootScope, $translate, $http, userUtils, requestStoreService, userPermissionsService, TeamsService, userAuth) {
        'ngInject';

        const events = [];
        events.push($rootScope.$on('userInvited', () => this.updateUsers(true)));
        events.push($rootScope.$on('invitesUpdated', () => this.updateUsers(true)));

        const escapeRegExp = str => str.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g, "\\$&");

        this.updateUserPermission = function (user) {
            return userPermissionsService.setRolesForUser(user.slug, [user.primary_role_name])
                .then(() => this.updateUsers(true));
        };

        this.updateUserTeam = (user) => {
            const params = { team: parseInt(user.team, 10) };
            $http.patch(`/api/users/${user.slug}/`, params)
                .then(() => {
                    this.updateUsers();
                    if (user.slug === $rootScope.user.slug) {
                        userAuth.getUserData(true);
                    }
                });
        };

        this.filterTeamMembers = (teamMember) => {
            const searchFor = escapeRegExp(this.searchTeamMember.toUpperCase());
            if (((this.searchTeamMember != null) === '') ||
                teamMember.full_name.toUpperCase().match(searchFor) ||
                (teamMember.email != null ? teamMember.email.toUpperCase().match(searchFor) : undefined)) {
                return true;
            } else {
                return false;
            }
        };

        this.sort = (sort) => {
            this.selectedSort = sort;
            return this.updateUsers();
        };

        this.updateUsers = (force) => {
            if (force == null) { force = false; }
            this.isLoading = true;
            return requestStoreService.getEndpoint('users', {
                force,
                params: {
                    ordering: this.selectedSort.key,
                    include_inactive: true
                }
            }).then(res => {
                const users = _.sortBy(res, user => user.email !== $rootScope.user.email)
                    .map((user) => {
                        if (user.team && user.team.id) {
                            user.team = user.team.id.toString();
                        }
                        return user;
                    });
                this.inactiveTeamMembers = _.filter(users, user => user.is_active !== true);
                return this.activeTeamMembers = _.difference(users, this.inactiveTeamMembers);
            }).finally(() => {
                return this.isLoading = false;
            });
        };

        this.pendingInvites = function ($event) {
            $($event.currentTarget).blur();
            return userUtils.pendingInvites();
        };

        this.$onInit = () => {
            this.roles = [];
            this.teams = [];
            this.loggedUser = $rootScope.user;
            this.searchTeamMember = '';
            this.isLoading = false;
            this.activeTeamMembers = [];
            this.inactiveTeamMembers = [];
            this.sortOptions = [
                {
                    key: 'primary_role_name,last_name,first_name',
                    name: $translate.instant('TEAM_MEMBERS_SORT_BY_RANK')
                },
                {
                    key: '-created_at',
                    name: $translate.instant('TEAM_MEMBERS_SORT_BY_DATE_JOINED')
                },
                {
                    key: '-last_login',
                    name: $translate.instant('TEAM_MEMBERS_SORT_BY_LAST_LOGIN')
                },
                {
                    key: 'last_name,first_name',
                    name: $translate.instant('TEAM_MEMBERS_SORT_BY_NAME')
                }
            ];
            this.selectedSort = this.sortOptions[0];
            this.updateUsers(true);

            userPermissionsService.getRoles(true).then((roles) => {
                this.roles = roles;
            });

            TeamsService.getTeams()
                .then((teams) => {
                    this.teams = teams;
                    this.teams.unshift({ id: null, name: '' });
                });
        };

        this.$onDestroy = () => Array.from(events).map((event) => event());

    }
}
);
