<div class="v3-modal view-payment-modal" data-testid="PaymentDetailsView">

    <div class="modal-header clearfix">
        <div class="clearfix">
            <a class="close" ng-click="$ctrl.dismiss()" aria-hidden="true" href>
                <span class="icon-cross-icon"></span>
            </a>
        </div>
        <div class="loader" ng-show="$ctrl.vendorDataLoading">
            <loader></loader>
        </div>
        <r-vendor-card
            ng-if="!$ctrl.vendorDataLoading && $ctrl.vendor"
            vendor="$ctrl.vendor"
            variant="'row'"
            on-vendor-name-click="$ctrl.vendorProfilePage">
        </r-vendor-card>
    </div>

    <div class="modal-body">

        <div class="payment-header"
            ng-if="$ctrl.paymentHeaderMsg">
            <r-alert
                message="$ctrl.paymentHeaderMsg"
                tag="$ctrl.paymentHeaderTag"
                size="'large'"></r-alert>
        </div>

        <div ng-if="$ctrl.$rootScope.is_user">
            <r-horizontal-navigation items="$ctrl.menuItems" active-item="$ctrl.activeTab" />
        </div>

        <section class="tabs">
            <div class="tab"
                ng-class="{ 'tab-details': $ctrl.$rootScope.is_user }"
                ng-if="$ctrl.activeTab === 0">

                <r-invoice-details invoice="$ctrl.payment"></r-invoice-details>

                <div class="clearfix view-payment-modal__line-items">

                    <div class="loader" ng-show="$ctrl.isLineItemsLoading">
                        <loader></loader>
                    </div>

                    <r-line-items-table
                        ng-if="!$ctrl.isLineItemsLoading && !$ctrl.globalLineItemTemplateFields.length"
                        default-value="$ctrl.paymentWithLineItems"
                        on-change="$ctrl.onLineItemsChange"
                        categories="$ctrl.lineItemCategories"
                        tax-rates="$ctrl.taxRates"
                        errors="$ctrl.lineItemsErrors"
                        read-only="true"></r-line-items-table>

                    <r-line-items-table-with-cf
                        ng-if="!$ctrl.isLineItemsLoading && $ctrl.globalLineItemTemplateFields.length"
                        default-value="$ctrl.paymentWithLineItems"
                        on-change="$ctrl.onLineItemsChange"
                        categories="$ctrl.lineItemCategories"
                        tax-rates="$ctrl.taxRates"
                        custom-fields="$ctrl.globalLineItemTemplateFields"
                        errors="$ctrl.lineItemsErrors"
                        read-only="true"></r-line-items-table-with-cf>
                </div>

                <div class="clearfix">
                    <div class="row">
                        <div class="col-sm-8">
                            <div ng-if="$ctrl.userPermissionsService.hasAccessToTasks() && $ctrl.payment.related_task.name">
                                <input-label
                                    label="'PAYMENTS_VIEW_MODAL_LABEL_TASK' | translate">
                                </input-label>
                                <div>
                                    <a class="view-payment-modal__project-name"
                                        href="{{ $ctrl.taskDetailsPage() }}">
                                        {{ $ctrl.payment.related_task.name }}
                                    </a>
                                </div>
                            </div>

                            <div ng-if="$ctrl.timesheets.length > 0"
                                class="view-payment-modal__note_and_file">
                                <div class="view-payment-modal__note">
                                    <input-label
                                        label="'PAYMENTS_VIEW_MODAL_LABEL_TIMESHEETS' | translate">
                                    </input-label>
                                    <p ng-repeat="timesheet in $ctrl.timesheets track by $index">
                                        {{ timesheet.date }} ({{ timesheet.status }})
                                    </p>
                                </div>
                            </div>

                            <div class="view-payment-modal__note_and_file"
                                ng-if="$ctrl.payment.note || $ctrl.payment.filename">

                                <div class="view-payment-modal__note"
                                    ng-if="$ctrl.payment.note">
                                    <input-label
                                        label="'PAYMENTS_VIEW_MODAL_LABEL_NOTE' | translate">
                                    </input-label>
                                    <div>
                                        <p class="pre-line">{{ $ctrl.payment.note }}</p>
                                    </div>
                                </div>

                                <div class="view-payment-modal__file"
                                    ng-if="$ctrl.payment.filename">
                                    <payment-download-link payment="$ctrl.payment">
                                        <i class="icon-file-empty"></i>
                                        <p>{{ $ctrl.payment.filename }}</p>
                                    </payment-download-link>
                                </div>

                            </div>
                            <div class="view-payment-modal__entity"
                                ng-if="$ctrl.userPermissionsService.hasAccessToTasks() && $ctrl.payment.entity">
                                <r-entity-details entity-id="$ctrl.payment.entity.id" variant="'payment-details'"></r-entity-details>
                            </div>

                            <div ng-if="$ctrl.payment.contract && $ctrl.userPermissionsService.hasAccessToContracts()">
                                <input-label
                                    label="'PAYMENTS_VIEW_MODAL_LABEL_CONTRACT' | translate">
                                </input-label>
                                <div>
                                    <a class="view-payment-modal__project-name"
                                        href="{{ $ctrl.getContractDetailsPageUrl() }}">
                                        {{ $ctrl.payment.contract.name }}
                                    </a>
                                </div>
                            </div>

                        </div>
                        <div class="col-sm-4">
                            <div class="view-payment-modal__partner"
                                ng-if="$root.tenantHasFeature('payments_assigned_buyer') && $ctrl.assigned_buyer">
                                <input-label
                                    label="'PAYMENTS_VIEW_MODAL_LABEL_ASSIGNED_BUYER' | translate">
                                </input-label>
                                <div ng-if="$ctrl.assigned_buyer" class="selected-vendor">
                                    <avatar-widget
                                        data-user="$ctrl.assigned_buyer"
                                        size="45">
                                    </avatar-widget>
                                    {{ $ctrl.assigned_buyer.full_name || $ctrl.assigned_buyer.email }}
                                </div>
                            </div>

                            <div class="clearfix">
                                <span ng-if="$ctrl.payment.status !== 'paid'">
                                    <input-label
                                        label="'PAYMENTS_VIEW_MODAL_LABEL_DUE' | translate">
                                    </input-label>
                                </span>
                                <div class="view-payment-modal__payment-due">
                                    <span ng-if="$ctrl.payment.status !== 'paid'">
                                        {{ $ctrl.payment.payment_due | date:"/Y/M/d" | default_value:"--/--/----" }}
                                    </span>
                                </div>
                            </div>

                            <div class="view-payment-modal__payout-method" ng-if="$ctrl.payment.payout_method">
                                <input-label
                                    label="'PAYMENTS_VIEW_MODAL_LABEL_PAYOUT_METHOD' | translate">
                                </input-label>
                                <div><r-payout-method-logo method-type="$ctrl.payment.payout_method.type"></r-payout-method-logo></div>
                                <div class="view-payment-modal__payout-method-name" ng-if="$ctrl.payment.payout_method.display_name">
                                    {{ $ctrl.payment.payout_method.display_name }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row view-payment-modal__milestone clearfix" ng-if="$ctrl.userPermissionsService.hasAccessToTasks() && $ctrl.payment.related_milestone.name">
                    <div class="col-sm-12">
                        <div>
                            <input-label
                                label="'PAYMENTS_VIEW_MODAL_LABEL_MILESTONE' | translate">
                            </input-label>
                            <div>
                                <p class="view-payment-modal__milestone-name">
                                    {{ $ctrl.payment.related_milestone.name }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div ng-if="$ctrl.paymentCustomFields.length"
                    class="view-payment-modal__custom_fields clearfix">
                    <project-custom-field
                        related-item="$ctrl.payment"
                        field="field"
                        value="field.value"
                        read-only="true"
                        ng-repeat="field in $ctrl.paymentCustomFields">
                    </project-custom-field>
                </div>

                <div class="view-payment-modal__rejection-reason"
                    ng-if="$ctrl.payment.status == 'rejected' && $ctrl.payment.reason">
                    <div class="view-payment-modal__note">
                        <input-label
                            label="'PAYMENTS_VIEW_MODAL_LABEL_REASON_FOR_REJECTION' | translate">
                        </input-label>
                        <div>
                            <p class="pre-line">{{ $ctrl.payment.reason }}</p>
                        </div>
                    </div>
                </div>

                <div class="view-payment-modal__processing-issue-description"
                    ng-if="$ctrl.payment.processing_problem_manual && $ctrl.payment.processing_problem_reason">
                    <div class="view-payment-modal__note">
                        <input-label
                            label="'PAYMENTS_VIEW_MODAL_LABEL_ISSUE_DESCRIPTION' | translate">
                        </input-label>
                        <div>
                            <p class="pre-line">{{ $ctrl.payment.processing_problem_reason }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tab tab-payment-logs"
                ng-if="$ctrl.activeTab === 1">
                <r-action-log-history
                    content-type="'documents.payment'"
                    object-id="$ctrl.payment.id">
                </r-action-log-history>
            </div>

        </section>

        <r-upsell-banner variant="'payout'"></r-upsell-banner>
    </div>

    <div class="view-payment-modal__footer">
        <r-invoice-actions
            invoice="$ctrl.payment"
            on-executed="$ctrl.onInvoiceActionExecuted">
        </r-invoice-actions>
    </div>

</div>
