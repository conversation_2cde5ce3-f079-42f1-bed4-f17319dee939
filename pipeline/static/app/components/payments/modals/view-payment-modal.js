'use strict';

import Payment from '../classes/payment.class';
import LineItem from '../classes/line-item.class';
import { Api, Utils, VendorHelpers } from 'shortlist-react';

const controller = function (
    $scope,
    $state,
    $rootScope,
    $modalInstance,
    $timeout,
    $translate,
    $http,
    $filter,
    paymentsService,
    tasksService,
    payment,
    task,
    userPermissionsService,
    userService,
    vendorUtils,
    WorkerService,
    LineItemService,
    customFieldTemplatesService,
) {
    this.$rootScope = $rootScope;

    this.payment = payment;
    this.assigned_buyer = null;
    this.activeTab = 0;

    this.isLineItemsLoading = true;
    this.lineItems = [];
    this.lineItemsErrors = { items: {} };
    this.lineItemCategories = [];
    this.taxRates = [];
    this.globalLineItemTemplateFields = [];
    this.paymentWithLineItems = new Payment({
        items: this.lineItems,
        total: 0,
    });
    this.paymentCustomFields = [];
    this.paymentHeaderMsg = '';
    this.paymentHeaderTag = '';

    this.menuItems = [
        {
            id: 0,
            label: $translate.instant('PAYMENTS_VIEW_MODAL_TAB_DETAILS'),
            execute: () => Promise.resolve((this.activeTab = 0)),
        },
        {
            id: 1,
            label: $translate.instant('PAYMENTS_VIEW_MODAL_TAB_AUDIT_TRAIL'),
            execute: () => Promise.resolve((this.activeTab = 1)),
        },
    ];

    $scope.closeEvents = [];

    const handleCustomFields = () => {
        this.paymentCustomFields =
            customFieldTemplatesService.mapFieldOtherValue(
                angular.copy(this.payment.custom_fields),
            ).filter((item) => {
              // we want to filter out customFields that have empty string
              // value for numeric values as it renders empty element
              if (item.type === 'number' && typeof item.value === 'string') {
                return item.value !== '';
              }
              return true;
            });
    };

    const $onInit = () => {
        this.handlePaymentHeader();
        handleCustomFields();

        if (this.payment.id && this.payment.path) {
            delete this.payment.path;
        }

        this.userPermissionsService = userPermissionsService;

        const lineItemsPromises = [];
        lineItemsPromises.push(LineItemService.getCategories());
        lineItemsPromises.push(LineItemService.getTaxRates());
        lineItemsPromises.push(
            customFieldTemplatesService.getGlobalCustomFieldTemplates(),
        );
        if (this.payment && this.payment.id) {
            lineItemsPromises.push(
                LineItemService.getLineItems(this.payment.id),
            );
        }
        this.payment.items = this.lineItems;
        this.payment.total = this.payment.total_amount || 0;
        this.isLineItemsLoading = true;
        Promise.all(lineItemsPromises)
            .then(([categories, taxRates, templates, lineItems]) => {
                if (Array.isArray(templates)) {
                    const lineItemGlobalCustomFieldTemplate = templates.find(
                        (template) => template.global_template === 'WorkItem',
                    );
                    if (
                        lineItemGlobalCustomFieldTemplate &&
                        lineItemGlobalCustomFieldTemplate.template_fields
                    ) {
                        //get rid of for new view payment modal
                        this.globalLineItemTemplateFields = lineItemGlobalCustomFieldTemplate.template_fields.map(({ child: nested, ...templateField }) => 
                        ({ ...templateField, ...(nested && {nested: { ":-other-:": [nested] } } )}) );
                    }
                }
                if (Array.isArray(categories)) {
                    this.lineItemCategories = categories;
                }
                if (Array.isArray(taxRates)) {
                    this.taxRates = taxRates;
                }
                if (Array.isArray(lineItems)) {
                    let defaultCategory =
                        LineItemService.getDefaultCategory(categories);
                    this.lineItems = lineItems.map(
                        (item) => new LineItem(item),
                    );
                    if (lineItems.length < 1) {
                        this.lineItems = [
                            new LineItem({
                                display_name: this.payment.number,
                                category: defaultCategory && defaultCategory.id,
                                currency: this.payment.currency,
                                quantity: 1,
                                unit_price: this.payment.total_amount,
                                amount: this.payment.total_amount,
                                _status: LineItem.STATUS_CREATED,
                            }),
                        ];
                    }
                    this.paymentWithLineItems = new Payment({
                        ...this.payment,
                        items: this.lineItems,
                        total: this.payment.total_amount,
                    });
                }
            })
            .finally(() => (this.isLineItemsLoading = false));

        this.vendor = null;
        this.vendorDataLoading = true;
        const getVendorDataPromises = [];
        if ($rootScope.is_user) {
            getVendorDataPromises.push(
                vendorUtils.getVendorBySlug(this.payment.vendor.slug),
            );
            if (this.payment.worker && this.payment.worker.slug) {
                getVendorDataPromises.push(
                    vendorUtils.getVendorBySlug(this.payment.worker.slug),
                );
            }
        } else if (
            $rootScope.user &&
            $rootScope.user.vendor &&
            $rootScope.user.vendor.is_supplier
        ) {
            getVendorDataPromises.push(Promise.resolve($rootScope.user.vendor));
            if (this.payment.worker && this.payment.worker.slug) {
                getVendorDataPromises.push(
                    WorkerService.getWorkerData(this.payment.worker.slug),
                );
            }
        } else {
            getVendorDataPromises.push(Promise.resolve($rootScope.user.vendor));
        }
        Promise.all(getVendorDataPromises)
            .then(([vendor, worker]) => {
                this.vendor = { ...vendor };
                if (worker) {
                    this.vendor = { ...worker, parent: { ...vendor } };
                }
            })
            .finally(() => (this.vendorDataLoading = false));

        // Lazy load buyers
        if (
            $rootScope.tenantHasFeature('payments_assigned_buyer') &&
            this.payment.assigned_buyer
        ) {
            let promise;
            if ($rootScope.is_user) {
                promise = userService.getDataWithParams({
                    fields: [
                        'id',
                        'first_name',
                        'last_name',
                        'full_name',
                        'slug',
                        'email',
                        'profile_picture_path',
                        'avatar_color',
                    ],
                    has_permission: 'buyer.payment.set approved',
                });
            } else {
                promise = $http.get('/api/v/payments/payment_approvers/');
            }

            promise.then((res) => {
                return (this.assigned_buyer = _.find(res.data, (buyer) => {
                    return buyer.id === this.payment.assigned_buyer;
                }));
            });
        }

        this.timesheets = [];
        if ($rootScope.tenantHasFeature('tasks_time_sheet')) {
            Api.Timesheets.getTaskTimesheetPeriods({
                paymentIds: [this.payment.id],
            }).then(({ data: timesheets }) => {
                this.timesheets = timesheets.map((timesheet) => ({
                    date: Utils.Date.startDateToEndDate(
                        timesheet.date_start,
                        timesheet.date_end,
                    ),
                    status: $translate.instant(
                        `TIMESHEETS_TIMESHEET_WEEKLY_STATUS.${timesheet.status}`,
                    ),
                }));
                !$rootScope.$$phase && $scope.$apply();
            });
        }
    };

    $scope.$on('$stateChangeStart', (event, toState) => {
        const skipGoingToPayments = toState.name === 'app.partner-details.profile';
        $scope.closeEvents.push({ skipGoingToPayments });
        $modalInstance.close($scope.closeEvents);
    }
    );

    $scope.$on('reloadPaymentModal', (e, payment) => {
        this.assigned_buyer = null;
        this.payment = payment;
        $onInit();
        if (!$rootScope.$$phase) {
            return $scope.$apply();
        }
    });

    var modalClosing = $scope.$on(
        'modal.closing',
        function (e, reason, closed) {
            paymentsService.viewPaymentModalClose();

            e.preventDefault();
            modalClosing(reason);

            $('.payments-modal').removeClass('in');

            const skipHidingModal = reason?.some(item=> !!item?.skipGoingToPayments);
            if(skipHidingModal){
                return;
            }

            return $timeout(function (reason) {
                $('.payments-modal').hide();
                if (closed) {
                    return $modalInstance.close(
                        Array.isArray(reason) ? reason : $scope.closeEvents,
                    );
                } else {
                    return $modalInstance.dismiss(
                        Array.isArray(reason) ? reason : $scope.closeEvents,
                    );
                }
            }, 500);
        },
    ); // This needs to be the same as bootstrap animation time

    this.vendorProfilePage = (vendor) => {
        $modalInstance.close([]);
        $state.go('app.partner-details.profile', { slug: vendor.slug });
    };

    this.taskDetailsPage = function () {
        return tasksService.getToTaskUrl(
            (task != null ? task.id : undefined) ||
                this.payment.related_task.id,
            (task != null ? task.task_group : undefined) ||
                this.payment.related_task.task_group,
        );
    };

    this.getContractDetailsPageUrl = function () {
        return $state.href('app.contracts.single-contract', { id: this.payment.contract.id });
    };

    this.onInvoiceActionExecuted = async (actionResult) => {
        const actionOpenedNewWindow = !actionResult;
        const actionWasCanceled = actionResult && actionResult.result === 'canceled';
        if (actionOpenedNewWindow || actionWasCanceled) {
            return;
        }
        if (actionResult.action === 'delete') {
            $scope.closeEvents.push(['deleted', this.payment.id]);
            $modalInstance.close($scope.closeEvents);
        } else {
            const payment = actionResult.result;
            $rootScope.$broadcast('reloadPaymentModal', payment);

            if (actionResult.action === 'add_similar') {
                $scope.closeEvents.push(['added', payment.id]);
                $modalInstance.close($scope.closeEvents);
            } else {
                $scope.closeEvents.push(['updated', payment.id]);
            }
        }
    };

    this.handlePaymentHeader = () => {
        if (this.payment.status === 'paid') {
            this.paymentHeaderMsg = $translate.instant(
                'PAYMENTS_DETAILS_HEADER_PAID',
                { date: $filter('date')(this.payment.paid_date) },
            );
            this.paymentHeaderTag = 'alert-success';
        } else if (this.payment.status === 'rejected') {
            this.paymentHeaderMsg = $translate.instant(
                'PAYMENTS_DETAILS_HEADER_REJECTED',
            );
            this.paymentHeaderTag = 'alert-danger';
        } else if (this.payment.status === 'scheduled') {
            if (Array.isArray(this.payment.processing_problem_reasons) && this.payment.processing_problem_reasons.length > 0) {
                this.paymentHeaderMsg = this.payment.processing_problem_reasons.map((reason) => {
                    return $translate.instant(
                        'PAYMENT_MODAL_ISSUE_INFO.' +
                            reason,
                        {
                            partnerName: VendorHelpers.getVendorName(
                                this.payment.vendor,
                            ),
                        },
                    );
                }).join(' ');
                this.paymentHeaderTag = 'alert-danger';
            } else {
                this.paymentHeaderMsg = $translate.instant(
                    'PAYMENTS_DETAILS_HEADER_SCHEDULED',
                    {
                        dateTime: $filter('dateTime')(
                            this.payment.scheduled_for,
                        ),
                    },
                );
                this.paymentHeaderTag = 'alert-info';
            }
        } else if (this.payment.status === 'processing' && !!this.payment.mark_as_paid_on_date) {
            this.paymentHeaderMsg = $translate.instant(
                'PAYMENTS_DETAILS_HEADER_PROCESSING',
                { date: $filter('date')(this.payment.mark_as_paid_on_date) },
            );
            this.paymentHeaderTag = 'alert-info';
        } else if (this.payment.status === 'in_flight' && !!this.payment.mark_as_paid_on_date) {
            this.paymentHeaderMsg = $translate.instant(
                'PAYMENTS_DETAILS_HEADER_IN_FLIGHT',
                { date: $filter('date')(this.payment.mark_as_paid_on_date) },
            );
            this.paymentHeaderTag = 'alert-info';
        } else {
            this.paymentHeaderMsg = '';
            this.paymentHeaderTag = '';
        }
    };

    this.discard = () => $modalInstance.close();
    this.dismiss = () => $modalInstance.dismiss();

    $onInit();
};

controller.$inject = [
    '$scope',
    '$state',
    '$rootScope',
    '$modalInstance',
    '$timeout',
    '$translate',
    '$http',
    '$filter',
    'paymentsService',
    'tasksService',
    'payment',
    'task',
    'userPermissionsService',
    'userService',
    'vendorUtils',
    'WorkerService',
    'LineItemService',
    'customFieldTemplatesService',
];

angular.module('pipeline.payments').controller('viewPaymentModal', controller);
