import { Utils } from 'shortlist-react';

export default class LineItem {

    static STATUS_NEW = 'new'; // status set internally by LineItems react component
    static STATUS_CREATED = 'created';
    static STATUS_UPDATED = 'updated';
    static STATUS_DELETED = 'deleted';

    constructor(data) {
        data && Object.assign(this, data);
        this.normalize();
    }

    normalize() {
        this.id = this.id || null;
        this.display_name = this.display_name || '';
        this.currency = this.currency || '';
        this.amount = this.amount || 0.00;
        this.category = this.category || null;
        this.quantity = Utils.roundDecimal(this.quantity || 0.00, 5);
        this.unit_price = this.unit_price || 0.00;
        this.custom_fields = this.custom_fields || [];
        this.custom_field_values = this.custom_fields;
        this.custom_field_templates = this.custom_field_templates || [];
        this._status = this._status || null; // internal use only
        // related task - in the future
    }

    validate($translate) {
        const error = {};
        if (![LineItem.STATUS_CREATED, LineItem.STATUS_UPDATED, LineItem.STATUS_DELETED].includes(this._status)) {
            return error;
        }
        if (!this.display_name || (this.display_name.trim().length < 1)) {
            error.display_name = [$translate.instant('PAYMENTS_ADD_MODAL_LINE_ITEMS_NAME_ERROR')];
        }
        if (!this.category) {
            error.category = [$translate.instant('PAYMENTS_ADD_MODAL_LINE_ITEMS_CATEGORY_ERROR')];
        }
        if (!this.quantity || (this.quantity <= 0)) {
            error.quantity = [$translate.instant('PAYMENTS_ADD_MODAL_LINE_ITEMS_QUANTITY_ERROR')];
        }
        if (!this.unit_price) {
            error.unit_price = [$translate.instant('PAYMENTS_ADD_MODAL_LINE_ITEMS_PRICE_ERROR')];
        }
        if (!this.amount) {
            error.amount = [$translate.instant('PAYMENTS_ADD_MODAL_AMOUNT_ERROR')];
        }
        return error;
    }
}
