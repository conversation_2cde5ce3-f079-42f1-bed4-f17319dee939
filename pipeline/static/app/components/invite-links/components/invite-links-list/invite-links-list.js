'use strict';

angular.module("pipeline.invite-links").component('inviteLinksList', {
    templateUrl: '/components/invite-links/components/invite-links-list/invite-links-list.html',
    controller($state, $rootScope, $translate, $location, inviteLinksService) {
        'ngInject'

        this.$onInit = function() {
            return this.refreshTemplates();
        };

        this.refreshTemplates = () => {
            this.links = [];
            return inviteLinksService.getInviteLinksList().then(res => {
                return this.links = res;
            });
        };

        this.openCreateInviteLinkModal = () => {
            return inviteLinksService.openEditInviteLinkModal({}, () => {
                this.refreshTemplates();
                return $translate('INVITE_LINKS_LIST_ITEM_ADDED').then(value => $rootScope.alertUser(value, "alert-success"));
            });
        };

        this.openEditInviteLinkModal = link => {
            return inviteLinksService.openEditInviteLinkModal(link, () => {
                this.refreshTemplates();
                return $translate('INVITE_LINKS_LIST_ITEM_EDITED').then(value => $rootScope.alertUser(value, "alert-success"));
            });
        };

        this.disableLink = function(link) {
            link.enabled = false;
            return inviteLinksService.saveInviteLink({
                key: link.key,
                enabled: link.enabled
            }).then(() => $translate('INVITE_LINKS_LIST_ITEM_DISABLED_MESSAGE')).then(value => $rootScope.alertUser(value, "alert-success"));
        };

        this.enableLink = function(link) {
            link.enabled = true;
            return inviteLinksService.saveInviteLink({
                key: link.key,
                enabled: link.enabled
            }).then(() => $translate('INVITE_LINKS_LIST_ITEM_ENABLED_MESSAGE')).then(value => $rootScope.alertUser(value, "alert-success"));
        };

        this.openRemoveModal = function(link) {
            return inviteLinksService.openRemoveInviteLinkModal(link).then(() => {
                this.refreshTemplates();
                return $translate('INVITE_LINKS_DELETE_MODAL_SUCCESS');
        }).then(value => $rootScope.alertUser(value, "alert-success"));
        };

    }
}
);
