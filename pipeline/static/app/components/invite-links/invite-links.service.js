const inviteLinksService = function($rootScope, $http, $q, $translate, $modal, ConfirmModal, onboardingWorkflowService, onboardingService) {

    const api = '/api/invite_links/';

    const getPromise = function(promise) {
        const deferred = $q.defer();
        promise.then(response => deferred.resolve(response.data))
        .catch(response => deferred.reject(response));
        return deferred.promise;
    };

    this.getInviteLinkPreviewByKey = key => getPromise($http.get(`/api/public/invite_links/${key}/signup/`));

    this.saveNewUser = (key, data) => $http.post(`/api/public/invite_links/${key}/signup/`, data);

    this.getInviteLinksList = () => getPromise($http.get(api));

    this.saveInviteLink = function(link) {
        if (link.key) {
            return getPromise($http.patch(`${api}${link.key}/`, link));
        } else {
            return getPromise($http.post(`${api}`, link));
        }
    };

    this.deleteInviteLink = link => getPromise($http.delete(`${api}${link.key}/`));

    this.openRemoveInviteLinkModal = function(link) {
        return ConfirmModal.open({
            header: $translate.instant('INVITE_LINKS_DELETE_MODAL_HEADER'),
            content: $translate.instant('INVITE_LINKS_DELETE_MODAL_COPY'),
            action: $translate.instant('INVITE_LINKS_DELETE_MODAL_DELETE_CTA'),
            actionPromise: () => {
                return this.deleteInviteLink(link);
            },
            cancel: $translate.instant('INVITE_LINKS_DELETE_MODAL_CANCEL_CTA')
        });
    };

    this.openEditInviteLinkModal = function(link, onSave = null, isPreview = false) {
        if (!link) {
            link = {};
        }

        link = angular.copy(link);

        if (!link.groups) {
            link.groups = [];
        }

        if (!link.workflows) {
            link.workflows = [];
        }

        if (!link.relationship_managers) {
            link.relationship_managers = [];
        }

        const modalInstance = $modal.open({
            templateUrl: '/components/invite-links/modals/edit-invite-link-modal.html',
            controller: 'editInviteLinkModal as $ctrl',
            resolve: {
                onSave() {
                    return $q.when(onSave);
                },
                link,
                availableGroups() {
                    return $http.get('/api/vendor_groups/?fields=id,slug,name,icon,logo');
                },
                workflows() {
                    return onboardingWorkflowService.getTemplatesForTemplateSelectorAvailableForInvitingNewPartners(_.pluck(link.workflows, 'id'));
                },
                isPreview() {
                    return isPreview;
                }
            }
        });

        return modalInstance.result;
    };

};

inviteLinksService.$inject = [
    '$rootScope',
    '$http',
    '$q',
    '$translate',
    '$modal',
    'ConfirmModal',
    'onboardingWorkflowService',
    'onboardingService'
];

angular.module('pipeline.invite-links').service('inviteLinksService', inviteLinksService);
