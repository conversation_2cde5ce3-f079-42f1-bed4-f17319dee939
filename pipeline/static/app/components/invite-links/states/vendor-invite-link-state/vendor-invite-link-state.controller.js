export default class {
    constructor($http, $state, $window, inviteLinksService, userAuth, userPermissionsService, onboardingWorkflowService) {
        'ngInject';

        Object.assign(this, {
            $http,
            $state,
            $window,
            inviteLinksService,
            userAuth,
            userPermissionsService,
            onboardingWorkflowService,
        });
    }

    $onInit() {
        this.vendorData = {
            is_company: this.inviteLink.is_company,
            email: '',
            first_name: '',
            last_name: '',
            terms: false
        };
    }

    onVendorCodeSubmitForInviteLink(vendorData, code) {
        return this.$http.patch(`/api/public/invite_links/${this.inviteLink.key}/signup/${code}/`, {
            email: vendorData.email,
        }).then(() => {
            return {
                il: this.inviteLink.key,
                signup: 1,
            };
        });
    }

    addVendorFromInviteLink(vendorData) {
        return this.inviteLinksService.saveNewUser(this.inviteLink.key, vendorData).then((res) => {
            return [
                res,
                {...res.data, resolveUserHomeUrlParams: {il: this.inviteLink.key}}
            ];
        });
    };

    onGoToGoogleAuth() {
        this.$window.location = this.loginOptions.google_oauth_url + '&invitation=invite_link-' + this.inviteLink.key;
    };
};
