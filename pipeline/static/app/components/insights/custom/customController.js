import insightsApp from "../insights.app.js";

const customInsightsController = ($scope, $state, $stateParams, customInsightsService) => {
    $scope.loading = true;
    customInsightsService.getSisenseConfiguration().then((sisense) => {
        $scope.insightsEnabled = sisense.enabled;
        $scope.insightsDashboards = sisense.dashboards;

        if (sisense.enabled && sisense.dashboards.length) {
            if (!$stateParams.id) {
                const dashboardId = sisense.dashboards[0].oid;
                return $state.go('app.insights.custom', { id: dashboardId });
            }
            $scope.dashboard = sisense.dashboards.find((d) => d.oid === $stateParams.id)
        }
        if ($scope.dashboard) {
            $scope.dashboardUrl = `/api/tools/analytics/embed/${$stateParams.id}/`
        }

        $scope.loading = false;
    });
};

customInsightsController.$inject = ["$scope", "$state", "$stateParams", "customInsightsService"];

insightsApp.controller('customInsightsController', customInsightsController);
