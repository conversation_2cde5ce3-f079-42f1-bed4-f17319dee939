'use strict';

export class VeriffVerification {
    static STATUS_CREATED = 'created';
    static STATUS_STARTED = 'started';
    static STATUS_SUBMITTED = 'submitted';
    static STATUS_RESUBMISSION_REQUESTED = 'resubmission_requested';
    static STATUS_APPROVED = 'approved';
    static STATUS_DECLINED = 'declined';
    static STATUS_REVIEW = 'review';
    static STATUS_ABANDONED = 'abandoned';
    static STATUS_EXPIRED = 'expired';

}

export class VeriffService {
    static VERIFICATION_API = '/api/veriff/verification/';
    static INTEGRATION_API = '/api/veriff/integration/'

    constructor($http) {
        'ngInject';

        Object.assign(this, {
            $http,
        });
    }

    getUsableVerification(veriff_type) {
        const params = {
            veriff_type: veriff_type
        };
        return this.$http.get(VeriffService.VERIFICATION_API, { params });
    }

    createVerification(veriff_type) {
        return this.$http.post(VeriffService.VERIFICATION_API, {veriff_type: veriff_type});
    }

    isVerificationDoable(verification) {
        return [
            VeriffVerification.STATUS_CREATED,
            VeriffVerification.STATUS_STARTED,
            VeriffVerification.STATUS_RESUBMISSION_REQUESTED
        ].includes(verification.status);
    }

    isVerificationSubmitted(verification) {
        return [
            VeriffVerification.STATUS_SUBMITTED,
            VeriffVerification.STATUS_APPROVED,
            VeriffVerification.STATUS_DECLINED
        ].includes(verification.status);
    }
}
