import AbstractModalController from '../../../vendor-configuration/modals/vendor-custom-field-section-edit-modal/abstract-modal.controller';

export default class GroupMemberPreviewModalController extends AbstractModalController {
    constructor($scope, $state, $http, $rootScope, $translate, $modalInstance, $stateParams, _,
                diffService, vendorUtils, portfolioService, vendorData, gridConfig, userPermissionsService,
                tabToOpen, group, sharedListKey, onboardingService) {
        'ngInject';
        super();

        Object.assign(this, {
            $scope,
            $state,
            $http,
            $rootScope,
            $modalInstance,
            $translate,
            $stateParams,
            _,
            diffService,
            vendorUtils,
            portfolioService,
            vendorData,
            gridConfig,
            userPermissionsService,
            tabToOpen,
            group,
            sharedListKey,
            onboardingService,
        });

        this.activeTab = 0;
        this.informationData = [];
        this.listCustomFields = {};
        this.isSaving = false;
        this.portfolio = null;
        this.resumeFile = false;
        this.tabsVisibility = {
            portfolio: false,
            resume: false,
        };
        this.hideEmail = true;
        this.hidePhoneNumber = true;
        this.hideLocation = true;
        this.isLoading = true;

        // TODO: refactor this in the future when react component is ready
        this.socialLinkIcon = {
            linkedin: 'linkedin-icon',
            twitter: 'twitter-icon',
            youtube: 'youtube-icon',
            facebook: 'facebook-icon',
            vimeo: 'vimeo-icon',
            behance: 'behance-icon',
            dribbble: 'dribbble-icon',
            instagram: 'instagram-icon',
            pinterest: 'pinterest-icon',
            blog: 'blog-icon',
            website: 'website-icon',
            tiktok: 'tiktok-icon',
            imdb: 'imdb-icon',
        };

        $scope.$on('$stateChangeStart', () => {
            $modalInstance.dismiss();
        });

        this.goToPortfolio = () => {
            $state.go('app.partner-details.portfolio', { slug: vendorData.slug });
        };

        this.goToVendorDetails = (vendor) => {
            this.$state.go('app.partner-details.profile', {
                slug: vendor.slug,
            }, { reload: 'app.partner-details.profile' });
            this.$modalInstance.dismiss();
        }

        this.saveChanges = () => {
            this.isSaving = true;
            let apiUrl = `/api/list/${this.group.slug}/${this.vendorData.slug}/`;
            if (this.sharedListKey) {
                apiUrl = `/api/public/list/${this.sharedListKey}/${this.vendorData.slug}/`;
            }
            this.vendorData.list_custom_fields = this.listCustomFields;
            this.$http.patch(apiUrl, {
                    custom_fields: this.listCustomFields,
                })
                .finally(() => {
                    this.isSaving = false;
                    this.discard([
                        {
                            type: 'update',
                            item: vendorData,
                        },
                    ]);
                });
        };

        this.$onInit();

        this.listCustomFields = {};

        if (sharedListKey) {
            this.isLoading = false;
        } else {
            // Load list custom fields data
            this.$http.get(`/api/list/${this.group.slug}/`, {
                params: {
                    slugs: vendorData.slug,
                }
            }).then(({ data: [vendorData] }) => {
                this.vendorData.list_custom_fields = {};
                if (vendorData && Array.isArray(vendorData.custom_fields)) {
                    vendorData.custom_fields.forEach(({id, value}) => {
                        this.vendorData.list_custom_fields[id] = value;
                        this.listCustomFields[id] = value;
                    })
                }
                this.isLoading = false;
                this.initialFormVersion = this._getFormSnapshot(true);
            });
        }

        if (this.tabToOpen) {
            if (this.tabToOpen === 'portfolio') {
                this.activeTab = 1;
            } else if (this.tabToOpen === 'resume') {
                this.activeTab = 2;
            }
        }

        this.gridConfig && this.gridConfig.columnConfig
            .filter((col) => col.config.isVisible && col.config.coreVariableName)
            .forEach((col) => {
                if (col.config.coreVariableName === 'email') {
                    this.hideEmail = false;
                } else if (col.config.coreVariableName === 'phone_number') {
                    this.hidePhoneNumber = false;
                } else if (col.config.coreVariableName === 'location') {
                    this.hideLocation = false;
                }

                if (col.config.coreVariableName === 'portfolio') {
                    this.tabsVisibility.portfolio = true;
                } else if (col.config.coreVariableName === 'resume') {
                    this.tabsVisibility.resume = true;
                } else {
                    this.informationData.push(col.config);
                }
            });

        if (
            this.userPermissionsService.hasAccessToDocuments()
            &&
            this.$rootScope.tenantHasFeature(`vendor_portfolios/${this.vendorData.vendor_type}`)
        ) {
            this.vendorUtils.getVendorsResume([this.vendorData.slug], 'pdf')
                .then((resume) => {
                    if (resume.length) {
                        this.resumeFile = resume[0];
                        this.tabsVisibility.resume = true;
                    } else {
                        this.tabsVisibility.resume = false;
                    }
                });

            this.portfolioService.getProjectsForVendor({
                vendor_slug: this.vendorData.slug
            }).then((res) => {
                this.portfolio = res;
                this.tabsVisibility.portfolio = Array.isArray(res) && res.length > 0;
            });
        } else {
            this.getPublicPortfolioResume(this.vendorData.slug, this.$stateParams.key);
        }
    }

    getFormSnapshot(init) {
        return (init) ? this.vendorData.list_custom_fields : this.listCustomFields;
    }

    getPublicPortfolioResume(slug = '', key = '') {
        this.$http.get(`/api/public/vendors/additional_data/${key}/${slug}/`).then(({ data: { resume = [], portfolio = [] } }) => {
            const resumeFiles = resume.map((file) => {
                let downloadUrl = '';
                if (this.sharedListKey) {
                    downloadUrl = `/api/public/requested_documents/${this.sharedListKey}/${file.id}/download/?inline=true`;
                } else {
                    downloadUrl = this.onboardingService.downloadRequestedDocumentUrl(file, true, null, this.vendorData.slug);
                }
                return {
                    ...file,
                    downloadUrl,
                }
            });
            this.resumeFile = resumeFiles[0] ? resumeFiles[0] : null;
            this.portfolio = portfolio;
            this.tabsVisibility.resume = Array.isArray(resumeFiles) && resumeFiles.length > 0;
            this.tabsVisibility.portfolio = Array.isArray(portfolio) && portfolio.length > 0;
        });
    }

    getFieldValue(variable, item) {
        if (variable.indexOf('listfield_') === 0) {
            return item.getTextCellData(this.vendorData);
        }

        let value = this.vendorData[variable] ? this.vendorData[variable] : '';
        if (Array.isArray(value)) {
            value = value.join(', ');
        } else if (variable === 'links.website') {
            value = this.vendorData.links.website ? this.generateSocialLink(this.vendorData.links.website, this.socialLinkIcon.website) : '';
        } else if (variable.indexOf('links.') === 0) {
            const links = [];
            Object.keys(this.vendorData.links).forEach((key) => {
                if (key !== 'website' && this.vendorData.links[key]) {
                    links.push(this.generateSocialLink(this.vendorData.links[key], this.socialLinkIcon[key]));
                }
            });
            value = links.length > 0 ? links.join(('<br />')) : '';
        } else if (variable === 'location') {
            value = value.query;
        }
        return value || '--';
    }

    generateSocialLink(url = '', icon = '') {
        return `<i class="icon-${icon}"></i> <a href="${url}" target="_blank" rel="noopener">${url}</a>`;
    }

    setFieldValueFunc(variable) {
        return (value) => {
            if (variable.indexOf('listfield') === 0) {
                const fieldId = parseInt(variable.split('_')[1], 10);
                this.listCustomFields[fieldId] = value;
            }
        };
    }

    goToInformationTab() {
        this.activeTab = 0;
    }

    goToPortfolioTab() {
        this.activeTab = 1;
    }

    goToResumeTab() {
        this.activeTab = 2;
    }

    close() {
        this.$modalInstance.dismiss();
    }
}
