.group-edit {

    &--max-width {
        max-width: 700px;
    }

    &__spacer {
        border-bottom: 1px solid $shortlist-grey-3;
        padding-bottom: 35px;
        margin-bottom: 35px;
    }

    &__label {
        font-weight: 600;
        font-size: 14px;
        margin-bottom: 5px;
        max-width: 460px;
        position: relative;

        &-mandatory {
            position: absolute;
            right: 35px;
            font-weight: normal;
            color: $shortlist-grey-2;
        }
    }

    &__cover-photo {
        margin-top: 30px;
        margin-bottom: 40px;
    }

    &__website {

        .input-text-widget {
            margin-bottom: 0;
        }
    }

    &__logo {

        &.has-error {

            .group-logo-upload__logo-not-uploaded {
                border: 2px solid $color-input-error;
                background-color: $pink-3;
            }
        }
    }

    &__section:not(:last-child) {
        margin-bottom: 40px;
    }

    &__location {
        margin-bottom: 15px;
    }

    &__select, &__remove-select {
        float: left;
    }

    &__remove-select {
        margin-left: 10px;
        margin-top: 10px;
    }

    .block-tile {
        padding: 40px 50px;
    }

    .vendor-list-item {

        .toolbar, .vendor-availability__wrapper, .vendor-status-icon {
            display: none;
        }
    }
}
