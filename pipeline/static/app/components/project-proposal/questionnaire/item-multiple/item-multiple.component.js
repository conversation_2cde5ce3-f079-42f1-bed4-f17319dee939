const projectProposalQuestionnaireItemMultiple = () =>
    ({
        scope: {
            readOnly: '=',
            disabled: '=',
            model: '=',
            item: '=',
            errors: '=',
            vendorData: '=?',
            vendorStageId: '=?',
            onRequestedDocumentChange: '&?',
        },
        templateUrl: '/components/project-proposal/questionnaire/item-multiple/item-multiple.template.html',
        controller($scope, _, QuestionnaireService) {
            'ngInject';

            let lastChecked = null;
            if (($scope.model != null ? $scope.model.answer : undefined) != null) {
                $scope.model.answer = _.filter($scope.model.answer, item => item !== '' && item !== null && item !== undefined);
            }

            $scope.responseMultipleFieldHasAnswer = () => QuestionnaireService.responseMultipleFieldHasAnswer($scope.model);

            $scope.showFreeform = function() {
                if (($scope.model != null ? $scope.model.answer : undefined) != null) {
                    return ($scope.model != null ? $scope.model.answer : undefined) && ( (($scope.model != null ? $scope.model.answer : undefined) === 'Other...') || Array.from($scope.model != null ? $scope.model.answer : undefined).includes('Other...') );
                }
            };

            $scope.onDocumentChange = () => {
                this.onRequestedDocumentChange();
            };

            return $scope.uncheck = function(event) {
                if (!$scope.item.mandatory) {
                    if (lastChecked === event.target.value) {
                        delete $scope.model.answer;
                        return lastChecked = null;
                    } else {
                        return lastChecked = event.target.value;
                    }
                }
            };
        }
    })
;

angular.module('pipeline.project-proposal')
    .directive('projectProposalQuestionnaireItemMultiple', projectProposalQuestionnaireItemMultiple);
