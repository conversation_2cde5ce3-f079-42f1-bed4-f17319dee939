<div id="project-proposal-nav">
    <nav class="section-nav animated">
        <ul class="nav nav-tabs">

            <li ui-sref-active="active" ng-if="$ctrl.projectType == 'private'">
                <a ui-sref="app.project-proposal.project({ slug: $ctrl.project.data.slug })">
                    <span>{{ 'PROJECT_SUBMENU_PROJECT' | translate }}</span>
                </a>
            </li>

            <li ui-sref-active="active" ng-if="$ctrl.projectType == 'public'">
                <a ui-sref="app.public-project-proposal.project({ id: $ctrl.project.data.id })">
                    <span>{{ 'PROJECT_SUBMENU_PROJECT' | translate }}</span>
                </a>
            </li>

            <li ui-sref-active="active"
                ng-if="!$root.tenantHasFeature('hide_public_message_board') && $ctrl.previewMode == false && $ctrl.projectType == 'private' && !$ctrl.project.data.archived">

                <a ui-sref="app.project-proposal.messages({ slug: $ctrl.project.data.slug })">
                    <span>{{ 'PROJECT_SUBMENU_MESSAGES' | translate }}</span>
                    <span class="label-counter"
                          ng-class="{'unread': $ctrl.project.data.has_unread_messages}">
                        {{ $ctrl.project.data.question_count }}
                    </span>
                </a>

            </li>

            <li ui-sref-active="active"
                ng-if="$ctrl.previewMode == false && $ctrl.projectType == 'private' && !$ctrl.project.data.archived">

                <a ui-sref="app.project-proposal.direct-message({ slug: $ctrl.project.data.slug })">
                    <span>{{ 'PROJECT_SUBMENU_DIRECT_MESSAGE' | translate }}</span>
                    <i class="icon-lock-icon"></i>
                </a>

            </li>

            <li ui-sref-active="active" ng-if="$ctrl.previewMode == false && $ctrl.projectType == 'private'">
                <a ui-sref="app.project-proposal.updates({ slug: $ctrl.project.data.slug })">
                    <span>{{ 'PROJECT_SUBMENU_UPDATES' | translate }}</span>
                </a>
            </li>

        </ul>
    </nav>
</div>
