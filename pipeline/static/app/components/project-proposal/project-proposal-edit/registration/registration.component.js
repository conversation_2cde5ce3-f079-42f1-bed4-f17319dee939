'use strict';

const projectProposalEditRegistration = {
    bindings: {
        vendorData: '<',
        recaptcha: '<',
        errors: '<',
        publicProject: '<?'
    },
    transclude: true,
    templateUrl: '/components/project-proposal/project-proposal-edit/registration/registration.template.html',
    controller($translate, termsAndPrivacyService) {
        'ngInject'

        this.$onInit = function() {
            this.vendorData.is_company = this.publicProject.vendor_type === 'business';
            this.customTermsAndConditions = $translate.instant('VENDOR_REGISTRATION_FORM_TERMS_AND_CONDITIONS_CUSTOM_CHECKBOX');

            if (this.customTermsAndConditions.indexOf('terms-and-conditions-contractors-custom-checkbox') >= 0) {
                this.customTermsAndConditions = false;
            }

            termsAndPrivacyService.getVendorTermsAndConditions().then((termsAndConditions) => {
                this.termsAndConditions = termsAndConditions;
            });
        };

    }
};

angular.module('pipeline.project-proposal')
    .component('projectProposalEditRegistration', projectProposalEditRegistration);
