'use strict';

const projectProposalEditResponseRfp = {
    bindings: {
        project: '<',
        projectType: '@',
        user: '<',
        previewMode: '<',
        key: '<'
    },
    templateUrl: '/components/project-proposal/project-proposal-edit/response/response-rfp.template.html',
    controller: 'projectProposalEditResponseController'
};

angular.module('pipeline.project-proposal').component('projectProposalEditResponseRfp', projectProposalEditResponseRfp);
