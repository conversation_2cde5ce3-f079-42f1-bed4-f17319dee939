'use strict';

const projectProposalEdit = {
    bindings: {
        project: '<',
        projectType: '@',
        user: '<',
        responseForm: '<',
        responseFormData: '<',
        previewMode: '<',
        recaptcha: '<',
        key: '<',
        hideRejectButton: '<?',
    },
    templateUrl: '/components/project-proposal/project-proposal-edit/project-proposal-edit.template.html',
    controller: 'projectProposalEditController'
};

angular.module('pipeline.project-proposal').component('projectProposalEdit', projectProposalEdit);
