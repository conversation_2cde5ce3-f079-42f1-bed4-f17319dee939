<div class="text-center">
    <!-- Link valid, display the form -->
    <form-card data-title="'RESET_PASSWORD_HEADER' | translate"
               ng-if="!$ctrl.success && $ctrl.showForm">
        <form ng-submit="$ctrl.setPassword()">
            <div class="form-group vendor-talent-pool-no-user__form__password1">
                <input type="password"
                       ng-model="$ctrl.password1"
                       ng-attr-placeholder="{{ 'RESET_PASSWORD_PASSWORD1_PLACEHOLDER' | translate }}"
                       class="form-control">
                <error-list errors="$ctrl.errorsFormatted.password1"></error-list>
            </div>
            <div class="form-group vendor-talent-pool-no-user__form__password2">
                <input type="password"
                       ng-model="$ctrl.password2"
                       ng-attr-placeholder="{{ 'RESET_PASSWORD_PASSWORD2_PLACEHOLDER' | translate }}"
                       class="form-control">
                <r-error-list class="error-list" errors="$ctrl.errorsFormatted.password2"></r-error-list>
            </div>
            <div class="form-group">
                <button class="btn-default full-width" type="submit" ng-disabled="$ctrl.processing">
                    <span class="btn-inner-text">
                        {{ 'RESET_PASSWORD_SUBMIT' | translate }}
                    </span>
                </button>
            </div>
        </form>
    </form-card>
    <!-- Link invalid, display the message -->
    <form-card data-title="'RESET_PASSWORD_LINK_EXPIRED_HEADER' | translate"
               ng-if="!$ctrl.success && !$ctrl.showForm">
        <div class="confirmation-message">
            <span>{{ 'RESET_PASSWORD_LINK_EXPIRED' | translate }}</span>
            <login-sign-in-button></login-sign-in-button>
        </div>
    </form-card>
    <!-- Password change success -->
    <form-card data-title="'RESET_PASSWORD_SUCCESS_HEADER' | translate"
               ng-if="$ctrl.success && $ctrl.showForm">
        <div class="confirmation-message">
            <span translate="RESET_PASSWORD_SUCCESS" translate-compile></span>
            <login-sign-in-button></login-sign-in-button>
        </div>
    </form-card>
    <r-toast-message />
</div>
