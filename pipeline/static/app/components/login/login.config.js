import { Worksuite } from 'shortlist-react';

function loginConfig($stateProvider) {
    'ngInject'

    let access = Worksuite.routingConfig.accessLevels;

    $stateProvider

    .state('login', {
        abstract: true,
        templateUrl: '/components/login/login.template.html',
        controller($scope, $rootScope, $translate) {
            'ngInject'
            $scope.tenantName = $rootScope.tenant.name || $translate.instant("PAGE_TITLE");
        }
    })

    .state('login.form', {
        url: '/login/?email&error&google_auth_error&sso_auth_error&logged_out&redirect&next',
        access: access.public,
        template: '<r-login-form-state />',
    })

    .state('login.assure', {
        url: '/assure/?redirect,next,transaction_ref',
        access: access.public,
        resolve: {
            $title: ($translate) => $translate('TITLE_LOGIN'),
        },
        backgroundColorFromTheme: true,
        params: {
            transaction_ref: null,
            rememberme: false,
        },
        template: `
            <paycasso-login params="$resolve.params"></paycasso-login>
        `,
    })

    .state('login.password-reset', {
        url: '/password_reset/?confirmation',
        access: access.public,
        backgroundColorFromTheme: true,
        template: `
            <password-reset-form></password-reset-form>
        `
    })

    .state('login.password-set', {
        url: '/password_reset/:uid/?token',
        access: access.public,
        backgroundColorFromTheme: true,
        template: `
            <password-set-form show-form="$resolve.showForm"></password-set-form>
        `,
        resolve: {
            $title: ($translate) => { return $translate('TITLE_SET_PASSWORD') },
            showForm: ($http, $stateParams, $q) => {
                let deferred = $q.defer();
                $http.post('/api/users/validate_reset_link/', {
                    uid: $stateParams.uid,
                    token: $stateParams.token
                }).then(() => {
                    return deferred.resolve(true);
                }, () => {
                    return deferred.resolve(false);
                });
                return deferred.promise;
            }
        }
    })

    .state('login.secure-login-form', {
        url: '/secure-login/?confirmation',
        access: access.public,
        backgroundColorFromTheme: true,
        template: `
            <secure-login-form></secure-login-form>
        `
    })

    .state('login.two-factor-auth-otp-setup', {
        url: '/two-factor-auth/otp-setup/',
        access: access.user,
        template: '<r-two-factor-auth-setup-state />',
    })

    .state('login.two-factor-backup-codes', {
        url: '/two-factor-auth/backup-codes/',
        access: access.user,
        template: '<r-two-factor-auth-backup-codes-state />',
    })

    .state('login.two-factor-auth-otp-verification', {
        url: '/two-factor-auth/otp-verification/?next,redirect',
        access: access.user,
        template: '<r-two-factor-auth-verification-state />',
    })

    .state('logout', {
        url: '/logout/',
        controller: 'logoutController as $ctrl'
    });
}

export default loginConfig
