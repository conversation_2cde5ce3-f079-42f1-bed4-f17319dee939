#project-sidebar-container {
    float: left;
    width: 100%;

    .text-red {
        color: $pink-1;
    }

    .description {
        word-break: break-word;

        &.show-less {
            overflow: hidden;
            max-height: 310px;
        }

        ul, ol {
            margin-bottom: 20px;
            padding-left: 20px;

            li {
                margin-bottom: 10px;
            }
        }
    }

    .project-back-arrow {
        position: absolute;
        left: 18px;
        top: 30px;
    }

    .project-skills-required {

        .definition {
            display: none;
        }
    }

    .project-name {
        h3 {
            max-width: 80%;
            text-overflow: ellipsis;
            overflow-x: hidden;
            white-space: nowrap;
        }

        .project-type {
            font-size: 16px;
            color: $shortlist-grey-2;
        }

        .status {
            margin-top: 7px;
            margin-left: 10px;
            vertical-align: middle;
        }

        .project-name-edit {
            position: relative;

            .dropdown {
                position: absolute;
                top: 0;
                right: 0;
            }
        }
    }

    .section-nav {
        margin-top: 50px;
        margin-bottom: 35px;
    }

    .mt0 {
        margin-top: 0px !important;
    }

    .row-centered {
        text-align: center;
    }

    .profile-col {
        @include make-md-column(12);
        padding-left: 60px;
        text-align: left;
        margin: auto;
        float: right !important;
    }
    .sidebar {
        .avatar-wrapper {
            margin: 2px;
            span.avatar > span {
                font-weight: 600;
                font-size: 15px;
                color: $white;
            }
        }
    }

    .uploadFiles {
        color: $shortlist-blue-1;
        display: table-row;

        & > div:first-child {
            padding: 20px;
            border: 2px dashed $shortlist-grey-3;
            display: table-cell;
            vertical-align: middle;

            .desc {
                color: $shortlist-grey-1;
                font-size: 15px;
                font-weight: 500;
            }
            .glyphicon {
                font-size: 40px;
                margin-right: 10px;
            }
        }
        & > div:last-child {
            min-width: 155px;
            white-space: nowrap;
            cursor: pointer;
            padding: 10px 20px;
            border: 2px dashed $shortlist-grey-3;
            border-left: 0px;
            font-weight: bold;
            display: table-cell;
            vertical-align: middle;

            span {
                padding-top: 2px;
            }
        }

    }

    .file-widget {
        margin-top: 5px;

        .uploaded-file {
            margin: 3px 0px;
            padding: 10px 6px;
            border: 1px solid $shortlist-grey-3;

            .link {
                font-size: 14px;
            }
            .icon-file-close {
                float: right;
                margin-right: 10px;
                margin-top: 7px;
            }
        }
        .uploaded-file-wrapper {
            padding-top: 0px;
            margin-top: 3px;
            width: inherit;
        }
        .btn-upload-wrapper {
            display: none;
        }
    }
    .add-user .material-icons {
        font-size: 53px;
        -ms-transform: rotate(45deg);
        -webkit-transform: rotate(45deg);
        transform: rotate(45deg);
    }

    label {
        text-transform: uppercase;
    }

    .border-bottom {
        border-bottom: 1px solid $shortlist-grey-3;
    }

    .section {
        box-sizing: border-box;
        padding: 40px 0px 40px 100px;
        border-bottom: 2px solid $shortlist-blue-4;

        h2 {
            color: $shortlist-grey-2;
            margin-bottom: 20px;
        }

        h4 {
            font-weight: bold;
            font-size: 12px;
            text-transform: uppercase;
            margin: 20px 0px 10px;
        }

        .counter {
            background: $shortlist-blue-1;
            position: absolute;
            left: 48px;
            font-size: 16px;
            font-weight: bold;
            width: 32px;
            height: 32px;
            line-height: 32px;
            text-align: center;
            border-radius: 16px;
            color: $white;
        }

        .job-sub-category {
            margin-top: 15px;
        }

        .sub-section {
            padding: 15px 0px;

            & > div {
                strong {
                    display: block;
                    font-weight: 500;
                }
            }
        }
        .pick-date {
            color: $shortlist-blue-1;
            font-weight: bold;
            cursor: pointer;

            .material-icons {
                vertical-align: middle;
            }
        }
    }

    .project-acceptance-deadline, .project-participation-deadline {

        &.has-error {
            color: $color-input-error;

            .pick-date {
                color: $color-input-error;
            }
        }
    }

    .category-tags {
        position: relative;

        &.has-error {

            .tag-list {
                border-color: $color-input-error;
                background: $pink-3;
            }
        }

        &.disabled {

            .tag-list {
                cursor: not-allowed;
                background: $shortlist-blue-4;
            }
        }
    }

    .switcher-line {
        width: 35px;

        &:before {
            background: $shortlist-blue-3;
            height: 10px;
        }
    }

    .switcher.active {
        .switcher-line:before {
            background: $shortlist-blue-3;
        }
        .switcher-line:after {
            background: $shortlist-blue-1;
            border: none;
        }
    }

    .has-error {
        .costItem {
            border: 1px solid $color-input-error;
            background-color: $pink-3;
        }
    }

    .costItem {
        background: $white;
        margin-bottom: 2px;
        padding: 10px 15px 10px 10px;

        .title {
            font-size: 14px;
            line-height: 42px;

            &.edit {
                cursor: pointer;
            }
        }
        .action {
            line-height: 42px;
            position: relative;
            span.glyphicon-remove {
                font-size: 20px;
                position: relative;
                top: 5px;
                color: rgba(80, 89, 128, 0.5);
            }
        }
    }
    .addItem {
        display: block;
        border: 1px dashed grey;
        text-align: center;
        padding: 15px 0px;
        color: $shortlist-blue-1;
        font-weight: bold;
    }
    .tools {
        .btn-default, .btn-second {
            color: $shortlist-blue-1;
            font-size: 14px;
            font-weight: 600;
        }
        .btn {
            font-size: 13px;
            color: $white;
            padding: 15px 30px;
            text-transform: uppercase;
            font-weight: bold;
        }
    }

    .project-budget-rate-container {

        .project-budget-currency {
            width: 150px !important;
        }

        .pull-left {

            &:not(.rate-suffix) {
                margin-right: 20px;
                width: 230px;
            }

            &.rate-suffix {
                padding-top: 47px;
            }
        }
    }

    .project-budget-container {

        .project-budget-currency {
            width: 150px !important;
        }

        .pull-left {
            margin-right: 20px;
            width: 230px;
        }
    }

    .triArrow > div:before {
        content: ' ';
        display: block;
        border-color: $shortlist-blue-4 transparent;
        border-width: 0px 15px 15px 15px;
        border-style: solid;
        width: 0px;
        margin: auto;
    }
    .noShadow {
        box-shadow: none !important;
    }

    select {
        max-width: 400px;

        &.questionTemplate {
            display: inline-block;
            width: 280px;
        }
    }
    .width400 {
        width: 400px;
    }
    .width200 {
        width: 200px;
    }
    .width100 {
        width: 100px;
    }
    .inline {
        display: inline-block !important;
    }

    .optional {
        font-style: italic;
        color: #999;
    }

    input[type="text"] {
        min-height: 40px;
    }

    textarea.form-control:not(.vendor-notes__textarea) {
        height: 150px;
    }

    .questionaire {
        background: $shortlist-blue-4;
        padding: 10px;
        border-top: 1px solid $shortlist-grey-3;

        .question {
            background: $white;
            display: table;
            width: 100%;
            padding: 10px;

            .icon {
                width: 40px;
            }

            .glyphicon-list-alt {
                font-size: 27px;
                margin: 0px 10px;
                color: $shortlist-blue-1;
            }

            & > div {
                display: table-cell;
                vertical-align: middle;
            }
            & > a {
                color: rgba(80, 89, 128, 0.5);
                vertical-align: middle;
                display: table-cell;
                font-size: 20px;
                text-align: center;

                &:hover {
                    color: $shortlist-blue-1;;
                }
            }
        }
    }
    .textEdit {
        border: $input-common-border;
        border-radius: 4px;
        padding: 6px 12px;
        min-height: 40px;

        &:hover, &:active {
            outline: none;
            border-color: $shortlist-blue-1;
        }

        .var {
            background: $yellow-3;
        }
        &.has-error {
            border-color: $color-input-error !important;
        }
    }

    .form-control[disabled] {
        background: $shortlist-blue-4;
    }

    /* Custom Radio */
    .customRadio {
        display: inline-block;

        input[type=radio] {
            border: 0;
            clip: rect(0 0 0 0);
            height: 1px;
            margin: -1px;
            overflow: hidden;
            padding: 0;
            position: absolute;
            width: 1px;
        }
        label {
            display: inline-block;
            cursor: pointer;
            text-transform: inherit;
            white-space: nowrap;

            &:before {
                content: '';
                display: inline-block;
                width: 1em;
                height: 1em;
                vertical-align: -0.15em;
                border-radius: 1em;
                border: 0.22em solid #fff;
                box-shadow: 0 0 0 0.15em $shortlist-blue-2;
                margin-right: 0.75em;
                transition: 0.5s ease all;
            }
        }

        input[type=radio]:checked + label:before {
            background: $shortlist-blue-1;
        }

    }

    .action-plus {
        position: relative;
        display: inline-block;
        margin-left: 20px;

        &:before {
            content: '+';
            display: inline-block;
            position: relative;
            margin-right: 5px;
            font-size: 22px;
            font-weight: 600;
            top: 3px;
            line-height: 12px;
        }
    }

    .switched-content {
        margin-top: 30px;
    }

    .awarded-project-feedback {

        .tag-list {
            i.material-icons {
                vertical-align: super !important;
            }
        }

        .add-your-feedback-trigger {
            font-weight: bold;
            padding-top: 10px;
        }

        .section-heading {
            margin-top: 50px;
            padding-bottom: 15px;
            height: 55px;
        }

        .partner-feedback-form {
            .tag-list {
                background: $white;

                .tags {
                    padding: 0px;
                }
            }
        }
    }

    tags {
        .tag-list {
            position: relative;
            border: $input-common-border;
            border-radius: 4px;
            padding: 5px !important;
            max-width: 400px;
            z-index: 100;

            &:hover {
                border-color: $shortlist-blue-1;
            }

            ul.tags-suggestion-list {
                width: 100%;
                margin: 0px 2px;
                font-weight: 600;
                color: $shortlist-blue-1;

                li.tags-suggestion {
                    padding: 10px 20px;

                    &.tags-suggestion-highlighted {
                        background: $shortlist-blue-4;
                        color: $shortlist-blue-1;
                    }
                }
            }

            .tags-input {
                min-height: 30px !important;
                border: none;
                padding: 0px 0px 0px 3px !important;
                min-width: 160px !important;
            }

            .tags {
                position: relative;

                .tag {
                    background: $shortlist-blue-1;
                    padding: 0px 22px 0 10px;
                    margin-bottom: 2px;
                    height: 30px;

                    span {
                        max-width: 140px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    span, a {
                        line-height: 32px;
                        height: 30px;
                        font-weight: 600;
                        display: inline-block;
                        font-size: 11px;
                    }

                    a {
                        position: absolute;
                        opacity: 1 !important;
                        margin-left: 0px;
                        margin-top: -1px;

                        i {
                            padding-left: 4px;
                            vertical-align: middle;
                            color: $white;
                            font-size: 14px;

                            &.glyphicon:before {
                                content: "\e014";
                            }
                        }
                    }
                    span {
                        border-right: 1px solid #6573c3;
                        padding-right: 12px;
                    }
                }
            }
        }
    }

    @include media-query-lg {
        .profile-col {
            @include make-md-column(12, 80px);
        }
    }

    @include media-query-md {
        .profile-col {
            @include make-md-column(12, 80px);
        }
    }
}
