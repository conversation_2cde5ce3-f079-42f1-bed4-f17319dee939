<section id="project-sidebar-container" class="sidebar-container">

    <div class="content">
        <div class="container container-flex">
            <div class="row row-centered">
                <div class="profile-col">
                    <div class="project-back-arrow">
                        <a ui-sref="app.projects">
                            <i class="icon-back-icon"></i>
                        </a>
                    </div>
                    <ng-include src="'/components/projects/templates/project-name.html'"></ng-include>
                    <div class="r-style-margin-top--40px r-style-margin-bottom--30px">
                        <r-horizontal-navigation items="menuItems" active-item="$state.current.name" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row row-centered">
            <div class="profile-col">
                <project-discussions></project-discussions>
            </div>
        </div>
    </div>

    <aside class="sidebar">
        <ng-include src="'/components/projects/shared/side-bar.html'"></ng-include>
    </aside>

</section>
