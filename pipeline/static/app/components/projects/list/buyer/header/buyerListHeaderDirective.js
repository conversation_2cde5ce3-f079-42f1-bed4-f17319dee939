import projectsApp from "../../../projects.app";

projectsApp.directive('projectsBuyerListHeader', () =>
    ({
        restrict: 'E',
        templateUrl: '/components/projects/list/buyer/header/buyerListHeaderView.html',
        controller($scope, $state, $translate) {
            $scope.$state = $state;
            $scope.$translate = $translate;

            $scope.menuItems = [
                {
                    id: 'app.projects',
                    label: $scope.$translate.instant('PROJECT_LIST_TABS_PROJECTS'),
                    execute: () => Promise.resolve($scope.$state.go('app.projects')),
                },
                {
                    id: 'app.campaigns.list',
                    label: $scope.$translate.instant('PROJECT_LIST_TABS_CAMPAIGNS'),
                    execute: () => Promise.resolve($scope.$state.go('app.campaigns.list')),
                }
            ];
        }
    })
);
