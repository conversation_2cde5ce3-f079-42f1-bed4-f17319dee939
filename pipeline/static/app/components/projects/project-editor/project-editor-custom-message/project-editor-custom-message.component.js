const projectEditCustomMessage = {
    bindings: {
        emailVariables: '<',
        customEmail: '<'
    },
    templateUrl: '/components/projects/project-editor/project-editor-custom-message/project-editor-custom-message.template.html',
    controller() {
        'ngInject'

        this.mentions = [{
            trigger: "#",
            displayTransform: (id) => `#${id}`,
            outputMask: (id) => `{${id}}`,
            data: this.emailVariables.map((i) => ({
                id: i.tag,
                display: i.label,
            }))
        }];
        this.onCustomEmailSubjectChange = (value) => this.customEmail.subject = value
        this.onCustomEmailMessageChange = (value) => this.customEmail.message = value
    }
}

export default projectEditCustomMessage
