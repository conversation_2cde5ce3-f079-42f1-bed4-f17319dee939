<section id="project-preview-overlay" ng-show="$ctrl.showProjectPreview">
    <preview-overlay close-trigger="$ctrl.closeProjectPreview()"></preview-overlay>

    <div class="project-preview-overlay-wrapper">
        <project-proposal project="$ctrl.projectPreviewData"
                          project-type="$ctrl.project.sharing == 'public' || $ctrl.project.sharing == 'with_key' ? 'public': 'private'"
                          user="$ctrl.$rootScope.user"
                          response-form="$ctrl.project.responseForm"
                          preview-mode="true">
            <project-proposal-project project="$ctrl.projectPreviewData"
                                      project-type="$ctrl.project.sharing == 'public' || $ctrl.project.sharing == 'with_key' ? 'public': 'private'"
                                      user="$ctrl.$rootScope.user">
                <project-proposal-edit project="$ctrl.projectPreviewData"
                                       project-type="$ctrl.project.sharing == 'public' || $ctrl.project.sharing == 'with_key' ? 'public': 'private'"
                                       user="$ctrl.$rootScope.user"
                                       hide-reject-button="true"
                                       response-form="$ctrl.project.responseForm"
                                       preview-mode="true">>
                </project-proposal-edit>
            </project-proposal-project>
        </project-proposal>
    </div>
</section>

<div class="project-editor">
    <div class="container">
        <div class="row">
            <div class="col-md-10 col-md-offset-1">

                <div class="block-tile">

                    <div class="editor-block">
                        <!-- Project name -->
                        <project-name name="$ctrl.initialProjectName"
                                      project-type="$ctrl.project.project_type"
                                      project="$ctrl.project"
                                      status="$ctrl.project.status">
                        </project-name>
                    </div>

                    <!-- Main form -->
                    <div class="editor-block">
                       <div class="row">
                           <div class="col-md-9">
                               <div class="section">
                                   <div class="project-name">
                                        <!-- Input: Project name -->
                                        <input-text initial-value="$ctrl.project.name"
                                                    max-length="100"
                                                    on-change="$ctrl.onProjectDataChange(value, 'name')"
                                                    label="'PROJECT_EDITOR_NAME_LABEL' | translate"
                                                    placeholder="'PROJECT_EDITOR_NAME_PLACEHOLDER' | translate">
                                        </input-text>
                                    </div>
                               </div>

                                <!-- Input: Project description -->
                               <project-editor-description initial-value="$ctrl.project.description"
                                                           on-change="$ctrl.onDescriptionChange"
                                                           on-attach-files="$ctrl.onAttachFiles()"
                                                           can-upload-files="$ctrl.canUploadFiles()"
                                                           cannot-upload-tooltip="$ctrl.cannotUploadTooltip"
                                                           label="'PROJECT_EDITOR_DESCRIPTION_LABEL' | translate"
                                                           placeholder="'PROJECT_EDITOR_DESCRIPTION_PLACEHOLDER' | translate">
                               </project-editor-description>

                               <div class="form-group">
                                   <!-- Project files -->
                                   <file-list-new files="$ctrl.project.files"
                                                  filter="$ctrl.notCustomFieldFile(file)"
                                                  file-removed="$ctrl.onFileRemoved(file)">
                                   </file-list-new>
                               </div>

                               <!-- Input: skills or services required -->
                               <project-editor-skills get-suggestions="$ctrl.searchSkillSuggestions"
                                                      label="'PROJECT_EDITOR_SKILLS_REQUIRED_LABEL' | translate"
                                                      prompt-text="'PROJECT_EDITOR_SKILLS_REQUIRED_PLACEHOLDER' | translate"
                                                      default-placeholder-text="'PROJECT_EDITOR_SKILLS_REQUIRED_PLACEHOLDER' | translate"
                                                      allow-only-suggested-tags="true"
                                                      max-num-tags="$ctrl.allowedSkillsNum"
                                                      tags-selected="$ctrl.skills.selected">
                               </project-editor-skills>

                               <div class="form-group">
                                    <project-editor-dates-assignment ng-if="$ctrl.project.project_type == 'assignment'"
                                                                     project="$ctrl.project"
                                                                     datepicker-min-date="$ctrl.datepickerMinDate">
                                    </project-editor-dates-assignment>

                                   <project-editor-dates-rfp ng-if="$ctrl.project.project_type == 'rfp'"
                                                             project="$ctrl.project"
                                                             datepicker-min-date="$ctrl.datepickerMinDate">
                                   </project-editor-dates-rfp>

                                   <project-editor-dates-rfi ng-if="$ctrl.project.project_type == 'rfi'"
                                                             project="$ctrl.project"
                                                             datepicker-min-date="$ctrl.datepickerMinDate">
                                   </project-editor-dates-rfi>
                               </div>
                           </div>
                       </div>
                    </div>

                    <!-- Location (Assignment only) -->
                    <div ng-if="$ctrl.project.project_type == 'assignment'"
                         class="editor-block">
                        <div class="row">
                            <div class="col-md-9">
                                <project-editor-location show-location="$ctrl.project.budget_duration.location_data"
                                                         initial-location="$ctrl.project.budget_duration.location"
                                                         on-change="$ctrl.onProjectLocationChange(showLocation, value)">
                                </project-editor-location>
                            </div>
                        </div>
                    </div>

                    <!-- Budget Settings - RFP -->
                    <div class="editor-block" ng-if="$ctrl.project.project_type == 'rfp'">
                        <accordion>
                            <accordion-group heading="{{ 'PROJECT_EDITOR_BUDGET_SETTINGS' | translate }}"
                                             is-open="false">
                                <div class="row">
                                    <div class="col-md-9">
                                        <project-editor-budget-rfp project="$ctrl.project"
                                                                   budget="$ctrl.budget"
                                                                   budgets="$ctrl.budgets"
                                                                   currencies="$ctrl.currencies"
                                                                   is-currency-edit-available="$ctrl.isCurrencyEditAvailable()"
                                                                   change-budget-type="$ctrl.changeBudgetType(selectedBudget)"
                                                                   budget-item-editing="$ctrl.budgetItemEditing"
                                                                   add-budget-item="$ctrl.addBudgetItem()"
                                                                   edit-budget-item="$ctrl.editBudgetItem(index)"
                                                                   remove-budget-item="$ctrl.removeBudgetItem(index)"
                                                                   update-budget-item="$ctrl.updateBudgetItem(index)"
                                                                   is-allowed-to-remove-budget-item="$ctrl.isAllowedToRemoveBudgetItem()">
                                        </project-editor-budget-rfp>
                                    </div>
                                </div>
                            </accordion-group>
                        </accordion>
                    </div>

                    <!-- Estimated Costs - Assignment -->
                    <div class="editor-block" ng-if="$ctrl.project.project_type == 'assignment'">
                        <accordion>
                            <accordion-group heading="{{ 'PROJECT_EDITOR_ESTIMATED_COSTS' | translate }}"
                                             is-open="false">
                                <div class="row">
                                    <div class="col-md-9">
                                        <project-editor-budget-assignment project="$ctrl.project"
                                                                          budget="$ctrl.budget"
                                                                          budgets="$ctrl.budgets"
                                                                          hourly-rates="$ctrl.hourlyRates"
                                                                          project-durations="$ctrl.projectDurations"
                                                                          currencies="$ctrl.currencies"
                                                                          is-currency-edit-available="$ctrl.isCurrencyEditAvailable()"
                                                                          change-budget-type="$ctrl.changeBudgetType(selectedBudget)"
                                                                          estimated-cost="$ctrl.estimatedCost"
                                                                          set-estimated-cost="$ctrl.setEstimatedCost(type)"
                                                                          hours-of-work-per-durations="$ctrl.hoursOfWorkPerDurations"
                                                                          set-hours-of-work-not-sure="$ctrl.setHoursOfWorkNotSure(hoursOfWorkNotSure)">
                                        </project-editor-budget-assignment>
                                    </div>
                                </div>
                            </accordion-group>
                        </accordion>
                    </div>

                    <!-- Project custom fields -->
                    <div class="editor-block" ng-if="$ctrl.projectTemplates.length">
                        <accordion>
                            <accordion-group heading="{{ 'PROJECT_EDITOR_CUSTOM_FIELDS_HEADER' | translate }}"
                                             is-open="$ctrl.accordions.customFields">

                                <custom-field-templates-search-input
                                        suggestions="$ctrl.projectTemplates"
                                        label="'CUSTOM_FIELDS_SEARCH_LABEL' | translate"
                                        prompt-text="'CUSTOM_FIELDS_SEARCH_PROMPT' | translate"
                                        default-placeholder-text="'CUSTOM_FIELDS_SEARCH_PLACEHOLDER' | translate"
                                        allow-only-suggested-tags="true"
                                        tags-selected="$ctrl.project_templates.selected">
                                </custom-field-templates-search-input>

                                <div ng-repeat="template in $ctrl.projectTemplatesData.customFields track by $index">
                                    <project-custom-field
                                            related-item="$ctrl.project"
                                            get-project-slug="$ctrl.getProjectSlug()"
                                            can-upload-files="$ctrl.canUploadFiles()"
                                            cannot-upload-tooltip="$ctrl.cannotUploadTooltip"
                                            field="field"
                                            value="$ctrl.getProjectCustomFieldValue(field)"
                                            get-child-field-value="$ctrl.getProjectCustomFieldValue(field)"
                                            on-change="$ctrl.setProjectCustomFieldValue(field, value)"
                                            ng-repeat="field in $ctrl.getTemplateFields(template.id) track by $index">
                                    </project-custom-field>
                                </div>

                            </accordion-group>
                        </accordion>
                    </div>

                    <!-- Participant questionnaire -->
                    <div class="editor-block" ng-if="$ctrl.project.status == 'draft'">
                        <accordion>
                            <accordion-group heading="{{ 'PROJECT_EDITOR_QUESTIONNAIRE_HEADER' | translate }}"
                                             is-open="false">
                                <div class="row">
                                    <div class="col-md-9">
                                        <project-editor-questionnaire response-form="$ctrl.responseForm"
                                                                      project="$ctrl.project"
                                                                      response-forms="$ctrl.responseForms"
                                                                      create-questionnaire="$ctrl.createQuestionnaire()"
                                                                      edit-questionnaire="$ctrl.editQuestionnaire(responseForm)"
                                                                      remove-questionnaire="$ctrl.removeQuestionnaire()"
                                                                      response-form-descr="$ctrl.responseFormDescr"
                                                                      response-form-questions="$ctrl.responseFormQuestions"
                                                                      update-preview-snippet="$ctrl.updatePreviewSnippet()">
                                        </project-editor-questionnaire>
                                    </div>
                                </div>
                            </accordion-group>
                        </accordion>
                    </div>

                    <!-- Custom message -->
                    <div class="editor-block">
                        <div class="row">
                            <div class="col-md-9">
                                <project-editor-custom-message
                                    custom-email="$ctrl.customEmail"
                                    email-variables="$ctrl.emailVariables"
                                ></project-editor-custom-message>
                            </div>
                        </div>
                    </div>

                    <!-- Project state -->
                    <div class="editor-block"
                         ng-if="$ctrl.project.status == 'draft'"
                         user-permissions="buyer/project/project member/publish">
                        <div class="row">
                            <div class="col-md-9">
                                <project-editor-state on-change="$ctrl.setProjectState(state)"
                                                      initial-state="$ctrl.projectIsLive">
                                </project-editor-state>
                            </div>
                        </div>
                    </div>

                    <!-- Buttons -->
                    <div class="editor-block">
                        <project-editor-buttons project-status="$ctrl.project.status"
                                                project-is-live="$ctrl.projectIsLive"
                                                is-project-saving="$ctrl.isProjectSaving"
                                                save-project="$ctrl.saveProject()"
                                                post-project="$ctrl.postProject()"
                                                update-project="$ctrl.updateProject()"
                                                preview-project="$ctrl.previewProject()"
                                                discard-changes="$ctrl.discardChanges()">
                        </project-editor-buttons>
                    </div>

                </div>

            </div>
        </div>
    </div>
</div>
