<div class="section project-editor-state">
    <div class="form-group project-state">
        <label>
            {{ 'PROJECT_EDITOR_STATE_LABEL' | translate }}
        </label>
        <p>{{ 'PROJECT_EDITOR_STATE_DESCRIPTION' | translate }}</p>
        <div class="select-switch inline no-labels">
            <switcher ng-model="$ctrl.projectIsLive"
                      ng-change="$ctrl.onStateChange(newValue, oldValue)"
                      label-true=""
                      label-false="">
            </switcher>
            <strong ng-show="$ctrl.projectIsLive">
                {{ 'PROJECT_EDITOR_STATE_SWITCH_LABEL_LIVE' | translate }}
            </strong>
            <strong ng-show="!$ctrl.projectIsLive">
                {{ 'PROJECT_EDITOR_STATE_SWITCH_LABEL_DRAFT' | translate }}
            </strong>
        </div>
    </div>
</div>
