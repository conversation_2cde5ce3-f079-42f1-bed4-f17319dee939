const projectEditorState = {
    bindings: {
        onChange: '&',
        initialState: '<'
    },
    templateUrl: '/components/projects/project-editor/project-editor-state/project-editor-state.template.html',
    controller() {
        this.$onInit = () => {
            this.projectIsLive = angular.copy(this.initialState);
        }

        this.onStateChange = (newValue, oldValue) => {
            this.onChange({state: newValue});
        }

    }
}

export default projectEditorState
