/* Make Parent Div of Modal Background Transparent */
.modal-content:has(.modal-create-project) {
    background: transparent;
}
.modal-create-project {

    background: $white;

    .modal-footer, .modal-header, .modal-body {
        background: none !important;
        margin-left: 48px;
        margin-right: 48px;
        padding-left: 0px !important;
        padding-right: 0px !important;
    }

    .modal-title {
        text-align: left !important;
        font-size: 33px !important;
        text-transform: capitalize;
    }

    a {
        transition: all 0.3s ease;
        color: $shortlist-blue-1;
    }

    .project-type-icon {
        display: block;
        margin-bottom: 10px;
        font-size: 60px;

        & > span {
            font-size: 60px !important;
        }
    }

    .project-type {
        font: $fonts-opensans-semibold;
        color: $shortlist-blue-1;
        margin-bottom: 10px;
    }

    .project-type-description {
        font: $fonts-opensans-semibold;
        font-size: 12px;
    }

    .modal-body {
        padding-top: 20px !important;
        padding-bottom: 50px !important;

        .row {

            & > div {
                text-align: center;

                a {
                    display: block;
                    text-align: center;
                    width: 100%;
                    padding-top: 20px;
                    padding-bottom: 20px;
                    min-height: 222px;

                    &:hover {
                        background: $shortlist-blue-4;
                        color: $shortlist-blue-1;
                    }

                    img {
                        display: block;
                        margin: auto;
                    }
                }
            }
        }
    }
}
