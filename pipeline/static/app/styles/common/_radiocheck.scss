.checkbox,
.radio {
    margin-bottom: 12px;
    padding-left: 32px;
    padding-top: 2px;
    position: relative;
    font-size: 14px;
    line-height: 18px;
    text-align: left;
    font-weight: normal;
    color: $shortlist-blue-1;

    &.selected {
        color: $gray-dark-color;
    }

    .checkbox-info {
        font-size: 16px !important;
        vertical-align: bottom;
        margin-left: 5px;
        display: inline-block;
        padding-bottom: 1px;
        color: $shortlist-grey-2;
    }

    .tooltip {
        font-weight: normal;
    }

    .icons {
        border-radius: 3px;
        color: $shortlist-grey-3;
        display: block;
        top: 0;
        left: 0;
        position: absolute;
        text-align: center;
        cursor: pointer;
        overflow: hidden;

        .icon-checked {
            opacity: 0;
        }

        &,
        .icon-checked,
        .icon-unchecked {
            width: 20px;
            height: 20px;
        }

        .material-icons {
            font: bold 12px/20px 'Material Icons';
            vertical-align: top;
        }
    }
}

.checkbox,
.radio {
    .icon-checked,
    .icon-unchecked {
        transition: opacity $link-transition-duration $ease-in-out-quad;
        opacity: 1;
        border-radius: 3px;
        display: inline-block;
        position: absolute;
        left: 0;
        top: 0;
        background-color: transparent;
        margin: 0;
        filter: none;
    }

    &.no-transition {
        .icon-checked,
        .icon-unchecked {
            transition: none;
        }
    }
}

.checkbox {
    .icons {
        line-height: 25px;
    }

    .icon-checked {
        background-color: $shortlist-blue-1;

        .material-icons {
            color: $white;
        }
    }
    .icon-unchecked {
        border: 2px $shortlist-blue-3 solid;
    }

    &.selected {
        .icon-checked {
            background-color: $shortlist-blue-1;
        }
    }

    &.disabled {
        color: $gray-dark-color !important;

        .icon-checked {
            cursor: not-allowed;
            background-color: $shortlist-grey-3;
        }

        .icon-unchecked {
            cursor: not-allowed;
            border: 3px $shortlist-grey-3 solid;
        }

        .checkbox-label {
            opacity: 0.5;
        }
    }
}

.radio {
    line-height: 18px;

    span.icons {
        margin-top: 3px;
        margin-left: 8px;
    }

    .icon-unchecked {
        color: #aaa;
    }

    .icon-checked {
        color: #aaa;
    }

    &.disabled {
        color: $gray-dark-color !important;

        .icon-checked, .icon-unchecked {
            cursor: not-allowed;

            .material-icons {
                opacity: 1;
                color: $shortlist-grey-3 !important;
            }
        }

        .radio-label {
            opacity: 0.5;
        }
    }
}

.checkbox,
.radio {
    input[type="checkbox"].custom-checkbox,
    input[type="radio"].custom-radio {
        outline: none !important;
        opacity: 0;
        position: absolute;
        margin: 0;
        padding: 0;
        left: 0;
        top: 0;
        width: 20px;
        height: 20px;

        &:checked + .icons {
            color: $dark-blue-color;

            .icon-unchecked {
                opacity: 0;
            }
            .icon-checked {
                opacity: 1;
                filter: none;
                color: $dark-blue-color;
            }
        }

        &:disabled + .icons {
            cursor: default;
            color: $shortlist-grey-3;

            .icon-unchecked {
                opacity: 1;
                filter: none;
            }
            .icon-checked {
                opacity: 0;
            }
        }

        &:disabled:checked + .icons {
            color: $shortlist-grey-3;

            .icon-unchecked {
                opacity: 0;
            }
            .icon-checked {
                opacity: 1;
                filter: none;
                color: $shortlist-grey-3;
            }
        }

        &:indeterminate + .icons {
            color: $gray-light-color;

            .icon-unchecked {
                opacity: 1;
                filter: none;
            }
            .icon-checked {
                opacity: 0;
            }
            &:before {
                content: "\2013";
                position: absolute;
                top: 0;
                left: 0;
                line-height: 20px;
                width: 20px;
                text-align: center;
                color: $white;
                font-size: 22px;
                z-index: 10;
            }

        }
    }

    &.primary input[type="checkbox"].custom-checkbox,
    &.primary input[type="radio"].custom-radio {
        & + .icons {
            color: $dark-blue-color;
        }

        &:checked + .icons {
            color: $dark-blue-color;
        }

        &:disabled + .icons {
            cursor: default;
            color: $gray-light-color;

            &.checked {
                color: $gray-light-color;
            }
        }

        &:indeterminate + .icons {
            color: $dark-blue-color;
        }

    }
}

.radio,
.checkbox {
    .input-group-addon & {
        margin: -2px 0;
        padding-left: 20px;

        .icons {
            color: mix($gray-light-color, white, 38%);
        }

        input[type="checkbox"].custom-checkbox,
        input[type="radio"].custom-radio {
            &:checked + .icons {
                color: $white;

                .icon-checked {
                    color: $white;
                }
            }
            &:disabled + .icons {
                color: $shortlist-grey-3;
            }
            &:disabled:checked + .icons {
                color: $shortlist-grey-3;

                .icon-checked {
                    color: $shortlist-grey-3;
                }
            }
        }
    }
}

.radio + .radio,
.checkbox + .checkbox {
    margin-top: 10px;
}

.form-inline .checkbox, .form-inline .radio {
    padding-left: 32px;
}

.form-horizontal {
    .radio,
    .checkbox {
        padding-top: 2px;
    }
}
