.loader {
    text-align: center;

    h2 {
        font-size: 28px;
        font-weight: bold;
        color: $shortlist-grey-2;
        margin-bottom: 30px;
    }

    ul {
        display: inline-block;
        list-style: none;
        padding: 0;

        li {
            box-sizing: border-box;
            border-radius: 50%;
            opacity: 0.2;

            &:nth-child(1) {
                @include animation(anim .9s infinite linear);
            }

            &:nth-child(2) {
                @include animation(anim .9s infinite linear);
                animation-delay: .3s;
            }

            &:nth-child(3) {
                @include animation(anim .9s infinite linear);
                animation-delay: .6s;
            }
        }

        @keyframes anim {
            from {
                opacity: 1;
            }

            to {
                opacity: 0.2;
            }
        }
    }
}

.load-more-wrapper {
    width: 100%;
    text-align: center;
    line-height: 34px;
    margin-top: 32px;
    position: absolute;
}

.indicator-dots {
    display: inline-block;
    list-style: none;
    padding: 0;

    li {
        box-sizing: border-box;
        border-radius: 50%;
        float: left;
        width: 10px;
        height: 10px;
        background: $shortlist-grey-2;
        margin-right:5px;

        &:last-child {
            margin-right: 0;
        }
    }
}
