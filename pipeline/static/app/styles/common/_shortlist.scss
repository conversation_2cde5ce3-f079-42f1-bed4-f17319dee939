.shortlist {
    &__text-1 {
        font-weight: 400;
        font-size: 14px;
        font-family: 'Open Sans', sans-serif;
        color: $shortlist-grey-1;
        letter-spacing: 0px;
        line-height: 140%;
    }

    &__text-2 {
        @extend .shortlist__text-1;

        font-size: 16px;
        font-weight: 600;
    }

    &__html-1 {
        @extend .shortlist__text-1;

        b,
        strong {
            font-weight: 600;
        }

        p {
            @extend .shortlist__text-1;
        }
    }

    &__block-tile-1 {
        border-radius: 4px;

        border: 1px solid $shortlist-grey-3;
        padding: 20px;
    }

    &__button-1 {
        border-radius: 4px;

        display: block;
        text-align: center;
        line-height: 60px;
        font-size: 12px;
        font-weight: 600;
        background-color: $shortlist-blue-4;
        border: 1px solid $shortlist-grey-3;
        color: $shortlist-blue-1;
        padding-left: 10px;
        padding-right: 10px;

        i {
            vertical-align: middle;
            margin-right: 3px;
            padding-bottom: 2px;
            font-size: 20px;

            &[class^='icon-'] {
                display: inline-block;
            }
        }

        &:hover {
            border: 1px solid $shortlist-blue-2;
        }

        &.has-error {
            border-color: $color-input-error;
            background-color: $pink-3;
        }

        &:disabled {
            color: $shortlist-grey-3;
            cursor: not-allowed;
        }

        &:hover:disabled {
            border: 1px solid $shortlist-grey-3;
        }
    }

    &__button-2 {
        @extend .shortlist__button-1;

        background-color: transparent;
    }

    &__button-3 {
        @extend .shortlist__button-1;

        color: $white;
        background-color: $shortlist-blue-1;
        border-width: 0;

        &.shortlist__button--small {
            line-height: 32px;
        }

        &:hover,
        &:focus,
        &:visited {
            color: $white;

            &:not(.has-error) {
                border-width: 0;
            }
        }

        &.has-error {
            color: $shortlist-blue-1;
            border-width: 1px;
        }
    }

    &__button {
        &--small {
            line-height: 37px;
        }
    }

    &__list-flex {
        display: flex;

        > div {
            font-size: 14px;
            white-space: nowrap;

            &:not(:last-child) {
                padding-right: 10px;
            }

            a[data-toggle='dropdown'] {
                font-size: 18px;
                color: $shortlist-grey-2;
            }
        }
    }

    &__column {
        &--main {
            flex-basis: 100%;
            font-weight: 600;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        &--right {
            text-align: right;
        }

        &--20px {
            min-width: 20px;
        }
    }

    &__list-1 {
        @extend .block-tile;

        padding: 20px;
        cursor: pointer;
    }

    &__list-2 {
        @extend .shortlist__list-1;
        border-radius: 4px;

        border: 1px solid $shortlist-grey-3;
        box-shadow: none;

        &:hover {
            border: 1px solid $shortlist-grey-2;
        }
    }

    &__list-3 {
        @extend .shortlist__list-2;

        border: 1px solid $shortlist-blue-3;
        background: $shortlist-blue-4;

        &:hover {
            border: 1px solid $shortlist-grey-2;
        }
    }

    &__table {
        $cell-padding-vertical: 10px;
        $cell-padding-horizontal: 15px;
        $border-color: $shortlist-grey-3;
        margin-bottom: 200px;

        display: table;
        width: 100%;

        &--columns-management-enabled {
            .shortlist__table__cell--header:nth-last-child(2) {
                padding-right: 45px;
            }
        }

        &--style1 {
            border-radius: 4px;
            border: 1px solid $border-color;
            background-color: $white;

            &.shortlist__table--data-row-hover-enabled {
                .shortlist__table__row {
                    cursor: pointer;

                    &:hover {
                        background-color: $shortlist-blue-4;
                    }
                }
            }

            .shortlist__table {
                &__cell {
                    padding: $cell-padding-vertical $cell-padding-horizontal;

                    &:not(:last-child) {
                        border-right: 1px solid $border-color;
                    }

                    &:last-child {
                        padding-right: $cell-padding-horizontal;
                    }

                    &--header {
                        font-size: 12px;
                        color: $shortlist-grey-2;
                        background-color: $shortlist-grey-4;
                        padding-top: $cell-padding-vertical;
                    }
                }
            }
        }

        &__row {
            display: table-row;

            &--is_loading {
                .shortlist__table__cell {
                    &:before {
                        content: '';
                        background-color: adjust-color(
                            $color: $white,
                            $alpha: -0.2
                        );
                        top: 0;
                        z-index: 1;
                    }
                }
            }
        }

        &__header_row {
            display: table-row;

            .dropdown {
                position: absolute;
                right: 24px;
                top: 2px;

                .dropdown-menu {
                    padding: 10px 0;
                    left: unset;
                    right: 0;

                    li a {
                        padding: 0 15px;
                    }
                }

                .btn-actions-dots {
                    padding: 0;

                    .material-icons {
                        font-size: 18px;
                        color: $shortlist-grey-2;
                    }
                }
            }
        }

        &__cell {
            display: table-cell;
            position: relative;
            padding: $cell-padding-vertical $cell-padding-horizontal
                $cell-padding-vertical 0;
            vertical-align: middle;
            // Hack for displaying bottom border in table row on IE11
            background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQYV2N48vT1fwAJGQO0TnUVfgAAAABJRU5ErkJggg==);
            background-repeat: repeat-x;
            background-position: bottom;

            a {
                font-weight: 600;
            }

            &:last-child {
                padding-right: 0;
            }

            &--header {
                font-weight: 600;
                padding-top: 0;

                &--hover-enabled {
                    cursor: pointer;
                }

                // &--selected {

                //     &:after {
                //         font-family: 'Material Icons';
                //         content: 'arrow_drop_down';
                //         font-size: 16px;
                //         position: absolute;
                //     }
                // }
            }

            &--header-sticky {
                position: -webkit-sticky; /* for Safari */
                position: sticky;
                top: 0;
                z-index: 1;
            }

            @for $i from 1 through 10 {
                &--main-#{$i * 10} {
                    width: $i * 10%;
                }
            }

            &--min-width-200 {
                min-width: 200px;
            }

            &--width-120 {
                width: 120px;
            }

            &--width-50 {
                width: 50px;
            }

            &--max-width-50 {
                min-width: 50px !important;
                width: 50px !important;
            }

            &--nowrap {
                white-space: nowrap;
            }

            &--align-center {
                text-align: center;
            }

            &--no-padding {
                padding: 0 !important;
            }

            &--dropdown {
                text-align: center;
                overflow: visible !important;
            }

            .dropdown-actions--max-height {
                .dropdown-menu {
                    max-height: 190px;
                    overflow-x: hidden;
                    overflow-y: auto;
                }
            }
        }

        &__editable {
            background-color: $yellow-3;
            min-width: 100px;
            max-width: 300px;
            width: max-content;

            span {
                cursor: pointer;
            }

            &__empty {
                color: $yellow-5;
            }
        }

        &__cell-avatar {
            $avatar-size: 40px;

            position: relative;
            height: $avatar-size + ($cell-padding-vertical * 2);

            &__vendor-name {
                display: inline-block;
                margin-left: $cell-padding-vertical;
            }

            .avatar-wrapper {
                position: absolute;
                top: 0;
                left: 0;
            }
        }

        &__cell-group-name-with-logo {
            display: block;
            margin-left: 50px;
            line-height: 40px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    &__support-text {
        font-size: 12px;
        font-style: italic;
        color: $shortlist-grey-2;
    }

    &__sticky-container {
        overflow: scroll;
        position: relative;
        max-height: calc(100vh - 200px);

        .shortlist__table__cell--header {
            line-height: 30px;
        }

        table {
            position: relative;
            border-collapse: collapse;
        }

        thead th {
            position: -webkit-sticky; /* for Safari */
            position: sticky;
            top: 0;
            z-index: 1;
            text-align: center;
        }

        thead th:first-child {
            left: 0;
            z-index: 2;

            &:after {
                border-right: 1px solid $shortlist-grey-3;
                content: '';
                position: absolute;
                right: -1px;
                top: 0;
                bottom: 0;
            }
        }

        tbody th {
            position: -webkit-sticky; /* for Safari */
            position: sticky;
            left: 0;
            border-right: 1px solid $shortlist-grey-3;
            z-index: 1;
        }

        tbody th:after {
            border-right: 1px solid $shortlist-grey-3;
            content: '';
            position: absolute;
            right: -1px;
            top: 0;
            bottom: 0;
        }

        &__header {
            height: 50px;
        }

        &-wrapper {
            position: relative;

            &.fixed-height-dynamic-table:after {
                content: none !important;
            }

            &:before {
                border: 1px solid $shortlist-grey-3;
                content: '';
                position: absolute;
                z-index: 3;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                pointer-events: none;
            }

            &:after {
                border-bottom: 1px solid $shortlist-grey-3;
                content: '';
                position: absolute;
                z-index: 3;
                top: 50px;
                left: 0;
                right: 0;
                pointer-events: none;
            }
        }
    }
}

.sl__t {
    $table-row-padding: 10px;
    $table-border-color: $shortlist-grey-3;
    $table-checkbox-width: 30px;
    $table-dropdown-width: 25px;

    display: table;
    border-top: 1px solid $table-border-color;
    margin-bottom: 20px;
    background: white;
    width: 100%;

    &::after {
        content: '';
        border-left: 1px solid $table-border-color;
        border-top: 1px solid $table-border-color;
        position: absolute;
        background: white;
        top: 0;
        bottom: 20px;
        right: -1000px;
        width: 1000px;
    }

    &::before {
        content: '';
        border-right: 1px solid $table-border-color;
        border-top: 1px solid $table-border-color;
        position: absolute;
        background: white;
        top: 0;
        bottom: 20px;
        left: -1000px;
        width: 1000px;
        z-index: 2;
    }

    &--hover-enabled {
        .sl__t__row {
            cursor: pointer;

            &:hover {
                background-color: $shortlist-blue-4;
            }
        }
    }

    &__overflow {
        overflow-x: scroll;
        overflow-y: hidden;
        padding-bottom: 150px;
    }

    &__wrapper {
        position: relative;
    }

    &--no-padding {
        padding: 0 !important;
    }

    &__header {
        background: $shortlist-grey-4;

        .btn-actions-dots {
            padding: 0 !important;
        }

        .sl__t__checkbox {
            position: absolute;
            background: $shortlist-grey-4;
            z-index: 4;
            border-bottom: 1px solid $table-border-color !important;
            width: 1000px;
            left: -971px;
            margin-left: $table-checkbox-width * -1 !important;
        }

        & > div {
            padding: $table-row-padding;
            padding-top: $table-row-padding + 5 !important;
            padding-bottom: $table-row-padding + 5 !important;
            color: $shortlist-grey-1 !important;
            font-weight: 600;
            position: relative;
            width: auto;
            vertical-align: top;
        }

        .floating {
            background: $shortlist-grey-4;
            border-bottom: 1px solid $table-border-color;
            display: none;
            position: absolute;
            z-index: 1;
            left: 0;
            right: 0;
            top: 0;
            padding: $table-row-padding;
            padding-top: $table-row-padding + 5 !important;
            padding-bottom: $table-row-padding + 5 !important;

            &.visible {
                display: block;
            }
        }
    }

    &__checkbox {
        position: absolute;
        z-index: 3;
        margin-left: ($table-checkbox-width + 10px) * -1;
        border: 0px solid !important;
        padding: 0 !important;

        label {
            cursor: pointer;
            padding: 13px;
        }

        input {
            cursor: pointer;
            opacity: 0;
            transform: scale(1.5);
        }
    }

    &__dropdown {
        width: 1000px !important;
        position: absolute !important;
        margin-right: $table-dropdown-width * -1;
        padding: 0 !important;
        background: $shortlist-grey-4;
        height: 40px;
        border-bottom: 1px solid $shortlist-grey-3;
        z-index: 1;
        right: (1000px - $table-dropdown-width + 1) * -1;

        .dropdown-menu {
            left: unset;
            right: 0;
        }

        .material-icons {
            color: $shortlist-grey-2;
            display: block;
            margin-top: -1px;
        }

        .floating {
            padding: $table-row-padding;
        }

        &__static {
            padding-left: $table-row-padding !important;
        }
    }

    &__row {
        display: table-row;

        .shortlist__table__cell {
            max-width: 300px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            &__without-overflow {
                overflow: initial;
                text-overflow: initial;
                white-space: initial;

                .vendor-services {
                    margin-right: -4px;
                    margin-bottom: -8px;
                }

                .skill {
                    font-size: 14px;
                    color: $shortlist-grey-1 !important;
                    font-weight: normal;
                    max-width: 200px;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    overflow: hidden;
                }
            }
        }

        &.shortlist__table__row--is_loading {
            .shortlist__table__cell {
                &:before {
                    position: absolute;
                    content: '';
                    background-color: adjust-color(
                        $color: $white,
                        $alpha: -0.2
                    );
                    top: 0;
                    bottom: 1px;
                    left: 0;
                    right: 0;
                    z-index: 1;
                }
            }
        }

        & > div {
            padding: $table-row-padding;
            border-right: 1px solid;
            border-color: $table-border-color;
            display: table-cell;

            &:last-child {
                border-right: 0;
            }
        }

        &:hover .sl__t__checkbox input,
        .sl__t__checkbox input:checked {
            opacity: 1;
        }
    }
}

.state-app__dashboard {
    &.feature__vendor_dashboard {
        background-color: var(--canvas-theme) !important;
    }
}

.state-app__shared-group-details {
    .shortlist__table__cell:last-child {
        padding-right: 10px;
    }
}

// This one is displayed in the root of html
.dropdown-menu {
    .dropdown-actions__label {
        padding: 0 30px;
        color: $shortlist-grey-1;
        font-weight: 600;
        font-size: 13px;
    }
}

body.dropdown {
    position: initial;
    display: initial;
}

// Needed to correctly display table with column management
.state-app__partner-side__all-workers,
.state-app__marketplace__job-openings__list {
    margin-bottom: 0 !important;
}

.all-workers-state,
.list-state,
.state-app__shared-group-details,
.state-app__marketplace__job-openings__details__tab-workflow__stage
    .state-app__marketplace__job-openings,
.state-app__marketplace__job-openings__details__tab-workflow__finished-stage
    .state-app__marketplace__job-openings,
.app__onboarding-workflow .onboarding-workflow-details-state__ui-view {
    overflow-x: hidden;

    .job-opening-list__create-cta > h4 {
        margin-bottom: 0;
        line-height: normal;
    }
}
