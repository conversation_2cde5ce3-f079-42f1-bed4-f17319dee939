.form-control {
    border-radius: $input-common-border-radius;
    height: $input-common-height;
    line-height: $input-common-height;
    color: $shortlist-grey-2;
    font: $input-common-font;

    &, &:active, &:focus {
        border: $input-common-border;
    }

    &:active, &:focus {
        color: $shortlist-grey-1;
    }

    &:hover {
        border-color: $shortlist-blue-1;
    }

    &.success {
        border-color: $shortlist-green-dark;
        background-color: $shortlist-grey-5;
    }
}

.input-group {
    .form-control {
        &,
        &:first-child,
        &:not(:first-child),
        &:not(:last-child),
        &:last-child {
            border-radius: $input-common-border-radius;
        }
    }
}

.has-error {
    input[type="tel"].iti__tel-input,
    .form-control {
        &, &:active, &:focus {
            border-color: $pink-1;
            background-color: $pink-3;
        }
    }
}

.input-with-icon {
    position: relative;
    .material-icons {
        position: absolute;
        left: 8px;
        top: 13px;
    }
    .form-control {
        padding-left: 35px;
    }
}

.switcher-line {
    width: 35px;

    &:before {
        background: $shortlist-blue-3;
        height: 10px;
    }
}

.switcher.active {
    .switcher-line:before {
        background: $shortlist-blue-3;
    }
    .switcher-line:after {
        background: $shortlist-blue-1;
        border: none;
    }
}

.select-switch {
    &.inline {
        display: flex;
        align-items: center;

        .switcher-line {
            margin-bottom: 0;
        }

        strong {
            position: relative;
            top: -3px;
            display: inline-block;
            font-weight: 500;
        }
    }

    &.no-labels {
        .switcher-label {
            display: none;
        }
    }
}

.form-card {
    border-radius: 5px;
    padding: 60px;
    background-color: $white;

    &__header {
        margin-bottom: 20px;
    }

    .confirmation-message {
        .icon {
            font-size: 60px;
        }

        .shortlist-heading-h4 {
            margin: 20px 0;
        }

        .copy {
            line-height: 20px;
        }
    }
}
