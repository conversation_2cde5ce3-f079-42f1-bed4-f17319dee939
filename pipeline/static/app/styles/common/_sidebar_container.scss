.sidebar-container {
    display: flex;

    .container-flex {
        width: 100%;
        max-width: 1230px;
        margin: auto;
    }

    .content {
        flex: 1;
        background-color: $white;
    }

    .sidebar {
        width: $sidebar-container-sidebar-width;
        padding: $sidebar-container-sidebar-padding;

        .tag-list {
            background: #FFFFFF;
        }

        .buttons {
            text-align: center;

            .btn-default,
            .btn-secondary {
                width: 100%;
                margin-bottom: 6px;
            }
        }

        .sidebar-block-with-separator {
            margin-top: $sidebar-block-padding;
            padding-bottom: $sidebar-block-padding;
            border-bottom: 1px $shortlist-grey-3 solid;

            &:last-child {
                border-bottom: none;
            }

            a {
                color: $button-default-color;

                &:hover {
                    color: $button-default-color-hover;
                }
            }
        }

        .heading-num-value {
            width: 50%;
            float: left;
        }

        .shortlist-heading-sidebar {
            margin-bottom:  15px;
        }

        .list-unstyled {
            font: 600 14px/24px 'Open Sans';
        }
    }
}
