'use strict';

import { createGroupOrList } from "../../components/groups/groups.classes";
import { Api, VendorHelpers } from 'shortlist-react';

angular.module("pipeline").service("vendorUtils",
 function($http, $rootScope, $modal, $timeout, $translate, asyncTaskTracker, $q, $state, _, userAuth,
    ConfirmModal, userPermissionsService, onboardingService, DateTimeFormatService) {

    const getPromise = function(promise) {
        const deferred = $q.defer();
        promise.then(response => deferred.resolve(response.data)).catch(response => deferred.reject(response.data));
        return deferred.promise;
    };

    this.getVendorsResume = (vendorSlugs, documentType = null) => {
        if (!userPermissionsService.hasAccessToDocuments()) {
            return Promise.resolve([]);
        }

        return $http.get('/api/requested_documents/newest/', {
            params: {
                type: 'resume',
                vendor: vendorSlugs.join(','),
            }
        }).then(({ data = [] } = { data: [] }) => (
            data.filter((i) => (i.filename && documentType && i.filename.indexOf(`.${documentType}`) > 0) || (!documentType))
                .map(i => ({
                    ...i,
                    downloadUrl: onboardingService.downloadRequestedDocumentUrl(i, true, null, i.vendor.slug)
                }))
        ));
    }

    this.checkUserToken = function(stateParams) {
        const deferred = $q.defer();

        const vendorUser = {
            uid: stateParams.uid,
            token: stateParams.token
        };

        $http.post(`/api/vendors/${stateParams.vendor_slug}/check_user_token/`, vendorUser)
        .then(function(vendorResponse) {
            const vendorData = vendorResponse;

            if (userAuth.isLoggedIn()) {
                return deferred.resolve({is_registered: true});
            }

            if (vendorData.data['transaction_ref']) {
                $state.go('login.assure', {
                    transaction_ref: vendorData.data['transaction_ref'],
                    redirect: stateParams.redirect_to,
                });
                return;
            }

            if (!$rootScope.tenantHasFeature('vendor_auto_login')) {

                if (vendorResponse.data.last_active != null) {
                    return $state.go("login.form", {redirect: stateParams.redirect_to});
                } else {
                    return deferred.resolve({
                        vendorUser,
                        vendorEmail: vendorResponse.data.email,
                        vendor_type: vendorData.data.vendor_type,
                        is_company: vendorData.data.is_company,
                        is_registered: false
                    });
                }

            } else {

                return userAuth.getUserData().then(function(response) {
                    if (response.data.slug) {
                        userAuth.login(response.data);
                        if (stateParams.project_slug) {
                            return $state.go("app.project-proposal", {slug: stateParams.project_slug});
                        } else {
                            return deferred.resolve({
                                is_registered: true});
                        }
                    } else {
                        return deferred.resolve({
                            vendorUser,
                            vendor_type: vendorData.data.vendor_type,
                            is_company: vendorData.data.is_company,
                            is_registered: false
                        });
                    }
                });
            }
        }
        , function(res) {
            if (res.data.error_id === 'INVALID_TOKEN_LOGIN_REQUIRED') {
                return $state.go("login.form", {redirect: stateParams.redirect_to});
            } else {
                return $state.go('request-new-token', stateParams);
            }
        });

        return deferred.promise;
    };

    this.updateVendorRank = (vendor, rankId) => getPromise($http.patch(`/api/vendors/${vendor.slug}/`, {rank: rankId}));

    this.getVendorBySlug = vendorSlug => getPromise($http.get(`/api/vendors/${vendorSlug}/`));

    this.invite = function(vendor) {
        vendor.sending = 'sending';
        return $http.post(`/api/vendors/${vendor.slug}/invite/`
        ).success(function(data) {
            $rootScope.$broadcast("vendorInvited", data);
            return $timeout((function() {
                vendor.invited_at = data.invited_at;
                vendor.sending = 'sent';
                $timeout(( () => vendor.sending = null), 2000);
            }), 2000);
        }).error(function(data) {
            const errorMsg = $translate.instant("TOAST_MSG_VENDOR_INVITE_ERROR");
            $rootScope.alertUser(errorMsg,
                "alert-danger");
            return vendor.sending = null;
        });
    };

    this.inviteReminder = function(vendors) {
        var optional = {
            vendors,
            message: '',
            onMessageChange(value) {
                return optional.message = value;
            }
        };

        const texts = {
            modalHeader: $translate.instant("SHORTLIST_MODAL_RESEND_INVITATIONS_MODAL_HEADER"),
            actionName: $translate.instant("SHORTLIST_MODAL_RESEND_INVITATIONS_ACTION_NAME", {count: vendors.length}),
            modalTitle: $translate.instant("SHORTLIST_MODAL_RESEND_INVITATIONS_MODAL_TITLE_MULTIPLE", {count: vendors.length})
        };

        if (vendors.length === 1) {
            texts.modalTitle = $translate.instant("SHORTLIST_MODAL_RESEND_INVITATIONS_MODAL_TITLE_SINGLE", {name: vendors[0].name || vendors[0].email});
        }

        return $modal.open({
            windowClass: "invite-reminder-modal",
            templateUrl: "/partials/vendor/modals/simple-multiple-vendor-action.html",
            controller: 'SimpleMultipleVendorAction',
            resolve: {
                vendors() {
                    return null;
                },
                texts() {
                    return texts;
                },
                optional() {
                    return optional;
                }
            }}).result.then(function() {
            const promises = _.map(vendors, vendor => $http.post(`/api/vendors/${vendor.private_slug || vendor.slug}/invite/`,
                {message: optional.message})
             );
            return $q.all(promises).then(function() {
                let message;
                for (let vendor of Array.from(vendors)) {
                    vendor.invited_at = momentNowTz().unix();
                }
                if (vendors.length > 1) {
                    message = $translate.instant("PROJECT_RESEND_INVITATIONS_HAS_BEEN_RESEND");
                } else {
                    message = $translate.instant("PROJECT_RESEND_INVITATION_HAS_BEEN_RESEND");
                }
                return $rootScope.alertUser(message, "alert-success");
            });
        });
    };

    this.reject = function(vendor) {
        const modalInstance = $modal.open({
            templateUrl: "/partials/vendor/modals/reject.html",
            size: 'lg',
            backdrop: "static",
            controller($scope, $modalInstance) {
                $scope.vendor = vendor;
                $scope.reject = () => $modalInstance.close();
                return $scope.dismiss = () => $modalInstance.dismiss("cancel");
            }
        });
        return modalInstance.result.then(() =>
            $http.post(`/api/vendors/${vendor.slug}/reject/`)
            .then(() =>
                // sending signal to reload vendor list
                $rootScope.$emit('vendorRejection')
            )
        );
    };

    this.accept = function(vendor) {
        const modalInstance = $modal.open({
            templateUrl: "/partials/vendor/modals/accept.html",
            size: 'lg',
            backdrop: "static",
            windowClass: "accept-vendor-modal",
            controller($scope, $modalInstance) {
                $scope.vendor = vendor;
                $scope.confirm = () =>
                    $http.post(`/api/vendors/${vendor.slug}/accept/`,
                        {message: vendor.invitation_email_body})
                    .then(function() {
                        $rootScope.$emit('vendorAcceptance');
                        return $modalInstance.close(vendor);
                    })
                ;
                return $scope.dismiss = () => $modalInstance.dismiss("cancel");
            }
        });
        return modalInstance.result.then(function(vendor) {});
    };

    this.addVendor = function(params) {
        if (params == null) { params = {}; }
        params.addToFavorite = $state.current.name === 'app.partners.shortlist.favorites';
        if ($state.current.name === 'app.partners.shortlist.staffing-suppliers') {
            params.type = 'staffing-supplier';
        }
        $state.go('app.partners-add', params);
        const deferred = $q.defer();
        return deferred.promise;
    };

    this.addVendorsRequest = function(vendors,
                          messageContent=null,
                          selectedGroup=null,
                          showAlert,
                          selectedAgreements,
                          invite,
                          workflows,
                          relationshipManagers,
                          messageSubject = null,
                          addToFavorite,
                          isBulkInvite,
                          staffingSupplierId = null) {

        let groups, request;
        if (showAlert == null) { showAlert = true; }
        if (selectedAgreements == null) { selectedAgreements = []; }
        if (invite == null) { invite = true; }
        if (workflows == null) { workflows = []; }
        if (relationshipManagers == null) { relationshipManagers = []; }
        if (addToFavorite == null) { addToFavorite = false; }
        if (isBulkInvite == null) { isBulkInvite = false; }
        const asyncTaskAddVendors = new asyncTaskTracker({
            timeoutTime: 5000,
        });

        // Invite a partner and assign straight to a chosen group.
        // A group can be supplied either a slug or an array of slugs.
        if (selectedGroup) {
            if (Array.isArray(selectedGroup)) {
                groups = selectedGroup.map(function(group) {
                    if (group.hasOwnProperty('slug')) { return group.slug; } else { return group; }
                });
            } else {
                groups = selectedGroup.hasOwnProperty('slug') ? selectedGroup.slug : selectedGroup;
            }
        } else { groups = null; }

        const data = {
            vendors,
            message_subject: messageSubject,
            message_content: messageContent,
            relationship_managers: relationshipManagers,
            group: groups,
            agreements: selectedAgreements,
            invite
        };

        if (staffingSupplierId) {
            data.parent_id = staffingSupplierId;
        }

        if (addToFavorite === true) {
            data.favorite = true;
        }

        if (userPermissionsService.checkPermission('buyer/onboarding template/all/assign')) {
            data.workflows = workflows;
        }

        if (isBulkInvite) {
            data.invite = true;
            request = $http.post('/api/vendors/bulk_update/', data);
        } else {
            request = $http.post('/api/vendors/', data);
        }
        return request.then(response =>
            asyncTaskAddVendors.registerAsyncTask(response)
            .then(function() {
                if (showAlert) {
                    let msg;
                    if (response.data.count === 1) {
                        Api.Vendors.singleByEmail(vendors[0].email).then(
                            ({ data: addedVendors }) => {
                                if (addedVendors[0]) {
                                    const params = {
                                        vendorName: VendorHelpers.getVendorName(addedVendors[0]),
                                        stateUrl: $state.href('app.partner-details.profile', {
                                            slug: addedVendors[0].slug,
                                        })
                                    }
                                    msg = $translate.instant('TOAST_MSG_VENDOR_PARTNER_ADDED_ONE', params);
                                    if (invite === true) {
                                        msg = $translate.instant('TOAST_MSG_VENDOR_PARTNER_INVITED_ONE', params);
                                    }
                                    $rootScope.alertUser(msg, 'alert-success');
                                }
                            },
                        );
                        undefined;
                    } else {
                        msg = $translate.instant("TOAST_MSG_VENDOR_PARTNER_ADDED_MANY", {count: response.data.count});
                        if (invite === true) {
                            msg = $translate.instant("TOAST_MSG_VENDOR_PARTNER_INVITED_MANY", {count: response.data.count});
                        }
                        $rootScope.alertUser(msg, "alert-success");
                        undefined;
                    }
                }

                $rootScope.$broadcast("updateVendors");

                return request;
            }
            // Request has failed
            , function() {
                const msg = $translate.instant("TOAST_MSG_ERROR_GENERIC");
                $rootScope.alertUser(msg, "alert-danger");

                return request;
            })
        );
    };

    this.changeVendorType = function(vendors, search_args, asyncTaskChangeVendorType, selectedVendorsCount) {
        if (search_args == null) { search_args = {}; }
        if (asyncTaskChangeVendorType == null) { asyncTaskChangeVendorType = false; }
        if (selectedVendorsCount == null) { selectedVendorsCount = 1; }
        const modalInstance = $modal.open({
            templateUrl: "/partials/vendor/modals/change-vendor-type-template.html",
            controller: 'changeVendorTypeController as $ctrl',
            resolve: {
                search_args() {
                    return search_args;
                },
                vendors() {
                    return vendors;
                },
                asyncTaskChangeVendorType() {
                    return asyncTaskChangeVendorType;
                },
                selectedVendorsCount() {
                    return selectedVendorsCount;
                }
            }
        });

        return modalInstance.result;
    };

    this.setVendorAvailability = (vendor, availability = null, availableFrom = null) =>
        getPromise($http.patch(`/api/vendors/${vendor.slug}/`, {
            availability,
            available_from: availableFrom
        }
        )
        )
    ;

    this.sendVendorAvailabilityRequest = function(vendors, message, dates) {
        if (dates == null) { dates = []; }
        return getPromise($http.post("/api/vendors/bulk_ask_for_availability/", {
            vendor_slugs: _.map(vendors, v => v.slug || v.private_slug),
            message,
            date_start: (dates[0] ? dates[0] : undefined),
            date_end: (dates[1] ? dates[1] : undefined)
        }));
    };


    this.openVendorAvailabilityRequestModal = function(vendors) {
        const send = this.sendVendorAvailabilityRequest;

        var optional = {
            _minDate: momentTz().toDate(),
            vendors,
            message: '',
            date_start: momentTz().add(1, 'weeks').startOf('week').toDate(),
            date_end: momentTz().add(1, 'weeks').endOf('week').toDate(),
            onMessageChange(value) {
                return optional.message = value;
            },
            onDateRangeChange(value) {
                [optional.date_start, optional.date_end] = value;
            },
        };


        return ConfirmModal.open({
            header: $translate.instant('VENDOR_AVAILABILITY_MODAL_HEADER'),
            contentTemplateUrl: '/shared/vendor/vendor-availability/vendor-availability-modal.template.html',
            cancel: $translate.instant('VENDOR_AVAILABILITY_MODAL_CANCEL'),
            action: $translate.instant('VENDOR_AVAILABILITY_MODAL_SAVE'),
            optional,
            actionPromise: variables => {
                return send(
                    vendors,
                    variables.message,
                    [DateTimeFormatService.formatDate(variables.date_start), DateTimeFormatService.formatDate(variables.date_end)]
                );
            }
        });
    };

    this.assignToGroup = function(group, vendors) {
        let vendor;
        return $http.post(`/api/vendor_groups/${group.slug}/assign_to_group/`,
            {vendors: (((() => {
                const result = [];
                for (vendor of Array.from(vendors)) {                     result.push(vendor.slug);
                }
                return result;
            })()))})
        .then((response) =>
            new asyncTaskTracker()
                .registerAsyncTask(response)
                .then(() => response),
        )
        .then(function(result) {
                let msg;
                let vendor;
                let updatedVendors = result.data;
                const messages = createGroupOrList(group).getMessages();
                if (updatedVendors.length > 1) {
                    msg = $translate.instant(messages.addPartnersModalSuccessPlural, { groupName: group.name });
                } else {
                    msg = $translate.instant(messages.addPartnersModalSuccess, { groupName: group.name });
                }
                $rootScope.alertUser(msg, "alert-success");
                // TODO we should check whether we really need this signal ...
                $rootScope.$broadcast("vendorsAddedToGroup", group, updatedVendors);
                // update vendor group in view without refreshing data
                updatedVendors = ((() => {
                    const result1 = [];
                    for (vendor of Array.from(updatedVendors)) {                         result1.push(vendor.slug);
                    }
                    return result1;
                })());
                return (() => {
                    const result2 = [];
                    for (vendor of Array.from(vendors)) {
                        if (Array.from(updatedVendors).includes(vendor.slug)) {
                            result2.push(vendor.groups.push(group));
                        } else {
                            result2.push(undefined);
                        }
                    }
                    return result2;
                })();
            }
        ,
            function(error) {
                const errorMsg = $translate.instant("TOAST_MSG_VENDOR_PARTNER_ADDED_TO_GROUP_ERROR");
                return $rootScope.alertUser(errorMsg, "alert-danger");
        });
    };

    this.assignToJob = function(project, vendors) {
        vendors = (Array.from(vendors).map((vendor) => vendor.slug));
        // TODO replace underscores with dashes
        return $http.post(`/api/projects/${project.slug}/assign_to_job/`,
            {vendors})
        .then(function() {
            let msg;
            if (vendors.length > 1) {
                if (project.status === 'draft') {
                    msg = $translate.instant("TOAST_MSG_VENDOR_PARTNER_INVITED_TO_JOB_MANY", {jobName: project.name});
                } else {
                    msg = $translate.instant("TOAST_MSG_VENDOR_PARTNER_ADDED_TO_JOB_MANY", {jobName: project.name});
                }
            } else {
                if (project.status === 'draft') {
                    msg = $translate.instant("TOAST_MSG_VENDOR_PARTNER_INVITED_TO_JOB_ONE", {jobName: project.name});
                } else {
                    msg = $translate.instant("TOAST_MSG_VENDOR_PARTNER_ADDED_TO_JOB_ONE", {jobName: project.name});
                }
            }
            $rootScope.alertUser(msg, "alert-success");
            // TODO we have to check whether we really need this signal
            return $rootScope.$broadcast("vendorsAddedToJob", project, vendors);
        }
        ,
            function() {
                const errorMsg = $translate.instant("TOAST_MSG_VENDOR_PARTNER_ADDED_TO_JOB_ERROR");
                return $rootScope.alertUser(errorMsg, "alert-danger");
        });
    };

});
