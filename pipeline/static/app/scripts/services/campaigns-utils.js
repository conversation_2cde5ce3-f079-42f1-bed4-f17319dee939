const service = function ($http, $q, $modal) {
    const getPromise = function (promise) {
        const deferred = $q.defer();
        promise.then(response => deferred.resolve(response.data)).catch(response => deferred.reject(response.data));
        return deferred.promise;
    };

    this.getCampaigns = function (params, cancelerPromise = null) {
        if (params == null) { params = { ordering: '-updated_at' }; }
        return getPromise($http.get("/api/campaigns/", {
            params,
            timeout: ((cancelerPromise != null) ? cancelerPromise : undefined)
        }
        )
        );
    };

    this.getCampaign = id => getPromise($http.get(`/api/campaigns/${id}`));

    this.getCampaignProjects = function (id, params) {
        if (params == null) { params = { ordering: '-updated_at' }; }
        return getPromise($http.get(`/api/campaigns/${id}/projects`,
            { params })
        );
    };

    this.addCampaign = campaign => getPromise($http.post("/api/campaigns/", campaign));

    this.createCampaignModal = function () {
        let modalInstance;
        return modalInstance = $modal.open({
            templateUrl: "/components/projects/campaigns/modals/add-campaign/add-campaign.template.html",
            controller: 'AddCampaignModalController as $ctrl',
            backdrop: "static"
        });
    };

    this.deleteCampaign = campaign => getPromise($http.delete(`/api/campaigns/${campaign.id}`));

    this.updateCampaign = campaign => getPromise($http.patch(`/api/campaigns/${campaign.id}`, campaign));
}

service.$inject = ["$http", "$q", "$modal"]

angular.module("pipeline").service("campaignsUtils", service);
