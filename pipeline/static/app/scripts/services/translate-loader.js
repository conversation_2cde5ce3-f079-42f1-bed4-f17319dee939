'use strict';

const factory = function ($http, $q, enDefinition) {

    const definitions =
        { sitewide_en_US: enDefinition };

    return function (options) {
        const deferred = $q.defer();
        if (definitions[options.key] != null) {
            deferred.resolve(definitions[options.key]);
        } else {
            deferred.reject(options.key);
        }

        return deferred.promise;
    };
}

factory.$inject = ["$http", "$q", "enDefinition"]

angular.module("pipeline").factory('translateLoader', factory);
