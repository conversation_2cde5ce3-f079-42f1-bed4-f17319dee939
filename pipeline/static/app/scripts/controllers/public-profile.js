'use strict';

angular.module("pipeline").controller("PublicProfileCtrl", function($state, $stateParams, $injector, $title, $translate, $filter, $http, _,
    projects, publicPageSetting) {

    this.title = $title;
    this.projects = projects;
    this.pageSettings = publicPageSetting;

    if (!this.pageSettings.header) { this.pageSettings.header = $translate.instant("PUBLIC_PAGE_DEFAULT_HEADER"); }
    if (!this.pageSettings.description) { this.pageSettings.description = $translate.instant("PUBLIC_PAGE_DEFAULT_DESCRIPTION"); }

    this.limitProjectsDisplay;

    this.displayTopPublicPage = () => {
        $('html, body').animate({scrollTop: 0}, 750, 'easeInOutExpo');
        this.isTopPageDisplayed = true;
        return this.limitProjectsDisplay = 3;
    };

    this.showAllProjects = () => {
        this.displayTopPublicPage();
        this.isTopPageDisplayed = false;
        this.limitProjectsDisplay = null;
        return $state.go('app.public-portal', {section: 'projects'});
    };

    this.isLightAccentColor = () => {
        return $filter('getContrastYIQ')(this.pageSettings.accent_color);
    };

    this.displayTopPublicPage();

    if ($stateParams.section === 'projects') {
        this.showAllProjects();
    }

});
