angular.module("pipeline").controller("UserCreateCtrl",
    function($rootScope, $scope, $state, $http, $stateParams, userAuth, inviteLink, loginOptions, $translate, $window, loginService) {
        $scope.loginOptions = loginOptions;
        $scope.enabledGoogleOauth = !!loginOptions.enable_google_oauth_for_buyers;
        $scope.enabledSSO = loginOptions.sso.length > 0;
        $scope.enabledStandardForm = loginOptions.password && loginOptions.password_for_buyers;
        $scope.hasSocialSignUpOptions = $scope.enabledGoogleOauth || $scope.enabledSSO;
        $scope.formTitle = $scope.hasSocialSignUpOptions ? $translate.instant('BUYER_REGISTRATION_FORM_TITLE_WITH_SOCIAL') : $translate.instant('BUYER_REGISTRATION_FORM_TITLE');
        $scope.errors = [];
        $scope.errorsFormatted = {};
        $scope.isSaving = false;

        if ($stateParams.google_auth_error) {
            loginService.alertUser($translate.instant('BUYER_REGISTRATION_FORM_ERROR_GOOGLE_AUTH'), 'alert-danger');
        }

        // we have to use promise object to resolve invalid activation link
        inviteLink.promise.catch(() => $state.go('app.invite-link-not-valid'));

        $scope.user = {
            uid: $stateParams.uid,
            token: $stateParams.token
        };

        $scope.displayErrors = (errors) => {
            const knownFields = ['first_name', 'last_name', 'password1', 'password2'];

            $scope.errors = Object.entries(errors)
                .filter(([id]) => knownFields.includes(id))
                .map(([id, error]) => ({
                    id,
                    section: `.buyer-invitation-form__${id}`,
                    error,
                }));

            if ($scope.errors.length) {
                $scope.errorsFormatted = loginService.displayErrors($scope.errors);
            } else {
                $scope.errorsFormatted = {};
            }
        }

        $scope.isFormValid = () => {
            const errors = {}
            const fieldRequiredMessage = $translate.instant('BUYER_REGISTRATION_FORM_ERROR_FIELD_REQUIRED');

            if (!$scope.user['first_name']) {
                errors['first_name'] = fieldRequiredMessage;
            }
            if (!$scope.user['last_name']) {
                errors['last_name'] = fieldRequiredMessage;
            }
            if (!$scope.user['password1']) {
                errors['password1'] = fieldRequiredMessage;
            }
            if (!$scope.user['password2']) {
                errors['password2'] = fieldRequiredMessage;
            } else if ($scope.user['password1'] !== $scope.user['password2']) {
                errors['password2'] = $translate.instant('BUYER_REGISTRATION_FORM_ERROR_PASSWORD_NOT_MATCH');
            }

            $scope.displayErrors(errors);

            return $scope.errors.length === 0;
        };

        $scope.createAccount = function() {

            if ($scope.isSaving) {
                return;
            }

            if (!$scope.isFormValid()) {
                return;
            }
            $scope.isSaving = true;

            $scope.errors = [];

            return $http.post("/api/users/create_account/", $scope.user).then((data) => {
                userAuth.login(data);
                return $state.go("root", {created: true});
            }).catch(response => {
                const errors = response.data;
                $scope.displayErrors(errors);
            }).finally(() => {
                $scope.isSaving = false;
            });
        };

        $scope.goToGoogleAuthStart = function () {
            $window.location = loginOptions.google_oauth_url + '&invitation=buyer_invitation-' + $stateParams.uid + '.' + $stateParams.token;
        }
});
