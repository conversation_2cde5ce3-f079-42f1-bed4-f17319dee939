'use strict';

import { Modals } from 'shortlist-react';

angular.module("pipeline").controller(
    "VendorListCtrl",
    function (
        $rootScope, $scope, $filter, $http, $modal, $state, $translate,
        _, vendorUtils, projectUtils, relationshipManagersService, asyncTaskTracker,
        inviteLinksService, userPermissionsService, groupsService
    ) {

        const _isStaffingSupplierListState = () => $state.current.name === 'app.partners.shortlist.staffing-suppliers';
        const _defaultStaffingSupplierType = $rootScope.tenantVendorTypes.types.find((i) => i.is_supplier);

        $scope.$state = $state;
        $scope.isPublicGroup = () => $state.includes('app.partners.groups');
        $scope.isArchivedLists = () => $state.includes('app.partners.archived-lists');
        $scope.getText = () => ($scope.isPublicGroup() ? {
            header: 'SHORTLIST_TAB_GROUPS',
            addNewItem: 'SHORTLIST_GROUPS_CTA',
        }: {
            header: 'SHORTLIST_TAB_LISTS',
            addNewItem: 'SHORTLIST_LISTS_CTA',
        });
        $scope.onCreateGroupClick = () => {
            $scope.isPublicGroup()
                ? $state.go('app.group.create')
                : Modals.openModal({
                      type: 'add_list_modal',
                      sharingTemplates: [],
                  })
                  .catch(() => {
                      // do nothing
                  });
        };
        $scope.canCreateGroupOrList = () => {
            if ($scope.isPublicGroup()) {
                return userPermissionsService.checkPermission('buyer/group/all/create');
            }
            return userPermissionsService.checkPermission('buyer/group/all/create private group');
        }

        $scope.viewParams =
            { listingType: 'list' };

        $scope.data = {
            group: null,
            functions: {}
        };

        // this function is placed here to speed up loading shortlist page
        // - we don't need projects list right after rendering the page
        $scope.projects = [];
        const getProjects = () =>
            projectUtils.getSimpleProjectList()
                .then(response => $scope.projects = response.data.results)
            ;
        getProjects();

        $scope.assignToGroup =
            { searchText: '' };
        $scope.assignToJob =
            { searchText: '' };
        $scope.favouriteVendor = vendorUtils.favourite;
        $scope.addVendor = vendorUtils.addVendor;

        $scope.getCopy = (type) => {
            let translation;
            if (type === 'add_vendor_button_label') {
                translation = 'SHORTLIST_ADD_PARTNER_CTA';
                if (_isStaffingSupplierListState()) {
                    translation = 'SHORTLIST_ADD_STAFFING_SUPPLIER_CTA';
                }
            } else if (type === 'header_title') {
                translation = 'SHORTLIST_TAB_FAVORITES';
                if ($state.includes('app.index.all-partners')) {
                    translation = 'USER_NAV_PARTNERS_ALL_PARTNERS';
                } else if (_isStaffingSupplierListState()) {
                    translation = 'USER_NAV_STAFFING_SUPPLIERS';
                } else if ($state.is('app.partners.shortlist.portfolios')) {
                    translation = 'USER_NAV_PARTNERS_PORTFOLIOS';
                }
            }
            return $translate.instant(translation);
        }

        $scope.invite = vendorUtils.invite;

        $scope.canCreateInviteLinks = () => userPermissionsService.checkPermission('buyer/invite link/all/create');
        $scope.canCreateVendors = () => userPermissionsService.checkPermission('buyer/vendor/all/create');
        $scope.canViewInviteLinks = () => userPermissionsService.checkPermission('buyer/invite link/all/list');
        $scope.canInviteAll = () => userPermissionsService.checkPermission('staff/vendor/all/invite');
        $scope.canCreateGroups = () => userPermissionsService.checkPermission('buyer/group/all/create');

        $scope.hasBulkImport = $scope.canCreateVendors() && $rootScope.tenantHasFeature('vendor_bulk_import')

        $scope.flatfileOptions = $rootScope.tenantVendorTypes.types.map((vendorType) => ({
            id: vendorType.name,
            name: vendorType.label,
            context: { template_id: vendorType.name },
        }));

        $scope.flatfileEnabled = $scope.flatfileOptions && $scope.hasBulkImport;

        $scope.addFilter = function (iterable, element) {
            if (iterable.indexOf(element) !== -1) { return; }
            return iterable.push(element);
        };

        $scope.isFilterActive = (field, value) =>
            (_.findIndex($scope.activeFilters, {
                type: field,
                label: value
            }
            )) !== -1
            ;

        $scope.addNewTag = tag => $scope.vendorFilters.text.push(tag.label);

        $scope.getSelectedVendors = () =>
            $filter('filter')($scope.vendors,
                { checked: true })
            ;

        $scope.doAssignToJob = function (project) {
            $scope.assignToJob.searchText = '';
            vendorUtils.assignToJob(project, $scope.getSelectedVendors());
            // unselect all vendors
            return Array.from($scope.vendors).map((vendor) =>
                (vendor.checked = false));
        };

        $scope.doAssignToGroup = function (group) {
            $scope.assignToGroup.searchText = '';
            vendorUtils.assignToGroup(group, $scope.getSelectedVendors());
            // unselect all vendors
            return Array.from($scope.vendors).map((vendor) =>
                (vendor.checked = false));
        };

        $scope.addVendorFilter = function (field, by_) {
            if (field === 'status') {
                for (let filter in $scope.vendorFilterOptions.status) {
                    const label = $scope.vendorFilterOptions.status[filter];
                    $scope.removeVendorFilter('status', filter);
                }
            }
            return $scope.vendorFilters[field].push(by_);
        };

        $scope.removeVendorFilter = (field, by_) => $scope.vendorFilters[field] = _.without($scope.vendorFilters[field], by_);

        $scope.sortVendors = function (by_) {
            if ($scope.vendorsSort.by === by_) { $scope.vendorsSort.reverse = !$scope.vendorsSort.reverse; }
            $scope.vendorsSort.by = by_;
            return $scope.vendorsSort.name = $scope.vendorsSortOptions[by_];
        };

        $scope.createInviteLink = function ($event) {
            $($event.currentTarget).blur();
            inviteLinksService.openEditInviteLinkModal(
                {
                    vendor_type: _isStaffingSupplierListState() ? _defaultStaffingSupplierType.name : null
                },
                () => $translate('INVITE_LINKS_LIST_ITEM_ADDED').then(value => $rootScope.alertUser(value, "alert-success")),
                false,
            );
        };

        $scope.addGroup = () => $state.go('app.group.create');

        $scope.inviteAll = () =>
            $http.post("/api/vendors/invite_all/")
                .then(function (response) {
                    const asyncTask = new asyncTaskTracker();
                    return asyncTask.registerAsyncTask(response)
                        .then(() => $state.reload());
                })
            ;

        $scope.addToProject = function (vendors) {
            if (vendors == null) { vendors = false; }
            const modalInstance = $modal.open({
                templateUrl: "/partials/vendor/modals/add-to-project.html",
                size: "lg",
                controller: 'VendorAddToProject',
                resolve: {
                    vendors() { return vendors || (_.filter($scope.vendors, 'checked', true)); },
                    public_vendors() { return []; }
                },

                backdrop: "static"
            });

            return modalInstance.result.then(function (data) {
                projectUtils.sendAddedToProjectToastMessage(data);
                $scope.unselectPartners();
                return getProjects();
            });
        };

        $scope.unselectPartners = function () {
            if ($scope.vendors) {
                return Array.from($scope.vendors).map((vendor) => (vendor.checked = false));
            }
        };

        $scope.removeAddedGroup = function (item) {
            const searchResult = $filter('filter')($scope.vendorFilters.group,
                { 'slug': item.slug });
            if (searchResult != null) { return !searchResult.length; } else { return false; }
        };

        $scope.editGroup = function (group) {
            const modalInstance = $modal.open({
                templateUrl: "/partials/vendor-group/modals/edit.html",
                controller: "VendorGroupEditCtrl",
                resolve: {
                    group() {
                        return group;
                    }
                },
                backdrop: "static"
            });

            return modalInstance.result.then(function (data) {
                $state.go('app.partners.group-details',
                    { slug: data.slug }
                    ,
                    { reload: true });
                $scope.data.group.name = data.name;
                $scope.data.group.description = data.description;
                return $rootScope.$emit('groupChanged');
            });
        };

        return $scope.deleteGroup = group =>
            groupsService.openDeleteGroupModal(group).then(() => $scope.data.group = null)
            ;
    });
