angular.module("pipeline").controller("ProjectCompleteCtrl",
    function($scope, $rootScope, $http, $filter, $translate, $modalInstance, project, projectUtils) {

        const successMessage = $translate.instant("PROJECT_COMPLETE_MODAL_ALERT" , {name: project.name});
        $scope.project = project;

        $scope.canAskForFeedback = () =>
            (project.awarded_at !== null) && ((project.team.length > 1) ||
            ((project.team.length === 1) && (_.filter(project.team, {slug: $rootScope.user.slug}).length === 0)))
        ;

        $scope.dismiss = () => $modalInstance.dismiss("cancel");

        $scope.confirm = () =>
            projectUtils.complete(project.slug, $scope.feedbackRequest.ask).success(function(data) {
                $rootScope.alertUser(successMessage, "alert-success");
                return $modalInstance.close(data);
            }).error(data => $rootScope.alertUser(data, "alert-danger"))
        ;

        return $scope.feedbackRequest = {ask: $scope.canAskForFeedback()};
});
