'use strict';

angular.module("pipeline").controller("ProjectDeleteCtrl",
    function($rootScope, $scope, $filter, $http, $translate, $modalInstance, project) {
        $scope.project = project;
        $scope.dismiss = () => $modalInstance.dismiss("cancel");

        $scope.projectDeleteMessage = $translate.instant("PROJECT_DELETE_MODAL_ALERT", {name: $scope.project.name});

        return $scope.delete = () =>
            $http.delete(`/api/projects/${$scope.project.slug}/`)
            .success(function() {
                $rootScope.alertUser($scope.projectDeleteMessage, "alert-success");
                $rootScope.$broadcast("projectDeleted");
                return $modalInstance.close();}).error(data => $rootScope.alertUser(data, "alert-danger"))
        ;
});
