'use strict';

angular.module("pipeline").controller('ProjectsListCtrl',
    function($rootScope, $scope, $http, $filter, $cookieStore, $modal, $translate, $state, $q, $timeout,
     projectUtils, listingUtils, campaignsUtils, isUser) {

        let projectRemovedOnDestroy;
        $scope.$on('$destroy', () => projectRemovedOnDestroy());

        $rootScope.hideNavigation = false;

        $scope.listingType = listingUtils.getListingType() || 'grid';
        $scope.showSearch = false;
        $scope.projects = [];
        $scope.addProject = projectUtils.addProject;
        $scope.createCampaignModal = campaignsUtils.createCampaignModal;

        $scope.data = { apiPath: isUser ? '/api/projects/' : '/api/v/projects/' };

        $scope.setListingType = function(value) {
            listingUtils.setListingType(value);
            return $scope.listingType = value;
        };

        // TODO search should be external module ( it's similar to vendor search )
        const cancelers = [];
        $scope.coreUpdateResults = function(page) {
            // Cancel running searches
            let canceler;
            if (page == null) { page = $scope.search.currentPage; }
            if (($scope.search.isLoading === true) && cancelers.length) {
                for (canceler of Array.from(cancelers)) { canceler.resolve(); }
            }
            canceler = $q.defer();
            cancelers.push(canceler);

            const searchProjects = function() {
                const params = {};
                params.page = page;
                params.page_size = 12;
                params.ordering = `${$scope.search.projectsSort.by}`;
                params.search = $scope.search.projectFilters.text;
                params.created_by = $scope.search.projectFilters.created_by;
                params.no_campaign = '';

                if (isUser) {
                    params.status = $scope.search.projectFilters.status;
                } else {
                    params.project_status = $scope.search.projectFilters.status;
                }

                if ((($scope.data.filters != null ? $scope.data.filters.noCampaigns : undefined) === true) && ($scope.search.projectFilters.text.length === 0)) {
                    params.no_campaign = 1;
                }
                return $http.get($scope.data.apiPath, {
                    timeout: canceler.promise,
                    params
                }
                );
            };

            const searchCallback = function(response) {
                if (page === 1) {
                    if (response.data.results != null) {
                        $scope.projects = response.data.results;
                    } else {
                        $scope.projects = response.data;
                    }
                } else {
                    if (response.data.results != null) {
                        $scope.projects = $scope.projects.concat(response.data.results);
                    } else {
                        $scope.projects = $scope.projects.concat(response.data);
                    }
                }
                $scope.search.hasMoreResults = response.data.next;
                $scope.search.currentPage++;
                return $scope.search.isLoading = false;
            };

            $scope.search.isLoading = true;

            if (($scope.data.filters != null ? $scope.data.filters.noCampaigns : undefined) === true) {
                return searchProjects().then(searchCallback);
            } else {
                return searchProjects().then(response => searchCallback(response));
            }
        };
        $scope.updateResults = _.debounce($scope.coreUpdateResults, 350);

        $scope.$on('$destroy', () => campaignRemovalOnDestroy());
        var campaignRemovalOnDestroy = $rootScope.$on("campaignRemoval", function() {

            let canceler;
            const page = $scope.search.currentPage;

            // Cancel running searches
            if (($scope.search.isLoading === true) && cancelers.length) {
                for (canceler of Array.from(cancelers)) { canceler.resolve(); }
            }
            canceler = $q.defer();
            cancelers.push(canceler);

            const searchProjects = function() {
                const params = {};
                params.page = page;
                params.page_size = 12;
                params.ordering = `${$scope.search.projectsSort.by}`;
                params.search = $scope.search.projectFilters.text;
                params.project_status = $scope.search.projectFilters.status;
                params.created_by = $scope.search.projectFilters.created_by;
                params.no_campaign = '';

                if (isUser) {
                    params.status = $scope.search.projectFilters.status;
                } else {
                    params.project_status = $scope.search.projectFilters.status;
                }

                if (($scope.data.filters != null ? $scope.data.filters.noCampaigns : undefined) === true) {
                    params.no_campaign = 1;
                }
                return $http.get($scope.data.apiPath, {
                    timeout: canceler.promise,
                    params
                }
                );
            };

            if (($scope.data.filters != null ? $scope.data.filters.noCampaigns : undefined) === true) {
                return searchProjects()
                .then(searchCallback);
            } else {
                return searchProjects().then(response => searchCallback(response));
            }
        });

        // this simple object only implements basics of sorting options,
        // and you have to implement additionally
        // - $scope.statusOptions
        // - $scope.search.projectsSort
        // - $scope.search.projectsSortOptions
        // - $scope.search.projectFilters
        // parameters in child controllers to have full searching functionality
        $scope.search = {
            hasMoreResults: false,
            isLoading: true,
            currentPage: 1,
            hasAnyResults: true
        };

        $scope.loadMoreResults = () => $scope.updateResults($scope.search.currentPage);

        $scope.resetResults = function() {
            $scope.search.currentPage = 1;
            $scope.search.hasMoreResults = false;
            $scope.search.hasAnyResults = true;
            return $scope.projects = [];
        };

        let searchTimeoutPromise = null;
        $scope.$watch('search.input', function(newValue) {
            if (newValue != null) {

                // Cancel running timeouts
                if (searchTimeoutPromise != null) { $timeout.cancel(searchTimeoutPromise); }

                // Delay search
                return searchTimeoutPromise = $timeout(function() {
                    if (newValue !== $scope.search.projectFilters.text) {
                        return $scope.search.projectFilters.text = newValue;
                    }
                }
                , 500);
            }
        });

        // it overwrites method $scope.data.setFilter from file list-user.js
        $scope.$watchCollection('search.projectFilters', function(newValue, oldValue) {
            if ((newValue !== oldValue) && (oldValue != null)) {
                $scope.resetResults();
                return $scope.updateResults();
            }
        });

        $scope.$watchCollection('search.projectsSort', function(newValue, oldValue) {
            // refresh query results only if filters has been already defined
            if ((newValue !== oldValue) && (oldValue != null)) {
                $scope.resetResults();
                return $scope.updateResults();
            }
        });

        // this was taken from old part of code, and should be changed asap
        // TODO refactor it
        $scope.sortProjects = function(by_) {
            if ($scope.search.projectsSort.by === by_) { $scope.search.projectsSort.reverse = !$scope.search.projectsSort.reverse; }
            $scope.search.projectsSort.by = by_;
            return $scope.search.projectsSort.name = $scope.search.projectsSortOptions[by_];
        };

        $scope.resetSearch = function() {
            $scope.search.projectFilters.text = '';
            return $scope.search.input = '';
        };

        return projectRemovedOnDestroy = $rootScope.$on('projectRemoved', function() {
            $scope.resetResults();
            return $scope.updateResults();
        });
});
