'use strict';

angular.module("pipeline").controller("ProjectAwardCtrl",
    function($scope, $rootScope, $http, $translate, $modalInstance, $q, _, vendor, project, forms, vendorProposal, projectUtils) {

        $scope.vendor = vendor;
        $scope.project = project;
        $scope.forms = forms.data[0];
        $scope.showProposalFee = false;
        $scope.totalProposalFee = vendorProposal.proposal_amount;

        $scope.projectAwardMessage = $translate.instant("PROJECT_AWARD_MODAL_ALERT", {name: $scope.project.name});
        $scope.projectAwardCopy = $translate.instant("PROJECT_AWARD_MODAL_COPY", {name: $scope.vendor.name});

        $scope.dismiss = () => $modalInstance.dismiss("cancel");

        return $scope.award = function(closeProject) {

            if (closeProject == null) { closeProject = false; }
            return $http.post(`/api/projects/${$scope.project.slug}/award/`, {
                vendor: $scope.vendor.id,
                proposal_amount: (($scope.totalProposalFee != null) && ($scope.totalProposalFee > 0) ? $scope.totalProposalFee : undefined)
            }).then(function(res) {
                if (closeProject === true) {
                    return projectUtils.close($scope.project.slug).then(() => res);
                } else {
                    return $q.when(res);
                }}).then(function(res) {
                $rootScope.alertUser($scope.projectAwardMessage, "alert-success");
                return $modalInstance.close(res.data);}).catch(function(res) {
                $rootScope.alertUser(res.data, "alert-danger");
                return $scope.dismiss();
            });
        };
});
