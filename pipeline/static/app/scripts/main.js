import './filepicker.js';
import './request-animation-frame';
import './underscore';
import './env-detection';
import './translation-keys';
import './app';
import './app-deferred-bootstrap';
import './directives';

import './moment-config';
import './config';

import './constants.js';
import './constants/translation-module';
import './constants/en-translation';

import './providers/user-auth';

// import React
import '../react/react';

// module imports
import '../shared/user-permissions/user-permissions.directive';
import '../shared/user-permissions/user-permissions.service';
import '../shared/user-permissions/user-permissions.vendor.service';
import '../shared/search-not-found/search-not-found.component';
import '../shared/preview-overlay/preview-overlay.component';
import '../shared/confirmation-modal/confirmation-modal.component';
import '../shared/dropdown-multiselect/dropdown-multiselect.component';
import '../shared/dropdown-multiselect/dropdown-multiselect.controller';
import '../shared/search-keyword/search-keyword.component';

import './other';
import './services/';
import './controllers/';
import '../components/insights';
import '../components/partner-profile';
import '../components/documents';
import '../components/integrations';
import '../components/invite-links';

import '../components/custom-field-templates';
import '../components/vendors/custom-vendor-fields';
import '../components/external-sign';
import '../components/tasks';
import '../components/onboarding-workflow';
import '../components/marketplace';
import '../components/groups';
import '../components/idle';
import '../components/message-board';
import '../components/timesheets';
import '../shared/search';
import '../components/settings-custom-rates';
import '../components/shared';
import '../components/notifications';
import '../components/project-proposal';
import '../components/projects';
import '../components/payments';
import '../components/payruns';
import '../components/request-feedback';
import '../components/approvals';
import '../components/partner-feedback';
import '../components/portfolio';
import '../components/onboarding';
import '../components/time-tracking';
import '../components/partners';
import '../components/user-templates';
import '../components/team-members';
import '../components/relationship-managers';
import '../components/messaging';
import '../components/top-header/top-header.directive';
import '../components/vendor-configuration';
import '../components/teams';
import '../components/dashboards';
import '../components/partner-side';
import '../components/contracts';
import '../components/expenses';
import './filters';
import './factories';

import GoogleMapsLoaderService from './services/google-maps-loader.service';
import requestStoreService from './services/request-store';
import GlobalErrorHandler from './services/global-error-handler.service';
import { SearchService, SearchUtilsService } from './services/search.service';
import FaviconService from '../scripts/services/favicon';
import colorUtilsService from '../scripts/services/color-utils';
import unsavedChangesModal from '../scripts/services/unsaved-changes';
import sendBulkMessageController from './controllers/vendor/send-bulk-message';
import AddRankController from "./controllers/vendor/add-rank";
import changeVendorTypeController from './controllers/vendor/change-vendor-type';
import addToTaskController from './controllers/vendor/add-to-task';
import taskService from '../components/tasks/tasks.service';
import DateUtilsService from './services/date-utils.service';
import LoginService from '../components/login/login.service';
import { PaycassoTrackerService } from '../components/integrations/paycasso.classes';
import { VeriffService } from '../components/integrations/veriff/veriff.classes';
import { CustomInsightsService } from './services/custom-insights.service';

import app from '../components/app/app.component';
import fileUploadCover from '../shared/file-upload-cover/file-upload-cover.component';
import fileUploadItem from '../shared/file-upload-item/file-upload-item.component';
import inputAvailabilityCalendar from '../shared/input-availability-calendar/input-availability-calendar.component';
import inputTwinAvailabilityCalendar from '../shared/input-twin-availability-calendar/input-twin-availability-calendar.component';
import inputText from '../shared/input-text/input-text.component';
import inputTextarea from '../shared/input-textarea/input-textarea.component';
import inputSwitch from '../shared/input-switch/input-switch.component';
import inputGroups from '../shared/input-groups/input-groups.component';
import inputLabel from '../shared/input-label/input-label.component';
import inputLocationDistance from '../shared/input-location-distance/input-location-distance.component';
import inputPassword from '../shared/input-password/input-password.component';
import googleMap from '../shared/google-map/google-map.component';
import inputPasswordService from '../shared/input-password/input-password.service';
import contextMenuTrigger from '../shared/context-menu-trigger/context-menu-trigger.directive';
import inputNumericMask from '../shared/input-numeric-mask/input-numeric-mask.directive';
import imageLoadedBroadcast from '../shared/image-loaded-broadcast/image-loaded-broadcast.directive';
import itemSelectorToolbarDropdown from '../components/relationship-managers/components/item-selector-toolbar-dropdown/item-selector-toolbar-dropdown.directive';
import setLayoutClasses from '../shared/set-layout-classes/set-layout-classes.directive';
import topActionBar from '../shared/top-action-bar/top-action-bar.directive';
import userCustomFields from '../shared/user-custom-fields/user-custom-fields.component';
import helpTooltip from '../shared/help-tooltip/help-tooltip.component';
import formCard from '../shared/form-card/form-card.component';
import searchTeamMembers from '../shared/search-team-members/search-team-members.component';
import copyButton from '../shared/copy-button/copy-button.component';
import floatingActionBar from '../shared/floating-action-bar/floating-action-bar.directive';
import columnsManagementHelper from '../shared/search/components/search/dynamic-table/columns-management-helper.directive.js'
import checkPasswordStrength from '../shared/check-password-strength/check-password-strength.directive';
import pageTabNav from '../shared/page-tab-nav/page-tab-nav.component';
import notificationInfoBox from '../shared/notification-info-box/notification-info-box.component';
import addPartners from '../shared/add-partners/add-partners.component';
import errorList from '../shared/error-list/error-list.component';
import searchLocation from '../shared/search-location/search-location.component';
import actionsButton from '../shared/actions-button/actions-button.component';
import customDropdown from '../shared/custom-dropdown/custom-dropdown.component';
import selectVendorType from '../shared/select-vendor-type/select-vendor-type.component';
import avatar from '../shared/avatar/avatar.component';
import status from '../shared/status/status.component';
import audioFile from '../shared/audio-file/audio-file.component';
import workingHours from '../shared/working-hours/working-hours.component';
import longTimeRequestMessage from '../shared/long-time-request-mesage/long-time-request-mesage.component';
import itemManagers from '../shared/item-managers/item-managers.component';

import vendorExpiredDocumentNotification from '../components/vendors/components/vendor-expired-document-notification/vendor-expired-document-notification.component';
import vendorNoteEdit from '../components/vendors/components/vendor-note-edit/vendor-note-edit.component';
import vendorTagsEdit from '../components/vendors/components/vendor-tags-edit/vendor-tags-edit.component';
import vendorProfileLink from '../components/vendors/components/vendor-profile-link/vendor-profile-link.component';
import vendorStatusIcon from '../components/vendors/components/vendor-status-icon/vendor-status-icon.component';
import vendorType from '../components/vendors/components/vendor-type/vendor-type.component';

import vendorProfileState from '../components/vendors/states/vendor-profile-state/vendor-profile-state.component';
import vendorMoreInfoState from '../components/vendors/states/vendor-more-info-state/vendor-more-info-state.component';
import vendorEmailConfirmationState from '../components/vendors/states/vendor-email-confirmation-state/vendor-email-confirmation-state.component';
import ArchiveVendorService from '../components/vendors/services/archive-vendor.service';
import VendorsService from '../components/vendors/services/vendors.service';

import tenantService from '../scripts/services/tenant.service';
import CustomEventsService from '../scripts/services/custom-events.service';

import projectEditor from '../components/projects/project-editor/project-editor.component';
import projectEditorDescription from '../components/projects/project-editor/project-editor-description/project-editor-description.component';
import projectEditorFileUpload from '../components/projects/project-editor/project-editor-file-upload/project-editor-file-upload.component';
import projectEditorSkills from '../components/projects/project-editor/project-editor-skills/project-editor-skills.component';
import projectEditorLocation from '../components/projects/project-editor/project-editor-location/project-editor-location.component';
import projectEditorButtons from '../components/projects/project-editor/project-editor-buttons/project-editor-buttons.component';
import projectEditorCustomMessage from '../components/projects/project-editor/project-editor-custom-message/project-editor-custom-message.component';
import projectEditorQuestionnaire from '../components/projects/project-editor/project-editor-questionnaire/project-editor-questionnaire.component';
import projectEditorBudgetAssignment from '../components/projects/project-editor/project-editor-budget-assignment/project-editor-budget-assignment.component';
import projectEditorBudgetRfp from '../components/projects/project-editor/project-editor-budget-rfp/project-editor-budget-rfp.component';
import projectEditorBudgetWidget from '../components/projects/project-editor/project-editor-budget-widget/project-editor-budget-widget.component';
import projectEditorHourlyRates from '../components/projects/project-editor/project-editor-hourly-rates/project-editor-hourly-rates.component';
import projectEditorState from '../components/projects/project-editor/project-editor-state/project-editor-state.component';
import projectEditorDatesAssignment from '../components/projects/project-editor/project-editor-dates-assignment/project-editor-dates-assignment.component';
import projectEditorDatesRfi from '../components/projects/project-editor/project-editor-dates-rfi/project-editor-dates-rfi.component';
import projectEditorDatesRfp from '../components/projects/project-editor/project-editor-dates-rfp/project-editor-dates-rfp.component';
import projectName from '../components/projects/project-name/project-name.component';
import projectStatus from '../components/projects/project-status/project-status.component';
import assignToTask from '../components/tasks/assign-to-task/assign-to-task.component';
import proposalNoteEdit from '../components/projects/proposal/proposal-note-edit/proposal-note-edit.component';

import addGroupsModalController from '../components/projects/modals/add-groups-modal';

import portfolioProjectTile from '../components/portfolio/components/portfolio-project-tile/portfolio-project-tile.components.js';

import partnersAdd from '../components/partners/components/partners-add/partners-add.component.js';
import notesComponent from '../components/partners/notes/notes.component';
import modalSendAgreementsController from '../components/partners/modal-send-agreements/modal-send-agreements.controller';
import unsavedChangesModalController from '../shared/modal-unsaved-changes/modal-unsaved-changes.controller';
import vendorFavoriteService from '../scripts/services/vendor-favorite';
import favoriteIcon from '../shared/favorite-icon/favorite-icon.component';
import pdfPreview from '../shared/pdf-preview/pdf-preview.component';
import AskForFeedback from '../shared/ask-for-feedback/ask-for-feedback.component';

import vendorAvailability from '../shared/vendor/vendor-availability/vendor-availability.component';
import vendorAvailabilityCalendar from '../shared/vendor/vendor-availability-calendar/vendor-availability-calendar.component';
import vendorAvailabilityInput from '../shared/vendor/vendor-availability-input/vendor-availability-input.component';
import vendorListItem from '../shared/vendor/vendor-list-item/vendor-list-item.component';
import vendorLocation from '../shared/vendor/vendor-location/vendor-location.component';
import vendorBankDetailsStatus from '../shared/vendor/vendor-bank-details-status/vendor-bank-details-status.component';
import userLocation from '../shared/user-location/user-location.component';
import vendorNotesService from '../components/partners/notes/notes.service';

import publicTopHeader from '../shared/public-top-header/public-top-header.component';

import notificationsConfigSettingsController from '../scripts/controllers/settings/notifications-config';

import vendorInviteLink from '../components/invite-links/components/vendor-invite-link/vendor-invite-link.component';
import confirmationCode from '../components/invite-links/components/confirmation-code/confirmation-code.component';
import vendorInviteLinkState from '../components/invite-links/states/vendor-invite-link-state/vendor-invite-link-state.component';
import AvailabilityService from './services/availability.service';
import vendorProposalCompare from './controllers/vendor/proposal-comparison';
import sortButtons from '../shared/sort-buttons/sort-buttons.component';

import LineItemService from '../components/payments/line-item.service';

import AppRun from '../scripts/app-run';
angular.module('pipeline')
    .run(AppRun)
    .service('customInsightsService', CustomInsightsService)
    .service('GoogleMapsLoaderService', GoogleMapsLoaderService)
    .service('requestStoreService', requestStoreService)
    .service('GlobalErrorHandler', GlobalErrorHandler)
    .service('FaviconService', FaviconService)
    .service('unsavedChangesModal', unsavedChangesModal)
    .service('tenantService', tenantService)
    .service('CustomEventsService', CustomEventsService)
    .service('inputPasswordService', inputPasswordService)
    .service('colorUtilsService', colorUtilsService)
    .service('vendorFavoriteService', vendorFavoriteService)
    .service('taskService', taskService)
    .service('SearchService', SearchService)
    .service('SearchUtilsService', SearchUtilsService)
    .service('AvailabilityService', AvailabilityService)
    .service('ArchiveVendorService', ArchiveVendorService)
    .service('VendorsService', VendorsService)
    .service('loginService', LoginService)
    .service('DateUtilsService', DateUtilsService)
    .service('paycassoTrackerService', PaycassoTrackerService)
    .service('veriffService', VeriffService)
    .component('app', app)
    .component('fileUploadCover', fileUploadCover)
    .component('fileUploadItem', fileUploadItem)
    .component('inputAvailabilityCalendar', inputAvailabilityCalendar)
    .component('inputTwinAvailabilityCalendar', inputTwinAvailabilityCalendar)
    .component('inputText', inputText)
    .component('inputTextarea', inputTextarea)
    .component('inputSwitch', inputSwitch)
    .component('inputLabel', inputLabel)
    .component('inputLocationDistance', inputLocationDistance)
    .component('inputPassword', inputPassword)
    .component('googleMap', googleMap)
    .directive('contextMenuTrigger', contextMenuTrigger)
    .directive('inputNumericMask', inputNumericMask)
    .directive('imageLoadedBroadcast', imageLoadedBroadcast)
    .directive('itemSelectorToolbarDropdown', itemSelectorToolbarDropdown)
    .directive('setLayoutClasses', setLayoutClasses)
    .component('inputGroups', inputGroups)
    .component('helpTooltip', helpTooltip)
    .component('notificationInfoBox', notificationInfoBox)
    .component('formCard', formCard)
    .component('vendorExpiredDocumentNotification', vendorExpiredDocumentNotification)
    .directive('floatingActionBar', floatingActionBar)
    .directive('columnsManagementHelper', columnsManagementHelper)
    .directive('checkPasswordStrength', checkPasswordStrength)
    .directive('topActionBar', topActionBar)
    .component('userCustomFields', userCustomFields)
    .component('vendorTagsEdit', vendorTagsEdit)
    .component('vendorNoteEdit', vendorNoteEdit)
    .component('vendorProfileLink', vendorProfileLink)
    .component('vendorStatusIcon', vendorStatusIcon)
    .component('vendorType', vendorType)
    .component('vendorProfileState', vendorProfileState)
    .component('vendorMoreInfoState', vendorMoreInfoState)
    .component('vendorEmailConfirmationState', vendorEmailConfirmationState)
    .component('searchTeamMembers', searchTeamMembers)
    .component('copyButton', copyButton)
    .component('pageTabNav', pageTabNav)
    .component('addPartners', addPartners)
    .component('publicTopHeader', publicTopHeader)
    .component('vendorListItem', vendorListItem)
    .component('vendorLocation', vendorLocation)
    .component('userLocation', userLocation)
    .component('errorList', errorList)
    .component('searchLocation', searchLocation)
    .component('actionsButton', actionsButton)
    .component('customDropdown', customDropdown)
    .component('selectVendorType', selectVendorType)
    .component('avatar', avatar)
    .component('status', status)
    .component('favoriteIcon', favoriteIcon)
    .component('audioFile', audioFile)
    .component('workingHours', workingHours)
    .component('longTimeRequestMessage', longTimeRequestMessage)
    .component('itemManagers', itemManagers)
    .component('pdfPreview', pdfPreview)
    .component('askForFeedback', AskForFeedback)
    .component('sortButtons', sortButtons)
    .controller('unsavedChangesModalController', unsavedChangesModalController)
    .controller('sendBulkMessageController', sendBulkMessageController)
    .controller('AddRankController', AddRankController)
    .controller('changeVendorTypeController', changeVendorTypeController)
    .controller('addToTaskController', addToTaskController)
    .controller('notificationsConfigSettingsCtrl', notificationsConfigSettingsController)
    .controller('vendorProposalCompareCtrl', vendorProposalCompare);

angular.module('pipeline.projects')
    .component('projectEditor', projectEditor)
    .component('projectEditorDescription', projectEditorDescription)
    .component('projectEditorFileUpload', projectEditorFileUpload)
    .component('projectEditorSkills', projectEditorSkills)
    .component('projectEditorLocation', projectEditorLocation)
    .component('projectEditorButtons', projectEditorButtons)
    .component('projectEditorCustomMessage', projectEditorCustomMessage)
    .component('projectEditorQuestionnaire', projectEditorQuestionnaire)
    .component('projectEditorBudgetAssignment', projectEditorBudgetAssignment)
    .component('projectEditorBudgetRfp', projectEditorBudgetRfp)
    .component('projectEditorBudgetWidget', projectEditorBudgetWidget)
    .component('projectEditorHourlyRates', projectEditorHourlyRates)
    .component('projectEditorState', projectEditorState)
    .component('projectEditorDatesAssignment', projectEditorDatesAssignment)
    .component('projectEditorDatesRfi', projectEditorDatesRfi)
    .component('projectEditorDatesRfp', projectEditorDatesRfp)
    .component('projectName', projectName)
    .component('projectStatus', projectStatus)
    .component('assignToTask', assignToTask)
    .component('proposalNoteEdit', proposalNoteEdit)
    .component('vendorAvailability', vendorAvailability)
    .component('vendorAvailabilityCalendar', vendorAvailabilityCalendar)
    .component('vendorAvailabilityInput', vendorAvailabilityInput)
    .controller('addGroupsModalController', addGroupsModalController);

angular.module('pipeline.payments')
    .service('LineItemService', LineItemService);

angular.module('pipeline.portfolio')
    .component('portfolioProjectTile', portfolioProjectTile);

angular.module('pipeline.partners')
    .component('partnersAdd', partnersAdd)
    .component('notes', notesComponent)
    .component('vendorBankDetailsStatus', vendorBankDetailsStatus)
    .controller('modalSendAgreementsController', modalSendAgreementsController)
    .service('vendorNotesService', vendorNotesService)

angular.module('pipeline.invite-links')
    .component('vendorInviteLinkState', vendorInviteLinkState)
    .component('confirmationCode', confirmationCode)
    .component('vendorInviteLink', vendorInviteLink);

import '../components/login';
