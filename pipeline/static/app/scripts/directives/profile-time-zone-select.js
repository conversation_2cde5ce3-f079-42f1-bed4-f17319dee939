'use strict';

angular.module("pipeline").directive('profileTimeZoneSelect', $translate =>
    ({
        restrict: 'E',
        scope: {
            tenant: "=",
            autoSave: '=?',
            isDisabled: '=',
        },
        controller($scope, $rootScope, $http, requestStoreService) {

            const successMessage = $translate.instant("TOAST_MSG_PROFILE_TIMEZONE_UPDATED");
            if (!$scope.autoSave) { $scope.autoSave = false; }

            requestStoreService.getEndpoint('timezones')
            .then(response => $scope.timezones = response.timezones);

            $scope.timezoneChanged = function() {
                if ($scope.autoSave) { return $scope.setTimezone(); }
            };

            return $scope.setTimezone = function() {
                $scope.isSaving = true;
                return $http.patch("/api/tenant/",
                    {timezone: $scope.tenant.timezone})
                .success(function(data) {
                    $rootScope.tenant = data;
                    return $rootScope.alertUser(successMessage, "alert-success");}).error(data => $scope.errors = data).finally(() => $scope.isSaving = false);
            };
        },

        templateUrl: "/partials/vendor/details/_time_zone_select.html"
    })
);
