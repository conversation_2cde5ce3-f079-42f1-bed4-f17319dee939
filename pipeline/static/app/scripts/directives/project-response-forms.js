'use strict';

pipelineApp
.directive('responseFormItems', ($rootScope, $document) =>
    ({
        link($scope, element, attrs) {
            const onClick = function(ev) {
                const target = $(ev.target);
                if ((target.parents('.form-item').length === 0) && (target.parents('.new-form-item').length === 0) && !target.is('.form-item') && !target.is('.new-form-item')) {
                    $rootScope.$broadcast('responseFormSaveModelState');
                    $rootScope.$broadcast('responseFormUpdateFormStructure');
                    return $scope.$apply();
                }
            };

            $scope.$on('$destroy', () => $document.off('click', onClick));

            return $document.on('click', onClick);
        }
    })
).directive('responseFormItemUser', ($rootScope, $timeout, _, projectResponseFormEditService) =>
    ({
        link($scope, element, attrs) {
            let responseFormFocusItemOnDestroy;
            const formEditService = new projectResponseFormEditService();

            const inputLabel = element.find('.item-label');

            $scope.savedModelState = {
                templateOptions: {
                    label: null,
                    placeholder: null
                },
                mandatory: null,
                allow_multiple: null,
                multi_line: null,
                only_integers: null,
                fields: [],
                answers: [],
                freeform_answer: {}
            };

            const isAnythingInEditMode = function() {
                const items = _.filter($scope.form.formFields, item => item.inEditMode === true);
                return items.length > 0;
            };


            // Clear errors (i.e. before saving)
            const clearItemErrors = () => delete $scope.errors[$scope.item.key];

            const clearMultipleState = function() {
                if ($scope.item.type === 'multiple') {
                    return $scope.item.answers = _.filter($scope.item.answers, item => item.label !== '');
                }
            };

            // Save a current model state. Used for Discard or
            // checking whether an item has been saved before
            const saveModelState = function() {
                if ($scope.item.mandatory !== $scope.savedModelState.mandatory) {
                    $scope.savedModelState.mandatory = $scope.item.mandatory;
                }
                if (!angular.equals($scope.item.templateOptions, $scope.savedModelState.templateOptions)) {
                    angular.copy($scope.item.templateOptions, $scope.savedModelState.templateOptions);
                }
                if (($scope.item.type === 'textarea') && ($scope.item.multi_line !== $scope.savedModelState.multi_line)) {
                    $scope.savedModelState.multi_line = $scope.item.multi_line;
                }
                if (($scope.item.type === 'numeric') && ($scope.item.only_integers !== $scope.savedModelState.only_integers)) {
                    $scope.savedModelState.only_integers = $scope.item.only_integers;
                }
                if (($scope.item.type === 'fee') && !angular.equals($scope.item.fields, $scope.savedModelState.fields)) {
                    angular.copy($scope.item.fields, $scope.savedModelState.fields);
                }
                if ($scope.item.type === 'multiple') {
                    if (!angular.equals($scope.item.answers, $scope.savedModelState.answers)) {
                        angular.copy($scope.item.answers, $scope.savedModelState.answers);
                    }
                    if (!angular.equals($scope.item.freeform_answer, $scope.savedModelState.freeform_answer)) {
                        angular.copy($scope.item.freeform_answer, $scope.savedModelState.freeform_answer);
                    }
                    if ($scope.item.allow_multiple !== $scope.savedModelState.allow_multiple) {
                        return $scope.savedModelState.allow_multiple = $scope.item.allow_multiple;
                    }
                }
            };

            saveModelState();

            /*
                elementInFocus variable has three states:
                1. `null` - element inactive, no other items selected (on load, after save/discard etc)
                2. `false` - element inactive and disabled for user (overlay visible)
                3. `true` - element in focus, currently selected
            */
            $scope.elementInFocus = null;
            $scope.editingDisabled = $scope.$eval(attrs.editingDisabled);

            $scope.$on('$destroy', function() {
                responseFormUpdatedOnDestroy();
                responseFormSavedOnDestroy();
                responseFormFocusItemOnDestroy();
                return responseFormSaveModelStateOnDestroy();
            });

            // Clear question (label) errors once started typing
            $scope.$watch("item.templateOptions.label", function(label) {
                if (label) { return ($scope.errors[$scope.item.key] != null ? $scope.errors[$scope.item.key].labelErrors = [] : undefined); }
            });

            // For multiple choice items, check for freeform answer errors
            if (($scope.item.type === 'multiple') && ($scope.item.freeform_answer != null)) {
                $scope.$watch("item.freeform_answer.label", function(label) {
                    if (label) { return ($scope.errors[$scope.item.key] != null ? $scope.errors[$scope.item.key].freeformErrors = [] : undefined); }
                });
            }


            // Blur all the fields and select the current one only
            $scope.selectItem = function(ev) {
                if (!$scope.elementInFocus && !$scope.editingDisabled && !$(ev.target).is('button') && !$(ev.target).is('.btn-inner-text') && !$(ev.target).is('.remove-answer') && !$(ev.target).is('.move-item') && !$(ev.target).is('.discard')) {
                    const errors = $scope.validateForm();
                    if (Object.keys(errors).length) { return; }
                    $scope.saveChanges(true);
                    $rootScope.$emit('responseFormUpdated');
                    $scope.focusItem();
                    saveModelState();
                    return;
                }
            };

            // Focus the whole item, switch from a default to an edit state
            $scope.focusItem = function() {
                $scope.$broadcast('focusItem');
                formEditService.activeItem = $scope.item.key;
                $scope.elementInFocus = true;
                $scope.inEditMode = true;
                return $scope.item.inEditMode = true;
            };

            // Blur the item and make it non-editable
            $scope.blurItem = function() {
                formEditService.activeItem = null;
                $scope.elementInFocus = false;
                $scope.inEditMode = false;
                return $scope.item.inEditMode = false;
            };

            $scope.saveChanges = function(silentMode) {
                const errors = $scope.validateForm();
                if (Object.keys(errors).length) { return; }
                clearItemErrors();
                saveModelState();
                $scope.item.inEditMode = false;
                return $rootScope.$emit('responseFormUpdateFormStructure', {silentMode});
            };

            $scope.discardChanges = function() {
                clearItemErrors();
                // Previously saved model state exists. Roll back
                if ($scope.savedModelState.templateOptions.label) {
                    angular.copy($scope.savedModelState.templateOptions, $scope.item.templateOptions);
                    $scope.item.mandatory = $scope.savedModelState.mandatory;
                    if ($scope.item.type === 'textarea') {
                        $scope.item.multi_line = $scope.savedModelState.multi_line;
                    }
                    if ($scope.item.type === 'numeric') {
                        $scope.item.only_integers = $scope.savedModelState.only_integers;
                    }
                    if ($scope.item.type === 'fee') {
                        $scope.item.fields = _.filter($scope.savedModelState.fields, item => item.label !== '');
                    }
                    if ($scope.item.type === 'multiple') {
                        $scope.item.answers = _.filter($scope.savedModelState.answers, item => item.label !== '');
                        $scope.item.allow_multiple = $scope.savedModelState.allow_multiple;
                        $scope.item.freeform_answer = $scope.savedModelState.freeform_answer;
                    }
                    $rootScope.$emit('responseFormSaved');
                    // No existing saved state (i.e. for a newly created item),
                    // delete the item instead
                } else { $scope.removeItem($scope.item.id); }
                return $scope.blurItem();
            };

            // At least one item, which is also not a header is mandatory in a response form
            $scope.allowedToRemove = function() {
                let allowedToRemove = false;
                // Don't allow to remove items at all if something is in EditMode
                if (isAnythingInEditMode()) { return false; }
                // Filter all form items except a project fee
                const items = _.filter($scope.form.formFields, item => item.type !== 'fee');
                // Two or more items present
                if (items.length > 1) {
                    if (items.length === 2) {
                        // Two items present. Current item is either a header or there aren't any headers at all
                        allowedToRemove = ($scope.item.type === 'header') || !_.findWhere($scope.form.formFields,
                                {type: 'header'});
                    } else {
                        allowedToRemove = true;
                    }
                    // Only one item - not able to remove
                } else {
                    allowedToRemove = false;
                }

                return allowedToRemove;
            };

            $scope.allowedToReorder = function() {
                // Don't allow to remove items at all if something is in EditMode
                if (isAnythingInEditMode()) { return false; }
                return ($scope.form.formFields.length > 1) && $scope.savedModelState.templateOptions.label;
            };

            var responseFormSaveModelStateOnDestroy = $rootScope.$on('responseFormSaveModelState', function() {
                const errors = $scope.validateForm();
                if (Object.keys(errors).length) { return; }
                return saveModelState();
            });

            var responseFormUpdatedOnDestroy = $rootScope.$on('responseFormUpdated', () => $scope.blurItem());

            var responseFormSavedOnDestroy = $rootScope.$on('responseFormSaved', function(event, data) {
                if (!data) { data = {silentMode: false}; }
                if (data.silentMode) { return; }
                $scope.blurItem();
                return $scope.elementInFocus = null;
            });

            return responseFormFocusItemOnDestroy = $rootScope.$on('responseFormFocusItem', function(event, data) {
                if (data.itemId === $scope.item.id) {
                    $scope.focusItem();
                    return $timeout(() => inputLabel.focus());
                } else {
                    return $scope.blurItem();
                }
            });
        }
    })
    ).directive('responseFormItemVendor', $rootScope => ({link($scope, element, attrs) {}})).directive('itemUserControls', () =>
    ({
        restrict: 'E',
        templateUrl: '/partials/project/tabs/response-forms/_item_user_controls.html'
    })
).directive('responseFormItemUserTools', () =>
    ({
        restrict: 'E',
        templateUrl: '/partials/project/tabs/response-forms/_item_user_tools.html'
    })
).directive('responseFormItemUserUiElements', () =>
    ({
        restrict: 'E',
        templateUrl: '/partials/project/tabs/response-forms/_item_user_ui_elements.html'
    })
).directive('responseFormItemUserHeader', () =>
    ({
        restrict: 'E',
        templateUrl: '/partials/project/tabs/response-forms/_item_header_user.html'
    })
).directive('responseFormItemUserNumeric', () =>
    ({
        restrict: 'E',
        templateUrl: '/partials/project/tabs/response-forms/_item_numeric_user.html'
    })
).directive('responseFormItemUserTextarea', () =>
    ({
        restrict: 'E',
        templateUrl: '/partials/project/tabs/response-forms/_item_textarea_user.html'
    })
).directive('responseFormItemUserPercentage', () =>
    ({
        restrict: 'E',
        templateUrl: '/partials/project/tabs/response-forms/_item_percentage_user.html'
    })
).directive('responseFormItemUserMultiple', () =>
    ({
        restrict: 'E',
        templateUrl: '/partials/project/tabs/response-forms/_item_multiple_user.html',
        controller($scope, _) {
            $scope.answerFocused = -1;

            const pushAnswer = () =>
                $scope.item.answers.push({
                    label: '',
                    placeholder: 'Add answer choice'
                })
            ;

            if ($scope.item.answers.length === 0) {
                pushAnswer();
                $scope.answerFocused = 0;
            }

            $scope.addAnswer = function() {
                $scope.item.answers = _.filter($scope.item.answers, item => item.label !== '');
                pushAnswer();
                return $scope.answerFocused = $scope.item.answers.length - 1;
            };

            $scope.removeAnswer = function(id) {
                $scope.item.answers.splice(id, 1);
                return $scope.validateForm();
            };

            $scope.focusAnswer = function(id) {
                if (id===-1) {
                    const errors = $scope.validateForm();
                    if (Object.keys(errors).length) { return; }
                }
                return $scope.answerFocused = id;
            };

            return $scope.iconName = function() {
                if ($scope.item.allow_multiple) {
                    return 'check_box_outline_blank';
                }
                return 'radio_button_unchecked';
            };
        }
    })
            ).directive('responseFormItemUserFilepicker', () =>
    ({
        restrict: 'E',
        templateUrl: '/partials/project/tabs/response-forms/_item_filepicker_user.html'
    })
);
