import inputTwinAvailabilityCalendarController from './input-twin-availability-calendar.controller';

const inputTwinAvailabilityCalendar = {
    bindings: {
        'displayOnDate': '<?',
        'readOnly': '<?',
        'weekStartDay': '=?',
        'selectedDates': '=?',
        'onInputTwinAvailabilityCalendarEvent': '&?',
    },
    templateUrl: '/shared/input-twin-availability-calendar/input-twin-availability-calendar.template.html',
    controller: inputTwinAvailabilityCalendarController,
};

export default inputTwinAvailabilityCalendar;
