const searchModule = angular.module('pipeline');

import CustomFieldsService from './custom-fields.service';
import SearchSaveService from './search-save.service';
import searchComponent from './components/search/search.component';
import searchSaveComponent from './components/search-save/search-save.component';
import searchFilterSelect from './components/search-filter-select/search-filter-select.component';
import searchFilterCheckbox from './components/search-filter-checkbox/search-filter-checkbox.component';
import searchNoResults from './components/search/no-results.component';
import searchDropdownClose from './directives/search-dropdown-close.directive';

searchModule
    .service('SearchSaveService', SearchSaveService)
    .service('CustomFieldsService', CustomFieldsService)
    .component('search', searchComponent)
    .component('searchSave', searchSaveComponent)
    .component('searchFilterCheckbox', searchFilterCheckbox)
    .component('searchFilterSelect', searchFilterSelect)
    .component('searchNoResults', searchNoResults)
    .directive('searchDropdownClose', searchDropdownClose)

export default searchModule;
