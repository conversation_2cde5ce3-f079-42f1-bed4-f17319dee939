import SearchGridConfiguration from '../../../search-grid-configuration.classes';
import * as SearchColumns from '../../../search-column-configuration.classes';
import { OnboardingWorkflowStage, OnboardingWorkflowVendor, OnboardingWorkflowEvents } from '../../../../../components/onboarding-workflow/onboarding-workflow.classes';

const buyerOnboardingWorkflowSearchGridConfig = ($injector, jobOpening, searchFiltersConfiguration, searchClass) => {

    const $translate = $injector.get('$translate');
    const $rootScope = $injector.get('$rootScope');
    const $state = $injector.get('$state');
    const WorkflowStageService = $injector.get('WorkflowStageService');
    const onboardingWorkflowService = $injector.get('onboardingWorkflowService');
    const stagePromises = {};

    const openSidePanel = (rowData, openResumeTab = false) => {

        onboardingWorkflowService.getVendorInStage(rowData.id)
            .then((response) => {

                let stagePromise = Promise.resolve();

                // Final stage doesn't have stage id
                if (rowData.stage && rowData.stage.id) {

                    // Cache stage details promises, so when clicking on other vendor we have this data already
                    if (!stagePromises[rowData.stage.id]) {
                        stagePromises[rowData.stage.id] = onboardingWorkflowService.getSingleStage(rowData.stage.id, rowData.workflow.id)
                            .then((res) => res[0]);
                    }
                    stagePromise = stagePromises[rowData.stage.id];
                }

                return Promise.all([
                    response,
                    stagePromise,
                ]);

            }).then(([response, stage]) => {

                const singleWorkflowVendor = new OnboardingWorkflowVendor({
                    ...response,
                    workflow: rowData.workflow,
                    stage: stage ? new OnboardingWorkflowStage(stage) : null,
                });

                singleWorkflowVendor.previewOpened = true;
                WorkflowStageService.openStagePreviewForVendorModal(
                    singleWorkflowVendor,
                    rowData.workflow, // allActionsWorkflow
                    () => { }, // this.arrowsActions(index, event.currentTarget),
                    {}, // event.currentTarget,
                    false,
                    jobOpening,
                    { openResumeTab },
                ).finally(() => {
                    singleWorkflowVendor.previewOpened = false;
                });
            });
    }

    const columns = [
        new SearchColumns.JobOpeningStatusColumn({
            headerName: 'DYNAMIC_TABLE__HEADER__STATUS',
            getStatusType: 'progress-status',
            stylesHeaderCell: ['shortlist__table__cell--max-width-50'],
            extractData: (rowData) => ({
                getStatus: () => (
                    WorkflowStageService.getOnboardingProgressStatus(rowData, true)
                )
            })
        }),
        new SearchColumns.UserNameWithAvatarColumn({
            headerName: 'DYNAMIC_TABLE__HEADER__PARTNER',
            avatarSize: 25,
            extractData: (rowData) => rowData.vendor,
        }),
        new SearchColumns.GenericTextColumn({
            headerName: 'DYNAMIC_TABLE__HEADER__CURRENT_STAGE',
            getTextCellData: (rowData) => rowData.workflow_completed ? '-': rowData.stage.name,
            validateData: () => (
                // Make current stage column visible everywhere except finished stage
                $state.current.name.indexOf('.finished-stage') === -1
            )
        }),
        new SearchColumns.GenericTextColumn({
            headerName: 'DYNAMIC_TABLE__HEADER__CONTEXT',
            isHtml: true,
            getTextCellData: (rowData) => {
                if (rowData.context.name === 'default') {
                    return `<span>${$translate.instant('ONBOARDING_CONTEXT_TYPE.default')}</span>`;
                }
                return `<span>
                    ${rowData.context.label}<br />
                    <span class="context-type">${$translate.instant('ONBOARDING_CONTEXT_TYPE.jobopening')}</span>
                </span>`;
            },
            validateData: () => (
                // Make context column visible everywhere except JO view
                !$state.includes('app.marketplace.job-openings')
            )
        }),
        new SearchColumns.GenericTextColumn({
            id: 9,
            headerName: $translate.instant('DYNAMIC_TABLE__HEADER__RESUME'),
            getTextCellAction: (rowData) => openSidePanel(rowData, true),
            getTextCellData: (rowData) => rowData.resumeDocument ? $translate.instant('DYNAMIC_TABLE__CELL__VIEW_RESUME') : ''
        }),
        new SearchColumns.GenericDateColumn({
            id: 5,
            headerName: 'DYNAMIC_TABLE__HEADER__LAST_LOGIN',
            getTextCellData: (rowData) => rowData._stage.name,
            extractData: (rowData) => rowData.last_active,
            emptyValue: '-',
            relativeTime: true,
        }),
        new SearchColumns.UserLocationColumn({
            id: 6,
            headerName: 'DYNAMIC_TABLE__HEADER__LOCATION',
            extractData: (rowData) => rowData.vendor,
        }),
        new SearchColumns.GenericTextColumn({
            id: 7,
            headerName: 'DYNAMIC_TABLE__HEADER__PHONE',
            getTextCellData: (rowData) => rowData.vendor.phone_number,
        }),
        new SearchColumns.GenericTextColumn({
            id: 8,
            headerName: 'DYNAMIC_TABLE__HEADER__EMAIL',
            getTextCellData: (rowData) => rowData.vendor.email,
        }),
        new SearchColumns.GenericDropdownColumn({
            headerName: '',
            order: 99,
            stylesDataCell: ['shortlist__table__cell--align-center', 'sl__t--no-padding'],
            stylesHeaderCell: ['shortlist__table__cell--max-width-50'],
            getDropdownCellData: (rowData) => (
                rowData.getActionsForVendorInStage(
                    WorkflowStageService,
                    jobOpening,
                ).filter(({ actionPromise, id }) => actionPromise && id !== 'proceed').map((item) => ({
                    ...item,
                    label: item.name,
                    actionPromise: item.actionPromise,
                }))
            ),
            doAction: (action, rowData, rowSettings, search) => {

                return action.actionPromise(rowData).then((res) => {

                    // Refresh filters
                    searchFiltersConfiguration.updateFilters();

                    let vendorPositionInList = search.listItems.indexOf(rowData);

                    // Search for action affected vendor and update list according to updated vendor details
                    return searchClass.getSearchPromise().execute([rowData.vendor.slug])
                        .then(({ response: { hits: vendorsInStages } }) => {

                            vendorsInStages = vendorsInStages.filter((i) => i.context.name === rowData.context.name);

                            // Vendor was not found on current list and no new ~ remove row
                            if (vendorsInStages.length === 0) {
                                rowSettings._isDeleted = true;
                            }

                            search.updateListItems(
                                vendorPositionInList,
                                vendorsInStages,
                                (item, updatedItem) => item.id === updatedItem.id,
                                (item) => {
                                    return (
                                        item.context.name === rowData.context.name &&
                                        item.vendor.id === rowData.vendor.id &&
                                        !vendorsInStages.find((i) => i.id === item.id)
                                    );
                                },
                            );
                        });
                });
            },
        }),
    ];

    return new SearchGridConfiguration(
        columns,
        {
            // If provided, clicking on row will trigger this action
            rowAction: openSidePanel,
            columnsManagementConfig: {
                bulkActions: {
                    itemsSelectedLabel: 'DISCOVERY_COUNT_SELECTED',
                    onComplete: () => {
                        // Reload page when bulk action is completed
                        // We could update affected rows using same logic as in rowAction but easier to reload
                        $rootScope.$broadcast(OnboardingWorkflowEvents.REFRESH_ONBOARDING_WORKFLOW_DETAILS);
                    }
                },
            },
            // This one is needed to re-render list, mostly in cases where list is returned too fast (e.g. sorted by JS)
            rowKey: (index, rowData) => {
                return rowData.id;
            },
        }
    );
}

export default buyerOnboardingWorkflowSearchGridConfig;
