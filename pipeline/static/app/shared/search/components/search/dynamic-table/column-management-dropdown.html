<dropdown
    ng-if="!$ctrl.searchGridConfiguration.columnsManagement.hideManageButton"
    class="dropdown-actions"
    auto-close="outsideClick"
    is-open="dropdownIsOpen"
>
    <a class="btn-actions-dots btn-actions btn" dropdown-toggle href>
        <i class="material-icons">add</i>
    </a>

    <ul
        ng-if="!$ctrl.searchGridConfiguration.columnsManagement.reactColumnManagement"
        class="dropdown-menu"
        dropdown-menu
    >
        <li
            ng-repeat="column in $ctrl.searchGridConfiguration.columnsManagement.getColumns()"
        >
            <input-switch
                size="small"
                initial-value="column.isVisible"
                on-change="$ctrl.searchGridConfiguration.toggleColumn(column)"
                label="{{ ::column.headerName | translate }}"
            ></input-switch>
        </li>
    </ul>
    <ul
        ng-if="$ctrl.searchGridConfiguration.columnsManagement.reactColumnManagement"
        class="dropdown-menu"
        style="min-width: 300px; max-width: 300px; padding: 0"
        dropdown-menu
    >
        <r-column-configuration
            ng-if="dropdownIsOpen"
            columns="$ctrl.searchGridConfiguration.columnsManagement.columnConfig"
            on-add="$ctrl.searchGridConfiguration.onAddColumn"
            on-remove="$ctrl.searchGridConfiguration.onRemoveColumn"
            on-hide="$ctrl.searchGridConfiguration.onHideColumn"
            on-show="$ctrl.searchGridConfiguration.onShowColumn"
            on-order-change="$ctrl.searchGridConfiguration.onOrderChange"
            options="{sortable: true}"
        ></r-column-configuration>
    </ul>
</dropdown>
