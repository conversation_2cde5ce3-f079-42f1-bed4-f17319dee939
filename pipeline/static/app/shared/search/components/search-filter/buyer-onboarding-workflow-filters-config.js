import SearchFiltersConfig from './search.filter';

export default class BuyerOnboardingWorkflowFiltersConfig extends SearchFiltersConfig {
    constructor(
        $injector,
        {
            response: {
                in_progress: inProgressVendors,
                stages: workflowStages,
                finished: finishedStageVendorCount,
                disqualified: disqualifiedStageVendorCount,
            },
            promiseWrapper,
        },
    ) {
        super($injector);

        Object.assign(this, {
            $state: $injector.get('$state'),
            $translate: $injector.get('$translate'),
            inProgressVendors,
            workflowStages,
            finishedStageVendorCount,
            disqualifiedStageVendorCount,
            promiseWrapper,
        });

        if (this.$state.includes('app.onboarding-workflow.details')) {
            this.rootState = 'app.onboarding-workflow.details';
        } else if (this.$state.includes('app.marketplace.job-openings.details.tab-workflow')) {
            this.rootState = 'app.marketplace.job-openings.details.tab-workflow';
        }
    }

    updateFilters() {
        this.promiseWrapper
            .execute()
            .then(
                ({
                    response: {
                        in_progress: inProgressVendors,
                        stages: workflowStages,
                        finished: finishedStageVendorCount,
                        disqualified: disqualifiedStageVendorCount,
                    },
                }) => {
                    Object.assign(this, {
                        inProgressVendors,
                        workflowStages,
                        finishedStageVendorCount,
                        disqualifiedStageVendorCount,
                    });
                    this.initFilters();
                },
            );
    }

    initFilters(initFiltersResponse = null, initFiltersPromises = null) {
        const allStages = {
            name: this.$translate.instant('ONBOARDING_WORKFLOW_STAGE__LABEL__ALL_STAGES'),
            count: this.inProgressVendors.vendors_in_progress_count + this.finishedStageVendorCount,
            _vendorsInProgress: true,
            sref: `${this.rootState}.stage({ stageId: '' })`,
            selected: false,
        };
        const selectedStages = this.workflowStages
            .filter((i) => i.stage_type !== 'action')
            .map((i) => ({
                name: i.name,
                count: i.vendors_current_count,
                selected: false,
                sref: `${this.rootState}.stage({ stageId: ${i.id} })`,
                _stage: i,
            }));
        const finished = {
            name: this.$translate.instant('ONBOARDING_WORKFLOW_STAGE__LABEL__FINISHED'),
            count: this.finishedStageVendorCount,
            _vendorsFinished: true,
            sref: `${this.rootState}.finished-stage`,
            selected: false,
        };
        const disqualified = {
            name: this.$translate.instant('ONBOARDING_WORKFLOW_STAGE__LABEL__DISQUALIFIED'),
            count: this.disqualifiedStageVendorCount,
            _vendorsDisqualified: true,
            sref: `${this.rootState}.disqualified-stage`,
            selected: false,
        };

        this.filters = [allStages, ...selectedStages, finished];
        if (this.disqualifiedStageVendorCount > 0) {
            this.filters.push(disqualified);
        }

        const selectedStage = this.filters.find((i) => i._stage && i._stage.id === parseInt(this.$state.params.stageId, 10));
        if (this.$state.current.name.indexOf('.finished-stage') > 0) {
            finished.selected = true;
        } else if (this.$state.current.name.indexOf('.disqualified-stage') > 0) {
            disqualified.selected = true;
        } else if (selectedStage) {
            selectedStage.selected = true;
        } else {
            allStages.selected = true;
        }
    }

    onFilterToggle(filter) {
        filter.selected = !filter.selected;

        // If no filters selected, select first one `_vendorsInProgress`
        const selectedFilter = this.filters.find((i) => i.selected);
        if (!selectedFilter) {
            this.filters[0].selected = true;
        } else {
            this.filters.forEach((i) => i.selected = (i === filter) ? true : false);
        }
        this.searchFunctions.search();
    }
}
