<div class="input-location-distance">
    <div class="input-location-distance__content">
        <div class="loader no-animate" ng-if="!$ctrl.isGoogleLoaded">
            <span>{{ 'LOCATION_DISTANCE_LOADING' | translate }}</span>
        </div>
        <div ng-if="$ctrl.isGoogleLoaded" class="no-animate">
            <div class="input-location-distance__content__inputs clearfix">
                <i class="icon-location-icon"></i>
                <input places-auto-complete
                       id="locationInput"
                       ng-model="$ctrl.location"
                       on-place-changed="$ctrl.placeChanged($ctrl)"
                       type="search"
                       types="[ 'geocode' ]"
                       class="form-control search-location__input no-animate"
                       placeholder="{{ 'LOCATION_DISTANCE_INPUT_PLACEHOLDER' | translate }}"
                />

                <dropdown-multi-select events="$ctrl.distanceEvents"
                                       extra-settings="$ctrl.distanceDropdownSettings"
                                       options="$ctrl.distanceOptions"
                                       selected-model="$ctrl.distanceSelected">
                </dropdown-multi-select>
            </div>
            <div>
                <a ng-click="$ctrl.search()"
                   class="btn-see-results btn-generic shortlist__button-3 shortlist__button--small"

                   href="">
                    {{ 'LOCATION_DISTANCE_CTA_SEARCH' | translate }}
                </a>
                <a ng-click="$ctrl.cancel()" class="btn-cancel shortlist__button-2 shortlist__button--small" href="">
                    {{ 'LOCATION_DISTANCE_CTA_CANCEL' | translate }}
                </a>
            </div>
        </div>
    </div>
</div>



