'use strict';
import { Timesheets } from 'shortlist-react';

const TimesheetPermissionsService = Timesheets.PermissionsService;

const userPermissionsService = function (
    $log,
    $q,
    $http,
    $rootScope,
    $translate,
    $injector,
    vendorExternalLinks,
) {
    const _pAppTypes = ['buyer', 'staff', 'vendor', 'approvals'];

    const _pTypeTypes = [
        'all',
        'activity',
        'contract',
        'contract template',
        'custom rates',
        'group',
        'review',
        'invite link',
        'vendor',
        'team',
        'team member',
        'template',
        'form template',
        'vendor rank',
        'budget range',
        'campaign',
        'project',
        'insight',
        'onboarding template',
        'agreement',
        'document',
        'payment',
        'settings',
        'job opening',
        'user',
        'user role',
        'custom vendor field',
        'user team',
        'dashboards',
        'task',
        'task group',
    ];

    const _roleNames = [
        'buyer.projects',
        'buyer.projects.publish',
        'buyer.projects.delete',
        'buyer.tasks',
        'buyer.tasks.live',
        'buyer.tasks.all_taskgroups',
        'buyer.tasks.manager_only',
        'buyer.tasks.user_team',
        'buyer.tasks.no_team',
        'buyer.documents',
        'buyer.documents.confidential',
        'buyer.agreements',
        'buyer.agreements.confidential',
        'buyer.insights',
        'buyer.payments.related',
        'buyer.payments.team_related',
        'buyer.payments.approve',
        'buyer.payments.schedule',
        'buyer.payments.reject',
        'buyer.payments.flag',
        'buyer.payments',
        'buyer.payments.vendor_relationship',
        'buyer.add vendors',
        'buyer.invite vendors',
        'buyer.set manager',
        'buyer.marketplace',
        'buyer.marketplace.user_job_openings',
        'buyer.marketplace.create',
        'buyer.marketplace.live',
        'buyer.contracts',
        'buyer.contracts.participant',
        'buyer.archive vendor',
        'buyer.reset vendor login link',
        'buyer.vendor delete',
        'buyer.force compliance',
        'buyer.change vendor type',
    ];

    let _cachedRoles = [];
    let _translations = [];
    let _settings = [];

    this.match = (mask) => {
        const reg = /(.{0,})\/(.{0,})\/(.{0,})\/(.{0,})/g;
        return reg.exec(mask);
    };

    this.isValid = (mask) => {
        let pActions, pApp, pConditions, pType;
        if (typeof mask !== 'string') {
            return false;
        }
        if (mask.charAt(0) === '!') {
            mask = mask.substr(1);
        }
        const matcher = this.match(mask);
        if (!matcher || matcher.length > 5) {
            return false;
        }
        [mask, pApp, pType, pConditions, pActions] = Array.from(matcher);
        if (_pAppTypes.indexOf(pApp) === -1) {
            return false;
        }
        if (_pTypeTypes.indexOf(pType) === -1) {
            return false;
        }
        return true;
    };

    this.check = (mask) => {
        // parse the permission string (i.e. "buyer/group/all/add vendor")
        // at this point we're sure the mask is valid
        let pActions, pApp, pConditions, pType;
        [mask, pApp, pType, pConditions, pActions] = Array.from(
            this.match(mask),
        );
        pActions = pActions.split(',');
        if (!this.permissions.hasOwnProperty(pApp)) {
            return false;
        }
        // 1. Check for master rights
        const masterRightsConditional = __guard__(
            this.permissions[pApp].all != null
                ? this.permissions[pApp].all.all
                : undefined,
            (x) => x.indexOf('all'),
        );
        if (
            masterRightsConditional !== -1 &&
            masterRightsConditional !== undefined
        ) {
            return true;
        }
        // 2. Check for object-based rights (i.e. vendor / group / document)
        if (
            !this.permissions[pApp].hasOwnProperty(pType) &&
            !this.permissions[pApp].hasOwnProperty('all')
        ) {
            return false;
        }
        if (this.permissions[pApp].hasOwnProperty('all')) {
            pType = 'all';
        }
        // 3. Check for conditions (typically 'all')
        if (
            !this.permissions[pApp][pType].hasOwnProperty(pConditions) &&
            !this.permissions[pApp][pType].hasOwnProperty('all')
        ) {
            return false;
        }
        if (
            this.permissions[pApp][pType].hasOwnProperty('all') &&
            !this.permissions[pApp][pType].hasOwnProperty(pConditions)
        ) {
            pConditions = 'all';
        }
        // 4. Check for actions (i.e. 'create' and 'edit')
        const permissionObj = this.permissions[pApp][pType][pConditions];
        // True - All actions permitted for this user
        if (permissionObj.indexOf('all') !== -1) {
            return true;
        }
        // Compare the required actions against ones in the dictionary
        for (var action of Array.from(pActions)) {
            if (permissionObj.indexOf(action) === -1) {
                return false;
            }
        }
        return true;
    };

    this.permissions = {};

    this.getPermissions = function () {
        const deferred = $q.defer();
        $http.get('/api/users/current/permissions/').then(
            (response) => {
                // TODO: Redux action dispatcher
                $rootScope.$broadcast('getPermissions', {
                    payload: response.data,
                });
                this.permissions = response.data;
                return deferred.resolve(response.data);
            },
            (error) => {
                this.permissions = {};
                return deferred.resolve({});
            },
        );
        return deferred.promise;
    };

    this.checkPermission = function (mask) {
        let negative;
        if (mask == null) {
            mask = '';
        }
        const isValid = this.isValid(mask) && mask !== '';
        if (!isValid) {
            // TODO: raise exception?
            $log.debug(`Invalid permission mask: ${mask}`);
            return true;
        }

        // check for the negative clause
        if (mask.charAt(0) === '!') {
            mask = mask.substr(1);
            negative = true;
        } else {
            negative = false;
        }

        const result = this.check(mask);

        if (negative) {
            return !result;
        } else {
            return result;
        }
    };

    this.getRoles = (selectable) => {
        if (selectable == null) {
            selectable = false;
        }
        if (_cachedRoles.length) {
            return new Promise((resolve) => {
                resolve(this.filterSelectable(_cachedRoles, selectable));
            });
        } else {
            return $http.get('/api/roles/').then((response) => {
                if (response && response.data) {
                    _cachedRoles = response.data;
                }
                return this.filterSelectable(_cachedRoles, selectable);
            });
        }
    };

    this.loadSettings = () => {
        return $http.get('/api/users/current/settings/').then(({ data }) => {
            return (_settings = data);
        });
    };

    this.filterSelectable = (items, selectable) => {
        if (items == null) {
            items = [];
        }
        if (selectable == null) {
            selectable = false;
        }
        if (!selectable) {
            return items;
        } else {
            return items.filter((item) => {
                return item.selectable;
            });
        }
    };

    this.getRole = (role) => $http.get(`/api/roles/${role}/`);

    this.setRole = (role, data) => $http.patch(`/api/roles/${role}/`, data);

    this.setRolesForUser = (userSlug, data) =>
        $http.put(`/api/users/${userSlug}/roles/`, data);

    this.mapRoleName = function (role) {
        const filteredRoles = _cachedRoles.filter((item) => item.name === role);
        if (filteredRoles[0] && filteredRoles[0].display_name) {
            let displayName = filteredRoles[0].display_name;
            const matches = Array.from(displayName.matchAll(/\[(.*?)\]/g));
            for (var match of Array.from(matches)) {
                if (_translations[match[1]]) {
                    displayName = displayName.replace(
                        match[0],
                        _translations[match[1]],
                    );
                }
            }
            return displayName;
        } else {
            return role;
        }
    };

    this.getBuyerRoles = () => _roleNames;

    this.hasAccessToOnboardingWorkflows = (vendorType = null) => {
        let left1;
        if (vendorType) {
            let left;
            return (left =
                $rootScope.vendorTypeHasFeature('onboarding', vendorType) &&
                $rootScope.tenantHasFeature('onboarding_workflows')) != null
                ? left
                : false;
        }
        return (left1 =
            $rootScope.tenantHasFeature('onboarding') &&
            $rootScope.tenantHasFeature('onboarding_workflows')) != null
            ? left1
            : false;
    };

    this.hasAccessToJobOpening = () => {
        return (
            this.checkPermission('buyer/job opening/all/view') ||
            this.checkPermission(
                'buyer/job opening/job opening creator/view',
            ) ||
            this.checkPermission(
                'buyer/job opening/this user in custom field/view',
            )
        );
    };

    this.hasAccessToMarketplace = () => {
        return $rootScope.tenantHasFeature('marketplace');
    };

    this.hasAccessToContracts = () => {
        return (
            $rootScope.tenantHasFeature('contracts') &&
            !$rootScope.tenantHasFeature('contracts_soon')
        );
    };

    this.hasAccessToVendorCustomFields = () => {
        return this.checkPermission('buyer/custom vendor field/all/edit');
    };

    this.hasAccessToProjects = () => {
        let left;
        return (left =
            $rootScope.tenantHasFeature('requests') &&
            ((this.permissions.buyer != null
                ? this.permissions.buyer.hasOwnProperty('project')
                : undefined) ||
                (this.permissions.vendor != null
                    ? this.permissions.vendor.hasOwnProperty('project')
                    : undefined) ||
                (this.permissions.buyer != null
                    ? this.permissions.buyer.hasOwnProperty('all')
                    : undefined) ||
                (this.permissions.vendor != null
                    ? this.permissions.vendor.hasOwnProperty('all')
                    : undefined))) != null
            ? left
            : false;
    };

    this.isVendorStarRatingEnabled = () => {
        return !$rootScope.tenantHasFeature('disable_vendor_star_rating');
    };

    this.hasAccessToTimeSheets = () => {
        return (this.hasAccessToStandardTimeSheets() || this.hasAccessToExtendedTimeSheets())
    };

    this.hasAccessToStandardTimeSheets = () => {
      return TimesheetPermissionsService.hasAccessToStandardTimeSheets();
    };

    this.hasAccessToExtendedTimeSheets = () => {
        return TimesheetPermissionsService.hasAccessToExtendedTimeSheets();
    };

    this.hasAccessToTimesheetPresets = () => {
        return TimesheetPermissionsService.hasAccessToTimesheetPresets();
    };

    this.hasTimesheetPresets = () => {
        return (
            TimesheetPermissionsService.hasAccessToTimesheetPresets() &&
            $injector.get('TimesheetService').timesheetPresets.availablePresets
                .length
        );
    };

    this.hasAccessToInsights = () => {
        let left;
        return (left =
            (this.permissions.insights != null
                ? this.permissions.insights.hasOwnProperty('all')
                : undefined) ||
            (this.permissions.insights != null
                ? this.permissions.insights.hasOwnProperty('insight')
                : undefined)) != null
            ? left
            : false;
    };

    this.hasAccessToDocumentsOrAgreements = () => {
        let left;
        return (left =
            (this.permissions.buyer != null
                ? this.permissions.buyer.hasOwnProperty('document')
                : undefined) ||
            (this.permissions.vendor != null
                ? this.permissions.vendor.hasOwnProperty('document')
                : undefined) ||
            (this.permissions.buyer != null
                ? this.permissions.buyer.hasOwnProperty('agreement')
                : undefined) ||
            (this.permissions.buyer != null
                ? this.permissions.buyer.hasOwnProperty('all')
                : undefined) ||
            (this.permissions.vendor != null
                ? this.permissions.vendor.hasOwnProperty('all')
                : undefined)) != null
            ? left
            : false;
    };

    this.hasAccessToConfidentialAgreement = () => {
        return this.checkPermission('buyer/agreement/all/view confidential');
    };

    this.hasAccessToDocuments = () => {
        let left;
        return (left =
            (this.permissions.buyer != null
                ? this.permissions.buyer.hasOwnProperty('document')
                : undefined) ||
            (this.permissions.vendor != null
                ? this.permissions.vendor.hasOwnProperty('document')
                : undefined) ||
            (this.permissions.buyer != null
                ? this.permissions.buyer.hasOwnProperty('all')
                : undefined) ||
            (this.permissions.vendor != null
                ? this.permissions.vendor.hasOwnProperty('all')
                : undefined)) != null
            ? left
            : false;
    };

    this.hasAccessToConfidentialDocuments = () => {
        return this.checkPermission('buyer/document/all/view confidential');
    };

    this.hasAccessToAgreements = () => {
        let left;
        return (left =
            (this.permissions.buyer != null
                ? this.permissions.buyer.hasOwnProperty('agreement')
                : undefined) ||
            (this.permissions.buyer != null
                ? this.permissions.buyer.hasOwnProperty('all')
                : undefined) ||
            ((this.permissions.vendor != null
                ? this.permissions.vendor.hasOwnProperty('all')
                : undefined) &&
                $rootScope.tenantHasFeature(
                    'hellosign/' +
                        ($rootScope.user.vendor != null
                            ? $rootScope.user.vendor.vendor_type
                            : undefined),
                ))) != null
            ? left
            : false;
    };

    this.hasAccessToReviews = (vendor) => {
        let left;
        let tenantFlag = $rootScope.tenantHasFeature('reviews');
        if (vendor) {
            tenantFlag = $rootScope.tenantHasFeature(
                'reviews/' + vendor.vendor_type,
            );
        }
        return (left =
            tenantFlag &&
            ((this.permissions.buyer != null
                ? this.permissions.buyer.hasOwnProperty('review')
                : undefined) ||
                (this.permissions.buyer != null
                    ? this.permissions.buyer.hasOwnProperty('all')
                    : undefined))) != null
            ? left
            : false;
    };

    this.hasAccessToTaskTemplatesInJobOpenings = () => {
        return (
            $rootScope.tenantHasFeature('task_templates_in_job_openings') &&
            this.hasAccessToTasks()
        );
    };

    this.hasAccessToTasks = () => {
        let left;
        return (left =
            $rootScope.tenantHasFeature('projects_and_tasks') &&
            ((this.permissions.buyer != null
                ? this.permissions.buyer.hasOwnProperty('task')
                : undefined) ||
                (this.permissions.vendor != null
                    ? this.permissions.vendor.hasOwnProperty('task')
                    : undefined) ||
                (this.permissions.buyer != null
                    ? this.permissions.buyer.hasOwnProperty('all')
                    : undefined) ||
                (this.permissions.vendor != null
                    ? this.permissions.vendor.hasOwnProperty('all')
                    : undefined))) != null
            ? left
            : false;
    };

    this.hasAccessToTimeTracking = () => {
        return (
            $rootScope.tenantHasFeature('time_tracking') &&
            (this.permissions.vendor != null
                ? this.permissions.vendor.hasOwnProperty('all')
                : undefined) &&
            !this.userIsStaffingSupplier()
        );
    };

    this.hasListAccessToAllTasks = () => {
        const perms = this.permissions.buyer;
        return (
            this.hasAccessToTasks() &&
            ((perms.hasOwnProperty('all') &&
                (perms.all.all.includes('list') ||
                    perms.all.all.includes('all'))) ||
                (perms.hasOwnProperty('task') &&
                    (perms.task.all.includes('list') ||
                        perms.task.all.includes('all'))))
        );
    };

    this.hasTeamsLimitedAccessToTasks = () => {
        const perms = this.permissions.buyer;
        return (
            this.hasAccessToTasks() &&
            perms.hasOwnProperty('task') &&
            perms.task.hasOwnProperty('task manager') &&
            ((perms.task.hasOwnProperty('task team') &&
                (perms.task['task team'].includes('list') ||
                    perms.task['task team'].includes('all'))) ||
                (perms.task.hasOwnProperty('task no team') &&
                    (perms.task['task no team'].includes('list') ||
                        perms.task['task no team'].includes('all'))))
        );
    };

    this.hasAccessToStageLocking = () => {
        return $rootScope.tenantHasFeature('onboarding_stage_locking');
    };

    this.canUnlockVendorStage = () => {
        return (
            this.hasAccessToStageLocking &&
            this.checkPermission('buyer/vendor/all/unlock')
        );
    };

    this.hasAccessToPayments = () => {
        let left;
        return (left =
            $rootScope.tenantHasFeature('payments') &&
            (this.iterateOverPermissions('payment', ['list', 'view']) ||
                (this.permissions.buyer != null
                    ? this.permissions.buyer.hasOwnProperty('all')
                    : undefined) ||
                (((this.permissions.vendor != null
                    ? this.permissions.vendor.hasOwnProperty('payment')
                    : undefined) ||
                    (this.permissions.vendor != null
                        ? this.permissions.vendor.hasOwnProperty('all')
                        : undefined)) &&
                    $rootScope.tenantHasFeature(
                        'payments/' +
                            __guard__(
                                $rootScope.user != null
                                    ? $rootScope.user.vendor
                                    : undefined,
                                (x) => x.vendor_type,
                            ),
                    )))) != null
            ? left
            : false;
    };

    this.hasAccessToVendorTax = () => {
        if (!this.userIsVendor()) {
            return false;
        }
        return $rootScope.vendorTypeHasFeature(
            'tax_information', $rootScope.user.vendor.vendor_type,
        );
    };

    this.hasAccessToVendorPayoutMethods = () => {
        if (!this.userIsVendor()) {
            return false;
        }
        return $rootScope.vendorTypeHasFeature(
            'bank_details', $rootScope.user.vendor.vendor_type,
        ) && $rootScope.tenantHasFeature('bank_details_mc');
    };

    this.hasAccessToVendorBankDetails = () => {
        if (!this.userIsVendor()) {
            return false;
        }
        return $rootScope.vendorTypeHasFeature(
            'bank_details', $rootScope.user.vendor.vendor_type,
        );
    };

    this.canAddPayments = () => {
        return (
            !$rootScope.user.vendor ||
            !$rootScope.tenantHasFeature('payments_partners_cannot_create')
        );
    };

    this.iterateOverPermissions = (permissionType, permissions) => {
        let havePermission = false;
        if (
            this.permissions.buyer != null
                ? this.permissions.buyer.hasOwnProperty('all')
                : undefined
        ) {
            return true;
        }
        if (
            this.permissions.buyer != null
                ? this.permissions.buyer.hasOwnProperty(permissionType)
                : undefined
        ) {
            if (
                this.permissions.buyer[permissionType].hasOwnProperty('all') &&
                this.permissions.buyer[permissionType]['all'].includes('all')
            ) {
                return true;
            }
            for (var permTyp in this.permissions.buyer[permissionType]) {
                var val = this.permissions.buyer[permissionType][permTyp];
                for (var perm of Array.from(permissions)) {
                    if (val.includes(perm)) {
                        havePermission = true;
                    }
                }
            }
        }
        return havePermission;
    };

    this.checkSectionAccess = (section) => {
        if (section === 'projects') {
            return this.hasAccessToProjects();
        }
        if (section === 'insights') {
            return this.hasAccessToInsights();
        }
        if (section === 'documents') {
            return this.hasAccessToDocumentsOrAgreements();
        }
        if (section === 'payments') {
            return this.hasAccessToPayments();
        }
        if (section === 'tasks') {
            return this.hasAccessToTasks();
        }
        if (section === 'vendorTax') {
            return this.hasAccessToVendorTax();
        }
        if (section === 'vendorPayoutMethods') {
            return this.hasAccessToVendorPayoutMethods();
        }
        if (section === 'vendorBankDetails') {
            return this.hasAccessToVendorBankDetails();
        }
        if (section === 'time_tracking') {
            return this.hasAccessToTimeTracking();
        }
        return true;
    };

    this.hasAccessToOnboardingSection = () => {
        return !_settings.includes('hide_onboarding_tab');
    };

    this.canSeeShowOnlyMyStagesCheckbox = () => {
        return !_settings.includes('hide_show_only_my_stages');
    };

    this.hasAccessToJobOpeningLocation = () => {
        return !_settings.includes('hide_job_opening_location');
    };

    this.hasAccessToJobOpeningApprovers = () => {
        return !_settings.includes('hide_job_opening_approvers');
    };

    this.hasAccessToJobOpeningDescription = () => {
        return !_settings.includes('hide_job_opening_description');
    };

    this.hasAccessToJobOpeningCustomFields = () => {
        return !_settings.includes('hide_job_opening_custom_fields');
    };

    this.hasAccessToBeamer = () => {
        return !_settings.includes('hide_beamer');
    };

    this.canOpenDistractionFreeMode = () => {
        return _settings.includes('redirect_to_distraction_free_mode');
    };

    this.hasAccessToProfileField = (fieldName) => {
        return !_settings.includes('hide_profile_' + fieldName);
    };

    this.hasAccessToProfileExternalLinks = () => {
        return (
            vendorExternalLinks.filter((link) =>
                this.hasAccessToProfileField(link),
            ).length > 0
        );
    };

    this.getDisplayedProfileFields = () => {
        const reducer = (fields, fieldName) => {
            fields[fieldName] = this.hasAccessToProfileField(fieldName);
            return fields;
        };

        return [
            'introduction',
            'titles',
            'skills',
            'rate_section',
            'cover_photo',
            'phone',
            'location',
            'working_hours',
        ]
            .concat(vendorExternalLinks)
            .reduce(reducer, {});
    };

    this.userIsStaffingSupplier = () => {
        return this.permissions.hasOwnProperty('supplier');
    };

    this.userIsVendor = () => {
        const user = $rootScope.user || {};
        if (!user.hasOwnProperty('vendor')) {
            return false;
        }
        return $rootScope.user.vendor;
    };

    this.userIsPublicVendor = () => {
        if (!this.userIsVendor()) {
            return false;
        }
        return !$rootScope.user.shortlisted;
    };

    this.userIsStaff = () => {
        return this.permissions.hasOwnProperty('staff');
    };

    this.userIsGuest = () => {
        return (
            this.permissions.hasOwnProperty('guest') &&
            !this.permissions.hasOwnProperty('staff') &&
            !$rootScope.user.is_admin
        );
    };

    this.userIsAdmin = () => {
        return $rootScope.user.is_admin;
    };

    this.canApproveVendor = (vendor) => {
        return (
            $rootScope.user.is_admin ||
            this.checkPermission('buyer/vendor/all/invite') ||
            vendor.status !== 'not invited'
        );
    };

    this.hasAccessToBulkTaskBooking = () => {
        return (
            $rootScope.tenantHasFeature('bulk_task_booking') &&
            $rootScope.is_user
        );
    };

    this.canRankSelectedVendors = () => {
        return (
            this.checkPermission('buyer/vendor/all/edit') &&
            $rootScope.userHasAccess($rootScope.accessLevels.admin)
        );
    };

    this.getApiPrefixByUserType = () => {
        if (this.userIsStaffingSupplier()) {
            return '/api/s/';
        } else if (this.userIsVendor()) {
            return '/api/v/';
        } else {
            return '/api/';
        }
    };

    this.getTranslations = () => {
        return $http
            .get('/api/tools/translations/')
            .then((response) => (_translations = response.data));
    };

    // @getRoles()
    this.getTranslations();
    this.loadSettings();
};

userPermissionsService.$inject = [
    '$log',
    '$q',
    '$http',
    '$rootScope',
    '$translate',
    '$injector',
    'vendorExternalLinks',
];

angular
    .module('user-permissions')
    .service('userPermissionsService', userPermissionsService);

function __guard__(value, transform) {
    return typeof value !== 'undefined' && value !== null
        ? transform(value)
        : undefined;
}
