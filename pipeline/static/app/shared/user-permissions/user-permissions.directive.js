'use strict';

const userPermissions = (
    $rootScope,
    $compile,
    $animate,
    userPermissionsService,
) => ({
    restrict: 'A',
    terminal: true,
    multiElement: true,
    priority: 1000,

    /*
        Making the directive terminal means it won't compile any other
        directives inside the target element. We have to trigger the compilation
        manually if permissions are granted.
    */
    compile($element, $attrs) {
        const link_ = $compile($element, null, 999);

        const link = function ($scope, $element, $attrs) {
            let ifClause;
            const childScope = $scope.$new();
            if ($attrs.userPermissionsIf) {
                ifClause = $scope.$eval($attrs.userPermissionsIf);
            } else {
                ifClause = true;
            }
            if (
                userPermissionsService.checkPermission(
                    $attrs.userPermissions,
                ) &&
                ifClause
            ) {
                return link_(
                    childScope,
                    (
                        cloned,
                        scope, // This callback is necessary to ensure filters are compiled & displayed as well
                    ) => $element.replaceWith(cloned),
                );
            } else {
                childScope.$destroy();
                return $element.empty();
            }
        };

        return link;
    },
});

userPermissions.$inject = [
    '$rootScope',
    '$compile',
    '$animate',
    'userPermissionsService',
];

angular
    .module('user-permissions', [
        'pascalprecht.translate',
        'ui.router',
    ])
    .directive('userPermissions', userPermissions);
