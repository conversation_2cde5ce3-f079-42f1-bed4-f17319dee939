export default class {
    constructor($scope, $timeout) {
        'ngInject';

        Object.assign(this, {
            $scope,
            $timeout
        });
    }

    $onInit() {
        this.show = false;
        this.timeoutHandle = false;
        this.time = this.time || 5000;
    }

    $onChanges(changes) {
        if (changes.trigger.currentValue) {
            this.start();
        } else {
            this.cancel();
        }
    }

    $onDestroy() {
        this.cancel();
    }

    start() {
        this.timeoutHandle = this.$timeout(() => {
            this.show = true;
        }, this.time);
    }

    cancel() {
        if (this.timeoutHandle) {
            this.show = false;
            this.$timeout.cancel(this.timeoutHandle);
        }
    }
};
