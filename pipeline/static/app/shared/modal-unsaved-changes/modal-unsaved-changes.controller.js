class unsavedChangesModalController {
    constructor($modalInstance, $rootScope) {
        'ngInject'

        this.$modalInstance = $modalInstance;
        this.$rootScope = $rootScope;
    }

    discard() {
        this.$modalInstance.close();
    }

    dismiss() {
        this.$modalInstance.dismiss();
        this.$rootScope.completeProgressBar();
    }
}

export default unsavedChangesModalController
