export default class AskForFeedbackController {

    constructor($rootScope, $scope, $q, $filter) {
        'ngInject';

        this.selectedUsers = {};
        this.customMessage = '';
        this.inviteToShortlist = false;

        Object.assign(this, {
            $rootScope,
            $scope,
            $q,
            $filter,
        });
    }

    $onInit() {
        this.$scope.$watch((() => {
            this.onChange({
                feedbackForm: {
                    users: Object.keys(this.selectedUsers).reduce((output, user) => {
                        const existingUser = this.users.find(i => i.full_name === user);
                        if (existingUser) {
                            output.push(existingUser.slug);
                        } else if (this.isValidEmail(user)) {
                            output.push(user);
                        }
                        return output;
                    }, []),
                    custom_message: this.customMessage,
                    invite_to_shortlist: this.inviteToShortlist,
                },
            });
        }));
    }

    searchUsers = (input) => {
        return this.$q.when(_.pluck(
            _.filter(this.users, user =>
                (user.full_name !== '') &&
                (user.full_name.toLowerCase().indexOf(input.toLowerCase()) !== -1) &&
                (user.id !== this.$rootScope.user.id) &&
                (Object.keys(this.selectedUsers).indexOf(user.full_name) === -1)
            ), 'full_name')
        );
    };

    isValidEmail = (input) => this.$filter('isValidEmail')(input);

    hasNewUsers = () => _.filter(Object.keys(this.selectedUsers), user => this.$filter('isValidEmail')(user)).length;
}
