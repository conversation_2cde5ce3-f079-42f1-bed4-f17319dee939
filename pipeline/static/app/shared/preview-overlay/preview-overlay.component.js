'use strict';

const previewOverlay = {
    bindings: {
        closeTrigger: '&',
    },
    template: `\
<div class="preview-overlay">
    <span class="copy">{{ 'PREVIEW_OVERLAY_COPY' | translate }}</span>
    <a ng-click="$ctrl.closeTrigger()" class="btn-overlay" href>
        <span class="btn-inner-text">
            {{ 'PREVIEW_OVERLAY_CLOSE' | translate }}
        </span>
    </a>
</div>\
`,
};

angular.module('pipeline').component('previewOverlay', previewOverlay);
