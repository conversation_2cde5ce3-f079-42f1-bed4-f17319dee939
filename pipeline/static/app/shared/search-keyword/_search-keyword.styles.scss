@use "sass:color";

.search-keyword {
    .icon-search-icon {
        transition: color $link-transition-duration $ease-in-out-quad;
        color: $shortlist-grey-2;
        font-size: 20px;
        vertical-align: text-top;
    }

    .icon-cross-icon {
        font-size: 11px;
        vertical-align: middle;
        color: color.adjust($shortlist-grey-2, $lightness: -15);
        cursor: pointer;
    }

    .form-control {
        @include placeholder {
            opacity: 0.75;
        }

        &::-ms-clear {
            display: none;
        }

        display: inline-block;
        background: none;
        border: none;
        width: 240px;
        margin-left: 5px;
        padding: 0;
    }

    &.with-keyword {
        .icon-search-icon {
            transition: color $link-transition-duration $ease-in-out-quad;
            color: color.adjust($shortlist-grey-2, $lightness: -15);
        }
    }
}
