.input-calendar {
    $border-radius: 6px;

    &--weekly {

        .input-calendar {

            &__weekdays {
                margin-bottom: 0;
            }

            &__day {
                color: $shortlist-grey-1;
                border-top: 0;
                border-left: 0;
                border-right: 0 !important;
                border-bottom: 0 !important;
                padding: 5px 5px 0 0;
                min-height: auto;

                &--empty-value {
                    color: $shortlist-grey-2;
                }
            }

            &__day-preview {
                margin-top: 0;
                margin-left: 0;
            }

            &__day-number {
                display: none;
            }
        }
    }

    &:not(.input-calendar--read-only) {

        .input-calendar__day:hover {
            cursor: pointer;
        }
    }

    &__day, &__weekday {
        float: left;
        width: (100% / 7);
    }

    &__header {
        text-align: center;
        margin-bottom: 15px;

        &__month {
            vertical-align: super;
            font-weight: 600;
            font-size: 18px;
            width: 250px;
            display: inline-block;
        }

        a {
            color: $shortlist-grey-2;
            cursor: pointer;
        }
    }

    &__weekdays {
        color: $shortlist-grey-2;
        font-size: 12px;
        font-weight: 600;
        margin-bottom: 15px;
    }

    &__day {
        position: relative;
        border-left: 1px solid $shortlist-grey-3;
        border-top: 1px solid $shortlist-grey-3;
        padding: 30px 5px 10px 5px;
        min-height: 90px;
        color: $shortlist-grey-3;

        &:nth-child(1) {
            border-radius: $border-radius 0 0 0;
        }

        &:nth-child(7) {
            border-radius: 0 $border-radius 0 0;
        }

        &:nth-last-child(7) {
            border-radius: 0 0 0 $border-radius;
        }

        &:last-child {
            border-radius: 0 0 $border-radius 0;
        }

        &:nth-child(7n) {
            border-right: 1px solid $shortlist-grey-3;
        }

        &:nth-last-child(-n+7) {
            border-bottom: 1px solid $shortlist-grey-3;
        }

        &--current-month {
            color: $shortlist-grey-1;
        }

        &--selected {
            background-color: $shortlist-blue-4;
        }

        &--today {

            .input-calendar__day-number {
                border-radius: 20px;
                background-color: $shortlist-blue-1;
                color: $white;
            }
        }
    }

    &__day-preview {
        margin-top: 13px;
        margin-left: 14px;
    }

    &__day-number {
        font: 700 11px 'Montserrat';
        position: absolute;
        left: 5px;
        top: 5px;
        width: 20px;
        height: 20px;
        text-align: center;
        padding: 3px;
    }
}

.input-error, .input-error:hover, .input-error:focus {
    border-color: $pink-1;
}

.error {
  font-weight: 600;
  font-size: 12px;
  color: $pink-1;
  line-height: 20px;
  margin-top: 5px;
}
