.vendor-availability-input {
    border-radius: 3px;
    text-align: center;
    font-size: 11px;
    font-weight: bold;
    position: relative;
    line-height: 35px;
    padding-right: 37px;
    width: 100%;
    max-width: 250px;

    &--large {

        .dropdown-menu {
            width: 170% !important;
            margin-left: -25%;
        }
    }

    &--read-only {
        padding-right: 0;

        .vendor-availability-input {

            &__anchor {
                cursor: default;
            }

            &__dropdown__arrow {
                display: none;
            }
        }
    }

    &__anchor {
        color: $white;

        &:hover, &:focus {
            color: $white;
        }
    }

    &__status {
        text-transform: uppercase;
    }

    &__dropdown {

        &__arrow {
            position: absolute;
            right: 0;
            font-size: 8px;
            color: rgba(255, 255, 255, 0.5);
            border-left: 1px solid rgba(255, 255, 255, 0.5);
            padding: 0px 14px;

            &:before {
                content: "▼";
            }
        }

        .dropdown-menu {
            top: -1px;
            margin-top: 1px;
            font-size: 12px;
            font-weight: normal;
            color: $shortlist-grey-1;
            width: 100%;

            a {
                font-weight: 600;
            }
        }
    }

    &__availability-updated, &__availability-requested {
        margin-top: 5px;
        color: $shortlist-grey-2;
        font-style: italic;
        font-size: 12px;
    }

    &__ask-for-update {
        margin-top: 5px;
        font-size: 12px;
        font-weight: 600;
    }

    &__soon-available {
        border-radius: 3px;
        text-align: center;
        font-size: 11px !important;
        font-weight: bold;
        position: relative;
        line-height: 35px !important;
        color: $white !important;
        margin: 0 20px;
        text-transform: uppercase;
        background-color: $orange-1 !important;
        font-weight: bold !important;

        &:hover, &:focus {
            color: $white;
            background-color: $orange-1 !important;
        }
    }

    &--not-set {
        background-color: $shortlist-grey-2;
    }

    &--available, &--available-partially {
        background-color: $shortlist-green;
    }

    &--not-available {
        background-color: $pink-1;
    }

    &--soon-available {
        background-color: $orange-1;
    }
}
