import { RedliningMentionable } from '@/RedliningEditor.types';
import { cn, withRef } from '@udecode/cn';
import { PlateElement, getHandler, useElement } from '@udecode/plate-common';
import { TMentionElement } from '@udecode/plate-mention';
import React, { useContext, useMemo } from 'react';
import {
    CommentsControlsContext,
    getSuggestionItemMeta,
} from '../../CommentControls';

const COMMENT_ATTRIBUTE_PREFIX = 'comment_';

export const MentionElement = withRef<
    typeof PlateElement,
    {
        prefix?: string;
        onClick?: (mentionNode: any) => void;
        renderLabel?: (mentionable: TMentionElement) => string;
    }
>(({ children, prefix, renderLabel, className, onClick, ...props }, ref) => {
    const element = useElement<TMentionElement>();

    const { mentionItems, mentionItemValues, signatureItems, messages } =
        useContext(CommentsControlsContext);

    const { isAvailable, isSignature, isDelete, mention } = useMemo(() => {
        const elementValue = element.value.toString();
        const commentId = getCommentIdFromChildren(element.children);

        const mention = mentionItems.find(
            // for some reason `item.key` was of type `number` so casting it to `string`
            ({ key }: RedliningMentionable) => key.toString() === elementValue
        );
        const signature = signatureItems.find(
            ({ text }: RedliningMentionable) => text === elementValue
        );
        const message = messages.find(({ id }) => id === commentId);

        if (!mention && !signature) {
            return {
                isAvailable: false,
                isSignature: false,
                mention: undefined,
            };
        }
        if (signature) {
            const meta = getSuggestionItemMeta(message);
            return {
                isAvailable: true,
                isSignature: true,
                isDelete: message ? meta.isDelete || meta.isReplace : false,
                mention: {
                    ...signature,
                    label: getSignatureLabel(signature),
                },
            };
        }
        // if `mentionItemValues` is not provided, means we are in agreement template editor
        if (mentionItemValues) {
            const fieldValue = mentionItemValues[elementValue];
            if (!fieldValue || fieldValue.length === 0) {
                return {
                    isAvailable: false,
                    isSignature: false,
                    mention: {
                        ...mention,
                        label: `${mention.label} (not found)`,
                    },
                };
            }
            return {
                isAvailable: true,
                isSignature: false,
                mention: {
                    ...mention,
                    label: fieldValue,
                },
            };
        }
        return {
            isAvailable: true,
            isSignature: false,
            mention,
        };
    }, [signatureItems, mentionItems, element, mentionItemValues, messages]);

    return (
        <PlateElement
            style={{
                display: 'inline',
                ...getMentionItemStyles(isAvailable, isSignature, isDelete),
            }}
            ref={ref}
            className={cn(
                'inline-block cursor-default rounded-md px-1.5 py-0.5 align-baseline text-sm font-medium',
                element.children[0].bold === true && 'font-bold',
                element.children[0].italic === true && 'italic',
                element.children[0].underline === true && 'underline'
            )}
            data-slate-value={element.value}
            contentEditable={false}
            onClick={getHandler(onClick, element)}
            {...props}
        >
            {prefix}
            {renderLabel
                ? renderLabel(element)
                : mention?.label
                  ? mention.label
                  : element.value}
            {children}
        </PlateElement>
    );
});

function getMentionItemStyles(
    isAvailable: boolean,
    isSignature: boolean,
    isDelete: boolean
) {
    const commonStyles = {
        fontSize: '1rem',
    };

    if (isSignature) {
        if (isDelete) {
            return {
                ...commonStyles,
                backgroundColor:
                    'var(--fills-canvas-fills-alerts-canvas-fill-danger)',
                textDecoration: 'line-through',
                fontStyle: 'italic',
                color: 'var(--glyphs-alerts-glyphs-danger)',
            };
        }
        return {
            ...commonStyles,
            backgroundColor:
                'var(--fills-canvas-fills-alerts-canvas-fill-info)',
            fontStyle: 'italic',
            color: 'var(--glyphs-alerts-glyphs-neutral)',
        };
    }
    if (isAvailable) {
        return {
            ...commonStyles,
            backgroundColor:
                'var(--fills-canvas-fills-alerts-canvas-fill-success)',
        };
    }
    return {
        ...commonStyles,
        backgroundColor: 'var(--fills-canvas-fills-alerts-canvas-fill-danger)',
    };
}

function getSignatureLabel(signature: RedliningMentionable) {
    const PREFIX = 'Signature:';
    if (signature.key === 'ALL_SIGNATURES') {
        return signature.label;
    }
    if (signature.context) {
        return `${PREFIX} ${signature.label} (${signature.context})`;
    }
    return `${PREFIX} ${signature.label}`;
}

function getCommentIdFromChildren(children?: unknown[]) {
    const elementWithComment = children?.find((child) =>
        Object.keys(child).find((key) =>
            key.startsWith(COMMENT_ATTRIBUTE_PREFIX)
        )
    );

    return elementWithComment
        ? Number(
              Object.keys(elementWithComment)
                  .filter((node) => node.startsWith(COMMENT_ATTRIBUTE_PREFIX))
                  .reverse()
                  .reduce(
                      (_, commentId) =>
                          commentId.replace(COMMENT_ATTRIBUTE_PREFIX, ''),
                      ''
                  )
          )
        : null;
}
