import { TCommentText } from '@udecode/plate-comments';
import { TText, createPluginFactory } from '@udecode/plate-common';
import { Editor, Transforms } from 'slate';
import { ReactEditor } from 'slate-react';

let clickStart = { x: 0, y: 0 };
export const SUGGESTION_ADD_MARKER = '⁆⁅';
export const createCursorPlacementPlugin = createPluginFactory({
    key: 'cursorPlacement',
    handlers: {
        onMouseDown:
            (editor) =>
            ({ clientX, clientY }) => {
                removeTextFromEditor(editor, SUGGESTION_ADD_MARKER);
                clickStart = { x: clientX, y: clientY };
            },
        onMouseUp:
            (editor) =>
            ({ clientX, clientY }) => {
                // Same position as click start, it's a click
                if (clientX === clickStart.x && clientY === clickStart.y) {
                    let domRange;
                    let caretPosition;

                    // @ts-ignore
                    if (document.caretPositionFromPoint) {
                        // @ts-ignore
                        caretPosition = document.caretPositionFromPoint(
                            clientX,
                            clientY
                        );
                        if (caretPosition) {
                            domRange = document.createRange();
                            domRange.setStart(
                                caretPosition.offsetNode,
                                caretPosition.offset
                            );
                            domRange.setEnd(
                                caretPosition.offsetNode,
                                caretPosition.offset
                            );
                        }
                    } else if (document.caretRangeFromPoint) {
                        caretPosition = document.caretRangeFromPoint(
                            clientX,
                            clientY
                        );
                        if (caretPosition) {
                            domRange = caretPosition;
                        }
                    }

                    if (caretPosition) {
                        const slatePoint = ReactEditor.toSlatePoint(
                            editor,
                            [domRange.startContainer, domRange.startOffset],
                            { exactMatch: false, suppressThrow: true }
                        );

                        if (slatePoint) {
                            const { path, offset } = slatePoint;
                            const [node] = Editor.node(editor, path);

                            // Clicking close to the other suggestions, will add marker inside of it, we don't want that
                            if ((node as TCommentText).comment) {
                                return;
                            }

                            Transforms.insertText(
                                editor,
                                SUGGESTION_ADD_MARKER,
                                {
                                    at: { path, offset },
                                }
                            );
                        }
                    }
                }
            },
    },
});

export function removeTextFromEditor(editor: Editor, text: string) {
    const [nodeEntry] = Editor.nodes<TText>(editor, {
        at: [],
        match: (n: TText) => n.text && n.text.includes(text),
    });
    if (nodeEntry) {
        const [node, path]: [TText, number[]] = nodeEntry;
        const startIndex = node.text.indexOf(text);
        const endIndex = startIndex + text.length;

        Transforms.delete(editor, {
            at: {
                anchor: { path, offset: startIndex },
                focus: { path, offset: endIndex },
            },
        });
    }
}
