import { CommentsControlsContext } from '@/CommentControls';
import { SlateContent } from '@/RedliningEditor.types';
import { MARK_COMMENT, getCommentKey } from '@udecode/plate-comments';
import {
    PlateEditor,
    TText,
    nanoid,
    setNodes,
    useEditorRef,
    useOnClickOutside,
} from '@udecode/plate-common';
import React from 'react';
import { useContext, useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { Range, Editor as SlateEditor, Text, Transforms } from 'slate';
import { ReactEditor } from 'slate-react';
import { Button } from './button';
import {
    CommentToolbarButtonStyle,
    removeTemporaryCommentIds,
} from './comment-toolbar-button';
import {
    SUGGESTION_ADD_MARKER,
    removeTextFromEditor,
} from './cursor-placement';

const TOOLBAR_VERTICAL_OFFSET = 40;
const TOOLBAR_HORIZONTAL_OFFSET = 20;

export const FloatingToolbarAdd = ({
    markerText,
}: {
    markerText: string;
}): JSX.Element => {
    const editor = useEditorRef();
    const [position, setPosition] = useState<{
        top: number;
        left: number;
    } | null>(null);
    const {
        setCommentActionType,
        setActiveCommentId,
        activeCommentId,
        setEditorValue,
    } = useContext(CommentsControlsContext);

    useEffect(() => {
        const updatePosition = () => {
            try {
                const rect = getMarkerRect(editor, markerText);
                if (rect) {
                    setPosition({
                        top: rect.top - TOOLBAR_VERTICAL_OFFSET,
                        left: rect.left - TOOLBAR_HORIZONTAL_OFFSET,
                    });
                } else {
                    setPosition(null);
                }
            } catch (e) {
                setPosition(null);
            }
        };

        updatePosition();

        window.addEventListener('scroll', updatePosition);
        window.addEventListener('resize', updatePosition);
        return () => {
            window.removeEventListener('scroll', updatePosition);
            window.removeEventListener('resize', updatePosition);
        };
    }, [editor.children, markerText]);

    // Hide toolbar when activeCommentId is set, means user clicked on `Suggest Adding Text` button
    useEffect(() => setPosition(null), [activeCommentId]);

    const ref = useOnClickOutside(() => {
        setPosition(null);
        removeTextFromEditor(editor, SUGGESTION_ADD_MARKER);
        setActiveCommentId(null);
        setCommentActionType(null);
    });

    if (!position) {
        return null;
    }

    const toolbar = (
        <div
            ref={ref}
            className="flex select-none items-center gap-1 absolute whitespace-nowrap border bg-popover px-1.5 py-1.5 opacity-100 shadow-md print:hidden z-[1300]"
            style={{
                top: position.top + window.scrollY,
                left: position.left + window.scrollX,
            }}
        >
            <Button
                style={CommentToolbarButtonStyle}
                onClick={() => {
                    setCommentActionType('add');
                    const commentId = nanoid();
                    setActiveCommentId(commentId);
                    removeTemporaryCommentIds(editor);
                    addCommentIdToText(editor, markerText, {
                        [MARK_COMMENT]: true,
                        [getCommentKey(commentId)]: true,
                    });
                    setEditorValue(editor.children as unknown as SlateContent);
                }}
                data-testid="RedliningEditor_AddTrigger"
            >
                Suggest Adding Text
            </Button>
        </div>
    );
    return createPortal(toolbar, document.body);
};

function addCommentIdToText(editor, searchText, props) {
    const { selection } = editor;

    for (const [node, path] of SlateEditor.nodes(editor, {
        at: [],
        match: (n) => Text.isText(n) && n.text.includes(searchText),
    })) {
        const { text } = node as TText;
        let start = 0;
        let index = text.indexOf(searchText, start);

        while (index !== -1) {
            const range = {
                anchor: { path, offset: index },
                focus: { path, offset: index + searchText.length },
            };

            setNodes(editor, props, {
                at: range,
                match: Text.isText,
                split: true,
            });

            start = index + searchText.length;
            index = text.indexOf(searchText, start);
        }
    }

    if (selection) {
        Transforms.select(editor, selection);
    }
}

function getMarkerRect(editor: PlateEditor, markerText: string) {
    const [nodeEntry] = SlateEditor.nodes<TText>(editor, {
        at: [],
        match: (n: TText) => n.text && n.text.includes(markerText),
    });
    if (!nodeEntry) {
        return null;
    }

    const [node, path]: [TText, number[]] = nodeEntry;
    const startIndex = node.text.indexOf(markerText);

    const markerRange: Range = {
        anchor: { path, offset: startIndex },
        focus: { path, offset: startIndex + markerText.length },
    };

    const domRange = ReactEditor.toDOMRange(editor, markerRange);
    return domRange.getBoundingClientRect();
}
