'use client';

import { CommentsControlsContext } from '@/CommentControls';
import { isTemporarySlateCommentId } from '@/RedliningEditor.helpers';
import { SlateContent } from '@/RedliningEditor.types';
import {
    TCommentText,
    unsetCommentNodesById,
    useCommentAddButton,
} from '@udecode/plate-comments';
import {
    PlateEditor,
    collapseSelection,
    useEditorRef,
} from '@udecode/plate-common';
import { useContext } from 'react';
import { Editor } from 'slate';
import { ToolbarButton } from './toolbar';

export function removeTemporaryCommentIds(editor) {
    const processNode = (node) => {
        Object.keys(node).forEach((key) => {
            if (key.startsWith('comment_')) {
                const commentId = key.substring('comment_'.length);
                if (isTemporarySlateCommentId(commentId)) {
                    unsetCommentNodesById(editor, { id: commentId });
                }
            }
        });
        if (node.children) {
            node.children.forEach(processNode);
        }
    };
    editor.children.forEach(processNode);
}

function getFirstTemporaryCommentId(editor) {
    let firstTemporaryCommentId = null;
    const findTemporaryCommentId = (node) => {
        Object.keys(node).some((key) => {
            if (key.startsWith('comment_')) {
                const commentId = key.substring('comment_'.length);
                if (isTemporarySlateCommentId(commentId)) {
                    firstTemporaryCommentId = commentId;
                    return true;
                }
            }
            return false;
        });
        if (!firstTemporaryCommentId && node.children) {
            return node.children.some(findTemporaryCommentId);
        }
        return false;
    };
    editor.children.some(findTemporaryCommentId);
    return firstTemporaryCommentId;
}

export const CommentToolbarButtonStyle = {
    backgroundColor:
        'rgb(from var(--actions-basic-actions-action-base) r g b / 0.07)',
    color: 'var(--actions-basic-actions-action-base)',
    fontWeight: 600,
    fontSize: '12px',
    lineHeight: '16.5px',
    height: '30px',
};

const getSelectedNodes = (editor: PlateEditor) => {
    if (!editor.selection) {
        return [];
    }
    const [start, end] = Editor.edges(editor, editor.selection);
    const nodes = [
        ...Editor.nodes(editor, { at: { anchor: start, focus: end } }),
    ];
    return nodes.map(([node]) => node);
};

export const isSelectedTextCommented = (editor: PlateEditor) =>
    getSelectedNodes(editor).some((node: TCommentText) => node.comment);

export function CommentToolbarButton() {
    const editor = useEditorRef();
    const { hidden, props } = useCommentAddButton();
    const {
        setCommentActionType,
        availableActions,
        setActiveCommentId,
        setEditorValue,
        alertTriggers: { displayFailureMessage },
    } = useContext(CommentsControlsContext);

    const handleClick = (event, type) => {
        if (isSelectedTextCommented(editor)) {
            collapseSelection(editor);
            displayFailureMessage(
                'Please wait for the current suggestion to be resolved before making changes.'
            );
            return;
        }
        removeTemporaryCommentIds(editor);
        props.onClick(event);
        setEditorValue(editor.children as unknown as SlateContent);
        setActiveCommentId(getFirstTemporaryCommentId(editor));
        setCommentActionType(type);
    };

    if (hidden) {
        return null;
    }

    return (
        <div style={{ display: 'flex', padding: '5px 2px', gap: '5px' }}>
            {availableActions.includes('quote') && (
                <ToolbarButton
                    style={CommentToolbarButtonStyle}
                    {...props}
                    onClick={(event) => handleClick(event, 'quote')}
                    data-testid="RedliningEditor_CommentTrigger"
                >
                    Quote In Comment
                </ToolbarButton>
            )}
            {availableActions.includes('replace') && (
                <ToolbarButton
                    style={CommentToolbarButtonStyle}
                    {...props}
                    onClick={(event) => handleClick(event, 'replace')}
                    data-testid="RedliningEditor_ReplaceTrigger"
                >
                    Suggest Edit
                </ToolbarButton>
            )}
            {availableActions.includes('remove') && (
                <ToolbarButton
                    style={CommentToolbarButtonStyle}
                    {...props}
                    onClick={(event) => handleClick(event, 'remove')}
                    data-testid="RedliningEditor_RemoveTrigger"
                >
                    Suggest Remove
                </ToolbarButton>
            )}
        </div>
    );
}
