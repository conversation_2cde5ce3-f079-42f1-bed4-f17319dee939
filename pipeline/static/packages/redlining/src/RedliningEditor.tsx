import PlateEditor from '@/components/plate-editor';
import { TooltipProvider } from '@/components/plate-ui/tooltip';
import '@/styles/globals.css';
import { PlateEditor as PlateEditorType } from '@udecode/plate-common';
import {
    KeyboardEvent,
    MouseEvent,
    RefObject,
    useCallback,
    useEffect,
    useRef,
} from 'react';
import { Editor as SlateEditor, Transforms } from 'slate';
import { ReactEditor } from 'slate-react';
import { SlateContent } from './RedliningEditor.types';
import { Mentionable } from './lib/plate/mentionables';

interface RedliningEditorProps {
    readOnly?: boolean;
    mentionItems?: Mentionable[];
    signatures?: Mentionable[];
    onChange?: (value: SlateContent) => void;
    defaultValue?: any;
    commentingEnabled?: boolean;
    editorRef?: RefObject<PlateEditorType>;
    isEditorBlocked?: boolean;
    isEditorLockedByCurrentUser?: boolean;
    onEditorClick?: (event: MouseEvent) => Promise<boolean>;
}
export const RedliningEditor = ({
    readOnly = false,
    mentionItems = [],
    signatures = [],
    defaultValue = {},
    onChange,
    commentingEnabled = false,
    editorRef,
    isEditorBlocked,
    isEditorLockedByCurrentUser,
    onEditorClick,
}: RedliningEditorProps) => {
    const containerRef = useRef(null);
    const isCommentingEnabled = commentingEnabled && !isEditorBlocked;
    const isOnClickEnabled =
        !isEditorBlocked && (commentingEnabled || !readOnly);

    const handleEditorFocus = (event: MouseEvent) => {
        if (
            !readOnly &&
            editorRef?.current &&
            // This needs to be check, otherwise clicking inside the agreement will move the cursor to the end
            event.target === event.currentTarget
        ) {
            const endPoint = SlateEditor.end(editorRef.current, []);
            Transforms.select(editorRef.current, endPoint);
            ReactEditor.focus(editorRef.current);
        }
    };

    const handleEditorClick = (event: MouseEvent) => {
        if (isEditorBlocked) {
            ReactEditor.blur(editorRef.current);
            return false;
        }
        if (onEditorClick && isOnClickEnabled) {
            onEditorClick(event).then((isLockSuccessful) => {
                if (isLockSuccessful) {
                    handleEditorFocus(event);
                }
            });
        } else {
            handleEditorFocus(event);
        }
    };

    // @todo - Blur should be executed when editor is not locked
    useEffect(() => {
        if (isEditorBlocked) {
            ReactEditor.blur(editorRef.current);
        }
    }, [isEditorBlocked]);

    // Use capture phase to intercept events before they reach child elements
    const handleCaptureEvents = useCallback(
        (event: MouseEvent | KeyboardEvent) => {
            if (isEditorBlocked) {
                ReactEditor.blur(editorRef.current);
                event.stopPropagation();
                event.preventDefault();
                return false;
            }
        },
        [isEditorBlocked]
    );

    return (
        <div
            onClick={handleEditorClick}
            onClickCapture={handleCaptureEvents}
            onMouseDownCapture={handleCaptureEvents}
            onKeyDownCapture={handleCaptureEvents}
            className="redlining-editor flex-grow rounded-lg border bg-background shadow"
            style={{
                outline: isEditorLockedByCurrentUser
                    ? '1px solid var(--action-base)'
                    : undefined,
            }}
        >
            <TooltipProvider>
                <PlateEditor
                    defaultValue={defaultValue}
                    editorRef={editorRef}
                    ref={containerRef}
                    editorReadOnly={readOnly}
                    isBlocked={isEditorBlocked}
                    mentionItems={mentionItems}
                    signatures={signatures}
                    onChange={onChange}
                    commentingEnabled={isCommentingEnabled}
                />
            </TooltipProvider>
        </div>
    );
};
