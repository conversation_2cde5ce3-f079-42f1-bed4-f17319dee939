import {
    MAR<PERSON>_BOLD,
    MAR<PERSON>_CODE,
    MARK_ITALIC,
    MARK_UNDERLINE,
} from '@udecode/plate-basic-marks';
import { MARK_HIGHLIGHT } from '@udecode/plate-highlight';

import { MyAutoformatRule } from '@/lib/plate/plate-types';

export const autoformatMarks: MyAutoformatRule[] = [
    {
        mode: 'mark',
        type: [MARK_BOLD, MARK_ITALIC],
        match: '***',
    },
    {
        mode: 'mark',
        type: [MARK_UNDERLINE, MARK_ITALIC],
        match: '__*',
    },
    {
        mode: 'mark',
        type: [MARK_UNDERLINE, MARK_BOLD],
        match: '__**',
    },
    {
        mode: 'mark',
        type: [MARK_UNDERLINE, MARK_BOLD, MARK_ITALIC],
        match: '___***',
    },
    {
        mode: 'mark',
        type: MARK_BOLD,
        match: '**',
    },
    {
        mode: 'mark',
        type: MAR<PERSON>_UNDERLINE,
        match: '__',
    },
    {
        mode: 'mark',
        type: MAR<PERSON>_ITALIC,
        match: '*',
    },
    {
        mode: 'mark',
        type: MARK_ITALIC,
        match: '_',
    },
    {
        mode: 'mark',
        type: MARK_HIGHLIGHT,
        match: '==',
    },
    {
        mode: 'mark',
        type: MARK_HIGHLIGHT,
        match: '≡',
    },
    {
        mode: 'mark',
        type: MARK_CODE,
        match: '`',
    },
];
