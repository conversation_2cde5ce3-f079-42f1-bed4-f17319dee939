import { Message } from '../../components/Alert/Alert.types';
import produce from 'immer';

const defaultState = {
  notifications: [],
};

export enum NotificationStateAction {
  ADD_NOTIFICATION = 'ADD_NOTIFICATION',
  REMOVE_FIRST_NOTIFICATION = 'SHIFT_NOTIFICATION',
  CLEAR_NOTIFICATIONS = 'CLEAR_NOTIFICATIONS',
}

export type notificationState = {
  notifications: Message[];
};

export const notificationReducer = (
  state: notificationState = defaultState,
  action: {
    type: NotificationStateAction;
    data: Message;
  }
) => {
  switch (action.type) {
    case NotificationStateAction.ADD_NOTIFICATION:
      return produce(state, (draft) => {
        draft.notifications.push(action.data);
      });
    case NotificationStateAction.REMOVE_FIRST_NOTIFICATION:
      return produce(state, (draft) => {
        draft.notifications.shift();
      });
    case NotificationStateAction.CLEAR_NOTIFICATIONS:
      return produce(state, (draft) => {
        draft.notifications = [];
      });
    default:
      return state;
  }
};
