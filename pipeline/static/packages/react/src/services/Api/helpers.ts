import { Entity } from '@Worksuite/Features/Entities/types/entity';
import { CountriesType } from '@Worksuite/Features/TaxInformation/types/tax-information';
import { TimesheetPresets } from '@Worksuite/Features/Timesheets/types/timesheets';
import { AxiosResponse } from 'axios';
import cloneDeep from 'lodash/cloneDeep';
import { VendorCustomField } from '../../components/VendorCustomFields/VendorCustomFields.types';
import { CustomFieldsTemplate } from '../../types/custom-fields-templates';
import { CustomRateType } from '../../types/custom-rates';
import { IntegrationsState } from '../../types/integrations';
import { CustomNotification } from '../../types/notifications';
import { PublicPageSettings } from '../../types/public';
import { Preference, SessionUser, UserRole } from '../../types/user';
import { Rank, VendorType } from '../../types/vendor';
import { WorkingHourRange } from '../../types/working-hours';
import { FilePickerBackendConfig } from '../Filepicker/filepicker';
import { reduxStore } from '../Redux';
import { get } from './base';

const customFieldHelpersPromise = import(
  '../../components/CustomFields/custom-fields.helpers'
);
let mapOtherFieldsToCustomFieldsFn = null;
customFieldHelpersPromise.then(
  ({ mapOtherFieldsToCustomFields }) =>
    (mapOtherFieldsToCustomFieldsFn = mapOtherFieldsToCustomFields)
);
const scopedMapOtherFieldsToCustomFields = async (url: string) => {
  if (!mapOtherFieldsToCustomFieldsFn) {
    const { mapOtherFieldsToCustomFields } = await customFieldHelpersPromise;
    mapOtherFieldsToCustomFieldsFn = mapOtherFieldsToCustomFields;
  }
  return mapOtherFieldsToCustomFieldsFn(url);
};

const cachedData =
  window.__REDUX_GET_CACHED_DATA__ ||
  (window.__REDUX_GET_CACHED_DATA__ = new Map());

const cacheDefinitions: Record<
  keyof CachedPromiseType,
  { get: any; depends?: (keyof CachedPromiseType)[] }
> = {
  '/vendor_types/': { get },
  '/custom_rate_types/': { get },
  '/v/custom_rate_types/': { get },
  '/custom_notifications/default_notifications/': { get },
  '/ranks/': { get },
  '/ranks/::enabled': {
    get: async () =>
      getCached('/ranks/').then((res) => ({
        ...res,
        data: res.data.filter((i) => i.enabled),
      })),
    depends: ['/ranks/'],
  },
  '/agreements/requests/upload_file_config/': { get },
  '/preferences/': { get },
  '/roles/': { get },
  '/tools/custom_vendor_fields/': { get },
  '/tools/working_hour_ranges/': { get },
  '/tools/countries/': { get },
  '/public/publicpagesettings/': { get },
  'Timesheets.getTimesheetPresets': {
    get: async () => {
      const {
        default: { getTimesheetPresets },
      } = await import('./Timesheets');
      return getTimesheetPresets();
    },
  },
  'Entities.getCurrentEntities': {
    get: async () => {
      const {
        default: { getCurrentEntities },
      } = await import('@Worksuite/Features/Entities/services/Api/Entities');
      return getCurrentEntities();
    },
  },
  '/users/': { get },
  '/v/global_fields_templates/': {
    get: scopedMapOtherFieldsToCustomFields,
  },
  '/s/global_fields_templates/': {
    get: scopedMapOtherFieldsToCustomFields,
  },
  '/g/global_fields_templates/': {
    get: scopedMapOtherFieldsToCustomFields,
  },
  '/global_fields_templates/': {
    get: scopedMapOtherFieldsToCustomFields,
  },
  '/integrations/status/': { get },
};

export type CachedPromiseType = {
  '/vendor_types/': VendorType[];
  '/custom_rate_types/': CustomRateType[];
  '/v/custom_rate_types/': CustomRateType[];
  '/custom_notifications/default_notifications/': CustomNotification[];
  '/ranks/': Rank[];
  '/ranks/::enabled': Rank[];
  '/agreements/requests/upload_file_config/': FilePickerBackendConfig;
  '/preferences/': Preference[];
  '/roles/': UserRole[];
  '/tools/custom_vendor_fields/': VendorCustomField[];
  '/tools/working_hour_ranges/': WorkingHourRange[];
  '/tools/countries/': CountriesType;
  '/public/publicpagesettings/': PublicPageSettings;
  'Timesheets.getTimesheetPresets': TimesheetPresets;
  'Entities.getCurrentEntities': Entity[];
  '/users/': SessionUser[];
  '/v/global_fields_templates/': CustomFieldsTemplate[];
  '/s/global_fields_templates/': CustomFieldsTemplate[];
  '/g/global_fields_templates/': CustomFieldsTemplate[];
  '/global_fields_templates/': CustomFieldsTemplate[];
  '/integrations/status/': IntegrationsState;
};

const getCached = async <T extends keyof CachedPromiseType>(
  key: T,
  refresh = false
): Promise<AxiosResponse<CachedPromiseType[T]>> => {
  if (!cacheDefinitions[key]) {
    throw new Error(`Definition ${key} not found.`);
  }
  if (!cachedData.has(key) || refresh) {
    Object.entries(cacheDefinitions).forEach(([dependentKey, definition]) => {
      if (definition.depends && definition.depends.includes(key)) {
        cachedData.delete(dependentKey);
      }
    });
    const promise = cacheDefinitions[key]
      .get(key)
      .then(cloneDeep)
      .catch((error) => {
        // Remove the promise from cache if it fails
        cachedData.delete(key);
        throw error;
      });
    cachedData.set(key, promise);
  }
  return cachedData.get(key).then(cloneDeep);
};

const clearCache = () => {
  cachedData.clear();
};

export const ApiHelper = {
  getCached,
  clearCache,
};

export const getUploadConfig = (
  configData: FilePickerBackendConfig,
  pickerOptions = {}
) => {
  const env = reduxStore.getState().env;
  return {
    filePickerKey: env.filepickerKey,
    clientConfig: {
      security: {
        signature: configData.signature,
        policy: configData.policy,
      },
    },
    storageConfig: {
      region: configData.region,
      container: configData.bucket,
      path: configData.upload_path,
    },
    storageDomain: configData.domain,
    pickerOptions,
  };
};

const getPromiseValue = <S>(
  result: PromiseSettledResult<S>,
  fallbackValue: any
) => (result.status === 'fulfilled' ? result.value : fallbackValue);

export const allSettledWithFallback = <T extends unknown[] | []>(
  promises: T,
  fallbackValue = null
): Promise<{ [P in keyof T]: Awaited<T[P]> }> => {
  if (!Array.isArray(promises)) {
    return Promise.resolve(fallbackValue);
  }

  return Promise.allSettled(promises)
    .then((promiseReturns) =>
      promiseReturns.map((promiseValue: PromiseSettledResult<unknown>) =>
        getPromiseValue(promiseValue, fallbackValue)
      )
    )
    .catch(() => null);
};
