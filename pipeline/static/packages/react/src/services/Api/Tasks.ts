import {
  Task,
  TaskRequestData,
  TaskTemplate,
  TimesheetTemplate,
} from '../../types/task';
import { BackendFile } from '../../utils/files';
import { ApiPagedResponse, get, getApiPrefix, post } from './base';

const getTasks = async <T extends number = undefined>(params: {
  ids?: (number | string)[];
  ordering?: '-updated_at';
  page?: number;
  page_size?: T;
  contract_id?: number;
}) => {
  const { ids, ...rest } = params;
  return await get<T extends undefined ? Task[] : ApiPagedResponse<Task>>(
    `${getApiPrefix()}/tasks/`,
    {
      params: {
        ...rest,
        id: Array.isArray(ids) ? ids.join(',') : null,
      },
    }
  );
};

export default {
  single: (id: number | string) => get<Task>(`${getApiPrefix()}/tasks/${id}/`),

  multipleById: (ids: (number | string)[]) => getTasks({ ids }),

  getTasks,

  getTaskTemplates: <T extends keyof TaskTemplate>(params: {
    fields: T[];
    without_job_opening_templates: boolean;
  }) =>
    get<Pick<TaskTemplate, T>[]>('/task_templates/', {
      params: {
        ...params,
        fields: Array.isArray(params.fields) ? params.fields.join(',') : null,
      },
    }),

  postTriggerTaskAction: (
    action: string,
    taskId: Task['id'],
    params?: Record<string, unknown>
  ) => post(`${getApiPrefix()}/tasks/${taskId}/actions/`, { action, params }),

  createTask: (task: TaskRequestData) =>
    post<Task>(`${getApiPrefix()}/tasks/`, task),

  uploadTaskFile: ({
    fileName,
    path,
    taskId,
    placement = 'description',
    name,
    createTask,
  }: {
    fileName: string;
    path: string;
    taskId: number;
    placement?: string;
    name: string;
    createTask: boolean;
  }) => {
    let queryParams = '';
    if (createTask) {
      queryParams = '?no_history=1';
    }
    return post<BackendFile>(
      `${getApiPrefix()}/task_groups/attachments/${queryParams}`,
      {
        file_name: fileName,
        path,
        task: taskId,
        placement,
        name,
      }
    );
  },

  loadTimesheetTemplates: () =>
    get<TimesheetTemplate[]>('/timesheet/templates/'),
};
