import { filterOutOtherCustomFields } from '../../../components/CustomFields/custom-fields.helpers';
import { VendorCustomField } from '../../../components/VendorCustomFields/VendorCustomFields.types';
import { GlobalTemplateType } from '../../../types/custom-fields-templates';
import { PublicPageSettings } from '../../../types/public';
import { get } from '../base';
import Feedback from './Feedback';
import { AxiosResponse } from 'axios';
import produce from 'immer';

export default {
  Feedback,
};

export const publicPageSettings = () =>
  get<PublicPageSettings>('/public/publicpagesettings/').then((res) =>
    produce(res, (draft: any) => {
      draft.data.job_opening_list_fields_config = draft.data
        .job_opening_list_fields_config || [{ id: 'location' }];
    })
  );

export const globalFieldsTemplatesForTypes = async (
  types: GlobalTemplateType[]
) => {
  return get('/public/global_fields_templates/').then(
    (res: AxiosResponse<any>) => {
      const data = res.data
        .filter((i) => types.includes(i.global_template))
        .reduce(
          (template_fields, i) => [...template_fields, ...i.template_fields],
          []
        );
      return {
        res,
        data,
        getWithoutOtherFields: () => filterOutOtherCustomFields(data),
      };
    }
  );
};

export const getPublicCustomFields = (key: string) => {
  return get<VendorCustomField[]>(`/public/custom_vendor_fields/${key}/`);
};
