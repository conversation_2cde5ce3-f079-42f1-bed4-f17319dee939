import { apiClient } from '../base';
import MockAdapter from 'axios-mock-adapter';

describe('apiClient', () => {
  let apiMock;

  beforeAll(() => {
    apiMock = new MockAdapter(apiClient);
    apiMock
      .onGet(/\/api\/ranks\//gm)
      .reply(({ params: { expectedResponse } }) => [0, expectedResponse]);
  });

  afterAll(() => {
    apiMock.reset();
  });

  test.each`
    value
    ${''}
    ${null}
    ${undefined}
    ${{}}
  `('expected $value', ({ value }) => {
    return apiClient
      .get('/ranks/', { params: { expectedResponse: value } })
      .catch((data) => {
        expect(data).toStrictEqual(value);
      });
  });
});
