import { isSupportedCustomFieldTypeInputComponent } from '../../components/CustomFields/CustomFieldList/CustomFieldList.helpers';
import { CustomFieldsRecord } from '../../components/CustomFields/CustomFields';
import { GlobalTemplateType } from '../../types/custom-fields-templates';
import { Task } from '../../types/task';
import { getDate } from '../../utils/date';
import CustomFieldsTemplates from './CustomFieldsTemplates';
import Tasks from './Tasks';
import {
  TimeEntryAPIResponse,
  createTimeEntryAPIResponse,
} from './TimeTracking';
import { ApiPagedResponse, get, getApiPrefix, post } from './base';
import { WeeklyTimesheet } from '@Worksuite/Features/Timesheets/types/timesheets';
import { isAfter } from 'date-fns';

export type TimesheetWorkDataWithNotes = Record<
  string,
  {
    minutes: number;
    note?: string;
  }
>;

export interface ApiPost_TaskTimesheet {
  work_data: TimesheetWorkDataWithNotes;
}

export interface FieldMappingAttributes {
  name: string;
  label: string;
}

export interface TimesheetPaymentSummary {
  currency: string;
  count: number;
  amount: number;
  total: number;
}

export interface TimesheetPayment {
  number: string;
  custom_fields: CustomFieldsRecord;
  budget_rate_per_time_unit?: number;
  work_item?: {
    category: number;
    tax_percent: number;
    custom_fields: CustomFieldsRecord;
  };
}

export interface TimesheetPreset {
  label: string;
  fraction: number;
  hours: number;
  available: boolean;
  key?: string;
}

export interface TaskTimesheet {
  minutes_total: number;
  work_data: {
    id: number;
    date_worked: string;
    minutes_worked: number;
    note: string;
  }[];
}

type FieldMappingAttributesApi = FieldMappingAttributes[];

export interface TimesheetBulkApproveApi {
  total: number;
  can_change: number;
  can_generate_payment: number;
  can_generate_payment_summary: TimesheetPaymentSummary[];
}

type TimeEntriesListParams = {
  page?: number;
  page_size?: number;
  in_progress?: 'False' | 'True';
  timesheet: WeeklyTimesheet;
};

export default {
  getAllTasks: () =>
    get<Task[]>(`${getApiPrefix()}/tasks/`).then((response) => response.data),

  submitTaskWeekTimesheet: (
    taskId: number,
    taskTimesheet: ApiPost_TaskTimesheet
  ) =>
    post<WeeklyTimesheet>(
      `${getApiPrefix()}/tasks/${taskId}/task_time_sheet/`,
      taskTimesheet
    ),

  updateTaskWeekTimesheet: (
    taskId: number,
    taskTimesheet: ApiPost_TaskTimesheet
  ) =>
    post<WeeklyTimesheet>(
      `${getApiPrefix()}/tasks/${taskId}/task_time_sheet/`,
      {
        ...taskTimesheet,
        status: 'unsubmitted',
      }
    ),

  getTaskWeekTimesheet: (taskId: number) =>
    get<TaskTimesheet>(`${getApiPrefix()}/tasks/${taskId}/task_time_sheet/`),

  getTimesheetById: (id: number, handleGuestRequest = false) =>
    get<WeeklyTimesheet>(
      `${getApiPrefix({
        includeGuest: handleGuestRequest,
      })}/task_time_sheet_periods/${id}/`,
      {
        params: {
          with_tasks_details: 1,
        },
      }
    ),

  rejectTimesheet: ({
    id,
    reason,
    validateOnly,
    handleGuestRequest = false,
  }: {
    id: number;
    reason?: string;
    validateOnly?: boolean;
    handleGuestRequest?: boolean;
  }) =>
    post(
      `${getApiPrefix({
        includeGuest: handleGuestRequest,
      })}/task_time_sheet_periods/${id}/status/`,
      {
        status: 'rejected',
        rejection_reason: reason,
      },
      {
        params: {
          validate_only: validateOnly && 'true',
        },
      }
    ),

  getTimesheetPeriods: (params) =>
    get<ApiPagedResponse<WeeklyTimesheet>>(
      `${getApiPrefix()}/task_time_sheet_periods/`,
      {
        params,
      }
    ),

  getAllTimesheetForTask: async ({ task }: { task: Task }) =>
    Promise.all([
      get<WeeklyTimesheet[]>(`${getApiPrefix()}/task_time_sheet_periods/`, {
        params: { task: task.id },
      }),
      Tasks.single(task.id),
    ]).then(([res, { data: task }]) => ({
      data: res.data
        .map((i) => ({ ...i, task }))
        .sort((a, b) =>
          isAfter(getDate(b.date_start), getDate(a.date_start)) ? 1 : -1
        ),
    })),

  getFieldMappingAttributes: () =>
    Promise.all([
      CustomFieldsTemplates.listGlobalTemplatesForTypes([
        GlobalTemplateType.Payment,
      ]),
      get<FieldMappingAttributesApi>(
        `${getApiPrefix()}/task_time_sheet_periods/field_mapping_attributes/`
      ),
    ]).then(
      ([
        { getWithoutOtherFields: getPaymentCustomFields },
        { data: mentions },
      ]) => {
        /**
         * This is filtering out CF which are not supported as input components
         * User should not be able to use these as mentions because it is not possible to populate values
         */
        const paymentCustomFields = getPaymentCustomFields();
        return mentions.filter((i) => {
          const paymentCustomFieldPrefix = 'template_field.';
          if (i.name.indexOf(paymentCustomFieldPrefix) === 0) {
            const customFieldId = parseInt(
              i.name.substring(paymentCustomFieldPrefix.length),
              10
            );
            const customField = paymentCustomFields.find(
              (i) => i.id === customFieldId
            );
            return (
              customField &&
              isSupportedCustomFieldTypeInputComponent(customField.type)
            );
          }
          return true;
        });
      }
    ),

  getTimesheetPeriodsBulkApprove: (params) =>
    get<TimesheetBulkApproveApi>(
      `${getApiPrefix({
        includeGuest: true,
      })}/task_time_sheet_periods/bulk_approve/`,
      {
        params,
      }
    ),

  saveTimesheetPeriodsBulkApprove: (data) =>
    post<TimesheetBulkApproveApi>(
      `${getApiPrefix({
        includeGuest: true,
      })}/task_time_sheet_periods/bulk_approve/`,
      data
    ),

  getTaskTimesheetPeriods: ({
    date_end_from = null,
    date_start_to = null,
    taskId = null,
    paymentIds = null,
    withTasksDetails = null,
    handleGuestRequest = false,
  }: {
    date_end_from?: string;
    date_start_to?: string;
    taskId?: string;
    paymentIds?: (string | number)[];
    withTasksDetails?: boolean;
    handleGuestRequest?: boolean;
  }) =>
    get<WeeklyTimesheet[]>(
      `${getApiPrefix({
        includeGuest: handleGuestRequest,
      })}/task_time_sheet_periods/`,
      {
        params: {
          date_end_from,
          date_start_to,
          task: taskId,
          payments: paymentIds?.join(','),
          with_tasks_details: withTasksDetails,
        },
      }
    ),

  getTimeEntries: (params: TimeEntriesListParams) =>
    get<{ count: number; results: TimeEntryAPIResponse[] }>(
      '/time_tracking/time_entries/',
      {
        params: {
          timesheet_period: params.timesheet.id,
          ordering: 'start_time',
        },
      }
    ).then((response) => ({
      hits: response.data.results.map(createTimeEntryAPIResponse),
      filters: [],
      highlight: false,
      query: '',
      total: response.data.count,
      page: params.page,
      size: params.page_size,
      terms: {},
    })),

  getTimesheetPresets: async () =>
    get<TimesheetPreset[]>('/tools/timesheet_presets/').then((res) => {
      const allPresets = res.data.map((i) => ({
        ...i,
        key: `${i.hours}:00`,
      }));
      return {
        data: {
          allPresets,
          availablePresets: allPresets.filter((i) => i.available),
        },
      };
    }),
};
