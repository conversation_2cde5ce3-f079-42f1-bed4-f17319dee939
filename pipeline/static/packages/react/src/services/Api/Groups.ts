import produce from 'immer';
import maxBy from 'lodash/maxBy';
import { Group } from '../../types/group';
import { ListTemplateField, VendorList } from '../../types/list';
import { Vendor } from '../../types/vendor';
import { destroy, get, patch, post } from './base';

const updateList = (listSlug: string, list: Partial<VendorList>) =>
  patch<VendorList>(`/vendor_groups/${listSlug}/`, list);

export default {
  updateList,

  vendorGroups: ({
    fields,
    type,
    slugs,
  }: {
    fields: (keyof Group)[];
    type: 'public' | 'private';
    slugs?: string[];
  }) =>
    get<Partial<Group>[]>('/vendor_groups/', {
      params: {
        slug: slugs?.join(','),
        fields: fields.join(','),
        type,
      },
    }),

  addTemplateField: async (
    vendorList: VendorList,
    templateField: Partial<ListTemplateField>
  ) => {
    // Save new template field
    const { data: updatedVendorList } = await updateList(vendorList.slug, {
      list_custom_fields_template: produce(
        vendorList.list_custom_fields_template,
        (draft) => {
          const order = maxBy(draft.template_fields, 'order')?.order + 1 || 1;
          draft.template_fields.push({
            ...templateField,
            order,
          } as ListTemplateField);
        }
      ),
    });
    const {
      list_custom_fields_template: { template_fields: templateFields },
    } = updatedVendorList;
    return {
      vendorList: updatedVendorList,
      templateField: templateFields[templateFields.length - 1],
    };
  },

  removeTemplateField: async (
    vendorList: VendorList,
    templateFieldId: number
  ) => {
    const { data: updatedVendorList } = await updateList(vendorList.slug, {
      list_custom_fields_template: produce(
        vendorList.list_custom_fields_template,
        (draft) => {
          const index = draft.template_fields.findIndex(
            (i) => i.id === templateFieldId
          );
          if (index !== -1) {
            draft.template_fields.splice(index, 1);
          }
        }
      ),
    });
    return updatedVendorList;
  },

  shareList: (slug: string) =>
    post<string>(`/vendor_groups/${slug}/share_list/`),
  getListShareKey: (slug: string) =>
    get<string>(`/vendor_groups/${slug}/share_list/`),
  stopSharingList: (slug: string) =>
    destroy<string>(`/vendor_groups/${slug}/share_list/`),

  saveGroup: (group: Group): Promise<{ data: Group }> => {
    if (group.slug) {
      return patch(`/vendor_groups/${group.slug}`, group);
    } else {
      return post('/vendor_groups/', group);
    }
  },

  addGroup: (params: {
    name: string;
    type: 'public' | 'private';
    vendors?: Vendor[];
    sharable_template?: number;
  }): Promise<{ data: Group }> =>
    post<Group>('/vendor_groups/', {
      name: params.name,
      type: params.type,
      ...(params.vendors
        ? {
            vendors: params.vendors.map(({ id }) => id),
          }
        : {}),
      ...(params.sharable_template
        ? {
            sharable_template: params.sharable_template,
          }
        : {}),
    }),
};
