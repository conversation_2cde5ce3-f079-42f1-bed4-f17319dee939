import { Meta } from '@storybook/blocks';

<Meta title="Documentation/Development/React/Functional Components" />

### Functional Components
- Always use **functional components** instead of class components.
- Use **arrow functions** for consistency and binding `this` automatically.

```tsx
import React from 'react';

interface MyComponentProps {
  title: string;
}

const MyComponent = ({ title }: MyComponentProps) => {
  return <h1>{title}</h1>;
};

export default MyComponent;
