import { Meta } from '@storybook/blocks';

<Meta title="Documentation/Development/Project" />

## Project overview

Worksuite is a software platform designed to manage and streamline workforce operations, such as freelance talent management, project tracking, payroll, and compliance. It enables businesses to effectively collaborate with distributed teams and contractors.

## Project structure
```
├── assets // static files, including icons
├── components
├── features
├── fixtures // API response mocks for tests and stories
├── i18n // translations
├── services // API handlers
├── stories // global documentation
├── styles // global styles
├── types // global types
├── utils
```

## Project technology
- **Frontend**: AngularJS, React
- **Styling**: Sass, Bootstrap, styled-components
- **Bundling**: Webpack, Esbuild, Vite
- **Testing**: Vitest, Jest
