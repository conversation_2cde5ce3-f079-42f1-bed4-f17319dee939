import { Meta } from '@storybook/blocks';

<Meta title="Documentation/Code Review/Best Practices Reviewers" />

## **Best Practices for Reviewers**
- **Be Respectful and Constructive:**
  - Frame feedback positively and focus on the code, not the person.
  - Use examples to illustrate your points.

- **Be Thorough:**
  - Don’t skim; take time to understand the changes.
  - Check associated files (e.g., styles, tests, documentation).

- **Ask Questions:**
  - If something isn’t clear, ask for clarification rather than assuming.

- **Prioritize Critical Issues:**
  - Highlight blocking issues (e.g., bugs, security concerns) first.
  - Leave non-critical suggestions for improvement comments.
