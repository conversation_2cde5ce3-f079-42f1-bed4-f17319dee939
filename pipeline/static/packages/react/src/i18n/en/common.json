{"Actions": {"Add": "Add", "BulkAdd": "Bulk add", "Delete": "Delete", "Remove": "Remove", "Edit": "Edit", "Restore": "Rest<PERSON>", "Review": "Review", "Update": "Update", "Close": "Close", "Duplicate": "Duplicate", "Download": "Download", "Select": "Select", "Ok": "OK", "Proceed": "Proceed", "Preview": "Preview", "Logout": "Logout", "Reset": "Reset", "Back": "Back", "Reload": "Reload", "BackToEditing": "Back to editing", "SaveChanges": "Save changes", "SelectAField": "Select a field...", "AddDocument": "Add document"}, "Errors": {"InvalidEmailFormat": "Invalid email format", "DashboardNotEmpty": "Dashboard action name cannot be empty.", "InvalidURL": "Invalid URL", "FieldValueGreater": "{{field}} value should be greater than {{value}}", "FieldValueGreaterOrEqual": "{{field}} value should be greater or equal to {{value}}", "FieldValueSmallerOrEqual": "{{field}} value should be smaller or equal to {{value}}", "InvalidURLShouldStartWith": "Url must start with http:// or https://", "ValueCannotBeEmpty": "Value cannot be empty", "ThisFieldIsRequired": "This field is required", "CheckToProceed": "You need to check this field to proceed", "TaskTemplateRequired": "You need to choose a $t(terms:task) template to create a $t(terms:task).", "ProjectRequired": "You need to choose $t(terms:Project) to create a $t(terms:task).", "WrongDateFormat": "Enter a date in the format {{date}}", "StartDateCannotBeAfterEndDate": "Start date cannot be after end date", "StartDateCannotBeSameAsEndDate": "Start date cannot be the same as the end date", "StartDateIsMissing": "Start date is missing", "EndDateIsMissing": "End date is missing", "StartDateCannotBeLessThanCurrentDate": "Start date cannot be earlier than current date", "EndDateCannotBeLessThanCurrentDate": "End date cannot be earlier than current date", "InvalidEmail": "Email address is not correct", "EmailVendorExists": "A $t(terms:partner) with this email already exists. \nLink the existing $t(terms:partner) to continue.", "InternalError": "Internal error cannot validate this field. Please try again later.", "AORIsEndDateExceedingYear": "AOR $t(terms:contracts) cannot exceed of 1 year"}, "FeedbackState": {"GenericErrorHeader": "Error", "GenericErrorCopy": "Something went wrong. Please try again later.", "InvalidLinkHeader": "Invalid link", "InvalidLinkCopy": "The link you've provided is invalid or expired.", "DocumentSignedHeader": "Document signed", "DocumentSignedCopy": "The document has been signed successfully.", "PermissionDeniedHeader": "Permission denied", "PermissionDeniedCopy": "You don't have permission to view this page."}, "Form": {"Cancel": "Cancel", "Save": "Save", "Saving": "Saving...", "Leave": "Leave", "SaveAndClose": "Save & close", "Close": "Close", "GotIt": "Got it", "Confirm": "Confirm"}, "FormLabels": {"ActionURL": "Action URL", "Description": "Description", "JobOpeningTemplate": "$t(terms:Job opening) template", "Name": "Name", "Workflow": "Workflow"}, "FormPlaceholders": {"ChooseJobOpeningTemplate": "Choose $t(terms:job opening) template", "SelectWorkflow": "Select workflow"}, "Messages": {"PartnerRestored": "{{vendorName}} has been restored.", "PartnerArchived": "<b>{{vendorName}}</b> has been archived.", "PartnersArchived": "Selected $t(terms:Partners) have been archived.", "BulkMessage": "Messages have been sent.", "BulkMessages": "Messages have been sent.", "PartnerAddedToGroup": "$t(terms:Partner) has been added to the $t(terms:group) <a href=\"{{stateUrl}}\">{{groupName}}</a>", "PartnersAddedToGroup": "$t(terms:Partners) have been added to the $t(terms:group) <a href=\"{{stateUrl}}\">{{groupName}}</a>", "PartnerAddedToList": "$t(terms:Partner) has been added to the list <a href=\"{{stateUrl}}\">{{groupName}}</a>", "PartnersAddedToList": "$t(terms:Partners) have been added to the list <a href=\"{{stateUrl}}\">{{groupName}}</a>", "VendorTypeChanged": "$t(terms:Partner) types have been changed", "PartnerAddedToJobOne": "New $t(terms:Partner) has been added to the job <a href=\"{{stateUrl}}\">{{jobName}}</a>", "PartnerAddedToJobMany": "New $t(terms:Partners) have been added to the job <a href=\"{{stateUrl}}\">{{jobName}}</a>", "PartnerInvitedToJobOne": "New $t(terms:Partner) has been invited to the job <a href=\"{{stateUrl}}\">{{jobName}}</a>", "PartnerInvitedToJobMany": "New $t(terms:Partners) have been invited to the job <a href=\"{{stateUrl}}\">{{jobName}}</a>", "PartnerRankSet": "$t(terms:Partner) rank has been set.", "PartnersRankSet": "$t(terms:Partners) rank have been set.", "PartnerManuallySetAsCompliant": "$t(terms:Partner) manually set as compliant.", "PartnersManuallySetAsCompliant": "$t(terms:Partners) manually set as compliant.", "PartnerRemovedFromList": "$t(terms:Partner) has been removed from the list.", "PartnersRemovedFromList": "$t(terms:Partners) have been removed from the list.", "ComplianceOverrideDisabled": "Compliance override disabled.", "PartnerLoginLinkReset": "Login link for $t(terms:Partner) {{vendorName}} has been reset."}, "Other": {"Yes": "Yes", "No": "No"}, "TableFiltersCommonValues": {"draft": "Draft", "ongoing": "Ongoing", "completed": "Completed", "archived": "Archived", "pending": "Pending", "accepted": "Accepted", "rejected": "Rejected", "canceled": "Canceled", "unsubmitted": "Unsubmitted", "approved": "Approved", "under_review": "Under review", "published": "Open", "disabled": "Cancelled", "scheduled": "Scheduled"}, "Table": {"Description": "Description", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "Groups": "$t(terms:Groups)", "ListColumn": "(List)", "RequestedDocumentColumn": "(Document)", "Location": "Location", "Website": "Website", "Resume": "Resume", "Portfolio": "Portfolio", "ShowResume": "Show resume", "ShowPortfolio": "Show portfolio", "SocialLinks": "Social links", "Name": "Name", "NoArchivedPartners": "No Archived $t(terms:Partners)", "NoWorkers": "No $t(terms:workers)", "NoJobOpenings": "No $t(terms:job openings)", "NoResultsFound": "No results found", "NoTasks": "No $t(terms:tasks)", "NoTaskGroups": "No $t(terms:projects)", "Partner": "$t(terms:Partner)", "SearchContracts": "Search {{count}} $t(terms:Contracts)", "SearchPartners": "Search {{count}} $t(terms:Partners)", "SearchJobOpenings": "Search {{count}} $t(terms:Job openings)", "SearchAgreementTemplates": "Search {{count}} agreement templates", "SearchTimesheets": "Search {{count}} timesheets", "Messages": "Messages", "MessageCounter": "{{count}} Message", "MessagesCounter": "{{count}} Messages", "Skills": "Skills", "Titles": "Titles", "Timesheet": "Timesheet", "Type": "Type", "Task": "$t(terms:Task)", "TaskName": "$t(terms:Task) name", "TaskFee": "$t(terms:Task) fee", "TaskInvoices": "$t(terms:Invoices)", "Phone": "Phone", "Email": "Email", "FilterBy": "Filter by", "MatchAll": "Match all", "MatchAny": "Match any", "VendorRating": "Rating", "VendorAvailability": "Availability", "Completeness": "Profile completeness", "RelationshipManagers": "Relationship managers", "StartDate": "Start date", "EndDate": "End date", "Status": "Status", "PaidDate": "Paid date", "NumberOfInvoices": "Number of $t(terms:Invoices)", "TotalAmount": "Total amount", "LastUpdated": "Last updated", "ActiveTasks": "Active $t(terms:tasks)", "BudgetOverview": "Budget overview", "InvitedAt": "Date invited", "ProjectManagers": "$t(terms:Project) managers", "Budget": "Budget", "TaskFees": "$t(terms:Task) fees", "Project": "$t(terms:Project)", "Teams": "Teams", "TaskManagers": "$t(terms:Task) managers", "TaskTotal": "$t(terms:Task) total", "TaskRate": "$t(terms:Task) rate", "Files": "$t(terms:Files)", "FileCounter": "{{count}} File", "FilesCounter": "{{count}} Files", "NoOfFiles": "{{number}} $t(terms:Files)", "Payments": "$t(terms:Invoices)", "MoreThan": "More than", "UpTo": "Up to", "Documents": "$t(terms:Documents)", "Weeks_1": "This week", "Weeks_2": "Next two weeks", "Weeks_4": "Next four weeks", "Weeks_6": "Next six weeks", "Weeks_12": "Next twelve weeks", "Weeks_custom": "Custom range...", "CustomRange": "Custom range", "Available": "Available", "Managers": "Managers", "Teammates": "Teammates", "PartnerEmail": "$t(terms:Partner)'s email", "VendorCompliance": "Compliance", "ContractPaidToDate": "Paid to date", "CreatedAt": "Created at", "CreatedBy": "Created by", "TemplateName": "Template name", "Total": "total", "ContractId": "$t(terms:Contract) ID", "VendorList": "$t(terms:Partner) List", "ComplianceOutcome": "Compliance outcome", "ComplianceCheck": "Compliance check"}, "TableFilters": {"payments_amount": "$t(terms:Invoices) amount", "archived": "Archived", "my_task_only": "Show only my $t(terms:tasks)", "active_tasks": "Show only active $t(terms:tasks)", "created_by": "Created by", "location": "Location", "date_posted": "Date posted", "status": "Status", "task_start_date": "Start date", "rank": "Rank", "availability": "Current availability", "services": "Skills", "job_opening": "$t(terms:Job opening)", "groups": {"slug": "$t(terms:Groups)"}, "contract": "$t(terms:Contract)", "vendor_type": "$t(terms:Partner) type", "task_end_date": "End date", "task_name": "Task name", "task_description": "Task description", "task_budget_total": "Total amount", "task_budget_rate": "Rate", "location_dict": {"postal_code": "$t(common:TableFilters.postal_code)", "administrative": "$t(common:TableFilters.administrative)", "country": "$t(common:TableFilters.country)", "city": "$t(common:TableFilters.city)", "state": "$t(common:TableFilters.state)", "street_address": "$t(common:TableFilters.street_address)"}, "city": "City", "parent": {"slug": "$t(terms:Staffing supplier)"}, "state": "State", "street_address": "Address", "postal_code": "Postal code", "administrative": "Administrative", "country": "Country", "working_hours": "Working Hours", "tags": "Tags", "radius": "<PERSON><PERSON>", "compliance": "Compliance", "availability_calendar": "Availability", "currency": "<PERSON><PERSON><PERSON><PERSON>", "approver": "Approver", "assigned_buyer": "Approver", "scheduled_for": "Scheduled for", "payout_method_type": "Payout Method", "vendor": "$t(terms:Partner)", "task_group": "$t(terms:Project)", "teams": "Team", "managers": "$t(terms:Task) manager", "date_start": "Start date", "date_end": "End date", "budget_rate_type": "$t(terms:Task) rate type", "budget_total": "$t(terms:Task) total", "budget_rate_per_time_unit": "$t(terms:Task) rate", "template": "Template", "min": "Min", "max": "Max", "onboarding_workflow": "Workflow", "candidates": {"id": "$t(terms:Partner)"}}, "TableSpecialValues": {"Other": "Other", "groups": {"slug": "$t(terms:Groups)"}, "job_opening": {"location": "Location"}, "availability_calendar": "Availability", "vendor": "$t(terms:Partner)", "task_group": "$t(terms:Project)", "teams": "Team", "contract": "$t(terms:Contract)", "managers": "$t(terms:Task) manager", "created_by": "Created by", "date_start": "Start date", "date_end": "End date", "budget_rate_type": "$t(terms:Task) rate type", "payments_amount": "$t(terms:Invoices) amount", "budget_total": "$t(terms:Task) total", "budget_rate_per_time_unit": "$t(terms:Task) rate", "currency": "$t(terms:Task) currency", "display_on": "Displayed In"}, "Headers": {"PublicJobOpenings": {"ActiveJobOpenings": "Active $t(terms:job openings)"}}, "VendorTileElements": {"GroupsTileElement": "$t(terms:Groups)", "SkillsTileElement": "Skills", "AvailabilityTileElement": "Availability", "CustomField": "Custom Field", "ContactInfoTileElement": "Contact Info", "EmailTileElement": "Email", "PhoneNumberTileElement": "Phone Number", "LocationTileElement": "Location", "DescriptionTileElement": "Description", "TitlesTileElement": "Titles", "NoteTileElement": "Internal note", "VendorListTileElement": "Lists", "DocumentsTileElement": "$t(terms:Documents)", "VendorRequestedDocumentTileElement": "Requested documents"}, "StaffingSupplier": "$t(terms:Staffing supplier)", "BulkAddAlerts": {"Success": "Processing of {{count}} $t(terms:{{type}}) rows finished successfully", "Failure": "There were problems with processing your $t(terms:{{type}})s. We've sent you an email with the details.", "ValidationFailure": "Your data is not valid. Review and fix the issues before submitting.", "Processing": "Your $t(terms:{{type}}) data is being processed in the background. You will be notified once the process is complete.", "CollectingRecords": "Collecting records for validation...", "BackendValidating": "Validating provided data...", "EmptyData": "Error occurred while adding $t(terms:{{type}}) rows. There was no valid data to process.", "AlreadySubmittedData": "Error occurred while adding $t(terms:{{type}}) rows. The given set of data was already imported.", "UnknownError": "Unknown error occurred while processing your records. Please try again later."}, "TaskGroupActions": {"Edit": "Edit $t(terms:project)", "Duplicate": "Duplicate $t(terms:project)", "MakeAllTasksLive": "Make all $t(terms:tasks) live", "CompleteAllLiveTasks": "Complete all live $t(terms:tasks)", "Remove": "Remove $t(terms:project)", "Archive": "Archive $t(terms:project)", "Unarchive": "Unarchive $t(terms:project)"}, "TimesheetActions": {"ApproveTimesheet": "Approve", "RejectTimesheet": "Reject", "SubmitTimesheet": "Submit for review", "EditInTimeTracking": "Edit in Time Tracking", "TimeEntriesAndNotes": "Time entries and notes"}, "TaskActions": {"Edit": "Edit $t(terms:task)", "AddInvoice": "Add $t(terms:Invoice)", "MakeLive": "Make Live", "Duplicate": "Duplicate", "RequestFeedback": "Request feedback", "LeaveReview": "Leave a review", "Complete": "Complete $t(terms:task)", "CancelTask": "Cancel $t(terms:task)", "ArchiveTask": "Archive $t(terms:task)", "AcceptTask": "Accept $t(terms:task)", "RejectTask": "Reject $t(terms:task)", "ResetTask": "Reset to pending", "ResetTo": "Reset to {{status}}", "ResetToPreviousStatus": "Reset to previous status", "RemoveTask": "Remove", "CopyAndAssignPartners": "Copy and assign $t(terms:partners)", "AssignPartners": "Assign $t(terms:partners)", "VendorMarkAsCompleted": "Mark $t(terms:task) as completed"}, "VendorActions": {"AddToRequest": "Add to $t(terms:request)", "AddToJobOpening": "Add to $t(terms:job opening)", "AddToGroup": "Add to $t(terms:group)", "AddToList": "Add to list", "Invite": "Invite to your $t(terms:Shortlist)", "RemoveFromGroup": "Remove from $t(terms:group)", "SendAgreement": "Send agreement from template", "ResendInvitations": "Resend invitations", "RankPartner": "Rank $t(terms:partner)", "AskForAvailability": "Ask for availability update", "RequestFeedback": "Request feedback", "SetRelationshipManagers": "Set relationship managers", "ManageOnboardingTemplates": "Manage $t(terms:onboarding) templates", "ManageWorkflows": "Manage workflows", "SendMessage": "Send message", "ChangeVendorType": "Change $t(terms:partner) type", "MoveToMyShortlist": "Move to $t(terms:My Shortlist)", "Archive": "Archive", "Remove": "Remove", "InviteToShortlist": "Invite to your $t(terms:Shortlist)", "AddToTask": "Add to $t(terms:task)", "SendAgreementForSigning": "Send agreement for signing", "AddReview": "Add review", "SendEmailLoginLink": "Send email login link", "EditProfileInformation": "Edit profile information", "ShareProfile": "Share profile", "ReviewOnboarding": "Review $t(terms:onboarding)", "AddToFavorites": "Add to $t(terms:favorites)", "RemoveFromFavorites": "Remove from $t(terms:favorites)", "ReviewOnboardingTemplates": "Review $t(terms:onboarding)", "SetAsCompliant": "Set as compliant", "RemoveFromList": "Remove from list", "RemoveCompliant": "Remove compliance override", "CreateContract": "Create $t(terms:Contract)", "AddTag": "Add $t(terms:tag)", "AddTags": "Add $t(terms:tags)"}, "ViewTypes": {"Cards": "Cards", "List": "List", "Portfolio": "Portfolio"}, "Search": {"Search": "Search", "StartTypingToSearch": "Start typing to search...", "NoSearchResults": "No search results", "CustomFields": "Custom fields", "Lists": "Lists", "RequestedDocuments": "Requested documents", "Availability": "Availability", "Save": "Save search", "SeeResults": "See results", "SearchLocation": "Search location", "Label": "Export", "LabelMultiple": "Export data...", "MoreFilters": "More filters..."}, "ExportTypes": {"Csv": {"Label": "Comma-separated values (.csv)"}}, "UserActions": {"AddPartner": "Add $t(terms:partner)", "GetInviteLink": "Get invite link", "AddStaffingSupplier": "Add $t(terms:staffing supplier)", "AddGroup": "Add $t(terms:group)", "AddList": "Add list", "NewJobOpening": "New $t(terms:job opening)", "CreateNewProject": "Create new $t(terms:project)", "AddNewRequest": "Add new $t(terms:request)", "AddNewExpense": "Add new $t(terms:expense)", "CreateNewWorkflow": "Create new workflow", "UploadFile": "Upload file", "AddNewGroup": "Add new $t(terms:group)", "InviteAllPartners": "Invite all $t(terms:partners)", "ManageInviteLinks": "Manage invite links", "CreateNewCampaign": "Create new $t(terms:campaign)", "ExportPayments": "Export $t(terms:Invoices)"}, "CustomFields": {"AddText": "Add text...", "SelectAnOption": "Select an option...", "Option": "option", "Option_other": "options", "OptionsLeft": "And {{count}} more $t(common:CustomFields.Option, {\"count\": {{count}}})..."}, "Forms": {"SelectOption": "Select option", "SubtotalExclTax": "Subtotal (excl. tax)", "InputMentionsHint": "Hint: You can insert variables using the \"#\" symbol"}, "Modals": {"SetAsCompliantModalHeaderSingle": "Are you sure you want to set $t(terms:partner) as compliant?", "SetAsCompliantModalHeaderMany": "Are you sure you want to set $t(terms:partners) as compliant?", "SetAsCompliantModalBodySingle": "$t(terms:Partner) will be permanently marked as compliant.", "SetAsCompliantModalBodyMany": "$t(terms:Partners) will be permanently marked as compliant.", "SetAsCompliantCloseButton": "YES, set as compliant", "RemoveCompliantModalHeader": "Are you sure you want to remove compliance override?", "RemoveCompliantModalBodySingle": "$t(terms:Partner) will be marked as complaint only if all the compliance rules are met.", "RemoveCompliantModalBodyMany": "$t(terms:Partners) will be marked as complaint only if all the compliance rules are met.", "RemoveCompliantCloseButton": "YES, remove override", "UploadSignedAgreementModalHeader": "Upload signed agreement", "UploadSignedAgreementModalBody": "If you have a PDF or an image file of a signed agreement you can upload it manually here", "UploadSignedAgreementModalUploadButton": "Upload agreement", "UploadSignedAgreementModalFilepickerButton": "Upload signed agreement", "UploadSignedAgreementModalSelectPlaceholder": "Select option", "UploadSignedAgreementModalSelectLabel": "Agreement template", "UploadSignedAgreementModalAgreementMissingError": "Agreement template should be selected", "UploadSignedAgreementModalFileMissingError": "Agreement file should be selected", "UnsavedChangesModal": {"BtnPrimary": "You will lose any changes you have made.", "BtnSecondary": "Back to editing", "DiscardAndLeave": "Discard changes and leave", "MainCopy": "What do you want to do?", "Header": "Discard changes and quit?", "UnsavedChanges": "You have unsaved changes. Are you sure you want to leave?"}, "AddDocumentTemplateModal": {"Header": "Add new document template", "EditHeader": "Edit document template", "DocumentNameLabel": "Document name", "DescriptionLabel": "Description", "DocumentTypeLabel": "Document type", "DocumentTypePlaceholder": "Select document type", "VerificationRequiredCheckbox": "Verification required", "ExpiryDateCheckbox": "Expiry date required", "MandatoryCheckbox": "Mandatory document", "VisibleForPartnersCheckbox": "Visible for $t(terms:partners)", "DocumentNameMissingError": "Document name is mandatory", "DocumentTypeMissingError": "Document type should be selected", "ConfidentialCheckbox": "Confidential document", "ConfidentialCheckboxHint": "Only administrator and team mates with special permission will see this document", "ConfirmModalMessageSingle": "This template is applied to <strong>{{vendorsCount}}</strong> $t(terms:Partner).\nAre you sure you want to update it?", "ConfirmModalMessageMultiple": "This template is applied to <strong>{{vendorsCount}}</strong> $t(terms:Partners).\nAre you sure you want to update it?", "ConfirmModalHeader": "Confirm changes", "ConfirmModalConfirmButtonLabel": "Yes, I am sure", "ConfirmModalCancelButtonLabel": "No, go back", "ConfirmModalNotifyPartnersCheckbox": "Do not notify $t(terms:Partners) of changes"}, "UploadDocumentModal": {"DocumentTypeLabel": "Document type", "IssueDateLabel": "Issue date", "ExpiryDateLabel": "Expiry date", "UploadDocumentLabel": "Upload document", "ExpirationDateError": "Expiration date should be later than issue date."}, "SessionExpiryModal": {"YourSessionHasExpired": "Your session has expired", "YourSessionWillExpireIn": "Your session will expire in {{text}}", "ContinueSession": "Continue Session", "EndSessionNow": "End session now", "EndSessionAndSave": "End session and save", "ChangesSavedAutomatically": "All the changes have been saved automatically.", "ChangesHaveBeenLost": "Some of your changes have been lost.", "YourSessionHasExpiredDueToInactivity": "Your session has expired due to inactivity.", "VendorExpireSoonCopy1": "Your session will soon expire due to your inactivity and anyone with the access rights to the Agreement will be able to edit, comment or suggest.", "VendorExpireSoonCopy2": "To extend the session, click ‘Continue Session’, add any comment or suggestion.", "BuyerExpireSoonCopy1": "Your session will soon expire due to inactivity. All changes will be saved automatically unless an issue occurs, and anyone with access rights to the Agreement will be able to edit, comment, or suggest.", "BuyerExpireSoonCopy2": "To extend the session, click ‘Continue Session’, make any changes to the Agreement text, add or resolve any comment, or resolve any suggestion."}, "AddListModal": {"Header": "Add list", "ListNameLabel": "Name", "ListNamePlaceholder": "List name", "PartnerTemplateLabel": "Choose a $t(terms:Partner) sharing template", "PartnerTemplateDescription": "You can customize the information you want to share of your $t(terms:partners).", "TemplateSetupButton": "Template setup", "CreateButton": "Create", "MoreOptionsButton": "More options...", "ListNameMissingError": "List name is mandatory", "ListTemplateMissingError": "$t(terms:Partner) sharing template is mandatory", "ListCreatedToastMessage": "New list has been created."}, "ShareListModal": {"Header": "Share this list", "AlertMessage": "The information in this list will be visible to anyone with a link.", "MainMessage": "Remember that all changes will be visible on shared view. You can change the profile information in template settings.", "LearnMoreLink": "Learn more", "StartSharingButton": "Start sharing", "StopSharingButton": "Stop sharing", "CopyLinkButton": "Copy link", "ToastCopiedMessage": "Shared list link copied.", "ToastStoppedSharingMessage": "This list is no longer shared."}, "ShareProfileModal": {"Header": "Share this profile", "CreateLinkLabel": "Create new sharing link", "PreviousLinkCreateLinkLabel": "... or create a new sharing link", "CreateLinkLabelDescription": "This link will show a limited view of the profile, based on the selected sharable profile template.", "GenerateLinkButton": "Generate new link", "PreviewButton": "Preview", "PreviewAndEditButton": "Preview and edit", "SharableLinkLabel": "Sharable link", "PreviousSharableLinkLabel": "Use the previously generated link...", "CopyButton": "Copy", "CopiedButton": "Copied!", "CopiedMessage": "Shared profile link copied.", "SharableLinkDescription": "The link will be active for one month.", "SharedProfileTitlePrefix": "Shared profile"}, "QuickView": {"Groups": "$t(terms:Groups)", "Description": "Description", "Profile": "Profile", "Resume": "Resume", "Portfolio": "Portfolio", "Tags": "Tags", "AddNote": "Add note...", "GenerateLink": "Generate new link", "Download": "Download", "Close": "Close", "ToastCopiedMessage": "Shared profile link copied.", "ViewFullProfile": "View full profile", "CopyShareLink": "Copy share link", "Generating": "Generating...", "LinkCopied": "Link copied"}, "SaveSearch": {"Header": "Save search", "NameInputLabel": "Name", "NameInputPlaceholder": "Enter search name", "Save": "Save", "SearchNameMissingError": "Search name is mandatory", "SaveButtonMessage": "Filters saved"}, "AddToJobOpening": {"Header": "Add $t(terms:partner) to a $t(terms:job opening)", "JobOpeningLabel": "Choose $t(terms:job opening)", "ConfirmButton": "Confirm & add", "JobOpeningMissingError": "Select $t(terms:job opening)", "SuccessToastMessage": "$t(terms:Partners) have been added"}, "AddToList": {"Header": "Add $t(terms:partner) to list", "Label": "Choose list", "ConfirmButton": "Add", "MissingError": "Select list", "SuccessToastMessage": "$t(terms:Partners) have been added", "New": "[New list]"}, "AddToGroup": {"Header": "Add $t(terms:partner) to $t(terms:group)", "Label": "Choose $t(terms:group)", "ConfirmButton": "Add", "MissingError": "Select $t(terms:group)", "SuccessToastMessage": "$t(terms:Partners) have been added", "New": "[New $t(terms:group)]"}, "ChooseAgreementTypeModal": {"Header": "Choose the agreement type"}, "EntityFormModal": {"HeaderAdd": "Add a new $t(terms:entity)", "HeaderEdit": "Edit $t(terms:entity)", "InputName": "$t(terms:Entity) name", "InputCompanyName": "Legal company name", "InputCountry": "Country", "InputCountryPlaceholder": "Choose country", "InputAddress": "Street", "InputCity": "City", "InputPostalCode": "Zip code / postal code", "InputRegistrationNumber": "Registration number", "InputTaxIdentificationNumber": "TAX ID number", "EntityNameMissingError": "$t(terms:Entity) name is mandatory", "EntityCompanyNameMissingError": "Legal company name is mandatory", "AddButton": "Add entity", "SaveButton": "Save changes", "EntitySavedMessage": "$t(terms:Entity) saved", "Warning": "We will update the $t(terms:Entity) linked to $t(terms:Tasks) and $t(terms:Invoices) that still need to be processed if there are any."}, "ChooseContractTemplateModal": {"Header": "New $t(terms:Contract) Template", "ContractTemplateNameLabel": "$t(terms:Contract) Template Name", "ContractTemplateNamePlaceholder": "Add $t(terms:Contract) Template name", "ContractTemplateCreateButton": "Create template", "ContractType": "$t(terms:Contract) type", "ContractTemplateNameMissingError": "$t(terms:Contract) template name is mandatory"}, "RejectContractModal": {"Header": "Reject $t(terms:Contract)", "ReasonLabel": "Reason for rejection", "RejectButton": "Reject", "ReasonMissingError": "Rejection reason is mandatory", "SuccessMessage": "$t(terms:Contract) rejected"}, "RejectContractAgreementModal": {"Header": "Reject $t(terms:Contract) agreement"}, "EndContractModal": {"Header": "Set end $t(terms:Contract) date", "AlertMessage": "Please choose the date when this $t(terms:Contract) will end.", "EndDateLabel": "$t(terms:Contract) end date", "SetEndDateButton": "Set $t(terms:Contract) end date", "EndDateInvalidError": "End date is invalid", "EndDateBeforeStartDateError": "End date must be later than start date", "EndDateSaveError": "Error occurred while saving $t(terms:Contract)", "EndDateSaveSuccess": "$t(terms:Contract) saved successfully", "ConfirmEndDateButton": "Confirm $t(terms:Contract) end date"}, "BroadcastModal": {"ModalTitle": "$t(terms:Partner) dashboard announcement", "AddButton": "Save and Make Live", "SaveDraftButton": "Save Draft", "InputTitle": "Title", "InputTitlePlaceholder": "Enter a descriptive title", "InputDescription": "Content", "InputDescriptionPlaceholder": "Enter your text here. Be clear and concise, and make sure to cover all essential information.", "InputLabel_vendor_group": "$t(terms:Partner) group", "InputPlaceholder_vendor_group": "All groups", "InputLabel_display_on": "Displayed in", "BroadcastPublishedMessage": "Announcement has been published", "BroadcastDraftMessageSaved": "Announcement draft has been saved", "BroadcastUpdatedMessage": "Announcement has been updated", "BroadcastTitleMissingError": "Announcement Title is mandatory", "BroadcastDescriptionMissingError": "Announcement Content is mandatory", "BroadcastDescriptionTooLongError": "Announcement Content is too long", "BroadcastDescriptionLengthMessage": "Ensure this field has no more than 1000 characters.", "VendorSummary": "The announcement will be displayed for {{count}} $t(terms:Partners).", "VendorSummaryEdit": "The announcement is displayed for {{count}} $t(terms:Partners)."}, "ComplianceOverrideOutcomeModal": {"Header": "Override the score-based Outcome?", "Description": "The score based outcome is: {{status}}. Do you want to override it?", "OverrideReason": "Override reason", "OverrideReasonPlaceholder": "Enter override reason", "ConfirmButton": "Override to Low risk", "RejectButton": "Override to High risk", "ReasonMissingError": "Reason is mandatory", "OverrideSuccessMessage": "Outcome has been updated", "OutcomeConfirmMessage": "Outcome has been confirmed", "OutcomeErrorMessage": "An error occurred. Please try again later"}, "AddVendorTags": {"SuccessMessage": "$t(terms:Tags) have been added", "ErrorMessage": "Error occurred while saving $t(terms:Tags)", "ErrorNoTagsSelectedMessage": "At least one $t(terms:partner) $t(terms:tag) must be selected."}, "MoveToStageModal": {"Header": "Move to $t(terms:stage)", "SelectStage": "Select $t(terms:stage)", "NoStageSelected": "No $t(terms:stage) selected", "SuccessMessage": "$t(terms:Partners) have been moved", "ErrorMessage": "Error occurred while moving $t(terms:Partners)"}, "AddNewFileModal": {"Header": {"AddNewFile": "Add new file", "EditFile": "Edit file"}, "UploadFileLabel": "Upload file", "DocumentTypeLabel": "Document type", "DocumentTypePlaceholder": "Choose Type", "IssueDateLabel": "Issue date", "ExpiryDateLabel": "Expiry date", "MessageLabel": "Message", "MessagePlaceholder": "Add short note...", "VisibleToPartnerLabel": "Visible to $t(terms:partner)", "NotifyPartnerViaEmailLabel": "Notify $t(terms:partner) via email", "NotifyPartnerViaEmailTooltip": "$t(terms:Partner) must be added to $t(terms:My Shortlist) to be informed about sending a document", "RelatedPartner": "Related $t(terms:partner)", "RelatedPartnerPlaceholder": "Type to search $t(terms:partner)", "Errors": {"ErrorNoFileSelectedMessage": "File is required", "ErrorNoTypeSelectedMessage": "Please choose document type", "ExpirationDateShouldBeAfterIssueDate": "Expiration date should be later than issue date."}, "Alerts": {"AlertDocumentSent": "Document has been successfully sent", "AlertDocumentUpdated": "Document has been successfully updated"}}, "AddNotificationModal": {"Header": {"AddNotification": "Add notification", "EditNotification": "Edit notification"}, "NameLabel": "Notification name", "NameHint": "Note: It will only be used internally", "RecipientsLabel": "Notification recipients", "RecipientsPlaceholder": "Enter teammate name or email", "StageManager": "$t(terms:Stage) manager", "PartnerAddedToThisStage": "$t(terms:Partner) added to this $t(terms:stage)", "OnboardingWorkflowStageNotificationLabel": "Send this Notification", "OnboardingWorkflowStageNotificationOptions": {"stage_completed": "When $t(terms:Partner) leaves the $t(terms:stage)", "entered_stage": "When $t(terms:Partner) enters the $t(terms:stage)", "vendor_completed": "When $t(terms:Partner) reaches 100% of the $t(terms:stage)", "vendor_disqualified": "When $t(terms:Partner) is $t(terms:disqualified) at this $t(terms:stage)", "send_manually": "Send this message manually from the $t(terms:stage)"}, "NotificationContentsHeader": "Notification contents ", "SubjectLabel": "Email subject", "SubjectPlaceholder": "Enter $t(terms:Email title)", "ContentLabel": "Content", "MentionHint": "$t(terms;Hint: You can insert variables using the \"#\" symbol)", "ButtonCaptionLabel": "Button caption", "ButtonCaptionPlaceholder": "Review $t(terms:onboarding)", "ButtonCaptionHint": "Hint: This text will be displayed on the button in the notification email", "IncludePdfLabel": "Include PDF with workflow summary", "IncludePdfPlaceholder": "This PDF includes data entered into $t(terms:stages) so far, activity log, and $t(terms:job opening) details (if present)", "Errors": {"CannotBeEmpty": "{{ field }} cannot be empty"}}, "OnboardingConfirmChangesModal": {"Header": "Confirm changes", "Partner": "$t(terms:partner)", "Partner_other": "$t(terms:partners)", "Description": "This $t(terms:onboarding) is applied to {{count}} $t(common:Modals.OnboardingConfirmChangesModal.Partner, {\"count\": {{count}}}). Are you sure you want to save the changes?", "CheckboxLabel": "Notify $t(terms:Partners) of changes"}}, "Tiles": {"AgreementTypes": {"Contract": {"Header": "$t(terms:Contract) agreement", "Description": "Agreement dedicated for $t(terms:Contracts)"}, "OneOff": {"Header": "One-off agreement", "Description": "Agreement that will be signed once by the $t(terms:partner)"}, "JobOpening": {"Header": "$t(terms:Job opening) agreement", "Description": "$t(terms:Partners) can sign a new copy of this agreement each time they're added to a $t(terms:job opening)"}, "Task": {"Header": "$t(terms:Task) agreement", "Description": "$t(terms:Partners) can sign a new copy of this agreement each time they're added to a $t(terms:task)"}}}, "RequestedDocumentTypes": {"license": "Professional license", "certificate": "Certificate", "insurance": "General Liability insurance", "workers compensation": "Workers compensation", "letter of recommendation": "Letter of recommendation", "sa": "Services agreement", "resume": "Resume/CV", "diploma": "Diploma", "transcripts": "Transcripts", "drivers licence": "Drivers license", "other document": "Other document"}, "AppWideSearchType": {"Buyer": "Teammates", "ErrorMessage": "An error occurred while searching. Please try again later"}, "Notifications": {"NoNotifications": "You don't have any notifications", "NoMoreNotifications": "There are no more notifications", "Loading": "Loading..."}, "CustomRates": {"Rates": "Rates", "NoResponse": "No response", "NotVisibleToPartners": "Not visible to $t(terms:partners)"}, "Users": {"RemovedSuffix": "removed"}, "FilterSettings": {"tasks": "$t(terms:Tasks)", "task_groups": "$t(terms:Projects)", "vendors": "Directory", "job_openings": "$t(terms:Job openings)", "requisition": "Requisition", "payments": "Payments", "contracts": "$t(terms:Contracts)", "ChoosePropertiesAsFilters": {"tasks": "Choose which $t(terms:tasks) properties should be available as filters", "task_groups": "Choose which $t(terms:projects) properties should be available as filters", "vendors": "Choose which $t(terms:partner) properties should be available as filters", "job_openings": "Choose which $t(terms:job openings) properties should be available as filters", "requisition": "Choose which requisition properties should be available as filters", "payments": "Choose which payments properties should be available as filters", "contracts": "Choose which $t(terms:contract) properties should be available as filters"}, "FieldName": "Field name", "Enable": "Enable", "Default": "Always visible", "SaveButtonMessage": "Filters saved", "UnsavedChangesModalHeader": "There are unsaved changes.", "UnsavedChangesModalContent": "Do you want to leave this tab?", "FilterCustomLabel": "Filter custom label"}, "ResumeTeaser": {"Show": "Show Resume", "Download": "Download Resume"}, "Documents": {"Document": "{{count}} Document", "Documents": "{{count}} Documents"}, "Dropdown": {"NoActions": "No actions available"}, "InputDateRangePicker": {"From": "From", "To": "To", "WrongDateFormat": "Incorrect date format", "ExampleOfCorrectDate": "e.g. {{date}}"}, "UserSelect": {"Partner": "$t(terms:Partner)", "StaffLabel": "(STAFF)"}, "StatusPreview": {"active": "Active", "approved": "Approved", "not_started": "Not started", "processing": "Processing...", "rejected": "Rejected", "uploaded_manually": "Uploaded manually"}, "PluralRules": {"one": "st", "two": "nd", "few": "rd", "other": "th"}, "FormPreviewDocument": {"addedOn": "Added on {{date}}", "generating": "Generating", "error": "{{documentName}} not generated"}, "FollowUpFor": "Follow up for:", "PublicJobOpenings": {"ZeroState": "No active $t(terms:Job openings)"}, "Confidential": "Confidential", "PartnerInviteAs": {"Label": "Invite as", "SelectVendorType": "Select $t(terms:partner) type"}, "Signature": {"SigningPlaceholder": "Enter your name", "SigningLabel": "Signature", "SigningDate": "Signing date"}, "Workers": {"AddWorker": "Add $t(terms:worker)"}, "InputPhoneNumber": {"Placeholder": "e.g. {{ phoneNumber }}"}, "ImageUpload": {"ClickToUpload": "Click here to upload cover photo"}}