{"fieldIsRequired": "\"{{field}}\" is a required field.", "fieldIsNotValid": "\"{{field}}\" is not valid.", "dependencyValidation": "\"{{fieldName}}\" is required when \"{{dependsOn}}\" is provided.", "endDateRequiredWhenStartDateProvided": "\"{{endDate}}\" is required when \"{{startDate}}\" is provided.", "startDateMustBeLaterThanFormattedDate": "\"{{startDate}}\" must be later than the \"{{formattedDate}}\".", "endDateMustBeLaterThanStartDate": "\"{{endDate}}\" must be later than the \"{{startDate}}\".", "endDateMustNotBeLaterThanFormattedDate": "\"{{endDate}}\" must not be later than the \"{{formattedDate}}\"."}