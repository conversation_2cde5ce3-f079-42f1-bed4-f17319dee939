{"TaskTemplate": "$t(terms:Task) Template", "AddTask": "Add $t(terms:Task)", "ChooseProject": "Choose $t(terms:Project)", "ChooseTemplate": "<PERSON><PERSON>", "AddTaskButtonTooltip": "You need to create a $t(terms:project) in order to add $t(terms:tasks).", "CompleteTaskModal": {"Title": "Mark $t(terms:Task) as Completed", "DaysLeft": "This $t(terms:task) ends in {{number}} days.", "DayLeft": "This $t(terms:task) ends tomorrow.", "AlertMessageVendor": "This cannot be undone. Make sure you’re finished with your work!"}, "TaskGroup": {"BudgetPopoverNote": "$t(terms:Tasks) that are Archived, Rejected or Cancelled are not calculated in budget", "NoTaskFees": "No $t(terms:tasks) $t(terms:fees)", "NoBudget": "No budget", "TaskFees": "$t(terms:Task) $t(terms:fees)", "BudgetOverrun": "{{value}} overrun!", "Budget": "Budget", "SearchTaskGroup": "Search {{count}} $t(terms:projects)", "SearchTask": "Search {{count}} $t(terms:tasks)", "Status": {"draft": "Draft", "ongoing": "Ongoing", "completed": "Completed", "archived": "Archived"}}, "InputTaskFee": {"TotalFee": "Total $t(terms:fee)", "UnitsLabel": "No. of {{label}}", "UnitsWorkedPlaceholder": "e.g. 30", "FeeRatePerTimeUnitPlaceholder": "e.g. 500.00", "FeeTotalPlaceholder": "e.g. 2,000.00", "RateCopied": "Rate copied from $t(terms:partner)'s profile", "RevertRate": "Revert to $t(terms:partner)'s rate", "TypeOfFee": "Type of $t(terms:fee)"}, "InputTaskGroup": {"SelectProject": "Search for existing $t(terms:project) or create a new one...", "NewProject": "(new $t(terms:Project))"}, "InputTaskTimesheetTemplate": {"SelectTimesheetTemplate": "Select Timesheet Template"}, "Task": {"NoPartnerSelected": "No $t(terms:partner) selected", "Status": {"draft": "Draft", "pending": "Pending", "accepted": "Accepted", "completed": "Completed", "rejected": "Rejected", "canceled": "Canceled", "archived": "Archived", "under_review": "Under review"}}, "TaskForm": {"TaskGroup": "$t(terms:Project)", "Name": "Name", "Description": "Description", "TaskManagers": "$t(terms:Task) Managers", "TaskManagersPlaceholder": "Search for $t(terms:Task) Managers", "TaskPartner": "$t(terms:Task) $t(terms:Partner)", "TaskPartnerPlaceholder": "Search for $t(terms:Partners)", "TimesheetTemplate": "Timesheet Template"}, "InputTaskDates": {"StartDate": "Start date", "EndDate": "End date"}, "ContractSummary": {"RelatedContract": "Related $t(terms:Contract)"}, "TaskActions": {"SuccessfullyRemoved": "$t(terms:Task) has been removed."}, "Modals": {"AddTaskNewFileModal": {"Header": "Add new file", "FileLabel": "Upload file", "NameLabel": "Name", "NamePlaceholder": "File name", "UploadFileButtonLabel": "Upload file"}}}