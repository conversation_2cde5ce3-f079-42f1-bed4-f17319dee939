import AgreementFieldOverflowError from '@Worksuite/Features/Agreements/AgreementFieldOverflowError/AgreementFieldOverflowError';
import AgreementTemplateEditor from '@Worksuite/Features/Agreements/AgreementTemplateEditor/AgreementTemplateEditor';
import AllAgreementTemplates from '@Worksuite/Features/Agreements/AllAgreementTemplates/AllAgreementTemplates';
import ComplianceAlert from '@Worksuite/Features/Compliance/components/ComplianceAlert/ComplianceAlert';
import WorkersComplianceForm from '@Worksuite/Features/Compliance/components/WorkersComplianceForm/WorkersComplianceForm';
import AllContractsContainer from '@Worksuite/Features/Contracts/containers/AllContractsContainer/AllContractsContainer';
import Currency from '@Worksuite/Features/Core/components/Currency/Currency';
import PDFViewer from '@Worksuite/Features/Core/components/DataDisplay/PDFViewer';
import EntityDetails from '@Worksuite/Features/Entities/components/EntityDetails/EntityDetails';
import EntityDropdown from '@Worksuite/Features/Entities/components/EntityDropdown/EntityDropdown';
import BuyerJobOpeningsContainer from '@Worksuite/Features/JobOpenings/containers/BuyerJobOpeningsContainer/BuyerJobOpeningsContainer';
import SupplierJobApplicationCandidatesContainer from '@Worksuite/Features/JobOpenings/containers/SupplierJobApplicationCandidatesContainer/SupplierJobApplicationCandidatesContainer';
import VendorJobOpeningStagesContainer from '@Worksuite/Features/JobOpenings/containers/VendorJobOpeningStagesContainer/VendorJobOpeningStagesContainer';
import * as Payments from '@Worksuite/Features/Payments/index';
import {
  PayoutMethodLogo,
  PayoutMethods,
  PayoutMethodsProfile,
} from '@Worksuite/Features/PayoutMethods';
import AddTaskDropdown from '@Worksuite/Features/Tasks/Tasks/AddTaskDropdown/AddTaskDropdown';
import * as Tasks from '@Worksuite/Features/Tasks/index';
import { Ten99NecView } from '@Worksuite/Features/TaxFiling/components/Ten99NecView';
import {
  TaxInformationForm,
  TaxInformationOnboarding,
  TaxInformationProfile,
} from '@Worksuite/Features/TaxInformation';
import TenantMessages from '@Worksuite/Features/TenantMessages/TenantMessages';
import TimeTracker from '@Worksuite/Features/TimeTracking/containers/TimeTracker';
import TimesheetHeader from '@Worksuite/Features/Timesheets/components/TimesheetHeader/TimesheetHeader';
import * as Timesheets from '@Worksuite/Features/Timesheets/index';
import * as TwoFactorAuth from '@Worksuite/Features/TwoFactorAuth/index';
import VendorExternalSyncSettings from '@Worksuite/Features/Vendor/components/ExternalSyncSettings/VendorExternalSyncSettings';
import VendorTags from '@Worksuite/Features/Vendor/components/VendorTags/VendorTags';
import AllWorkersContainer from '@Worksuite/Features/Vendor/containers/AllWorkersContainer/AllWorkersContainer';
import VendorComplianceContainer from '@Worksuite/Features/Vendor/containers/VendorComplianceContainer/VendorComplianceContainer';
import OnboardingStageList from '@Worksuite/Features/Workflows/components/OnboardingStageList/OnboardingStageList';
import * as Icons from './assets/icons';
import ActionLogHistory from './components/ActionLogHistory/ActionLogHistory';
import Alert from './components/Alert/Alert';
import ColumnConfiguration from './components/ColumnConfiguration/ColumnConfiguration';
import AllPartnersContainer from './components/Containers/AllPartnersContainer/AllPartnersContainer';
import BuyerTaskGroupsContainer from './components/Containers/BuyerTaskGroupsContainer/BuyerTaskGroupsContainer';
import BuyerTasksContainer from './components/Containers/BuyerTasksContainer/BuyerTasksContainer';
import DashboardActionsContainer from './components/Containers/Dashboard/DashboardActionsContainer/DashboardActionsContainer';
import DashboardWidget from './components/Containers/Dashboard/DashboardWidget/DashboardWidget';
import PublicJobOpeningsContainer from './components/Containers/PublicJobOpeningsContainer/PublicJobOpeningsContainer';
import RequestedFeedbackCointainer from './components/Containers/RequestedFeedbackCointainer/RequestedFeedbackCointainer';
import ReviewCointainer from './components/Containers/ReviewCointainer/ReviewCointainer';
import VendorsListContainer from './components/Containers/VendorsListContainer/VendorsListContainer';
import GlobalCustomFieldsTemplatesContainer from './components/CustomFields/GlobalCustomFieldsTemplatesContainer/GlobalCustomFieldsTemplatesContainer';
import DataTable from './components/DataTable/DataTable';
import InsuranceViewForBuyer from './components/Documents/InsuranceViewForBuyer';
import InsuranceViewForVendor from './components/Documents/InsuranceViewForVendor';
import FlatfileButton from './components/Flatfile/FlatfileButton/FlatfileButton';
import AutoComplete from './components/FormElements/AutoComplete/AutoComplete';
import Button from './components/FormElements/Button/Button';
import ErrorList from './components/FormElements/ErrorList/ErrorList';
import InputCheckbox from './components/FormElements/InputCheckbox';
import InputDatePicker from './components/FormElements/InputDatePicker/InputDatePicker';
import InputDateRangePicker from './components/FormElements/InputDateRangePicker/InputDateRangePicker';
import InputEditor from './components/FormElements/InputEditor';
import InputFile from './components/FormElements/InputFile/InputFile';
import InputLabel from './components/FormElements/InputLabel/InputLabel';
import InputMentions from './components/FormElements/InputMentions/InputMentions';
import InputRadioButtonGroup from './components/FormElements/InputRadioButtonGroup/InputRadioButtonGroup';
import InputText from './components/FormElements/InputText/InputText';
import InputTextArea from './components/FormElements/InputTextArea/InputTextArea';
import * as Forms from './components/Forms';
import EmailUnsubscribeForm from './components/Forms/EmailUnsubscribeForm/EmailUnsubscribeForm';
import ImageUpload from './components/ImageUpload/ImageUpload';
import InsightsAccessDenied from './components/Insights/InsightsAccessDenied';
import ToastMessage from './components/Layout/ToastMessage/ToastMessage';
import LineItemsTable from './components/LineItems/LineItemsTable/LineItemsTable';
import LineItemsTableWithCF from './components/LineItems/LineItemsTableWithCF/LineItemsTableWithCF';
import NotificationList from './components/Lists/NotificationList/NotificationList';
import PublicJobOpeningsConfigList from './components/Lists/PublicJobOpeningsConfigList/PublicJobOpeningsConfigList';
import Ten99PStageForBuyer from './components/OnboardingWorkflow/components/Ten99PSetupStage/Ten99PStageForBuyer';
import Ten99PStageForVendor from './components/OnboardingWorkflow/components/Ten99PSetupStage/Ten99PStageForVendor';
import Ten99PSetupStageForBuyer from './components/OnboardingWorkflow/components/Ten99PSetupStage/deprecated/Ten99PSetupStageForBuyer';
import Ten99PSetupStageForVendor from './components/OnboardingWorkflow/components/Ten99PSetupStage/deprecated/Ten99PSetupStageForVendor';
import { rewriteLegacyStateParams } from './components/Search/ElasticSearchFilters/ElasticSearchFilters.helpers';
import * as States from './components/Shortlist/States/index';
import NewSideNavigation from './components/Sidebar/NewSideNavigation/NewSideNavigation';
import ResumeTeaser from './components/Teasers/ResumeTeaser/ResumeTeaser';
import BuyerAllTimesheets from './components/Timesheets/Containers/BuyerAllTimesheets/BuyerAllTimesheets';
import ConfirmationBox from './components/Utils/ConfirmationBox/ConfirmationBox';
import HorizontalNavigation from './components/Utils/HorizontalNavigation/HorizontalNavigation';
import List from './components/Utils/List/List';
import LogoGroup from './components/Utils/LogoGroup/LogoGroup';
import { openModal } from './components/Utils/Modals/modal.helpers';
import RecommendToggle from './components/Utils/RecommendToggle/RecommendToggle';
import SharableTemplateSelect from './components/Utils/SharableProfiles/SharableTemplateSelect';
import Spinner from './components/Utils/Spinner/Spinner';
import StarRating from './components/Utils/StarRating/StarRating';
import UserRoleNameByUserId from './components/Utils/UserRoleNameByUserId/UserRoleNameByUserId';
import WorksuiteLoader from './components/Utils/WorksuiteLoader/WorksuiteLoader';
import ZeroState from './components/Utils/ZeroState/ZeroState';
import ContactInfo from './components/Vendors/Profile/ContactInfo/ContactInfo';
import VendorGroupManagers from './components/Vendors/Profile/VendorGroupManagers/VendorGroupManagers';
import VendorProfileGroups from './components/Vendors/Profile/VendorProfileGroups/VendorProfileGroups';
import VendorWorkedWith from './components/Vendors/Profile/VendorWorkedWith/VendorWorkedWith';
import VendorCard from './components/Vendors/VendorCard/VendorCard';
import VendorCustomRates from './components/Vendors/VendorCustomRates/VendorCustomRates';
import * as VendorHelpers from './components/Vendors/vendors.helpers';
import { mapJobOpeningsToWorkflowSummaryStages } from './components/Workflow/WorkflowSummary/JobOpeningSummary.helper';
import WorkflowSummary from './components/Workflow/WorkflowSummary/WorkflowSummary';
import ComplianceOverview from './features/Compliance/components/ComplianceOverview/ComplianceOverview';
import InputPhoneNumber from './features/Core/components/FormElements/InputPhoneNumber/InputPhoneNumber';
import i18n from './i18n/i18n';
import { ShortlistAnalyticsService } from './services/Analytics';
import Api from './services/Api';
import { ApiHelper } from './services/Api/helpers';
import { reduxStore } from './services/Redux';
import * as Documents from './services/Shortlist/Documents';
import VendorBankDetailsShortlistPay from './services/Shortlist/ShortlistPay/vendorBankDetailsShortlistPay.class';
import { accessLevels, userRoles } from './services/Shortlist/access-levels';
import { react2angular, setMuiLicense } from './utils/index';
import * as Utils from './utils/index';
import {
  getDefaultSettingsForSearchInView,
  stringifyQueryParams,
} from './utils/other';
import { WorksuiteTheme } from './utils/worksuite-theme';

export { Api, ApiHelper };
export { Forms };
export { i18n };
export { Icons };
export { react2angular };
export { reduxStore };
export { States };
export {
  ActionLogHistory,
  AddTaskDropdown,
  Alert,
  AllContractsContainer,
  AllPartnersContainer,
  AllWorkersContainer,
  AutoComplete,
  Button,
  BuyerAllTimesheets,
  BuyerJobOpeningsContainer,
  BuyerTaskGroupsContainer,
  BuyerTasksContainer,
  ColumnConfiguration,
  ComplianceAlert,
  ComplianceOverview,
  ConfirmationBox,
  ContactInfo,
  Currency,
  DashboardActionsContainer,
  DashboardWidget,
  DataTable,
  EmailUnsubscribeForm,
  EntityDetails,
  EntityDropdown,
  ErrorList,
  FlatfileButton,
  GlobalCustomFieldsTemplatesContainer,
  HorizontalNavigation,
  ImageUpload,
  InputCheckbox,
  InputDatePicker,
  InputDateRangePicker,
  InputEditor,
  InputFile,
  InputLabel,
  InputMentions,
  InputRadioButtonGroup,
  InputPhoneNumber,
  InputText,
  InputTextArea,
  InsightsAccessDenied,
  InsuranceViewForBuyer,
  InsuranceViewForVendor,
  LineItemsTable,
  LineItemsTableWithCF,
  List,
  LogoGroup,
  NewSideNavigation,
  NotificationList,
  OnboardingStageList,
  PDFViewer,
  PublicJobOpeningsConfigList,
  PublicJobOpeningsContainer,
  RecommendToggle,
  RequestedFeedbackCointainer,
  ResumeTeaser,
  ReviewCointainer,
  SharableTemplateSelect,
  Spinner,
  StarRating,
  PayoutMethods,
  PayoutMethodsProfile,
  PayoutMethodLogo,
  SupplierJobApplicationCandidatesContainer,
  TaxInformationForm,
  TaxInformationProfile,
  TaxInformationOnboarding,
  Ten99NecView,
  Ten99PStageForBuyer,
  Ten99PStageForVendor,
  Ten99PSetupStageForBuyer,
  Ten99PSetupStageForVendor,
  TimesheetHeader,
  ToastMessage,
  UserRoleNameByUserId,
  VendorCard,
  VendorComplianceContainer,
  VendorCustomRates,
  VendorGroupManagers,
  VendorHelpers,
  VendorJobOpeningStagesContainer,
  VendorExternalSyncSettings,
  VendorProfileGroups,
  VendorTags,
  VendorsListContainer,
  VendorWorkedWith,
  WorkflowStageTypeContent,
  WorkersComplianceForm,
  WorkflowSummary,
  WorksuiteLoader,
  ZeroState,
  FeedbackState,
  CustomFieldWrapper,
};
export { Documents };
export { ShortlistAnalyticsService };
export {
  AgreementFieldOverflowError,
  AgreementTemplateEditor,
  AllAgreementTemplates,
};
export { TenantMessages };
export { TimeTracker };
export { Utils };

const StateUtils = {
  rewriteLegacyStateParams,
  getDefaultSettingsForSearchInView,
  stringifyQueryParams,
};
export { StateUtils };

const Modals = {
  openModal,
};
export { Modals };

const JobOpeningsUtils = {
  mapJobOpeningsToWorkflowSummaryStages,
};
export { JobOpeningsUtils };

const Worksuite = {
  routingConfig: {
    userRoles,
    accessLevels,
  },
  shortlistPay: {
    VendorBankDetailsShortlistPay,
  },
};
export { Worksuite };

// Timesheets
export { Timesheets };

// Tasks
export { Tasks };

// Payments
export { Payments };

// Dashboard
export * as Dashboard from '@Worksuite/Features/Dashboard/index';

// Upsell
import { UpsellBanner } from '@Worksuite/Features/Upsell';
import CustomFieldWrapper from './components/CustomFields/CustomFieldWrapper/CustomFieldWrapper';
import { CUSTOM_FIELDS_OTHER_PREFIX } from './components/CustomFields/custom-fields.helpers';
const Upsell = {
  UpsellBanner,
};
export { Upsell };

// Portfolio
import { PortfolioGrid, PortfolioSummary } from './components/Portfolio';
import FeedbackState from './components/Utils/FeedbackState/FeedbackState';
import WorkflowStageTypeContent from './features/Workflows/components/WorkflowStageTypeContent/WorkflowStageTypeContent';
const Portfolio = {
  PortfolioGrid,
  PortfolioSummary,
};
export { Portfolio };

// Core
export * as Core from '@Worksuite/Features/Core/index';

// TwoFactorAuth
export { TwoFactorAuth };

export { WorksuiteTheme };

export { CUSTOM_FIELDS_OTHER_PREFIX };

setMuiLicense();
