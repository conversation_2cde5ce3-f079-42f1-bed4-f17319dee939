import { SimpleUser } from './user';

export type NotificationRecipient =
  | Pick<
      SimpleUser,
      | 'avatar_color'
      | 'slug'
      | 'full_name'
      | 'initials'
      | 'profile_picture_path'
      | 'deleted'
      | 'email'
    >
  | Pick<SimpleUser, 'slug' | 'email'>;

export interface CustomNotification {
  name: string;
  is_internal: boolean;
  send_sms: boolean;
  send_to_vendor: boolean;
  send_to_stage_managers: boolean;
  send_condition: {
    condition: string;
  };
  button_label: string;
  subject: string;
  content: string;
  stage_type: string[];
  auto_proceed_to_next_stage: [boolean, boolean];
  dynamic_recipients?: number[];
  id?: number;
  isNew?: boolean;
  recipients: NotificationRecipient[];
  attach_onboarding_summary?: boolean;
  _status?: CustomNotificationStatusType;
}

type CustomNotificationStatusType =
  | 'not-changed'
  | 'added'
  | 'changed'
  | 'deleted';

export interface EventNotification {
  id: number;
  timestamp: string;
  excerpt: string;
  message: string;
  link: string;
  // vendor	null
  // project	null
  // user	null
}
