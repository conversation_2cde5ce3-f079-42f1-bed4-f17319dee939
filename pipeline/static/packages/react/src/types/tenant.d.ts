import { TENANT_FEATURE } from '../services/Shortlist/feature-flags';
import { KeysToCamelCase } from './custom-types';
import { Preference } from './user';
import { Entity } from '@Worksuite/Features/Entities/types/entity';

export type Tenant = {
  currency: string;
  description: string;
  distance_unit: 'km' | 'mi';
  domain_url: string;
  features: TENANT_FEATURE[];
  frontend_inactivity_timeout: number;
  name: string;
  projects_total: number;
  timezone: string;
  users_total: number;
  vendors_total: number;
  entities: Entity[];
  preferences: KeysToCamelCase<Partial<Record<Preference['key'], string>>>;
};
