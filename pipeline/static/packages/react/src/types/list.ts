import { CustomFieldType } from '../components/CustomFields/CustomFields';
import { Group } from './group';

export interface ListTemplateFieldSimpleWithValue {
  visible_to_vendors: boolean;
  id: number;
  type: CustomFieldType;
  value: any;
  label: string;
}

interface FieldConfig {
  name: string;
  editable?: boolean;
}

export interface ListTemplateField {
  id: number;
  brief_label: string;
  choices: string[];
  description: string;
  field_group?: string;
  label: string;
  mandatory: boolean;
  max_len: any;
  order: number;
  other_label?: string;
  type: CustomFieldType;
  visible_to_vendors: boolean;
}

interface ListCustomFieldsTemplate {
  id: number;
  template_fields: ListTemplateField[];
}

export interface VendorList extends Group {
  type: 'private';
  fields_config: FieldConfig[];
  list_custom_fields_template: ListCustomFieldsTemplate;
  sharable_template: Group['sharable_template'];
  cover_photo: string;
  logo: string;
}
