import { SearchParams } from '../components/Search/ElasticSearch/ElasticSearch.types';
import { SimpleUser } from './user';
import { Vendor } from './vendor';

export type ExpensesSearchParams = SearchParams & {
  aggregations: boolean;
};

type Action = {
  slug: string;
  is_allowed: boolean;
  reasons: string[] | null;
};

export const enum ExpenseStatus {
  NEW = 'new',
  REJECTED = 'rejected',
  APPROVED = 'approved',
  BUNDLED = 'bundled',
  SCHEDULED = 'scheduled',
  PROCESSING = 'processing',
  IN_FLIGHT = 'in_flight',
  PAID = 'paid',
}

type Category = {
  id: number;
  name: string;
};

type Task = {
  id: number;
  name: string;
  status: string;
  task_group: number;
  task_group_name: string;
};

// TODO: fix this type once it is
// implemented on backend side
type Invoice = {
  id: number;
  name: string;
  status: string;
};

export type Expense = {
  actions_availability: Record<ExpenseAction, Action>;
  amount: number;
  approved_by: SimpleUser | null;
  approver: SimpleUser | null;
  category: Category | null;
  created_at: string;
  created_by: SimpleUser | null;
  currency: string;
  deleted_at: string | null;
  expense_id: string;
  filename: string | null;
  id: number;
  incurred_at: string;
  invoice: Invoice;
  name: string;
  note: string | null;
  rejected_at: string | null;
  rejection_reason: string | null;
  status: ExpenseStatus;
  task: Task | null;
  updated_at: string;
  vendor: Vendor | null;
};

export const ExpenseActions = [
  'approve',
  'reject',
  // todo: enable remaining actions and implement handlers
  // 'add_similar',
  // 'delete',
  // 'download_attachment',
  // 'edit',
  // 'flag_problem',
  // 'generate_pdf',
  // 'paid',
  // 'schedule',
  // 'unflag_problem',
  // 'unpaid',
  // 'unrejected',
  // 'unschedule',
  // 'generate_zip',
] as const;

export type ExpenseAction = (typeof ExpenseActions)[number];
