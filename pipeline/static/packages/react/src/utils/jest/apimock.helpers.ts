type historyResult = {
  url: string;
  data?: any;
  params?: any;
};
type simplifiedHistory = {
  get?: historyResult[];
  post?: historyResult[];
  patch?: historyResult[];
  put?: historyResult[];
  delete?: historyResult[];
};

export const simplifiedAPIHistory = (apiMock): simplifiedHistory => {
  const result: simplifiedHistory = {};
  const methods = ['get', 'post', 'patch', 'put', 'delete'];
  for (const method of methods) {
    if (
      Array.isArray(apiMock.history[method]) &&
      apiMock.history[method].length > 0
    ) {
      result[method] = apiMock.history[method].map((entry) => {
        return {
          url: entry.url,
          ...(entry.data ? { data: entry.data } : {}),
          ...(entry.params ? { params: entry.params } : {}),
        };
      });
    }
  }
  return result;
};
