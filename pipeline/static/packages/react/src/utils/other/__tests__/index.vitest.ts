import { nullifyProps, searchItemsByQuery } from '..';

describe('searchItemsByQuery - ', () => {
  const columns = [
    {
      _translatedName: 'Skills',
      id: 'VendorSkills',
    },
    {
      _translatedName: 'Titles',
      id: 'VendorTitles',
    },
    {
      _translatedName: 'Description',
      id: 'custom_25',
    },
    {
      _translatedName: 'Number of kids',
      id: 'custom_21',
    },
    {
      _translatedName: 'Description',
      id: 'VendorDescription',
    },
  ];
  test('should return filtered columns if search string matches', () => {
    const filteredColumns = searchItemsByQuery(
      columns,
      'descr',
      (i: any) => i._translatedName
    );

    expect(filteredColumns.length).toEqual(2);
    filteredColumns.forEach((column) =>
      expect(column._translatedName).toEqual('Description')
    );
  });
  test("should return empty array if search string doesn't match", () => {
    const filteredColumns = searchItemsByQuery(
      columns,
      'hourly',
      (i: any) => i._translatedName
    );

    expect(filteredColumns.length).toEqual(0);
  });
  test('should return original array if search string is empty', () => {
    const filteredColumns = searchItemsByQuery(
      columns,
      '',
      (i: any) => i._translatedName
    );

    expect(filteredColumns.length).toEqual(columns.length);
  });
});

describe('nullifyProps', () => {
  test('should nullify specified properties', () => {
    const obj = { a: 1, b: 2, c: 3 };
    expect(nullifyProps(obj, ['b'])).toEqual({ a: 1, b: null, c: 3 });
  });

  test('should not nullify objects by default', () => {
    const obj = { a: 1, b: { c: 2, d: 3 }, e: 4 };
    expect(nullifyProps(obj, ['b'])).toEqual(obj);
  });

  test('should nullify entire objects when nullifyObjects is true', () => {
    const obj = { a: 1, b: { c: 2, d: 3 }, e: 4 };
    expect(nullifyProps(obj, ['b'], true)).toEqual({ a: 1, b: null, e: 4 });
  });

  test('should handle arrays', () => {
    const arr = { a: 1, b: ['c', 'd'], e: 4 };
    expect(nullifyProps(arr, ['e'])).toEqual({ a: 1, b: ['c', 'd'], e: null });
  });

  test('should return null for null input', () => {
    expect(nullifyProps(null, [])).toBeNull();
  });
});
