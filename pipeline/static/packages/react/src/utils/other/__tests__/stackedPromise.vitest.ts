import { getStackedPromise, useStackedPromise } from '../stackedPromise';
import { renderHook, waitFor } from '@testing-library/react';

const getFetchPromise = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        '1': 'a',
        '2': 'b',
        111: 'b',
        222: 'b',
      });
    }, 0);
  });
};

describe('getStackedPromise', () => {
  test('requesting one set should be called only once', async () => {
    const fetchPromise = vi.fn(getFetchPromise);
    await getStackedPromise(['1', '2'], 'stack-1', fetchPromise);
    expect(fetchPromise).toHaveBeenCalledTimes(1);
    expect(fetchPromise).toHaveBeenLastCalledWith(['1', '2']);
  });

  test('requesting two sets should be called only once', async () => {
    const fetchPromise = vi.fn(getFetchPromise);
    const cacheId = 'stack-1';
    const promise1 = getStackedPromise(['11', '22'], cacheId, fetchPromise);
    const promise2 = getStackedPromise(['33', '44'], cacheId, fetchPromise);
    await Promise.all([promise1, promise2]);
    expect(fetchPromise).toHaveBeenCalledTimes(1);
    expect(fetchPromise).toHaveBeenLastCalledWith(['11', '22', '33', '44']);
    expect(promise1).toBe(promise2);
  });

  test('requesting two sets with different stack ids should be called twice', async () => {
    const fetchPromise = vi.fn(getFetchPromise);
    const promise1 = getStackedPromise(['111', '222'], 'stack-1', fetchPromise);
    const promise2 = getStackedPromise(['111', '222'], 'stack-2', fetchPromise);
    const [result1, result2] = await Promise.all([promise1, promise2]);
    expect(fetchPromise).toHaveBeenCalledTimes(2);
    expect(promise1).not.toBe(promise2);
    expect(result1).toMatchObject({ '111': 'b', '222': 'b' });
    expect(result2).toMatchObject({ '111': 'b', '222': 'b' });
  });

  test('first request should cache, next two should not use cache', async () => {
    const fetchPromise = vi.fn(getFetchPromise);
    const cacheId = 'stack-3';

    await getStackedPromise(['33', '44'], cacheId, fetchPromise);
    expect(fetchPromise).toHaveBeenLastCalledWith(['33', '44']);
    const promise1 = await getStackedPromise(
      ['33', '44'],
      cacheId,
      fetchPromise,
      {
        cache: false,
      }
    );
    expect(fetchPromise).toHaveBeenLastCalledWith(['33', '44']);
    const promise2 = await getStackedPromise(
      ['33', '44'],
      cacheId,
      fetchPromise,
      {
        cache: false,
      }
    );
    expect(fetchPromise).toHaveBeenLastCalledWith(['33', '44']);
    expect(fetchPromise).toHaveBeenCalledTimes(3);
    expect(promise1).not.toBe(promise2);
  });

  test('two requests without cache but requested in the same time should have same promise', async () => {
    const fetchPromise = vi.fn(getFetchPromise);
    const cacheId = 'stack-4';

    await getStackedPromise(['33', '44'], cacheId, fetchPromise);
    expect(fetchPromise).toHaveBeenLastCalledWith(['33', '44']);
    const promise1 = getStackedPromise(['33', '44'], cacheId, fetchPromise, {
      cache: false,
    });
    expect(fetchPromise).toHaveBeenLastCalledWith(['33', '44']);
    const promise2 = getStackedPromise(['33', '44'], cacheId, fetchPromise, {
      cache: false,
    });
    expect(fetchPromise).toHaveBeenLastCalledWith(['33', '44']);
    expect(fetchPromise).toHaveBeenCalledTimes(1);
    expect(promise1).toBe(promise2);
  });

  test('falsey responses should be returned', async () => {
    let callCount = 0;
    const cacheId = 'stack-41';

    const fetch = vi.fn(() => {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            '33': callCount++ === 0 ? 0 : false,
          });
        }, 0);
      });
    });

    const result1 = await getStackedPromise(['33'], cacheId, fetch, {
      cache: false,
    });
    expect(fetch).toHaveBeenLastCalledWith(['33']);
    expect(result1).toMatchObject({ '33': 0 });

    const result2 = await getStackedPromise(['33'], cacheId, fetch, {
      cache: false,
    });
    expect(fetch).toHaveBeenLastCalledWith(['33']);
    expect(result2).toMatchObject({ '33': false });
  });
});

describe('useStackedPromise', () => {
  test("returned data should be cleared if passed ids array is empty and previous data state wasn't empty", async () => {
    const fetchPromise = vi.fn(getFetchPromise);
    let ids = [];
    const { result, rerender } = renderHook(() =>
      useStackedPromise(ids, 'stack-3', fetchPromise)
    );

    expect(fetchPromise).toHaveBeenCalledTimes(0);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.data.length).toEqual(0);

    ids = ['111', '222'];
    rerender();

    await waitFor(() => {
      expect(fetchPromise).toHaveBeenCalledTimes(1);
    });
    expect(result.current.isLoading).toBe(false);
    expect(result.current.data.length).toEqual(2);

    ids = [];
    rerender();

    expect(fetchPromise).toHaveBeenCalledTimes(1);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.data.length).toEqual(0);
  });
});
