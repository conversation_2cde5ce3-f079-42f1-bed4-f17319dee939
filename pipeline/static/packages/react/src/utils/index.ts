import { LicenseInfo } from '@mui/x-license';
import currency from 'currency.js';
import { react2angular } from 'react2angular';
import { InvoiceLineItem } from '../components/LineItems/LineItems';
import { getApiPrefix } from '../services/Api/base';
import * as FilePickerUtils from '../services/Filepicker/filepicker';
import * as Date from './date';
import * as FileUtils from './files';
import * as LocaleUtils from './locale';
import { roundDecimal } from './other';
import * as Time from './time.utils';
import Validators from './validators';

export const setMuiLicense = () => {
  LicenseInfo.setLicenseKey(
    '2dd538f75d45435e3963d60ef13af64fTz0xMDc5NDcsRT0xNzcxNjMxOTk5MDAwLFM9cHJvLExNPXN1YnNjcmlwdGlvbixQVj1RMy0yMDI0LEtWPTI='
  );
};

export const formatCurrency = (
  value: string | number,
  decimals = 2,
  currency: string = null
) =>
  `${parseFloat(value.toString().split(',').join('')).toLocaleString('en-GB', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  })}${currency ? ` ${currency}` : ''}`;

export const formatIntegerNumber = (value: string | number): string => {
  if (isNaN(Number(value))) {
    return '';
  }
  return Math.trunc(parseFloat(value.toString())).toLocaleString('en-GB');
};

export const countTax = (amount: number, tax: number, decimals = 2) =>
  roundDecimal((tax / 100) * amount, decimals);

export const recalculateTax = (lineItems: InvoiceLineItem[]): number =>
  roundDecimal(
    parseFloat(
      lineItems
        .filter(({ _status: status }) => status !== 'deleted')
        .reduce(
          (prevValue, i) => currency(prevValue).add(i.tax_amount).value,
          0
        )
        .toString()
    )
  );

export const noop = (...args: any[]) => {}; //eslint-disable-line

export {
  react2angular,
  Validators,
  roundDecimal,
  Time,
  Date,
  LocaleUtils,
  FileUtils,
  getApiPrefix,
  FilePickerUtils,
};
