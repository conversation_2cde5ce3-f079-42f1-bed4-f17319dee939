import React from 'react';
import { withErrorBoundary } from 'react-error-boundary';
import { WorksuitePermissionError } from './errors';

function ErrorFallback({ error }) {
  if (error instanceof WorksuitePermissionError) {
    return null;
  }
  throw error;
}

export const withWorksuiteErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>
) =>
  withErrorBoundary(Component, {
    fallbackRender: ErrorFallback,
  });
