import debounce from 'lodash/debounce';
import {
  useCallback,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from 'react';

export type IsItemVisible<T = any> = (
  item: T,
  itemElement: HTMLElement,
  itemsContainerElement: HTMLElement
) => boolean;

const isItemVisibleDefault: IsItemVisible = (
  _,
  itemElement: HTMLElement,
  itemsContainerElement: HTMLElement
) => {
  const itemRect = itemElement.getBoundingClientRect();
  const itemsContainerRect = itemsContainerElement.getBoundingClientRect();
  return (
    Math.floor(itemRect.right) <= Math.floor(itemsContainerRect.right) &&
    Math.floor(itemRect.bottom) <= Math.floor(itemsContainerRect.bottom)
  );
};

// leaving generic default to `any` for legacy purposes
const useDetermineItemsVisibility = <T = any>(
  items: T[],
  isItemVisible?: IsItemVisible<T>
) => {
  const [renderCount, setRenderCount] = useState(0);
  const [visibleItems, setVisibleItems] = useState<T[]>(items);
  const [hiddenItems, setHiddenItems] = useState<T[]>([]);
  const itemsContainerRef = useRef<HTMLElement>(null);
  const itemElementRefs = useRef<Array<[T, HTMLElement]>>([]);
  itemElementRefs.current = [];

  const recalculateVisibilityFromScratch = useCallback(() => {
    setVisibleItems([...items]);
    setHiddenItems([]);
    setRenderCount(0);
  }, [items]);

  useEffect(recalculateVisibilityFromScratch, [items]);

  useEffect(() => {
    const handleResize = debounce(recalculateVisibilityFromScratch, 50);
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [recalculateVisibilityFromScratch]);

  useLayoutEffect(() => {
    if (renderCount >= 2) {
      // Second rerender should be sufficient to show everything.
      return;
    }
    if (!itemsContainerRef.current) {
      return;
    }

    let visibleCounter = 0;
    for (const [item, itemElement] of itemElementRefs.current) {
      if (!itemElement) {
        continue;
      }
      const itemVisible = (isItemVisible ?? isItemVisibleDefault)(
        item,
        itemElement,
        itemsContainerRef.current
      );
      if (itemVisible) {
        visibleCounter += 1;
      }
    }
    setVisibleItems(items.slice(0, visibleCounter));
    setHiddenItems(items.slice(visibleCounter));
    setRenderCount((prev) => prev + 1);
  });

  return {
    visibleItems,
    hiddenItems,

    setContainerElementRef: (element: HTMLElement) => {
      itemsContainerRef.current = element;
    },

    addItemElementRef: (item: T, element: HTMLElement) => {
      itemElementRefs.current.push([item, element]);
    },
  };
};

export default useDetermineItemsVisibility;
