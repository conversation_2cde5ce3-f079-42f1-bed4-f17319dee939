import debounce from 'lodash/debounce';
import { Dispatch, SetStateAction, useEffect, useRef, useState } from 'react';
import { cloneDeep } from '../other';

export const useOnClickOutside = (refs, handler, outsideRefs?) => {
  useEffect(() => {
    const listener = (event) => {
      if (
        refs.some((ref) => !ref.current || ref.current.contains(event.target))
      ) {
        return;
      }
      if (
        outsideRefs &&
        outsideRefs.some(
          (ref) => !ref.current || ref.current.contains(event.target)
        )
      ) {
        handler(event);
      } else if (!outsideRefs) {
        // beware that in case of outsideRefs missing, catches all clicks from document
        handler(event);
      }
    };

    document.addEventListener('click', listener);
    document.addEventListener('touchstart', listener);

    return () => {
      document.removeEventListener('click', listener);
      document.removeEventListener('touchstart', listener);
    };
  }, [...[refs], handler, ...[outsideRefs]]);
};

// https://usehooks.com/useDebounce/
export const useDebounce = <P>(value: P, delay: number) => {
  // State and setters for debounced value
  const [debouncedValue, setDebouncedValue] = useState(value);
  useEffect(
    () => {
      // Update debounced value after delay
      const handler = setTimeout(() => {
        setDebouncedValue(value);
      }, delay);
      // Cancel the timeout if value changes (also on delay change or unmount)
      // This is how we prevent debounced value from updating if value is changed ...
      // .. within the delay period. Timeout gets cleared and restarted.
      return () => {
        clearTimeout(handler);
      };
    },
    [value, delay] // Only re-call effect if value or delay changes
  );
  return debouncedValue;
};

export const useDebouncedState = <T>(
  initialValue: T,
  delay: number
): [T, Dispatch<SetStateAction<T>>] => {
  const [value, setValue] = useState<T>(initialValue);
  const updateQueue = useRef<Array<SetStateAction<T>>>([]);
  const debouncedApplyUpdates = useRef<ReturnType<typeof debounce>>();

  useEffect(() => {
    debouncedApplyUpdates.current = debounce(() => {
      setValue((prevValue) => {
        let nextValue = cloneDeep(prevValue);

        updateQueue.current.forEach((update) => {
          if (typeof update === 'function') {
            nextValue = (update as (prevState: T) => T)(nextValue);
          } else if (Array.isArray(nextValue)) {
            nextValue = update as T;
          } else if (typeof nextValue === 'object' && nextValue !== null) {
            nextValue = { ...nextValue, ...(update as T) };
          } else {
            nextValue = update;
          }
        });

        updateQueue.current = [];
        return nextValue;
      });
    }, delay);

    return () => {
      debouncedApplyUpdates.current?.cancel();
    };
  }, [delay]);

  const setDebouncedValue = (update: SetStateAction<T>) => {
    updateQueue.current.push(update);
    if (debouncedApplyUpdates.current) {
      debouncedApplyUpdates.current();
    }
  };

  return [value, setDebouncedValue];
};
