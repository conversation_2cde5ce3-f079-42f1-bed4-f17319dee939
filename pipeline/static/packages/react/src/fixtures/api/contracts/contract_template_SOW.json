{"id": 87, "name": "Simple SOW", "description": null, "with_end_date": true, "is_end_date_mandatory": false, "enable_expiration_period": true, "contract_details_template": null, "contract_setup_description": null, "with_fixed_rate_payments": false, "payments_setup_description": null, "with_statement_of_work": true, "workflow": null, "automations": [], "approval_type": "acceptance", "agreement_template": null, "agreement_choose_before_signing": false, "agreement_preview_before_signing": false, "signers": [{"id": 112, "type": "partner", "user": null, "acceptance_type": "contract"}], "amendment_signers": [{"id": 113, "type": "partner", "user": null, "acceptance_type": "contract_amendment"}], "number_of_contracts": 19, "archived": false}