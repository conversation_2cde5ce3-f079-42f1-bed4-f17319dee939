[{"id": 959, "reference_id": null, "name": "SOW with tasks and change requests 2", "description": "task added, task 2 changed", "template": 132, "partner": {"id": 1, "name": "Kinder fajer ;", "full_name": "Kinder fajer ;", "logo": "//s3-eu-west-1.amazonaws.com/shortlist-eu-west-1-logos/bury/vendor-logo/kinderfajer/QmS10NxS0CcUjNttfAoA_kinder.png", "slug": "<PERSON><PERSON><PERSON><PERSON>", "shortlisted": true, "email": "mateusz.bur<PERSON><PERSON>+<EMAIL>", "vendor_type": "freelancer", "status": "active", "onboarded": "onboarding-underway", "is_supplier": false, "vendor": true}, "external_partner": null, "vendor_group": null, "period": "fixed_dates", "duration": null, "start_date": "2024-08-27", "end_date": null, "status": "ended", "ending_soon": false, "under_revision": false, "archived": false, "created_at": "2024-08-27T07:53:41.248665Z", "created_by": {"id": 169, "first_name": "<PERSON>", "last_name": "Żogała", "email": "<EMAIL>", "avatar_color": "#74a588", "full_name": "<PERSON>", "name": "<PERSON>", "initials": "DŻ", "role": {"bitMask": 16, "title": "staff"}, "slug": "damian-zogala-2", "is_active": true, "vendor": false, "profile_picture_path": null, "deleted": false, "buyer": true, "guest": false}, "updated_at": "2024-08-27T07:53:41.248690Z", "actions": [], "payment_template": null, "revision_number": 2, "revisions": []}, {"id": 958, "reference_id": null, "name": "SOW with tasks and change requests 2", "description": "task added", "template": 132, "partner": {"id": 1, "name": "Kinder fajer ;", "full_name": "Kinder fajer ;", "logo": "//s3-eu-west-1.amazonaws.com/shortlist-eu-west-1-logos/bury/vendor-logo/kinderfajer/QmS10NxS0CcUjNttfAoA_kinder.png", "slug": "<PERSON><PERSON><PERSON><PERSON>", "shortlisted": true, "email": "mateusz.bur<PERSON><PERSON>+<EMAIL>", "vendor_type": "freelancer", "status": "active", "onboarded": "onboarding-underway", "is_supplier": false, "vendor": true}, "external_partner": null, "vendor_group": null, "period": "fixed_dates", "duration": null, "start_date": "2024-08-27", "end_date": null, "status": "ended", "ending_soon": false, "under_revision": false, "archived": false, "created_at": "2024-08-27T07:51:49.437493Z", "created_by": {"id": 169, "first_name": "<PERSON>", "last_name": "Żogała", "email": "<EMAIL>", "avatar_color": "#74a588", "full_name": "<PERSON>", "name": "<PERSON>", "initials": "DŻ", "role": {"bitMask": 16, "title": "staff"}, "slug": "damian-zogala-2", "is_active": true, "vendor": false, "profile_picture_path": null, "deleted": false, "buyer": true, "guest": false}, "updated_at": "2024-08-27T07:51:49.437516Z", "actions": [], "payment_template": null, "revision_number": 1, "revisions": []}, {"id": 957, "reference_id": null, "name": "SOW with tasks and change requests 2", "description": null, "template": 132, "partner": {"id": 1, "name": "Kinder fajer ;", "full_name": "Kinder fajer ;", "logo": "//s3-eu-west-1.amazonaws.com/shortlist-eu-west-1-logos/bury/vendor-logo/kinderfajer/QmS10NxS0CcUjNttfAoA_kinder.png", "slug": "<PERSON><PERSON><PERSON><PERSON>", "shortlisted": true, "email": "mateusz.bur<PERSON><PERSON>+<EMAIL>", "vendor_type": "freelancer", "status": "active", "onboarded": "onboarding-underway", "is_supplier": false, "vendor": true}, "external_partner": null, "vendor_group": null, "period": "fixed_dates", "duration": null, "start_date": "2024-08-27", "end_date": null, "status": "live", "ending_soon": false, "under_revision": false, "archived": false, "created_at": "2024-08-27T07:51:49.423376Z", "created_by": {"id": 11, "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "avatar_color": "#004158", "full_name": "<PERSON>", "name": "<PERSON>", "initials": "DZ", "role": {"bitMask": 16, "title": "staff"}, "slug": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is_active": true, "vendor": false, "profile_picture_path": null, "deleted": false, "buyer": true, "guest": false}, "updated_at": "2024-08-27T07:51:49.423394Z", "actions": [], "payment_template": null, "revision_number": 0, "revisions": []}]