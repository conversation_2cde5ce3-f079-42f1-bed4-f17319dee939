{"id": 330, "reference_id": null, "name": "Test Contract 3", "description": "Aliquam volutpat euismod sollicitudin. Duis leo ligula, blandit non urna id, laoreet vestibulum leo. Integer molestie purus massa, ac mollis ipsum hendrerit nec. Nunc tincidunt orci in urna congue tincidunt.", "currency": null, "template": 70, "partner": {"id": 1, "name": "Kinder fajer", "full_name": "Kinder fajer", "logo": "//s3-eu-west-1.amazonaws.com/shortlist-eu-west-1-logos/bury/vendor-logo/kinderfajer/QmS10NxS0CcUjNttfAoA_kinder.png", "slug": "<PERSON><PERSON><PERSON><PERSON>", "shortlisted": true, "email": "mateusz.bur<PERSON><PERSON>+<EMAIL>", "email_verified": true, "full_address": null, "location": {"city": "Tychy", "query": "Tychy, Polska", "state": "Śląskie", "_geoloc": {"lat": 50.12180069999999, "lng": 19.0200022}, "country": "Polska", "objectID": "ChIJgYJAAuLHFkcRoSazoHJEVto", "postal_code": "43", "administrative": "Śląskie"}, "phone_number": "+48515111222", "status": "active", "vendor_type": "freelancer", "onboarded": "onboarding-underway", "has_sign_request": false, "has_requested_document_expired": true}, "external_partner": null, "period": "fixed_dates", "duration": null, "start_date": "2024-05-06", "end_date": "2024-07-25", "status": "live", "ending_soon": false, "under_revision": false, "archived": false, "created_by": {"id": 11, "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "avatar_color": "#004158", "full_name": "<PERSON>", "name": "<PERSON>", "initials": "JD", "role": {"bitMask": 16, "title": "staff"}, "slug": "joe-doe", "is_active": true, "vendor": false, "profile_picture_path": null, "deleted": false, "buyer": true, "guest": false}, "updated_at": "2024-05-06T07:16:58.474936Z", "custom_fields": [], "vendor_workflow": null, "acceptances": [{"id": 226, "signer": {"id": 96, "type": "partner", "user": null, "acceptance_type": "contract"}, "user": {"id": 2, "first_name": "Kinder", "last_name": "fajer"}, "status": "accepted", "completed_at": "2024-05-06T07:17:18.616177Z", "rejection_reason": null}], "payment_template": {"name": "Kontrakt FR 26042024_1  - Monthly payment", "pay_period": {"frequency": "monthly", "month_days": [31], "week_days": [], "interval": 1}, "amount": 1000.0, "currency": "USD", "tax": 3, "tax_amount": 230.0, "category": 1, "first_pay_period_amount_calculations": ["full_amount", "custom_amount", "calculate_calendar_days", "calculate_working_days"], "last_pay_period_amount_calculations": ["full_amount", "custom_amount", "calculate_calendar_days", "calculate_working_days"], "first_pay_period_amount_calculation": "calculate_working_days", "first_pay_period_custom_amount": null, "last_pay_period_amount_calculation": "calculate_working_days", "last_pay_period_custom_amount": null, "first_pay_period": {"start_date": "2024-05-06", "end_date": "2024-05-31", "pay_date": "2024-05-31", "days": 20, "rate": 869.57, "amount": 1069.57, "day_rate": 43.48, "tax_amount": 200.0}, "last_pay_period": {"start_date": "2024-07-01", "end_date": "2024-07-25", "pay_date": "2024-07-31", "days": 19, "rate": 826.09, "amount": 1016.09, "day_rate": 43.48, "tax_amount": 190.0}, "custom_fields": [{"id": 186, "type": "date", "label": "Custom Field date", "value": "2024-04-27", "visible_to_vendors": true}], "assigned_buyer": {"id": 11, "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "avatar_color": "#004158", "full_name": "<PERSON>", "name": "<PERSON>", "initials": "JD", "role": {"bitMask": 16, "title": "staff"}, "slug": "joe-doe", "is_active": true, "vendor": false, "profile_picture_path": null, "deleted": false, "buyer": true, "guest": false}}, "task_templates": null, "actions": ["edit", "duplicate", "create_amendment"], "agreements": [], "revision_number": 0, "revisions": [{"id": 340, "status": "rejected", "revision_number": 3}, {"id": 337, "status": "rejected", "revision_number": 2}, {"id": 334, "status": "rejected", "revision_number": 1}]}