{"count": 117, "next": "https://weddingorg.testing.shortlist-test.net/api/tasks/?name=&ordering=-updated_at&page=6&page_size=12&search=&status=", "previous": "https://weddingorg.testing.shortlist-test.net/api/tasks/?name=&ordering=-updated_at&page=4&page_size=12&search=&status=", "results": [{"id": 79, "name": "prostyfmfelkfklfe", "description": null, "status": "accepted", "prev_status": "pending", "date_start": null, "date_end": null, "task_group": 52, "task_group_name": "feksklsfd lk", "vendor": {"id": 29, "name": "<PERSON>", "full_name": "<PERSON>", "logo": "//s3.amazonaws.com/shortlist-logos/weddingorg/vendor-logo/papa-smerf/gBoyZi3SeS7G6KJiUIy5_papasmerf.jpg", "slug": "papa-smerf", "shortlisted": true, "email": "<EMAIL>", "invited_at": "2020-11-08T17:07:31.218025Z", "status": "active", "has_sign_request": true, "is_company": false, "vendor_type": "freelancer", "company_name": "<PERSON>", "avatar_color": "#efc94c", "first_name": "<PERSON>", "last_name": "Smerf", "has_requested_document_expired": false, "availability": null, "availability_updated": null, "availability_requested": null, "available_from": null, "initials": "PS", "profile_picture_path": null, "onboarded": "onboarding-underway", "is_worker": false, "is_supplier": false, "parent": null}, "skills": [], "budget_rate_type": 1, "budget_rate_per_time_unit": 20.0, "budget_time_units_worked": 5.0, "budget_total": 100.0, "currency": "USD", "updated_at": "2021-05-21T09:35:05.844852Z", "created_by": {"id": 1, "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "email": "joanna.kola<PERSON><EMAIL>", "avatar_color": "#f16233", "full_name": "<PERSON>", "name": "<PERSON>", "initials": "JK", "role": {"bitMask": 16, "title": "staff"}, "slug": "joan<PERSON>-k<PERSON><PERSON><PERSON>", "is_company": null, "is_active": true, "vendor": false, "company_name": null, "profile_picture_path": null, "vendor_logo": null, "vendor_slug": null, "deleted": false, "buyer": true, "guest": false}, "created_at": "2021-05-21T09:34:19.449392Z", "can_edit": true, "can_add_invoice": true, "rejection_reason": null, "unread_messages": 0, "task_managers": [], "files_count": 0, "custom_fields": [], "custom_fields_templates": [49], "work_dates": [], "task_template": 53, "teams": [], "timesheets_approvers": [2]}, {"id": 78, "name": "task dla teamu", "description": null, "status": "draft", "prev_status": "draft", "date_start": null, "date_end": null, "task_group": 51, "task_group_name": "Projekt dla teamu add to jo", "vendor": null, "skills": [], "budget_rate_type": 1, "budget_rate_per_time_unit": null, "budget_time_units_worked": null, "budget_total": null, "currency": "USD", "updated_at": "2021-04-23T12:54:12.549933Z", "created_by": {"id": 1, "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "email": "joanna.kola<PERSON><EMAIL>", "avatar_color": "#f16233", "full_name": "<PERSON>", "name": "<PERSON>", "initials": "JK", "role": {"bitMask": 16, "title": "staff"}, "slug": "joan<PERSON>-k<PERSON><PERSON><PERSON>", "is_company": null, "is_active": true, "vendor": false, "company_name": null, "profile_picture_path": null, "vendor_logo": null, "vendor_slug": null, "deleted": false, "buyer": true, "guest": false}, "created_at": "2021-04-23T12:54:11.919463Z", "can_edit": true, "can_add_invoice": false, "rejection_reason": null, "unread_messages": 0, "task_managers": [], "files_count": 0, "custom_fields": [], "custom_fields_templates": [49], "work_dates": [], "task_template": 53, "teams": [], "timesheets_approvers": []}, {"id": 77, "name": "TS approv  test", "description": null, "status": "draft", "prev_status": "draft", "date_start": null, "date_end": null, "task_group": 50, "task_group_name": "TS app", "vendor": null, "skills": [], "budget_rate_type": 1, "budget_rate_per_time_unit": 5.0, "budget_time_units_worked": 30.0, "budget_total": 150.0, "currency": "USD", "updated_at": "2021-04-21T07:13:32.538077Z", "created_by": {"id": 1, "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "email": "joanna.kola<PERSON><EMAIL>", "avatar_color": "#f16233", "full_name": "<PERSON>", "name": "<PERSON>", "initials": "JK", "role": {"bitMask": 16, "title": "staff"}, "slug": "joan<PERSON>-k<PERSON><PERSON><PERSON>", "is_company": null, "is_active": true, "vendor": false, "company_name": null, "profile_picture_path": null, "vendor_logo": null, "vendor_slug": null, "deleted": false, "buyer": true, "guest": false}, "created_at": "2021-04-21T07:13:31.325513Z", "can_edit": true, "can_add_invoice": false, "rejection_reason": null, "unread_messages": 0, "task_managers": [], "files_count": 0, "custom_fields": [], "custom_fields_templates": [49], "work_dates": [], "task_template": 53, "teams": [], "timesheets_approvers": [65, 2]}, {"id": 76, "name": "TS testy testy", "description": null, "status": "accepted", "prev_status": "pending", "date_start": null, "date_end": null, "task_group": 49, "task_group_name": "test TS", "vendor": {"id": 29, "name": "<PERSON>", "full_name": "<PERSON>", "logo": "//s3.amazonaws.com/shortlist-logos/weddingorg/vendor-logo/papa-smerf/gBoyZi3SeS7G6KJiUIy5_papasmerf.jpg", "slug": "papa-smerf", "shortlisted": true, "email": "<EMAIL>", "invited_at": "2020-11-08T17:07:31.218025Z", "status": "active", "has_sign_request": true, "is_company": false, "vendor_type": "freelancer", "company_name": "<PERSON>", "avatar_color": "#efc94c", "first_name": "<PERSON>", "last_name": "Smerf", "has_requested_document_expired": false, "availability": null, "availability_updated": null, "availability_requested": null, "available_from": null, "initials": "PS", "profile_picture_path": null, "onboarded": "onboarding-underway", "is_worker": false, "is_supplier": false, "parent": null}, "skills": [], "budget_rate_type": 1, "budget_rate_per_time_unit": 100.0, "budget_time_units_worked": 10.0, "budget_total": 1000.0, "currency": "USD", "updated_at": "2021-04-14T11:03:53.188831Z", "created_by": {"id": 1, "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "email": "joanna.kola<PERSON><EMAIL>", "avatar_color": "#f16233", "full_name": "<PERSON>", "name": "<PERSON>", "initials": "JK", "role": {"bitMask": 16, "title": "staff"}, "slug": "joan<PERSON>-k<PERSON><PERSON><PERSON>", "is_company": null, "is_active": true, "vendor": false, "company_name": null, "profile_picture_path": null, "vendor_logo": null, "vendor_slug": null, "deleted": false, "buyer": true, "guest": false}, "created_at": "2021-04-14T11:02:02.548615Z", "can_edit": true, "can_add_invoice": true, "rejection_reason": null, "unread_messages": 0, "task_managers": [], "files_count": 0, "custom_fields": [], "custom_fields_templates": [49], "work_dates": [], "task_template": 53, "teams": [], "timesheets_approvers": []}, {"id": 75, "name": "buyer regular 08/04/2021", "description": null, "status": "draft", "prev_status": "draft", "date_start": null, "date_end": null, "task_group": 47, "task_group_name": "buyer regular 08/04", "vendor": null, "skills": [], "budget_rate_type": 1, "budget_rate_per_time_unit": null, "budget_time_units_worked": null, "budget_total": null, "currency": "USD", "updated_at": "2021-04-08T07:24:43.594427Z", "created_by": {"id": 1, "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "email": "joanna.kola<PERSON><EMAIL>", "avatar_color": "#f16233", "full_name": "<PERSON>", "name": "<PERSON>", "initials": "JK", "role": {"bitMask": 16, "title": "staff"}, "slug": "joan<PERSON>-k<PERSON><PERSON><PERSON>", "is_company": null, "is_active": true, "vendor": false, "company_name": null, "profile_picture_path": null, "vendor_logo": null, "vendor_slug": null, "deleted": false, "buyer": true, "guest": false}, "created_at": "2021-04-08T07:21:13.422412Z", "can_edit": true, "can_add_invoice": false, "rejection_reason": null, "unread_messages": 0, "task_managers": [51], "files_count": 0, "custom_fields": [], "custom_fields_templates": [49], "work_dates": [], "task_template": 53, "teams": [], "timesheets_approvers": []}, {"id": 74, "name": "worker ss", "description": null, "status": "pending", "prev_status": "draft", "date_start": null, "date_end": null, "task_group": 46, "task_group_name": "testy worker ss", "vendor": {"id": 40, "name": "Worker Test", "full_name": "Worker Test", "logo": null, "slug": "worker-testing", "shortlisted": true, "email": "<EMAIL>", "invited_at": "2021-04-06T08:50:03.394423Z", "status": "active", "has_sign_request": true, "is_company": false, "vendor_type": "worker", "company_name": "Worker Test", "avatar_color": "#334d5c", "first_name": "Worker", "last_name": "Test", "has_requested_document_expired": false, "availability": null, "availability_updated": null, "availability_requested": null, "available_from": null, "initials": "WT", "profile_picture_path": null, "onboarded": "not-onboarded", "is_worker": true, "is_supplier": false, "parent": {"id": 39, "name": "Testowy Supplier", "full_name": "Testowy Supplier", "logo": null, "slug": "testowy-supplier", "shortlisted": true, "vendor_type": "supplier", "status": "invited", "onboarded": "not-onboarded", "is_supplier": true, "vendor": true}}, "skills": [], "budget_rate_type": 1, "budget_rate_per_time_unit": null, "budget_time_units_worked": null, "budget_total": null, "currency": "USD", "updated_at": "2021-04-06T08:52:39.912444Z", "created_by": {"id": 2, "first_name": "Buyer1", "last_name": "Jedynka", "email": "<EMAIL>", "avatar_color": "#2e708a", "full_name": "Buyer1 Jedynka", "name": "Buyer1 Jedynka", "initials": "BJ", "role": {"bitMask": 8, "title": "admin"}, "slug": "u-1vi2z", "is_company": null, "is_active": true, "vendor": false, "company_name": null, "profile_picture_path": null, "vendor_logo": null, "vendor_slug": null, "deleted": false, "buyer": true, "guest": false}, "created_at": "2021-04-06T08:52:31.268800Z", "can_edit": true, "can_add_invoice": true, "rejection_reason": null, "unread_messages": 0, "task_managers": [2, 51], "files_count": 0, "custom_fields": [], "custom_fields_templates": [49], "work_dates": [], "task_template": 53, "teams": [], "timesheets_approvers": []}, {"id": 73, "name": "prostyhhhh", "description": null, "status": "pending", "prev_status": "draft", "date_start": null, "date_end": null, "task_group": 45, "task_group_name": "selector", "vendor": {"id": 4, "name": "Groszek Groszkowy", "full_name": "Groszek Groszkowy", "logo": "//s3-eu-west-1.amazonaws.com/shortlist-eu-west-1-logos/weddingorg/vendor-logo/groszek/ijm9s3oSqS8tatZzHEqw_lis.jpg", "slug": "groszek", "shortlisted": true, "email": "joanna.kolacka+g<PERSON><PERSON><PERSON>@shortlist.co", "invited_at": "2020-07-06T20:03:00.115000Z", "status": "active", "has_sign_request": true, "is_company": false, "vendor_type": "freelancer", "company_name": "Groszek Groszkowy", "avatar_color": "#e27a3f", "first_name": "Groszek", "last_name": "Groszkowy", "has_requested_document_expired": false, "availability": null, "availability_updated": "2020-07-09T18:41:19.276467Z", "availability_requested": null, "available_from": null, "initials": "GG", "profile_picture_path": null, "onboarded": "onboarding-underway", "is_worker": false, "is_supplier": false, "parent": null}, "skills": [], "budget_rate_type": 1, "budget_rate_per_time_unit": null, "budget_time_units_worked": null, "budget_total": null, "currency": "USD", "updated_at": "2021-03-24T16:25:15.466063Z", "created_by": {"id": 1, "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "email": "joanna.kola<PERSON><EMAIL>", "avatar_color": "#f16233", "full_name": "<PERSON>", "name": "<PERSON>", "initials": "JK", "role": {"bitMask": 16, "title": "staff"}, "slug": "joan<PERSON>-k<PERSON><PERSON><PERSON>", "is_company": null, "is_active": true, "vendor": false, "company_name": null, "profile_picture_path": null, "vendor_logo": null, "vendor_slug": null, "deleted": false, "buyer": true, "guest": false}, "created_at": "2021-03-24T16:25:09.639881Z", "can_edit": true, "can_add_invoice": true, "rejection_reason": null, "unread_messages": 0, "task_managers": [], "files_count": 0, "custom_fields": [], "custom_fields_templates": [49], "work_dates": [], "task_template": 53, "teams": [], "timesheets_approvers": []}, {"id": 72, "name": "prosty", "description": "", "status": "accepted", "prev_status": "pending", "date_start": null, "date_end": null, "task_group": 42, "task_group_name": "retesty 12332211111", "vendor": {"id": 31, "name": "Grazyna Wro", "full_name": "Grazyna Wro", "logo": "//s3.amazonaws.com/shortlist-logos/weddingorg/vendor-logo/grazyna-wro/xTey7ojoToKunDsRUOTR_wrona.jpg", "slug": "grazyna-wro", "shortlisted": true, "email": "<EMAIL>", "invited_at": "2020-11-16T16:35:11.786195Z", "status": "active", "has_sign_request": true, "is_company": false, "vendor_type": "freelancer", "company_name": "Grazyna Wro", "avatar_color": "#01a7a5", "first_name": "Grazyna", "last_name": "<PERSON><PERSON>", "has_requested_document_expired": false, "availability": null, "availability_updated": null, "availability_requested": null, "available_from": null, "initials": "GW", "profile_picture_path": null, "onboarded": "not-onboarded", "is_worker": false, "is_supplier": false, "parent": null}, "skills": [], "budget_rate_type": null, "budget_rate_per_time_unit": null, "budget_time_units_worked": null, "budget_total": 1000.0, "currency": "USD", "updated_at": "2021-03-15T09:57:04.863003Z", "created_by": {"id": 1, "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "email": "joanna.kola<PERSON><EMAIL>", "avatar_color": "#f16233", "full_name": "<PERSON>", "name": "<PERSON>", "initials": "JK", "role": {"bitMask": 16, "title": "staff"}, "slug": "joan<PERSON>-k<PERSON><PERSON><PERSON>", "is_company": null, "is_active": true, "vendor": false, "company_name": null, "profile_picture_path": null, "vendor_logo": null, "vendor_slug": null, "deleted": false, "buyer": true, "guest": false}, "created_at": "2021-03-15T09:52:06.311657Z", "can_edit": true, "can_add_invoice": true, "rejection_reason": null, "unread_messages": 0, "task_managers": [2], "files_count": 0, "custom_fields": [], "custom_fields_templates": [49], "work_dates": [], "task_template": 53, "teams": [], "timesheets_approvers": []}, {"id": 71, "name": "prosty", "description": null, "status": "draft", "prev_status": "draft", "date_start": null, "date_end": null, "task_group": 40, "task_group_name": "testststs", "vendor": null, "skills": [], "budget_rate_type": 1, "budget_rate_per_time_unit": null, "budget_time_units_worked": null, "budget_total": null, "currency": "USD", "updated_at": "2021-03-09T09:53:00.308994Z", "created_by": {"id": 1, "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "email": "joanna.kola<PERSON><EMAIL>", "avatar_color": "#f16233", "full_name": "<PERSON>", "name": "<PERSON>", "initials": "JK", "role": {"bitMask": 16, "title": "staff"}, "slug": "joan<PERSON>-k<PERSON><PERSON><PERSON>", "is_company": null, "is_active": true, "vendor": false, "company_name": null, "profile_picture_path": null, "vendor_logo": null, "vendor_slug": null, "deleted": false, "buyer": true, "guest": false}, "created_at": "2021-03-09T09:51:47.956422Z", "can_edit": true, "can_add_invoice": false, "rejection_reason": null, "unread_messages": 0, "task_managers": [2], "files_count": 0, "custom_fields": [], "custom_fields_templates": [49], "work_dates": [], "task_template": 53, "teams": [], "timesheets_approvers": []}, {"id": 70, "name": "pisupisupisup<PERSON>up<PERSON>upis<PERSON>", "description": "pisupisu", "status": "pending", "prev_status": "draft", "date_start": null, "date_end": null, "task_group": 37, "task_group_name": "Project 2", "vendor": {"id": 4, "name": "Groszek Groszkowy", "full_name": "Groszek Groszkowy", "logo": "//s3-eu-west-1.amazonaws.com/shortlist-eu-west-1-logos/weddingorg/vendor-logo/groszek/ijm9s3oSqS8tatZzHEqw_lis.jpg", "slug": "groszek", "shortlisted": true, "email": "joanna.kolacka+g<PERSON><PERSON><PERSON>@shortlist.co", "invited_at": "2020-07-06T20:03:00.115000Z", "status": "active", "has_sign_request": true, "is_company": false, "vendor_type": "freelancer", "company_name": "Groszek Groszkowy", "avatar_color": "#e27a3f", "first_name": "Groszek", "last_name": "Groszkowy", "has_requested_document_expired": false, "availability": null, "availability_updated": "2020-07-09T18:41:19.276467Z", "availability_requested": null, "available_from": null, "initials": "GG", "profile_picture_path": null, "onboarded": "onboarding-underway", "is_worker": false, "is_supplier": false, "parent": null}, "skills": null, "budget_rate_type": null, "budget_rate_per_time_unit": null, "budget_time_units_worked": null, "budget_total": null, "currency": "USD", "updated_at": "2021-02-17T08:57:01.383651Z", "created_by": {"id": 1, "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "email": "joanna.kola<PERSON><EMAIL>", "avatar_color": "#f16233", "full_name": "<PERSON>", "name": "<PERSON>", "initials": "JK", "role": {"bitMask": 16, "title": "staff"}, "slug": "joan<PERSON>-k<PERSON><PERSON><PERSON>", "is_company": null, "is_active": true, "vendor": false, "company_name": null, "profile_picture_path": null, "vendor_logo": null, "vendor_slug": null, "deleted": false, "buyer": true, "guest": false}, "created_at": "2021-02-17T08:57:01.135600Z", "can_edit": true, "can_add_invoice": true, "rejection_reason": null, "unread_messages": 0, "task_managers": [46], "files_count": 0, "custom_fields": [], "custom_fields_templates": [49], "work_dates": null, "task_template": null, "teams": [], "timesheets_approvers": []}, {"id": 69, "name": "<PERSON><PERSON><PERSON>@@!@", "description": "opis", "status": "pending", "prev_status": "draft", "date_start": null, "date_end": null, "task_group": 37, "task_group_name": "Project 2", "vendor": {"id": 31, "name": "Grazyna Wro", "full_name": "Grazyna Wro", "logo": "//s3.amazonaws.com/shortlist-logos/weddingorg/vendor-logo/grazyna-wro/xTey7ojoToKunDsRUOTR_wrona.jpg", "slug": "grazyna-wro", "shortlisted": true, "email": "<EMAIL>", "invited_at": "2020-11-16T16:35:11.786195Z", "status": "active", "has_sign_request": true, "is_company": false, "vendor_type": "freelancer", "company_name": "Grazyna Wro", "avatar_color": "#01a7a5", "first_name": "Grazyna", "last_name": "<PERSON><PERSON>", "has_requested_document_expired": false, "availability": null, "availability_updated": null, "availability_requested": null, "available_from": null, "initials": "GW", "profile_picture_path": null, "onboarded": "not-onboarded", "is_worker": false, "is_supplier": false, "parent": null}, "skills": null, "budget_rate_type": null, "budget_rate_per_time_unit": null, "budget_time_units_worked": null, "budget_total": null, "currency": "USD", "updated_at": "2021-02-17T08:57:00.718826Z", "created_by": {"id": 1, "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "email": "joanna.kola<PERSON><EMAIL>", "avatar_color": "#f16233", "full_name": "<PERSON>", "name": "<PERSON>", "initials": "JK", "role": {"bitMask": 16, "title": "staff"}, "slug": "joan<PERSON>-k<PERSON><PERSON><PERSON>", "is_company": null, "is_active": true, "vendor": false, "company_name": null, "profile_picture_path": null, "vendor_logo": null, "vendor_slug": null, "deleted": false, "buyer": true, "guest": false}, "created_at": "2021-02-17T08:57:00.449425Z", "can_edit": true, "can_add_invoice": true, "rejection_reason": null, "unread_messages": 0, "task_managers": [46], "files_count": 0, "custom_fields": [], "custom_fields_templates": [49], "work_dates": null, "task_template": null, "teams": [], "timesheets_approvers": []}, {"id": 68, "name": "nazwa", "description": null, "status": "pending", "prev_status": "pending", "date_start": null, "date_end": null, "task_group": 37, "task_group_name": "Project 2", "vendor": {"id": 4, "name": "Groszek Groszkowy", "full_name": "Groszek Groszkowy", "logo": "//s3-eu-west-1.amazonaws.com/shortlist-eu-west-1-logos/weddingorg/vendor-logo/groszek/ijm9s3oSqS8tatZzHEqw_lis.jpg", "slug": "groszek", "shortlisted": true, "email": "joanna.kolacka+g<PERSON><PERSON><PERSON>@shortlist.co", "invited_at": "2020-07-06T20:03:00.115000Z", "status": "active", "has_sign_request": true, "is_company": false, "vendor_type": "freelancer", "company_name": "Groszek Groszkowy", "avatar_color": "#e27a3f", "first_name": "Groszek", "last_name": "Groszkowy", "has_requested_document_expired": false, "availability": null, "availability_updated": "2020-07-09T18:41:19.276467Z", "availability_requested": null, "available_from": null, "initials": "GG", "profile_picture_path": null, "onboarded": "onboarding-underway", "is_worker": false, "is_supplier": false, "parent": null}, "skills": [], "budget_rate_type": null, "budget_rate_per_time_unit": null, "budget_time_units_worked": null, "budget_total": null, "currency": "USD", "updated_at": "2021-02-17T08:55:49.515856Z", "created_by": {"id": 1, "first_name": "<PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "email": "joanna.kola<PERSON><EMAIL>", "avatar_color": "#f16233", "full_name": "<PERSON>", "name": "<PERSON>", "initials": "JK", "role": {"bitMask": 16, "title": "staff"}, "slug": "joan<PERSON>-k<PERSON><PERSON><PERSON>", "is_company": null, "is_active": true, "vendor": false, "company_name": null, "profile_picture_path": null, "vendor_logo": null, "vendor_slug": null, "deleted": false, "buyer": true, "guest": false}, "created_at": "2021-02-17T08:55:23.546902Z", "can_edit": true, "can_add_invoice": true, "rejection_reason": null, "unread_messages": 0, "task_managers": [46], "files_count": 0, "custom_fields": [], "custom_fields_templates": [49], "work_dates": [], "task_template": null, "teams": [], "timesheets_approvers": []}]}