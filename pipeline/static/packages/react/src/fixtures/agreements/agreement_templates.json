{"count": 4, "results": [{"id": 1, "legacy": false, "name": "Lorem ipsum", "confidential": false, "file": {"filename": "Agreement.pdf", "path": "Agreement.pdf", "url": "https://example.com/Agreement.pdf"}, "fields": ["current date"], "signers": [{"type": "partner", "role": "Partner", "editable": false}], "last_used": "2021-07-05T06:39:20.977044Z", "status": "ready", "type": "one-off"}, {"id": 2, "legacy": false, "name": "Multisign lorem ipsum", "confidential": true, "file": {"filename": "Multisign-agreement.pdf", "path": "Multisign-agreement.pdf", "url": "https://example.com/Multisign-agreement.pdf"}, "fields": ["current date", "first_name"], "signers": [{"type": "partner", "role": "Partner", "editable": false}, {"type": "user", "role": "Buyer 1", "id": 2, "email": "<EMAIL>", "name": "<PERSON>", "editable": true}, {"type": "user", "role": "Buyer 2", "id": 3, "email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>", "editable": true}, {"type": "user", "role": "Buyer 3", "id": 4, "email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>", "editable": true}, {"type": "user", "role": "Buyer 4", "id": "template_field.249", "editable": true}, {"type": "external", "role": "External signer", "name": "<PERSON>", "email": "<EMAIL>", "editable": true}], "last_used": "2022-10-26T06:39:20.977044Z", "status": "ready", "type": "one-off"}, {"id": 3, "legacy": true, "name": "Legacy template", "confidential": false, "file": {"filename": "Legacy-agreement.pdf", "path": "Legacy-agreement.pdf", "url": "https://example.com/Legacy-agreement.pdf"}, "fields": ["current date"], "signers": [{"type": "partner", "role": "Partner", "editable": false}, {"type": "user", "role": "Buyer", "id": 1, "email": "<EMAIL>", "name": "<PERSON>", "editable": true}, {"type": "external", "role": "External", "id": 2, "email": "", "name": "", "editable": true}], "status": "ready", "type": "task"}, {"id": 4, "legacy": false, "name": "Archived example", "confidential": false, "file": {"filename": "Archived-agreement.pdf", "path": "Archived-agreement.pdf", "url": "https://example.com/Archived-agreement.pdf"}, "fields": ["current date"], "signers": [{"type": "partner", "role": "Partner", "editable": false}, {"type": "user", "role": "Buyer", "id": 3, "email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>", "editable": true}], "last_used": "2022-06-26T06:39:20.977044Z", "status": "disabled", "type": "job-opening"}]}