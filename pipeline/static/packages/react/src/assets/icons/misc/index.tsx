import { Colors } from '../../../styles/global.styles';
import ArrowRightIcon from './arrow-right.svg';
import CheckboxOff from './checkbox-off.svg';
import CheckboxOn from './checkbox-on.svg';
import FileArchive from './file-archive.svg';
import FileDefault from './file-default.svg';
import FileExcel from './file-excel.svg';
import FilePdf from './file-pdf.svg';
import FilePresentation from './file-presentation.svg';
import FileWord from './file-word.svg';
import PayoutMethodPayoneer from './payout-method-payoneer.svg';
import PortfolioIcon from './portfolio.svg';
import styled from '@emotion/styled';
import PropTypes from 'prop-types';

const customIcon = (icon) => {
  const styledIcon = styled(icon)(
    ({
      fill = Colors.black,
      size = 48,
      height,
    }: {
      fill?: string;
      size?: number;
      height?: number;
    }) => ({
      fill,
      width: `${size}px`,
      height: height ? `${height}px` : `${size}px`,
      fillRule: 'evenodd',
      verticalAlign: 'middle',
    })
  );
  styledIcon.propTypes = {
    fill: PropTypes.string,
    size: PropTypes.number,
  };
  return styledIcon;
};

export const CheckboxOffIcon = customIcon(CheckboxOff);
export const CheckboxOnIcon = customIcon(CheckboxOn);
export const FileDefaultIcon = customIcon(FileDefault);
export const FileWordIcon = customIcon(FileWord);
export const FileExcelIcon = customIcon(FileExcel);
export const FileArchiveIcon = customIcon(FileArchive);
export const FilePresentationIcon = customIcon(FilePresentation);
export const FilePdfIcon = customIcon(FilePdf);
export const PayoutMethodPayoneerIcon = customIcon(PayoutMethodPayoneer);

export { ArrowRightIcon, PortfolioIcon };
