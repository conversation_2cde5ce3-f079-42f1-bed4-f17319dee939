import { ReactNode } from 'react';
import { Label } from '../../../../styles/typography';
import {
  ExpenseDetailsListItemElement,
  ExpenseDetailsListItemWrapper,
} from './ExpenseDetailsModalComponent.styles';

type Props = {
  label: string;
  children: ReactNode;
};

export function ExpenseDetailsListItem({ label, children }: Props) {
  return (
    <ExpenseDetailsListItemWrapper>
      <ExpenseDetailsListItemElement>
        <Label>{label}</Label>
      </ExpenseDetailsListItemElement>
      <ExpenseDetailsListItemElement>{children}</ExpenseDetailsListItemElement>
    </ExpenseDetailsListItemWrapper>
  );
}
