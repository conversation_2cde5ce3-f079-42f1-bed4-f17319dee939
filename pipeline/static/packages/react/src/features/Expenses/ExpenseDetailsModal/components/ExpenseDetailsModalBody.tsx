import { Expense } from '../../../../types/expenses';
import { ExpenseAlert, getExpenseAlertConfig } from './Alerts';
import { ExpenseDetailsModalBundledInvoice } from './ExpenseDetailsModalBundledInvoice';
import { ExpenseDetailsBodyWrapper } from './ExpenseDetailsModalComponent.styles';
import { ExpenseDetailsModalList } from './ExpenseDetailsModalList';
import { ExpenseDetailsModalSummary } from './ExpenseDetailsModalSummary';

export function ExpenseDetailsModalBody({ expense }: { expense: Expense }) {
  const hasInvoice = !!expense.invoice;
  const { shouldShowAlert } = getExpenseAlertConfig(expense);

  return (
    <ExpenseDetailsBodyWrapper>
      {shouldShowAlert && <ExpenseAlert expense={expense} />}
      <ExpenseDetailsModalSummary expense={expense} />
      <ExpenseDetailsModalList expense={expense} />
      {hasInvoice && (
        <ExpenseDetailsModalBundledInvoice invoice={expense.invoice} />
      )}
    </ExpenseDetailsBodyWrapper>
  );
}
