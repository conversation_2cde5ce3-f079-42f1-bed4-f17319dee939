import copy from 'copy-to-clipboard';
import { useState } from 'react';
import i18n from '../../../../i18n/i18n';
import { displaySuccessMessage } from '../../../../services/Reducers/Notifications.reducer.helper';
import { BodySmall, LabelSmall } from '../../../../styles/typography';
import { Expense } from '../../../../types/expenses';
import { formatDateTimeWithUserSettings } from '../../../../utils/date';
import {
  ExpenseDetailsSummaryCopyButton,
  ExpenseDetailsSummaryCopySuccessButton,
  ExpenseDetailsSummaryItem,
  ExpenseDetailsSummaryWrapper,
} from './ExpenseDetailsModalComponent.styles';

const t = (key: string) =>
  i18n.t(`Expenses:ExpenseDetails.ExpenseDetailsSummary.${key}`);

export function ExpenseDetailsModalSummary({ expense }: { expense: Expense }) {
  return (
    <ExpenseDetailsSummaryWrapper>
      <ExpenseDetailsSummaryItem>
        <LabelSmall>{t('ExpenseId')}:</LabelSmall>
        <BodySmall>{expense.expense_id}</BodySmall>
        <CopyWrapper expenseId={expense.expense_id} />
      </ExpenseDetailsSummaryItem>
      <ExpenseDetailsSummaryItem>
        <LabelSmall>{t('CreatedAt')}:</LabelSmall>
        <BodySmall>
          {formatDateTimeWithUserSettings(expense.created_at)}
        </BodySmall>
      </ExpenseDetailsSummaryItem>
    </ExpenseDetailsSummaryWrapper>
  );
}

function CopyWrapper({ expenseId }: { expenseId: string }) {
  const ACTION_TIMEOUT = 3000;
  const [copied, setCopied] = useState(false);

  const handleClick = () => {
    copy(expenseId);
    setCopied(true);
    displaySuccessMessage(t('ClipboardSuccessMessage'), {
      autoHideDuration: ACTION_TIMEOUT,
    });
    setTimeout(() => setCopied(false), ACTION_TIMEOUT);
  };

  return (
    <>
      {!copied && <ExpenseDetailsSummaryCopyButton onClick={handleClick} />}
      {copied && <ExpenseDetailsSummaryCopySuccessButton />}
    </>
  );
}
