import { Expense, ExpenseStatus } from '../../../../../types/expenses';

export function getExpenseAlertConfig(expense: Expense) {
  const hasInvoice = !!expense.invoice;
  const shouldShowRejectedAlert =
    expense.status === ExpenseStatus.REJECTED && expense.rejected_at;
  const shouldShowPaidAlert = expense.status === ExpenseStatus.PAID;
  const shouldShowBundledAlert =
    [
      ExpenseStatus.BUNDLED,
      ExpenseStatus.SCHEDULED,
      ExpenseStatus.PROCESSING,
      ExpenseStatus.IN_FLIGHT,
    ].includes(expense.status) && hasInvoice;

  const shouldShowAlert =
    shouldShowBundledAlert || shouldShowPaidAlert || shouldShowRejectedAlert;

  return {
    shouldShowAlert,
    alerts: {
      shouldShowBundledAlert,
      shouldShowPaidAlert,
      shouldShowRejectedAlert,
    },
  };
}
