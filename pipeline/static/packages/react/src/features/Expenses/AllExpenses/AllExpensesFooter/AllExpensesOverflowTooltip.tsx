import { Fragment, ReactNode } from 'react';
import Tooltip from '../../../../components/Utils/Tooltip/Tooltip';
import { formatCurrency } from '../../../../utils';
import {
  AllExpensesMoreCurrencyListContainer,
  AllExpensesTooltipContentWrapper,
} from './AllExpensesFooter.styles';
import { AllExpensesFooterAggregationsCurrencyTotal } from './AllexpensesFooter.types';

export function AllExpensesOverflowTooltip({
  children,
  items,
}: {
  children: ReactNode;
  items: AllExpensesFooterAggregationsCurrencyTotal[];
}) {
  return (
    <Tooltip
      placement="left"
      tooltip={
        <AllExpensesTooltipContentWrapper>
          {items.map(({ currency, amount, count }) => (
            <Fragment key={currency}>
              <span>{formatCurrency(amount)}</span>
              {` ${currency} (${count})`}
            </Fragment>
          ))}
        </AllExpensesTooltipContentWrapper>
      }
    >
      <AllExpensesMoreCurrencyListContainer>
        {children}
      </AllExpensesMoreCurrencyListContainer>
    </Tooltip>
  );
}
