import React, { useState } from 'react';
import { TickCircleIcon } from '../../../assets/icons/small';
import Button from '../../../components/FormElements/Button/Button';
import { withModal } from '../../../components/Utils/Modals/modal.hooks';
import { ModalFooter } from '../../../components/Utils/Modals/modal.styles';
import { ShortlistModal } from '../../../components/Utils/Modals/modal.types';
import i18n from '../../../i18n/i18n';
import Upsell from '../../../services/Api/Upsell';
import { HeaderLarge, Label, LabelSlim } from '../../../styles/typography';
import {
  CheckListItem,
  CheckListWrapper,
  InquiryLabel,
  UpsellModalBody,
} from './UpsellModal.styles';

export interface UpsellModal extends ShortlistModal {
  type: 'upsell_modal';
}

const t = (key: string) => i18n.t(`Upsell:Payout.Modal.${key}`);

export default withModal(
  () => {
    const [inquirySent, setInquirySent] = useState(false);

    const handleClick = async () => {
      await Upsell.request('payments');
      setInquirySent(true);
    };

    return (
      <>
        <UpsellModalBody>
          <img src="/img/upsell-pay.svg" alt="Upsell Pay Image SVG" />
          <HeaderLarge>{t('Header')}</HeaderLarge>
          <CheckList />
        </UpsellModalBody>
        <ModalFooter>
          {!inquirySent && (
            <Button
              variant="upsellPromo"
              label={t('CTA')}
              onClick={handleClick}
            />
          )}
          {inquirySent && (
            <InquiryLabel>{t('InquirySentMessage')}</InquiryLabel>
          )}
        </ModalFooter>
      </>
    );
  },
  {
    getHeader: () => t('Title'),
    defaultModalSize: 'lg',
  }
);

function CheckList() {
  const items = [
    'GlobalPayout',
    'ConciergeService',
    'RealTimeVisibility',
    'SimplifiedAccounting',
    'ReducedRisk',
  ];

  return (
    <CheckListWrapper>
      {items.map((item) => (
        <CheckListItem key={item}>
          <TickCircleIcon fill="var(--promo-promopurple-100)" />
          <span>
            <Label>{t(`${item}.Title`)}</Label>{' '}
            <LabelSlim>{t(`${item}.Description`)}</LabelSlim>
          </span>
        </CheckListItem>
      ))}
    </CheckListWrapper>
  );
}
