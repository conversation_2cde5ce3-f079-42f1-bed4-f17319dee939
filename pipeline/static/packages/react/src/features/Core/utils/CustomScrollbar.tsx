import { Colors } from '../../../styles/global.styles';
import { useDebouncedState } from '../../../utils/hooks';
import styled from '@emotion/styled';
import PerfectScrollbar from 'perfect-scrollbar';
import 'perfect-scrollbar/css/perfect-scrollbar.css';
import React, { useEffect, useRef, useState } from 'react';

const PerfectScrollbarWrapper = styled('div')(({
  barColor,
  alwaysVisible,
}: {
  barColor?: string;
  alwaysVisible: boolean;
}) => {
  return {
    position: 'relative',
    boxSizing: 'border-box',

    '.ps__rail-y, .ps__rail-y:hover': {
      background: 'transparent !important',
      '.ps__thumb-y': {
        width: '7px !important',
        opacity: 0.8,
      },
    },

    '.ps__rail-y ': {
      '.ps__thumb-y': {
        background: `${barColor || Colors.grey2} !important`,
      },
    },

    '.ps__rail-y:hover, .ps__rail-y.ps--clicking': {
      '.ps__thumb-y': {
        opacity: 1,
      },
    },

    ...(alwaysVisible && {
      '.ps__rail-y': {
        opacity: '1 !important',
      },
    }),
  };
});
const CustomScrollbar = ({
  children,
  height,
  barColor,
  alwaysVisible = false,
  update = false,
}: {
  children: React.ReactNode;
  height: number;
  barColor?: string;
  alwaysVisible?: boolean;
  update?: boolean;
}) => {
  const [perfectScrollbar, setPerfectScrollbar] = useState(null);
  const [childrenHeight, setChildrenHeight] = useDebouncedState(null, 10);
  const menuScrollRef = useRef(null);
  const menuScroll = menuScrollRef.current;
  const childrenRef = useRef(null);

  useEffect(() => {
    if (!childrenRef.current) {
      return;
    }
    const resizeObserver = new ResizeObserver(() => {
      childrenRef.current?.clientHeight &&
        setChildrenHeight(childrenRef.current.clientHeight);
    });
    resizeObserver.observe(childrenRef.current);
    return () => resizeObserver.disconnect();
  }, []);

  const initiateScrollbar = () => {
    perfectScrollbar?.destroy();
    menuScroll &&
      setPerfectScrollbar(
        new PerfectScrollbar(menuScroll, {
          wheelSpeed: 0.3,
        })
      );
  };

  useEffect(() => {
    if (perfectScrollbar) {
      perfectScrollbar.update();
    }
  }, [height, childrenHeight]);

  useEffect(() => {
    initiateScrollbar();
  }, [alwaysVisible, menuScroll, update]);

  return (
    <PerfectScrollbarWrapper
      ref={menuScrollRef}
      style={{ height: `${height}px` }}
      barColor={barColor}
      alwaysVisible={alwaysVisible}
    >
      <div ref={childrenRef}>{children}</div>
    </PerfectScrollbarWrapper>
  );
};

export default CustomScrollbar;
