import { composeStories } from '@storybook/react';
import { fireEvent, render, screen } from '@testing-library/react';
import * as stories from './InputPartners.stories';

describe('InputPartners - ', () => {
  test('Default snapshot should match', async () => {
    const { Default: InputPartners } = composeStories(stories);
    const { container } = render(<InputPartners />);
    const input = container.querySelector('input[role="combobox"]');
    fireEvent.keyDown(input, { key: 's' });
    fireEvent.input(input, { target: { value: 's' } });
    expect(await screen.findByText('Worker 1')).toBeVisible();
    expect(container).toMatchSnapshot();
  });
});

describe('InputPartners - ', () => {
  test('DefaultValues snapshot should match', async () => {
    const { DefaultValues: InputPartners } = composeStories(stories);
    const { container } = render(<InputPartners />);
    const input = await screen.findByDisplayValue('Staffing Supplier');
    expect(input).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
});
