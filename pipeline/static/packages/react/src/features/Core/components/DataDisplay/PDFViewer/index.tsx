import NativePDFViewer, { supportsPDFNatively } from './NativePDFViewer';
import InlinePDFViewer from './PDFViewer';
import PropTypes from 'prop-types';
import React from 'react';

const PDFViewer = ({ pdfUrl }: { pdfUrl: string }) => {
  if (supportsPDFNatively()) {
    return <NativePDFViewer pdfUrl={pdfUrl} />;
  }

  return <InlinePDFViewer pdfUrl={pdfUrl} />;
};

export default PDFViewer;

PDFViewer.propTypes = {
  pdfUrl: PropTypes.string,
};
