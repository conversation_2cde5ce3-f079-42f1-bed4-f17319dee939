import { sub } from 'date-fns';

export const messages = [
  {
    id: 17,
    message_type: 'task:390',
    created_by: {
      slug: null,
      first_name: 'Worksuite',
      last_name: 'Support',
      email: '<EMAIL>',
      full_name: 'Worksuite Support',
      initials: 'W <PERSON>',
      avatar_color: '#d6d6d6',
      vendor: false,
      company_name: '',
      id: null,
      is_staff: true,
      profile_picture_path: [
        '//s3.amazonaws.com/shortlist-logos/support_avatar.svg',
      ],
    },
    content: 'Hello <PERSON>',
    parent_message: null,
    created_at: '2024-07-30T13:09:44.505524Z',
    updated_at: '2024-07-30T13:09:44.505529Z',
    deleted_at: null,
    can_edit: false,
    secured: false,
    vendor_id: 1,
  },
  {
    id: 18,
    message_type: 'task:390',
    created_by: {
      slug: null,
      first_name: 'Worksuite',
      last_name: 'Support',
      email: '<EMAIL>',
      full_name: 'Worksuite Support',
      initials: 'W <PERSON>',
      avatar_color: '#d6d6d6',
      vendor: false,
      company_name: '',
      id: null,
      is_staff: true,
      profile_picture_path: [
        '//s3.amazonaws.com/shortlist-logos/support_avatar.svg',
      ],
    },
    content: 'I am testing stuff',
    parent_message: null,
    created_at: '2024-07-30T13:09:47.789899Z',
    updated_at: '2024-07-30T13:09:47.789904Z',
    deleted_at: '2024-07-30T13:09:47.789904Z',
    can_edit: false,
    secured: false,
    vendor_id: 1,
  },
  {
    id: 19,
    message_type: 'task:390',
    created_by: {
      slug: null,
      first_name: 'Worksuite',
      last_name: 'Support',
      email: '<EMAIL>',
      full_name: 'Worksuite Support',
      initials: 'W S',
      avatar_color: '#d6d6d6',
      vendor: false,
      company_name: '',
      id: null,
      is_staff: true,
      profile_picture_path: [
        '//s3.amazonaws.com/shortlist-logos/support_avatar.svg',
      ],
    },
    content: 'can you respond?',
    parent_message: null,
    created_at: '2024-07-30T13:09:50.421293Z',
    updated_at: '2024-07-30T13:09:50.421298Z',
    deleted_at: null,
    can_edit: false,
    secured: false,
    vendor_id: 1,
  },
  {
    id: 20,
    message_type: 'task:390',
    created_by: {
      avatar_color: '#950719',
      slug: 'u-xwq3m',
      full_name: 'Kevin Malone',
      initials: 'KM',
      vendor_logo:
        '//s3-eu-west-1.amazonaws.com/shortlist-eu-west-1-logos/domwt/vendor-logo/rosalind-arusha-arkadina-altalune-florence-thurman/Y2jxMGtTQnOk2RwCW4rE_Office-1200-baumgartner1.jpg',
      profile_picture_path: null,
      vendor_slug: 'rosalind-arusha-arkadina-altalune-florence-thurman',
      deleted: false,
    },
    content: 'Hello I am responding',
    parent_message: null,
    created_at: '2024-07-30T13:10:43.908565Z',
    updated_at: '2024-07-30T13:10:43.908569Z',
    deleted_at: null,
    can_edit: true,
    secured: false,
    vendor_id: 1,
  },
  {
    id: 21,
    message_type: 'task:390',
    created_by: {
      avatar_color: '#950719',
      slug: 'u-xwq3m',
      full_name: 'Kevin Malone',
      initials: 'KM',
      vendor_logo:
        '//s3-eu-west-1.amazonaws.com/shortlist-eu-west-1-logos/domwt/vendor-logo/rosalind-arusha-arkadina-altalune-florence-thurman/Y2jxMGtTQnOk2RwCW4rE_Office-1200-baumgartner1.jpg',
      profile_picture_path: null,
      vendor_slug: 'rosalind-arusha-arkadina-altalune-florence-thurman',
      deleted: false,
    },
    content: 'what do you want?',
    parent_message: null,
    created_at: '2024-07-30T13:10:46.701994Z',
    updated_at: '2024-07-30T13:11:02.701998Z',
    deleted_at: null,
    can_edit: true,
    secured: false,
    vendor_id: 1,
  },
  {
    id: 211,
    message_type: 'task:390',
    created_by: {
      avatar_color: '#950719',
      slug: 'u-xwq3m',
      full_name: 'Kevin Malone',
      initials: 'KM',
      vendor_logo:
        '//s3-eu-west-1.amazonaws.com/shortlist-eu-west-1-logos/domwt/vendor-logo/rosalind-arusha-arkadina-altalune-florence-thurman/Y2jxMGtTQnOk2RwCW4rE_Office-1200-baumgartner1.jpg',
      profile_picture_path: null,
      vendor_slug: 'rosalind-arusha-arkadina-altalune-florence-thurman',
      deleted: false,
    },
    content: 'what do you want?',
    parent_message: null,
    created_at: sub(new Date(), { days: 1, minutes: 30 }).toISOString(),
    updated_at: sub(new Date(), { days: 1, minutes: 28 }).toISOString(),
    deleted_at: null,
    can_edit: true,
    secured: false,
    vendor_id: 1,
  },
  {
    id: 233,
    message_type: 'task:390',
    created_by: {
      slug: null,
      first_name: 'Worksuite',
      last_name: 'Support',
      email: '<EMAIL>',
      full_name: 'Worksuite Support',
      initials: 'W S',
      avatar_color: '#d6d6d6',
      vendor: false,
      company_name: '',
      id: null,
      is_staff: true,
      profile_picture_path: [
        '//s3.amazonaws.com/shortlist-logos/support_avatar.svg',
      ],
    },
    content: 'Are you here mate?',
    parent_message: null,
    created_at: sub(new Date(), { days: 1 }).toISOString(),
    updated_at: sub(new Date(), { days: 1 }).toISOString(),
    deleted_at: null,
    can_edit: false,
    secured: false,
    vendor_id: 1,
  },
  {
    id: 22,
    message_type: 'task:390',
    created_by: {
      slug: null,
      first_name: 'Worksuite',
      last_name: 'Support',
      email: '<EMAIL>',
      full_name: 'Worksuite Support',
      initials: 'W S',
      avatar_color: '#d6d6d6',
      vendor: false,
      company_name: '',
      id: null,
      is_staff: true,
      profile_picture_path: [
        '//s3.amazonaws.com/shortlist-logos/support_avatar.svg',
      ],
    },
    content:
      'Hello again, I wanted to be nice & helpful. Do you have anything that bothers you?',
    parent_message: null,
    created_at: sub(new Date(), { minutes: 20 }).toISOString(),
    updated_at: sub(new Date(), { minutes: 20 }).toISOString(),
    deleted_at: null,
    can_edit: false,
    secured: false,
    vendor_id: 1,
  },
  {
    id: 23,
    message_type: 'task:390',
    created_by: {
      slug: null,
      first_name: 'Worksuite',
      last_name: 'Support',
      email: '<EMAIL>',
      full_name: 'Worksuite Support',
      initials: 'W S',
      avatar_color: '#d6d6d6',
      vendor: false,
      company_name: '',
      id: null,
      is_staff: true,
      profile_picture_path: [
        '//s3.amazonaws.com/shortlist-logos/support_avatar.svg',
      ],
    },
    content: 'We wanna help with onboarding and stuff',
    parent_message: null,
    created_at: sub(new Date(), { minutes: 15 }).toISOString(),
    updated_at: sub(new Date(), { minutes: 15 }).toISOString(),
    deleted_at: null,
    can_edit: false,
    secured: false,
    vendor_id: 1,
  },
  {
    id: 24,
    message_type: 'task:390',
    created_by: {
      avatar_color: '#950719',
      slug: 'u-xwq3m',
      full_name: 'Kevin Malone',
      initials: 'KM',
      vendor_logo:
        '//s3-eu-west-1.amazonaws.com/shortlist-eu-west-1-logos/domwt/vendor-logo/rosalind-arusha-arkadina-altalune-florence-thurman/Y2jxMGtTQnOk2RwCW4rE_Office-1200-baumgartner1.jpg',
      profile_picture_path: null,
      vendor_slug: 'rosalind-arusha-arkadina-altalune-florence-thurman',
      deleted: false,
    },
    content:
      "Hey again, I don't have anything specific currently. We can talk later once I am more into the platform.",
    parent_message: null,
    created_at: sub(new Date(), { minutes: 6 }).toISOString(),
    updated_at: sub(new Date(), { minutes: 6 }).toISOString(),
    deleted_at: null,
    can_edit: true,
    secured: false,
    vendor_id: 1,
  },
  {
    id: 25,
    message_type: 'task:390',
    created_by: {
      avatar_color: '#950719',
      slug: 'u-xwq3m',
      full_name: 'Kevin Malone',
      initials: 'KM',
      vendor_logo:
        '//s3-eu-west-1.amazonaws.com/shortlist-eu-west-1-logos/domwt/vendor-logo/rosalind-arusha-arkadina-altalune-florence-thurman/Y2jxMGtTQnOk2RwCW4rE_Office-1200-baumgartner1.jpg',
      profile_picture_path: null,
      vendor_slug: 'rosalind-arusha-arkadina-altalune-florence-thurman',
      deleted: false,
    },
    content: 'What do you think?',
    parent_message: null,
    created_at: sub(new Date(), { minutes: 5 }).toISOString(),
    updated_at: sub(new Date(), { minutes: 5 }).toISOString(),
    deleted_at: null,
    can_edit: true,
    secured: false,
    vendor_id: 1,
  },
];
