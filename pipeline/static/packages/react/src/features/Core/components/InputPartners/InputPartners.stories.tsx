import React, { useEffect, useState } from 'react';
import vendors from '../../../../fixtures/api/vendors/vendors.json';
import { getApiMockAdapter } from '../../../../utils/storybook-utils';
import { withStorybookTenantManagement } from '../../../../utils/storybook.hooks';
import { InputPartners } from './InputPartners';

export default {
  title: 'Core/Components/InputPartners',
  component: InputPartners,
};

type Props = {
  defaultValues?: number[];
};

const Template = ({ defaultValues }: Props) => {
  const [isLoaded, setIsLoaded] = useState(false);
  useEffect(() => {
    const mock = getApiMockAdapter();
    mock.onGet(/\/api\/vendors\/.*/gm).reply(200, { results: vendors.hits });
    mock.onPost(/\/api\/search\/vendors\//gm).reply(200, vendors);
    setIsLoaded(true);
    return () => mock.reset();
  }, []);
  return isLoaded ? (
    <InputPartners
      // eslint-disable-next-line
      onChange={(values) => console.log('InputPartners', values)}
      defaultValues={defaultValues}
      placeholder="Search for a vendor"
    />
  ) : null;
};

export const Default = withStorybookTenantManagement(() => <Template />, {
  defaultFeatures: ['availability', 'availability_calendar'],
  changeableFeatures: ['availability', 'availability_calendar'],
});

export const DefaultValues = withStorybookTenantManagement(
  () => <Template defaultValues={[2]} />,
  {
    defaultFeatures: ['availability', 'availability_calendar'],
    changeableFeatures: ['availability', 'availability_calendar'],
  }
);
