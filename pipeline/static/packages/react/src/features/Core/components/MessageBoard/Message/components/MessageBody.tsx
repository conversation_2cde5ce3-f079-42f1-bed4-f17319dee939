import { SuggestionMeta } from '@worksuite/redlining';
import { MessageItem } from '../../MessageBoard.types';
import { QuotationWrapper } from '../Message.styles';
import { Direction } from '../Message.types';
import {
  MessageSuggestionHeader,
  getMessageSuggestionHeaderVariant,
} from './MessageSuggestionHeader';
import { MessageSuggestionStatus } from './MessageSuggestionStatus';

type Props = {
  suggestionMeta: SuggestionMeta;
  direction: Direction;
  message: MessageItem;
};

export function MessageBody({ message, direction, suggestionMeta }: Props) {
  const {
    content,
    status,
    quoted_text: quotation,
    reviewed_by: reviewedBy,
  } = message;
  const {
    isSuggestion,
    isQuotedText,
    isReplace,
    isAdd,
    isPending,
    isDelete,
    replacementText,
  } = suggestionMeta;

  return (
    <>
      {(isSuggestion || isQuotedText) && (
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <MessageSuggestionHeader
            variant={getMessageSuggestionHeaderVariant(suggestionMeta)}
            direction={direction}
          />
          <MessageSuggestionStatus
            status={isQuotedText && !isPending ? 'resolved' : status}
            reviewedBy={reviewedBy?.full_name}
          />
        </div>
      )}
      {quotation && (
        <QuotationWrapper
          title={quotation}
          variant={getQuotationWrapperVariant(isReplace, isDelete)}
        >
          {quotation}
        </QuotationWrapper>
      )}
      {(isReplace || isAdd) && (
        <>
          {isReplace && (
            <MessageSuggestionHeader variant="with" direction={direction} />
          )}
          <QuotationWrapper title={replacementText} variant="add">
            {replacementText}
          </QuotationWrapper>
        </>
      )}
      <div>{content}</div>
    </>
  );
}

function getQuotationWrapperVariant(isReplace: boolean, isDelete: boolean) {
  if (isReplace || isDelete) {
    return 'remove' as const;
  }
  return undefined;
}
