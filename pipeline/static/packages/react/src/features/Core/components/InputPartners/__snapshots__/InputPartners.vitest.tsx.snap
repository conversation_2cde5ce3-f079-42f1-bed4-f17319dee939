// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`InputPartners -  > Default snapshot should match 1`] = `
.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class:hover .checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class {
  margin: 1px 0 -1px 6px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  color: #303757;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  fill: #000000;
  width: 28px;
  height: 28px;
  fill-rule: evenodd;
  vertical-align: middle;
  position: absolute;
  top: 10px;
  right: 5px;
  fill: #465AB6;
  pointer-events: none;
}

.emotion-class .MuiAutocomplete-inputRoot[class*="MuiOutlinedInput-root"] .MuiAutocomplete-input {
  padding-left: 35px!important;
}

.emotion-class {
  position: relative;
}

.emotion-class legend,
.emotion-class .MuiChip-root,
.emotion-class .MuiAutocomplete-endAdornment {
  display: none;
}

.emotion-class .MuiAutocomplete-inputRoot[class*="MuiOutlinedInput-root"] {
  padding: 5px;
}

.emotion-class .MuiAutocomplete-inputRoot[class*="MuiOutlinedInput-root"] .MuiAutocomplete-input {
  font-size: 14px;
  color: #303757;
  padding-left: 35px;
  font-family: "Open Sans",sans-serif;
}

.emotion-class .MuiAutocomplete-hasPopupIcon.MuiAutocomplete-hasClearIcon .MuiAutocomplete-inputRoot[class*="MuiOutlinedInput-root"] {
  padding-right: 0;
}

.emotion-class .MuiInputBase-root:hover .MuiOutlinedInput-notchedOutline,
.emotion-class .MuiInputBase-root:active .MuiOutlinedInput-notchedOutline,
.emotion-class .MuiInputBase-root:focus .MuiOutlinedInput-notchedOutline {
  border-color: #B4BCE0;
  outline: none;
}

.emotion-class .MuiInputBase-root:hover .MuiOutlinedInput-notchedOutline:focus,
.emotion-class .MuiInputBase-root:active .MuiOutlinedInput-notchedOutline:focus,
.emotion-class .MuiInputBase-root:focus .MuiOutlinedInput-notchedOutline:focus {
  border-color: #465AB6;
  outline: none;
}

.emotion-class .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #465AB6;
}

.emotion-class .MuiOutlinedInput-notchedOutline {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 34px;
  min-height: auto;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  top: 0;
}

.emotion-class .MuiOutlinedInput-notchedOutline:hover {
  border-color: #B4BCE0;
}

.emotion-class .MuiOutlinedInput-notchedOutline:focus {
  border-color: #465AB6;
}

.emotion-class .MuiOutlinedInput-notchedOutline::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class .MuiOutlinedInput-notchedOutline::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class .MuiOutlinedInput-notchedOutline:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class .MuiOutlinedInput-notchedOutline::placeholder {
  color: #A6ABBF;
}

.emotion-class .MuiOutlinedInput-root.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: #E4E5EB;
}

.emotion-class .MuiAutocomplete-root {
  background-color: #FFFFFF;
  position: relative;
}

.emotion-class.Mui-focused .MuiAutocomplete-clearIndicator {
  visibility: visible;
}

@media (pointer: fine) {
  .emotion-class:hover .MuiAutocomplete-clearIndicator {
    visibility: visible;
  }
}

.emotion-class .MuiAutocomplete-tag {
  margin: 3px;
  max-width: calc(100% - 6px);
}

.MuiAutocomplete-hasPopupIcon.emotion-class .MuiAutocomplete-inputRoot,
.MuiAutocomplete-hasClearIcon.emotion-class .MuiAutocomplete-inputRoot {
  padding-right: 30px;
}

.MuiAutocomplete-hasPopupIcon.MuiAutocomplete-hasClearIcon.emotion-class .MuiAutocomplete-inputRoot {
  padding-right: 56px;
}

.emotion-class .MuiAutocomplete-inputRoot .MuiAutocomplete-input {
  width: 0;
  min-width: 30px;
}

.emotion-class .MuiInput-root {
  padding-bottom: 1px;
}

.emotion-class .MuiInput-root .MuiInput-input {
  padding: 4px 4px 4px 0px;
}

.emotion-class .MuiInput-root.MuiInputBase-sizeSmall .MuiInput-input {
  padding: 2px 4px 3px 0;
}

.emotion-class .MuiOutlinedInput-root {
  padding: 9px;
}

.MuiAutocomplete-hasPopupIcon.emotion-class .MuiOutlinedInput-root,
.MuiAutocomplete-hasClearIcon.emotion-class .MuiOutlinedInput-root {
  padding-right: 39px;
}

.MuiAutocomplete-hasPopupIcon.MuiAutocomplete-hasClearIcon.emotion-class .MuiOutlinedInput-root {
  padding-right: 65px;
}

.emotion-class .MuiOutlinedInput-root .MuiAutocomplete-input {
  padding: 7.5px 4px 7.5px 5px;
}

.emotion-class .MuiOutlinedInput-root .MuiAutocomplete-endAdornment {
  right: 9px;
}

.emotion-class .MuiOutlinedInput-root.MuiInputBase-sizeSmall {
  padding-top: 6px;
  padding-bottom: 6px;
  padding-left: 6px;
}

.emotion-class .MuiOutlinedInput-root.MuiInputBase-sizeSmall .MuiAutocomplete-input {
  padding: 2.5px 4px 2.5px 8px;
}

.emotion-class .MuiFilledInput-root {
  padding-top: 19px;
  padding-left: 8px;
}

.MuiAutocomplete-hasPopupIcon.emotion-class .MuiFilledInput-root,
.MuiAutocomplete-hasClearIcon.emotion-class .MuiFilledInput-root {
  padding-right: 39px;
}

.MuiAutocomplete-hasPopupIcon.MuiAutocomplete-hasClearIcon.emotion-class .MuiFilledInput-root {
  padding-right: 65px;
}

.emotion-class .MuiFilledInput-root .MuiFilledInput-input {
  padding: 7px 4px;
}

.emotion-class .MuiFilledInput-root .MuiAutocomplete-endAdornment {
  right: 9px;
}

.emotion-class .MuiFilledInput-root.MuiInputBase-sizeSmall {
  padding-bottom: 1px;
}

.emotion-class .MuiFilledInput-root.MuiInputBase-sizeSmall .MuiFilledInput-input {
  padding: 2.5px 4px;
}

.emotion-class .MuiInputBase-hiddenLabel {
  padding-top: 8px;
}

.emotion-class .MuiFilledInput-root.MuiInputBase-hiddenLabel {
  padding-top: 0;
  padding-bottom: 0;
}

.emotion-class .MuiFilledInput-root.MuiInputBase-hiddenLabel .MuiAutocomplete-input {
  padding-top: 16px;
  padding-bottom: 17px;
}

.emotion-class .MuiFilledInput-root.MuiInputBase-hiddenLabel.MuiInputBase-sizeSmall .MuiAutocomplete-input {
  padding-top: 8px;
  padding-bottom: 9px;
}

.emotion-class .MuiAutocomplete-input {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  text-overflow: ellipsis;
  opacity: 0;
}

.emotion-class .MuiAutocomplete-input {
  opacity: 1;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
  vertical-align: top;
  width: 100%;
}

.emotion-class {
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.4375em;
  letter-spacing: 0.00938em;
  color: rgba(0, 0, 0, 0.87);
  box-sizing: border-box;
  position: relative;
  cursor: text;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  position: relative;
  border-radius: 4px;
  padding-right: 14px;
}

.emotion-class.Mui-disabled {
  color: rgba(0, 0, 0, 0.38);
  cursor: default;
}

.emotion-class:hover .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.87);
}

@media (hover: none) {
  .emotion-class:hover .MuiOutlinedInput-notchedOutline {
    border-color: rgba(0, 0, 0, 0.23);
  }
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-width: 2px;
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #1976d2;
}

.emotion-class.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: #d32f2f;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.26);
}

.emotion-class {
  font: inherit;
  letter-spacing: inherit;
  color: currentColor;
  padding: 4px 0 5px;
  border: 0;
  box-sizing: content-box;
  background: none;
  height: 1.4375em;
  margin: 0;
  -webkit-tap-highlight-color: transparent;
  display: block;
  min-width: 0;
  width: 100%;
  -webkit-animation-name: mui-auto-fill-cancel;
  animation-name: mui-auto-fill-cancel;
  -webkit-animation-duration: 10ms;
  animation-duration: 10ms;
  padding: 16.5px 14px;
  padding-right: 0;
}

.emotion-class::-webkit-input-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class::-moz-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class::-ms-input-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class:focus {
  outline: 0;
}

.emotion-class:invalid {
  box-shadow: none;
}

.emotion-class::-webkit-search-decoration {
  -webkit-appearance: none;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-webkit-input-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-moz-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-ms-input-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-webkit-input-placeholder {
  opacity: 0.42;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-moz-placeholder {
  opacity: 0.42;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-ms-input-placeholder {
  opacity: 0.42;
}

.emotion-class.Mui-disabled {
  opacity: 1;
  -webkit-text-fill-color: rgba(0, 0, 0, 0.38);
}

.emotion-class:-webkit-autofill {
  -webkit-animation-duration: 5000s;
  animation-duration: 5000s;
  -webkit-animation-name: mui-auto-fill;
  animation-name: mui-auto-fill;
}

.emotion-class:-webkit-autofill {
  border-radius: inherit;
}

.emotion-class {
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(0, -50%);
  -moz-transform: translate(0, -50%);
  -ms-transform: translate(0, -50%);
  transform: translate(0, -50%);
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  padding: 2px;
  margin-right: -2px;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: 1.5rem;
}

.emotion-class {
  text-align: left;
  position: absolute;
  bottom: 0;
  right: 0;
  top: -5px;
  left: 0;
  margin: 0;
  padding: 0 8px;
  pointer-events: none;
  border-radius: inherit;
  border-style: solid;
  border-width: 1px;
  overflow: hidden;
  min-width: 0%;
  border-color: rgba(0, 0, 0, 0.23);
}

.emotion-class {
  float: unset;
  width: auto;
  overflow: hidden;
  padding: 0;
  line-height: 11px;
  -webkit-transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
  transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
}

.emotion-class {
  z-index: 1300;
  position: absolute;
}

.emotion-class .MuiAutocomplete-paper {
  margin-top: 0;
}

.emotion-class .MuiPaper-rounded {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.emotion-class .MuiAutocomplete-listbox {
  padding-top: 5px;
}

.emotion-class .MuiAutocomplete-option {
  padding: 8px 20px;
}

.emotion-class .MuiAutocomplete-option[data-focus="true"] {
  background-color: #F5F6FA;
}

.emotion-class .MuiPaper-elevation1 {
  box-shadow: 0px 2px 6px rgba(48, 59, 87, 0.38);
}

.emotion-class .MuiAutocomplete-noOptions {
  color: #A6ABBF;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  position: relative;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0.00938em;
  overflow: auto;
}

.emotion-class .footer {
  padding: var(--spacing-s) var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  text-align: center;
}

.emotion-class {
  list-style: none;
  margin: 0;
  padding: 8px 0;
  max-height: 40vh;
  overflow: auto;
  position: relative;
}

.emotion-class .MuiAutocomplete-option {
  min-height: 48px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  padding-top: 6px;
  box-sizing: border-box;
  outline: 0;
  -webkit-tap-highlight-color: transparent;
  padding-bottom: 6px;
  padding-left: 16px;
  padding-right: 16px;
}

@media (min-width:600px) {
  .emotion-class .MuiAutocomplete-option {
    min-height: auto;
  }
}

.emotion-class .MuiAutocomplete-option.Mui-focused {
  background-color: rgba(0, 0, 0, 0.04);
}

@media (hover: none) {
  .emotion-class .MuiAutocomplete-option.Mui-focused {
    background-color: transparent;
  }
}

.emotion-class .MuiAutocomplete-option[aria-disabled="true"] {
  opacity: 0.38;
  pointer-events: none;
}

.emotion-class .MuiAutocomplete-option.Mui-focusVisible {
  background-color: rgba(0, 0, 0, 0.12);
}

.emotion-class .MuiAutocomplete-option[aria-selected="true"] {
  background-color: rgba(25, 118, 210, 0.08);
}

.emotion-class .MuiAutocomplete-option[aria-selected="true"].Mui-focused {
  background-color: rgba(25, 118, 210, 0.12);
}

@media (hover: none) {
  .emotion-class .MuiAutocomplete-option[aria-selected="true"].Mui-focused {
    background-color: rgba(0, 0, 0, 0.08);
  }
}

.emotion-class .MuiAutocomplete-option[aria-selected="true"].Mui-focusVisible {
  background-color: rgba(25, 118, 210, 0.2);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  row-gap: 12px;
  -webkit-column-gap: 12px;
  column-gap: 12px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-right: 10px;
  margin-left: -8px;
  width: 100%;
}

.emotion-class {
  position: relative;
  max-height: -webkit-fit-content;
  max-height: -moz-fit-content;
  max-height: fit-content;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  display: inline-block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  box-sizing: border-box;
  border-radius: 50%;
  text-align: center;
  color: #A6ABBF;
  font-size: 12px;
  font-weight: 700;
  background-color: #E4E5EB;
  background-image: none;
  -webkit-background-size: cover;
  background-size: cover;
  -webkit-background-position: center;
  background-position: center;
  box-shadow: inset 0px 0px 11px rgba(0, 0, 0, 0.1);
}

.emotion-class {
  position: absolute;
  height: 14.5px;
  bottom: -3px;
  right: -1px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #A6ABBF;
  font-size: 12px;
  font-style: italic;
  font-weight: 400;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-left: -5px;
}

.emotion-class {
  fill: #FFA64C;
  width: 29px;
  height: 29px;
  fill-rule: evenodd;
  vertical-align: middle;
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 10px;
  line-height: 20px;
  color: #A6ABBF;
  text-transform: uppercase;
  margin-left: 5px;
}

.emotion-class {
  fill: #06CC87;
  width: 29px;
  height: 29px;
  fill-rule: evenodd;
  vertical-align: middle;
}

.emotion-class {
  fill: #FF7F8A;
  width: 29px;
  height: 29px;
  fill-rule: evenodd;
  vertical-align: middle;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  display: inline-block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  box-sizing: border-box;
  border-radius: 50%;
  text-align: center;
  color: #A6ABBF;
  font-size: 12px;
  font-weight: 700;
  background-color: #E4E5EB;
  background-image: url('//s3-eu-west-1.amazonaws.com/shortlist-eu-west-1-logos/domwt/vendor-logo/creed-bratton/tk829ixfQmOddMe6moXK_6391622e764b2.jpeg');
  -webkit-background-size: cover;
  background-size: cover;
  -webkit-background-position: center;
  background-position: center;
  box-shadow: inset 0px 0px 11px rgba(0, 0, 0, 0.1);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  display: inline-block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  box-sizing: border-box;
  border-radius: 50%;
  text-align: center;
  color: #A6ABBF;
  font-size: 12px;
  font-weight: 700;
  background-color: #E4E5EB;
  background-image: url('//s3-eu-west-1.amazonaws.com/shortlist-eu-west-1-logos/domwt/vendor-logo/rosalind-arusha-arkadina-altalune-florence-thurman/Y2jxMGtTQnOk2RwCW4rE_Office-1200-baumgartner1.jpg');
  -webkit-background-size: cover;
  background-size: cover;
  -webkit-background-position: center;
  background-position: center;
  box-shadow: inset 0px 0px 11px rgba(0, 0, 0, 0.1);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  display: inline-block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  box-sizing: border-box;
  border-radius: 50%;
  text-align: center;
  color: #A6ABBF;
  font-size: 12px;
  font-weight: 700;
  background-color: #E4E5EB;
  background-image: url('//s3-eu-west-1.amazonaws.com/shortlist-eu-west-1-logos/domwt/vendor-logo/sammy-smith/FE9HmzpKSTC9TLg2Ta9Z_intro-1526005235.jpg');
  -webkit-background-size: cover;
  background-size: cover;
  -webkit-background-position: center;
  background-position: center;
  box-shadow: inset 0px 0px 11px rgba(0, 0, 0, 0.1);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  display: inline-block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  box-sizing: border-box;
  border-radius: 50%;
  text-align: center;
  color: #A6ABBF;
  font-size: 12px;
  font-weight: 700;
  background-color: #E4E5EB;
  background-image: url('//s3-eu-west-1.amazonaws.com/shortlist-eu-west-1-logos/domwt/vendor-logo/pam-beesly/I2PLGtdrQdCV3usElAxV_original.jpeg');
  -webkit-background-size: cover;
  background-size: cover;
  -webkit-background-position: center;
  background-position: center;
  box-shadow: inset 0px 0px 11px rgba(0, 0, 0, 0.1);
}

<div>
  <div
    style="position: sticky; top: 0px; padding: 10px 2rem; width: 100%; z-index: 10000; background-color: white; margin: -32px 0px 10px -2rem; box-shadow: rgb(0 0 0 / 10%) 0px -1px 0px 0px inset; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #E4E5EB;"
  >
    <div
      style="display: flex; justify-content: space-between;"
    >
      <div>
        <span
          class="emotion-class"
        >
          Tenant features
        </span>
        <div
          style="margin-top: 5px;"
        >
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    checked=""
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                    classname="checkboxOnIcon css-1c1jprp"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    availability
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    checked=""
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                    classname="checkboxOnIcon css-1c1jprp"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    availability_calendar
                  </span>
                </span>
              </label>
            </div>
          </span>
        </div>
      </div>
      <div
        style="white-space: nowrap;"
      >
        <span
          class="emotion-class"
        >
          Logged user
        </span>
        <div
          style="margin-top: 5px; width: 130px;"
        >
          <div
            class="emotion-class"
          >
            <select
              class="emotion-class"
            >
              <option
                data-testid="SelectOption_Staff"
                value="staff"
              >
                Staff
              </option>
              <option
                data-testid="SelectOption_BuyerAdmin"
                value="buyer-admin"
              >
                Buyer Admin
              </option>
              <option
                data-testid="SelectOption_BuyerRegular"
                value="buyer-regular"
              >
                Buyer Regular
              </option>
              <option
                data-testid="SelectOption_Vendor"
                value="vendor"
              >
                Vendor
              </option>
            </select>
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
              class="emotion-class"
              size="28"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="emotion-class"
  >
    <div
      class="emotion-class"
    >
      <div
        class="MuiAutocomplete-root Mui-expanded MuiAutocomplete-hasPopupIcon emotion-class"
      >
        <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m22.5841%2035.5c15.4044%2035.5%209.58411%2029.6797%209.58411%2022.5c9.58411%2015.3203%2015.4044%209.5%2022.5841%209.5c29.7638%209.5%2035.5841%2015.3203%2035.5841%2022.5c35.5841%2025.5494%2034.5342%2028.3535%2032.7761%2030.5707l38.416%2036.2106l36.2947%2038.3319l30.6548%2032.692c28.4376%2034.4501%2025.6335%2035.5%2022.5841%2035.5zm32.5841%2022.5c32.5841%2028.0228%2028.107%2032.5%2022.5841%2032.5c17.0613%2032.5%2012.5841%2028.0228%2012.5841%2022.5c12.5841%2016.9772%2017.0613%2012.5%2022.5841%2012.5c28.107%2012.5%2032.5841%2016.9772%2032.5841%2022.5z'/%3e%3c/svg%3e
          classname="css-1cmc4y2"
          fill="#A6ABBF"
          size="24"
        />
        <div
          class="MuiFormControl-root MuiFormControl-fullWidth MuiTextField-root emotion-class"
        >
          <div
            class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-fullWidth MuiInputBase-formControl MuiInputBase-adornedEnd MuiAutocomplete-inputRoot emotion-class"
          >
            <input
              aria-activedescendant=":r2:-option-0"
              aria-autocomplete="both"
              aria-controls=":r2:-listbox"
              aria-expanded="true"
              aria-invalid="false"
              autocapitalize="none"
              autocomplete="off"
              class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd MuiAutocomplete-input MuiAutocomplete-inputFocused emotion-class"
              id=":r2:"
              placeholder="Search for a vendor"
              role="combobox"
              spellcheck="false"
              type="text"
              value=""
            />
            <div
              class="MuiAutocomplete-endAdornment emotion-class"
            >
              <button
                aria-label="Close"
                class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium MuiAutocomplete-popupIndicator MuiAutocomplete-popupIndicatorOpen emotion-class"
                tabindex="-1"
                title="Close"
                type="button"
              >
                <svg
                  aria-hidden="true"
                  class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium emotion-class"
                  data-testid="ArrowDropDownIcon"
                  focusable="false"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M7 10l5 5 5-5z"
                  />
                </svg>
              </button>
            </div>
            <fieldset
              aria-hidden="true"
              class="MuiOutlinedInput-notchedOutline emotion-class"
            >
              <legend
                class="emotion-class"
              >
                <span
                  aria-hidden="true"
                  class="notranslate"
                >
                  ​
                </span>
              </legend>
            </fieldset>
          </div>
        </div>
      </div>
      <div
        class="MuiPopper-root MuiAutocomplete-popper MuiAutocomplete-popperDisablePortal emotion-class"
        data-popper-escaped=""
        data-popper-placement="bottom"
        data-popper-reference-hidden=""
        role="presentation"
        style="position: absolute; top: 0px; left: 0px; width: 0px; margin: 0px; right: auto; bottom: auto; transform: translate(0px, 0px);"
      >
        <div
          class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation1 MuiAutocomplete-paper emotion-class"
          style="--Paper-shadow: 0px 2px 1px -1px rgba(0,0,0,0.2),0px 1px 1px 0px rgba(0,0,0,0.14),0px 1px 3px 0px rgba(0,0,0,0.12);"
        >
          <ul
            aria-labelledby=":r2:-label"
            class="MuiAutocomplete-listbox emotion-class"
            id=":r2:-listbox"
            role="listbox"
          >
            <div>
              <span
                aria-disabled="false"
                aria-selected="false"
                class="MuiAutocomplete-option emotion-class Mui-focused"
                data-option-index="0"
                id=":r2:-option-0"
                role="option"
                style="width: 100%; color: #465AB6;"
                tabindex="-1"
              >
                <div
                  class="emotion-class"
                >
                  <div
                    style="display: flex; justify-content: center;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <p
                        class="emotion-class"
                      >
                        SS
                      </p>
                      <div
                        class="emotion-class"
                        size="14.5"
                      >
                        <span
                          aria-label=""
                          class=""
                          data-mui-internal-clone-element="true"
                          style="display: flex; align-items: center; justify-content: center; width: 14.5px; height: 14.5px; border-radius: 20px; background: #06CC87; padding: 0px; border: 2px solid white; box-sizing: border-box;"
                        >
                          <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                            classname="css-j73ydx"
                            fill="white"
                            size="14.5"
                          />
                        </span>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-class"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span>
                        Staffing Supplier
                      </span>
                      <span
                        class="emotion-class"
                      />
                    </div>
                    <div
                      class="emotion-class"
                    >
                      <div
                        class="emotion-class"
                      >
                        <data:image/svg+xml,%3csvg%20width='16'%20height='20'%20viewbox='0%200%2016%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='8'%20cy='10'%20r='5'%20/%3e%3c/svg%3e
                          class="emotion-class"
                          fill="#FFA64C"
                          style="width: 20px; height: 20px;"
                        />
                        <p
                          class="emotion-class"
                        >
                          Available Soon 10 months ago
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </span>
            </div>
            <div>
              <span
                aria-disabled="false"
                aria-selected="false"
                class="MuiAutocomplete-option emotion-class"
                data-option-index="1"
                id=":r2:-option-1"
                role="option"
                style="width: 100%; color: #465AB6;"
                tabindex="-1"
              >
                <div
                  class="emotion-class"
                >
                  <div
                    style="display: flex; justify-content: center;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <p
                        class="emotion-class"
                      >
                        W1
                      </p>
                      <div
                        class="emotion-class"
                        size="14.5"
                      >
                        <span
                          aria-label=""
                          class=""
                          data-mui-internal-clone-element="true"
                          style="display: flex; align-items: center; justify-content: center; width: 14.5px; height: 14.5px; border-radius: 20px; background: #06CC87; padding: 0px; border: 2px solid white; box-sizing: border-box;"
                        >
                          <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                            classname="css-j73ydx"
                            fill="white"
                            size="14.5"
                          />
                        </span>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-class"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span>
                        Worker 1
                      </span>
                      <span
                        class="emotion-class"
                      >
                        Staffing Supplier
                      </span>
                    </div>
                    <div
                      class="emotion-class"
                    >
                      <div
                        class="emotion-class"
                      >
                        <data:image/svg+xml,%3csvg%20width='16'%20height='20'%20viewbox='0%200%2016%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='8'%20cy='10'%20r='5'%20/%3e%3c/svg%3e
                          class="emotion-class"
                          fill="#06CC87"
                          style="width: 20px; height: 20px;"
                        />
                        <p
                          class="emotion-class"
                        >
                          Available
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </span>
            </div>
            <div>
              <span
                aria-disabled="false"
                aria-selected="false"
                class="MuiAutocomplete-option emotion-class"
                data-option-index="2"
                id=":r2:-option-2"
                role="option"
                style="width: 100%; color: #465AB6;"
                tabindex="-1"
              >
                <div
                  class="emotion-class"
                >
                  <div
                    style="display: flex; justify-content: center;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <p
                        class="emotion-class"
                      >
                        W2
                      </p>
                      <div
                        class="emotion-class"
                        size="14.5"
                      >
                        <span
                          aria-label=""
                          class=""
                          data-mui-internal-clone-element="true"
                          style="display: flex; align-items: center; justify-content: center; width: 14.5px; height: 14.5px; border-radius: 20px; background: #06CC87; padding: 0px; border: 2px solid white; box-sizing: border-box;"
                        >
                          <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                            classname="css-j73ydx"
                            fill="white"
                            size="14.5"
                          />
                        </span>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-class"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span>
                        W 2
                      </span>
                      <span
                        class="emotion-class"
                      >
                        Staffing Supplier
                      </span>
                    </div>
                    <div
                      class="emotion-class"
                    >
                      <div
                        class="emotion-class"
                      >
                        <data:image/svg+xml,%3csvg%20width='16'%20height='20'%20viewbox='0%200%2016%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='8'%20cy='10'%20r='5'%20/%3e%3c/svg%3e
                          class="emotion-class"
                          fill="#FF7F8A"
                          style="width: 20px; height: 20px;"
                        />
                        <p
                          class="emotion-class"
                        >
                          Unavailable
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </span>
            </div>
            <div>
              <span
                aria-disabled="false"
                aria-selected="false"
                class="MuiAutocomplete-option emotion-class"
                data-option-index="3"
                id=":r2:-option-3"
                role="option"
                style="width: 100%; color: #465AB6;"
                tabindex="-1"
              >
                <div
                  class="emotion-class"
                >
                  <div
                    style="display: flex; justify-content: center;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <p
                        class="emotion-class"
                      >
                        B
                      </p>
                      <div
                        class="emotion-class"
                        size="14.5"
                      >
                        <span
                          aria-label=""
                          class=""
                          data-mui-internal-clone-element="true"
                          style="display: flex; align-items: center; justify-content: center; width: 14.5px; height: 14.5px; border-radius: 20px; background: #06CC87; padding: 0px; border: 2px solid white; box-sizing: border-box;"
                        >
                          <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                            classname="css-j73ydx"
                            fill="white"
                            size="14.5"
                          />
                        </span>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-class"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span>
                        BlackCo
                      </span>
                      <span
                        class="emotion-class"
                      />
                    </div>
                    <div
                      class="emotion-class"
                    >
                      <div
                        class="emotion-class"
                      >
                        <data:image/svg+xml,%3csvg%20width='16'%20height='20'%20viewbox='0%200%2016%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='8'%20cy='10'%20r='5'%20/%3e%3c/svg%3e
                          class="emotion-class"
                          fill="#06CC87"
                          style="width: 20px; height: 20px;"
                        />
                        <p
                          class="emotion-class"
                        >
                          Available
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </span>
            </div>
            <div>
              <span
                aria-disabled="false"
                aria-selected="false"
                class="MuiAutocomplete-option emotion-class"
                data-option-index="4"
                id=":r2:-option-4"
                role="option"
                style="width: 100%; color: #465AB6;"
                tabindex="-1"
              >
                <div
                  class="emotion-class"
                >
                  <div
                    style="display: flex; justify-content: center;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <p
                        class="emotion-class"
                      >
                        SN
                      </p>
                      <div
                        class="emotion-class"
                        size="14.5"
                      >
                        <span
                          aria-label=""
                          class=""
                          data-mui-internal-clone-element="true"
                          style="display: flex; align-items: center; justify-content: center; width: 14.5px; height: 14.5px; border-radius: 20px; background: #06CC87; padding: 0px; border: 2px solid white; box-sizing: border-box;"
                        >
                          <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                            classname="css-j73ydx"
                            fill="white"
                            size="14.5"
                          />
                        </span>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-class"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span>
                        Sarah Newman
                      </span>
                      <span
                        class="emotion-class"
                      />
                    </div>
                    <div
                      class="emotion-class"
                    >
                      <div
                        class="emotion-class"
                      >
                        <data:image/svg+xml,%3csvg%20width='16'%20height='20'%20viewbox='0%200%2016%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='8'%20cy='10'%20r='5'%20/%3e%3c/svg%3e
                          class="emotion-class"
                          fill="#06CC87"
                          style="width: 20px; height: 20px;"
                        />
                        <p
                          class="emotion-class"
                        >
                          Available
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </span>
            </div>
            <div>
              <span
                aria-disabled="false"
                aria-selected="false"
                class="MuiAutocomplete-option emotion-class"
                data-option-index="5"
                id=":r2:-option-5"
                role="option"
                style="width: 100%; color: #465AB6;"
                tabindex="-1"
              >
                <div
                  class="emotion-class"
                >
                  <div
                    style="display: flex; justify-content: center;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <p
                        class="emotion-class"
                      >
                         
                      </p>
                      <div
                        class="emotion-class"
                        size="14.5"
                      >
                        <span
                          aria-label=""
                          class=""
                          data-mui-internal-clone-element="true"
                          style="display: flex; align-items: center; justify-content: center; width: 14.5px; height: 14.5px; border-radius: 20px; background: #06CC87; padding: 0px; border: 2px solid white; box-sizing: border-box;"
                        >
                          <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                            classname="css-j73ydx"
                            fill="white"
                            size="14.5"
                          />
                        </span>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-class"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span>
                        Creed Bratton
                      </span>
                      <span
                        class="emotion-class"
                      />
                    </div>
                    <div
                      class="emotion-class"
                    >
                      <div
                        class="emotion-class"
                      >
                        <data:image/svg+xml,%3csvg%20width='16'%20height='20'%20viewbox='0%200%2016%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='8'%20cy='10'%20r='5'%20/%3e%3c/svg%3e
                          class="emotion-class"
                          fill="#FFA64C"
                          style="width: 20px; height: 20px;"
                        />
                        <p
                          class="emotion-class"
                        >
                          Available Soon
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </span>
            </div>
            <div>
              <span
                aria-disabled="false"
                aria-selected="false"
                class="MuiAutocomplete-option emotion-class"
                data-option-index="6"
                id=":r2:-option-6"
                role="option"
                style="width: 100%; color: #465AB6;"
                tabindex="-1"
              >
                <div
                  class="emotion-class"
                >
                  <div
                    style="display: flex; justify-content: center;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <p
                        class="emotion-class"
                      >
                         
                      </p>
                      <div
                        class="emotion-class"
                        size="14.5"
                      >
                        <span
                          aria-label="Unsigned document"
                          class=""
                          data-mui-internal-clone-element="true"
                          style="display: flex; align-items: center; justify-content: center; width: 14.5px; height: 14.5px; border-radius: 20px; background: #FF7F8A; padding: 0px; border: 2px solid white; box-sizing: border-box;"
                        >
                          <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m27%2011v15.8696l25.8%2027h22.2l21%2015.8696v11h27z'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m20.4998%2033.5c20.4998%2032.5189%2020.8378%2031.6903%2021.514%2031.0142c22.1901%2030.3381%2023.0187%2030%2023.9998%2030c24.9808%2030%2025.8094%2030.3381%2026.4856%2031.0142c27.1617%2031.6903%2027.4998%2032.5189%2027.4998%2033.5c27.4998%2034.4811%2027.1617%2035.3096%2026.4856%2035.9858c25.8094%2036.6619%2024.9808%2037%2023.9998%2037c23.0187%2037%2022.1901%2036.6619%2021.514%2035.9858c20.8378%2035.3096%2020.4998%2034.4811%2020.4998%2033.5v33.5z'%20/%3e%3c/svg%3e
                            classname="css-j73ydx"
                            fill="white"
                            size="14.5"
                          />
                        </span>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-class"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span>
                        Kevin Malone
                      </span>
                      <span
                        class="emotion-class"
                      />
                    </div>
                    <div
                      class="emotion-class"
                    >
                      <div
                        class="emotion-class"
                      >
                        <data:image/svg+xml,%3csvg%20width='16'%20height='20'%20viewbox='0%200%2016%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='8'%20cy='10'%20r='5'%20/%3e%3c/svg%3e
                          class="emotion-class"
                          fill="#06CC87"
                          style="width: 20px; height: 20px;"
                        />
                        <p
                          class="emotion-class"
                        >
                          Available
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </span>
            </div>
            <div>
              <span
                aria-disabled="false"
                aria-selected="false"
                class="MuiAutocomplete-option emotion-class"
                data-option-index="7"
                id=":r2:-option-7"
                role="option"
                style="width: 100%; color: #465AB6;"
                tabindex="-1"
              >
                <div
                  class="emotion-class"
                >
                  <div
                    style="display: flex; justify-content: center;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <p
                        class="emotion-class"
                      >
                         
                      </p>
                      <div
                        class="emotion-class"
                        size="14.5"
                      >
                        <span
                          aria-label="Unsigned document"
                          class=""
                          data-mui-internal-clone-element="true"
                          style="display: flex; align-items: center; justify-content: center; width: 14.5px; height: 14.5px; border-radius: 20px; background: #FF7F8A; padding: 0px; border: 2px solid white; box-sizing: border-box;"
                        >
                          <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m27%2011v15.8696l25.8%2027h22.2l21%2015.8696v11h27z'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m20.4998%2033.5c20.4998%2032.5189%2020.8378%2031.6903%2021.514%2031.0142c22.1901%2030.3381%2023.0187%2030%2023.9998%2030c24.9808%2030%2025.8094%2030.3381%2026.4856%2031.0142c27.1617%2031.6903%2027.4998%2032.5189%2027.4998%2033.5c27.4998%2034.4811%2027.1617%2035.3096%2026.4856%2035.9858c25.8094%2036.6619%2024.9808%2037%2023.9998%2037c23.0187%2037%2022.1901%2036.6619%2021.514%2035.9858c20.8378%2035.3096%2020.4998%2034.4811%2020.4998%2033.5v33.5z'%20/%3e%3c/svg%3e
                            classname="css-j73ydx"
                            fill="white"
                            size="14.5"
                          />
                        </span>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-class"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span>
                        Meredith Palmer
                      </span>
                      <span
                        class="emotion-class"
                      />
                    </div>
                    <div
                      class="emotion-class"
                    >
                      <div
                        class="emotion-class"
                      >
                        <data:image/svg+xml,%3csvg%20width='16'%20height='20'%20viewbox='0%200%2016%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='8'%20cy='10'%20r='5'%20/%3e%3c/svg%3e
                          class="emotion-class"
                          fill="#06CC87"
                          style="width: 20px; height: 20px;"
                        />
                        <p
                          class="emotion-class"
                        >
                          Available
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </span>
            </div>
            <div>
              <span
                aria-disabled="false"
                aria-selected="false"
                class="MuiAutocomplete-option emotion-class"
                data-option-index="8"
                id=":r2:-option-8"
                role="option"
                style="width: 100%; color: #465AB6;"
                tabindex="-1"
              >
                <div
                  class="emotion-class"
                >
                  <div
                    style="display: flex; justify-content: center;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <p
                        class="emotion-class"
                      >
                         
                      </p>
                      <div
                        class="emotion-class"
                        size="14.5"
                      >
                        <span
                          aria-label="Unsigned document"
                          class=""
                          data-mui-internal-clone-element="true"
                          style="display: flex; align-items: center; justify-content: center; width: 14.5px; height: 14.5px; border-radius: 20px; background: #FF7F8A; padding: 0px; border: 2px solid white; box-sizing: border-box;"
                        >
                          <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m27%2011v15.8696l25.8%2027h22.2l21%2015.8696v11h27z'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m20.4998%2033.5c20.4998%2032.5189%2020.8378%2031.6903%2021.514%2031.0142c22.1901%2030.3381%2023.0187%2030%2023.9998%2030c24.9808%2030%2025.8094%2030.3381%2026.4856%2031.0142c27.1617%2031.6903%2027.4998%2032.5189%2027.4998%2033.5c27.4998%2034.4811%2027.1617%2035.3096%2026.4856%2035.9858c25.8094%2036.6619%2024.9808%2037%2023.9998%2037c23.0187%2037%2022.1901%2036.6619%2021.514%2035.9858c20.8378%2035.3096%2020.4998%2034.4811%2020.4998%2033.5v33.5z'%20/%3e%3c/svg%3e
                            classname="css-j73ydx"
                            fill="white"
                            size="14.5"
                          />
                        </span>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-class"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span>
                        Pam Beesly
                      </span>
                      <span
                        class="emotion-class"
                      />
                    </div>
                    <div
                      class="emotion-class"
                    >
                      <div
                        class="emotion-class"
                      >
                        <data:image/svg+xml,%3csvg%20width='16'%20height='20'%20viewbox='0%200%2016%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3ccircle%20cx='8'%20cy='10'%20r='5'%20/%3e%3c/svg%3e
                          class="emotion-class"
                          fill="#06CC87"
                          style="width: 20px; height: 20px;"
                        />
                        <p
                          class="emotion-class"
                        >
                          Available
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </span>
            </div>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`InputPartners -  > DefaultValues snapshot should match 1`] = `
.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class:hover .checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class {
  margin: 1px 0 -1px 6px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  color: #303757;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  fill: #000000;
  width: 28px;
  height: 28px;
  fill-rule: evenodd;
  vertical-align: middle;
  position: absolute;
  top: 10px;
  right: 5px;
  fill: #465AB6;
  pointer-events: none;
}

.emotion-class .MuiAutocomplete-popper {
  display: none;
}

.emotion-class .MuiAutocomplete-inputRoot[class*="MuiOutlinedInput-root"] .MuiAutocomplete-input {
  padding-left: 72px!important;
}

.emotion-class {
  position: relative;
}

.emotion-class legend,
.emotion-class .MuiChip-root,
.emotion-class .MuiAutocomplete-endAdornment {
  display: none;
}

.emotion-class .MuiAutocomplete-inputRoot[class*="MuiOutlinedInput-root"] {
  padding: 5px;
}

.emotion-class .MuiAutocomplete-inputRoot[class*="MuiOutlinedInput-root"] .MuiAutocomplete-input {
  font-size: 14px;
  color: #303757;
  padding-left: 35px;
  font-family: "Open Sans",sans-serif;
}

.emotion-class .MuiAutocomplete-hasPopupIcon.MuiAutocomplete-hasClearIcon .MuiAutocomplete-inputRoot[class*="MuiOutlinedInput-root"] {
  padding-right: 0;
}

.emotion-class .MuiInputBase-root:hover .MuiOutlinedInput-notchedOutline,
.emotion-class .MuiInputBase-root:active .MuiOutlinedInput-notchedOutline,
.emotion-class .MuiInputBase-root:focus .MuiOutlinedInput-notchedOutline {
  border-color: #B4BCE0;
  outline: none;
}

.emotion-class .MuiInputBase-root:hover .MuiOutlinedInput-notchedOutline:focus,
.emotion-class .MuiInputBase-root:active .MuiOutlinedInput-notchedOutline:focus,
.emotion-class .MuiInputBase-root:focus .MuiOutlinedInput-notchedOutline:focus {
  border-color: #465AB6;
  outline: none;
}

.emotion-class .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #465AB6;
}

.emotion-class .MuiOutlinedInput-notchedOutline {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 34px;
  min-height: auto;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  top: 0;
}

.emotion-class .MuiOutlinedInput-notchedOutline:hover {
  border-color: #B4BCE0;
}

.emotion-class .MuiOutlinedInput-notchedOutline:focus {
  border-color: #465AB6;
}

.emotion-class .MuiOutlinedInput-notchedOutline::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class .MuiOutlinedInput-notchedOutline::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class .MuiOutlinedInput-notchedOutline:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class .MuiOutlinedInput-notchedOutline::placeholder {
  color: #A6ABBF;
}

.emotion-class .MuiOutlinedInput-root.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: #E4E5EB;
}

.emotion-class .MuiAutocomplete-root {
  background-color: #FFFFFF;
  position: relative;
}

.emotion-class.Mui-focused .MuiAutocomplete-clearIndicator {
  visibility: visible;
}

@media (pointer: fine) {
  .emotion-class:hover .MuiAutocomplete-clearIndicator {
    visibility: visible;
  }
}

.emotion-class .MuiAutocomplete-tag {
  margin: 3px;
  max-width: calc(100% - 6px);
}

.MuiAutocomplete-hasPopupIcon.emotion-class .MuiAutocomplete-inputRoot,
.MuiAutocomplete-hasClearIcon.emotion-class .MuiAutocomplete-inputRoot {
  padding-right: 30px;
}

.MuiAutocomplete-hasPopupIcon.MuiAutocomplete-hasClearIcon.emotion-class .MuiAutocomplete-inputRoot {
  padding-right: 56px;
}

.emotion-class .MuiAutocomplete-inputRoot .MuiAutocomplete-input {
  width: 0;
  min-width: 30px;
}

.emotion-class .MuiInput-root {
  padding-bottom: 1px;
}

.emotion-class .MuiInput-root .MuiInput-input {
  padding: 4px 4px 4px 0px;
}

.emotion-class .MuiInput-root.MuiInputBase-sizeSmall .MuiInput-input {
  padding: 2px 4px 3px 0;
}

.emotion-class .MuiOutlinedInput-root {
  padding: 9px;
}

.MuiAutocomplete-hasPopupIcon.emotion-class .MuiOutlinedInput-root,
.MuiAutocomplete-hasClearIcon.emotion-class .MuiOutlinedInput-root {
  padding-right: 39px;
}

.MuiAutocomplete-hasPopupIcon.MuiAutocomplete-hasClearIcon.emotion-class .MuiOutlinedInput-root {
  padding-right: 65px;
}

.emotion-class .MuiOutlinedInput-root .MuiAutocomplete-input {
  padding: 7.5px 4px 7.5px 5px;
}

.emotion-class .MuiOutlinedInput-root .MuiAutocomplete-endAdornment {
  right: 9px;
}

.emotion-class .MuiOutlinedInput-root.MuiInputBase-sizeSmall {
  padding-top: 6px;
  padding-bottom: 6px;
  padding-left: 6px;
}

.emotion-class .MuiOutlinedInput-root.MuiInputBase-sizeSmall .MuiAutocomplete-input {
  padding: 2.5px 4px 2.5px 8px;
}

.emotion-class .MuiFilledInput-root {
  padding-top: 19px;
  padding-left: 8px;
}

.MuiAutocomplete-hasPopupIcon.emotion-class .MuiFilledInput-root,
.MuiAutocomplete-hasClearIcon.emotion-class .MuiFilledInput-root {
  padding-right: 39px;
}

.MuiAutocomplete-hasPopupIcon.MuiAutocomplete-hasClearIcon.emotion-class .MuiFilledInput-root {
  padding-right: 65px;
}

.emotion-class .MuiFilledInput-root .MuiFilledInput-input {
  padding: 7px 4px;
}

.emotion-class .MuiFilledInput-root .MuiAutocomplete-endAdornment {
  right: 9px;
}

.emotion-class .MuiFilledInput-root.MuiInputBase-sizeSmall {
  padding-bottom: 1px;
}

.emotion-class .MuiFilledInput-root.MuiInputBase-sizeSmall .MuiFilledInput-input {
  padding: 2.5px 4px;
}

.emotion-class .MuiInputBase-hiddenLabel {
  padding-top: 8px;
}

.emotion-class .MuiFilledInput-root.MuiInputBase-hiddenLabel {
  padding-top: 0;
  padding-bottom: 0;
}

.emotion-class .MuiFilledInput-root.MuiInputBase-hiddenLabel .MuiAutocomplete-input {
  padding-top: 16px;
  padding-bottom: 17px;
}

.emotion-class .MuiFilledInput-root.MuiInputBase-hiddenLabel.MuiInputBase-sizeSmall .MuiAutocomplete-input {
  padding-top: 8px;
  padding-bottom: 9px;
}

.emotion-class .MuiAutocomplete-input {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  text-overflow: ellipsis;
  opacity: 0;
}

.emotion-class .MuiAutocomplete-input {
  opacity: 1;
}

.emotion-class {
  width: 30px;
  height: 30px;
  position: absolute;
  top: 7px;
  left: 40px;
}

.emotion-class {
  position: relative;
  max-height: -webkit-fit-content;
  max-height: -moz-fit-content;
  max-height: fit-content;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  display: inline-block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  box-sizing: border-box;
  border-radius: 50%;
  text-align: center;
  color: #A6ABBF;
  font-size: 12px;
  font-weight: 700;
  background-color: #E4E5EB;
  background-image: none;
  -webkit-background-size: cover;
  background-size: cover;
  -webkit-background-position: center;
  background-position: center;
  box-shadow: inset 0px 0px 11px rgba(0, 0, 0, 0.1);
}

.emotion-class {
  position: absolute;
  height: 14.5px;
  bottom: -3px;
  right: -1px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
  vertical-align: top;
  width: 100%;
}

.emotion-class {
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.4375em;
  letter-spacing: 0.00938em;
  color: rgba(0, 0, 0, 0.87);
  box-sizing: border-box;
  position: relative;
  cursor: text;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  position: relative;
  border-radius: 4px;
  padding-right: 14px;
}

.emotion-class.Mui-disabled {
  color: rgba(0, 0, 0, 0.38);
  cursor: default;
}

.emotion-class:hover .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.87);
}

@media (hover: none) {
  .emotion-class:hover .MuiOutlinedInput-notchedOutline {
    border-color: rgba(0, 0, 0, 0.23);
  }
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-width: 2px;
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #1976d2;
}

.emotion-class.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: #d32f2f;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.26);
}

.emotion-class {
  font: inherit;
  letter-spacing: inherit;
  color: currentColor;
  padding: 4px 0 5px;
  border: 0;
  box-sizing: content-box;
  background: none;
  height: 1.4375em;
  margin: 0;
  -webkit-tap-highlight-color: transparent;
  display: block;
  min-width: 0;
  width: 100%;
  -webkit-animation-name: mui-auto-fill-cancel;
  animation-name: mui-auto-fill-cancel;
  -webkit-animation-duration: 10ms;
  animation-duration: 10ms;
  padding: 16.5px 14px;
  padding-right: 0;
}

.emotion-class::-webkit-input-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class::-moz-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class::-ms-input-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class:focus {
  outline: 0;
}

.emotion-class:invalid {
  box-shadow: none;
}

.emotion-class::-webkit-search-decoration {
  -webkit-appearance: none;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-webkit-input-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-moz-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-ms-input-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-webkit-input-placeholder {
  opacity: 0.42;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-moz-placeholder {
  opacity: 0.42;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-ms-input-placeholder {
  opacity: 0.42;
}

.emotion-class.Mui-disabled {
  opacity: 1;
  -webkit-text-fill-color: rgba(0, 0, 0, 0.38);
}

.emotion-class:-webkit-autofill {
  -webkit-animation-duration: 5000s;
  animation-duration: 5000s;
  -webkit-animation-name: mui-auto-fill;
  animation-name: mui-auto-fill;
}

.emotion-class:-webkit-autofill {
  border-radius: inherit;
}

.emotion-class {
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(0, -50%);
  -moz-transform: translate(0, -50%);
  -ms-transform: translate(0, -50%);
  transform: translate(0, -50%);
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  padding: 2px;
  margin-right: -2px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: 1.5rem;
}

.emotion-class {
  text-align: left;
  position: absolute;
  bottom: 0;
  right: 0;
  top: -5px;
  left: 0;
  margin: 0;
  padding: 0 8px;
  pointer-events: none;
  border-radius: inherit;
  border-style: solid;
  border-width: 1px;
  overflow: hidden;
  min-width: 0%;
  border-color: rgba(0, 0, 0, 0.23);
}

.emotion-class {
  float: unset;
  width: auto;
  overflow: hidden;
  padding: 0;
  line-height: 11px;
  -webkit-transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
  transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
}

<div>
  <div
    style="position: sticky; top: 0px; padding: 10px 2rem; width: 100%; z-index: 10000; background-color: white; margin: -32px 0px 10px -2rem; box-shadow: rgb(0 0 0 / 10%) 0px -1px 0px 0px inset; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #E4E5EB;"
  >
    <div
      style="display: flex; justify-content: space-between;"
    >
      <div>
        <span
          class="emotion-class"
        >
          Tenant features
        </span>
        <div
          style="margin-top: 5px;"
        >
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    checked=""
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                    classname="checkboxOnIcon css-1c1jprp"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    availability
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    checked=""
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                    classname="checkboxOnIcon css-1c1jprp"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    availability_calendar
                  </span>
                </span>
              </label>
            </div>
          </span>
        </div>
      </div>
      <div
        style="white-space: nowrap;"
      >
        <span
          class="emotion-class"
        >
          Logged user
        </span>
        <div
          style="margin-top: 5px; width: 130px;"
        >
          <div
            class="emotion-class"
          >
            <select
              class="emotion-class"
            >
              <option
                data-testid="SelectOption_Staff"
                value="staff"
              >
                Staff
              </option>
              <option
                data-testid="SelectOption_BuyerAdmin"
                value="buyer-admin"
              >
                Buyer Admin
              </option>
              <option
                data-testid="SelectOption_BuyerRegular"
                value="buyer-regular"
              >
                Buyer Regular
              </option>
              <option
                data-testid="SelectOption_Vendor"
                value="vendor"
              >
                Vendor
              </option>
            </select>
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
              class="emotion-class"
              size="28"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="emotion-class"
  >
    <div
      class="emotion-class"
    >
      <div
        class="MuiAutocomplete-root MuiAutocomplete-hasPopupIcon emotion-class"
      >
        <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m22.5841%2035.5c15.4044%2035.5%209.58411%2029.6797%209.58411%2022.5c9.58411%2015.3203%2015.4044%209.5%2022.5841%209.5c29.7638%209.5%2035.5841%2015.3203%2035.5841%2022.5c35.5841%2025.5494%2034.5342%2028.3535%2032.7761%2030.5707l38.416%2036.2106l36.2947%2038.3319l30.6548%2032.692c28.4376%2034.4501%2025.6335%2035.5%2022.5841%2035.5zm32.5841%2022.5c32.5841%2028.0228%2028.107%2032.5%2022.5841%2032.5c17.0613%2032.5%2012.5841%2028.0228%2012.5841%2022.5c12.5841%2016.9772%2017.0613%2012.5%2022.5841%2012.5c28.107%2012.5%2032.5841%2016.9772%2032.5841%2022.5z'/%3e%3c/svg%3e
          classname="css-1cmc4y2"
          fill="#A6ABBF"
          size="24"
        />
        <div
          class="emotion-class"
        >
          <div
            style="display: flex; justify-content: center;"
          >
            <div
              class="emotion-class"
            >
              <p
                class="emotion-class"
              >
                SS
              </p>
              <div
                class="emotion-class"
                size="14.5"
              >
                <span
                  aria-label=""
                  class=""
                  data-mui-internal-clone-element="true"
                  style="display: flex; align-items: center; justify-content: center; width: 14.5px; height: 14.5px; border-radius: 20px; background: #06CC87; padding: 0px; border: 2px solid white; box-sizing: border-box;"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                    classname="css-j73ydx"
                    fill="white"
                    size="14.5"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>
        <div
          class="MuiFormControl-root MuiFormControl-fullWidth MuiTextField-root emotion-class"
        >
          <div
            class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-fullWidth MuiInputBase-formControl MuiInputBase-adornedEnd MuiAutocomplete-inputRoot emotion-class"
          >
            <input
              aria-autocomplete="both"
              aria-expanded="false"
              aria-invalid="false"
              autocapitalize="none"
              autocomplete="off"
              class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd MuiAutocomplete-input MuiAutocomplete-inputFocused emotion-class"
              id=":rg:"
              placeholder="Search for a vendor"
              role="combobox"
              spellcheck="false"
              type="text"
              value="Staffing Supplier"
            />
            <div
              class="MuiAutocomplete-endAdornment emotion-class"
            >
              <button
                aria-label="Open"
                class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium MuiAutocomplete-popupIndicator emotion-class"
                tabindex="-1"
                title="Open"
                type="button"
              >
                <svg
                  aria-hidden="true"
                  class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium emotion-class"
                  data-testid="ArrowDropDownIcon"
                  focusable="false"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M7 10l5 5 5-5z"
                  />
                </svg>
              </button>
            </div>
            <fieldset
              aria-hidden="true"
              class="MuiOutlinedInput-notchedOutline emotion-class"
            >
              <legend
                class="emotion-class"
              >
                <span
                  aria-hidden="true"
                  class="notranslate"
                >
                  ​
                </span>
              </legend>
            </fieldset>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
