import {
  FilePickerConfigCallback,
  UploadedFile,
  uploadFileWithPicker,
} from '../../../../../services/Filepicker/filepicker';
import {
  BackendFile,
  convertUploadedFileToBackendFile,
} from '../../../../../utils/files';
import { useAsync } from '../../../../../utils/hooks/useAsync';
import React, { useCallback, useEffect, useState } from 'react';

export interface InputFilesHandleProps {
  getFilePickerConfig: FilePickerConfigCallback;
  onChange: (files: BackendFile[]) => void;
  files?: BackendFile[];
  slots: {
    InputHandle: ({
      openPicker,
    }: {
      openPicker: () => void;
    }) => React.ReactNode;
  };
}

export const InputFilesHandle = ({
  getFilePickerConfig,
  onChange,
  files = [],
  slots: { InputHandle },
}: InputFilesHandleProps) => {
  const [uploadedFiles, setUploadedFiles] = useState<BackendFile[]>(files);

  const {
    isLoading,
    data: { filePickerConfig },
  } = useAsync({
    promise: getFilePickerConfig,
    onResolve: (filePickerConfig) => ({
      filePickerConfig,
    }),
  });

  useEffect(() => {
    setUploadedFiles(files);
  }, [files]);

  const openPicker = useCallback(() => {
    uploadFileWithPicker({
      config: filePickerConfig,
      onSuccess,
    });
  }, [filePickerConfig]);

  const onSuccess = (uploadedFile: UploadedFile) => {
    setUploadedFiles((files) => [
      convertUploadedFileToBackendFile(uploadedFile),
      ...files,
    ]);
  };

  useEffect(() => {
    onChange?.(uploadedFiles);
  }, [JSON.stringify(uploadedFiles)]);

  if (!isLoading && InputHandle) {
    return InputHandle({
      openPicker,
    });
  }
  return null;
};
