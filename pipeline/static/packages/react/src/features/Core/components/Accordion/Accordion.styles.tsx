import { FontFamily, Margins } from '../../../../styles/global.styles';
import styled from '@emotion/styled';
import { createTheme } from '@mui/material';
import { Theme } from '@emotion/react';

export const accordionTheme = (outerTheme: Theme, disableExpanding: boolean) =>
  createTheme(outerTheme, {
    components: {
      MuiAccordion: {
        styleOverrides: {
          root: {
            boxShadow: 'none',
          },
        },
      },
      MuiAccordionSummary: {
        styleOverrides: {
          root: {
            cursor: disableExpanding ? 'default !important' : null,
          },
          content: {
            marginTop: Margins.default,
            marginBottom: Margins.default,
            lineHeight: '30px',
            paddingRight: '10px',
          },
        },
      },
    },
  });

export const AccordionHeaderWrapper = styled('div')({
  width: '100%',
  display: 'flex',
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  gap: '20px',
});

export const AccordionContentWrapper = styled('div')({
  fontFamily: FontFamily.default,
  fontWeight: 400,
  fontSize: '14px',
  lineHeight: '20px',
});
