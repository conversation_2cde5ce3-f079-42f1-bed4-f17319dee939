import React from 'react';
import ExpandContent from './ExpandContent';

export default {
  title: 'Core/Components/ExpandContent',
  component: ExpandContent,
};

const Template = (args) => {
  return (
    <div
      style={{
        maxWidth: '500px',
        border: '3px solid red',
        padding: 'var(--spacing-m)',
      }}
    >
      <ExpandContent>{args.content}</ExpandContent>
    </div>
  );
};

export const Default = (args) => Template(args);
Default.args = {
  content:
    'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Mauris volutpat dolor vel nunc laoreet tempus. Nullam pretium justo sodales, tincidunt turpis eget, sodales ex. Phasellus lacinia ac velit non lacinia. Nullam tristique finibus augue. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Mauris volutpat dolor vel nunc laoreet tempus. Nullam pretium justo sodales, tincidunt turpis eget, sodales ex. Phasellus lacinia ac velit non lacinia. Nullam tristique finibus augue. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Mauris volutpat dolor vel nunc laoreet tempus. Nullam pretium justo sodales, tincidunt turpis eget, sodales ex. Phasellus lacinia ac velit non lacinia. Nullam tristique finibus augue. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Mauris volutpat dolor vel nunc laoreet tempus. Nullam pretium justo sodales, tincidunt turpis eget, sodales ex. Phasellus lacinia ac velit non lacinia. Nullam tristique finibus augue.',
};
export const LessContent = (args) => Template(args);
LessContent.args = {
  content: 'Lorem ipsum dolor sit amet.',
};
