import { ShortlistState } from '../../../components/Shortlist/State/ShortlistState';

import React from 'react';
import VendorJobOpeningsList from '@Worksuite/Features/JobOpenings/containers/VendorJobOpeningsList/VendorJobOpeningsList';
import { getReduxSessionUser } from '../../../services/Reducers/User.reducer.helper';
import SupplierJobOpeningsList from '@Worksuite/Features/JobOpenings/containers/VendorJobOpeningsList/SupplierJobOpeningsList';

const VendorJobOpeningsListState = () => {
  const isSupplier = getReduxSessionUser().isSupplier;
  return (
    <ShortlistState stateName="JOBOPENINGS__VENDOR_JOB_OPENINGS_LIST">
      {isSupplier ? <SupplierJobOpeningsList /> : <VendorJobOpeningsList />}
    </ShortlistState>
  );
};

export default VendorJobOpeningsListState;
