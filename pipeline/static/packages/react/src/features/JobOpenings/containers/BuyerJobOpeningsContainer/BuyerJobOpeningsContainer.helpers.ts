import JobOpeningsApi from '../../../../services/Api/JobOpenings';
import { MarketplaceService } from '../../../../services/Marketplace/MarketplaceService';
import { goToStateOrOpenInNewTab } from '../../../../services/Reducers/AngularMigration.helpers';
import { JobOpening } from '../../../../types/job-opening';
import { getJobOpeningActions } from '../job-opening-actions';
import {
  convertItemActionsToDropdownOptions,
  wrapActionsWithRowUpdate,
} from '../../../../utils/other/item-actions';
import { CustomField } from '../../../../components/CustomFields/CustomFields';
import { composeCustomFieldKey } from '../../../../components/CustomFields/custom-fields.helpers';
import { DataTableProps } from '../../../../components/DataTable/DataTable.types';
import { SearchParams } from '../../../../components/Search/ElasticSearch/ElasticSearch.types';
import { mapCustomFieldToColumn } from '../../../../components/Utils/List/List.helpers';
import produce from 'immer';
import { useEffect, useState } from 'react';

export const useCustomFieldColumns = (customFields: CustomField[]) => {
  const [customFieldColumns, setCustomFieldColumns] = useState(null);

  useEffect(() => {
    if (customFields) {
      setCustomFieldColumns(mapCustomFieldsToColumns(customFields));
    }
  }, [customFields]);

  return { customFieldColumns };
};

const mapCustomFieldsToColumns = (customFields: CustomField[]) => {
  const convertedCustomFields = [];
  customFields.forEach((customField) => {
    try {
      convertedCustomFields.push({
        ...mapCustomFieldToColumn(customField),
        getCellValue: (item) => item[composeCustomFieldKey(customField.id)],
      });
    } catch (e) {
      // Carry on
    }
  });
  return convertedCustomFields;
};

export const getSearchCallback =
  (jobOpeningTemplateId?: number) => (params: SearchParams) => {
    params = produce(params, (draft) => {
      if (
        !params.terms &&
        !draft.filters.some((filter) => filter.hasOwnProperty('archived'))
      ) {
        draft.filters.push({ archived: false });
      }
      if (jobOpeningTemplateId) {
        draft.filters.push({
          job_opening_template_id: jobOpeningTemplateId.toString(),
        });
      }
    });
    return JobOpeningsApi.search(params);
  };

export const getJobOpeningDetailsStateName = (jobOpening: JobOpening) =>
  MarketplaceService.getJobOpeningPages(jobOpening)[0];

export const goToJobOpeningDetailsPage: DataTableProps['onRowClick'] = (
  data: JobOpening,
  event
) => {
  goToStateOrOpenInNewTab(getJobOpeningDetailsStateName(data), {
    id: data.id,
  })(event);
};

export const getJobOpeningDropdownOptions = async (data, context) => {
  const actions = await getJobOpeningActions(data as JobOpening);
  const dataTableActions = wrapActionsWithRowUpdate(actions, data, context);
  return convertItemActionsToDropdownOptions(dataTableActions);
};
