import { ShortlistState } from '../../../components/Shortlist/State/ShortlistState';
import ContractsComingSoonContainer from '../containers/ContractsComingSoonContainer/ContractsComingSoonContainer';
import PropTypes from 'prop-types';
import React from 'react';

const ContractsComingSoonState = ({
  calendlyOnClick = null,
}: {
  calendlyOnClick: (event) => void;
}) => {
  return (
    <ShortlistState stateName={'CONTRACTS__COMING_SOON'}>
      <ContractsComingSoonContainer calendlyOnClick={calendlyOnClick} />
    </ShortlistState>
  );
};

ContractsComingSoonState.propTypes = {
  calendlyOnClick: PropTypes.func,
};
export default ContractsComingSoonState;
