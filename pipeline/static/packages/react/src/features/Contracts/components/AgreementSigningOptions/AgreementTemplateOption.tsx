import Button from '../../../../components/FormElements/Button/Button';
import { openModal } from '../../../../components/Utils/Modals/modal.helpers';
import i18n from '../../../../i18n/i18n';
import Contracts from '../../../../services/Api/Contracts';
import {
  displayFailureMessage,
  displaySuccessMessage,
} from '../../../../services/Reducers/Notifications.reducer.helper';
import { ContractResponseData } from '../../../../types/contracts';
import { noop } from '../../../../utils';
import AgreementTemplateSelect from '../AgreementTemplateSelect/AgreementTemplateSelect';
import { AgreementTemplate } from '@Worksuite/Features/Agreements/AgreementTemplateEditor/AgreementTemplateEditor.d';
import { getErrorsList } from '@Worksuite/Features/Contracts/containers/contracts.helpers';
import styled from '@emotion/styled';
import React, { useState } from 'react';

const t = (id: string, params?: {}) =>
  i18n.t(`Contracts:ContractAcceptance.${id}`, params);

const AgreementTemplateSelectWrapper = styled('div')(
  ({ hasAgreementTemplates = false }: { hasAgreementTemplates: boolean }) => ({
    ...(!hasAgreementTemplates && {
      marginBottom: '-20px',
    }),
  })
);

const AgreementTemplateOption = ({
  contractId,
  onChange,
}: {
  contractId: ContractResponseData['id'];
  onChange: () => void;
}) => {
  const [selectedTemplateId, setSelectedTemplateId] = useState<number>();
  const [agreementTemplates, setAgreementTemplates] = useState<
    AgreementTemplate[]
  >([]);
  const hasAgreementTemplates = agreementTemplates.length > 0;
  const selectedAgreementTemplateName = agreementTemplates?.find(
    ({ id }) => selectedTemplateId === id
  )?.name;
  const handleModalConfirm = async () => {
    try {
      await Contracts.saveContractAgreementTemplate(
        contractId,
        selectedTemplateId
      );
      displaySuccessMessage(t('SaveAgreementTemplateSuccessMessage'));
      onChange();
    } catch ({ data }) {
      displayFailureMessage(getErrorsList(data));
    }
  };

  return (
    <AgreementTemplateSelectWrapper
      hasAgreementTemplates={hasAgreementTemplates}
    >
      <AgreementTemplateSelect
        onChange={({ agreementId }) => setSelectedTemplateId(agreementId)}
        onTemplatesChange={setAgreementTemplates}
      />

      {hasAgreementTemplates && (
        <Button
          rounded={false}
          size="small"
          label={t('ConfirmAgreementOptionButton')}
          onClick={() =>
            openModal({
              type: 'message_modal',
              header: t('ConfirmAgreementOptionModalHeader'),
              message: t('ConfirmAgreementOptionModalBody', {
                agreementName: selectedAgreementTemplateName,
              }),
              renderMessageAsHtml: true,
              confirmButtonLabel: t('ConfirmAgreementOptionModalConfirmButton'),
              cancelButtonLabel: i18n.t('common:Form.Cancel'),
            })
              .then(handleModalConfirm)
              .catch(noop)
          }
          disabled={!selectedTemplateId}
        />
      )}
    </AgreementTemplateSelectWrapper>
  );
};

export default AgreementTemplateOption;
