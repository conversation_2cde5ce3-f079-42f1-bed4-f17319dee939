import contract from '../../../../fixtures/api/contracts/contract.json';
import contractToCompare from '../../../../fixtures/api/contracts/contract_compare.json';
import paymentsLineItemsCategories from '../../../../fixtures/api/payments/line_items_categories/index.json';
import tasks from '../../../../fixtures/api/tasks/index.json';
import changedTasks from '../../../../fixtures/api/tasks/index_for_compare.json';
import { reduxStore } from '../../../../services/Redux';
import { getApiMockAdapter } from '../../../../utils/storybook-utils';
import ContractCompareChanges from './ContractCompareChanges';
import React, { useEffect, useState } from 'react';

export default {
  title: 'Features/Contracts/Components/ContractCompareChanges',
  component: ContractCompareChanges,
};

const addTaskIds = (tasks) =>
  tasks.map((task) => ({
    ...task,
    task_id: task.id,
  }));

const contractWithTasks = {
  ...contract,
  task_templates: addTaskIds(tasks),
};

const contractToCompareWithTasks = {
  ...contractToCompare,
  task_templates: addTaskIds(changedTasks),
};

const Template = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  useEffect(() => {
    const mock = getApiMockAdapter();
    mock
      .onGet(/\/api\/payments\/line_items_categories\//gm)
      .reply(200, paymentsLineItemsCategories);
    reduxStore.dispatch({
      type: 'SET_TENANT',
      tenant: {
        features: [],
      },
    });
    setIsLoaded(true);
    return () => mock.reset();
  }, []);
  return isLoaded ? (
    <div style={{ maxWidth: '900px' }}>
      <ContractCompareChanges
        contractPrevVersion={contractWithTasks}
        contractChangedVersion={contractToCompareWithTasks}
        contract={contractWithTasks}
        customRates={[]}
        taskGroups={null}
        taskGlobalCustomFields={null}
      />
    </div>
  ) : (
    <></>
  );
};

export const Default = () => Template();
