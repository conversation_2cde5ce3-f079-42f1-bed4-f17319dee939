import * as stories from './ContractStageAction.stories';
import { composeStories } from '@storybook/react';
import { render, screen } from '@testing-library/react';
import React from 'react';

describe('ContractStageAction - ', () => {
  test('check with snapshot', async () => {
    const { Default: ContractStageAction } = composeStories(stories);
    const { container } = render(<ContractStageAction />);

    expect(screen.getByText('Graphic designer contract')).toBeVisible();
    expect(container).toMatchSnapshot();
  });
});
