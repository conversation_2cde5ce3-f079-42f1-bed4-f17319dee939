import <PERSON><PERSON> from 'joi';
import { useEffect, useState } from 'react';
import Button from '../../../../components/FormElements/Button/Button';
import InputDatePicker from '../../../../components/FormElements/InputDatePicker/InputDatePicker';
import InputText from '../../../../components/FormElements/InputText/InputText';
import contractJSON from '../../../../fixtures/api/contracts/contract_SOW.json';
import globalFieldsTemplates from '../../../../fixtures/api/custom_fields_templates/global_fields_templates.json';
import taskGroupsJSON from '../../../../fixtures/api/task_groups/multiple_by_id_filtered.json';
import timesheetTemplates from '../../../../fixtures/api/timesheets/timesheet_templates.json';
import customRateTypesJSON from '../../../../fixtures/api2/custom_rate_types/index.json';
import { ContractRequestData } from '../../../../types/contracts';
import { useDebouncedState } from '../../../../utils/hooks';
import { getApiMockAdapter } from '../../../../utils/storybook-utils';
import { withStorybookTenantManagement } from '../../../../utils/storybook.hooks';
import { validateSchema } from '../../../Core/components/Form/form.helpers';
import { ContractTasks } from './ContractTasks';

export default {
  title: 'Features/Contracts/Components/ContractTasks',
  component: ContractTasks,
};

type Props = {
  hasTasks?: boolean;
  isTimeAndMaterial?: boolean;
};

const Template = ({ hasTasks, isTimeAndMaterial }: Props) => {
  const [schema, setSchema] = useState<Joi.Schema>();
  const [contract, setContract] = useDebouncedState(
    {
      ...contractJSON,
      start_date: '2024-06-10',
      end_date: '2024-06-15',
    } as unknown as ContractRequestData,
    500
  );
  const [tasks, setTasks] = useState(hasTasks ? contract.task_templates : []);
  const [errors, setErrors] = useState([]);

  const [isLoaded, setIsLoaded] = useState(false);
  useEffect(() => {
    const mock = getApiMockAdapter();
    mock.onGet(/\/api\/custom_rate_types\//gm).reply(200, customRateTypesJSON);
    mock.onGet(/\/api\/task_groups\/[^/]*\//gm).reply(({ url }) => {
      if (url) {
        const [, , id] = url.split('/');
        return [
          200,
          {
            ...taskGroupsJSON.find((i) => String(i.id) === id),
            date_start: '2024-06-12',
            date_end: '2024-06-13',
          },
        ];
      }
      throw new Error();
    });
    mock
      .onGet(/\/api\/task_groups\//gm)
      .reply(200, { results: taskGroupsJSON });
    mock
      .onGet(/\/api\/global_fields_templates\//gm)
      .reply(200, globalFieldsTemplates);
    setIsLoaded(true);
    mock
      .onGet(/\/api\/timesheet\/templates\//gm)
      .reply(200, timesheetTemplates);
    return () => mock.reset();
  }, []);

  if (!isLoaded) {
    return <></>;
  }
  return (
    <div style={{ width: '650px' }}>
      <InputText
        defaultValue={contract.name}
        onChange={(name) => setContract({ ...contract, name })}
      />

      <InputText
        defaultValue={contract.currency}
        onChange={(currency) =>
          setContract({
            ...contract,
            payment_template: {
              ...contract.payment_template,
              currency,
            },
          })
        }
      />

      <InputDatePicker
        defaultValue={contract.start_date || undefined}
        onChange={(start_date) => setContract({ ...contract, start_date })}
      />
      <InputDatePicker
        defaultValue={contract.end_date || undefined}
        onChange={(end_date) => setContract({ ...contract, end_date })}
      />

      <ContractTasks
        contract={contract}
        tasks={tasks}
        onChange={setTasks}
        onSchemaChange={setSchema}
        errors={errors}
        onChangeTotalTaskFees={console.log} // eslint-disable-line
        isTimeAndMaterial={isTimeAndMaterial}
      />

      <Button
        label="Submit"
        disabled={!schema}
        onClick={() => {
          if (schema) {
            const result = validateSchema(schema, tasks);
            setErrors(result.errors);
          }
        }}
      />
    </div>
  );
};

export const Default = withStorybookTenantManagement(
  () => <Template hasTasks />,
  {
    defaultFeatures: [],
    changeableFeatures: [],
    defaultSessionUser: 'buyer-admin',
  }
);

export const NoTasksInitially = withStorybookTenantManagement(
  () => <Template />,
  {
    defaultFeatures: [],
    changeableFeatures: [],
    defaultSessionUser: 'buyer-admin',
  }
);

export const TimeAndMaterial = () => <Template isTimeAndMaterial />;
