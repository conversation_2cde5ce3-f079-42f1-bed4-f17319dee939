import Spinner from '../../../../components/Utils/Spinner/Spinner';
import Contracts from '../../../../services/Api/Contracts';
import { ContractResponseData } from '../../../../types/contracts';
import { useAsync } from '../../../../utils/hooks/useAsync';
import ContractAcceptanceWorkflow from '@Worksuite/Features/Contracts/components/ContractAcceptance/ContractAcceptanceWorkflow';
import {
  ContractChangeRequestHistoryRowBody,
  ContractChangeRequestHistoryStagePreview,
  ContractChangeRequestHistoryWrapper,
} from '@Worksuite/Features/Contracts/components/ContractChangeRequestHistory/ContractChangeRequestHistory.styles';
import RevisionZeroContent from '@Worksuite/Features/Contracts/components/ContractChangeRequestHistory/RowRevisionZeroContent';
import ContractCompareChanges from '@Worksuite/Features/Contracts/components/ContractCompareChanges/ContractCompareChanges';
import i18n from 'i18next';
import React from 'react';
import { Project } from '../../../../types/project';
import { CustomRateType } from '../../../../types/custom-rates';
import { CustomField } from '../../../../components/CustomFields/CustomFields';

const RowContent = ({
  contract,
  onContractDataChange,
  revisionNumber,
  acceptedRevisions,
  taskGroups,
  customRates,
  taskGlobalCustomFields,
}: {
  contract: ContractResponseData;
  onContractDataChange: () => void;
  revisionNumber: number;
  acceptedRevisions: number[];
  taskGroups: Pick<Project, 'id' | 'name'>[];
  customRates: CustomRateType[];
  taskGlobalCustomFields: CustomField[];
}) => {
  const prevAcceptedRevNumber =
    acceptedRevisions.find(
      (acceptedRevision) => acceptedRevision < revisionNumber
    ) ?? 0;

  const { isLoading: isLoadingRevision, data: selectedRevision } = useAsync(
    {
      promise:
        revisionNumber >= 0
          ? () => Contracts.getRevision(contract.id, revisionNumber)
          : null,
      onResolve: ({ data }) => data,
    },
    [revisionNumber]
  );

  const { isLoading: isLoadingPrevRevision, data: prevRevision } = useAsync(
    {
      promise:
        revisionNumber - 1 >= 0
          ? () => Contracts.getRevision(contract.id, prevAcceptedRevNumber)
          : null,
      onResolve: ({ data }) => data,
    },
    [revisionNumber]
  );

  const isLoading = isLoadingRevision || isLoadingPrevRevision;

  const isRevisionZero =
    !isLoadingRevision &&
    revisionNumber === 0 &&
    Object.keys(selectedRevision).length;
  const hasRevisionsToCompare =
    !isLoading &&
    Object.keys(selectedRevision).length &&
    Object.keys(prevRevision).length;

  const getHeaderString = (revNumber: number) => {
    if (revNumber === 0) {
      return i18n.t(
        'Contracts:ChangeRequestHistory.RevisionZeroAdditionalInfo'
      );
    }

    return i18n.t('Contracts:ContractChangeRequestAcceptance.RevisionNumber', {
      revisionNumber: revNumber,
    });
  };

  if (isRevisionZero) {
    return (
      <RevisionZeroContent
        contract={contract}
        onContractDataChange={onContractDataChange}
      />
    );
  }

  if (hasRevisionsToCompare) {
    const customHeaders = {
      prev: {
        name: getHeaderString(prevAcceptedRevNumber),
        color: 'var(--statuses-status-initiated)',
      },
      changed: {
        name: getHeaderString(revisionNumber),
        color: 'var(--statuses-status-processing)',
      },
    };

    return (
      <ContractChangeRequestHistoryRowBody>
        <ContractChangeRequestHistoryWrapper>
          <ContractCompareChanges
            contractPrevVersion={prevRevision}
            contractChangedVersion={selectedRevision}
            customHeaders={customHeaders}
            contract={contract}
            customRates={customRates}
            taskGroups={taskGroups}
            taskGlobalCustomFields={taskGlobalCustomFields}
          />
        </ContractChangeRequestHistoryWrapper>

        <ContractChangeRequestHistoryStagePreview>
          <ContractAcceptanceWorkflow
            contract={selectedRevision}
            onContractDataChange={onContractDataChange}
            readOnly
          />
        </ContractChangeRequestHistoryStagePreview>
      </ContractChangeRequestHistoryRowBody>
    );
  }
  return <Spinner />;
};

export default RowContent;
