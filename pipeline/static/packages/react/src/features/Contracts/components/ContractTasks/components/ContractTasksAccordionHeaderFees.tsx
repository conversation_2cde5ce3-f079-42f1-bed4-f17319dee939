import styled from '@emotion/styled';
import { EmptyIconGrey } from '../../../../../components/DataTable/DataTable.helpers';
import { getBudgetRateTypeValue } from '../../../../../components/PreviewForm/ContentElements/FormPreviewTask';
import { getTranslationFn } from '../../../../../i18n/i18n.helpers';
import {
  Body,
  BodySmall,
  Label,
  LabelSmall,
  LabelXSmall,
} from '../../../../../styles/typography';
import { ContractTask } from '../../../../../types/contracts';
import { CustomRateType } from '../../../../../types/custom-rates';
import { Task } from '../../../../../types/task';
import { formatCurrency } from '../../../../../utils';
import { generateTestId } from '../../../../../utils/test.utils';

const getTestId = (testId: string) =>
  generateTestId(testId, 'ContractTasksAccordionHeaderFees')['data-testid'];

const t = getTranslationFn('Contracts:ContractTasksPreview');

const TaskPreviewHeaderTotalFee = styled(BodySmall)({
  display: 'flex',
  gap: 'var(--spacing-m)',
  whiteSpace: 'nowrap',
  justifyContent: 'flex-end',
});

const TaskPreviewHeaderTotalFeeValue = styled('span')({
  color: 'var(--glyphs-basic-glyphs-subdued)',
});

const SmallGreyText = styled(LabelXSmall)({
  color: 'var(--glyphs-basic-glyphs-subdued)',
});

const BudgetRatePerTimeUnitValue = styled(Body)({
  color: 'var(--glyphs-basic-glyphs-subdued)',
});

const TaskPreviewHeaderFees = styled('div')({
  display: 'table',
  margin: '-10px 0',
});

const TaskPreviewHeaderRow = styled('div')({
  display: 'table-row',
  lineHeight: '20px',

  'div:first-of-type': {
    textAlign: 'right',
    paddingRight: 'var(--spacing-s)',
  },

  'div:last-of-type svg': {
    margin: '-10px -6px',
  },
});

const TaskPreviewHeaderCol = styled('div')({
  display: 'table-cell',
  whiteSpace: 'nowrap',
});

const ContractTasksAccordionHeaderFees = ({
  task,
  currency,
  customRates,
}: {
  task: Task | ContractTask;
  currency: string;
  customRates: CustomRateType[];
}) => {
  const customRateLabel = getBudgetRateTypeValue(
    customRates,
    task.budget_rate_type
  );
  return (
    <>
      {!task.budget_rate_type && task.budget_total && (
        <TaskPreviewHeaderTotalFee>
          <LabelSmall data-testid={getTestId(`TotalFeeLabel`)}>
            {t('TotalFee')}
          </LabelSmall>
          <TaskPreviewHeaderTotalFeeValue
            data-testid={getTestId(`TotalFeeValue`)}
          >
            {formatCurrency(task.budget_total, 2, currency)}
          </TaskPreviewHeaderTotalFeeValue>
        </TaskPreviewHeaderTotalFee>
      )}
      {task.budget_rate_type && (
        <TaskPreviewHeaderFees>
          <TaskPreviewHeaderRow>
            <TaskPreviewHeaderCol>
              <Label data-testid={getTestId(`${customRateLabel}Label`)}>
                {customRateLabel}
              </Label>
            </TaskPreviewHeaderCol>
            <TaskPreviewHeaderCol>
              <BudgetRatePerTimeUnitValue
                data-testid={getTestId(`${customRateLabel}Value`)}
              >
                {task.budget_rate_per_time_unit ? (
                  formatCurrency(task.budget_rate_per_time_unit, 2, currency)
                ) : (
                  <EmptyIconGrey />
                )}
              </BudgetRatePerTimeUnitValue>
            </TaskPreviewHeaderCol>
          </TaskPreviewHeaderRow>
          <TaskPreviewHeaderRow>
            <TaskPreviewHeaderCol>
              <SmallGreyText data-testid={getTestId(`TotalFeeLabel`)}>
                {t('TotalFee')}
              </SmallGreyText>
            </TaskPreviewHeaderCol>
            <TaskPreviewHeaderCol>
              <SmallGreyText data-testid={getTestId(`TotalFeeValue`)}>
                {task.budget_total ? (
                  formatCurrency(task.budget_total, 2, currency)
                ) : (
                  <EmptyIconGrey />
                )}
              </SmallGreyText>
            </TaskPreviewHeaderCol>
          </TaskPreviewHeaderRow>
        </TaskPreviewHeaderFees>
      )}
    </>
  );
};

export default ContractTasksAccordionHeaderFees;
