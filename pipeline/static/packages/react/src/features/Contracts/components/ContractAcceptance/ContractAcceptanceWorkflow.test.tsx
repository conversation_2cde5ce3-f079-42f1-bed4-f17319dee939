import { SHORTLIST_USER_KEY } from '../../../../utils/storybook.hooks';
import * as stories from './ContractAcceptanceWorkflow.stories';
import { composeStories } from '@storybook/react';
import '@testing-library/jest-dom';
import { render } from '@testing-library/react';
import React from 'react';

describe('ContractAcceptanceWorkflow - ', () => {
  test.each`
    story                 | userType
    ${'WaitingForVendor'} | ${'buyer-admin'}
    ${'WaitingForVendor'} | ${'buyer-regular'}
    ${'WaitingForVendor'} | ${'vendor'}
    ${'Rejected'}         | ${'buyer-admin'}
    ${'Rejected'}         | ${'buyer-regular'}
    ${'Rejected'}         | ${'vendor'}
    ${'WaitingForBuyer'}  | ${'buyer-admin'}
    ${'WaitingForBuyer'}  | ${'buyer-regular'}
    ${'WaitingForBuyer'}  | ${'vendor'}
    ${'Accepted'}         | ${'buyer-admin'}
    ${'Accepted'}         | ${'buyer-regular'}
    ${'Accepted'}         | ${'vendor'}
  `('$story as $userType snapshot should match', ({ story, userType }) => {
    sessionStorage.setItem(SHORTLIST_USER_KEY, userType);
    const composedStories = composeStories(stories);
    const { container } = render(React.createElement(composedStories[story]));
    expect(container).toMatchSnapshot();
  });
});
