import i18n from '../../../../i18n/i18n';
import { ContractResponseData } from '../../../../types/contracts';
import { Vendor } from '../../../../types/vendor';
import AgreementTemplateOption from './AgreementTemplateOption';
import UploadSignedAgreementOption from './UploadSignedAgreementOption';
import UploadAgreementForSigningOption from '@Worksuite/Features/Contracts/components/AgreementSigningOptions/UploadAgreementForSigningOption';
import ToggleButtonGroup from '@Worksuite/Features/Core/components/ToggleButtonGroup/ToggleButtonGroup';
import styled from '@emotion/styled';
import React, { useState } from 'react';

const t = (id: string) => i18n.t(`Contracts:ContractAcceptance.${id}`);

const AgreementSigningOptionsWrapper = styled('div')({
  display: 'flex',
  flexDirection: 'column',
  gap: 'var(--spacing-l)',
});

export type SigningOptionTabType =
  | 'agreement_template'
  | 'upload_signed_agreement'
  | 'upload_agreement_for_signing';

const AgreementSigningOptions = ({
  contract,
  vendorSlug,
  onChange,
  onTabChange,
}: {
  contract: ContractResponseData;
  vendorSlug: Vendor['slug'];
  onChange: () => void;
  onTabChange: (tab: SigningOptionTabType) => void;
}) => {
  const [currentTab, setCurrentTab] =
    useState<SigningOptionTabType>('agreement_template');

  return (
    <AgreementSigningOptionsWrapper>
      <ToggleButtonGroup
        options={[
          {
            text: t('AgreementTemplate'),
            key: 'agreement_template',
          },
          {
            text: t('UploadSignedAgreement'),
            key: 'upload_signed_agreement',
          },
          {
            text: t('UploadAgreementForSigning'),
            key: 'upload_agreement_for_signing',
          },
        ]}
        defaultValue={currentTab}
        onChange={(tab: SigningOptionTabType) => {
          setCurrentTab(tab);
          onTabChange(tab);
        }}
      />
      {currentTab === 'agreement_template' && (
        <AgreementTemplateOption contractId={contract.id} onChange={onChange} />
      )}
      {currentTab === 'upload_signed_agreement' && (
        <UploadSignedAgreementOption
          contractId={contract.id}
          vendorSlug={vendorSlug}
          onChange={onChange}
        />
      )}
      {currentTab === 'upload_agreement_for_signing' && (
        <UploadAgreementForSigningOption
          contract={contract}
          onChange={onChange}
        />
      )}
    </AgreementSigningOptionsWrapper>
  );
};

export default AgreementSigningOptions;
