import { ContractResponseData } from '../../../../types/contracts';
import ContractAcceptanceWorkflow from '@Worksuite/Features/Contracts/components/ContractAcceptance/ContractAcceptanceWorkflow';
import {
  ContractChangeRequestHistoryRowBody,
  ContractChangeRequestHistoryStagePreview,
} from '@Worksuite/Features/Contracts/components/ContractChangeRequestHistory/ContractChangeRequestHistory.styles';
import React from 'react';

const RevisionZeroContent = ({
  contract,
  onContractDataChange,
}: {
  contract: ContractResponseData;
  onContractDataChange: () => void;
}) => {
  return (
    <ContractChangeRequestHistoryRowBody>
      <ContractChangeRequestHistoryStagePreview className="ContractChangeRequestHistory_revisionZero">
        <ContractAcceptanceWorkflow
          contract={contract}
          onContractDataChange={onContractDataChange}
        />
      </ContractChangeRequestHistoryStagePreview>
    </ContractChangeRequestHistoryRowBody>
  );
};

export default RevisionZeroContent;
