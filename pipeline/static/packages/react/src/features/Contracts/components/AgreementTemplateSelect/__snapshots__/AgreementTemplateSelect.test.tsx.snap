// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AgreementTemplateSelect -  selecting agreement template should show list of templates 1`] = `
.emotion-class {
  width: 100%;
  box-sizing: border-box;
  background-color: transparent;
  border-radius: 4px;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  padding: 0;
  white-space: pre-wrap;
  color: #303757;
}

.emotion-class {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  padding-bottom: 20px;
  position: relative;
}

.emotion-class {
  box-sizing: border-box;
  margin: 0;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  max-width: 100%;
}

@media (min-width:600px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

@media (min-width:900px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

@media (min-width:1200px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

@media (min-width:1536px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  width: 100%;
  height: 1px;
  background-color: #E4E5EB;
  margin-bottom: 20px;
}

.emotion-class {
  margin-top: 10px;
  padding-left: 30px;
}

.emotion-class li {
  margin-bottom: 10px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 40px;
}

.emotion-class {
  position: relative;
}

.emotion-class svg {
  fill: #465AB6;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class {
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
  font-size: 13px;
  color: #465AB6;
  vertical-align: middle;
}

.emotion-class {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  padding-bottom: 0;
  position: relative;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class:hover .checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class:hover>.checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6!important;
}

.emotion-class {
  margin: 1px 0 -1px 6px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  color: #303757;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

<div>
  <div
    class="FormContainerWrapper emotion-class"
    data-testid="AgreementTemplateSelectContainer"
  >
    <div
      class="MuiGrid-root MuiGrid-container FormContainerItem emotion-class"
      data-testid="FormContainerItem_AgreementTemplate"
    >
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-12 FormContainerItemElement FormContainerItemElementFirst FormContainerItemElementLast emotion-class"
      >
        <div
          style="margin-bottom: 5px;"
        >
          <span
            class="emotion-class"
            data-testid="InputLabel_SelectTemplate"
          >
            Select template
          </span>
        </div>
        <div
          class="emotion-class"
        >
          <select
            class="emotion-class"
            data-testid="Select_SelectTemplate"
          >
            <option
              value=""
            >
              Choose agreement template
            </option>
            <option
              data-testid="SelectOption_1"
              value="1"
            >
              Lorem ipsum
            </option>
            <option
              data-testid="SelectOption_2"
              value="2"
            >
              Multisign lorem ipsum
            </option>
            <option
              data-testid="SelectOption_3"
              value="3"
            >
              Legacy template
            </option>
            <option
              data-testid="SelectOption_4"
              value="4"
            >
              Archived example
            </option>
          </select>
          <icon-mock
            classname="css-1qbf8bs"
            disabled="false"
            size="28"
          />
        </div>
      </div>
    </div>
    <div
      class="emotion-class"
    />
    <div
      class="MuiGrid-root MuiGrid-container FormContainerItem emotion-class"
      data-testid="FormContainerItem_AgreementTemplateSigners"
    >
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-12 FormContainerItemElement FormContainerItemElementFirst FormContainerItemElementLast emotion-class"
      >
        <span
          class="emotion-class"
        >
          Agreement to be signed by (in order):
        </span>
        <ul
          class="emotion-class"
        >
          <li>
            Partner
          </li>
          <li>
            Emanuel Christiansen
          </li>
          <li>
            Adelbert Nolan
          </li>
          <li>
            Maurizio Pollini
          </li>
          <li />
        </ul>
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-container FormContainerItem emotion-class"
      data-testid="FormContainerItem_AcceptanceAgreementButtons"
    >
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-12 FormContainerItemElement FormContainerItemElementFirst FormContainerItemElementLast emotion-class"
      >
        <div
          class="emotion-class"
        >
          <a
            class=" emotion-class"
            data-testid="AgreementTemplateSelect_EditButton"
          >
            <icon-mock
              classname="css-12ecxzt"
              size="20"
              style="margin: 0px 5px 0px -2px;"
            />
            <span
              class="emotion-class"
            >
              Edit this agreement template
            </span>
          </a>
          <a
            class=" emotion-class"
            data-testid="AgreementTemplateSelect_PreviewButton"
          >
            <icon-mock
              classname="css-12ecxzt"
              size="20"
              style="margin: 0px 5px 0px -2px;"
            />
            <span
              class="emotion-class"
            >
              Preview the agreement
            </span>
          </a>
        </div>
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-container FormContainerItem emotion-class"
      data-testid="FormContainerItem_AcceptanceAgreementSkipTemplate"
    >
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-12 FormContainerItemElement FormContainerItemElementFirst FormContainerItemElementLast emotion-class"
      >
        <div
          class="emotion-class"
        >
          <label
            class="emotion-class"
            data-testid="InputCheckbox"
          >
            <span
              class="emotion-class"
            >
              <input
                type="checkbox"
              />
              <icon-mock
                classname="checkboxOffIcon css-1dmky3q"
                fill="#D1D6ED"
              />
            </span>
            <span
              class="emotion-class"
            >
              <span
                aria-label=""
                class=" emotion-class"
                data-mui-internal-clone-element="true"
              >
                Skip template selection until the Contract review
              </span>
            </span>
          </label>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`AgreementTemplateSelect -  snapshot should match 1`] = `
.emotion-class {
  width: 100%;
  box-sizing: border-box;
  background-color: transparent;
  border-radius: 4px;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  padding: 0;
  white-space: pre-wrap;
  color: #303757;
}

.emotion-class {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  padding-bottom: 20px;
  position: relative;
}

.emotion-class {
  box-sizing: border-box;
  margin: 0;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  max-width: 100%;
}

@media (min-width:600px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

@media (min-width:900px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

@media (min-width:1200px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

@media (min-width:1536px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 40px;
}

.emotion-class {
  position: relative;
}

.emotion-class svg {
  fill: #A6ABBF;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: default;
}

.emotion-class {
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
  font-size: 13px;
  color: #A6ABBF;
  vertical-align: middle;
}

.emotion-class {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  padding-bottom: 0;
  position: relative;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class:hover .checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class:hover>.checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6!important;
}

.emotion-class {
  margin: 1px 0 -1px 6px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  color: #303757;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

<div>
  <div
    class="FormContainerWrapper emotion-class"
    data-testid="AgreementTemplateSelectContainer"
  >
    <div
      class="MuiGrid-root MuiGrid-container FormContainerItem emotion-class"
      data-testid="FormContainerItem_AgreementTemplate"
    >
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-12 FormContainerItemElement FormContainerItemElementFirst FormContainerItemElementLast emotion-class"
      >
        <div
          style="margin-bottom: 5px;"
        >
          <span
            class="emotion-class"
            data-testid="InputLabel_SelectTemplate"
          >
            Select template
          </span>
        </div>
        <div
          class="emotion-class"
        >
          <select
            class="emotion-class"
            data-testid="Select_SelectTemplate"
          >
            <option
              value=""
            >
              Choose agreement template
            </option>
            <option
              data-testid="SelectOption_1"
              value="1"
            >
              Lorem ipsum
            </option>
            <option
              data-testid="SelectOption_2"
              value="2"
            >
              Multisign lorem ipsum
            </option>
            <option
              data-testid="SelectOption_3"
              value="3"
            >
              Legacy template
            </option>
            <option
              data-testid="SelectOption_4"
              value="4"
            >
              Archived example
            </option>
          </select>
          <icon-mock
            classname="css-1qbf8bs"
            disabled="false"
            size="28"
          />
        </div>
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-container FormContainerItem emotion-class"
      data-testid="FormContainerItem_AcceptanceAgreementButtons"
    >
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-12 FormContainerItemElement FormContainerItemElementFirst FormContainerItemElementLast emotion-class"
      >
        <div
          class="emotion-class"
        >
          <a
            class=" emotion-class"
            data-testid="AgreementTemplateSelect_EditButton"
            disabled=""
          >
            <icon-mock
              classname="css-12ecxzt"
              size="20"
              style="margin: 0px 5px 0px -2px;"
            />
            <span
              class="emotion-class"
              disabled=""
            >
              Edit this agreement template
            </span>
          </a>
          <a
            class=" emotion-class"
            data-testid="AgreementTemplateSelect_PreviewButton"
            disabled=""
          >
            <icon-mock
              classname="css-12ecxzt"
              size="20"
              style="margin: 0px 5px 0px -2px;"
            />
            <span
              class="emotion-class"
              disabled=""
            >
              Preview the agreement
            </span>
          </a>
        </div>
      </div>
    </div>
    <div
      class="MuiGrid-root MuiGrid-container FormContainerItem emotion-class"
      data-testid="FormContainerItem_AcceptanceAgreementSkipTemplate"
    >
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-12 FormContainerItemElement FormContainerItemElementFirst FormContainerItemElementLast emotion-class"
      >
        <div
          class="emotion-class"
        >
          <label
            class="emotion-class"
            data-testid="InputCheckbox"
          >
            <span
              class="emotion-class"
            >
              <input
                type="checkbox"
              />
              <icon-mock
                classname="checkboxOffIcon css-1dmky3q"
                fill="#D1D6ED"
              />
            </span>
            <span
              class="emotion-class"
            >
              <span
                aria-label=""
                class=" emotion-class"
                data-mui-internal-clone-element="true"
              >
                Skip template selection until the Contract review
              </span>
            </span>
          </label>
        </div>
      </div>
    </div>
  </div>
</div>
`;
