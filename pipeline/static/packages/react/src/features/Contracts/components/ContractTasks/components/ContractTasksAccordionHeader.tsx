import React from 'react';
import Spinner from '../../../../../components/Utils/Spinner/Spinner';
import { ApiHelper } from '../../../../../services/Api/helpers';
import { HeaderXLarge, LabelSlimGray } from '../../../../../styles/typography';
import { ContractTask } from '../../../../../types/contracts';
import { useAsync } from '../../../../../utils/hooks/useAsync';
import { ContractTasksAccordionHeaderWrapper } from '../ContractTasks.styles';
import ContractTasksAccordionHeaderFees from './ContractTasksAccordionHeaderFees';

type Props = {
  title: string;
  currency: string;
  task: ContractTask;
};

export function ContractTasksAccordionHeader({ title, currency, task }: Props) {
  const { isLoading, data: customRateTypes } = useAsync({
    promise: () => ApiHelper.getCached('/custom_rate_types/'),
    onResolve: ({ data }) => data,
  });

  if (isLoading) {
    return <Spinner />;
  }

  return (
    <ContractTasksAccordionHeaderWrapper>
      <HeaderXLarge>{title}</HeaderXLarge>
      <LabelSlimGray>
        <ContractTasksAccordionHeaderFees
          task={task}
          currency={currency}
          customRates={customRateTypes}
        />
      </LabelSlimGray>
    </ContractTasksAccordionHeaderWrapper>
  );
}
