// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`ContractReimbursementBudgetToggle -  > default snapshot should match 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  border-radius: 4px;
}

.emotion-class .MuiToggleButtonGroup-grouped.Mui-selected+.MuiToggleButtonGroup-grouped.Mui-selected {
  border-left: 0;
  margin-left: 0;
}

.emotion-class .MuiToggleButtonGroup-firstButton,
.emotion-class .MuiToggleButtonGroup-middleButton {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.emotion-class .MuiToggleButtonGroup-lastButton,
.emotion-class .MuiToggleButtonGroup-middleButton {
  margin-left: -1px;
  border-left: 1px solid transparent;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.emotion-class .MuiToggleButtonGroup-lastButton.Mui-disabled,
.emotion-class .MuiToggleButtonGroup-middleButton.Mui-disabled {
  border-left: 1px solid transparent;
}

.emotion-class .MuiToggleButton-root {
  font-family: "Open Sans",sans-serif;
  font-size: 12px;
  text-transform: initial;
  font-weight: 600;
  letter-spacing: 1;
  padding: 8px 12px;
  border: 1px solid var(--borders-form-borders-form-border);
  line-height: 22px;
  color: var(--actions-subtle-actions-action-subtle);
}

.emotion-class .MuiToggleButton-root:hover {
  background-color: var(--actions-subtle-actions-action-subtle-background-hover);
  color: var(--actions-subtle-actions-action-subtle-hover);
}

.emotion-class .MuiToggleButton-root.Mui-selected {
  background-color: var(--actions-subtle-actions-action-subtle-background-selected);
  color: var(--actions-subtle-actions-action-subtle-selected);
}

.emotion-class .MuiToggleButton-root.Mui-disabled {
  background-color: var(--actions-subtle-actions-action-subtle-background-disabled);
  color: var(--actions-subtle-actions-action-subtle-disabled);
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.75;
  letter-spacing: 0.02857em;
  text-transform: uppercase;
  border-radius: 4px;
  padding: 11px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  color: rgba(0, 0, 0, 0.54);
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class.Mui-disabled {
  color: rgba(0, 0, 0, 0.26);
  border: 1px solid rgba(0, 0, 0, 0.12);
}

.emotion-class:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
  background-color: rgba(0, 0, 0, 0.04);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-selected {
  color: rgba(0, 0, 0, 0.87);
  background-color: rgba(0, 0, 0, 0.08);
}

.emotion-class.Mui-selected:hover {
  background-color: rgba(0, 0, 0, 0.12);
}

@media (hover: none) {
  .emotion-class.Mui-selected:hover {
    background-color: rgba(0, 0, 0, 0.08);
  }
}

.emotion-class {
  position: relative;
}

.emotion-class input {
  padding-right: 50px;
  max-height: var(--spacing-2xl);
  min-height: var(--spacing-2xl);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  margin-bottom: 5px;
}

.emotion-class>a {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  box-sizing: border-box;
  width: 100%;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--glyphs-forms-glyphs-form-placeholder);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  position: absolute;
  bottom: 14px;
  right: var(--spacing-m);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: row nowrap;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 18px;
  line-height: 30px;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class:hover .checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class:hover>.checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6!important;
}

.emotion-class {
  margin: 1px 0 -1px 6px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  color: #303757;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

<div>
  <div
    class="emotion-class"
  >
    <h3
      class="emotion-class"
    >
      Reimbursement Allowance
    </h3>
    <p
      class="emotion-class"
    >
      An additional amount to cover any extra spendings.
    </p>
    <div
      class="MuiToggleButtonGroup-root MuiToggleButtonGroup-horizontal emotion-class"
      role="group"
    >
      <button
        aria-pressed="false"
        class="MuiButtonBase-root MuiToggleButtonGroup-grouped MuiToggleButtonGroup-groupedHorizontal MuiToggleButton-root MuiToggleButton-sizeMedium MuiToggleButton-standard MuiToggleButtonGroup-grouped MuiToggleButtonGroup-groupedHorizontal MuiToggleButtonGroup-firstButton emotion-class"
        tabindex="0"
        type="button"
        value="no_allowance"
      >
        No allowance
      </button>
      <button
        aria-pressed="true"
        class="MuiButtonBase-root MuiToggleButtonGroup-grouped MuiToggleButtonGroup-groupedHorizontal MuiToggleButton-root Mui-selected MuiToggleButton-sizeMedium MuiToggleButton-standard MuiToggleButtonGroup-grouped MuiToggleButtonGroup-groupedHorizontal MuiToggleButtonGroup-lastButton emotion-class"
        tabindex="0"
        type="button"
        value="general_allowance"
      >
        General allowance
      </button>
    </div>
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      >
        <div>
          <span
            class="emotion-class"
            data-testid="InputLabel_Allowance"
          >
            Allowance
          </span>
        </div>
      </div>
      <div
        class="emotion-class"
      >
        <input
          class="emotion-class"
          type="text"
          value="14999.99"
        />
        <span
          class="emotion-class"
        >
          USD
        </span>
      </div>
      <div
        class="emotion-class"
      >
        <div />
      </div>
    </div>
    <div
      class="emotion-class"
    >
      <h4
        class="emotion-class"
      >
        Allowed Invoice categories
      </h4>
      <button
        class=" emotion-class"
        data-testid="Button_AllowedInvoiceCategoriesSelectAll"
      >
        <span
          class="emotion-class"
        >
          Select / deselect all
        </span>
      </button>
    </div>
    <div
      class="emotion-class"
    >
      <label
        class="emotion-class"
        data-testid="InputCheckbox"
      >
        <span
          class="emotion-class"
        >
          <input
            type="checkbox"
          />
          <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
            classname="checkboxOffIcon css-1dmky3q"
            fill="#D1D6ED"
          />
        </span>
        <span
          class="emotion-class"
        >
          <span
            aria-label=""
            class=" emotion-class"
            data-mui-internal-clone-element="true"
          >
            Child category #1
          </span>
        </span>
      </label>
    </div>
    <div
      class="emotion-class"
    >
      <label
        class="emotion-class"
        data-testid="InputCheckbox"
      >
        <span
          class="emotion-class"
        >
          <input
            checked=""
            type="checkbox"
          />
          <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
            classname="checkboxOnIcon css-1c1jprp"
          />
        </span>
        <span
          class="emotion-class"
        >
          <span
            aria-label=""
            class=" emotion-class"
            data-mui-internal-clone-element="true"
          >
            Work item category #2
          </span>
        </span>
      </label>
    </div>
  </div>
</div>
`;

exports[`ContractReimbursementBudgetToggle -  > no categories snapshot should match 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  border-radius: 4px;
}

.emotion-class .MuiToggleButtonGroup-grouped.Mui-selected+.MuiToggleButtonGroup-grouped.Mui-selected {
  border-left: 0;
  margin-left: 0;
}

.emotion-class .MuiToggleButtonGroup-firstButton,
.emotion-class .MuiToggleButtonGroup-middleButton {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.emotion-class .MuiToggleButtonGroup-lastButton,
.emotion-class .MuiToggleButtonGroup-middleButton {
  margin-left: -1px;
  border-left: 1px solid transparent;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.emotion-class .MuiToggleButtonGroup-lastButton.Mui-disabled,
.emotion-class .MuiToggleButtonGroup-middleButton.Mui-disabled {
  border-left: 1px solid transparent;
}

.emotion-class .MuiToggleButton-root {
  font-family: "Open Sans",sans-serif;
  font-size: 12px;
  text-transform: initial;
  font-weight: 600;
  letter-spacing: 1;
  padding: 8px 12px;
  border: 1px solid var(--borders-form-borders-form-border);
  line-height: 22px;
  color: var(--actions-subtle-actions-action-subtle);
}

.emotion-class .MuiToggleButton-root:hover {
  background-color: var(--actions-subtle-actions-action-subtle-background-hover);
  color: var(--actions-subtle-actions-action-subtle-hover);
}

.emotion-class .MuiToggleButton-root.Mui-selected {
  background-color: var(--actions-subtle-actions-action-subtle-background-selected);
  color: var(--actions-subtle-actions-action-subtle-selected);
}

.emotion-class .MuiToggleButton-root.Mui-disabled {
  background-color: var(--actions-subtle-actions-action-subtle-background-disabled);
  color: var(--actions-subtle-actions-action-subtle-disabled);
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.75;
  letter-spacing: 0.02857em;
  text-transform: uppercase;
  border-radius: 4px;
  padding: 11px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  color: rgba(0, 0, 0, 0.54);
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class.Mui-disabled {
  color: rgba(0, 0, 0, 0.26);
  border: 1px solid rgba(0, 0, 0, 0.12);
}

.emotion-class:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
  background-color: rgba(0, 0, 0, 0.04);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-selected {
  color: rgba(0, 0, 0, 0.87);
  background-color: rgba(0, 0, 0, 0.08);
}

.emotion-class.Mui-selected:hover {
  background-color: rgba(0, 0, 0, 0.12);
}

@media (hover: none) {
  .emotion-class.Mui-selected:hover {
    background-color: rgba(0, 0, 0, 0.08);
  }
}

.emotion-class {
  position: relative;
}

.emotion-class input {
  padding-right: 50px;
  max-height: var(--spacing-2xl);
  min-height: var(--spacing-2xl);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  margin-bottom: 5px;
}

.emotion-class>a {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  box-sizing: border-box;
  width: 100%;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--glyphs-forms-glyphs-form-placeholder);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  position: absolute;
  bottom: 14px;
  right: var(--spacing-m);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: row nowrap;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 18px;
  line-height: 30px;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: var(--actions-action-semifills-semifill-action-disabled);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: default;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: #C5C8D5;
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class {
  color: #C5C8D5;
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: none;
  transition: none;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  background-color: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 6px 16px;
  color: rgb(30, 70, 32);
  border: 1px solid #4caf50;
}

.emotion-class .MuiAlert-icon {
  color: #2e7d32;
}

.emotion-class.MuiAlert-root {
  width: 100%;
  max-width: 100%;
  color: #303757;
  background-color: #FFF6ED;
  border-color: #FFD2A6;
  font-family: "Open Sans",sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  padding: 15px 10px 15px 20px;
  margin: 0;
  box-sizing: border-box;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class.MuiAlert-root .MuiAlert-icon {
  padding: 0;
  margin: 0 4px 0 0;
}

.emotion-class.MuiAlert-root .MuiAlert-message {
  padding: 0;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.emotion-class.MuiAlert-root a {
  color: #465AB6;
}

@media (max-width: 768px) {
  .emotion-class.MuiAlert-root {
    padding: 20px;
  }
}

.emotion-class {
  margin-right: 12px;
  padding: 7px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 22px;
  opacity: 0.9;
}

.emotion-class {
  padding: 8px 0;
  min-width: 0;
  overflow: auto;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class>:first-of-type {
  padding: 0;
}

@media (max-width: 768px) {
  .emotion-class {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: start;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: start;
  }

  .emotion-class div {
    margin: 15px 0 0 0;
  }
}

<div>
  <div
    class="emotion-class"
  >
    <h3
      class="emotion-class"
    >
      Reimbursement Allowance
    </h3>
    <p
      class="emotion-class"
    >
      An additional amount to cover any extra spendings.
    </p>
    <div
      class="MuiToggleButtonGroup-root MuiToggleButtonGroup-horizontal emotion-class"
      role="group"
    >
      <button
        aria-pressed="false"
        class="MuiButtonBase-root MuiToggleButtonGroup-grouped MuiToggleButtonGroup-groupedHorizontal MuiToggleButton-root MuiToggleButton-sizeMedium MuiToggleButton-standard MuiToggleButtonGroup-grouped MuiToggleButtonGroup-groupedHorizontal MuiToggleButtonGroup-firstButton emotion-class"
        tabindex="0"
        type="button"
        value="no_allowance"
      >
        No allowance
      </button>
      <button
        aria-pressed="true"
        class="MuiButtonBase-root MuiToggleButtonGroup-grouped MuiToggleButtonGroup-groupedHorizontal MuiToggleButton-root Mui-selected MuiToggleButton-sizeMedium MuiToggleButton-standard MuiToggleButtonGroup-grouped MuiToggleButtonGroup-groupedHorizontal MuiToggleButtonGroup-lastButton emotion-class"
        tabindex="0"
        type="button"
        value="general_allowance"
      >
        General allowance
      </button>
    </div>
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      >
        <div>
          <span
            class="emotion-class"
            data-testid="InputLabel_Allowance"
          >
            Allowance
          </span>
        </div>
      </div>
      <div
        class="emotion-class"
      >
        <input
          class="emotion-class"
          type="text"
          value="14999.99"
        />
        <span
          class="emotion-class"
        >
          USD
        </span>
      </div>
      <div
        class="emotion-class"
      >
        <div />
      </div>
    </div>
    <div
      class="emotion-class"
    >
      <h4
        class="emotion-class"
      >
        Allowed Invoice categories
      </h4>
      <button
        class=" emotion-class"
        data-testid="Button_AllowedInvoiceCategoriesSelectAll"
        disabled=""
      >
        <span
          class="emotion-class"
          disabled=""
        >
          Select / deselect all
        </span>
      </button>
    </div>
    <div
      class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 MuiAlert-root MuiAlert-colorSuccess MuiAlert-outlinedSuccess MuiAlert-outlined emotion-class"
      role="alert"
      style="--Paper-shadow: none;"
    >
      <div
        class="MuiAlert-icon emotion-class"
      >
        <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m26.3979%2010.4758l40.0796%2033.4659c40.9269%2034.8897%2040.4596%2036.7308%2039.0358%2037.5781c38.5717%2037.8543%2038.0416%2038.0001%2037.5016%2038.0001h10.4982c8.84138%2038.0001%207.49823%2036.6569%207.49823%2035.0001c7.49823%2034.4719%207.63769%2033.953%207.90252%2033.496l21.2242%2010.5059c22.0549%209.07236%2023.8904%208.58362%2025.324%209.41431c25.7666%209.67079%2026.1363%2010.0362%2026.3979%2010.4758zm26.5001%2017.5001h21.5001v21.0001l22.5001%2026.5001h25.5001l26.5001%2021.0001v17.5001zm22.2245%2029.2245c21.7416%2029.7075%2021.5001%2030.2993%2021.5001%2031.0001c21.5001%2031.7008%2021.7416%2032.2927%2022.2245%2032.7756c22.7075%2033.2586%2023.2993%2033.5001%2024.0001%2033.5001c24.7009%2033.5001%2025.2927%2033.2586%2025.7757%2032.7756c26.2586%2032.2927%2026.5001%2031.7008%2026.5001%2031.0001c26.5001%2030.2993%2026.2586%2029.7075%2025.7757%2029.2245c25.2927%2028.7415%2024.7009%2028.5001%2024.0001%2028.5001c23.2993%2028.5001%2022.7075%2028.7415%2022.2245%2029.2245z'/%3e%3c/svg%3e
          classname="css-hdmw1m"
          fill="#FFA64C"
          fontsize="inherit"
          size="30"
        />
      </div>
      <div
        class="MuiAlert-message emotion-class"
      >
        <div
          class="emotion-class"
        >
          <div>
            To provide an allowance, at least one expense category must exist.
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
