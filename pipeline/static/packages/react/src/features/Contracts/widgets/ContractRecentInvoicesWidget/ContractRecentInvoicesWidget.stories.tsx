import { ContractResponseData } from '../../../../types/contracts';
import { with<PERSON>rowserRouter } from '../../../../utils/hoc/withBrowserRouter';
import { getApiMockAdapter } from '../../../../utils/storybook-utils';
import { generatePayments } from '../../../Payments/fixtures/payments';
import { computeAggregations } from '../../../Payments/fixtures/search';
import { CONTRACT_TODAY_FIXED_DATE } from '../../containers/contracts.helpers';
import ContractRecentInvoicesWidget from './ContractRecentInvoicesWidget';
import MockDate from 'mockdate';
import React, { useEffect, useState } from 'react';

export default {
  title: 'Features/Contracts/Widgets/ContractRecentInvoicesWidget',
  component: ContractRecentInvoicesWidget,
};

const Template = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  MockDate.set(CONTRACT_TODAY_FIXED_DATE);
  const mock = getApiMockAdapter();
  mock.onPost(/\/api\/payments\/v2\/search\//gm).reply(() => {
    const hits = generatePayments(2);
    const aggregations = computeAggregations(hits);
    return [
      200,
      {
        hits,
        aggregations,
        total: hits.length,
        page: 0,
      },
    ];
  });

  useEffect(() => {
    setIsLoaded(true);
    return () => {
      mock.reset();
    };
  }, []);

  const Component = withBrowserRouter(ContractRecentInvoicesWidget);
  return isLoaded ? (
    <div style={{ width: '400px' }}>
      <Component
        contract={{ id: 1 } as ContractResponseData}
        isAutomaticInvoicingEnabled
      />
    </div>
  ) : (
    <></>
  );
};

export const Default = () => Template();
