import Spinner from '../../../../components/Utils/Spinner/Spinner';
import ZeroState from '../../../../components/Utils/ZeroState/ZeroState';
import Tools from '../../../../features/TaxInformation/services/TaxInformation';
import i18n from '../../../../i18n/i18n';
import { useAsync } from '../../../../utils/hooks/useAsync';
import { generateTestId } from '../../../../utils/test.utils';
import ContractTaxInformationWidgetRow from '@Worksuite/Features/Contracts/widgets/ContractTaxInformationWidget/ContractTaxInformationWidgetRow';
import {
  WidgetContent,
  WidgetContentRow,
  WidgetSeparator,
} from '@Worksuite/Features/Contracts/widgets/ContractWidgets.styles';
import { WidgetWrapperBaseFlexColumn } from '@Worksuite/Features/Core/widgets/widget.styles';
import { WidgetHeader } from '@Worksuite/Features/Dashboard/components/WidgetHeader/WidgetHeader';
import {
  getFormConfig,
  getProfileItems,
} from '@Worksuite/Features/TaxInformation/utils/TaxInformation.config';
import { mapToFormValues } from '@Worksuite/Features/TaxInformation/utils/TaxInformation.helpers';
import React, { useMemo } from 'react';

const t = (id: string) =>
  i18n.t(`Contracts:ContractWidgets:ContractTaxInformationWidget.${id}`);

const ContractTaxInformationWidget = ({
  vendorSlug,
}: {
  vendorSlug: string;
}) => {
  const { isLoading, data: taxInformation } = useAsync({
    promise: () => Tools.getTaxInformationByVendor(vendorSlug),
    onResolve: ({ data }) => data,
  });

  const { visibleFields } = useMemo(
    () =>
      taxInformation
        ? getFormConfig({
            taxInformation: mapToFormValues(taxInformation),
            isForm: false,
          })
        : { visibleFields: [] },
    [taxInformation]
  );

  const taxInfoItems = useMemo(
    () =>
      visibleFields?.length > 0 && taxInformation
        ? getProfileItems({
            visibleFields,
            taxInformation,
          })
        : [],
    [visibleFields, taxInformation]
  );

  if (isLoading) {
    return <Spinner />;
  }

  const getItem = (key: string) => {
    const item = taxInfoItems.find((item) => item.key === key);
    if (!item || !item?.value) {
      return null;
    }
    return item;
  };

  const rowResidentialCountry = getItem('residential_country');
  const rowState = getItem('state');
  const rowCountry = getItem('country');
  const rowType = getItem('type');
  const rowTaxClassification = getItem('tax_classification');

  const hasUpperSection = rowResidentialCountry || rowState;
  const hasLowerSection = rowCountry || rowType || rowTaxClassification;
  const showSeparator = hasUpperSection && hasLowerSection;
  const isZeroState = !(hasUpperSection || hasLowerSection);

  return (
    <WidgetWrapperBaseFlexColumn
      className="canvas-base"
      {...generateTestId('ContractTaxInformationWidget')}
    >
      <WidgetHeader title={t('Header')} copy={t('HeaderCopy')} />
      <WidgetContent>
        {isZeroState ? (
          <ZeroState variant="variant1" copy={t('ZeroState')} icon="" />
        ) : (
          <>
            <ContractTaxInformationWidgetRow
              item={rowResidentialCountry}
              testId="ResidentialCountry"
            />

            <ContractTaxInformationWidgetRow item={rowState} testId="State" />

            {showSeparator && (
              <WidgetContentRow>
                <WidgetSeparator />
              </WidgetContentRow>
            )}

            <ContractTaxInformationWidgetRow
              item={rowCountry}
              testId="Country"
            />

            <ContractTaxInformationWidgetRow item={rowType} testId="Type" />

            <ContractTaxInformationWidgetRow
              item={rowTaxClassification}
              testId="TaxClassification"
            />
          </>
        )}
      </WidgetContent>
    </WidgetWrapperBaseFlexColumn>
  );
};

export default ContractTaxInformationWidget;
