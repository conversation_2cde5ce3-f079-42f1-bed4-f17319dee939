import * as stories from './ContractRecentInvoicesWidget.stories';
import { composeStories } from '@storybook/react';
import { render, screen } from '@testing-library/react';
import React from 'react';

describe('ContractRecentInvoicesWidget - ', () => {
  test('check with snapshot', async () => {
    const { Default: ContractRecentInvoicesWidget } = composeStories(stories);
    const { container } = render(<ContractRecentInvoicesWidget />);

    expect(await screen.findByText('Invoicing')).toBeVisible();
    expect(container).toMatchSnapshot();
  });
});
