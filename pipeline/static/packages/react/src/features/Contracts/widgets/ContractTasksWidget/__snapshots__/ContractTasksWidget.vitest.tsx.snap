// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`ContractTasksWidget -  > check with snapshot 1`] = `
.emotion-class {
  border-radius: var(--spacing-s);
  box-shadow: var(--box-shadow-1);
  padding: var(--spacing-m);
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  row-gap: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-sizing: border-box;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-column-gap: var(--spacing-s);
  column-gap: var(--spacing-s);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 18px;
  line-height: 30px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--spacing-xs);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 30px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 0;
  padding-bottom: 0;
  padding-left: 0;
  min-width: 30px;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 30px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-s);
  width: 100%;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  cursor: pointer;
}

.emotion-class:hover>div>span:first-of-type {
  color: var(--actions-subtle-actions-action-subtle-hover);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  overflow: hidden;
  row-gap: var(--spacing-2xs);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--actions-basic-actions-action-base)!important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #A6ABBF;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  row-gap: var(--spacing-2xs);
}

.emotion-class {
  height: 20px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
}

.emotion-class>div {
  line-height: 17px;
  padding: 0 var(--spacing-xs);
}

.emotion-class {
  display: inline-block;
  vertical-align: top;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #FFFFFF;
  background-color: #A6ABBF;
  font-family: "Montserrat",sans-serif;
  font-weight: 700;
  font-size: 10px;
  line-height: 20px;
  letter-spacing: 0.5px;
  text-align: center;
  text-transform: uppercase;
  border-radius: var(--spacing-2xs);
  margin: 0;
  padding: 0 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-class {
  text-align: right;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  font-weight: 700;
  color: #303757;
  margin-right: 5px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #303757;
}

.emotion-class {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  position: relative;
}

.emotion-class::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--borders-encapsulation-separator-divider);
  top: 50%;
}

.emotion-class {
  display: inline-block;
  vertical-align: top;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #FFFFFF;
  background-color: #303757;
  font-family: "Montserrat",sans-serif;
  font-weight: 700;
  font-size: 10px;
  line-height: 20px;
  letter-spacing: 0.5px;
  text-align: center;
  text-transform: uppercase;
  border-radius: var(--spacing-2xs);
  margin: 0;
  padding: 0 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-class {
  display: inline-block;
  vertical-align: top;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #FFFFFF;
  background-color: #FFA64C;
  font-family: "Montserrat",sans-serif;
  font-weight: 700;
  font-size: 10px;
  line-height: 20px;
  letter-spacing: 0.5px;
  text-align: center;
  text-transform: uppercase;
  border-radius: var(--spacing-2xs);
  margin: 0;
  padding: 0 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-class {
  display: inline-block;
  vertical-align: top;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #FFFFFF;
  background-color: #A400FF;
  font-family: "Montserrat",sans-serif;
  font-weight: 700;
  font-size: 10px;
  line-height: 20px;
  letter-spacing: 0.5px;
  text-align: center;
  text-transform: uppercase;
  border-radius: var(--spacing-2xs);
  margin: 0;
  padding: 0 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div>
  <div
    style="width: 400px;"
  >
    <div
      class="canvas-base emotion-class"
      data-testid="ContractTasksWidget"
    >
      <div>
        <div
          class="emotion-class"
        >
          <div
            style="display: flex;"
          >
            <h4
              class="emotion-class"
              style="flex-grow: 1;"
            >
              Contract Tasks
            </h4>
          </div>
          <button
            class=" emotion-class"
            data-testid="ContractTasksWidget_ViewAll"
          >
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m30.5%2010v7h41v17.5h38v12.1213l29.2573%2020.864l27.136%2018.7426l35.8786%2010h30.5zm27%2011h13.5c11.8431%2011%2010.5%2012.3431%2010.5%2014v34.5c10.5%2036.1569%2011.8431%2037.5%2013.5%2037.5h34c35.6569%2037.5%2037%2036.1569%2037%2034.5v21h34v34.5h13.5v14h27v11z'%20/%3e%3c/svg%3e
              classname="css-12ecxzt"
              size="20"
            />
          </button>
        </div>
        <p
          class="emotion-class"
          data-testid="WidgetHeader_Description"
          style="color: var(--glyphs-basic-glyphs-subdued);"
        >
          An overview of contracted tasks.
        </p>
      </div>
      <div
        class="emotion-class"
      >
        <div
          class="emotion-class"
          data-testid="ContractTasksWidget_TaskLink"
        >
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_TaskName"
            >
              temp 106
            </span>
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_ProjectName"
            >
              task 106
            </span>
          </div>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                data-testid="Chip"
              >
                Draft
              </div>
            </div>
            <div
              class="emotion-class"
              data-testid="Currency_TaskFee"
            >
              <span
                class="emotion-class"
              >
                654.00
              </span>
              <span
                class="emotion-class"
              >
                USD
              </span>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
          style="padding: var(--spacing-xs);"
        />
        <div
          class="emotion-class"
          data-testid="ContractTasksWidget_TaskLink"
        >
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_TaskName"
            >
              Groszek Groszkowy Task 1
            </span>
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_ProjectName"
            >
              Project 1
            </span>
          </div>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                data-testid="Chip"
              >
                Completed
              </div>
            </div>
            <div
              class="emotion-class"
              data-testid="Currency_TaskFee"
            >
              <span
                class="emotion-class"
              >
                1,500.00
              </span>
              <span
                class="emotion-class"
              >
                USD
              </span>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
          style="padding: var(--spacing-xs);"
        />
        <div
          class="emotion-class"
          data-testid="ContractTasksWidget_TaskLink"
        >
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_TaskName"
            >
              Groszek Groszkowy Task 1 Groszek Groszkowy Task 1
            </span>
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_ProjectName"
            >
              Project 1
            </span>
          </div>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                data-testid="Chip"
              >
                Completed
              </div>
            </div>
            <div
              class="emotion-class"
              data-testid="Currency_TaskFee"
            >
              <span
                class="emotion-class"
              >
                10,000.00
              </span>
              <span
                class="emotion-class"
              >
                USD
              </span>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
          style="padding: var(--spacing-xs);"
        />
        <div
          class="emotion-class"
          data-testid="ContractTasksWidget_TaskLink"
        >
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_TaskName"
            >
              completed completed completed
            </span>
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_ProjectName"
            >
              Project 1
            </span>
          </div>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                data-testid="Chip"
              >
                Completed
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
          style="padding: var(--spacing-xs);"
        />
        <div
          class="emotion-class"
          data-testid="ContractTasksWidget_TaskLink"
        >
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_TaskName"
            >
              pending pending
            </span>
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_ProjectName"
            >
              Project 1
            </span>
          </div>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                data-testid="Chip"
              >
                Completed
              </div>
            </div>
            <div
              class="emotion-class"
              data-testid="Currency_TaskFee"
            >
              <span
                class="emotion-class"
              >
                500.00
              </span>
              <span
                class="emotion-class"
              >
                USD
              </span>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
          style="padding: var(--spacing-xs);"
        />
        <div
          class="emotion-class"
          data-testid="ContractTasksWidget_TaskLink"
        >
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_TaskName"
            >
              Task 01
            </span>
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_ProjectName"
            >
              Project 1
            </span>
          </div>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                data-testid="Chip"
              >
                Draft
              </div>
            </div>
            <div
              class="emotion-class"
              data-testid="Currency_TaskFee"
            >
              <span
                class="emotion-class"
              >
                2,000.00
              </span>
              <span
                class="emotion-class"
              >
                USD
              </span>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
          style="padding: var(--spacing-xs);"
        />
        <div
          class="emotion-class"
          data-testid="ContractTasksWidget_TaskLink"
        >
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_TaskName"
            >
              restety timesheety
            </span>
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_ProjectName"
            >
              TT ostatnie szlify
            </span>
          </div>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                data-testid="Chip"
              >
                Draft
              </div>
            </div>
            <div
              class="emotion-class"
              data-testid="Currency_TaskFee"
            >
              <span
                class="emotion-class"
              >
                100.00
              </span>
              <span
                class="emotion-class"
              >
                USD
              </span>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
          style="padding: var(--spacing-xs);"
        />
        <div
          class="emotion-class"
          data-testid="ContractTasksWidget_TaskLink"
        >
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_TaskName"
            >
              fee mand, non edit hide
            </span>
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_ProjectName"
            >
              Waluta test
            </span>
          </div>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                data-testid="Chip"
              >
                Pending
              </div>
            </div>
            <div
              class="emotion-class"
              data-testid="Currency_TaskFee"
            >
              <span
                class="emotion-class"
              >
                2,000.00
              </span>
              <span
                class="emotion-class"
              >
                USD
              </span>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
          style="padding: var(--spacing-xs);"
        />
        <div
          class="emotion-class"
          data-testid="ContractTasksWidget_TaskLink"
        >
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_TaskName"
            >
              asd
            </span>
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_ProjectName"
            >
              123123
            </span>
          </div>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                data-testid="Chip"
              >
                Pending
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
          style="padding: var(--spacing-xs);"
        />
        <div
          class="emotion-class"
          data-testid="ContractTasksWidget_TaskLink"
        >
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_TaskName"
            >
              z CF
            </span>
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_ProjectName"
            >
              123123
            </span>
          </div>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                data-testid="Chip"
              >
                Accepted
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
          style="padding: var(--spacing-xs);"
        />
        <div
          class="emotion-class"
          data-testid="ContractTasksWidget_TaskLink"
        >
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_TaskName"
            >
              Task name do testow template
            </span>
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_ProjectName"
            >
              dodaj nowy
            </span>
          </div>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                data-testid="Chip"
              >
                Accepted
              </div>
            </div>
            <div
              class="emotion-class"
              data-testid="Currency_TaskFee"
            >
              <span
                class="emotion-class"
              >
                10,000.00
              </span>
              <span
                class="emotion-class"
              >
                USD
              </span>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
          style="padding: var(--spacing-xs);"
        />
        <div
          class="emotion-class"
          data-testid="ContractTasksWidget_TaskLink"
        >
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_TaskName"
            >
              z CF
            </span>
            <span
              class="emotion-class"
              data-testid="ContractTasksWidget_ProjectName"
            >
              2411 new
            </span>
          </div>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                data-testid="Chip"
              >
                Pending
              </div>
            </div>
            <div
              class="emotion-class"
              data-testid="Currency_TaskFee"
            >
              <span
                class="emotion-class"
              >
                150.00
              </span>
              <span
                class="emotion-class"
              >
                USD
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
