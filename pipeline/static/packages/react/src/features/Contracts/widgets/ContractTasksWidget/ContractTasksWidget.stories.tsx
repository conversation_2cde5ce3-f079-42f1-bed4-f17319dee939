import contractSOW from '../../../../fixtures/api/contracts/contract_SOW.json';
import tasksApi from '../../../../fixtures/api/tasks/page1.json';
import { withBrowserRouter } from '../../../../utils/hoc/withBrowserRouter';
import { getApiMockAdapter } from '../../../../utils/storybook-utils';
import ContractTasksWidget from './ContractTasksWidget';
import React, { useEffect, useState } from 'react';

export default {
  title: 'Features/Contracts/Widgets/ContractTasksWidget',
  component: ContractTasksWidget,
};

const Template = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const mock = getApiMockAdapter();
  mock.onGet(/\/api\/(v\/)?tasks\//gm).reply(200, tasksApi);
  useEffect(() => {
    setIsLoaded(true);
    return () => {
      mock.reset();
    };
  }, []);

  const Component = withBrowserRouter(ContractTasksWidget);
  return isLoaded ? (
    <div style={{ width: '400px' }}>
      <Component contract={contractSOW} currency="USD" />
    </div>
  ) : (
    <></>
  );
};

export const Default = () => Template();
