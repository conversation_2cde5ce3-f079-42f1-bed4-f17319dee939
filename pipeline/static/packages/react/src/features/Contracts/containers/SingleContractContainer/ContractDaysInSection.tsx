import FormContainer from '../../../../components/Containers/FormContainer/FormContainer';
import { FormContainerItemType } from '../../../../components/Containers/FormContainer/FormContainer.types';
import InputLabel from '../../../../components/FormElements/InputLabel/InputLabel';
import FormPreviewContentTextArea from '../../../../components/PreviewForm/ContentElements/FormPreviewContentTextArea';
import i18n from '../../../../i18n/i18n';
import { Contract } from '../../../../types/contracts';
import { formatDateWithUserSettings } from '../../../../utils/date';
import { getDifferenceToDateLabel } from '../../../../utils/time.utils';
import { FlexWrapper } from '@Worksuite/Features/Contracts/containers/ContractWizardContainer/ContractWizardContainer.styles';
import { HeaderXLargeTopSpacingM } from '@Worksuite/Features/Contracts/containers/contracts.styles';
import React, { useMemo } from 'react';

const t = (id: string) => i18n.t(`Contracts:${id}`);

const ContractDaysInSection = ({
  contract,
  variant = 'scheduled',
}: {
  contract: Contract;
  variant?: 'scheduled' | 'ending_soon';
}) => {
  const dateTo =
    variant === 'ending_soon' ? contract.end_date : contract.start_date;
  const daysInValues = useMemo(() => getDifferenceToDateLabel(dateTo), []);
  const contractDaysInItems: FormContainerItemType[] = [
    {
      id: `contract_${variant}_in`,
      elements: [
        {
          id: `contract_${variant}_in_label`,
          content: <InputLabel label={t(`ContractDaysIn.${variant}.DaysIn`)} />,
          columnSize: 4,
        },
        {
          id: `contract_${variant}_in_value`,
          content: <FormPreviewContentTextArea value={daysInValues} />,
          columnSize: 8,
        },
      ],
      variant: 'hasDivider',
    },
    {
      id: `contract_${variant}_in_date`,
      elements: [
        {
          id: `contract_${variant}_in_date_label`,
          content: <InputLabel label={t(`ContractDaysIn.${variant}.Date`)} />,
          columnSize: 4,
        },
        {
          id: `contract_${variant}_in_date_value`,
          content: (
            <FormPreviewContentTextArea
              value={formatDateWithUserSettings(dateTo)}
            />
          ),
          columnSize: 8,
        },
      ],
    },
  ];

  return (
    <FlexWrapper>
      <HeaderXLargeTopSpacingM>
        {t(`ContractDaysIn.${variant}.Title`)}
      </HeaderXLargeTopSpacingM>
      <FormContainer items={contractDaysInItems} spacing={2} variant="info" />
    </FlexWrapper>
  );
};

export default ContractDaysInSection;
