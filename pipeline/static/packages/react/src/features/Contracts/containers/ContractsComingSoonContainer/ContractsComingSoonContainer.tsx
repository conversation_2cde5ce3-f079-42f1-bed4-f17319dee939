import Button from '../../../../components/FormElements/Button/Button';
import i18n from '../../../../i18n/i18n';
import Contracts from '../../../../services/Api/Contracts';
import { HeaderH2 } from '../../../../styles/typography';
import { useMediaQueries } from '../../../../utils/hooks/useMediaQueries';
import { safeMessage } from '../../../../utils/html';
import { SessionUserVariableStorage } from '../../../../utils/session-user-variable-storage';
import ComingSoonInfoBox from './ComingSoonInfoBox';
import * as Styles from './ContractsComingSoonContainer.styles';
import { Grid } from '@mui/material';
import React, { useState } from 'react';
import { generateTestId } from '../../../../utils/test.utils';

const t = (id: string) => i18n.t(`Contracts:ComingSoon.${id}`);

const getTestId = (id: string) =>
  generateTestId(id, 'ContractsComingSoonContainer');

const ContractsComingSoonContainer = ({
  calendlyOnClick = null,
}: {
  calendlyOnClick: (event) => void;
}) => {
  const sessionStorage = SessionUserVariableStorage(
    'contracts_early_access',
    false
  );
  const getEarlyAccessLabel = () =>
    sessionStorage.get() ? t('Buttons.InTouch') : t('Buttons.EarlyAccess');
  const [earlyAccessLabel, setEarlyAccessLabel] = useState(
    getEarlyAccessLabel()
  );
  const onEarlyAccessClick = async () => {
    if (!sessionStorage.get()) {
      try {
        await Contracts.requestEarlyAccess();
        sessionStorage.set(true);
        setEarlyAccessLabel(getEarlyAccessLabel());
      } catch (e) {
        //
      }
    }
  };

  const { isMobile, isTablet, isLaptop } = useMediaQueries();
  const isUnder1200pxWidth = isMobile || isTablet || isLaptop;

  return (
    <Styles.OuterWrapper>
      <Styles.Wrapper>
        <Grid container>
          <Grid item xs={12} lg={6} order={isUnder1200pxWidth ? 1 : 2}>
            <Styles.MapWrapper>
              <img
                src="/img/contracts-soon-map-v1.png"
                alt="contracts-soon-map-v1.png"
                {...getTestId('MapImg')}
              />
            </Styles.MapWrapper>
          </Grid>
          <Grid item xs={12} lg={6} order={isUnder1200pxWidth ? 2 : 1}>
            <Styles.IntroductionBox>
              <Styles.StyledLabel {...getTestId('Title')}>
                {t('Title')}
              </Styles.StyledLabel>
              <HeaderH2>{t('Subtitle')}</HeaderH2>
              <div>
                <Styles.StyledBody
                  dangerouslySetInnerHTML={{
                    __html: safeMessage(t('Description1')),
                  }}
                />
              </div>
              <div>
                <Styles.StyledBody>{t('Description2')}</Styles.StyledBody>
              </div>
              <Styles.ButtonWrapper>
                {calendlyOnClick && false && (
                  <Button
                    label={t('Buttons.BookDemo')}
                    onClick={calendlyOnClick}
                  />
                )}
                {sessionStorage.get() && (
                  <Styles.StyledLabelInTouch>
                    {earlyAccessLabel}
                  </Styles.StyledLabelInTouch>
                )}
                {!sessionStorage.get() && (
                  <Button
                    label={earlyAccessLabel}
                    onClick={onEarlyAccessClick}
                  />
                )}
              </Styles.ButtonWrapper>
            </Styles.IntroductionBox>
          </Grid>
        </Grid>

        <Grid container>
          <ComingSoonInfoBox
            iconString="EngageGlobalIcon"
            title={t('InfoBoxes.Box1Title')}
            description={t('InfoBoxes.Box1Description')}
          />
          <ComingSoonInfoBox
            iconString="HireGlobalIcon"
            title={t('InfoBoxes.Box2Title')}
            description={t('InfoBoxes.Box2Description')}
          />
          <ComingSoonInfoBox
            iconString="PaymentGlobalIcon"
            title={t('InfoBoxes.Box3Title')}
            description={t('InfoBoxes.Box3Description')}
          />
        </Grid>
      </Styles.Wrapper>
    </Styles.OuterWrapper>
  );
};

export default ContractsComingSoonContainer;
