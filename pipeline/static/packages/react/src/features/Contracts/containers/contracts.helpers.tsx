import ContractScheduleTooltip from '@Worksuite/Features/Contracts/components/ContractScheduleTooltip/ContractScheduleTooltip';
import Currency from '@Worksuite/Features/Core/components/Currency/Currency';
import ToggleButtonGroup from '@Worksuite/Features/Core/components/ToggleButtonGroup/ToggleButtonGroup';
import {
  differenceInYears,
  isAfter,
  isBefore,
  isEqual,
  isValid,
  parseISO,
  startOfDay,
} from 'date-fns';
import { MutableRefObject, ReactNode, useState } from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';
import Alert from '../../../components/Alert/Alert';
import { BulkAction } from '../../../components/BulkActions';
import { FormContainerItemType } from '../../../components/Containers/FormContainer/FormContainer.types';
import { CustomField } from '../../../components/CustomFields/CustomFields';
import { reduceCustomFields } from '../../../components/CustomFields/custom-fields.helpers';
import { SelectValue } from '../../../components/FormElements/AutoComplete/AutoComplete';
import InputDatePicker from '../../../components/FormElements/InputDatePicker/InputDatePicker';
import InputSelect from '../../../components/FormElements/InputSelect/InputSelect';
import InputText from '../../../components/FormElements/InputText/InputText';
import InputTextArea from '../../../components/FormElements/InputTextArea/InputTextArea';
import SearchableUserSelect, {
  UserSearchCallback,
} from '../../../components/FormElements/UserSelect/SearchableUserSelect';
import {
  ADDITIONAL_OPTION_ID,
  DefaultValueType,
} from '../../../components/FormElements/UserSelect/UserSelect';
import VendorListSelect from '../../../components/FormElements/VendorListSelect/VendorListSelect';
import {
  createPreviewFormItems,
  getPreviewCustomFields,
} from '../../../components/PreviewForm/PreviewForm.helpers';
import { PreviewFormField } from '../../../components/PreviewForm/PreviewForm.types';
import { SearchParams } from '../../../components/Search/ElasticSearch/ElasticSearch.types';
import SelectVendorType from '../../../components/Vendors/SelectVendorType/SelectVendorType';
import { getVendorName } from '../../../components/Vendors/vendors.helpers';
import i18n from '../../../i18n/i18n';
import { LineItemsCategory, TaxRate } from '../../../services/Api/Payments';
import Vendors from '../../../services/Api/Vendors';
import { getReduxSessionUser } from '../../../services/Reducers/User.reducer.helper';
import { tenantHasFeature } from '../../../services/Shortlist/feature-flags';
import { HeaderH3 } from '../../../styles/typography';
import {
  AcceptanceType,
  ChangeRequestType,
  Contract,
  ContractAcceptance,
  ContractAcceptanceSigner,
  ContractBudget,
  ContractChangeRequestSigner,
  ContractChangeRequestStatusType,
  ContractInvoiceCurrencyField,
  ContractInvoicingTemplate,
  ContractPaymentPeriodType,
  ContractPaymentSettings,
  ContractPaymentTemplate,
  ContractPaymentsSchedule,
  ContractRequestData,
  ContractResponseData,
  ContractSearchResponseData,
  ContractStatusType,
  ContractTask,
  ContractTaskTemplate,
  ContractTemplate,
  ContractTemplateStatusType,
  ContractType,
  ContractTypeVariety,
  FieldsConfigType,
  PaymentPrefix,
  Schedule,
  SchedulerWeekDaysType,
} from '../../../types/contracts';
import { ErrorObject } from '../../../types/custom-types';
import { SimpleUser } from '../../../types/user';
import { Vendor } from '../../../types/vendor';
import { formatCurrency, noop } from '../../../utils';
import {
  DAYS_OF_WEEK_LONG,
  formatDateWithUserSettings,
  getOrdinal,
} from '../../../utils/date';
import { FORM_RULE_REQUIRED } from '../../../utils/forms';
import { removeAllHtmlTags, safeMessage } from '../../../utils/html';
import {
  cloneDeep,
  deleteProps,
  isEmpty,
  removeEmptyProps,
} from '../../../utils/other';
import { getActionsBase } from '../../../utils/other/item-actions';
import { snakeToTitleCase } from '../../../utils/string';
import { generateTestId } from '../../../utils/test.utils';
import { isValidEmail } from '../../../utils/validators/other';
import { isTaskNew } from '../components/ContractTasks/ContractTasks.helpers';
import {
  FlexRowInputWrapper,
  InputStyled,
} from './ContractTemplateEditorContainer/ContractTemplateEditorContainer.styles';
import {
  FlexWrapper,
  InputRowWrapper,
} from './ContractWizardContainer/ContractWizardContainer.styles';
import {
  OccurrencesWrapper,
  PaymentsFormNextCycleRateWrapper,
  StyledPaymentsFormNextCycleRate,
} from './SingleContractContainer/SingleContractContainer.styles';
import { ContractAction } from './contract-action.types';
import { contractActionBlueprints } from './contract-actions';
import { ContractTemplateAction } from './contract-template-action.types';
import { contractTemplateActionBlueprints } from './contract-template-actions';
import {
  contractAutomationTriggerOptions,
  contractPaymentMonthlySpans,
  contractPaymentPeriodOptions,
  contractVariableDurationOptions,
  shortWeekDays,
} from './contracts.config';
import { PaddingBottomSpacingM } from './contracts.styles';

const t = (id: string, params?: Record<string, string | number>) =>
  i18n.t(`Contracts:${id}`, params);

export const getAutomationTriggerOptions = (isEndingSoonAvailable: boolean) =>
  contractAutomationTriggerOptions
    .filter((option) =>
      isEndingSoonAvailable ? true : option !== 'on_contract_ending_soon'
    )
    .map((option) => ({
      key: option,
      text: t(`ContractTriggerCondition.${option}`),
    }));

export const getDefaultTeammates = (
  contractSigners: ContractAcceptanceSigner[] | ContractChangeRequestSigner[],
  users: SimpleUser[] = []
) => {
  const userSelectOptions: SelectValue[] = [];
  const defaultPartner: DefaultValueType = {
    key: 'partner',
    text: t('Partner'),
    canRemove: false,
  };

  if (contractSigners.length === 0) {
    return [defaultPartner];
  }

  contractSigners.forEach((contractSigner) => {
    if (contractSigner.type === 'partner') {
      userSelectOptions.push(defaultPartner);
    } else if (contractSigner?.user) {
      const user = users.find((user) => user.id === contractSigner.user);
      user &&
        userSelectOptions.push({
          key: contractSigner.user,
          text: getVendorName(user),
        });
    }
  });

  return userSelectOptions;
};

export const getContractPaymentPeriodOptions = () =>
  contractPaymentPeriodOptions.map((period) => ({
    text: t(`ContractPayments.Periods.${period}`),
    key: period,
  }));

export const getContractPaymentMonthlySpans = () =>
  contractPaymentMonthlySpans.map((span) => ({
    text: t(`ContractPayments.TimeSpans.${span}`),
    key: span,
  }));

export const getContractWeeklyPaymentSpans = (
  period: ContractPaymentPeriodType
) =>
  shortWeekDays.map((day, index) => ({
    text: `${
      period === 'biweekly' ? `${t('ContractPayments.BiweeklyPrefix')} ` : ''
    }${DAYS_OF_WEEK_LONG[index]}`,
    key: day,
  }));

export const getPaymentDayOfTheMonthOptions = () => {
  const options = [...Array(31)].map((_, index) => index + 1);
  return options.map((day) => ({
    text: `${day === 31 ? t('ContractPayments.last') : getOrdinal(day)} ${t(
      'ContractPayments.day'
    )} ${t('ContractPayments.ofTheMonth')}`,
    key: day,
  }));
};

export const getPaymentDayOfTheWeekOptions = () =>
  shortWeekDays.map((day, index) => ({
    text: `${t('ContractPayments.last')} ${DAYS_OF_WEEK_LONG[index]} ${t(
      'ContractPayments.ofTheMonth'
    )}`,
    key: day,
  }));

export const getContractAcceptanceTypeOptions = () => {
  const options: AcceptanceType[] = ['agreement', 'acceptance'];
  return options.map((contractAcceptanceType) => ({
    label: t(`ContractAcceptanceType.${contractAcceptanceType}.label`),
    description: t(
      `ContractAcceptanceType.${contractAcceptanceType}.description`
    ),
    value: contractAcceptanceType,
  }));
};

export const getContractChangeRequestTypeOptions = () => {
  const changeRequestTypes: ChangeRequestType[] = [
    'signed_annex_document',
    'accept_in_worksuite',
  ];
  return changeRequestTypes.map((contractChangeRequestType) => ({
    label: t(`ContractChangeRequestType.${contractChangeRequestType}.label`),
    description: t(
      `ContractChangeRequestType.${contractChangeRequestType}.description`
    ),
    value: contractChangeRequestType,
    disabled: contractChangeRequestType === 'signed_annex_document',
  }));
};

const getContractDurationOptions = (
  isEndDateMandatory: boolean,
  allowFixedDates: boolean,
  hasWorkflowWithAor: boolean
) => {
  const excludedForAor = ['indefinite', 'two_years', 'specific_date'];
  return contractVariableDurationOptions
    .filter((option) => (isEndDateMandatory ? option !== 'indefinite' : true))
    .filter((option) => (allowFixedDates ? true : option !== 'specific_date'))
    .filter((option) =>
      hasWorkflowWithAor ? !excludedForAor.includes(option) : true
    )
    .map((option) => ({
      key: option,
      text: t(`ContractDuration.${option}`),
    }));
};

const getPartnerId = (partner: Contract['partner']): number[] => {
  const isPartnerObject = typeof partner === 'object';
  const partnerId = isPartnerObject ? (partner as Vendor)?.id : partner;
  return partner ? [partnerId] : undefined;
};

export const useContractDetailsFixedFields = ({
  disabled = false,
  formProps,
  isEndDateMandatory,
  scrollRefs,
  searchCallback,
  allowVariableDates = false,
  allowFixedDates = true,
  fieldsConfig = {},
  contractType,
  hasWorkflowWithAor = false,
  isChangeRequest = false,
}: {
  disabled?: boolean;
  formProps: UseFormReturn<ContractRequestData>;
  isEndDateMandatory: boolean;
  scrollRefs?: MutableRefObject<any>;
  searchCallback?: UserSearchCallback;
  allowVariableDates?: boolean;
  allowFixedDates?: boolean;
  fieldsConfig?: FieldsConfigType;
  contractType?: ContractType;
  hasWorkflowWithAor?: boolean;
  isChangeRequest?: boolean;
}): FormContainerItemType[] => {
  const [foundVendorId, setFoundVendorId] = useState<number>(null);
  if (foundVendorId && foundVendorId === formProps.getValues('partner')) {
    setFoundVendorId(null);
  }
  const [vendorGroupSelected, setVendorGroupSelected] = useState(false);
  const [showNotOnboardedAlert, setShowNotOnboardedAlert] = useState(false);
  const showDatesToggle = allowVariableDates && allowFixedDates;
  const isFixedRateContract = contractType === 'FixedRate';
  const getModalHelpers = async () =>
    await import('../../../components/Utils/Modals/modal.helpers');
  const shouldDatesToggleChange = async (value: string) => {
    const { openModal } = await getModalHelpers();
    if (
      ['SOW', 'TimeAndMaterial'].includes(contractType) &&
      value === 'variable_dates'
    ) {
      return openModal({
        type: 'message_modal',
        header: t('VariableDatesModal.Header'),
        message: t('VariableDatesModal.Message'),
        confirmButtonLabel: t('VariableDatesModal.ConfirmButton'),
        cancelButtonLabel: i18n.t('common:Form.Cancel'),
      })
        .then(() => true)
        .catch(() => false);
    } else {
      return true;
    }
  };
  const canAddNewVendor =
    !tenantHasFeature('tasks_requires_fully_onboarded_vendors') &&
    !tenantHasFeature('tasks_requires_compliant_vendors');

  const { isStaff } = getReduxSessionUser();
  const isEndDateDisabled =
    hasWorkflowWithAor && formProps.getValues('status') === 'live' && !isStaff;
  const showPastInvoicesWarning =
    isFixedRateContract &&
    !isChangeRequest &&
    isBefore(
      parseISO(formProps.getValues('start_date')),
      startOfDay(new Date())
    );

  return [
    {
      id: 'fixed-fields-name',
      elements: [
        {
          id: 'name',
          content: (
            <InputStyled>
              <Controller
                render={({ field, fieldState }) => (
                  <div ref={scrollRefs?.current[field.name]}>
                    <InputText
                      disabled={disabled || fieldsConfig[field.name]?.disabled}
                      label={t('Fields.ContractName')}
                      placeholder={t('Fields.ContractNamePlaceholder')}
                      additionalInfo={fieldsConfig[field.name]?.description}
                      defaultValue={field.value}
                      onChange={field.onChange}
                      errors={
                        fieldState.error ? [fieldState?.error?.message] : []
                      }
                      testId="ContractName"
                      maxLength={200}
                    />
                  </div>
                )}
                rules={FORM_RULE_REQUIRED}
                name="name"
                control={formProps.control}
              />
            </InputStyled>
          ),
        },
      ],
    },
    {
      id: 'fixed-fields-description',
      elements: [
        {
          id: 'description',
          content: (
            <InputStyled>
              <Controller
                render={({ field }) => (
                  <div ref={scrollRefs?.current[field.name]}>
                    <InputTextArea
                      disabled={disabled}
                      isOptional
                      label={t('Fields.ContractDescription')}
                      placeholder={t('Fields.ContractDescriptionPlaceholder')}
                      defaultValue={field.value ?? ''}
                      onChange={field.onChange}
                      testId="ContractDescription"
                    />
                  </div>
                )}
                name="description"
                control={formProps.control}
              />
            </InputStyled>
          ),
        },
      ],
    },
    ...(!fieldsConfig['partner']?.disabled
      ? [
          {
            id: 'fixed-fields-partner-selection',
            elements: [
              {
                id: 'partner-selection-toggle',
                content: (
                  <FlexWrapper>
                    <HeaderH3>{t('Fields.PartnerSelection')}</HeaderH3>
                    <ToggleButtonGroup
                      options={[
                        {
                          text: t('Fields.PartnerSelectionSingle'),
                          key: 'single',
                        },
                        {
                          text: t('Fields.PartnerSelectionList'),
                          key: 'list',
                        },
                      ]}
                      defaultValue={
                        formProps.getValues('vendor_group') ? 'list' : 'single'
                      }
                      onChange={(data) => {
                        if (data) {
                          setVendorGroupSelected(data === 'list');
                          if (data === 'list') {
                            formProps.setValue('external_partner', null);
                            formProps.setValue('partner', null);
                          } else {
                            formProps.setValue('vendor_group', null);
                          }
                        }
                      }}
                      disabled={disabled}
                    />
                  </FlexWrapper>
                ),
              },
            ],
          },
        ]
      : []),
    ...(!formProps.getValues('vendor_group') && !vendorGroupSelected
      ? [
          {
            id: 'fixed-fields-partner',
            elements: [
              {
                id: 'partner',
                content: (
                  <Controller
                    render={({ field, fieldState }) => {
                      const userSelectKey =
                        foundVendorId && foundVendorId === field.value
                          ? `UserSelect_${field.value}`
                          : 'UserSelect';

                      const getDefaultValue = () => {
                        if (formProps.getValues('external_partner.email')) {
                          return ADDITIONAL_OPTION_ID;
                        } else {
                          return field.value;
                        }
                      };

                      return (
                        <FlexWrapper ref={scrollRefs?.current[field.name]}>
                          <SearchableUserSelect
                            key={userSelectKey}
                            disabled={
                              disabled || fieldsConfig[field.name]?.disabled
                            }
                            placeholder={
                              fieldsConfig[field.name]?.placeholder ??
                              t('Fields.PartnerPlaceholder')
                            }
                            label={t('Fields.PartnerLabel')}
                            additionalInfo={
                              fieldsConfig[field.name]?.description ??
                              t('Fields.PartnerAdditionalInfo')
                            }
                            defaultValues={getPartnerId(getDefaultValue())}
                            errors={
                              fieldState.error
                                ? [fieldState?.error?.message]
                                : []
                            }
                            onChange={(data, users) => {
                              if (
                                tenantHasFeature(
                                  'tasks_requires_fully_onboarded_vendors'
                                )
                              ) {
                                setShowNotOnboardedAlert(
                                  users.some(
                                    ({ onboarded }) =>
                                      onboarded !== 'fully-onboarded'
                                  )
                                );
                              }
                              if (isEmpty(data)) {
                                formProps.setValue('external_partner', null);
                              }
                              field.onChange(...data);
                            }}
                            searchCallback={searchCallback}
                            testId="ContractPartner"
                            additionalOption={
                              canAddNewVendor && {
                                label: t('Fields.PartnerAddNew'),
                              }
                            }
                            renderUserStatus
                          />
                          {showNotOnboardedAlert && (
                            <Alert
                              message={i18n.t(
                                'Contracts:Fields.PartnerNotOnboarded'
                              )}
                              tag="alert-danger"
                              size="large"
                            />
                          )}
                        </FlexWrapper>
                      );
                    }}
                    rules={FORM_RULE_REQUIRED}
                    name="partner"
                    control={formProps.control}
                  />
                ),
              },
            ],
          },
        ]
      : []),
    ...(formProps.getValues('partner') === ADDITIONAL_OPTION_ID
      ? [
          {
            id: 'fixed-fields-new-partner',
            elements: [
              {
                id: 'fixed-fields-new-partner-name',
                content: (
                  <>
                    <PaddingBottomSpacingM>
                      <FlexRowInputWrapper>
                        <InputStyled>
                          <Controller
                            render={({ field, fieldState }) => (
                              <div ref={scrollRefs?.current[field.name]}>
                                <InputText
                                  disabled={disabled}
                                  label={t('Fields.PartnerAddNewName')}
                                  placeholder={t(
                                    'Fields.PartnerAddNewNamePlaceholder'
                                  )}
                                  defaultValue={field.value}
                                  onChange={field.onChange}
                                  errors={
                                    fieldState.error
                                      ? [fieldState?.error?.message]
                                      : []
                                  }
                                  testId="ContractNewPartnerName"
                                />
                              </div>
                            )}
                            rules={FORM_RULE_REQUIRED}
                            name="external_partner.name"
                            control={formProps.control}
                          />
                        </InputStyled>
                        <InputStyled>
                          <Controller
                            render={({ field, fieldState }) => (
                              <div ref={scrollRefs?.current[field.name]}>
                                <InputText
                                  disabled={disabled}
                                  label={t('Fields.PartnerAddNewEmail')}
                                  placeholder={t(
                                    'Fields.PartnerAddNewEmailPlaceholder'
                                  )}
                                  defaultValue={field.value}
                                  onChange={(data) => {
                                    field.onChange(data);
                                    setFoundVendorId(null);
                                    fieldState.invalid &&
                                      formProps.clearErrors(
                                        'external_partner.email'
                                      );
                                  }}
                                  labelAction={{
                                    label: t(
                                      `Fields.${
                                        foundVendorId
                                          ? 'PartnerAddNewLinkVendor'
                                          : 'Clear'
                                      }`
                                    ),
                                    action: () => {
                                      if (foundVendorId) {
                                        formProps.setValue(
                                          'partner',
                                          foundVendorId
                                        );
                                        formProps.setValue(
                                          'external_partner',
                                          null
                                        );
                                      } else {
                                        formProps.setValue(
                                          'external_partner.email',
                                          null
                                        );
                                      }
                                    },
                                    disabled: false,
                                  }}
                                  errors={
                                    fieldState.error
                                      ? [fieldState?.error?.message]
                                      : []
                                  }
                                  testId="ContractNewPartnerEmail"
                                />
                              </div>
                            )}
                            rules={{
                              ...FORM_RULE_REQUIRED,
                              validate: {
                                checkIsEmailValid: (value) => {
                                  if (!isValidEmail(value)) {
                                    return i18n.t('Errors.InvalidEmail');
                                  } else {
                                    return Vendors.singleByEmail(value)
                                      .then(({ data }) => {
                                        if (data.length > 0) {
                                          setFoundVendorId(data[0].id);
                                          return i18n.t(
                                            'Errors.EmailVendorExists'
                                          );
                                        } else {
                                          return true;
                                        }
                                      })
                                      .catch(() =>
                                        i18n.t('Errors.InternalError')
                                      );
                                  }
                                },
                              },
                            }}
                            name="external_partner.email"
                            control={formProps.control}
                          />
                        </InputStyled>
                      </FlexRowInputWrapper>
                    </PaddingBottomSpacingM>

                    <InputStyled>
                      <Controller
                        render={({ field, fieldState }) => {
                          const getVendorTypeDefaultValue = (data) => {
                            if (!field.value) {
                              formProps.setValue(
                                'external_partner.vendor_type',
                                data
                              );
                            }
                          };
                          return (
                            <div ref={scrollRefs?.current[field.name]}>
                              <SelectVendorType
                                disabled={disabled}
                                onChange={field.onChange}
                                errors={
                                  fieldState.error
                                    ? [fieldState?.error?.message]
                                    : []
                                }
                                testId="ContractNewPartnerType"
                                handleDefaultValue={getVendorTypeDefaultValue}
                                defaultValue={field.value}
                              />
                            </div>
                          );
                        }}
                        rules={{
                          validate: {
                            checkIsEmpty: (value) => {
                              if (!value) {
                                return i18n.t('Errors.ThisFieldIsRequired');
                              }
                              return true;
                            },
                          },
                        }}
                        name="external_partner.vendor_type"
                        control={formProps.control}
                      />
                    </InputStyled>
                  </>
                ),
              },
            ],
          },
        ]
      : []),
    ...(formProps.getValues('vendor_group') || vendorGroupSelected
      ? [
          {
            id: 'fixed-fields-vendor-group',
            elements: [
              {
                id: 'vendor-group',
                content: (
                  <Controller
                    render={({ field, fieldState }) => (
                      <div ref={scrollRefs?.current[field.name]}>
                        <VendorListSelect
                          onChange={field.onChange}
                          defaultListId={formProps.getValues('vendor_group')}
                          disabled={disabled}
                          contractType={contractType}
                          errors={
                            fieldState.error ? [fieldState?.error?.message] : []
                          }
                          testId="ContractVendorGroup"
                        />
                      </div>
                    )}
                    rules={FORM_RULE_REQUIRED}
                    name="vendor_group"
                    control={formProps.control}
                  />
                ),
              },
            ],
          },
        ]
      : []),
    ...(showDatesToggle
      ? [
          {
            id: 'fixed-fields-variable-dates-toggle',
            elements: [
              {
                id: 'dates-toggle',
                content: (
                  <Controller
                    render={({ field }) => (
                      <FlexWrapper>
                        <HeaderH3>{t('Fields.ContractSettings')}</HeaderH3>
                        <ToggleButtonGroup
                          options={[
                            {
                              text: t('Fields.FixedDates'),
                              key: 'fixed_dates',
                            },
                            {
                              text: t('Fields.VariableDates'),
                              key: 'variable_dates',
                            },
                          ]}
                          defaultValue={field.value}
                          shouldChange={shouldDatesToggleChange}
                          onChange={field.onChange}
                        />
                      </FlexWrapper>
                    )}
                    rules={FORM_RULE_REQUIRED}
                    name="period"
                    control={formProps.control}
                  />
                ),
              },
            ],
          },
        ]
      : []),
    ...(!allowVariableDates || formProps.getValues('period') === 'fixed_dates'
      ? [
          {
            id: 'fixed-fields-dates',
            elements: [
              {
                id: 'input-dates',
                content: (
                  <FlexWrapper>
                    {!showDatesToggle && (
                      <HeaderH3>{t('Fields.ContractSettings')}</HeaderH3>
                    )}
                    <InputRowWrapper>
                      <InputStyled>
                        <Controller
                          render={({ field, fieldState }) => (
                            <div ref={scrollRefs?.current[field.name]}>
                              <InputDatePicker
                                label={t('Fields.StartDate')}
                                placeholder={t('Fields.ChooseDatePlaceholder')}
                                disabled={
                                  disabled || fieldsConfig[field.name]?.disabled
                                }
                                defaultValue={field.value}
                                errors={
                                  fieldState.error
                                    ? [fieldState?.error?.message]
                                    : []
                                }
                                onChange={field.onChange}
                                testId="ContractStartDate"
                              />
                            </div>
                          )}
                          rules={{
                            ...FORM_RULE_REQUIRED,
                            validate: {
                              checkStartDateAfterEndDate: (value) => {
                                const selectedStartDate = parseISO(value);
                                const selectedEndDate = parseISO(
                                  formProps.getValues('end_date')
                                );
                                if (isValid(selectedEndDate)) {
                                  if (
                                    isAfter(selectedStartDate, selectedEndDate)
                                  ) {
                                    return i18n.t(
                                      'Errors.StartDateCannotBeAfterEndDate'
                                    );
                                  } else if (
                                    isEqual(selectedStartDate, selectedEndDate)
                                  ) {
                                    return i18n.t(
                                      'Errors.StartDateCannotBeSameAsEndDate'
                                    );
                                  }
                                }
                                return true;
                              },
                            },
                          }}
                          name="start_date"
                          control={formProps.control}
                        />
                      </InputStyled>
                      <InputStyled>
                        <Controller
                          render={({ field, fieldState }) => (
                            <div ref={scrollRefs?.current[field.name]}>
                              <InputDatePicker
                                label={t('Fields.EndDate')}
                                placeholder={t('Fields.ChooseDatePlaceholder')}
                                errors={
                                  fieldState.error
                                    ? [fieldState?.error?.message]
                                    : []
                                }
                                isOptional={!isEndDateMandatory}
                                disabled={
                                  disabled ||
                                  isEndDateDisabled ||
                                  fieldsConfig[field.name]?.disabled
                                }
                                defaultValue={field.value}
                                onChange={field.onChange}
                                testId="ContractEndDate"
                              />
                            </div>
                          )}
                          rules={{
                            ...(isEndDateMandatory && FORM_RULE_REQUIRED),
                            validate: {
                              checkAORIsEndDateExceedingYear: (value) => {
                                if (hasWorkflowWithAor) {
                                  const selectedStartDate = parseISO(
                                    formProps.getValues('start_date')
                                  );
                                  const selectedEndDate = parseISO(value);

                                  if (isValid(selectedStartDate)) {
                                    if (
                                      differenceInYears(
                                        selectedEndDate,
                                        selectedStartDate
                                      ) > 0
                                    ) {
                                      return i18n.t(
                                        'Errors.AORIsEndDateExceedingYear'
                                      );
                                    }
                                  }
                                }
                                return true;
                              },
                            },
                          }}
                          name="end_date"
                          control={formProps.control}
                        />
                      </InputStyled>
                    </InputRowWrapper>
                    {showPastInvoicesWarning && (
                      <Alert
                        message={i18n.t(
                          'Contracts:ContractPayments.PastInvoicesWarning'
                        )}
                        tag="alert-warning"
                        size="large"
                      />
                    )}
                  </FlexWrapper>
                ),
              },
            ],
          },
        ]
      : [
          {
            id: 'variable-fields-dates',
            elements: [
              {
                id: 'input-dates',
                content: (
                  <FlexWrapper>
                    {!showDatesToggle && (
                      <HeaderH3>{t('Fields.ContractSettings')}</HeaderH3>
                    )}
                    <InputRowWrapper>
                      <InputStyled>
                        <InputDatePicker
                          label={t('Fields.StartDate')}
                          placeholder={t('Fields.SameAsSigningDate')}
                          disabled={true}
                          testId={
                            generateTestId(
                              'VariableDatesStartDate',
                              'ContractWizard'
                            )['data-testid']
                          }
                          labelAction={{
                            label: '',
                            action: noop,
                            disabled: true,
                          }}
                        />
                      </InputStyled>
                      <InputStyled>
                        {formProps.getValues('duration') === 'specific_date' ? (
                          <Controller
                            render={({ field, fieldState }) => (
                              <div ref={scrollRefs?.current[field.name]}>
                                <InputDatePicker
                                  label={t('Fields.EndDate')}
                                  placeholder={t(
                                    'Fields.ChooseDatePlaceholder'
                                  )}
                                  errors={
                                    fieldState.error
                                      ? [fieldState?.error?.message]
                                      : []
                                  }
                                  disabled={disabled}
                                  defaultValue={field.value}
                                  onChange={(value) => {
                                    if (!value || isValid(parseISO(value))) {
                                      field.onChange(value);
                                    }
                                  }}
                                  testId={
                                    generateTestId(
                                      'VariableDatesEndDate',
                                      'ContractWizard'
                                    )['data-testid']
                                  }
                                  labelAction={{
                                    label: i18n.t('Actions.Reset'),
                                    action: () => {
                                      formProps.setValue(
                                        'duration',
                                        'one_year'
                                      );
                                      formProps.setValue('end_date', null);
                                    },
                                    disabled,
                                  }}
                                />
                              </div>
                            )}
                            rules={FORM_RULE_REQUIRED}
                            name="end_date"
                            control={formProps.control}
                          />
                        ) : (
                          <Controller
                            render={({ field }) => (
                              <div ref={scrollRefs?.current[field.name]}>
                                <InputSelect
                                  label={t('Fields.EndDate')}
                                  options={getContractDurationOptions(
                                    isEndDateMandatory,
                                    allowFixedDates,
                                    hasWorkflowWithAor
                                  )}
                                  onChange={({ key }) => {
                                    field.onChange(key);
                                  }}
                                  defaultValue={formProps.getValues('duration')}
                                />
                              </div>
                            )}
                            rules={FORM_RULE_REQUIRED}
                            name="duration"
                            control={formProps.control}
                          />
                        )}
                      </InputStyled>
                    </InputRowWrapper>
                  </FlexWrapper>
                ),
              },
            ],
          },
        ]),
  ];
};

const getContractPaymentScheduleString = (schedule: Schedule) => {
  let span = '';
  let period: ContractPaymentPeriodType = 'monthly';
  if (schedule.frequency === 'monthly') {
    if (schedule.month_days.length) {
      span = getPaymentDayOfTheMonthOptions().find(
        (option) => option.key === schedule.month_days[0]
      )?.text;
    } else {
      span = getPaymentDayOfTheWeekOptions().find(
        (option) => option.key === schedule.week_days[0].replace('(-1)', '')
      )?.text;
    }
  } else {
    if (schedule.interval === 1) {
      period = 'weekly';
    } else {
      period = 'biweekly';
    }
    span = getContractWeeklyPaymentSpans(period).find(
      (option) => option.key === schedule.week_days[0]
    )?.text;
  }
  span =
    period === 'weekly' ? span : span.charAt(0).toLowerCase() + span.slice(1);
  return `${t(`ContractPayments.Periods.${period}`)}, ${t(
    'ContractPayments.every'
  )} ${span}`;
};

export const reduceContractCustomFields = (data: ContractResponseData) => {
  const dataCopy = cloneDeep(data);
  if (Array.isArray(dataCopy.custom_fields)) {
    dataCopy.custom_fields = dataCopy.custom_fields.reduce(
      reduceCustomFields,
      {}
    );
  }
  if (Array.isArray(dataCopy.payment_template?.custom_fields)) {
    dataCopy.payment_template.custom_fields =
      dataCopy.payment_template.custom_fields.reduce(reduceCustomFields, {});
  }
  if (Array.isArray(dataCopy.invoicing_template?.custom_fields)) {
    dataCopy.invoicing_template.custom_fields =
      dataCopy.invoicing_template.custom_fields.reduce(reduceCustomFields, {});
  }
  if (dataCopy.task_templates?.length > 0) {
    dataCopy.task_templates = dataCopy.task_templates.map((template) => {
      const templateCopy = cloneDeep(template);
      if (Array.isArray(template.custom_fields)) {
        templateCopy.custom_fields = template.custom_fields.reduce(
          reduceCustomFields,
          {}
        );
      }
      return templateCopy;
    });
  }
  return dataCopy;
};

export const getContractActions = (
  contract: ContractResponseData | ContractSearchResponseData
): Promise<ContractAction[]> =>
  getActionsBase(
    contractActionBlueprints,
    (blueprint, contract) => blueprint.isAvailableSingle(contract),
    (blueprint, contract) => blueprint.prepareActionSingle(contract),
    contract
  );

export const getContractBulkActions = (
  contracts: ContractSearchResponseData[]
): Promise<BulkAction[]> =>
  getActionsBase(
    contractActionBlueprints,
    (blueprint, contracts) => blueprint.isAvailableMany(contracts),
    (blueprint, contracts) => blueprint.prepareActionMany(contracts),
    contracts
  );

export const getContractActionsBySearchParams = ({
  query,
  filters,
  sort,
}: SearchParams): Promise<BulkAction[]> =>
  getActionsBase(
    contractActionBlueprints,
    (blueprint) => blueprint.isAvailableBulk?.() ?? false,
    (blueprint, params) => blueprint.prepareActionBulk?.(params) ?? noop,
    { query, filters, sort }
  );

export const getContractTemplateActions = (
  contractTemplate: ContractTemplate
): Promise<ContractTemplateAction[]> =>
  getActionsBase(
    contractTemplateActionBlueprints,
    (blueprint, template) => blueprint.isAvailableSingle(template),
    (blueprint, template) => blueprint.prepareActionSingle(template),
    contractTemplate
  );

export const getPrefixedPropName = <T extends string, K extends string>(
  prefix: T,
  id: K
): `${T}_${K}` => `${prefix}_${id}`;

export const CONTRACT_TODAY_FIXED_DATE = new Date('2023-06-01');

export const getContractPaymentSchedule = (
  paymentSettings: ContractPaymentSettings
) => {
  const paymentSchedule: Schedule = {
    frequency: 'weekly',
    month_days: [],
    week_days: [],
    interval: 1,
  };

  if (paymentSettings?.period === 'monthly') {
    paymentSchedule.frequency = 'monthly';
    if (
      paymentSettings?.span === 'day_of_month' &&
      paymentSettings.day_of_month
    ) {
      paymentSchedule.month_days = [Number(paymentSettings.day_of_month)];
    } else if (
      paymentSettings?.span === 'day_of_week' &&
      paymentSettings.day_of_month
    ) {
      paymentSchedule.week_days = [
        `${String(paymentSettings.day_of_month) as SchedulerWeekDaysType}(-1)`,
      ];
    }
  } else if (paymentSettings?.span) {
    paymentSchedule.week_days = [
      String(paymentSettings.span) as SchedulerWeekDaysType,
    ];
  }

  if (paymentSettings?.period === 'biweekly') {
    paymentSchedule.interval = 2;
  }

  return paymentSchedule;
};

export const getContractPaymentSettings = (schedule: Schedule) => {
  const paymentSettings = {} as ContractPaymentSettings;
  if (schedule?.frequency === 'monthly') {
    paymentSettings.period = 'monthly';
    if (schedule.month_days.length) {
      paymentSettings.span = 'day_of_month';
      paymentSettings.day_of_month = schedule.month_days[0];
    } else {
      paymentSettings.span = 'day_of_week';
      paymentSettings.day_of_month = schedule.week_days[0].replace('(-1)', '');
    }
  } else if (schedule?.frequency === 'weekly') {
    if (schedule.interval === 1) {
      paymentSettings.period = 'weekly';
    } else {
      paymentSettings.period = 'biweekly';
    }
    paymentSettings.span = schedule.week_days[0] as SchedulerWeekDaysType;
  } else {
    paymentSettings.period = 'monthly';
    paymentSettings.span = 'day_of_month';
    paymentSettings.day_of_month = 31;
  }

  return paymentSettings;
};

export const getContractStatus = (
  contract:
    | ContractResponseData
    | ContractSearchResponseData
    | ContractRequestData
): ContractStatusType => {
  switch (true) {
    case contract.status === 'live' && contract.ending_soon:
      return 'ending_soon';
    case contract.archived:
      return 'archived';
    case contract.status === 'live' && contract.under_revision:
      return 'change_request';
    default:
      return contract.status;
  }
};

export const getContractTemplateStatus = (
  template: ContractTemplate
): ContractTemplateStatusType => {
  switch (true) {
    case template.archived:
      return 'archived';
    case template.number_of_contracts > 0:
      return 'active';
    default:
      return 'unused';
  }
};

export const getCoveredPeriod = (
  contractPaymentTemplate: ContractPaymentTemplate,
  paymentPrefix: PaymentPrefix
) => {
  const payPeriodProp = getPrefixedPropName(paymentPrefix, 'pay_period');
  return `${formatDateWithUserSettings(
    contractPaymentTemplate?.[payPeriodProp]?.start_date
  )} - ${formatDateWithUserSettings(
    contractPaymentTemplate?.[payPeriodProp]?.end_date
  )}`;
};

export const getSelectedCalculation = (
  contractPaymentTemplate: ContractPaymentTemplate,
  paymentPrefix: PaymentPrefix
) =>
  contractPaymentTemplate?.[
    getPrefixedPropName(paymentPrefix, 'pay_period_amount_calculation')
  ];

export const getPeriodAmountLabel = (
  contractPaymentTemplate: ContractPaymentTemplate
) => {
  const period =
    contractPaymentTemplate?.pay_period.interval === 2
      ? 'biweekly'
      : contractPaymentTemplate?.pay_period.frequency;
  return `${t(`ContractPayments.Periods.${period}`)} ${t(
    'ContractPayments.rate'
  )}`;
};

export const getPeriodTaxLabel = (
  contractPaymentTemplate: ContractPaymentTemplate
) => {
  const period =
    contractPaymentTemplate?.pay_period.interval === 2
      ? 'biweekly'
      : contractPaymentTemplate?.pay_period.frequency;
  return `${t(`ContractPayments.Periods.${period}`)} ${t(
    'ContractPayments.tax'
  )}`;
};

export const getContractPaymentDays = (
  contractPaymentTemplate: ContractPaymentTemplate,
  paymentPrefix: PaymentPrefix
) => {
  const contractPaymentOption =
    contractPaymentTemplate?.[
      getPrefixedPropName(paymentPrefix, 'pay_period_amount_calculation')
    ];

  const days =
    contractPaymentTemplate?.[getPrefixedPropName(paymentPrefix, 'pay_period')]
      .days;

  const daysValue = t('ContractPayments.DaysValue', {
    count: days,
  });

  const daysLabel = t('ContractPayments.DaysLabel', {
    type:
      contractPaymentOption === 'calculate_working_days'
        ? 'Working'
        : 'Calendar',
    count: days,
  });

  return {
    label: daysLabel,
    value: daysValue,
  };
};

const getContractStartDateString = (contract: Contract): string => {
  if (contract.start_date) {
    return formatDateWithUserSettings(contract.start_date);
  }
  if (!contract.start_date && contract.period === 'variable_dates') {
    return t('Fields.SameAsSigningDate');
  }
};

const getContractEndDateString = (contract: Contract): string => {
  if (contract.end_date) {
    return formatDateWithUserSettings(contract.end_date);
  } else if (contract.period === 'variable_dates' && contract.duration) {
    return t(`ContractDuration.${contract.duration}`);
  }
  return t('ContractPayments.Indefinite');
};

const getContractAssignedBuyer = (contract: Contract) => {
  if (contract.payment_template?.assigned_buyer) {
    return contract.payment_template.assigned_buyer as SimpleUser;
  }
  return null;
};

export const getContractDetailsData = (
  contract: ContractResponseData,
  customFields: CustomField[]
) => {
  const isVendor = getReduxSessionUser().isVendor;
  const assignedBuyer = getContractAssignedBuyer(contract);
  const partner = contract.external_partner
    ? ({
        name: contract.external_partner.name,
        email: contract.external_partner.email,
        vendor_type: contract.external_partner.vendor_type,
      } as Vendor)
    : contract.partner;

  const newContractDetailsItems: PreviewFormField[] = [
    ...(!isVendor && (contract.partner || contract.external_partner)
      ? ([
          {
            id: 'partner',
            label: null,
            value: partner,
            type: 'partner',
          },
        ] as const)
      : []),
    ...(!isVendor && contract?.vendor_group
      ? ([
          {
            id: 'vendor_group',
            label: null,
            value: contract.vendor_group,
            type: 'vendor_group',
          },
        ] as const)
      : []),
    {
      id: 'reference_id',
      label: { label: t('Fields.ContractId') },
      value: contract.reference_id,
      type: 'cf_text_line',
    },
    {
      id: 'start_date',
      label: { label: t('Fields.StartDate') },
      value: getContractStartDateString(contract),
      type: 'cf_text_line',
    },
    {
      id: 'end_date',
      label: { label: t('Fields.EndDate') },
      value: getContractEndDateString(contract),
      type: 'cf_text_line',
    },
    {
      id: 'description',
      label: { label: t('Fields.ContractDescription') },
      value: contract.description,
      type: 'cf_text_line',
    },
    ...(tenantHasFeature('payments_assigned_buyer') && contract.payment_template
      ? [
          {
            id: 'payment_approver',
            label: {
              label: t('ContractPayments.InvoiceApprover'),
            },
            type: 'cf_user' as const,
            value: assignedBuyer ? [assignedBuyer] : undefined,
          },
        ]
      : []),
  ];

  const previewItems = getPreviewCustomFields(
    contract?.custom_fields,
    customFields,
    isVendor
  );

  return [...newContractDetailsItems, ...previewItems];
};

const formatPeriodDates = (startDate: string, endDate: string) =>
  `${formatDateWithUserSettings(startDate)} - ${formatDateWithUserSettings(
    endDate
  )}`;

const getContractDuration = (
  contractPaymentTemplate: ContractPaymentTemplate,
  contractSchedules?: ContractPaymentsSchedule
) => {
  if (contractPaymentTemplate?.last_pay_period) {
    return formatPeriodDates(
      contractPaymentTemplate?.first_pay_period?.start_date,
      contractPaymentTemplate?.last_pay_period?.end_date
    );
  } else if (contractSchedules?.total_invoices === 1) {
    return formatPeriodDates(
      contractSchedules?.pay_periods[0]?.start_date,
      contractSchedules?.pay_periods[0]?.end_date
    );
  }
  return t('ContractPayments.Indefinite');
};

export const getRateFrequency = (
  contractPaymentTemplate: ContractPaymentTemplate
) => {
  if (!contractPaymentTemplate?.amount) {
    return null;
  }
  const period =
    contractPaymentTemplate?.pay_period.interval === 2
      ? 'biweekly'
      : contractPaymentTemplate?.pay_period.frequency;
  const amount = formatCurrency(
    contractPaymentTemplate.amount,
    2,
    contractPaymentTemplate.currency
  );
  return `${amount} ${t(`ContractPayments.PerPeriod.${period}`)}`;
};

export const getPayPeriodLabel = (payPeriod: Schedule) => {
  switch (payPeriod.frequency) {
    case 'monthly':
      return t('ContractPayments.Periods.monthly');
    case 'weekly':
      if (payPeriod.interval === 2) {
        return t('ContractPayments.Periods.biweekly');
      }
      return t('ContractPayments.Periods.weekly');
    default:
      return '';
  }
};

const getTotalInvoicesValue = (
  paymentTemplate: ContractPaymentTemplate,
  contractSchedules?: ContractPaymentsSchedule
) => {
  const startOfDescriptionText = t(
    'ContractPayments.TotalInvoicesStartOfDescription',
    {
      frequency: getPayPeriodLabel(paymentTemplate.pay_period).toLowerCase(),
    }
  );
  const occurrencesText = `${contractSchedules.total_invoices} ${t(
    'ContractPayments.occurrence',
    { count: contractSchedules.total_invoices }
  )}`;
  const startingDateText = t('ContractPayments.TotalInvoicesStartingDate', {
    startDate: formatDateWithUserSettings(
      contractSchedules.pay_periods[0].pay_date
    ),
  });

  return (
    <>
      {startOfDescriptionText}{' '}
      <ContractScheduleTooltip
        contractSchedules={contractSchedules}
        currency={paymentTemplate.currency}
      >
        <OccurrencesWrapper {...generateTestId('Occurrences', 'TotalInvoices')}>
          {occurrencesText}
        </OccurrencesWrapper>
      </ContractScheduleTooltip>{' '}
      {startingDateText}
    </>
  );
};

export const getNextCycleRateString = (
  paymentTemplate: ContractPaymentTemplate,
  nextRevisionPaymentTemplate: ContractPaymentTemplate
) => {
  const contractRate = getRateFrequency(paymentTemplate);
  const nextRevisionRate = getRateFrequency(nextRevisionPaymentTemplate);
  const showNextCycleRate =
    nextRevisionRate && contractRate !== nextRevisionRate;
  return showNextCycleRate
    ? t('ContractPayments.RateFromNextCycle', {
        contractRate: nextRevisionRate,
      })
    : '';
};

export const getContractSummaryFields = (
  paymentTemplate: ContractPaymentTemplate,
  contractSchedules?: ContractPaymentsSchedule,
  isChangeRequest?: boolean,
  nextRevisionPaymentTemplate?: ContractPaymentTemplate,
  taxRates?: TaxRate[]
) => {
  const nextCycleRateString = getNextCycleRateString(
    paymentTemplate,
    nextRevisionPaymentTemplate
  );
  const hasTaxRates = taxRates?.length > 0 && paymentTemplate.tax;

  return createPreviewFormItems([
    {
      id: 'payments_schedule',
      label: { label: t('ContractPayments.InvoicingFrequency') },
      type: 'cf_text_line',
      value: paymentTemplate?.pay_period
        ? getContractPaymentScheduleString(paymentTemplate.pay_period)
        : undefined,
    },
    {
      id: 'payments_amount_without_tax',
      label: { label: getPeriodAmountLabel(paymentTemplate) },
      labelAdditionalInfo: hasTaxRates
        ? t('ContractPayments.TaxExcluded')
        : null,
      type: 'react_node',
      value: (
        <PaymentsFormNextCycleRateWrapper>
          <span>{getRateFrequency(paymentTemplate)}</span>
          {nextCycleRateString && (
            <StyledPaymentsFormNextCycleRate>
              {nextCycleRateString}
            </StyledPaymentsFormNextCycleRate>
          )}
        </PaymentsFormNextCycleRateWrapper>
      ),
    },
    ...(hasTaxRates
      ? ([
          {
            id: 'payments_tax_rate',
            label: { label: t('ContractPayments.TaxRate') },
            type: 'cf_text_line',
            value: getTaxValueString(taxRates, paymentTemplate.tax),
          },
          {
            id: 'payments_tax_amount',
            label: { label: getPeriodTaxLabel(paymentTemplate) },
            type: 'cf_text_line',
            value: paymentTemplate.tax_amount
              ? formatCurrency(
                  paymentTemplate.tax_amount,
                  2,
                  paymentTemplate.currency
                )
              : '',
          },
          {
            id: 'payments_amount_with_tax',
            label: { label: getPeriodAmountLabel(paymentTemplate) },
            labelAdditionalInfo: hasTaxRates
              ? t('ContractPayments.TaxIncluded')
              : null,
            type: 'cf_text_line',
            value: paymentTemplate.amount
              ? formatCurrency(
                  paymentTemplate.amount + paymentTemplate.tax_amount,
                  2,
                  paymentTemplate.currency
                )
              : '',
          },
        ] as const)
      : []),
    ...(!isChangeRequest && contractSchedules?.total_invoices
      ? ([
          {
            id: 'contract_total_fees',
            label: { label: t('ContractPayments.TotalFees') },
            labelAdditionalInfo: hasTaxRates
              ? t('ContractPayments.TaxIncluded')
              : null,
            type: 'cf_text_line',
            value:
              contractSchedules?.total_amount &&
              formatCurrency(
                contractSchedules.total_amount,
                2,
                paymentTemplate.currency
              ),
          },
          {
            id: 'contract_total_invoices',
            label: {
              label: t('ContractPayments.TotalInvoices'),
            },
            type: 'react_node',
            value:
              contractSchedules?.total_invoices &&
              getTotalInvoicesValue(paymentTemplate, contractSchedules),
          },
        ] as const)
      : []),
    {
      id: 'contract_duration',
      label: { label: t('ContractPayments.ContractDuration') },
      type: 'cf_text_line',
      value: getContractDuration(paymentTemplate, contractSchedules),
    },
    {
      id: 'payments_name',
      label: { label: t('ContractPayments.InvoiceName') },
      type: 'cf_text_line',
      value: paymentTemplate?.name,
    },
  ]);
};

export const convertHintContentToHTML = (content: string) => (
  <div
    dangerouslySetInnerHTML={{
      __html: safeMessage(content, ['data-list']),
    }}
  />
);

export const getFormattedHTMLContent = (content) => {
  if (removeAllHtmlTags(content).length === 0) {
    return '';
  }
  return content;
};

export const isContractStatusNotEqual = (
  status: ContractStatusType,
  statusToCheck: ContractStatusType
) => status && status !== statusToCheck;

export const getContractAssignedBuyerId = (
  paymentTemplate: ContractPaymentTemplate
) => {
  const assignedBuyer = paymentTemplate.assigned_buyer;
  if (assignedBuyer && typeof assignedBuyer === 'object') {
    return assignedBuyer.id;
  }
  return assignedBuyer;
};

const getErrorsArray = (
  item: ErrorObject,
  fieldNames?: Record<string, string>,
  parentKey?: string
): string[] => {
  if (!item) {
    return [];
  }

  return Object.entries(item).flatMap(([key, value]) => {
    const displayKey = fieldNames?.[key] ?? snakeToTitleCase(key);
    const fullKey = parentKey ? `${parentKey}: ${displayKey}` : displayKey;

    if (Array.isArray(value)) {
      return value.flatMap((errorMsg) => {
        if (typeof errorMsg === 'string') {
          return key === 'non_field_errors'
            ? errorMsg
            : `${fullKey}: ${errorMsg}`;
        } else if (typeof errorMsg === 'object' && errorMsg !== null) {
          return getErrorsArray(errorMsg, fieldNames, fullKey);
        }
        return [];
      });
    } else if (typeof value === 'object' && value !== null) {
      return getErrorsArray(value, fieldNames, fullKey);
    } else if (value !== undefined && value !== null) {
      return [key === 'non_field_errors' ? value : `${fullKey}: ${value}`];
    }

    return [];
  });
};

export const getErrorsList = (
  data,
  getErrorMessage?: (error: string) => string,
  fieldNames?: Record<string, string>
) => {
  const errors = getErrorsArray(data, fieldNames);
  if (errors.length === 1) {
    return getErrorMessage ? getErrorMessage(errors[0]) : errors[0];
  }
  return `<ul>${errors
    .map(
      (error) => `<li>${getErrorMessage ? getErrorMessage(error) : error}</li>`
    )
    .join('')}</ul>`;
};

export const getCleanedContractData = (
  contractData: ContractRequestData,
  currentStep?: number
) => {
  const cleanedContractData = removeEmptyProps(contractData, true);
  if (currentStep === 1) {
    cleanedContractData.payment_template = deleteProps(
      cleanedContractData.payment_template,
      [
        'first_pay_period_amount_calculation',
        'last_pay_period_amount_calculation',
        'first_pay_period_custom_amount',
        'last_pay_period_custom_amount',
      ]
    ) as ContractPaymentTemplate;
  }
  if (contractData.payment_template) {
    cleanedContractData.currency = contractData.payment_template.currency;
  }
  if (!contractData.end_date) {
    cleanedContractData.end_date = null;
  }
  if (!contractData.start_date) {
    cleanedContractData.start_date = null;
  }
  if (!contractData.duration) {
    cleanedContractData.duration = null;
  }
  if (contractData.external_partner) {
    cleanedContractData.partner = null;
    cleanedContractData.vendor_group = null;
  } else {
    cleanedContractData.external_partner = null;
  }
  if (contractData.vendor_group) {
    cleanedContractData.partner = null;
    cleanedContractData.external_partner = null;
  } else {
    cleanedContractData.vendor_group = null;
  }
  if (cleanedContractData.payment_template) {
    cleanedContractData.payment_template.assigned_buyer =
      getContractAssignedBuyerId(contractData.payment_template);
  }
  if (Array.isArray(cleanedContractData.task_templates)) {
    cleanedContractData.task_templates =
      normalizeContractTaskTemplateData(contractData);
  }
  if (contractData.budget?.contracted_budget.budget_type === 'no_budget') {
    cleanedContractData.budget.contracted_budget.allocated_amount = null;
    cleanedContractData.budget.contracted_budget.prevent_over_budget_expenses =
      null;
  }
  if (
    contractData.budget?.reimbursement_allowance.allowance_type ===
    'no_allowance'
  ) {
    cleanedContractData.budget.reimbursement_allowance.allocated_amount = null;
  }
  if (
    contractData.invoicing_template?.invoice_issuance_type === 'manual_task'
  ) {
    cleanedContractData.invoicing_template = deleteProps(
      contractData.invoicing_template,
      [
        'name',
        'assigned_buyer',
        'category',
        'custom_fields',
        'mark_as_approved',
        'currency',
      ]
    );
  }
  if (
    cleanedContractData.ten99_insurance &&
    Object.keys(cleanedContractData.ten99_insurance).length === 0
  ) {
    cleanedContractData.ten99_insurance = null;
  }

  return cleanedContractData;
};

const normalizeContractTaskTemplateData = (
  contractData: ContractRequestData
): ContractTaskTemplate[] => {
  const {
    task_templates: taskTemplates,
    currency,
    partner,
    vendor_group: vendorGroup,
  } = contractData;
  return taskTemplates.map((template: ContractTask) => {
    const isNewTask = isTaskNew(template);
    const hasVariableDates = hasContractVariableDates(contractData);
    const shouldSkipDate = isNewTask && hasVariableDates;
    const vendorField = template.vendor?.id
      ? { vendor: { id: template.vendor.id } }
      : vendorGroup || contractData.external_partner
      ? {}
      : { vendor: { id: partner } };

    return {
      id: template.template_id,
      name: template.name,
      description: template.description,
      date_start: shouldSkipDate ? null : template.date_start,
      date_end: shouldSkipDate ? null : template.date_end,
      budget_rate_type: template.budget_rate_type,
      budget_total: template.budget_total,
      budget_rate_per_time_unit: template.budget_rate_per_time_unit,
      budget_time_units_worked: template.budget_time_units_worked,
      timesheet_template: template.timesheet_template,
      managers: template.managers,
      task_group: template.task_group?.id
        ? template.task_group
        : {
            ...template.task_group,
            currency,
          },
      ...(!isNewTask
        ? {
            task_id: template.task_id,
            vendor: template.vendor,
          }
        : {}),
      custom_fields: template.custom_fields,
      ...vendorField,
    };
  });
};

export const getContractType = (
  template: ContractTypeVariety
): ContractType => {
  if (template.with_statement_of_work) {
    return 'SOW';
  } else if (template.with_time_and_material) {
    return 'TimeAndMaterial';
  }
  if (template.with_time_and_material) {
    return 'TimeAndMaterial';
  }
  return template.with_fixed_rate_payments ? 'FixedRate' : 'GeneralFramework';
};

export const getContractTypeName = (template: ContractTypeVariety) =>
  t(`ContractTemplateTypes.${getContractType(template)}.Header`);

export const prepareContractRequestData = (
  contractData: ContractResponseData
) => {
  const contractDataCopy = {
    ...reduceContractCustomFields(contractData),
    partner: contractData.partner?.id,
  };
  return contractDataCopy as unknown as ContractRequestData;
};

const getTaxValueString = (taxRates: TaxRate[], paymentTemplateTax: number) =>
  `${
    taxRates.find((taxRate) => taxRate.id === paymentTemplateTax).percent_value
  }%`;

const getPrefixedInvoiceCurrencyField = (
  contractPaymentTemplate: ContractPaymentTemplate,
  paymentPrefix: PaymentPrefix,
  field: ContractInvoiceCurrencyField,
  decimals: number = 2
) => {
  const currencyField =
    contractPaymentTemplate?.[
      getPrefixedPropName(paymentPrefix, 'pay_period')
    ]?.[field];
  if (currencyField >= 0) {
    return formatCurrency(
      currencyField,
      decimals,
      contractPaymentTemplate.currency
    );
  }
  return undefined;
};

export const getPrefixedInvoiceCurrencyFields = (
  paymentTemplate: ContractPaymentTemplate,
  paymentPrefix: PaymentPrefix
) =>
  ['tax_amount', 'amount', 'day_rate'].map(
    (key: ContractInvoiceCurrencyField) =>
      getPrefixedInvoiceCurrencyField(paymentTemplate, paymentPrefix, key, 2)
  );

export const getSignerLabelForContract = (
  acceptance: ContractAcceptance,
  contractPartner: ContractResponseData['partner']
) => {
  if (
    !acceptance.user.first_name &&
    !acceptance.user.last_name &&
    acceptance.signer?.type === 'partner'
  ) {
    return contractPartner.full_name;
  }
  return `${acceptance.user.first_name} ${acceptance.user.last_name}`;
};

export const getChangeRequestStatus = (
  curVal: ReactNode,
  newVal: ReactNode
): ContractChangeRequestStatusType => {
  if (curVal && !newVal) {
    return 'removed';
  } else if (!curVal && newVal) {
    return 'added';
  } else if (curVal === newVal) {
    return 'unchanged';
  }
  return 'changed';
};

export const getContractTaskIds = (contractTasks: ContractTaskTemplate[]) =>
  contractTasks.map(({ task_id }) => task_id).filter(Boolean);

export const getContractBudgetData = (
  contractBudget: ContractBudget,
  currency: string,
  lineItemCategories: LineItemsCategory[]
): PreviewFormField[] => {
  const t = (id: string) =>
    i18n.t(`Contracts:ContractBudgetAndReimbursementAllowance.${id}`);

  const isVendor = getReduxSessionUser().isVendor;
  const contractedBudget = contractBudget?.contracted_budget;
  const reimbursementAllowance = contractBudget?.reimbursement_allowance;

  const overBudgetInvoicingProtectionValue = t(
    contractedBudget?.prevent_over_budget_expenses ? 'Active' : 'Inactive'
  );

  const expenseCategories =
    reimbursementAllowance?.expense_reimbursement_allowances.map(
      (item) => item.expense_category
    );
  const categoryNames = lineItemCategories
    .filter(({ id }) => expenseCategories?.includes(id))
    .map(({ name }) => `${name}\n`);

  const contractBudgetFields: PreviewFormField[] = isVendor
    ? []
    : [
        {
          id: 'contract-budget-type',
          label: { label: t('ContractBudgetTypeLabel') },
          value: t(contractedBudget?.budget_type ?? 'no_budget'),
          type: 'cf_text_line',
        },
        ...(contractedBudget?.budget_type === 'general_budget'
          ? ([
              {
                id: 'contract-budget',
                label: { label: t('ContractBudgetLabel') },
                value: (
                  <Currency
                    textAlign="left"
                    value={contractedBudget.allocated_amount}
                    currency={currency}
                  />
                ),
                type: 'cf_text_line',
              },
              {
                id: 'contract-budget-invoicing-protection',
                label: { label: t('OverBudgetInvoicingProtectionLabel') },
                value: overBudgetInvoicingProtectionValue,
                type: 'cf_text_line',
              },
            ] as const)
          : []),
      ];

  const reimbursementFields: PreviewFormField[] = [
    {
      id: 'reimbursement-allowance-type',
      label: { label: t('ReimbursementAllowanceTypeLabel') },
      value: t(reimbursementAllowance?.allowance_type ?? 'no_allowance'),
      type: 'cf_text_line',
    },
    ...(reimbursementAllowance?.allowance_type === 'general_allowance'
      ? ([
          {
            id: 'reimbursement-allowance',
            label: { label: t('ReimbursementAllowanceLabel') },
            value: (
              <Currency
                textAlign="left"
                value={reimbursementAllowance.allocated_amount}
                currency={currency}
              />
            ),
            type: 'cf_text_line',
          },
          {
            id: 'reimbursement-allowance-categories',
            label: { label: t('ReimbursementAllowanceCategoriesLabel') },
            value: categoryNames,
            type: 'cf_text_line',
          },
        ] as const)
      : []),
  ];

  return [...contractBudgetFields, ...reimbursementFields];
};

export const getContractInvoicingData = (
  invoicingTemplate: ContractInvoicingTemplate
): PreviewFormField[] => {
  const isSimpleUser = (
    assignedBuyer: ContractInvoicingTemplate['assigned_buyer']
  ): assignedBuyer is SimpleUser =>
    typeof assignedBuyer === 'object' && 'id' in assignedBuyer;
  const isVendor = getReduxSessionUser().isVendor;

  return [
    {
      id: 'contract-invoicing',
      label: { label: t('ContractInvoicing.ContractInvoicingLabel') },
      value: t(
        `ContractInvoicing.Options.${invoicingTemplate.invoice_issuance_type}`
      ),
      type: 'cf_text_line',
    },
    ...(invoicingTemplate.invoice_issuance_type === 'automated_cumulative'
      ? ([
          ...(!isVendor
            ? [
                {
                  id: 'contract-invoice-auto-approve',
                  label: { label: t('ContractInvoicing.MarkPaymentsApproved') },
                  value: invoicingTemplate.mark_as_approved,
                  type: 'cf_boolean' as const,
                },
              ]
            : []),
          {
            id: 'contract-invoice-name',
            label: { label: t('ContractInvoicing.ContractInvoiceNameLabel') },
            value: invoicingTemplate.name,
            type: 'cf_text_line',
          },
          ...(tenantHasFeature('payments_assigned_buyer') &&
          invoicingTemplate.assigned_buyer
            ? [
                {
                  id: 'contract-invoice-approver',
                  label: {
                    label: t(
                      'ContractInvoicing.ContractInvoicingApproverLabel'
                    ),
                  },
                  type: 'cf_user' as const,
                  value: isSimpleUser(invoicingTemplate.assigned_buyer)
                    ? [invoicingTemplate.assigned_buyer]
                    : undefined,
                },
              ]
            : []),
          {
            id: 'contract-invoicing-currency',
            label: {
              label: t('ContractInvoicing.ContractInvoicingCurrencyLabel'),
            },
            value: invoicingTemplate.currency,
            type: 'cf_text_line',
          },
        ] as const)
      : []),
  ];
};

export const getPaymentFieldPrefix = (contractType: ContractType) =>
  ['SOW', 'TimeAndMaterial'].includes(contractType)
    ? 'invoicing_template'
    : 'payment_template';

export const hasContractVariableDates = (contract: ContractRequestData) =>
  contract.period === 'variable_dates' && !contract.start_date;
