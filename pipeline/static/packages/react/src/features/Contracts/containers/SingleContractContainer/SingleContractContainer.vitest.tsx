import { composeStories } from '@storybook/react';
import { fireEvent, screen, within } from '@testing-library/react';
import { SingleContractNavTabName } from '../../../../types/contracts';
import {
  render,
  viMockIntersectionObserver,
} from '../../../../utils/vitest.helpers';
import { setInitialState } from '../../../Payments/components/PaymentsDataTable/PaymentsDataTable.hooks';
import * as stories from './SingleContractContainer.stories';

// exclude WithChangeRequestHistory, SOW stories
const {
  WithChangeRequestHistory,
  SOW,
  TemplateTimeAndMaterial,
  ...filteredStories
} = stories;
const composedStories = composeStories(filteredStories);

const setTab = (tabName: SingleContractNavTabName) => {
  location.search = `?tab=${tabName}`;
};

describe('SingleContractContainer - ', () => {
  beforeEach(async () => {
    viMockIntersectionObserver();
  });

  afterEach(() => {
    vi.clearAllMocks();
    location.search = '';
    setInitialState();
  });

  test('change request history snapshot should match', async () => {
    setTab('change_requests');
    const { WithChangeRequestHistory: SingleContractContainer } =
      composeStories(stories);
    render(<SingleContractContainer />);
    const changeRequestHistory = await screen.findByText(
      'Change Request history'
    );
    expect(changeRequestHistory).toBeInTheDocument();

    const rowRev3 = await screen.findByText('(Rev. 3)');
    fireEvent.click(rowRev3);

    expect(await screen.findByText('bo tak 2')).toBeInTheDocument();

    // there is no easy way to stabilize this assertion, because MuiCollapse still runs transition even if 0 duration
    // expect(container).toMatchSnapshot();
  });

  test.each(Object.entries(composedStories))(
    'snapshot should match: %s',
    async (storyName, SingleContractContainer) => {
      setTab('details');
      const { container } = render(<SingleContractContainer />);
      expect(
        await screen.findByText('Details', {}, { timeout: 5000 })
      ).toBeVisible();
      if (storyName === 'AsVendor') {
        expect(await screen.findByText('Kamil Stoch')).toBeVisible();
        expect(
          await screen.findByText('Next 3 Upcoming Invoices')
        ).toBeVisible();
      } else {
        expect(
          await screen.findByText('Konrad Siery', {}, { timeout: 5000 })
        ).toBeVisible();
      }
      if (storyName === 'StatusUnderReview') {
        expect(await screen.findByText('Dane osobowe')).toBeInTheDocument();
      } else if (storyName === 'StatusAccepting') {
        expect(await screen.findByText('Sheila James')).toBeInTheDocument();
      }

      expect(container).toMatchSnapshot();
    }
  );

  test('payments snapshot should match', async () => {
    setTab('invoices');
    const { WithInvoices: SingleContractContainer } = composeStories(stories);
    const { container } = render(<SingleContractContainer />);
    expect(
      await within(container).findByTestId('Search_DataTable')
    ).toBeVisible();
  });

  test('activity log snapshot should match', async () => {
    setTab('activity_log');
    const { Default: SingleContractContainer } = composeStories(stories);
    const { container } = render(<SingleContractContainer />);
    await within(container).findByText('Contract was set to: canceled');

    expect(container).toMatchSnapshot();
  });

  test('overview snapshot should match', async () => {
    setTab('overview');
    const { WithInvoices: SingleContractContainer } = composeStories(stories);
    const { container } = render(<SingleContractContainer />);
    await within(container).findByText('The crucial Contracts information.');
    await within(container).findByText('Invoice. Invoice.');
    await within(container).findByText('<EMAIL>');

    expect(container).toMatchSnapshot();
  });

  test('original revision should load correctly', async () => {
    setTab('change_requests');
    const { WithChangeRequestHistory: SingleContractContainer } =
      composeStories(stories);
    render(<SingleContractContainer />);
    const changeRequestHistory = await screen.findByText(
      'Change Request history'
    );
    expect(changeRequestHistory).toBeInTheDocument();

    const rowOriginal = await screen.findByText('(Original)');
    fireEvent.click(rowOriginal);
    expect(await screen.findByText('Kinder fajer')).toBeInTheDocument();
  });

  test('agreement for signing should match', async () => {
    setTab('details');
    const { AgreementSigningOptions: SingleContractContainer } =
      composeStories(stories);
    const { container } = render(<SingleContractContainer />);
    const agreementTab = await within(container).findByText(
      'Upload Agreement for signing'
    );
    fireEvent.click(agreementTab);
    await within(container).findByText('Agreement for signing');
    await within(container).findByText('Message to partner');
  });

  test('settings snapshot should match', async () => {
    setTab('settings');
    const { SOW: SingleContractContainer } = composeStories(stories);
    const { container } = render(<SingleContractContainer />);
    await within(container).findByText('Activity log');
    expect(
      await within(container).findByText('Contract settings')
    ).toBeVisible();

    expect(container).toMatchSnapshot();
  });

  test('SOW task with CF snapshot should match', async () => {
    setTab('details');
    const { SOW: SingleContractContainer } = composeStories(stories);
    const { container } = render(<SingleContractContainer />);
    expect(await screen.findByText('Contract details')).toBeVisible();
    expect(
      await within(container).findByText('Super short answer')
    ).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
});
