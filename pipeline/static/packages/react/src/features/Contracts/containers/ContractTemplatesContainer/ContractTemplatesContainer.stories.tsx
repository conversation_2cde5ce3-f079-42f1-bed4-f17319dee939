import templatesSearchResult from '../../../../fixtures/api/contracts/search/templates_page0.json';
import { getApiMockAdapter } from '../../../../utils/storybook-utils';
import ContractTemplatesState from '../../states/ContractTemplatesState';
import ContractTemplatesContainer from './ContractTemplatesContainer';
import React, { useEffect, useState } from 'react';

export default {
  title: 'Features/Contracts/Containers/ContractTemplatesContainer',
  component: ContractTemplatesContainer,
};

const Template = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  useEffect(() => {
    const mock = getApiMockAdapter();
    mock
      .onPost(/\/api\/search\/contract_templates\//gm)
      .reply(200, templatesSearchResult);
    mock.onGet(/\/api\/users\/current\/preferences\/.*\//gm).reply(200, {});
    mock.onPut(/\/api\/users\/current\/preferences\/.*\//gm).reply(200, {});
    setIsLoaded(true);
    return () => mock.reset();
  }, []);
  return isLoaded ? <ContractTemplatesState /> : null;
};

export const Default = () => Template();
