import AgreementTemplateSelect from '@Worksuite/Features/Contracts/components/AgreementTemplateSelect/AgreementTemplateSelect';
import { FlexWrapper } from '@Worksuite/Features/Contracts/containers/ContractWizardContainer/ContractWizardContainer.styles';
import { useContext, useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import FormContainer from '../../../../../components/Containers/FormContainer/FormContainer';
import { FormContainerItemType } from '../../../../../components/Containers/FormContainer/FormContainer.types';
import InputCheckbox from '../../../../../components/FormElements/InputCheckbox';
import InputRadioButtonGroup from '../../../../../components/FormElements/InputRadioButtonGroup/InputRadioButtonGroup';
import UserSelect from '../../../../../components/FormElements/UserSelect/UserSelect';
import HintBox from '../../../../../components/HintBox/HintBox';
import Spinner from '../../../../../components/Utils/Spinner/Spinner';
import i18n from '../../../../../i18n/i18n';
import Api from '../../../../../services/Api';
import { HeaderLarge, HeaderXLarge } from '../../../../../styles/typography';
import { ContractTemplate } from '../../../../../types/contracts';
import { SimpleUser } from '../../../../../types/user';
import { makeCancelable } from '../../../../../utils/other';
import {
  convertHintContentToHTML,
  getContractAcceptanceTypeOptions,
  getDefaultTeammates,
} from '../../contracts.helpers';
import { ContractTemplateEditorContext } from '../ContractTemplateEditor.context';
import {
  RadioGroupWrapper,
  SettingsFormWrapper,
  StyledAcceptanceHintBox,
} from '../ContractTemplateEditorContainer.styles';
import DisabledComplianceTemplateAlert from '../DisabledComplianceTemplateAlert';
import UsedTemplateAlert from '../UsedTemplateAlert';

const t = (id: string) => i18n.t(`Contracts:ContractAcceptance.${id}`);

const ContractAcceptanceForm = () => {
  const {
    templateData,
    setTemplateData,
    isTemplateDisabled,
    isComplianceDisabled,
    errors,
    setErrors,
  } = useContext(ContractTemplateEditorContext);
  const { setValue, control, watch } = useForm({ defaultValues: templateData });
  const [teammates, setTeammates] = useState<SimpleUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const subscription = watch((data: ContractTemplate) => {
      setTemplateData(data);
    });
    return () => subscription.unsubscribe();
  }, [templateData]);

  useEffect(() => {
    setErrors((prevState) => ({
      ...prevState,
      agreementTemplate:
        templateData.approval_type === 'agreement' &&
        !templateData.agreement_choose_before_signing &&
        !templateData.agreement_template,
    }));
  }, [
    templateData?.agreement_choose_before_signing,
    templateData?.agreement_template,
    templateData?.approval_type,
  ]);

  useEffect(() => {
    if (
      templateData.approval_type === 'acceptance' &&
      templateData.agreement_template
    ) {
      setValue('agreement_template', null);
    } else if (
      templateData.approval_type === 'agreement' &&
      templateData.signers?.length > 0
    ) {
      setValue('signers', []);
    }
  }, [templateData?.approval_type]);

  useEffect(() => {
    const cancelablePromise = makeCancelable(Api.Users.getUsers());
    cancelablePromise.promise
      .then(({ data }) => {
        setTeammates(data);
      })
      .finally(() => {
        setIsLoading(false);
      });
    return () => {
      cancelablePromise.cancel();
    };
  }, []);

  const acceptanceTypeItems: FormContainerItemType[] = [
    {
      id: 'acceptance-section',
      elements: [
        {
          id: 'acceptance-type-label',
          content: (
            <HeaderLarge>{t('ContractAcceptanceTypeLabel')}</HeaderLarge>
          ),
        },
      ],
    },
    {
      id: 'acceptance-type',
      elements: [
        {
          id: 'acceptance-type-radio',
          content: (
            <Controller
              render={({ field }) => (
                <RadioGroupWrapper>
                  <InputRadioButtonGroup
                    defaultValue={templateData.approval_type}
                    options={getContractAcceptanceTypeOptions()}
                    onChange={field.onChange}
                    disabled={isTemplateDisabled || isComplianceDisabled}
                  />
                </RadioGroupWrapper>
              )}
              name="approval_type"
              control={control}
            />
          ),
        },
      ],
      variant: 'hasDivider',
    },
  ];
  const acceptanceTypeSelectItems: FormContainerItemType[] = [
    {
      id: 'acceptance-type-select-header',
      elements: [
        {
          id: 'acceptance-type-select-label',
          content: (
            <HeaderLarge>
              {t(
                templateData.approval_type === 'agreement'
                  ? 'AgreementTemplate'
                  : 'AcceptanceOptionsLabel'
              )}
            </HeaderLarge>
          ),
        },
      ],
    },
    ...(templateData.approval_type === 'agreement'
      ? [
          {
            id: 'acceptance-agreement-template',
            elements: [
              {
                id: 'acceptance-agreement-template-select',
                content: (
                  <Controller
                    render={({ field }) => (
                      <AgreementTemplateSelect
                        onChange={({ agreementId, skipTemplateSelection }) => {
                          field.onChange(agreementId);
                          setValue(
                            'agreement_choose_before_signing',
                            skipTemplateSelection
                          );
                        }}
                        defaultValue={templateData.agreement_template}
                        disabled={isTemplateDisabled || isComplianceDisabled}
                        skipTemplateSelection={
                          templateData.agreement_choose_before_signing
                        }
                        showSettings
                        errors={
                          errors?.agreementTemplate
                            ? [i18n.t('Errors.ThisFieldIsRequired')]
                            : []
                        }
                      />
                    )}
                    name="agreement_template"
                    control={control}
                  />
                ),
              },
            ],
          },
        ]
      : [
          {
            id: 'accept-in-worksuite',
            elements: [
              {
                id: 'accept-in-worksuite-select',
                content: (
                  <Controller
                    render={({ field }) => (
                      <UserSelect
                        showInputOnDisabled={false}
                        multiChoice={true}
                        label={t('WhoShouldSignThisContract')}
                        defaultValues={getDefaultTeammates(
                          templateData.signers || [],
                          teammates
                        )}
                        placeholder={t('SearchForTeammates')}
                        users={teammates}
                        onChange={(data) => {
                          if (data.length > 0) {
                            field.onChange(
                              data.map((item) => {
                                if (typeof item === 'string') {
                                  return {
                                    type: item,
                                  };
                                } else {
                                  return {
                                    user: item,
                                    type: 'buyer',
                                  };
                                }
                              })
                            );
                          }
                        }}
                        disabled={isTemplateDisabled || isComplianceDisabled}
                      />
                    )}
                    name="signers"
                    control={control}
                  />
                ),
              },
            ],
          },
        ]),
  ];

  const agreementSigningOptionItems: FormContainerItemType[] = [
    {
      id: 'signing-options-header',
      elements: [
        {
          id: 'signing-options-label',
          content: (
            <HeaderLarge>{t('AgreementSigningOptionsLabel')}</HeaderLarge>
          ),
        },
      ],
    },
    {
      id: 'acceptance-agreement-enable-preview',
      elements: [
        {
          id: 'enable-preview-checkbox',
          content: (
            <Controller
              render={({ field }) => (
                <InputCheckbox
                  label={t('EnablePreviewButton')}
                  checked={field.value}
                  onChange={field.onChange}
                  disabled={
                    isTemplateDisabled ||
                    isComplianceDisabled ||
                    (!templateData?.agreement_template &&
                      !templateData?.agreement_choose_before_signing)
                  }
                />
              )}
              name="agreement_preview_before_signing"
              control={control}
            />
          ),
        },
      ],
    },
  ];

  return (
    <FlexWrapper>
      <HeaderXLarge>{t('ContractAcceptanceHeader')}</HeaderXLarge>

      <StyledAcceptanceHintBox>
        {isTemplateDisabled ? (
          <UsedTemplateAlert />
        ) : isComplianceDisabled ? (
          <DisabledComplianceTemplateAlert />
        ) : (
          <HintBox title={t('Hint.Header')}>
            {convertHintContentToHTML(t('Hint.Description'))}
          </HintBox>
        )}
      </StyledAcceptanceHintBox>

      {isLoading ? (
        <Spinner />
      ) : (
        <SettingsFormWrapper>
          <FormContainer items={acceptanceTypeItems} />
          <FormContainer items={acceptanceTypeSelectItems} />
          {templateData.approval_type === 'agreement' && (
            <FormContainer items={agreementSigningOptionItems} />
          )}
        </SettingsFormWrapper>
      )}
    </FlexWrapper>
  );
};

export default ContractAcceptanceForm;
