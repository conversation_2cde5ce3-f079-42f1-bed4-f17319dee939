import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { PayoutMethodPayoneerIcon } from '../../../../assets/icons/misc';
import { BankDetailsIcon } from '../../../../assets/icons/small';
import Button from '../../../../components/FormElements/Button/Button';
import i18n from '../../../../i18n/i18n';
import { goToAngularState } from '../../../../services/Reducers/AngularMigration.helpers';
import { maskSensitiveData } from '../../../../utils/html';
import { generateTestId } from '../../../../utils/test.utils';
import PayoutMethodsApi from '../../services/PayoutMethods';
import {
  Body,
  BodyLarge,
  GrayBody,
  HeaderLarge,
  PrimaryStatus,
} from '../../styles/PayoutMethods.styles';
import { PayoutMethodTileProps } from '../../types/PayoutMethods.types';
import {
  getStatusLiteral,
  iconPerStatus,
  showMakePrimary,
} from '../../utils/PayoutMethods.helpers';
import {
  BankTransferTitle,
  ButtonRow,
  DetailsContainer,
  PayoutStatus,
  PayoutStatusLabel,
  Row,
  TileContainer,
} from './PayoutMethodTile.styles';

export const PayoutMethodTile: React.FC<PayoutMethodTileProps> = (props) => {
  const { t } = useTranslation('PayoutMethods');
  const [loading, setLoading] = useState(false);
  const { methodType, paymentProcessorId, setErrorMessage } = props;
  const isPrimary = 'isPrimary' in props ? props.isPrimary : false;
  const isZeroState = !('status' in props) || 'registrationLink' in props;

  const getDetailsComponent = () => {
    if (!isZeroState) {
      const { currency, status } = props;
      const statusLiteral = getStatusLiteral(props);
      return (
        <>
          <Row>
            <BodyLarge {...generateTestId('Account', 'PayoutMethodTile')}>
              {props.methodType === 'BankTransfer' && props.bankAccountName}
              {props.methodType === 'Payoneer' && props.accountId}
            </BodyLarge>
            <GrayBody {...generateTestId('Currency', 'PayoutMethodTile')}>
              {currency}
            </GrayBody>
          </Row>
          <Row>
            {'additionalData' in props && (
              <GrayBody
                {...generateTestId('AdditionalData', 'PayoutMethodTile')}
              >
                {props.additionalData.isSensitive
                  ? maskSensitiveData(props.additionalData.value)
                  : props.additionalData.value}
              </GrayBody>
            )}
            {i18n.exists(`PayoutMethods:Statuses.${statusLiteral}`) && (
              <PayoutStatus
                {...generateTestId(statusLiteral, 'PayoutMethodTile')}
              >
                {iconPerStatus[status]}
                <PayoutStatusLabel status={status}>
                  {t(`Statuses.${statusLiteral}`)}
                </PayoutStatusLabel>
              </PayoutStatus>
            )}
          </Row>
        </>
      );
    }
    return <Body>{t(`PayoutMethods.${methodType}.zeroState`)}</Body>;
  };

  const getButtonComponent = () => {
    if (!isZeroState) {
      const { status, disabledMakePrimary, onSetPrimaryMethod, id } = props;

      const handleSeeDetails = () => {
        if (methodType === 'Payoneer') {
          props.onOpenDetails();
          return;
        }

        goToAngularState('app.company-profile.bank-details');
      };

      const handleSetPrimaryMethod = async (
        e: React.MouseEvent<HTMLButtonElement>
      ) => {
        if (e.currentTarget && !e.currentTarget.disabled) {
          e.currentTarget.disabled = true;
          await onSetPrimaryMethod(id);
        }
      };

      return (
        <>
          <Button
            style={{ flex: 1 }}
            onClick={handleSeeDetails}
            variant={status === 'rejected' ? 'secondary' : 'tertiary'}
            rounded={false}
            color={status === 'rejected' ? 'red' : 'blue'}
            label={t(
              `Buttons.${
                status === 'rejected' ? 'reviewDetails' : 'seeDetails'
              }`
            )}
            testId={
              generateTestId(`DetailsButton-${status}`, 'PayoutMethodTile')[
                'data-testid'
              ]
            }
          />

          {showMakePrimary(isPrimary, status) && (
            <Button
              style={{ flex: 1 }}
              onClick={handleSetPrimaryMethod}
              variant="secondary"
              rounded={false}
              disabled={disabledMakePrimary}
              color="blue"
              label={t(`Buttons.makePrimary`)}
              testId={
                generateTestId('MakePrimary', 'PayoutMethodTile')['data-testid']
              }
            />
          )}
        </>
      );
    }

    const handleSetUp = async () => {
      if (methodType === 'Payoneer') {
        setLoading(true);
        let link = props.registrationLink;
        if (!link) {
          try {
            const {
              data: {
                schema_data: { registration_link },
              },
            } = await PayoutMethodsApi.createPayoutMethod(paymentProcessorId);
            link = registration_link;
          } catch (error) {
            const message = error.data?.['__all__']?.join(' ');
            setErrorMessage(message);
            setLoading(false);
            return;
          }
        }
        setTimeout(() => {
          window.location.replace(link);
          setLoading(false);
        }, 200);
        return;
      }

      goToAngularState('app.company-profile.bank-details');
    };

    return (
      <Button
        style={{ flex: 1 }}
        variant="secondary"
        rounded={false}
        color="blue"
        label={t(`PayoutMethods.${methodType}.setUpButton`)}
        onClick={handleSetUp}
        testId={
          generateTestId('SetUpButton', 'PayoutMethodTile')['data-testid']
        }
        loading={loading}
      />
    );
  };

  const getMethodTypeComponent = () => {
    const defaultComponent = (
      <BankTransferTitle>
        <BankDetailsIcon
          size={32}
          fill="var(--actions-basic-actions-action-base)"
        />
        <HeaderLarge>{t('PayoutMethods.BankTransfer.title')}</HeaderLarge>
      </BankTransferTitle>
    );
    switch (methodType) {
      case 'BankTransfer':
        return defaultComponent;
      case 'Payoneer':
        return <PayoutMethodPayoneerIcon size={124} height={24} />;
      default:
        return defaultComponent;
    }
  };

  return (
    <TileContainer
      isPrimary={isPrimary}
      {...generateTestId('Container', 'PayoutMethodTile')}
    >
      <Row>
        {getMethodTypeComponent()}
        {isPrimary && (
          <PrimaryStatus {...generateTestId('Primary', 'PayoutMethodTile')}>
            {t('PrimaryStatus')}
          </PrimaryStatus>
        )}
      </Row>
      <DetailsContainer>{getDetailsComponent()}</DetailsContainer>
      <ButtonRow>{getButtonComponent()}</ButtonRow>
    </TileContainer>
  );
};
