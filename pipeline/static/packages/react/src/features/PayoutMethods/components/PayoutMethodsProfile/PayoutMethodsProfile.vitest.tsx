import * as stories from './PayoutMethodsProfile.stories';
import { composeStories } from '@storybook/react';
import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import React from 'react';

const composedStories = composeStories(stories);

describe('PayoutMethodsProfile', () => {
  test.each(Object.entries(composedStories))(
    'snapshot should match: %s',
    async (key, StoryItem) => {
      const { container } = render(<StoryItem />);

      if (key === 'OnlyPayoneer') {
        await screen.findAllByText('Payoneer');
      } else {
        await screen.findAllByText('Bank Transfer');
      }

      expect(container).toMatchSnapshot();
    }
  );
});
