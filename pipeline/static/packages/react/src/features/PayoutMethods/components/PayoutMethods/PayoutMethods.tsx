import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import Alert from '../../../../components/Alert/Alert';
import { openModal } from '../../../../components/Utils/Modals/modal.helpers';
import Spinner from '../../../../components/Utils/Spinner/Spinner';
import PaymentsApi from '../../../../services/Api/Payments';
import { RootState, withRedux } from '../../../../services/Redux';
import { useAsync } from '../../../../utils/hooks/useAsync';
import { generateTestId } from '../../../../utils/test.utils';
import PayoutMethodsApi from '../../services/PayoutMethods';
import { Body } from '../../styles/PayoutMethods.styles';
import {
  PayoutMethodTileProps,
  PayoutMethodTileType,
} from '../../types/PayoutMethods.types';
import {
  getAlertProps,
  getBankTransferProps,
  getPayoneerProps,
  t,
} from '../../utils/PayoutMethods.helpers';
import { DetailsSidepanel } from '../DetailsSidepanel/DetailsSidepanel';
import { PayoutMethodTile } from '../PayoutMethodTile/PayoutMethodTile';
import {
  AlertContainer,
  Container,
  PayoutMethodsContainer,
  SectionTitle,
} from './PayoutMethods.styles';

const PayoutMethods: React.FC = () => {
  const [visibleDetails, setVisibleDetails] = useState<PayoutMethodTileType>();
  const vendorSlug = useSelector(
    (state: RootState) => state.user.userData?.vendor?.slug
  );
  const [reloadData, setReloadData] = useState(false);
  const [errorMessage, setErrorMessage] = useState();

  const handleSetPrimaryMethod = async (id: string) => {
    try {
      await openModal({
        type: 'primary_payout_method_change_modal',
        onSetPrimaryMethod: async () => {
          await PayoutMethodsApi.setPrimaryMethod(id);
          setReloadData((state) => !state);
        },
      });
    } catch (error) {}
  };

  const handleCloseDetails = () => setVisibleDetails(undefined);

  const handleRemovePayoutMethod = async (id: string) => {
    handleCloseDetails();
    await PayoutMethodsApi.deletePayoutMethod(id);
    setReloadData((state) => !state);
  };

  const {
    isLoading,
    isError,
    data: { tilesProps = [], alertProps, payoutMethods = [] },
  } = useAsync(
    {
      promise: () =>
        Promise.all([
          PayoutMethodsApi.getPayoutMethodsByVendor(vendorSlug),
          PaymentsApi.getPaymentProcessors(),
        ]),
      onResolve: ([{ data: payoutMethods }, { data: paymentProcessors }]) => {
        if (paymentProcessors.length === 0) {
          return { tilesProps: [] };
        }
        const tilesProps: PayoutMethodTileProps[] = [];

        const bankTransferProps = getBankTransferProps(
          payoutMethods,
          paymentProcessors,
          handleSetPrimaryMethod
        );
        if (bankTransferProps) {
          tilesProps.push(bankTransferProps);
        }

        const payoneerProps = getPayoneerProps(
          payoutMethods,
          paymentProcessors,
          handleSetPrimaryMethod,
          () => setVisibleDetails('Payoneer')
        );
        if (payoneerProps) {
          tilesProps.push(payoneerProps);
        }

        const alertProps = getAlertProps(payoutMethods);

        return { tilesProps, alertProps, payoutMethods };
      },
    },
    [reloadData]
  );

  const payoutMethodDetails = tilesProps.find(
    ({ methodType }) => methodType === visibleDetails
  );

  const getMainContentComponent = () => {
    if (isLoading) {
      return <Spinner />;
    } else if (isError || tilesProps.length === 0 || errorMessage) {
      return (
        <Alert
          iconString="BankDetailsErrorIcon"
          message={errorMessage ?? t('Error')}
          tag="alert-danger"
          size="large"
        />
      );
    }
    return (
      <>
        {tilesProps.length > 1 && <Body>{t('ChoosePayoutMethod')}</Body>}
        {alertProps && (
          <AlertContainer>
            <Alert
              {...alertProps}
              iconString="BankDetailsErrorIcon"
              size="large"
            />
          </AlertContainer>
        )}
        <PayoutMethodsContainer
          {...generateTestId('PayoutMethodsContainer', 'PayoutMethods')}
        >
          {tilesProps.map((tileProps) => (
            <PayoutMethodTile
              key={tileProps.methodType}
              {...tileProps}
              setErrorMessage={setErrorMessage}
            />
          ))}
        </PayoutMethodsContainer>
      </>
    );
  };

  return (
    <Container>
      <DetailsSidepanel
        handleClose={handleCloseDetails}
        visibleDetails={visibleDetails}
        payoutMethod={payoutMethodDetails}
        payoutMethods={payoutMethods}
        onRemove={handleRemovePayoutMethod}
      />
      <SectionTitle>{t('Title')}</SectionTitle>
      {getMainContentComponent()}
    </Container>
  );
};

const PayoutMethodsWithRedux = withRedux(PayoutMethods);

export default PayoutMethodsWithRedux;
