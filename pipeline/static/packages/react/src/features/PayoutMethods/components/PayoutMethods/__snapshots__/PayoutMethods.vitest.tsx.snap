// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`PayoutMethods > renders inactive Payoneer details 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
  margin-bottom: var(--spacing-xl);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-top: var(--spacing-m);
  gap: var(--spacing-l);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
  border: var(--spacing-2xs) solid var(--actions-subtle-actions-action-subtle-selected);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  max-height: 32px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--spacing-s);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 10px;
  line-height: 20px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-s);
  border-radius: 2px;
  background: var(--statuses-status-active);
  color: var(--glyphs-basic-glyphs-inverted);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  color: var(--glyphs-basic-glyphs-base);
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--spacing-2xs);
  margin-left: auto;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: var(--statuses-status-active);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  gap: var(--spacing-m);
  height: 30px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: var(--statuses-status-inactive);
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: #FFFFFF;
  border: 1px solid var(--action-negative);
  padding-top: 0;
  padding-right: 19px;
  padding-bottom: 0;
  padding-left: 19px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-negative);
}

.emotion-class span {
  line-height: 33px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-negative);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  position: fixed;
  z-index: 1300;
  right: 0;
  bottom: 0;
  top: 0;
  left: 0;
  pointer-events: none;
}

.emotion-class .MuiModal-backdrop {
  background-color: transparent;
  pointer-events: none;
}

.emotion-class {
  position: fixed;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  right: 0;
  bottom: 0;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  -webkit-tap-highlight-color: transparent;
  z-index: -1;
}

.emotion-class {
  pointer-events: auto;
  background-color: #FFFFFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: absolute;
  width: 714px;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  top: 0;
  right: 0;
  outline: none;
  box-shadow: 15px 0px 10px 15px rgb(7 10 25 / 50%);
}

@media (max-width: 768px) {
  .emotion-class {
    width: 100%;
  }
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  padding: var(--spacing-xl) var(--spacing-xl) 0 var(--spacing-xl);
  min-height: -webkit-fit-content;
  min-height: -moz-fit-content;
  min-height: fit-content;
  box-sizing: border-box;
  position: relative;
  z-index: 100;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--spacing-m);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
}

.emotion-class {
  position: relative;
}

.emotion-class svg {
  fill: #B4BCE0;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class:hover svg {
  fill: #465AB6;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
  padding: var(--spacing-xl) var(--spacing-l) var(--spacing-m);
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  background-color: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 6px 16px;
  color: rgb(30, 70, 32);
  border: 1px solid #4caf50;
}

.emotion-class .MuiAlert-icon {
  color: #2e7d32;
}

.emotion-class.MuiAlert-root {
  width: 100%;
  max-width: 100%;
  color: #303757;
  background-color: #FFF2F3;
  border-color: #FFBFC5;
  font-family: "Open Sans",sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  padding: 15px 10px 15px 20px;
  margin: 0;
  box-sizing: border-box;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class.MuiAlert-root .MuiAlert-icon {
  padding: 0;
  margin: 0 4px 0 0;
}

.emotion-class.MuiAlert-root .MuiAlert-message {
  padding: 0;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.emotion-class.MuiAlert-root a {
  color: #465AB6;
}

@media (max-width: 768px) {
  .emotion-class.MuiAlert-root {
    padding: 20px;
  }
}

.emotion-class {
  margin-right: 12px;
  padding: 7px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 22px;
  opacity: 0.9;
}

.emotion-class {
  padding: 8px 0;
  min-width: 0;
  overflow: auto;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class>:first-of-type {
  padding: 0;
}

@media (max-width: 768px) {
  .emotion-class {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: start;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: start;
  }

  .emotion-class div {
    margin: 15px 0 0 0;
  }
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-s-plus);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: 50px;
  padding: var(--spacing-xl);
  margin-top: auto;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: #FFFFFF;
  border: 1px solid var(--action-negative);
  padding-top: 0;
  padding-right: 19px;
  padding-bottom: 0;
  padding-left: 19px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-negative);
}

.emotion-class span {
  line-height: 33px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

<body
    style="padding-right: 1024px; overflow: hidden;"
  >
    <div
      aria-hidden="true"
      id="modal-portal"
    />
    <div
      aria-hidden="true"
    >
      <div
        class="emotion-class"
      >
        <h3
          class="emotion-class"
        >
          Payout methods
        </h3>
        <p
          class="emotion-class"
        >
          Choose your primary payout method.
        </p>
        <div
          class="emotion-class"
          data-testid="PayoutMethods_PayoutMethodsContainer"
        >
          <div
            class="emotion-class"
            data-testid="PayoutMethodTile_Container"
          >
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
              >
                <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m13%2036.5c11.3431%2036.5%2010%2035.1569%2010%2033.5v14.5c10%2012.8431%2011.3431%2011.5%2013%2011.5h35c36.6569%2011.5%2038%2012.8431%2038%2014.5v18.5h29.75c26.5744%2018.5%2024%2021.0744%2024%2024.25c24%2027.4256%2026.5744%2030%2029.75%2030h38v33.5c38%2035.1569%2036.6569%2036.5%2035%2036.5h13zm29%2028c27.3431%2028%2026%2026.6569%2026%2025v23.5c26%2021.8431%2027.3431%2020.5%2029%2020.5h38v28h29zm32.5%2024.25c32.5%2025.4926%2031.4926%2026.5%2030.25%2026.5c29.0074%2026.5%2028%2025.4926%2028%2024.25c28%2023.0074%2029.0074%2022%2030.25%2022c31.4926%2022%2032.5%2023.0074%2032.5%2024.25z'%20/%3e%3c/svg%3e
                  classname="css-10a16n8"
                  fill="var(--actions-basic-actions-action-base)"
                  size="32"
                />
                <h5
                  class="emotion-class"
                >
                  Bank transfer
                </h5>
              </div>
              <p
                class="emotion-class"
                data-testid="PayoutMethodTile_Primary"
              >
                primary
              </p>
            </div>
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
              >
                <p
                  class="emotion-class"
                  data-testid="PayoutMethodTile_Account"
                >
                  Ewela Asdd
                </p>
                <p
                  class="emotion-class"
                  data-testid="PayoutMethodTile_Currency"
                >
                  PLN
                </p>
              </div>
              <div
                class="emotion-class"
              >
                <p
                  class="emotion-class"
                  data-testid="PayoutMethodTile_AdditionalData"
                >
                  ************5196
                </p>
                <div
                  class="emotion-class"
                  data-testid="PayoutMethodTile_Verified"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                    classname="css-8sv2oy"
                    fill="var(--statuses-status-active)"
                    size="24"
                  />
                  <span
                    class="emotion-class"
                  >
                    Verified
                  </span>
                </div>
              </div>
            </div>
            <div
              class="emotion-class"
            >
              <button
                class=" emotion-class"
                data-testid="PayoutMethodTile_DetailsButtonActive"
                style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
              >
                <span
                  class="emotion-class"
                >
                  See details
                </span>
              </button>
            </div>
          </div>
          <div
            class="emotion-class"
            data-testid="PayoutMethodTile_Container"
          >
            <div
              class="emotion-class"
            >
              </src/assets/icons/misc/payout-method-payoneer.svg
                classname="css-1b2xl8c"
                height="24"
                size="124"
              />
            </div>
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
              >
                <p
                  class="emotion-class"
                  data-testid="PayoutMethodTile_Account"
                />
                <p
                  class="emotion-class"
                  data-testid="PayoutMethodTile_Currency"
                />
              </div>
              <div
                class="emotion-class"
              >
                <div
                  class="emotion-class"
                  data-testid="PayoutMethodTile_Inactive"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9.5%2023.9167c9.5%2031.8917%2016.025%2038.4167%2024%2038.4167c31.975%2038.4167%2038.5%2031.8917%2038.5%2023.9167c38.5%2015.9417%2031.975%209.41669%2024%209.41669c16.025%209.41669%209.5%2015.9417%209.5%2023.9167zm26.4997%2014.4167v17.9167l25.4997%2025.9167h22.4997l21.4997%2017.9167v14.4167h26.4997zm22.2242%2028.6411c21.7412%2029.1241%2021.4997%2029.7159%2021.4997%2030.4167c21.4997%2031.1175%2021.7412%2031.7093%2022.2242%2032.1923c22.7071%2032.6752%2023.299%2032.9167%2023.9997%2032.9167c24.7005%2032.9167%2025.2924%2032.6752%2025.7753%2032.1923c26.2583%2031.7093%2026.4997%2031.1175%2026.4997%2030.4167c26.4997%2029.7159%2026.2583%2029.1241%2025.7753%2028.6411c25.2924%2028.1582%2024.7005%2027.9167%2023.9997%2027.9167c23.299%2027.9167%2022.7071%2028.1582%2022.2242%2028.6411z'%20/%3e%3c/svg%3e
                    classname="css-1s425qw"
                    fill="var(--statuses-status-inactive)"
                    size="24"
                  />
                  <span
                    class="emotion-class"
                  >
                    Inactive
                  </span>
                </div>
              </div>
            </div>
            <div
              class="emotion-class"
            >
              <button
                class=" emotion-class"
                data-testid="PayoutMethodTile_DetailsButtonRejected"
                style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
              >
                <span
                  class="emotion-class"
                >
                  Review details
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="MuiModal-root emotion-class"
      role="presentation"
    >
      <div
        aria-hidden="true"
        class="MuiBackdrop-root MuiModal-backdrop emotion-class"
        style="opacity: 1; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
      />
      <div
        data-testid="sentinelStart"
        tabindex="0"
      />
      <div
        class="emotion-class"
        style="transform: none; transition: transform 225ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;"
        tabindex="-1"
        width="714"
      >
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <h3
              class="emotion-class"
            >
              Payoneer details
            </h3>
          </div>
          <a
            class=" emotion-class"
            data-testid="SidePanel_CloseButton"
            style="margin-left: auto;"
          >
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m21.5%2024l13%2032.5l15.5%2035l24%2026.5l32.5%2035l35%2032.5l26.5%2024l35%2015.5l32.5%2013l24%2021.5l15.5%2013l13%2015.5l21.5%2024z'/%3e%3c/svg%3e
              classname="css-10pudoj"
              size="30"
            />
          </a>
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 MuiAlert-root MuiAlert-colorSuccess MuiAlert-outlinedSuccess MuiAlert-outlined emotion-class"
            role="alert"
            style="--Paper-shadow: none;"
          >
            <div
              class="MuiAlert-icon emotion-class"
            >
              <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m9.99951%2033.5c9.99951%2035.1569%2011.3427%2036.5%2012.9995%2036.5h28.5126c28.5039%2036.3344%2028.4995%2036.1677%2028.4995%2036c28.4995%2033.7308%2029.3081%2031.6394%2030.651%2030h26.9995c25.3427%2030%2023.9995%2028.6569%2023.9995%2027v21.5c23.9995%2019.8431%2025.3427%2018.5%2026.9995%2018.5h37.9995v14.5c37.9995%2012.8431%2036.6564%2011.5%2034.9995%2011.5h12.9995c11.3427%2011.5%209.99951%2012.8431%209.99951%2014.5v33.5z'%20/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37.9995%2026.5v20.5h27.4995c26.6711%2020.5%2025.9995%2021.1716%2025.9995%2022v26.5c25.9995%2027.3284%2026.6711%2028%2027.4995%2028h32.8956c34.3727%2027.0515%2036.1255%2026.5%2037.9995%2026.5zm30.2495%2026.5c31.4922%2026.5%2032.4995%2025.4926%2032.4995%2024.25c32.4995%2023.0074%2031.4922%2022%2030.2495%2022c29.0069%2022%2027.9995%2023.0074%2027.9995%2024.25c27.9995%2025.4926%2029.0069%2026.5%2030.2495%2026.5z'%20/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m29.9995%2036c29.9995%2031.6%2033.5995%2028%2037.9995%2028c42.3995%2028%2045.9995%2031.6%2045.9995%2036c45.9995%2040.4%2042.3995%2044%2037.9995%2044c33.5995%2044%2029.9995%2040.4%2029.9995%2036zm37.9464%2034.6766l41.276%2031.3469l42.665%2032.7359l39.3353%2036.0655l42.4604%2039.1906l40.9806%2040.6704l37.8555%2037.5453l34.5259%2040.8749l33.137%2039.486l36.4666%2036.1564l33.3415%2033.0313l34.8213%2031.5515l37.9464%2034.6766z'%20/%3e%3c/svg%3e
                classname="css-1pafx7u"
                fill="#FF7F8A"
                fontsize="inherit"
                size="30"
              />
            </div>
            <div
              class="MuiAlert-message emotion-class"
            >
              <div
                class="emotion-class"
              >
                <div>
                  Payoneer account status: inactive.
                </div>
              </div>
            </div>
          </div>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <div>
                <span
                  class="emotion-class"
                >
                  Payoneer User ID
                </span>
                <p
                  class="emotion-class"
                  data-testid="PayoneerDetails_UserId"
                >
                  -
                </p>
              </div>
              <div>
                <span
                  class="emotion-class"
                >
                  Invoice Currency
                </span>
                <p
                  class="emotion-class"
                  data-testid="PayoneerDetails_Currency"
                >
                  -
                </p>
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <button
              class=" emotion-class"
              data-testid="PayoutMethodTile_RemovePayoutMethod"
            >
              <span
                class="emotion-class"
              >
                Remove this method
              </span>
            </button>
          </div>
        </div>
      </div>
      <div
        data-testid="sentinelEnd"
        tabindex="0"
      />
    </div>
  </body>,
  "container": .emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
  margin-bottom: var(--spacing-xl);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-top: var(--spacing-m);
  gap: var(--spacing-l);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
  border: var(--spacing-2xs) solid var(--actions-subtle-actions-action-subtle-selected);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  max-height: 32px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--spacing-s);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 10px;
  line-height: 20px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-s);
  border-radius: 2px;
  background: var(--statuses-status-active);
  color: var(--glyphs-basic-glyphs-inverted);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  color: var(--glyphs-basic-glyphs-base);
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--spacing-2xs);
  margin-left: auto;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: var(--statuses-status-active);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  gap: var(--spacing-m);
  height: 30px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: var(--statuses-status-inactive);
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: #FFFFFF;
  border: 1px solid var(--action-negative);
  padding-top: 0;
  padding-right: 19px;
  padding-bottom: 0;
  padding-left: 19px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-negative);
}

.emotion-class span {
  line-height: 33px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-negative);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div
    aria-hidden="true"
  >
    <div
      class="emotion-class"
    >
      <h3
        class="emotion-class"
      >
        Payout methods
      </h3>
      <p
        class="emotion-class"
      >
        Choose your primary payout method.
      </p>
      <div
        class="emotion-class"
        data-testid="PayoutMethods_PayoutMethodsContainer"
      >
        <div
          class="emotion-class"
          data-testid="PayoutMethodTile_Container"
        >
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m13%2036.5c11.3431%2036.5%2010%2035.1569%2010%2033.5v14.5c10%2012.8431%2011.3431%2011.5%2013%2011.5h35c36.6569%2011.5%2038%2012.8431%2038%2014.5v18.5h29.75c26.5744%2018.5%2024%2021.0744%2024%2024.25c24%2027.4256%2026.5744%2030%2029.75%2030h38v33.5c38%2035.1569%2036.6569%2036.5%2035%2036.5h13zm29%2028c27.3431%2028%2026%2026.6569%2026%2025v23.5c26%2021.8431%2027.3431%2020.5%2029%2020.5h38v28h29zm32.5%2024.25c32.5%2025.4926%2031.4926%2026.5%2030.25%2026.5c29.0074%2026.5%2028%2025.4926%2028%2024.25c28%2023.0074%2029.0074%2022%2030.25%2022c31.4926%2022%2032.5%2023.0074%2032.5%2024.25z'%20/%3e%3c/svg%3e
                classname="css-10a16n8"
                fill="var(--actions-basic-actions-action-base)"
                size="32"
              />
              <h5
                class="emotion-class"
              >
                Bank transfer
              </h5>
            </div>
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Primary"
            >
              primary
            </p>
          </div>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <p
                class="emotion-class"
                data-testid="PayoutMethodTile_Account"
              >
                Ewela Asdd
              </p>
              <p
                class="emotion-class"
                data-testid="PayoutMethodTile_Currency"
              >
                PLN
              </p>
            </div>
            <div
              class="emotion-class"
            >
              <p
                class="emotion-class"
                data-testid="PayoutMethodTile_AdditionalData"
              >
                ************5196
              </p>
              <div
                class="emotion-class"
                data-testid="PayoutMethodTile_Verified"
              >
                <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                  classname="css-8sv2oy"
                  fill="var(--statuses-status-active)"
                  size="24"
                />
                <span
                  class="emotion-class"
                >
                  Verified
                </span>
              </div>
            </div>
          </div>
          <div
            class="emotion-class"
          >
            <button
              class=" emotion-class"
              data-testid="PayoutMethodTile_DetailsButtonActive"
              style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
            >
              <span
                class="emotion-class"
              >
                See details
              </span>
            </button>
          </div>
        </div>
        <div
          class="emotion-class"
          data-testid="PayoutMethodTile_Container"
        >
          <div
            class="emotion-class"
          >
            </src/assets/icons/misc/payout-method-payoneer.svg
              classname="css-1b2xl8c"
              height="24"
              size="124"
            />
          </div>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <p
                class="emotion-class"
                data-testid="PayoutMethodTile_Account"
              />
              <p
                class="emotion-class"
                data-testid="PayoutMethodTile_Currency"
              />
            </div>
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                data-testid="PayoutMethodTile_Inactive"
              >
                <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9.5%2023.9167c9.5%2031.8917%2016.025%2038.4167%2024%2038.4167c31.975%2038.4167%2038.5%2031.8917%2038.5%2023.9167c38.5%2015.9417%2031.975%209.41669%2024%209.41669c16.025%209.41669%209.5%2015.9417%209.5%2023.9167zm26.4997%2014.4167v17.9167l25.4997%2025.9167h22.4997l21.4997%2017.9167v14.4167h26.4997zm22.2242%2028.6411c21.7412%2029.1241%2021.4997%2029.7159%2021.4997%2030.4167c21.4997%2031.1175%2021.7412%2031.7093%2022.2242%2032.1923c22.7071%2032.6752%2023.299%2032.9167%2023.9997%2032.9167c24.7005%2032.9167%2025.2924%2032.6752%2025.7753%2032.1923c26.2583%2031.7093%2026.4997%2031.1175%2026.4997%2030.4167c26.4997%2029.7159%2026.2583%2029.1241%2025.7753%2028.6411c25.2924%2028.1582%2024.7005%2027.9167%2023.9997%2027.9167c23.299%2027.9167%2022.7071%2028.1582%2022.2242%2028.6411z'%20/%3e%3c/svg%3e
                  classname="css-1s425qw"
                  fill="var(--statuses-status-inactive)"
                  size="24"
                />
                <span
                  class="emotion-class"
                >
                  Inactive
                </span>
              </div>
            </div>
          </div>
          <div
            class="emotion-class"
          >
            <button
              class=" emotion-class"
              data-testid="PayoutMethodTile_DetailsButtonRejected"
              style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
            >
              <span
                class="emotion-class"
              >
                Review details
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`PayoutMethods > renders primary Payoneer details 1`] = `
{
  "asFragment": [Function],
  "baseElement": .emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
  margin-bottom: var(--spacing-xl);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-top: var(--spacing-m);
  gap: var(--spacing-l);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  max-height: 32px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--spacing-s);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  color: var(--glyphs-basic-glyphs-base);
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--spacing-2xs);
  margin-left: auto;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: var(--statuses-status-active);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  gap: var(--spacing-m);
  height: 30px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: #F7F7F7;
  border: 1px solid var(--action-base);
  padding-top: 0;
  padding-right: 19px;
  padding-bottom: 0;
  padding-left: 19px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 33px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
  border: var(--spacing-2xs) solid var(--actions-subtle-actions-action-subtle-selected);
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 10px;
  line-height: 20px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-s);
  border-radius: 2px;
  background: var(--statuses-status-active);
  color: var(--glyphs-basic-glyphs-inverted);
}

.emotion-class {
  position: fixed;
  z-index: 1300;
  right: 0;
  bottom: 0;
  top: 0;
  left: 0;
  pointer-events: none;
}

.emotion-class .MuiModal-backdrop {
  background-color: transparent;
  pointer-events: none;
}

.emotion-class {
  position: fixed;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  right: 0;
  bottom: 0;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  -webkit-tap-highlight-color: transparent;
  z-index: -1;
}

.emotion-class {
  pointer-events: auto;
  background-color: #FFFFFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: absolute;
  width: 714px;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  top: 0;
  right: 0;
  outline: none;
  box-shadow: 15px 0px 10px 15px rgb(7 10 25 / 50%);
}

@media (max-width: 768px) {
  .emotion-class {
    width: 100%;
  }
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  padding: var(--spacing-xl) var(--spacing-xl) 0 var(--spacing-xl);
  min-height: -webkit-fit-content;
  min-height: -moz-fit-content;
  min-height: fit-content;
  box-sizing: border-box;
  position: relative;
  z-index: 100;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--spacing-m);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
}

.emotion-class {
  position: relative;
}

.emotion-class svg {
  fill: #B4BCE0;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class:hover svg {
  fill: #465AB6;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
  padding: var(--spacing-xl) var(--spacing-l) var(--spacing-m);
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  background-color: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 6px 16px;
  color: rgb(30, 70, 32);
  border: 1px solid #4caf50;
}

.emotion-class .MuiAlert-icon {
  color: #2e7d32;
}

.emotion-class.MuiAlert-root {
  width: 100%;
  max-width: 100%;
  color: #303757;
  background-color: #E3FAF7;
  border-color: #82E5C2;
  font-family: "Open Sans",sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  padding: 15px 10px 15px 20px;
  margin: 0;
  box-sizing: border-box;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class.MuiAlert-root .MuiAlert-icon {
  padding: 0;
  margin: 0 4px 0 0;
}

.emotion-class.MuiAlert-root .MuiAlert-message {
  padding: 0;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.emotion-class.MuiAlert-root a {
  color: #465AB6;
}

@media (max-width: 768px) {
  .emotion-class.MuiAlert-root {
    padding: 20px;
  }
}

.emotion-class {
  margin-right: 12px;
  padding: 7px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 22px;
  opacity: 0.9;
}

.emotion-class {
  padding: 8px 0;
  min-width: 0;
  overflow: auto;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class>:first-of-type {
  padding: 0;
}

@media (max-width: 768px) {
  .emotion-class {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: start;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: start;
  }

  .emotion-class div {
    margin: 15px 0 0 0;
  }
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-s-plus);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: 50px;
  padding: var(--spacing-xl);
  margin-top: auto;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: #FFFFFF;
  border: 1px solid var(--action-negative);
  padding-top: 0;
  padding-right: 19px;
  padding-bottom: 0;
  padding-left: 19px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-negative);
}

.emotion-class span {
  line-height: 33px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-negative);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<body
    style="padding-right: 1024px; overflow: hidden;"
  >
    <div
      aria-hidden="true"
      id="modal-portal"
    />
    <div
      aria-hidden="true"
    >
      <div
        class="emotion-class"
      >
        <h3
          class="emotion-class"
        >
          Payout methods
        </h3>
        <p
          class="emotion-class"
        >
          Choose your primary payout method.
        </p>
        <div
          class="emotion-class"
          data-testid="PayoutMethods_PayoutMethodsContainer"
        >
          <div
            class="emotion-class"
            data-testid="PayoutMethodTile_Container"
          >
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
              >
                <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m13%2036.5c11.3431%2036.5%2010%2035.1569%2010%2033.5v14.5c10%2012.8431%2011.3431%2011.5%2013%2011.5h35c36.6569%2011.5%2038%2012.8431%2038%2014.5v18.5h29.75c26.5744%2018.5%2024%2021.0744%2024%2024.25c24%2027.4256%2026.5744%2030%2029.75%2030h38v33.5c38%2035.1569%2036.6569%2036.5%2035%2036.5h13zm29%2028c27.3431%2028%2026%2026.6569%2026%2025v23.5c26%2021.8431%2027.3431%2020.5%2029%2020.5h38v28h29zm32.5%2024.25c32.5%2025.4926%2031.4926%2026.5%2030.25%2026.5c29.0074%2026.5%2028%2025.4926%2028%2024.25c28%2023.0074%2029.0074%2022%2030.25%2022c31.4926%2022%2032.5%2023.0074%2032.5%2024.25z'%20/%3e%3c/svg%3e
                  classname="css-10a16n8"
                  fill="var(--actions-basic-actions-action-base)"
                  size="32"
                />
                <h5
                  class="emotion-class"
                >
                  Bank transfer
                </h5>
              </div>
            </div>
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
              >
                <p
                  class="emotion-class"
                  data-testid="PayoutMethodTile_Account"
                >
                  Ewela Asdd
                </p>
                <p
                  class="emotion-class"
                  data-testid="PayoutMethodTile_Currency"
                >
                  PLN
                </p>
              </div>
              <div
                class="emotion-class"
              >
                <p
                  class="emotion-class"
                  data-testid="PayoutMethodTile_AdditionalData"
                >
                  ************5196
                </p>
                <div
                  class="emotion-class"
                  data-testid="PayoutMethodTile_Verified"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                    classname="css-8sv2oy"
                    fill="var(--statuses-status-active)"
                    size="24"
                  />
                  <span
                    class="emotion-class"
                  >
                    Verified
                  </span>
                </div>
              </div>
            </div>
            <div
              class="emotion-class"
            >
              <button
                class=" emotion-class"
                data-testid="PayoutMethodTile_DetailsButtonActive"
                style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
              >
                <span
                  class="emotion-class"
                >
                  See details
                </span>
              </button>
              <button
                class=" emotion-class"
                data-testid="PayoutMethodTile_MakePrimary"
                style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
              >
                <span
                  class="emotion-class"
                >
                  Make primary
                </span>
              </button>
            </div>
          </div>
          <div
            class="emotion-class"
            data-testid="PayoutMethodTile_Container"
          >
            <div
              class="emotion-class"
            >
              </src/assets/icons/misc/payout-method-payoneer.svg
                classname="css-1b2xl8c"
                height="24"
                size="124"
              />
              <p
                class="emotion-class"
                data-testid="PayoutMethodTile_Primary"
              >
                primary
              </p>
            </div>
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
              >
                <p
                  class="emotion-class"
                  data-testid="PayoutMethodTile_Account"
                >
                  7550264
                </p>
                <p
                  class="emotion-class"
                  data-testid="PayoutMethodTile_Currency"
                >
                  USD
                </p>
              </div>
              <div
                class="emotion-class"
              >
                <div
                  class="emotion-class"
                  data-testid="PayoutMethodTile_Active"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                    classname="css-8sv2oy"
                    fill="var(--statuses-status-active)"
                    size="24"
                  />
                  <span
                    class="emotion-class"
                  >
                    Active
                  </span>
                </div>
              </div>
            </div>
            <div
              class="emotion-class"
            >
              <button
                class=" emotion-class"
                data-testid="PayoutMethodTile_DetailsButtonActive"
                style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
              >
                <span
                  class="emotion-class"
                >
                  See details
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="MuiModal-root emotion-class"
      role="presentation"
    >
      <div
        aria-hidden="true"
        class="MuiBackdrop-root MuiModal-backdrop emotion-class"
        style="opacity: 1; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
      />
      <div
        data-testid="sentinelStart"
        tabindex="0"
      />
      <div
        class="emotion-class"
        style="transform: none; transition: transform 225ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;"
        tabindex="-1"
        width="714"
      >
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <h3
              class="emotion-class"
            >
              Payoneer details
            </h3>
            <p
              class="emotion-class"
              data-testid="DetailsSidepanel_Primary"
            >
              primary
            </p>
          </div>
          <a
            class=" emotion-class"
            data-testid="SidePanel_CloseButton"
            style="margin-left: auto;"
          >
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m21.5%2024l13%2032.5l15.5%2035l24%2026.5l32.5%2035l35%2032.5l26.5%2024l35%2015.5l32.5%2013l24%2021.5l15.5%2013l13%2015.5l21.5%2024z'/%3e%3c/svg%3e
              classname="css-10pudoj"
              size="30"
            />
          </a>
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 MuiAlert-root MuiAlert-colorSuccess MuiAlert-outlinedSuccess MuiAlert-outlined emotion-class"
            role="alert"
            style="--Paper-shadow: none;"
          >
            <div
              class="MuiAlert-icon emotion-class"
            >
              <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m9.99951%2033.5c9.99951%2035.1569%2011.3427%2036.5%2012.9995%2036.5h28.5126c28.5039%2036.3344%2028.4995%2036.1677%2028.4995%2036c28.4995%2033.7308%2029.3081%2031.6394%2030.651%2030h26.9995c25.3427%2030%2023.9995%2028.6569%2023.9995%2027v21.5c23.9995%2019.8431%2025.3427%2018.5%2026.9995%2018.5h37.9995v14.5c37.9995%2012.8431%2036.6564%2011.5%2034.9995%2011.5h12.9995c11.3427%2011.5%209.99951%2012.8431%209.99951%2014.5v33.5z'%20/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37.9995%2026.5v20.5h27.4995c26.6711%2020.5%2025.9995%2021.1716%2025.9995%2022v26.5c25.9995%2027.3284%2026.6711%2028%2027.4995%2028h32.8956c34.3727%2027.0515%2036.1255%2026.5%2037.9995%2026.5zm30.2495%2026.5c31.4922%2026.5%2032.4995%2025.4926%2032.4995%2024.25c32.4995%2023.0074%2031.4922%2022%2030.2495%2022c29.0069%2022%2027.9995%2023.0074%2027.9995%2024.25c27.9995%2025.4926%2029.0069%2026.5%2030.2495%2026.5z'%20/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37.9995%2028c33.5995%2028%2029.9995%2031.6%2029.9995%2036c29.9995%2040.4%2033.5995%2044%2037.9995%2044c42.3995%2044%2045.9995%2040.4%2045.9995%2036c45.9995%2031.6%2042.3995%2028%2037.9995%2028zm32.7247%2035.7576l33.895%2034.5872l36.8961%2037.5882l42.104%2032.3803l43.2744%2033.5507l36.8961%2039.929l32.7247%2035.7576z'%20/%3e%3c/svg%3e
                classname="css-gmvm0x"
                fill="#06CC87"
                fontsize="inherit"
                size="30"
              />
            </div>
            <div
              class="MuiAlert-message emotion-class"
            >
              <div
                class="emotion-class"
              >
                <div>
                  Payoneer account status: active.
                </div>
              </div>
            </div>
          </div>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <div>
                <span
                  class="emotion-class"
                >
                  Payoneer User ID
                </span>
                <p
                  class="emotion-class"
                  data-testid="PayoneerDetails_UserId"
                >
                  7550264
                </p>
              </div>
              <div>
                <span
                  class="emotion-class"
                >
                  Invoice Currency
                </span>
                <p
                  class="emotion-class"
                  data-testid="PayoneerDetails_Currency"
                >
                  USD
                </p>
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <button
              class=" emotion-class"
              data-testid="PayoutMethodTile_RemovePayoutMethod"
            >
              <span
                class="emotion-class"
              >
                Remove this method
              </span>
            </button>
          </div>
        </div>
      </div>
      <div
        data-testid="sentinelEnd"
        tabindex="0"
      />
    </div>
  </body>,
  "container": .emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
  margin-bottom: var(--spacing-xl);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-top: var(--spacing-m);
  gap: var(--spacing-l);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  max-height: 32px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--spacing-s);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  color: var(--glyphs-basic-glyphs-base);
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--spacing-2xs);
  margin-left: auto;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: var(--statuses-status-active);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  gap: var(--spacing-m);
  height: 30px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: #F7F7F7;
  border: 1px solid var(--action-base);
  padding-top: 0;
  padding-right: 19px;
  padding-bottom: 0;
  padding-left: 19px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 33px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
  border: var(--spacing-2xs) solid var(--actions-subtle-actions-action-subtle-selected);
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 10px;
  line-height: 20px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-s);
  border-radius: 2px;
  background: var(--statuses-status-active);
  color: var(--glyphs-basic-glyphs-inverted);
}

<div
    aria-hidden="true"
  >
    <div
      class="emotion-class"
    >
      <h3
        class="emotion-class"
      >
        Payout methods
      </h3>
      <p
        class="emotion-class"
      >
        Choose your primary payout method.
      </p>
      <div
        class="emotion-class"
        data-testid="PayoutMethods_PayoutMethodsContainer"
      >
        <div
          class="emotion-class"
          data-testid="PayoutMethodTile_Container"
        >
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m13%2036.5c11.3431%2036.5%2010%2035.1569%2010%2033.5v14.5c10%2012.8431%2011.3431%2011.5%2013%2011.5h35c36.6569%2011.5%2038%2012.8431%2038%2014.5v18.5h29.75c26.5744%2018.5%2024%2021.0744%2024%2024.25c24%2027.4256%2026.5744%2030%2029.75%2030h38v33.5c38%2035.1569%2036.6569%2036.5%2035%2036.5h13zm29%2028c27.3431%2028%2026%2026.6569%2026%2025v23.5c26%2021.8431%2027.3431%2020.5%2029%2020.5h38v28h29zm32.5%2024.25c32.5%2025.4926%2031.4926%2026.5%2030.25%2026.5c29.0074%2026.5%2028%2025.4926%2028%2024.25c28%2023.0074%2029.0074%2022%2030.25%2022c31.4926%2022%2032.5%2023.0074%2032.5%2024.25z'%20/%3e%3c/svg%3e
                classname="css-10a16n8"
                fill="var(--actions-basic-actions-action-base)"
                size="32"
              />
              <h5
                class="emotion-class"
              >
                Bank transfer
              </h5>
            </div>
          </div>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <p
                class="emotion-class"
                data-testid="PayoutMethodTile_Account"
              >
                Ewela Asdd
              </p>
              <p
                class="emotion-class"
                data-testid="PayoutMethodTile_Currency"
              >
                PLN
              </p>
            </div>
            <div
              class="emotion-class"
            >
              <p
                class="emotion-class"
                data-testid="PayoutMethodTile_AdditionalData"
              >
                ************5196
              </p>
              <div
                class="emotion-class"
                data-testid="PayoutMethodTile_Verified"
              >
                <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                  classname="css-8sv2oy"
                  fill="var(--statuses-status-active)"
                  size="24"
                />
                <span
                  class="emotion-class"
                >
                  Verified
                </span>
              </div>
            </div>
          </div>
          <div
            class="emotion-class"
          >
            <button
              class=" emotion-class"
              data-testid="PayoutMethodTile_DetailsButtonActive"
              style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
            >
              <span
                class="emotion-class"
              >
                See details
              </span>
            </button>
            <button
              class=" emotion-class"
              data-testid="PayoutMethodTile_MakePrimary"
              style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
            >
              <span
                class="emotion-class"
              >
                Make primary
              </span>
            </button>
          </div>
        </div>
        <div
          class="emotion-class"
          data-testid="PayoutMethodTile_Container"
        >
          <div
            class="emotion-class"
          >
            </src/assets/icons/misc/payout-method-payoneer.svg
              classname="css-1b2xl8c"
              height="24"
              size="124"
            />
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Primary"
            >
              primary
            </p>
          </div>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <p
                class="emotion-class"
                data-testid="PayoutMethodTile_Account"
              >
                7550264
              </p>
              <p
                class="emotion-class"
                data-testid="PayoutMethodTile_Currency"
              >
                USD
              </p>
            </div>
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                data-testid="PayoutMethodTile_Active"
              >
                <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                  classname="css-8sv2oy"
                  fill="var(--statuses-status-active)"
                  size="24"
                />
                <span
                  class="emotion-class"
                >
                  Active
                </span>
              </div>
            </div>
          </div>
          <div
            class="emotion-class"
          >
            <button
              class=" emotion-class"
              data-testid="PayoutMethodTile_DetailsButtonActive"
              style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
            >
              <span
                class="emotion-class"
              >
                See details
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;

exports[`PayoutMethods > snapshot should match: BlockedPrimaryMethod 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
  margin-bottom: var(--spacing-xl);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  margin-top: var(--spacing-m);
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  background-color: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 6px 16px;
  color: rgb(30, 70, 32);
  border: 1px solid #4caf50;
}

.emotion-class .MuiAlert-icon {
  color: #2e7d32;
}

.emotion-class.MuiAlert-root {
  width: 100%;
  max-width: 100%;
  color: #303757;
  background-color: #E9F7FE;
  border-color: #91D9FA;
  font-family: "Open Sans",sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  padding: 15px 10px 15px 20px;
  margin: 0;
  box-sizing: border-box;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class.MuiAlert-root .MuiAlert-icon {
  padding: 0;
  margin: 0 4px 0 0;
}

.emotion-class.MuiAlert-root .MuiAlert-message {
  padding: 0;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.emotion-class.MuiAlert-root a {
  color: #465AB6;
}

@media (max-width: 768px) {
  .emotion-class.MuiAlert-root {
    padding: 20px;
  }
}

.emotion-class {
  margin-right: 12px;
  padding: 7px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 22px;
  opacity: 0.9;
}

.emotion-class {
  padding: 8px 0;
  min-width: 0;
  overflow: auto;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class>:first-of-type {
  padding: 0;
}

@media (max-width: 768px) {
  .emotion-class {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: start;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: start;
  }

  .emotion-class div {
    margin: 15px 0 0 0;
  }
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-top: var(--spacing-m);
  gap: var(--spacing-l);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
  border: var(--spacing-2xs) solid var(--actions-subtle-actions-action-subtle-selected);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  max-height: 32px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--spacing-s);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 10px;
  line-height: 20px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-s);
  border-radius: 2px;
  background: var(--statuses-status-active);
  color: var(--glyphs-basic-glyphs-inverted);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  color: var(--glyphs-basic-glyphs-base);
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--spacing-2xs);
  margin-left: auto;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: var(--statuses-status-active);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  gap: var(--spacing-m);
  height: 30px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: #FFFFFF;
  border: 1px solid #C5C8D5;
  padding-top: 0;
  padding-right: 19px;
  padding-bottom: 0;
  padding-left: 19px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: default;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: #C5C8D5;
}

.emotion-class span {
  line-height: 33px;
}

.emotion-class {
  color: #C5C8D5;
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div>
  <div
    class="emotion-class"
  >
    <h3
      class="emotion-class"
    >
      Payout methods
    </h3>
    <p
      class="emotion-class"
    >
      Choose your primary payout method.
    </p>
    <div
      class="emotion-class"
    >
      <div
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 MuiAlert-root MuiAlert-colorSuccess MuiAlert-outlinedSuccess MuiAlert-outlined emotion-class"
        role="alert"
        style="--Paper-shadow: none;"
      >
        <div
          class="MuiAlert-icon emotion-class"
        >
          <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m9.99951%2033.5c9.99951%2035.1569%2011.3427%2036.5%2012.9995%2036.5h28.5126c28.5039%2036.3344%2028.4995%2036.1677%2028.4995%2036c28.4995%2033.7308%2029.3081%2031.6394%2030.651%2030h26.9995c25.3427%2030%2023.9995%2028.6569%2023.9995%2027v21.5c23.9995%2019.8431%2025.3427%2018.5%2026.9995%2018.5h37.9995v14.5c37.9995%2012.8431%2036.6564%2011.5%2034.9995%2011.5h12.9995c11.3427%2011.5%209.99951%2012.8431%209.99951%2014.5v33.5z'%20/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37.9995%2026.5v20.5h27.4995c26.6711%2020.5%2025.9995%2021.1716%2025.9995%2022v26.5c25.9995%2027.3284%2026.6711%2028%2027.4995%2028h32.8956c34.3727%2027.0515%2036.1255%2026.5%2037.9995%2026.5zm30.2495%2026.5c31.4922%2026.5%2032.4995%2025.4926%2032.4995%2024.25c32.4995%2023.0074%2031.4922%2022%2030.2495%2022c29.0069%2022%2027.9995%2023.0074%2027.9995%2024.25c27.9995%2025.4926%2029.0069%2026.5%2030.2495%2026.5z'%20/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m29.9995%2036c29.9995%2031.6%2033.5995%2028%2037.9995%2028c42.3995%2028%2045.9995%2031.6%2045.9995%2036c45.9995%2040.4%2042.3995%2044%2037.9995%2044c33.5995%2044%2029.9995%2040.4%2029.9995%2036zm37.9464%2034.6766l41.276%2031.3469l42.665%2032.7359l39.3353%2036.0655l42.4604%2039.1906l40.9806%2040.6704l37.8555%2037.5453l34.5259%2040.8749l33.137%2039.486l36.4666%2036.1564l33.3415%2033.0313l34.8213%2031.5515l37.9464%2034.6766z'%20/%3e%3c/svg%3e
            classname="css-nspwwt"
            fill="#23B4F5"
            fontsize="inherit"
            size="30"
          />
        </div>
        <div
          class="MuiAlert-message emotion-class"
        >
          <div
            class="emotion-class"
          >
            <div>
              Switching payout methods is unavailable while invoices are being processed.
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="emotion-class"
      data-testid="PayoutMethods_PayoutMethodsContainer"
    >
      <div
        class="emotion-class"
        data-testid="PayoutMethodTile_Container"
      >
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m13%2036.5c11.3431%2036.5%2010%2035.1569%2010%2033.5v14.5c10%2012.8431%2011.3431%2011.5%2013%2011.5h35c36.6569%2011.5%2038%2012.8431%2038%2014.5v18.5h29.75c26.5744%2018.5%2024%2021.0744%2024%2024.25c24%2027.4256%2026.5744%2030%2029.75%2030h38v33.5c38%2035.1569%2036.6569%2036.5%2035%2036.5h13zm29%2028c27.3431%2028%2026%2026.6569%2026%2025v23.5c26%2021.8431%2027.3431%2020.5%2029%2020.5h38v28h29zm32.5%2024.25c32.5%2025.4926%2031.4926%2026.5%2030.25%2026.5c29.0074%2026.5%2028%2025.4926%2028%2024.25c28%2023.0074%2029.0074%2022%2030.25%2022c31.4926%2022%2032.5%2023.0074%2032.5%2024.25z'%20/%3e%3c/svg%3e
              classname="css-10a16n8"
              fill="var(--actions-basic-actions-action-base)"
              size="32"
            />
            <h5
              class="emotion-class"
            >
              Bank transfer
            </h5>
          </div>
          <p
            class="emotion-class"
            data-testid="PayoutMethodTile_Primary"
          >
            primary
          </p>
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Account"
            >
              Ewela Asdd
            </p>
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Currency"
            >
              PLN
            </p>
          </div>
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_AdditionalData"
            >
              ************5196
            </p>
            <div
              class="emotion-class"
              data-testid="PayoutMethodTile_Verified"
            >
              <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                classname="css-8sv2oy"
                fill="var(--statuses-status-active)"
                size="24"
              />
              <span
                class="emotion-class"
              >
                Verified
              </span>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
        >
          <button
            class=" emotion-class"
            data-testid="PayoutMethodTile_DetailsButtonActive"
            style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
          >
            <span
              class="emotion-class"
            >
              See details
            </span>
          </button>
        </div>
      </div>
      <div
        class="emotion-class"
        data-testid="PayoutMethodTile_Container"
      >
        <div
          class="emotion-class"
        >
          </src/assets/icons/misc/payout-method-payoneer.svg
            classname="css-1b2xl8c"
            height="24"
            size="124"
          />
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Account"
            >
              7550264
            </p>
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Currency"
            >
              USD
            </p>
          </div>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
              data-testid="PayoutMethodTile_Active"
            >
              <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                classname="css-8sv2oy"
                fill="var(--statuses-status-active)"
                size="24"
              />
              <span
                class="emotion-class"
              >
                Active
              </span>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
        >
          <button
            class=" emotion-class"
            data-testid="PayoutMethodTile_DetailsButtonActive"
            style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
          >
            <span
              class="emotion-class"
            >
              See details
            </span>
          </button>
          <button
            class=" emotion-class"
            data-testid="PayoutMethodTile_MakePrimary"
            disabled=""
            style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
          >
            <span
              class="emotion-class"
              disabled=""
            >
              Make primary
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`PayoutMethods > snapshot should match: ConnectToPayoneerError 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
  margin-bottom: var(--spacing-xl);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-top: var(--spacing-m);
  gap: var(--spacing-l);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
  border: var(--spacing-2xs) solid var(--actions-subtle-actions-action-subtle-selected);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  max-height: 32px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--spacing-s);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 10px;
  line-height: 20px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-s);
  border-radius: 2px;
  background: var(--statuses-status-active);
  color: var(--glyphs-basic-glyphs-inverted);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  color: var(--glyphs-basic-glyphs-base);
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--spacing-2xs);
  margin-left: auto;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: var(--statuses-status-active);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  gap: var(--spacing-m);
  height: 30px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: #F7F7F7;
  border: 1px solid var(--action-base);
  padding-top: 0;
  padding-right: 19px;
  padding-bottom: 0;
  padding-left: 19px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 33px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

<div>
  <div
    class="emotion-class"
  >
    <h3
      class="emotion-class"
    >
      Payout methods
    </h3>
    <p
      class="emotion-class"
    >
      Choose your primary payout method.
    </p>
    <div
      class="emotion-class"
      data-testid="PayoutMethods_PayoutMethodsContainer"
    >
      <div
        class="emotion-class"
        data-testid="PayoutMethodTile_Container"
      >
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m13%2036.5c11.3431%2036.5%2010%2035.1569%2010%2033.5v14.5c10%2012.8431%2011.3431%2011.5%2013%2011.5h35c36.6569%2011.5%2038%2012.8431%2038%2014.5v18.5h29.75c26.5744%2018.5%2024%2021.0744%2024%2024.25c24%2027.4256%2026.5744%2030%2029.75%2030h38v33.5c38%2035.1569%2036.6569%2036.5%2035%2036.5h13zm29%2028c27.3431%2028%2026%2026.6569%2026%2025v23.5c26%2021.8431%2027.3431%2020.5%2029%2020.5h38v28h29zm32.5%2024.25c32.5%2025.4926%2031.4926%2026.5%2030.25%2026.5c29.0074%2026.5%2028%2025.4926%2028%2024.25c28%2023.0074%2029.0074%2022%2030.25%2022c31.4926%2022%2032.5%2023.0074%2032.5%2024.25z'%20/%3e%3c/svg%3e
              classname="css-10a16n8"
              fill="var(--actions-basic-actions-action-base)"
              size="32"
            />
            <h5
              class="emotion-class"
            >
              Bank transfer
            </h5>
          </div>
          <p
            class="emotion-class"
            data-testid="PayoutMethodTile_Primary"
          >
            primary
          </p>
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Account"
            >
              Ralph Kaminski
            </p>
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Currency"
            >
              USD
            </p>
          </div>
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_AdditionalData"
            >
              **********6819
            </p>
            <div
              class="emotion-class"
              data-testid="PayoutMethodTile_Verified"
            >
              <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                classname="css-8sv2oy"
                fill="var(--statuses-status-active)"
                size="24"
              />
              <span
                class="emotion-class"
              >
                Verified
              </span>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
        >
          <button
            class=" emotion-class"
            data-testid="PayoutMethodTile_DetailsButtonActive"
            style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
          >
            <span
              class="emotion-class"
            >
              See details
            </span>
          </button>
        </div>
      </div>
      <div
        class="emotion-class"
        data-testid="PayoutMethodTile_Container"
      >
        <div
          class="emotion-class"
        >
          </src/assets/icons/misc/payout-method-payoneer.svg
            classname="css-1b2xl8c"
            height="24"
            size="124"
          />
        </div>
        <div
          class="emotion-class"
        >
          <p
            class="emotion-class"
          >
            Connect your Payoneer account to start using it as a payout method.
          </p>
        </div>
        <div
          class="emotion-class"
        >
          <button
            class=" emotion-class"
            data-testid="PayoutMethodTile_SetUpButton"
            style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
          >
            <span
              class="emotion-class"
            >
              Connect with Payoneer
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`PayoutMethods > snapshot should match: DefaultErrorMessage 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
  margin-bottom: var(--spacing-xl);
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  background-color: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 6px 16px;
  color: rgb(30, 70, 32);
  border: 1px solid #4caf50;
}

.emotion-class .MuiAlert-icon {
  color: #2e7d32;
}

.emotion-class.MuiAlert-root {
  width: 100%;
  max-width: 100%;
  color: #303757;
  background-color: #FFF2F3;
  border-color: #FFBFC5;
  font-family: "Open Sans",sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  padding: 15px 10px 15px 20px;
  margin: 0;
  box-sizing: border-box;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class.MuiAlert-root .MuiAlert-icon {
  padding: 0;
  margin: 0 4px 0 0;
}

.emotion-class.MuiAlert-root .MuiAlert-message {
  padding: 0;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.emotion-class.MuiAlert-root a {
  color: #465AB6;
}

@media (max-width: 768px) {
  .emotion-class.MuiAlert-root {
    padding: 20px;
  }
}

.emotion-class {
  margin-right: 12px;
  padding: 7px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 22px;
  opacity: 0.9;
}

.emotion-class {
  padding: 8px 0;
  min-width: 0;
  overflow: auto;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class>:first-of-type {
  padding: 0;
}

@media (max-width: 768px) {
  .emotion-class {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: start;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: start;
  }

  .emotion-class div {
    margin: 15px 0 0 0;
  }
}

<div>
  <div
    class="emotion-class"
  >
    <h3
      class="emotion-class"
    >
      Payout methods
    </h3>
    <div
      class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 MuiAlert-root MuiAlert-colorSuccess MuiAlert-outlinedSuccess MuiAlert-outlined emotion-class"
      role="alert"
      style="--Paper-shadow: none;"
    >
      <div
        class="MuiAlert-icon emotion-class"
      >
        <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m9.99951%2033.5c9.99951%2035.1569%2011.3427%2036.5%2012.9995%2036.5h28.5126c28.5039%2036.3344%2028.4995%2036.1677%2028.4995%2036c28.4995%2033.7308%2029.3081%2031.6394%2030.651%2030h26.9995c25.3427%2030%2023.9995%2028.6569%2023.9995%2027v21.5c23.9995%2019.8431%2025.3427%2018.5%2026.9995%2018.5h37.9995v14.5c37.9995%2012.8431%2036.6564%2011.5%2034.9995%2011.5h12.9995c11.3427%2011.5%209.99951%2012.8431%209.99951%2014.5v33.5z'%20/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37.9995%2026.5v20.5h27.4995c26.6711%2020.5%2025.9995%2021.1716%2025.9995%2022v26.5c25.9995%2027.3284%2026.6711%2028%2027.4995%2028h32.8956c34.3727%2027.0515%2036.1255%2026.5%2037.9995%2026.5zm30.2495%2026.5c31.4922%2026.5%2032.4995%2025.4926%2032.4995%2024.25c32.4995%2023.0074%2031.4922%2022%2030.2495%2022c29.0069%2022%2027.9995%2023.0074%2027.9995%2024.25c27.9995%2025.4926%2029.0069%2026.5%2030.2495%2026.5z'%20/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m29.9995%2036c29.9995%2031.6%2033.5995%2028%2037.9995%2028c42.3995%2028%2045.9995%2031.6%2045.9995%2036c45.9995%2040.4%2042.3995%2044%2037.9995%2044c33.5995%2044%2029.9995%2040.4%2029.9995%2036zm37.9464%2034.6766l41.276%2031.3469l42.665%2032.7359l39.3353%2036.0655l42.4604%2039.1906l40.9806%2040.6704l37.8555%2037.5453l34.5259%2040.8749l33.137%2039.486l36.4666%2036.1564l33.3415%2033.0313l34.8213%2031.5515l37.9464%2034.6766z'%20/%3e%3c/svg%3e
          classname="css-1pafx7u"
          fill="#FF7F8A"
          fontsize="inherit"
          size="30"
        />
      </div>
      <div
        class="MuiAlert-message emotion-class"
      >
        <div
          class="emotion-class"
        >
          <div>
            Something went wrong. Try again in few minutes.
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`PayoutMethods > snapshot should match: MissingSelectedPrimaryMethod 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
  margin-bottom: var(--spacing-xl);
}

.emotion-class {
  margin-top: var(--spacing-m);
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  background-color: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 6px 16px;
  color: rgb(30, 70, 32);
  border: 1px solid #4caf50;
}

.emotion-class .MuiAlert-icon {
  color: #2e7d32;
}

.emotion-class.MuiAlert-root {
  width: 100%;
  max-width: 100%;
  color: #303757;
  background-color: #FFF6ED;
  border-color: #FFD2A6;
  font-family: "Open Sans",sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  padding: 15px 10px 15px 20px;
  margin: 0;
  box-sizing: border-box;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class.MuiAlert-root .MuiAlert-icon {
  padding: 0;
  margin: 0 4px 0 0;
}

.emotion-class.MuiAlert-root .MuiAlert-message {
  padding: 0;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.emotion-class.MuiAlert-root a {
  color: #465AB6;
}

@media (max-width: 768px) {
  .emotion-class.MuiAlert-root {
    padding: 20px;
  }
}

.emotion-class {
  margin-right: 12px;
  padding: 7px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 22px;
  opacity: 0.9;
}

.emotion-class {
  padding: 8px 0;
  min-width: 0;
  overflow: auto;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class>:first-of-type {
  padding: 0;
}

@media (max-width: 768px) {
  .emotion-class {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: start;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: start;
  }

  .emotion-class div {
    margin: 15px 0 0 0;
  }
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-top: var(--spacing-m);
  gap: var(--spacing-l);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  max-height: 32px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--spacing-s);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  color: var(--glyphs-basic-glyphs-base);
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--spacing-2xs);
  margin-left: auto;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: var(--statuses-status-active);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  gap: var(--spacing-m);
  height: 30px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: #F7F7F7;
  border: 1px solid var(--action-base);
  padding-top: 0;
  padding-right: 19px;
  padding-bottom: 0;
  padding-left: 19px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 33px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

<div>
  <div
    class="emotion-class"
  >
    <h3
      class="emotion-class"
    >
      Payout methods
    </h3>
    <div
      class="emotion-class"
    >
      <div
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 MuiAlert-root MuiAlert-colorSuccess MuiAlert-outlinedSuccess MuiAlert-outlined emotion-class"
        role="alert"
        style="--Paper-shadow: none;"
      >
        <div
          class="MuiAlert-icon emotion-class"
        >
          <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m9.99951%2033.5c9.99951%2035.1569%2011.3427%2036.5%2012.9995%2036.5h28.5126c28.5039%2036.3344%2028.4995%2036.1677%2028.4995%2036c28.4995%2033.7308%2029.3081%2031.6394%2030.651%2030h26.9995c25.3427%2030%2023.9995%2028.6569%2023.9995%2027v21.5c23.9995%2019.8431%2025.3427%2018.5%2026.9995%2018.5h37.9995v14.5c37.9995%2012.8431%2036.6564%2011.5%2034.9995%2011.5h12.9995c11.3427%2011.5%209.99951%2012.8431%209.99951%2014.5v33.5z'%20/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37.9995%2026.5v20.5h27.4995c26.6711%2020.5%2025.9995%2021.1716%2025.9995%2022v26.5c25.9995%2027.3284%2026.6711%2028%2027.4995%2028h32.8956c34.3727%2027.0515%2036.1255%2026.5%2037.9995%2026.5zm30.2495%2026.5c31.4922%2026.5%2032.4995%2025.4926%2032.4995%2024.25c32.4995%2023.0074%2031.4922%2022%2030.2495%2022c29.0069%2022%2027.9995%2023.0074%2027.9995%2024.25c27.9995%2025.4926%2029.0069%2026.5%2030.2495%2026.5z'%20/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m29.9995%2036c29.9995%2031.6%2033.5995%2028%2037.9995%2028c42.3995%2028%2045.9995%2031.6%2045.9995%2036c45.9995%2040.4%2042.3995%2044%2037.9995%2044c33.5995%2044%2029.9995%2040.4%2029.9995%2036zm37.9464%2034.6766l41.276%2031.3469l42.665%2032.7359l39.3353%2036.0655l42.4604%2039.1906l40.9806%2040.6704l37.8555%2037.5453l34.5259%2040.8749l33.137%2039.486l36.4666%2036.1564l33.3415%2033.0313l34.8213%2031.5515l37.9464%2034.6766z'%20/%3e%3c/svg%3e
            classname="css-hdmw1m"
            fill="#FFA64C"
            fontsize="inherit"
            size="30"
          />
        </div>
        <div
          class="MuiAlert-message emotion-class"
        >
          <div
            class="emotion-class"
          >
            <div>
              Your Primary Payout Method is not selected. Please set a Primary Payout Method to get paid.
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="emotion-class"
      data-testid="PayoutMethods_PayoutMethodsContainer"
    >
      <div
        class="emotion-class"
        data-testid="PayoutMethodTile_Container"
      >
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m13%2036.5c11.3431%2036.5%2010%2035.1569%2010%2033.5v14.5c10%2012.8431%2011.3431%2011.5%2013%2011.5h35c36.6569%2011.5%2038%2012.8431%2038%2014.5v18.5h29.75c26.5744%2018.5%2024%2021.0744%2024%2024.25c24%2027.4256%2026.5744%2030%2029.75%2030h38v33.5c38%2035.1569%2036.6569%2036.5%2035%2036.5h13zm29%2028c27.3431%2028%2026%2026.6569%2026%2025v23.5c26%2021.8431%2027.3431%2020.5%2029%2020.5h38v28h29zm32.5%2024.25c32.5%2025.4926%2031.4926%2026.5%2030.25%2026.5c29.0074%2026.5%2028%2025.4926%2028%2024.25c28%2023.0074%2029.0074%2022%2030.25%2022c31.4926%2022%2032.5%2023.0074%2032.5%2024.25z'%20/%3e%3c/svg%3e
              classname="css-10a16n8"
              fill="var(--actions-basic-actions-action-base)"
              size="32"
            />
            <h5
              class="emotion-class"
            >
              Bank transfer
            </h5>
          </div>
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Account"
            >
              Ralph Kaminski
            </p>
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Currency"
            >
              PLN
            </p>
          </div>
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_AdditionalData"
            >
              ************************5196
            </p>
            <div
              class="emotion-class"
              data-testid="PayoutMethodTile_Verified"
            >
              <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                classname="css-8sv2oy"
                fill="var(--statuses-status-active)"
                size="24"
              />
              <span
                class="emotion-class"
              >
                Verified
              </span>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
        >
          <button
            class=" emotion-class"
            data-testid="PayoutMethodTile_DetailsButtonActive"
            style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
          >
            <span
              class="emotion-class"
            >
              See details
            </span>
          </button>
          <button
            class=" emotion-class"
            data-testid="PayoutMethodTile_MakePrimary"
            style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
          >
            <span
              class="emotion-class"
            >
              Make primary
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`PayoutMethods > snapshot should match: NonPayableBankTransfer 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
  margin-bottom: var(--spacing-xl);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-top: var(--spacing-m);
  gap: var(--spacing-l);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
  border: var(--spacing-2xs) solid var(--actions-subtle-actions-action-subtle-selected);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  max-height: 32px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--spacing-s);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 10px;
  line-height: 20px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-s);
  border-radius: 2px;
  background: var(--statuses-status-active);
  color: var(--glyphs-basic-glyphs-inverted);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  color: var(--glyphs-basic-glyphs-base);
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--spacing-2xs);
  margin-left: auto;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: var(--statuses-status-active);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  gap: var(--spacing-m);
  height: 30px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div>
  <div
    class="emotion-class"
  >
    <h3
      class="emotion-class"
    >
      Payout methods
    </h3>
    <div
      class="emotion-class"
      data-testid="PayoutMethods_PayoutMethodsContainer"
    >
      <div
        class="emotion-class"
        data-testid="PayoutMethodTile_Container"
      >
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m13%2036.5c11.3431%2036.5%2010%2035.1569%2010%2033.5v14.5c10%2012.8431%2011.3431%2011.5%2013%2011.5h35c36.6569%2011.5%2038%2012.8431%2038%2014.5v18.5h29.75c26.5744%2018.5%2024%2021.0744%2024%2024.25c24%2027.4256%2026.5744%2030%2029.75%2030h38v33.5c38%2035.1569%2036.6569%2036.5%2035%2036.5h13zm29%2028c27.3431%2028%2026%2026.6569%2026%2025v23.5c26%2021.8431%2027.3431%2020.5%2029%2020.5h38v28h29zm32.5%2024.25c32.5%2025.4926%2031.4926%2026.5%2030.25%2026.5c29.0074%2026.5%2028%2025.4926%2028%2024.25c28%2023.0074%2029.0074%2022%2030.25%2022c31.4926%2022%2032.5%2023.0074%2032.5%2024.25z'%20/%3e%3c/svg%3e
              classname="css-10a16n8"
              fill="var(--actions-basic-actions-action-base)"
              size="32"
            />
            <h5
              class="emotion-class"
            >
              Bank transfer
            </h5>
          </div>
          <p
            class="emotion-class"
            data-testid="PayoutMethodTile_Primary"
          >
            primary
          </p>
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Account"
            >
              Ralph Kaminski
            </p>
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Currency"
            >
              PLN
            </p>
          </div>
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_AdditionalData"
            >
              ************************5196
            </p>
            <div
              class="emotion-class"
              data-testid="PayoutMethodTile_Complete"
            >
              <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                classname="css-8sv2oy"
                fill="var(--statuses-status-active)"
                size="24"
              />
              <span
                class="emotion-class"
              >
                Complete
              </span>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
        >
          <button
            class=" emotion-class"
            data-testid="PayoutMethodTile_DetailsButtonComplete"
            style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
          >
            <span
              class="emotion-class"
            >
              See details
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`PayoutMethods > snapshot should match: NonRegisteredPayoneer 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
  margin-bottom: var(--spacing-xl);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  margin-top: var(--spacing-m);
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  background-color: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 6px 16px;
  color: rgb(30, 70, 32);
  border: 1px solid #4caf50;
}

.emotion-class .MuiAlert-icon {
  color: #2e7d32;
}

.emotion-class.MuiAlert-root {
  width: 100%;
  max-width: 100%;
  color: #303757;
  background-color: #FFF6ED;
  border-color: #FFD2A6;
  font-family: "Open Sans",sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  padding: 15px 10px 15px 20px;
  margin: 0;
  box-sizing: border-box;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class.MuiAlert-root .MuiAlert-icon {
  padding: 0;
  margin: 0 4px 0 0;
}

.emotion-class.MuiAlert-root .MuiAlert-message {
  padding: 0;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.emotion-class.MuiAlert-root a {
  color: #465AB6;
}

@media (max-width: 768px) {
  .emotion-class.MuiAlert-root {
    padding: 20px;
  }
}

.emotion-class {
  margin-right: 12px;
  padding: 7px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 22px;
  opacity: 0.9;
}

.emotion-class {
  padding: 8px 0;
  min-width: 0;
  overflow: auto;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class>:first-of-type {
  padding: 0;
}

@media (max-width: 768px) {
  .emotion-class {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: start;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: start;
  }

  .emotion-class div {
    margin: 15px 0 0 0;
  }
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-top: var(--spacing-m);
  gap: var(--spacing-l);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  max-height: 32px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--spacing-s);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  color: var(--glyphs-basic-glyphs-base);
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  gap: var(--spacing-m);
  height: 30px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: #F7F7F7;
  border: 1px solid var(--action-base);
  padding-top: 0;
  padding-right: 19px;
  padding-bottom: 0;
  padding-left: 19px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 33px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div>
  <div
    class="emotion-class"
  >
    <h3
      class="emotion-class"
    >
      Payout methods
    </h3>
    <p
      class="emotion-class"
    >
      Choose your primary payout method.
    </p>
    <div
      class="emotion-class"
    >
      <div
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 MuiAlert-root MuiAlert-colorSuccess MuiAlert-outlinedSuccess MuiAlert-outlined emotion-class"
        role="alert"
        style="--Paper-shadow: none;"
      >
        <div
          class="MuiAlert-icon emotion-class"
        >
          <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m9.99951%2033.5c9.99951%2035.1569%2011.3427%2036.5%2012.9995%2036.5h28.5126c28.5039%2036.3344%2028.4995%2036.1677%2028.4995%2036c28.4995%2033.7308%2029.3081%2031.6394%2030.651%2030h26.9995c25.3427%2030%2023.9995%2028.6569%2023.9995%2027v21.5c23.9995%2019.8431%2025.3427%2018.5%2026.9995%2018.5h37.9995v14.5c37.9995%2012.8431%2036.6564%2011.5%2034.9995%2011.5h12.9995c11.3427%2011.5%209.99951%2012.8431%209.99951%2014.5v33.5z'%20/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37.9995%2026.5v20.5h27.4995c26.6711%2020.5%2025.9995%2021.1716%2025.9995%2022v26.5c25.9995%2027.3284%2026.6711%2028%2027.4995%2028h32.8956c34.3727%2027.0515%2036.1255%2026.5%2037.9995%2026.5zm30.2495%2026.5c31.4922%2026.5%2032.4995%2025.4926%2032.4995%2024.25c32.4995%2023.0074%2031.4922%2022%2030.2495%2022c29.0069%2022%2027.9995%2023.0074%2027.9995%2024.25c27.9995%2025.4926%2029.0069%2026.5%2030.2495%2026.5z'%20/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m29.9995%2036c29.9995%2031.6%2033.5995%2028%2037.9995%2028c42.3995%2028%2045.9995%2031.6%2045.9995%2036c45.9995%2040.4%2042.3995%2044%2037.9995%2044c33.5995%2044%2029.9995%2040.4%2029.9995%2036zm37.9464%2034.6766l41.276%2031.3469l42.665%2032.7359l39.3353%2036.0655l42.4604%2039.1906l40.9806%2040.6704l37.8555%2037.5453l34.5259%2040.8749l33.137%2039.486l36.4666%2036.1564l33.3415%2033.0313l34.8213%2031.5515l37.9464%2034.6766z'%20/%3e%3c/svg%3e
            classname="css-hdmw1m"
            fill="#FFA64C"
            fontsize="inherit"
            size="30"
          />
        </div>
        <div
          class="MuiAlert-message emotion-class"
        >
          <div
            class="emotion-class"
          >
            <div>
              Your Primary Payout Method is not selected. Please set a Primary Payout Method to get paid.
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="emotion-class"
      data-testid="PayoutMethods_PayoutMethodsContainer"
    >
      <div
        class="emotion-class"
        data-testid="PayoutMethodTile_Container"
      >
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m13%2036.5c11.3431%2036.5%2010%2035.1569%2010%2033.5v14.5c10%2012.8431%2011.3431%2011.5%2013%2011.5h35c36.6569%2011.5%2038%2012.8431%2038%2014.5v18.5h29.75c26.5744%2018.5%2024%2021.0744%2024%2024.25c24%2027.4256%2026.5744%2030%2029.75%2030h38v33.5c38%2035.1569%2036.6569%2036.5%2035%2036.5h13zm29%2028c27.3431%2028%2026%2026.6569%2026%2025v23.5c26%2021.8431%2027.3431%2020.5%2029%2020.5h38v28h29zm32.5%2024.25c32.5%2025.4926%2031.4926%2026.5%2030.25%2026.5c29.0074%2026.5%2028%2025.4926%2028%2024.25c28%2023.0074%2029.0074%2022%2030.25%2022c31.4926%2022%2032.5%2023.0074%2032.5%2024.25z'%20/%3e%3c/svg%3e
              classname="css-10a16n8"
              fill="var(--actions-basic-actions-action-base)"
              size="32"
            />
            <h5
              class="emotion-class"
            >
              Bank transfer
            </h5>
          </div>
        </div>
        <div
          class="emotion-class"
        >
          <p
            class="emotion-class"
          >
            Verify your bank account to start using it as a payout method.
          </p>
        </div>
        <div
          class="emotion-class"
        >
          <button
            class=" emotion-class"
            data-testid="PayoutMethodTile_SetUpButton"
            style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
          >
            <span
              class="emotion-class"
            >
              Set up bank account
            </span>
          </button>
        </div>
      </div>
      <div
        class="emotion-class"
        data-testid="PayoutMethodTile_Container"
      >
        <div
          class="emotion-class"
        >
          </src/assets/icons/misc/payout-method-payoneer.svg
            classname="css-1b2xl8c"
            height="24"
            size="124"
          />
        </div>
        <div
          class="emotion-class"
        >
          <p
            class="emotion-class"
          >
            Connect your Payoneer account to start using it as a payout method.
          </p>
        </div>
        <div
          class="emotion-class"
        >
          <button
            class=" emotion-class"
            data-testid="PayoutMethodTile_SetUpButton"
            style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
          >
            <span
              class="emotion-class"
            >
              Connect with Payoneer
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`PayoutMethods > snapshot should match: NonVerifiedBankTransfer 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
  margin-bottom: var(--spacing-xl);
}

.emotion-class {
  margin-top: var(--spacing-m);
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  background-color: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 6px 16px;
  color: rgb(30, 70, 32);
  border: 1px solid #4caf50;
}

.emotion-class .MuiAlert-icon {
  color: #2e7d32;
}

.emotion-class.MuiAlert-root {
  width: 100%;
  max-width: 100%;
  color: #303757;
  background-color: #FFF6ED;
  border-color: #FFD2A6;
  font-family: "Open Sans",sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  padding: 15px 10px 15px 20px;
  margin: 0;
  box-sizing: border-box;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class.MuiAlert-root .MuiAlert-icon {
  padding: 0;
  margin: 0 4px 0 0;
}

.emotion-class.MuiAlert-root .MuiAlert-message {
  padding: 0;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.emotion-class.MuiAlert-root a {
  color: #465AB6;
}

@media (max-width: 768px) {
  .emotion-class.MuiAlert-root {
    padding: 20px;
  }
}

.emotion-class {
  margin-right: 12px;
  padding: 7px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 22px;
  opacity: 0.9;
}

.emotion-class {
  padding: 8px 0;
  min-width: 0;
  overflow: auto;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class>:first-of-type {
  padding: 0;
}

@media (max-width: 768px) {
  .emotion-class {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: start;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: start;
  }

  .emotion-class div {
    margin: 15px 0 0 0;
  }
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-top: var(--spacing-m);
  gap: var(--spacing-l);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  max-height: 32px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--spacing-s);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  color: var(--glyphs-basic-glyphs-base);
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  gap: var(--spacing-m);
  height: 30px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: #F7F7F7;
  border: 1px solid var(--action-base);
  padding-top: 0;
  padding-right: 19px;
  padding-bottom: 0;
  padding-left: 19px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 33px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div>
  <div
    class="emotion-class"
  >
    <h3
      class="emotion-class"
    >
      Payout methods
    </h3>
    <div
      class="emotion-class"
    >
      <div
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 MuiAlert-root MuiAlert-colorSuccess MuiAlert-outlinedSuccess MuiAlert-outlined emotion-class"
        role="alert"
        style="--Paper-shadow: none;"
      >
        <div
          class="MuiAlert-icon emotion-class"
        >
          <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m9.99951%2033.5c9.99951%2035.1569%2011.3427%2036.5%2012.9995%2036.5h28.5126c28.5039%2036.3344%2028.4995%2036.1677%2028.4995%2036c28.4995%2033.7308%2029.3081%2031.6394%2030.651%2030h26.9995c25.3427%2030%2023.9995%2028.6569%2023.9995%2027v21.5c23.9995%2019.8431%2025.3427%2018.5%2026.9995%2018.5h37.9995v14.5c37.9995%2012.8431%2036.6564%2011.5%2034.9995%2011.5h12.9995c11.3427%2011.5%209.99951%2012.8431%209.99951%2014.5v33.5z'%20/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37.9995%2026.5v20.5h27.4995c26.6711%2020.5%2025.9995%2021.1716%2025.9995%2022v26.5c25.9995%2027.3284%2026.6711%2028%2027.4995%2028h32.8956c34.3727%2027.0515%2036.1255%2026.5%2037.9995%2026.5zm30.2495%2026.5c31.4922%2026.5%2032.4995%2025.4926%2032.4995%2024.25c32.4995%2023.0074%2031.4922%2022%2030.2495%2022c29.0069%2022%2027.9995%2023.0074%2027.9995%2024.25c27.9995%2025.4926%2029.0069%2026.5%2030.2495%2026.5z'%20/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m29.9995%2036c29.9995%2031.6%2033.5995%2028%2037.9995%2028c42.3995%2028%2045.9995%2031.6%2045.9995%2036c45.9995%2040.4%2042.3995%2044%2037.9995%2044c33.5995%2044%2029.9995%2040.4%2029.9995%2036zm37.9464%2034.6766l41.276%2031.3469l42.665%2032.7359l39.3353%2036.0655l42.4604%2039.1906l40.9806%2040.6704l37.8555%2037.5453l34.5259%2040.8749l33.137%2039.486l36.4666%2036.1564l33.3415%2033.0313l34.8213%2031.5515l37.9464%2034.6766z'%20/%3e%3c/svg%3e
            classname="css-hdmw1m"
            fill="#FFA64C"
            fontsize="inherit"
            size="30"
          />
        </div>
        <div
          class="MuiAlert-message emotion-class"
        >
          <div
            class="emotion-class"
          >
            <div>
              Your Primary Payout Method is not selected. Please set a Primary Payout Method to get paid.
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="emotion-class"
      data-testid="PayoutMethods_PayoutMethodsContainer"
    >
      <div
        class="emotion-class"
        data-testid="PayoutMethodTile_Container"
      >
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m13%2036.5c11.3431%2036.5%2010%2035.1569%2010%2033.5v14.5c10%2012.8431%2011.3431%2011.5%2013%2011.5h35c36.6569%2011.5%2038%2012.8431%2038%2014.5v18.5h29.75c26.5744%2018.5%2024%2021.0744%2024%2024.25c24%2027.4256%2026.5744%2030%2029.75%2030h38v33.5c38%2035.1569%2036.6569%2036.5%2035%2036.5h13zm29%2028c27.3431%2028%2026%2026.6569%2026%2025v23.5c26%2021.8431%2027.3431%2020.5%2029%2020.5h38v28h29zm32.5%2024.25c32.5%2025.4926%2031.4926%2026.5%2030.25%2026.5c29.0074%2026.5%2028%2025.4926%2028%2024.25c28%2023.0074%2029.0074%2022%2030.25%2022c31.4926%2022%2032.5%2023.0074%2032.5%2024.25z'%20/%3e%3c/svg%3e
              classname="css-10a16n8"
              fill="var(--actions-basic-actions-action-base)"
              size="32"
            />
            <h5
              class="emotion-class"
            >
              Bank transfer
            </h5>
          </div>
        </div>
        <div
          class="emotion-class"
        >
          <p
            class="emotion-class"
          >
            Verify your bank account to start using it as a payout method.
          </p>
        </div>
        <div
          class="emotion-class"
        >
          <button
            class=" emotion-class"
            data-testid="PayoutMethodTile_SetUpButton"
            style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
          >
            <span
              class="emotion-class"
            >
              Set up bank account
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`PayoutMethods > snapshot should match: NonVerifiedPayoneer 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
  margin-bottom: var(--spacing-xl);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-top: var(--spacing-m);
  gap: var(--spacing-l);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
  border: var(--spacing-2xs) solid var(--actions-subtle-actions-action-subtle-selected);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  max-height: 32px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--spacing-s);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 10px;
  line-height: 20px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-s);
  border-radius: 2px;
  background: var(--statuses-status-active);
  color: var(--glyphs-basic-glyphs-inverted);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  color: var(--glyphs-basic-glyphs-base);
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--spacing-2xs);
  margin-left: auto;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: var(--statuses-status-active);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  gap: var(--spacing-m);
  height: 30px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: #F7F7F7;
  border: 1px solid var(--action-base);
  padding-top: 0;
  padding-right: 19px;
  padding-bottom: 0;
  padding-left: 19px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 33px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

<div>
  <div
    class="emotion-class"
  >
    <h3
      class="emotion-class"
    >
      Payout methods
    </h3>
    <p
      class="emotion-class"
    >
      Choose your primary payout method.
    </p>
    <div
      class="emotion-class"
      data-testid="PayoutMethods_PayoutMethodsContainer"
    >
      <div
        class="emotion-class"
        data-testid="PayoutMethodTile_Container"
      >
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m13%2036.5c11.3431%2036.5%2010%2035.1569%2010%2033.5v14.5c10%2012.8431%2011.3431%2011.5%2013%2011.5h35c36.6569%2011.5%2038%2012.8431%2038%2014.5v18.5h29.75c26.5744%2018.5%2024%2021.0744%2024%2024.25c24%2027.4256%2026.5744%2030%2029.75%2030h38v33.5c38%2035.1569%2036.6569%2036.5%2035%2036.5h13zm29%2028c27.3431%2028%2026%2026.6569%2026%2025v23.5c26%2021.8431%2027.3431%2020.5%2029%2020.5h38v28h29zm32.5%2024.25c32.5%2025.4926%2031.4926%2026.5%2030.25%2026.5c29.0074%2026.5%2028%2025.4926%2028%2024.25c28%2023.0074%2029.0074%2022%2030.25%2022c31.4926%2022%2032.5%2023.0074%2032.5%2024.25z'%20/%3e%3c/svg%3e
              classname="css-10a16n8"
              fill="var(--actions-basic-actions-action-base)"
              size="32"
            />
            <h5
              class="emotion-class"
            >
              Bank transfer
            </h5>
          </div>
          <p
            class="emotion-class"
            data-testid="PayoutMethodTile_Primary"
          >
            primary
          </p>
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Account"
            >
              Ralph Kaminski
            </p>
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Currency"
            >
              USD
            </p>
          </div>
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_AdditionalData"
            >
              **********6819
            </p>
            <div
              class="emotion-class"
              data-testid="PayoutMethodTile_Verified"
            >
              <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                classname="css-8sv2oy"
                fill="var(--statuses-status-active)"
                size="24"
              />
              <span
                class="emotion-class"
              >
                Verified
              </span>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
        >
          <button
            class=" emotion-class"
            data-testid="PayoutMethodTile_DetailsButtonActive"
            style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
          >
            <span
              class="emotion-class"
            >
              See details
            </span>
          </button>
        </div>
      </div>
      <div
        class="emotion-class"
        data-testid="PayoutMethodTile_Container"
      >
        <div
          class="emotion-class"
        >
          </src/assets/icons/misc/payout-method-payoneer.svg
            classname="css-1b2xl8c"
            height="24"
            size="124"
          />
        </div>
        <div
          class="emotion-class"
        >
          <p
            class="emotion-class"
          >
            Connect your Payoneer account to start using it as a payout method.
          </p>
        </div>
        <div
          class="emotion-class"
        >
          <button
            class=" emotion-class"
            data-testid="PayoutMethodTile_SetUpButton"
            style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
          >
            <span
              class="emotion-class"
            >
              Connect with Payoneer
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`PayoutMethods > snapshot should match: RejectedPayoneer 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
  margin-bottom: var(--spacing-xl);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-top: var(--spacing-m);
  gap: var(--spacing-l);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
  border: var(--spacing-2xs) solid var(--actions-subtle-actions-action-subtle-selected);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  max-height: 32px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--spacing-s);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 10px;
  line-height: 20px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-s);
  border-radius: 2px;
  background: var(--statuses-status-active);
  color: var(--glyphs-basic-glyphs-inverted);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  color: var(--glyphs-basic-glyphs-base);
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--spacing-2xs);
  margin-left: auto;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: var(--statuses-status-active);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  gap: var(--spacing-m);
  height: 30px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: var(--statuses-status-inactive);
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: #FFFFFF;
  border: 1px solid var(--action-negative);
  padding-top: 0;
  padding-right: 19px;
  padding-bottom: 0;
  padding-left: 19px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-negative);
}

.emotion-class span {
  line-height: 33px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-negative);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div>
  <div
    class="emotion-class"
  >
    <h3
      class="emotion-class"
    >
      Payout methods
    </h3>
    <p
      class="emotion-class"
    >
      Choose your primary payout method.
    </p>
    <div
      class="emotion-class"
      data-testid="PayoutMethods_PayoutMethodsContainer"
    >
      <div
        class="emotion-class"
        data-testid="PayoutMethodTile_Container"
      >
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m13%2036.5c11.3431%2036.5%2010%2035.1569%2010%2033.5v14.5c10%2012.8431%2011.3431%2011.5%2013%2011.5h35c36.6569%2011.5%2038%2012.8431%2038%2014.5v18.5h29.75c26.5744%2018.5%2024%2021.0744%2024%2024.25c24%2027.4256%2026.5744%2030%2029.75%2030h38v33.5c38%2035.1569%2036.6569%2036.5%2035%2036.5h13zm29%2028c27.3431%2028%2026%2026.6569%2026%2025v23.5c26%2021.8431%2027.3431%2020.5%2029%2020.5h38v28h29zm32.5%2024.25c32.5%2025.4926%2031.4926%2026.5%2030.25%2026.5c29.0074%2026.5%2028%2025.4926%2028%2024.25c28%2023.0074%2029.0074%2022%2030.25%2022c31.4926%2022%2032.5%2023.0074%2032.5%2024.25z'%20/%3e%3c/svg%3e
              classname="css-10a16n8"
              fill="var(--actions-basic-actions-action-base)"
              size="32"
            />
            <h5
              class="emotion-class"
            >
              Bank transfer
            </h5>
          </div>
          <p
            class="emotion-class"
            data-testid="PayoutMethodTile_Primary"
          >
            primary
          </p>
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Account"
            >
              Ewela Asdd
            </p>
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Currency"
            >
              PLN
            </p>
          </div>
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_AdditionalData"
            >
              ************5196
            </p>
            <div
              class="emotion-class"
              data-testid="PayoutMethodTile_Verified"
            >
              <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                classname="css-8sv2oy"
                fill="var(--statuses-status-active)"
                size="24"
              />
              <span
                class="emotion-class"
              >
                Verified
              </span>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
        >
          <button
            class=" emotion-class"
            data-testid="PayoutMethodTile_DetailsButtonActive"
            style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
          >
            <span
              class="emotion-class"
            >
              See details
            </span>
          </button>
        </div>
      </div>
      <div
        class="emotion-class"
        data-testid="PayoutMethodTile_Container"
      >
        <div
          class="emotion-class"
        >
          </src/assets/icons/misc/payout-method-payoneer.svg
            classname="css-1b2xl8c"
            height="24"
            size="124"
          />
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Account"
            />
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Currency"
            />
          </div>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
              data-testid="PayoutMethodTile_Inactive"
            >
              <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9.5%2023.9167c9.5%2031.8917%2016.025%2038.4167%2024%2038.4167c31.975%2038.4167%2038.5%2031.8917%2038.5%2023.9167c38.5%2015.9417%2031.975%209.41669%2024%209.41669c16.025%209.41669%209.5%2015.9417%209.5%2023.9167zm26.4997%2014.4167v17.9167l25.4997%2025.9167h22.4997l21.4997%2017.9167v14.4167h26.4997zm22.2242%2028.6411c21.7412%2029.1241%2021.4997%2029.7159%2021.4997%2030.4167c21.4997%2031.1175%2021.7412%2031.7093%2022.2242%2032.1923c22.7071%2032.6752%2023.299%2032.9167%2023.9997%2032.9167c24.7005%2032.9167%2025.2924%2032.6752%2025.7753%2032.1923c26.2583%2031.7093%2026.4997%2031.1175%2026.4997%2030.4167c26.4997%2029.7159%2026.2583%2029.1241%2025.7753%2028.6411c25.2924%2028.1582%2024.7005%2027.9167%2023.9997%2027.9167c23.299%2027.9167%2022.7071%2028.1582%2022.2242%2028.6411z'%20/%3e%3c/svg%3e
                classname="css-1s425qw"
                fill="var(--statuses-status-inactive)"
                size="24"
              />
              <span
                class="emotion-class"
              >
                Inactive
              </span>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
        >
          <button
            class=" emotion-class"
            data-testid="PayoutMethodTile_DetailsButtonRejected"
            style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
          >
            <span
              class="emotion-class"
            >
              Review details
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`PayoutMethods > snapshot should match: RejectedPrimaryBankTransfer 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
  margin-bottom: var(--spacing-xl);
}

.emotion-class {
  margin-top: var(--spacing-m);
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  background-color: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 6px 16px;
  color: rgb(30, 70, 32);
  border: 1px solid #4caf50;
}

.emotion-class .MuiAlert-icon {
  color: #2e7d32;
}

.emotion-class.MuiAlert-root {
  width: 100%;
  max-width: 100%;
  color: #303757;
  background-color: #FFF6ED;
  border-color: #FFD2A6;
  font-family: "Open Sans",sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  padding: 15px 10px 15px 20px;
  margin: 0;
  box-sizing: border-box;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class.MuiAlert-root .MuiAlert-icon {
  padding: 0;
  margin: 0 4px 0 0;
}

.emotion-class.MuiAlert-root .MuiAlert-message {
  padding: 0;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.emotion-class.MuiAlert-root a {
  color: #465AB6;
}

@media (max-width: 768px) {
  .emotion-class.MuiAlert-root {
    padding: 20px;
  }
}

.emotion-class {
  margin-right: 12px;
  padding: 7px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 22px;
  opacity: 0.9;
}

.emotion-class {
  padding: 8px 0;
  min-width: 0;
  overflow: auto;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class>:first-of-type {
  padding: 0;
}

@media (max-width: 768px) {
  .emotion-class {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: start;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: start;
  }

  .emotion-class div {
    margin: 15px 0 0 0;
  }
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-top: var(--spacing-m);
  gap: var(--spacing-l);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
  border: var(--spacing-2xs) solid var(--actions-subtle-actions-action-subtle-selected);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  max-height: 32px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--spacing-s);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 10px;
  line-height: 20px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-s);
  border-radius: 2px;
  background: var(--statuses-status-active);
  color: var(--glyphs-basic-glyphs-inverted);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  color: var(--glyphs-basic-glyphs-base);
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--spacing-2xs);
  margin-left: auto;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: var(--statuses-status-inactive);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  gap: var(--spacing-m);
  height: 30px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: #FFFFFF;
  border: 1px solid var(--action-negative);
  padding-top: 0;
  padding-right: 19px;
  padding-bottom: 0;
  padding-left: 19px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-negative);
}

.emotion-class span {
  line-height: 33px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-negative);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div>
  <div
    class="emotion-class"
  >
    <h3
      class="emotion-class"
    >
      Payout methods
    </h3>
    <div
      class="emotion-class"
    >
      <div
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 MuiAlert-root MuiAlert-colorSuccess MuiAlert-outlinedSuccess MuiAlert-outlined emotion-class"
        role="alert"
        style="--Paper-shadow: none;"
      >
        <div
          class="MuiAlert-icon emotion-class"
        >
          <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m9.99951%2033.5c9.99951%2035.1569%2011.3427%2036.5%2012.9995%2036.5h28.5126c28.5039%2036.3344%2028.4995%2036.1677%2028.4995%2036c28.4995%2033.7308%2029.3081%2031.6394%2030.651%2030h26.9995c25.3427%2030%2023.9995%2028.6569%2023.9995%2027v21.5c23.9995%2019.8431%2025.3427%2018.5%2026.9995%2018.5h37.9995v14.5c37.9995%2012.8431%2036.6564%2011.5%2034.9995%2011.5h12.9995c11.3427%2011.5%209.99951%2012.8431%209.99951%2014.5v33.5z'%20/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37.9995%2026.5v20.5h27.4995c26.6711%2020.5%2025.9995%2021.1716%2025.9995%2022v26.5c25.9995%2027.3284%2026.6711%2028%2027.4995%2028h32.8956c34.3727%2027.0515%2036.1255%2026.5%2037.9995%2026.5zm30.2495%2026.5c31.4922%2026.5%2032.4995%2025.4926%2032.4995%2024.25c32.4995%2023.0074%2031.4922%2022%2030.2495%2022c29.0069%2022%2027.9995%2023.0074%2027.9995%2024.25c27.9995%2025.4926%2029.0069%2026.5%2030.2495%2026.5z'%20/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m29.9995%2036c29.9995%2031.6%2033.5995%2028%2037.9995%2028c42.3995%2028%2045.9995%2031.6%2045.9995%2036c45.9995%2040.4%2042.3995%2044%2037.9995%2044c33.5995%2044%2029.9995%2040.4%2029.9995%2036zm37.9464%2034.6766l41.276%2031.3469l42.665%2032.7359l39.3353%2036.0655l42.4604%2039.1906l40.9806%2040.6704l37.8555%2037.5453l34.5259%2040.8749l33.137%2039.486l36.4666%2036.1564l33.3415%2033.0313l34.8213%2031.5515l37.9464%2034.6766z'%20/%3e%3c/svg%3e
            classname="css-hdmw1m"
            fill="#FFA64C"
            fontsize="inherit"
            size="30"
          />
        </div>
        <div
          class="MuiAlert-message emotion-class"
        >
          <div
            class="emotion-class"
          >
            <div>
              Your Primary Payout Method became inactive. Please set a new Primary Payout Method or resolve the issue to get paid.
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="emotion-class"
      data-testid="PayoutMethods_PayoutMethodsContainer"
    >
      <div
        class="emotion-class"
        data-testid="PayoutMethodTile_Container"
      >
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m13%2036.5c11.3431%2036.5%2010%2035.1569%2010%2033.5v14.5c10%2012.8431%2011.3431%2011.5%2013%2011.5h35c36.6569%2011.5%2038%2012.8431%2038%2014.5v18.5h29.75c26.5744%2018.5%2024%2021.0744%2024%2024.25c24%2027.4256%2026.5744%2030%2029.75%2030h38v33.5c38%2035.1569%2036.6569%2036.5%2035%2036.5h13zm29%2028c27.3431%2028%2026%2026.6569%2026%2025v23.5c26%2021.8431%2027.3431%2020.5%2029%2020.5h38v28h29zm32.5%2024.25c32.5%2025.4926%2031.4926%2026.5%2030.25%2026.5c29.0074%2026.5%2028%2025.4926%2028%2024.25c28%2023.0074%2029.0074%2022%2030.25%2022c31.4926%2022%2032.5%2023.0074%2032.5%2024.25z'%20/%3e%3c/svg%3e
              classname="css-10a16n8"
              fill="var(--actions-basic-actions-action-base)"
              size="32"
            />
            <h5
              class="emotion-class"
            >
              Bank transfer
            </h5>
          </div>
          <p
            class="emotion-class"
            data-testid="PayoutMethodTile_Primary"
          >
            primary
          </p>
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Account"
            >
              Ralph Kaminski
            </p>
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Currency"
            >
              PLN
            </p>
          </div>
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_AdditionalData"
            >
              ************************5196
            </p>
            <div
              class="emotion-class"
              data-testid="PayoutMethodTile_Error"
            >
              <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9.5%2023.9167c9.5%2031.8917%2016.025%2038.4167%2024%2038.4167c31.975%2038.4167%2038.5%2031.8917%2038.5%2023.9167c38.5%2015.9417%2031.975%209.41669%2024%209.41669c16.025%209.41669%209.5%2015.9417%209.5%2023.9167zm26.4997%2014.4167v17.9167l25.4997%2025.9167h22.4997l21.4997%2017.9167v14.4167h26.4997zm22.2242%2028.6411c21.7412%2029.1241%2021.4997%2029.7159%2021.4997%2030.4167c21.4997%2031.1175%2021.7412%2031.7093%2022.2242%2032.1923c22.7071%2032.6752%2023.299%2032.9167%2023.9997%2032.9167c24.7005%2032.9167%2025.2924%2032.6752%2025.7753%2032.1923c26.2583%2031.7093%2026.4997%2031.1175%2026.4997%2030.4167c26.4997%2029.7159%2026.2583%2029.1241%2025.7753%2028.6411c25.2924%2028.1582%2024.7005%2027.9167%2023.9997%2027.9167c23.299%2027.9167%2022.7071%2028.1582%2022.2242%2028.6411z'%20/%3e%3c/svg%3e
                classname="css-1s425qw"
                fill="var(--statuses-status-inactive)"
                size="24"
              />
              <span
                class="emotion-class"
              >
                Error
              </span>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
        >
          <button
            class=" emotion-class"
            data-testid="PayoutMethodTile_DetailsButtonRejected"
            style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
          >
            <span
              class="emotion-class"
            >
              Review details
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`PayoutMethods > snapshot should match: VerifiedBankTransfer 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
  margin-bottom: var(--spacing-xl);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-top: var(--spacing-m);
  gap: var(--spacing-l);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
  border: var(--spacing-2xs) solid var(--actions-subtle-actions-action-subtle-selected);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  max-height: 32px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--spacing-s);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 10px;
  line-height: 20px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-s);
  border-radius: 2px;
  background: var(--statuses-status-active);
  color: var(--glyphs-basic-glyphs-inverted);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  color: var(--glyphs-basic-glyphs-base);
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--spacing-2xs);
  margin-left: auto;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: var(--statuses-status-active);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  gap: var(--spacing-m);
  height: 30px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div>
  <div
    class="emotion-class"
  >
    <h3
      class="emotion-class"
    >
      Payout methods
    </h3>
    <div
      class="emotion-class"
      data-testid="PayoutMethods_PayoutMethodsContainer"
    >
      <div
        class="emotion-class"
        data-testid="PayoutMethodTile_Container"
      >
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m13%2036.5c11.3431%2036.5%2010%2035.1569%2010%2033.5v14.5c10%2012.8431%2011.3431%2011.5%2013%2011.5h35c36.6569%2011.5%2038%2012.8431%2038%2014.5v18.5h29.75c26.5744%2018.5%2024%2021.0744%2024%2024.25c24%2027.4256%2026.5744%2030%2029.75%2030h38v33.5c38%2035.1569%2036.6569%2036.5%2035%2036.5h13zm29%2028c27.3431%2028%2026%2026.6569%2026%2025v23.5c26%2021.8431%2027.3431%2020.5%2029%2020.5h38v28h29zm32.5%2024.25c32.5%2025.4926%2031.4926%2026.5%2030.25%2026.5c29.0074%2026.5%2028%2025.4926%2028%2024.25c28%2023.0074%2029.0074%2022%2030.25%2022c31.4926%2022%2032.5%2023.0074%2032.5%2024.25z'%20/%3e%3c/svg%3e
              classname="css-10a16n8"
              fill="var(--actions-basic-actions-action-base)"
              size="32"
            />
            <h5
              class="emotion-class"
            >
              Bank transfer
            </h5>
          </div>
          <p
            class="emotion-class"
            data-testid="PayoutMethodTile_Primary"
          >
            primary
          </p>
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Account"
            >
              Ralph Kaminski
            </p>
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Currency"
            >
              USD
            </p>
          </div>
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_AdditionalData"
            >
              **********6819
            </p>
            <div
              class="emotion-class"
              data-testid="PayoutMethodTile_Verified"
            >
              <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                classname="css-8sv2oy"
                fill="var(--statuses-status-active)"
                size="24"
              />
              <span
                class="emotion-class"
              >
                Verified
              </span>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
        >
          <button
            class=" emotion-class"
            data-testid="PayoutMethodTile_DetailsButtonActive"
            style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
          >
            <span
              class="emotion-class"
            >
              See details
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`PayoutMethods > snapshot should match: VerifiedPayoneer 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
  margin-bottom: var(--spacing-xl);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin-top: var(--spacing-m);
  gap: var(--spacing-l);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  max-height: 32px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: var(--spacing-s);
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  color: var(--glyphs-basic-glyphs-base);
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 16px;
  line-height: 25px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: var(--spacing-2xs);
  margin-left: auto;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: var(--statuses-status-active);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  gap: var(--spacing-m);
  height: 30px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 35px;
  background-color: #F7F7F7;
  border: 1px solid var(--action-base);
  padding-top: 0;
  padding-right: 19px;
  padding-bottom: 0;
  padding-left: 19px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 33px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 380px;
  max-width: 480px;
  padding: var(--spacing-l) var(--spacing-xl);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: var(--spacing-m);
  border-radius: var(--spacing-s);
  background: var(--fills-canvas-fills-basic-canvas-fill-base);
  box-shadow: var(--box-shadow-2);
  box-sizing: border-box;
  border: var(--spacing-2xs) solid var(--actions-subtle-actions-action-subtle-selected);
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 10px;
  line-height: 20px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-s);
  border-radius: 2px;
  background: var(--statuses-status-active);
  color: var(--glyphs-basic-glyphs-inverted);
}

<div>
  <div
    class="emotion-class"
  >
    <h3
      class="emotion-class"
    >
      Payout methods
    </h3>
    <p
      class="emotion-class"
    >
      Choose your primary payout method.
    </p>
    <div
      class="emotion-class"
      data-testid="PayoutMethods_PayoutMethodsContainer"
    >
      <div
        class="emotion-class"
        data-testid="PayoutMethodTile_Container"
      >
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m13%2036.5c11.3431%2036.5%2010%2035.1569%2010%2033.5v14.5c10%2012.8431%2011.3431%2011.5%2013%2011.5h35c36.6569%2011.5%2038%2012.8431%2038%2014.5v18.5h29.75c26.5744%2018.5%2024%2021.0744%2024%2024.25c24%2027.4256%2026.5744%2030%2029.75%2030h38v33.5c38%2035.1569%2036.6569%2036.5%2035%2036.5h13zm29%2028c27.3431%2028%2026%2026.6569%2026%2025v23.5c26%2021.8431%2027.3431%2020.5%2029%2020.5h38v28h29zm32.5%2024.25c32.5%2025.4926%2031.4926%2026.5%2030.25%2026.5c29.0074%2026.5%2028%2025.4926%2028%2024.25c28%2023.0074%2029.0074%2022%2030.25%2022c31.4926%2022%2032.5%2023.0074%2032.5%2024.25z'%20/%3e%3c/svg%3e
              classname="css-10a16n8"
              fill="var(--actions-basic-actions-action-base)"
              size="32"
            />
            <h5
              class="emotion-class"
            >
              Bank transfer
            </h5>
          </div>
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Account"
            >
              Ewela Asdd
            </p>
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Currency"
            >
              PLN
            </p>
          </div>
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_AdditionalData"
            >
              ************5196
            </p>
            <div
              class="emotion-class"
              data-testid="PayoutMethodTile_Verified"
            >
              <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                classname="css-8sv2oy"
                fill="var(--statuses-status-active)"
                size="24"
              />
              <span
                class="emotion-class"
              >
                Verified
              </span>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
        >
          <button
            class=" emotion-class"
            data-testid="PayoutMethodTile_DetailsButtonActive"
            style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
          >
            <span
              class="emotion-class"
            >
              See details
            </span>
          </button>
          <button
            class=" emotion-class"
            data-testid="PayoutMethodTile_MakePrimary"
            style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
          >
            <span
              class="emotion-class"
            >
              Make primary
            </span>
          </button>
        </div>
      </div>
      <div
        class="emotion-class"
        data-testid="PayoutMethodTile_Container"
      >
        <div
          class="emotion-class"
        >
          </src/assets/icons/misc/payout-method-payoneer.svg
            classname="css-1b2xl8c"
            height="24"
            size="124"
          />
          <p
            class="emotion-class"
            data-testid="PayoutMethodTile_Primary"
          >
            primary
          </p>
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Account"
            >
              7550264
            </p>
            <p
              class="emotion-class"
              data-testid="PayoutMethodTile_Currency"
            >
              USD
            </p>
          </div>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
              data-testid="PayoutMethodTile_Active"
            >
              <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                classname="css-8sv2oy"
                fill="var(--statuses-status-active)"
                size="24"
              />
              <span
                class="emotion-class"
              >
                Active
              </span>
            </div>
          </div>
        </div>
        <div
          class="emotion-class"
        >
          <button
            class=" emotion-class"
            data-testid="PayoutMethodTile_DetailsButtonActive"
            style="flex-grow: 1; flex-shrink: 1; flex-basis: 0%;"
          >
            <span
              class="emotion-class"
            >
              See details
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;
