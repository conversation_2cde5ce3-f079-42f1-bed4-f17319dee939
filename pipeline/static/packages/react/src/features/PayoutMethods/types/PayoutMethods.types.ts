import { FieldsVisibility } from '../../../types/vendor';
import { BankDetails, PayoneerData } from './payout-methods';

export type PayoutMethodTileType = 'BankTransfer' | 'Payoneer';

export type PayoutMethodTileStatus =
  | 'active'
  | 'complete'
  | 'pending'
  | 'rejected';

export interface PayoneerDetailsProps {
  currency?: string;
  accountId?: string;
}

interface PayoutMethodZeroStateProps {
  paymentProcessorId: string;
  registrationLink?: string;
  setErrorMessage?: React.Dispatch<React.SetStateAction<string>>;
}
export type PayoutMethodTileProps =
  | (PayoutMethodZeroStateProps & { methodType: PayoutMethodTileType })
  | (PayoutMethodZeroStateProps & {
      methodType: 'BankTransfer';
      id: string;
      isPrimary: boolean;
      status: PayoutMethodTileStatus;
      bankAccountName: string;
      additionalData: {
        isSensitive: boolean;
        value: string;
      };
      currency: string;
      onSetPrimaryMethod: (id: string) => Promise<void>;
      disabledMakePrimary: boolean;
    })
  | (PayoutMethodZeroStateProps &
      PayoneerDetailsProps & {
        methodType: 'Payoneer';
        id: string;
        isPrimary: boolean;
        status: PayoutMethodTileStatus;
        onOpenDetails: () => void;
        onSetPrimaryMethod: (id: string) => Promise<void>;
        disabledMakePrimary: boolean;
      });

export type PayoutMethodProfileProps =
  | {
      methodType: 'BankTransfer';
      data: BankDetails;
      isPrimary: boolean;
      fields: FieldsVisibility[];
    }
  | {
      methodType: 'Payoneer';
      data: Pick<PayoneerData, 'account_id'>;
      isPrimary: boolean;
    };
