import styled from '@emotion/styled';
import { BodySmall } from '../../../../styles/typography';

export const IconWrapper = styled('span')({
  position: 'relative',
  width: 24,
  height: 15,
  display: 'inline-block',
  [`svg`]: {
    position: 'absolute',
    top: -2,
  },
});

export const WorkflowNameColumnWrapper = styled('div')({
  display: 'flex',
  justifyContent: 'space-between',
  position: 'relative',
});

export const WorkflowRequiredWrapper = styled('div')({
  display: 'flex',
  alignItems: 'center',
  paddingRight: '50px',
});

export const WorkflowRequiredBody = styled(BodySmall)({
  color: 'var(--glyphs-basic-glyphs-subdued-more)',
  fontStyle: 'italic',
});
