import WorkflowEditorForm from '@Worksuite/Features/Workflows/containers/WorkflowEditorContainer/WorkflowEditorForm';
import { useEffect, useState } from 'react';
import Spinner from '../../../../components/Utils/Spinner/Spinner';
import Onboarding from '../../../../services/Api/Onboarding';
import {
  OnboardingWorkflow,
  WorkflowCategory,
} from '../../../../types/onboarding';
import { useAsync } from '../../../../utils/hooks/useAsync';
import { WorkflowEditorContainerWrapper } from './WorkflowEditorContainer.styles';

const WorkflowEditorContainer = ({
  workflowId = null,
}: {
  workflowId?: number;
}) => {
  const [workflow, setWorkflow] = useState<OnboardingWorkflow>(null);

  const queryParams = new URLSearchParams(window.location.search);
  const workflowCategoryFromUrl = queryParams.get(
    'category'
  ) as WorkflowCategory;

  const initialWorkflow = {
    name: '',
    description_widget: '',
    stages_count: 0,
    vendors_count: 0,
    vendors_in_progress_count: 0,
    stages: [],
    available_for_inviting_new_partners: true,
    is_default: false,
    required: false,
    disqualified: 0,
    category: workflowCategoryFromUrl || null,
  };

  useEffect(() => {
    if (!workflowId) {
      setWorkflow(initialWorkflow);
    }
  }, [workflowCategoryFromUrl]);

  const { isLoading } = useAsync({
    skip: !workflowId,
    promise: () =>
      Promise.all([
        Onboarding.getOnboardingWorkflow(workflowId),
        Onboarding.getOnboardingWorkflowStages(workflowId),
      ]),
    onResolve: ([{ data: workflowData }, { data: stages }]) => {
      setWorkflow({
        ...workflowData,
        stages,
      });
    },
  });

  const { isLoading: isLoadingIntegrations, data: integrations } = useAsync({
    promise: () => Onboarding.getOnboardingIntegrations(),
    onResolve: ({ data }) => data,
    defaultData: {},
  });

  if (isLoading || isLoadingIntegrations) {
    return <Spinner />;
  }

  return (
    <WorkflowEditorContainerWrapper>
      <WorkflowEditorForm workflow={workflow} integrations={integrations} />
    </WorkflowEditorContainerWrapper>
  );
};

export default WorkflowEditorContainer;
