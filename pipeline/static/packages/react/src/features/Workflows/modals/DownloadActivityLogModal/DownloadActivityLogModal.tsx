import { ShortlistModal } from '../../../../components/Utils/Modals/modal.types';
import { withModal } from '../../../../components/Utils/Modals/modal.hooks';
import React, { useState } from 'react';
import i18n from '../../../../i18n/i18n';
import {
  ModalBody,
  ModalFooter,
} from '../../../../components/Utils/Modals/modal.styles';
import Button from '../../../../components/FormElements/Button/Button';
import InputDatePicker from '../../../../components/FormElements/InputDatePicker/InputDatePicker';
import { isAfter, isValid, parseISO } from 'date-fns';
import styled from '@emotion/styled';
import { generateTestId } from '../../../../utils/test.utils';

const t = (id: string) => i18n.t(`Workflows:DownloadActivityLogModal.${id}`);
const getTestId = (id: string) =>
  generateTestId(id, 'DownloadActivityLogModal')['data-testid'];

const InputRowWrapper = styled('div')({
  display: 'flex',
  gap: 'var(--spacing-m)',
  position: 'relative',
});

const InputStyled = styled('div')({
  flexGrow: 1,
});

export interface DownloadActivityLogModal extends ShortlistModal {
  type: 'download_activity_log_modal';
  workflowId: number;
}

export default withModal(
  ({ modalClose, modalDismiss, workflowId }: DownloadActivityLogModal) => {
    const [dateRange, setDateRange] = useState<[string, string]>([null, null]);
    const [errors, setErrors] = useState([]);

    const validate = () => {
      const errors = [];
      const [startDate, endDate] = dateRange;

      if (startDate && endDate) {
        const parsedStartDate = parseISO(startDate);
        const parsedEndDate = parseISO(endDate);

        if (
          isValid(parsedStartDate) &&
          isValid(parsedEndDate) &&
          isAfter(parsedStartDate, parsedEndDate)
        ) {
          errors.push(i18n.t('Errors.StartDateCannotBeAfterEndDate'));
        }
      }
      setErrors(errors);
      return !errors.length;
    };

    const handleDownload = () => {
      if (!validate()) {
        return;
      }
      const [startDate, endDate] = dateRange;
      const url = `/api/onboarding/workflow/${workflowId}/export/?start_date=${startDate ?? ''}&end_date=${endDate ?? ''}`;
      window.open(url, '_blank');
      modalClose();
    };

    return (
      <div data-testid={getTestId('Container')}>
        <ModalBody withSections>
          <div
            dangerouslySetInnerHTML={{
              __html: t('Copy'),
            }}
          ></div>
          <InputRowWrapper>
            <InputStyled>
              <InputDatePicker
                label={t('StartDate')}
                placeholder={t('SelectDatePlaceholder')}
                errors={errors[0] ? errors : []}
                isOptional
                onChange={(value) =>
                  setDateRange((prevState) => [value, prevState[1]])
                }
                testId={getTestId('StartDate')}
              />
            </InputStyled>
            <InputStyled>
              <InputDatePicker
                label={t('EndDate')}
                placeholder={t('SelectDatePlaceholder')}
                isOptional
                onChange={(value) =>
                  setDateRange((prevState) => [prevState[0], value])
                }
                testId={getTestId('EndDate')}
              />
            </InputStyled>
          </InputRowWrapper>
        </ModalBody>
        <ModalFooter>
          <Button label={t('ConfirmButton')} onClick={handleDownload} />
          <Button
            label={i18n.t('Form.Cancel')}
            variant="tertiary"
            onClick={modalDismiss}
          />
        </ModalFooter>
      </div>
    );
  },
  {
    getHeader: () => t('Header'),
  }
);
