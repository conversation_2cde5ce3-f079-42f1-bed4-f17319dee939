import i18n from 'i18next';
import DropdownSelect from '../../../../../components/FormElements/DropdownSelect/DropdownSelect';
import { openModal } from '../../../../../components/Utils/Modals/modal.helpers';
import { UserPermissionsService } from '../../../../../services/Shortlist/User/UserPermissionsService';
import {
  StageAction,
  StageActionParams,
} from '../../../../../types/onboarding';
import { noop } from '../../../../../utils';
import { getUniqueId } from '../../../../../utils/forms';
import WorkflowActionStageItems from '../../WorkflowActionStageItems/WorkflowActionStageItems';
import { ActionStageDropdownWrapper } from '../WorkflowStageTypeContent.styles';

const ActionStageTypeContent = ({
  workflowId,
  actions,
  errors,
  onChange,
  workflowCategory,
  availableActions,
}: {
  workflowId: number;
  actions: StageAction[];
  errors: Record<string, string[]>;
  onChange: (data: { actions: StageAction[] }) => void;
  workflowCategory: string;
  availableActions: { standard: string[]; custom: string[] };
}) => {
  const allActions = [...availableActions.standard, ...availableActions.custom];

  const checkActionCanBeAdded = (type: string) => {
    if (!allActions.includes(type)) {
      return false;
    } else if (
      type === 'create_contract' &&
      !UserPermissionsService.hasAccessToContracts()
    ) {
      return false;
    } else if (Array.isArray(actions)) {
      if (
        type === 'create_task' &&
        !UserPermissionsService.hasAccessToTaskTemplatesInJobOpenings()
      ) {
        return false;
      } else {
        return (
          actions.filter(({ action_name }) => action_name === type).length === 0
        );
      }
    }
    return true;
  };

  const addAction = (type: string = null) => {
    if (!Array.isArray(actions)) {
      onChange({ actions: [] });
    }
    if (checkActionCanBeAdded(type)) {
      const actionParams: StageActionParams = {};
      switch (type) {
        case 'change_vendor_type':
          actionParams.vendor_type = '';
          break;
        case 'checkr_continuous_check':
          actionParams.package_type = '';
          break;
        case 'add_to_group':
          actionParams.group = [];
          break;
        case 'remove_from_group':
          actionParams.group = [];
          break;
        case 'set_manager':
          actionParams.manager = [];
          break;
        case 'create_contract':
          openModal(
            {
              type: 'add_contract_modal',
            },
            undefined,
            'sidepanel'
          )
            .then((contract) => {
              actionParams.contract_data = contract;
              addActionToTemplate(type, actionParams);
            })
            .catch(noop);
          break;
      }

      if (type !== 'create_contract') {
        addActionToTemplate(type, actionParams);
      }
    }
  };

  const addActionToTemplate = (
    type: string,
    actionParams: StageActionParams
  ) => {
    const uid = getUniqueId();
    onChange({
      actions: [
        ...actions,
        {
          id: uid,
          action_name: type,
          action_params: actionParams,
          isNew: true,
        },
      ],
    });
  };

  const dropdownActions = availableActions.standard
    .filter(checkActionCanBeAdded)
    .map((action) => ({
      key: action,
      value: i18n.t(
        `Workflows:WorkflowActionStageItems.OnboardingWorkflowActionStage.DropdownLabels.${action}`
      ),
    }));

  const dropdownCustomActions = availableActions.custom
    .filter(checkActionCanBeAdded)
    .map((action) => ({
      key: action,
      value: action,
    }));

  const allDropdownActions = [...dropdownActions, ...dropdownCustomActions];

  return (
    <div>
      <WorkflowActionStageItems
        workflowId={workflowId}
        actionStages={actions}
        errors={errors}
        onChange={(data) => onChange({ actions: data })}
        workflowCategory={workflowCategory}
        customActions={availableActions.custom}
      />
      {allDropdownActions.length > 0 && (
        <ActionStageDropdownWrapper>
          <DropdownSelect
            label={i18n.t(
              'Onboarding:StageAdditionalInformation.action.AddAction'
            )}
            options={allDropdownActions}
            onChange={(data) => addAction(data[0]?.key)}
            iconStart="AddIcon"
          />
        </ActionStageDropdownWrapper>
      )}
    </div>
  );
};

export default ActionStageTypeContent;
