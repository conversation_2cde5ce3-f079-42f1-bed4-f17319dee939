import * as stories from './WorkflowStageList.stories';
import { composeStories } from '@storybook/react';
import { render, screen } from '@testing-library/react';
import React from 'react';

describe('WorkflowStageList - ', () => {
  test('check with snapshot', async () => {
    const { Default: WorkflowStageList } = composeStories(stories);
    const { container } = render(<WorkflowStageList />);
    expect(await screen.findByText('Information for Vendor')).toBeVisible();
    expect(container).toMatchSnapshot();
  });
});
