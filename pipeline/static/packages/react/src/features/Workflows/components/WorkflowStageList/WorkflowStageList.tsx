import WorkflowStage from '@Worksuite/Features/Workflows/components/WorkflowStage/WorkflowStage';
import { Theme, ThemeProvider } from '@emotion/react';
import { OnDragEndResponder } from '@hello-pangea/dnd';
import i18n from 'i18next';
import React, { useEffect, useState } from 'react';
import { OnboardingIcon } from '../../../../assets/icons/small';
import InputLabel from '../../../../components/FormElements/InputLabel/InputLabel';
import { DroppableTableBody } from '../../../../components/Utils/List/DroppableTableBody';
import { Table } from '../../../../components/Utils/List/List.styles';
import { OnboardingStage } from '../../../../types/onboarding';
import {
  NoStagesInfoWrapper,
  NoStagesInfoWrapperHeader,
  WorkflowStageListTrSpacer,
  WorkflowStageListWrapper,
} from '../WorkflowStage/WorkflowStage.styles';

const WorkflowStageList = ({
  workflowStages,
  onEdit,
  onChange,
}: {
  workflowStages: OnboardingStage[];
  onEdit: (stage: OnboardingStage) => void;
  onChange: (data: OnboardingStage[]) => void;
}) => {
  const [stages, setStages] = useState(workflowStages);

  useEffect(() => {
    setStages(workflowStages);
  }, [workflowStages]);

  const onDragEnd: OnDragEndResponder = (result) => {
    if (!result.destination) {
      return;
    }

    const newStages = Array.from(stages);
    const [reorderedStage] = newStages.splice(result.source.index, 1);
    newStages.splice(result.destination.index, 0, reorderedStage);
    onChange(newStages);
    setStages(newStages);
  };

  const theme = (parentTheme: Theme): Theme => ({
    ...parentTheme,
    ListComponentStyle: {
      variant: 'list',
      rowClickEnabled: true,
    },
    InputComponents: {
      variant: 'table',
    },
  });

  const onStageDelete = (stage: OnboardingStage) => {
    const newStages = stages.filter(({ id }) => id !== stage.id);
    onChange(newStages);
    setStages(newStages);
  };

  const onStageEdit = (stage: OnboardingStage) => onEdit(stage);

  if (!stages) {
    return null;
  }

  return (
    <WorkflowStageListWrapper>
      <InputLabel label={i18n.t('Workflows:WorkflowStageList.Header')} />

      {stages?.length === 0 && (
        <NoStagesInfoWrapper>
          <OnboardingIcon
            fill="var(--glyphs-basic-glyphs-subdued-more)"
            size={24}
          />
          <NoStagesInfoWrapperHeader>
            {i18n.t('Workflows:WorkflowStageList.HeaderNoStages')}
          </NoStagesInfoWrapperHeader>
        </NoStagesInfoWrapper>
      )}

      <ThemeProvider theme={theme}>
        <Table cellPadding={0} cellSpacing={0}>
          <DroppableTableBody
            items={stages}
            onDragEnd={onDragEnd}
            variant="dragAfterContent"
          >
            {(item, provided, snapshot) => (
              <>
                {item !== stages[0] && (
                  <WorkflowStageListTrSpacer className="WorkflowStageList_TrSpacer">
                    <td />
                  </WorkflowStageListTrSpacer>
                )}

                <WorkflowStage
                  stage={item}
                  provided={provided}
                  snapshot={snapshot}
                  onStageDelete={onStageDelete}
                  onStageEdit={onStageEdit}
                />
              </>
            )}
          </DroppableTableBody>
        </Table>
      </ThemeProvider>
    </WorkflowStageListWrapper>
  );
};

export default WorkflowStageList;
