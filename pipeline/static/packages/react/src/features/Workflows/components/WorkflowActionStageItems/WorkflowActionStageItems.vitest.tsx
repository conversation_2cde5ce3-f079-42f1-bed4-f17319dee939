import * as stories from './WorkflowActionStageItems.stories';
import { composeStories } from '@storybook/react';
import { render, screen } from '@testing-library/react';
import React from 'react';

const composedStories = composeStories(stories);

describe('WorkflowActionStageItems - ', () => {
  test.each(Object.entries(composedStories))(
    'snapshot should match: %s',
    async (storyName, WorkflowActionStageItems) => {
      const { container } = render(<WorkflowActionStageItems />);
      expect(
        await screen.findByText(
          'When partner enters this stage, do these actions'
        )
      ).toBeVisible();
      if (storyName === 'WithErrors') {
        expect(await screen.findByText('No manager selected')).toBeVisible();
      }
      expect(container).toMatchSnapshot();
    }
  );
});
