import i18n from 'i18next';
import { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import type { DropdownSelectItem } from '../../../../../components/FormElements/DropdownSelect/DropdownSelect.types';
import InputCheckbox from '../../../../../components/FormElements/InputCheckbox';
import StageAddQuestions from '../../../../../components/OnboardingWorkflow/components/StageAddQuestions/StageAddQuestions';
import Spinner from '../../../../../components/Utils/Spinner/Spinner';
import Onboarding from '../../../../../services/Api/Onboarding';
import {
  OnboardingStage,
  RequestDataStageType,
} from '../../../../../types/onboarding';
import { useAsync } from '../../../../../utils/hooks/useAsync';
import { generateTestId } from '../../../../../utils/test.utils';
import { RequestedDataStageTypeContentWrapper } from '../WorkflowStageTypeContent.styles';

const t = (id: string) =>
  i18n.t(`Onboarding:StageAdditionalInformation.request_data.${id}`);

const getTestId = (testId: string) =>
  generateTestId(testId, 'RequestDataStageTypeContent')['data-testid'];

const RequestDataStageTypeContent = ({
  stage,
  availableProfileFields,
  availableDocumentRequestFields,
  loadingAvailableProfileFields,
  loadingAvailableDocumentRequests,
  onChange,
  workflowId,
}: {
  stage: OnboardingStage;
  availableProfileFields: DropdownSelectItem[];
  availableDocumentRequestFields: DropdownSelectItem[];
  loadingAvailableProfileFields: boolean;
  loadingAvailableDocumentRequests: boolean;
  onChange: (data: RequestDataStageType) => void;
  workflowId: number;
}) => {
  const { isLoading: isLoadingMentions, data: contentVariables } = useAsync({
    promise: () => Onboarding.getContentVariables(workflowId),
    onResolve: ({ data }) => data,
    defaultData: [],
  });

  const isLoading =
    loadingAvailableProfileFields ||
    loadingAvailableDocumentRequests ||
    isLoadingMentions;

  const mentions = [
    {
      trigger: '#',
      data: contentVariables.map(({ name, label }) => ({
        id: name,
        display: label,
      })),
      displayTransform: (id) => `#${id}`,
      outputMask: (id) => `{${id}}`,
    },
  ];

  const { control, watch } = useForm<OnboardingStage>({
    defaultValues: {
      custom_fields_template: {
        ...stage.custom_fields_template,
        template_fields: stage.custom_fields_template.template_fields,
      },
      is_buyer_editable: stage.is_buyer_editable,
      dont_prefill_answers_with_existing_data:
        stage.dont_prefill_answers_with_existing_data,
    },
  });

  useEffect(() => {
    const { unsubscribe } = watch(onChange);
    return () => unsubscribe();
  }, [watch]);

  return (
    <RequestedDataStageTypeContentWrapper>
      {isLoading ? (
        <Spinner />
      ) : (
        <Controller
          render={({ field }) => (
            <div>
              <StageAddQuestions
                stage={stage}
                templateFields={stage.custom_fields_template.template_fields}
                onChange={field.onChange}
                availableProfileFields={availableProfileFields}
                availableDocumentRequestFields={availableDocumentRequestFields}
                mentions={mentions}
              />
            </div>
          )}
          name="custom_fields_template.template_fields"
          control={control}
        />
      )}

      {!stage.is_internal && (
        <Controller
          render={({ field }) => (
            <div>
              <InputCheckbox
                onChange={field.onChange}
                checked={field.value}
                label={t('AllowTeammatesToFillLabel')}
                hintTooltip={t('AllowTeammatesToFillTooltip')}
                testId={getTestId('IsBuyerEditableCheckbox')}
              />
            </div>
          )}
          name="is_buyer_editable"
          control={control}
        />
      )}

      <Controller
        render={({ field }) => (
          <div>
            <InputCheckbox
              onChange={field.onChange}
              checked={field.value}
              label={t('DontPrefillLabel')}
              hintTooltip={t('DontPrefillTooltip')}
              testId={getTestId('DontPrefillCheckbox')}
            />
          </div>
        )}
        name="dont_prefill_answers_with_existing_data"
        control={control}
      />
    </RequestedDataStageTypeContentWrapper>
  );
};

export default RequestDataStageTypeContent;
