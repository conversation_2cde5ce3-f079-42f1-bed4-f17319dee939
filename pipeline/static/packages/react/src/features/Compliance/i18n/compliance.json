{"ComplianceAlertMessage": {"force-compliant": "$t(terms:Partner) manually set as compliant", "compliant": "All compliance tasks are completed", "not-compliant": "Outstanding compliance tasks"}, "ComplianceAlertButtonLabel": "Review", "WorkersComplianceForm": {"PreviousQuestionButton": "Back", "NextQuestionButton": "Next", "ErrorMessage": "Something went wrong. Please try again later.", "FinishedMessage": "The questionnaire is now ready to submit.", "FinishedSubmittedMessage": "The questionnaire has been already submitted."}, "ComplianceOverview": {"ComplianceAllResponses": {"Header": "All Responses", "QualificationReasonLabel": "Qualification reason"}, "ComplianceQualificationReasons": {"Header": "Supporting Reasons"}, "ComplianceOutcomeDetails": {"Header": "Compliance Results", "Fields": {"Country": "Country", "Questionnaire": "Questionnaire", "QuestionnairePassThreshold": "Questionnaire pass threshold", "QuestionnaireFailThreshold": "Questionnaire fail threshold", "Score": "Achieved score", "ScoreStatus": "Score-based outcome", "OutcomeStatus": "Outcome status", "OutcomeOverriddenTo": "Outcome overridden to", "OverrideReason": "Override reason"}, "OutcomeStatus": {"confirmed": "Confirmed", "unconfirmed": "Unconfirmed", "overridden": "Overridden"}, "Outcome": "Outcome", "Description": {"low_risk": "The contractor is unlikely to be reclassified to employee status based upon the information provided.", "high_risk": "The contractor will almost certainly be classified as an employee.", "inconclusive": "The Outcome needs to be processed by $t(terms:Shortlist) Staff."}, "Buttons": {"ConfirmOutcome": "Confirm outcome", "OverrideOutcome": "Override outcome"}, "Modals": {"ConfirmationModal": {"Title": "Confirm the score-based Outcome?", "Body": "The score-based Outcome ({{status}})  will be confirmed."}}}}, "ComplianceAllResponses": {"Header": "All Responses", "QualificationReasonLabel": "Qualification reason"}, "ComplianceCheckLevel": {"agent_of_record": "Agent Of Record", "classify": "Classify", "null": "No Compliance Coverage"}, "ScoreStatus": {"low_risk": "Low Risk", "high_risk": "High Risk", "inconclusive": "Under review", "no_result_yet": "No result yet"}, "ScoreBasedOutcome": {"low_risk": "Low risk", "high_risk": "High risk", "inconclusive": "Inconclusive"}}