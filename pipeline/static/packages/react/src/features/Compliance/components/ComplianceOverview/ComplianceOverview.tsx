import i18n from 'i18next';
import PropTypes from 'prop-types';
import { useState } from 'react';
import PreviewForm from '../../../../components/PreviewForm/PreviewForm';
import Spinner from '../../../../components/Utils/Spinner/Spinner';
import Compliance from '../../../../services/Api/Compliance';
import { getReduxSessionUser } from '../../../../services/Reducers/User.reducer.helper';
import { HeaderH3 } from '../../../../styles/typography';
import { ComplianceOutcomeDetails } from '../../../../types/compliance';
import { useAsync } from '../../../../utils/hooks/useAsync';
import ComplianceAllResponsesSection from './ComplianceAllResponsesSection';
import { getComplianceOutcomeDetailsData } from './ComplianceOverview.helpers';
import {
  ComplianceOverviewSection,
  ComplianceOverviewWrapper,
} from './ComplianceOverview.styles';
import ComplianceQualificationReasonsSection from './ComplianceQualificationReasonsSection';

const t = (id: string) => i18n.t(`Compliance:ComplianceOverview.${id}`);

const ComplianceOverview = ({
  vendorId,
  questionnaireId,
}: {
  vendorId: number;
  questionnaireId: string;
}) => {
  const { isStaff, isBuyer } = getReduxSessionUser();
  const [complianceOutcome, setComplianceOutcome] =
    useState<ComplianceOutcomeDetails>();
  const { isLoading } = useAsync({
    promise: () => Compliance.getComplianceOutcome(vendorId, questionnaireId),
    onResolve: ({ data }) => setComplianceOutcome(data),
  });

  if (isLoading) {
    return <Spinner />;
  }

  return (
    <ComplianceOverviewWrapper>
      <ComplianceOverviewSection>
        <HeaderH3>{t('ComplianceOutcomeDetails.Header')}</HeaderH3>
        <PreviewForm
          items={getComplianceOutcomeDetailsData(
            complianceOutcome,
            vendorId,
            questionnaireId,
            isStaff,
            setComplianceOutcome
          )}
          labelColumnSize={6}
        />
      </ComplianceOverviewSection>
      {isStaff && (
        <ComplianceAllResponsesSection complianceOutcome={complianceOutcome} />
      )}
      {!isStaff && isBuyer && (
        <ComplianceQualificationReasonsSection
          complianceOutcome={complianceOutcome}
        />
      )}
    </ComplianceOverviewWrapper>
  );
};

ComplianceOverview.propTypes = {
  vendorId: PropTypes.number,
  questionnaireId: PropTypes.string,
};

export default ComplianceOverview;
