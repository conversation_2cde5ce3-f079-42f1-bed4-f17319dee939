/* eslint-disable no-console */
import { getApiMockAdapter } from '../../../../utils/storybook-utils';
import TwoFactorAuthVerificationContainer from './TwoFactorAuthVerificationContainer';
import React, { useEffect, useState } from 'react';

export default {
  title: 'Features/TwoFactorAuth/Containers/TwoFactorAuthVerificationContainer',
  component: TwoFactorAuthVerificationContainer,
};

export const Default = (args) => {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const mock = getApiMockAdapter({ delayResponse: 0 });
    mock.onPost(/\/api\/two_factor_auth\/otp_verify\//gm).reply((config) => {
      const data = JSON.parse(config.data);
      if (data.otp_token === '111111') {
        return [200];
      } else {
        return [400];
      }
    });
    setIsLoaded(true);
    return () => mock.reset();
  }, []);

  return isLoaded ? (
    <div style={{ width: '446px' }}>
      <TwoFactorAuthVerificationContainer {...args} />
    </div>
  ) : (
    'Loading...'
  );
};

Default.args = {
  onSuccessVerification: () => {
    console.log('successful verification');
  },
};
