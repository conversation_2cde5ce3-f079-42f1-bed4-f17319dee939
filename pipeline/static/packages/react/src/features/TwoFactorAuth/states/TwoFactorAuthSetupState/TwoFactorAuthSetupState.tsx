import LoginContainer from '../../../../components/Containers/Login/LoginContainer';
import { ShortlistState } from '../../../../components/Shortlist/State/ShortlistState';
import { goToAngularState } from '../../../../services/Reducers/AngularMigration.helpers';
import TwoFactorAuthSetupContainer from '@Worksuite/Features/TwoFactorAuth/containers/TwoFactorAuthSetupContainer/TwoFactorAuthSetupContainer';
import React from 'react';

const TwoFactorAuthSetupState = () => {
  const onSuccessVerification = () => {
    goToAngularState('login.two-factor-backup-codes');
  };

  return (
    <ShortlistState stateName="TWOFACTORAUTH__OTP_SETUP" hideHeader={true}>
      <LoginContainer>
        <TwoFactorAuthSetupContainer
          onSuccessVerification={onSuccessVerification}
        />
      </LoginContainer>
    </ShortlistState>
  );
};

export default TwoFactorAuthSetupState;
