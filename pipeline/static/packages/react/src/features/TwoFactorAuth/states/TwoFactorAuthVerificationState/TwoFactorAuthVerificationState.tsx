import LoginContainer from '../../../../components/Containers/Login/LoginContainer';
import { ShortlistState } from '../../../../components/Shortlist/State/ShortlistState';
import { LoginService } from '../../../../services/Shortlist/Login/LoginService';
import TwoFactorAuthVerificationContainer from '@Worksuite/Features/TwoFactorAuth/containers/TwoFactorAuthVerificationContainer/TwoFactorAuthVerificationContainer';
import React from 'react';

const TwoFactorAuthVerificationState = () => {
  const handleSuccessVerification = async () => await LoginService.login();
  return (
    <ShortlistState
      stateName="TWOFACTORAUTH__OTP_VERIFICATION"
      hideHeader={true}
    >
      <LoginContainer>
        <TwoFactorAuthVerificationContainer
          onSuccessVerification={handleSuccessVerification}
        />
      </LoginContainer>
    </ShortlistState>
  );
};

export default TwoFactorAuthVerificationState;
