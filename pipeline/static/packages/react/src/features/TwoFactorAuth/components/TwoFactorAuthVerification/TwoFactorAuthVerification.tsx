import Spinner from '../../../../components/Utils/Spinner/Spinner';
import i18n from '../../../../i18n/i18n';
import TwoFactorAuth from '../../../../services/Api/TwoFactorAuth';
import { generateTestId } from '../../../../utils/test.utils';
import CodeInput from '../CodeInput/CodeInput';
import {
  CodeInputWrapper,
  ErrorMessage,
  SpinnerAndErrorMessageWrapper,
} from './TwoFactorAuthVerification.styles';
import React from 'react';

const t = (id: string) =>
  i18n.t(`TwoFactorAuth:TwoFactorAuthVerification.${id}`);

const TwoFactorAuthVerification = ({ onSuccessVerification }) => {
  const [isLoading, setIsLoading] = React.useState(false);
  const [isError, setIsError] = React.useState(false);

  const handleCodeChange = ({ newCode }): Promise<void> => {
    setIsError(false);
    return new Promise<void>((resolve, reject) => {
      setIsLoading(true);
      TwoFactorAuth.verifyOtpToken({ otpToken: newCode })
        .then(() => {
          onSuccessVerification();
          resolve();
        })
        .catch((error) => {
          setIsError(true);
          reject(error);
        })
        .finally(() => {
          setIsLoading(false);
        });
    });
  };

  return (
    <div {...generateTestId('TwoFactorAuthVerification')}>
      <CodeInputWrapper>
        <CodeInput
          length={6}
          onCodeChange={handleCodeChange}
          isError={isError}
        />
      </CodeInputWrapper>
      <SpinnerAndErrorMessageWrapper>
        {isLoading && <Spinner />}
        {isError && (
          <ErrorMessage
            {...generateTestId('ErrorMessage', 'TwoFactorAuthVerification')}
          >
            {t('ErrorMessage')}
          </ErrorMessage>
        )}
      </SpinnerAndErrorMessageWrapper>
    </div>
  );
};

export default TwoFactorAuthVerification;
