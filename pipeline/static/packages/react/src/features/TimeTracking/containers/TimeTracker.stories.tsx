import { getApiMockAdapter } from '../../../utils/storybook-utils';
import {
  generateTask,
  generateTaskGroup,
  generateTimeEntries,
  generateTimeEntry,
  generateTimeEntryFormErrorResponse,
} from '../TimeTracking.stories.helpers';
import TimeTracker from './TimeTracker';
import { addDays, addHours } from 'date-fns';
import React, { useEffect, useState } from 'react';

export default {
  title: 'Features/TimeTracking/Containers/TimeTracker',
  component: TimeTracker,
  decorators: [],
};

const Template = ({
  tasks,
  tasksHttpStatus,
  timeEntriesCompleted,
  timeEntriesInProgress,
}) => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const mock = getApiMockAdapter({ delayResponse: 500 });
    mock.onGet(/\/api\/v\/tasks\//gm).reply(() => {
      return [tasksHttpStatus, tasks];
    });
    mock.onGet(/\/api\/time_tracking\/time_entries\//gm).reply((config) => {
      const page = config.params.page;
      const inProgress: boolean = config.params.in_progress === 'True';
      if (inProgress) {
        return [200, timeEntriesInProgress[page - 1]];
      }
      return [200, timeEntriesCompleted[page - 1]];
    });
    mock.onPost(/\/api\/time_tracking\/time_entries\//gm).reply((config) => {
      const requestData = JSON.parse(config.data);
      if (parseInt(requestData.task) === tasks[0].id) {
        return [201, generateTimeEntry(requestData)];
      }
      return [400, generateTimeEntryFormErrorResponse()];
    });
    mock.onPatch(/\/api\/time_tracking\/time_entries\/.*/gm).reply((config) => {
      const requestData = JSON.parse(config.data);
      return [
        200,
        generateTimeEntry({ uuid: requestData.uuid, description: 'updated!' }),
      ];
    });
    setIsLoading(false);
    return () => mock.reset();
  }, []);

  return !isLoading ? <TimeTracker /> : null;
};

const generatePaginatedAPIResponse = (timeEntries, entriesPerPage) => {
  if (timeEntries.length === 0) {
    return [
      {
        count: 0,
        next: null,
        previous: null,
        results: [],
      },
    ];
  }
  const totalPages = Math.ceil(timeEntries.length / entriesPerPage);
  return new Array(totalPages).fill({}).map((e, i) => {
    return {
      count: timeEntries.length,
      next: null,
      previous: null,
      results: timeEntries.slice(i * entriesPerPage, (i + 1) * entriesPerPage),
    };
  });
};

const tasks = [
  generateTask({
    name: 'MVP-001: Litwo! Ojczyzno moja! Ty jesteś jak zdrowie. Ile cię stracił.',
    task_group: generateTaskGroup({
      name: 'Project #1 with quite long name (not too much)',
    }),
  }),
  generateTask({
    name: 'MVP-002: Dziś człowieka nie miał być siedzeniem.',
    task_group: generateTaskGroup({ name: 'Project #2' }),
  }),
];

export const EmptyState = () => (
  <Template
    tasks={tasks}
    tasksHttpStatus={200}
    timeEntriesCompleted={generatePaginatedAPIResponse([], 30)}
    timeEntriesInProgress={generatePaginatedAPIResponse([], 30)}
  />
);
export const FullExample = () => (
  <Template
    tasks={tasks}
    tasksHttpStatus={200}
    timeEntriesCompleted={generatePaginatedAPIResponse(
      [
        generateTimeEntry({
          description: '5 days in the future',
          start_time: addDays(new Date(), 5).toISOString(),
          end_time: addHours(addDays(new Date(), 5), 3).toISOString(),
          duration_seconds: 3 * 3600,
          task: tasks[0],
        }),
        generateTimeEntry({
          description: 'tomorrow',
          start_time: addDays(new Date(), 1).toISOString(),
          end_time: addDays(addHours(new Date(), 4), 1).toISOString(),
          duration_seconds: 4 * 3600,
          task: tasks[0],
        }),
        generateTimeEntry({
          description: 'today 1',
          start_time: new Date().toISOString(),
          end_time: addHours(new Date(), 4).toISOString(),
          duration_seconds: 4 * 3600,
          task: tasks[0],
        }),
        generateTimeEntry({
          description: 'today 2',
          start_time: new Date().toISOString(),
          end_time: addHours(new Date(), 2).toISOString(),
          duration_seconds: 2 * 3600,
          task: tasks[0],
        }),
        generateTimeEntry({
          description: 'yesterday',
          start_time: addDays(new Date(), -1).toISOString(),
          end_time: addHours(addDays(new Date(), -1), 3).toISOString(),
          duration_seconds: 3 * 3600,
          task: tasks[0],
        }),
        generateTimeEntry({
          description: '5 days ago',
          start_time: addDays(new Date(), -5).toISOString(),
          end_time: addHours(addDays(new Date(), -5), 3).toISOString(),
          duration_seconds: 3 * 3600,
          task: tasks[0],
        }),
        generateTimeEntry({
          description: '5 days 23 hours 59 minutes 59 seconds',
          task: tasks[0],
          start_time: '2022-02-28T23:00:00Z',
          end_time: '2022-03-06T22:59:59Z',
          duration_seconds: 518399,
        }),
        generateTimeEntry({
          description: '2 months 2 days 23 hours 59 minutes 59 seconds',
          task: tasks[1],
          start_time: '2022-02-28T23:00:00Z',
          end_time: '2022-05-01T21:59:59Z',
          duration_seconds: 5356799,
        }),
        generateTimeEntry({
          description: '3 years 23 hours 59 minutes 59 seconds',
          task: tasks[0],
          start_time: '2022-02-28T23:00:00Z',
          end_time: '2025-03-01T22:59:59Z',
          duration_seconds: 94780799,
        }),
        generateTimeEntry({
          description: 'with system error',
          task: null,
          system_error:
            "This task has timesheet that can't be modified at the moment.",
        }),
        generateTimeEntry({
          description: 'no vendor',
          task: tasks[1],
          vendor: null,
        }),
        generateTimeEntry({ description: 'no task', task: null }),
        generateTimeEntry({
          description: null,
          task: generateTask({
            name: 'no description;)',
            task_group: generateTaskGroup(),
          }),
        }),
        generateTimeEntry({
          description: 'no end_time',
          task: tasks[0],
          end_time: null,
        }),
        generateTimeEntry({
          description: 'no duration_seconds',
          task: tasks[1],
          duration_seconds: null,
        }),
        generateTimeEntry({
          task: tasks[1],
          description:
            'Rumienił się, wleciała przez płotki, przez nosy, a pani ta prędka, zmieszana rozmowa w tej krucze, długie zwijały się żenił i cofnął się. dziewica krzyknęła boleśnie niewyraźnie, jak znawcy, ci wesele. Jest z tabakiery waży w pół kroku Tak każe przyzwoitość). nikt tam nie staropolska, ani jarmułek, ani jarmułek, ani małą. niełatwą, bo tak rzuciły. Tuż myśliwców herbowne klejnoty wyryte i wysoką jego postać kształtną i trudno zaradzić wolał gości niewiele z nim ją w kuca.',
        }),
        generateTimeEntry({
          task: generateTask({
            task: tasks[0],
            name: 'Rumienił się, wleciała przez płotki, przez nosy, a pani ta prędka, zmieszana rozmowa w tej krucze, długie zwijały się żenił i cofnął się. dziewica krzyknęła boleśnie niewyraźnie, jak znawcy, ci wesele. Jest z tabakiery waży w pół kroku Tak każe przyzwoitość). nikt tam nie staropolska, ani jarmułek, ani jarmułek, ani małą. niełatwą, bo tak rzuciły. Tuż myśliwców herbowne klejnoty wyryte i wysoką jego postać kształtną i trudno zaradzić wolał gości niewiele z nim ją w kuca.',
            task_group: generateTaskGroup(),
          }),
        }),
        generateTimeEntry({
          description: 'no start_time',
          task: tasks[1],
          start_time: null,
        }),
      ],
      30
    )}
    timeEntriesInProgress={generatePaginatedAPIResponse([], 30)}
  />
);
export const LongListTimerWithTask = () => (
  <Template
    tasks={tasks}
    tasksHttpStatus={200}
    timeEntriesCompleted={generatePaginatedAPIResponse(
      generateTimeEntries(155, [null, ...tasks]),
      30
    )}
    timeEntriesInProgress={generatePaginatedAPIResponse(
      [generateTimeEntry({ task: tasks[0], end_time: null })],
      30
    )}
  />
);
export const LongListTimerWithoutTask = () => (
  <Template
    tasks={tasks}
    tasksHttpStatus={200}
    timeEntriesCompleted={generatePaginatedAPIResponse(
      generateTimeEntries(155, [null, ...tasks]),
      30
    )}
    timeEntriesInProgress={generatePaginatedAPIResponse(
      [generateTimeEntry({ task: null, end_time: null })],
      30
    )}
  />
);
export const LongListTimerEmpty = () => (
  <Template
    tasks={tasks}
    tasksHttpStatus={200}
    timeEntriesCompleted={generatePaginatedAPIResponse(
      generateTimeEntries(155, [null, ...tasks]),
      30
    )}
    timeEntriesInProgress={generatePaginatedAPIResponse([], 30)}
  />
);
export const GetTasksHttp404Error = () => (
  <Template
    tasks={tasks}
    tasksHttpStatus={404}
    timeEntriesCompleted={[]}
    timeEntriesInProgress={[]}
  />
);
export const GetTasksHttp500Error = () => (
  <Template
    tasks={tasks}
    tasksHttpStatus={500}
    timeEntriesCompleted={[]}
    timeEntriesInProgress={[]}
  />
);
