export enum TimerStatus {
  LOADING = 'loading',
  INACTIVE = 'inactive',
  DEFAULT = 'default',
  ACTIVE = 'active',
  OVERTIME = 'overtime',
}

export interface TimeEntryTask {
  id: number;
  name: string;
  task_group: {
    id: number;
    name: string;
  };
  time_spent: number;
  time_limit: number;
}

export interface TimeEntryForm {
  uuid: string;
  task?: [number];
  vendor: number;
  start_time: string;
  end_time?: string;
  description?: string;
  duration_second?: number;
}
