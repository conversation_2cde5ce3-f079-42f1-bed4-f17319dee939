import Alert from '../../../../components/Alert/Alert';
import Button from '../../../../components/FormElements/Button/Button';
import { withModal } from '../../../../components/Utils/Modals/modal.hooks';
import {
  <PERSON>dal<PERSON>ody,
  ModalFooter,
} from '../../../../components/Utils/Modals/modal.styles';
import { ShortlistModal } from '../../../../components/Utils/Modals/modal.types';
import i18n from '../../../../i18n/i18n';
import Api from '../../../../services/Api';
import { getReduxEntities } from '../../../../services/Reducers/Tenant.reducer.helper';
import { Margins } from '../../../../styles/global.styles';
import { generateTestId } from '../../../../utils/test.utils';
import EntityDropdown from '../../components/EntityDropdown/EntityDropdown';
import { Entity } from '../../types/entity';
import {
  GridCellFullWidth,
  GridCellLabel,
  GridCellValue,
  GridContainer,
  GridRow,
} from '@Worksuite/Features/Core/layout/GridContainer';
import styled from '@emotion/styled';
import React, { ReactNode, useEffect, useState } from 'react';

const StyledModalBody = styled(ModalBody)({
  display: 'flex',
  flexDirection: 'column',
  gap: Margins.default,
});

const t = (id: string) => i18n.t(`Entities:DeleteDefaultEntityModal.${id}`);

export interface DeleteDefaultEntityModal extends ShortlistModal {
  type: 'delete_entity_modal';
  entityData: Entity;
}

const createRow = (key: string, value: ReactNode, fullWidth?: boolean) => (
  <GridRow>
    {fullWidth ? (
      <GridCellFullWidth>{value}</GridCellFullWidth>
    ) : (
      <>
        <GridCellLabel>{t(key)}</GridCellLabel>
        <GridCellValue>{value}</GridCellValue>
      </>
    )}
  </GridRow>
);

export default withModal(
  ({
    entityData,
    modalClose,
    modalDismiss,
    modalSize,
  }: DeleteDefaultEntityModal) => {
    const [isSaving, setIsSaving] = useState<boolean>(false);
    const [newEntity, setNewEntity] = useState<Entity>();

    const entities = getReduxEntities();
    const oneEntity =
      entities.filter((e) => e.id !== entityData.id).length === 1;

    useEffect(() => {
      if (oneEntity) {
        setNewEntity(entities.find((e) => e.id !== entityData.id));
      }
    }, []);

    return (
      <div {...generateTestId('Container', 'DeleteDefaultEntityModal')}>
        <StyledModalBody className="ModalBody">
          <GridContainer>{createRow('Deleted', entityData.name)}</GridContainer>
          <Alert
            isBold={false}
            message={oneEntity ? t('WarningOneEntity') : t('Warning')}
            size="large"
            tag="alert-warning"
          />
          <GridContainer>
            {!oneEntity &&
              createRow(
                '',
                <EntityDropdown
                  defaultEntityId={newEntity?.id}
                  excludedEntityId={entityData.id}
                  customLabel={t('Set')}
                  placeholder={t('ChooseEntity')}
                  onChange={(v) => {
                    setNewEntity(
                      entities.find((entity) => entity.id === Number(v.key))
                    );
                  }}
                />,
                true
              )}
            {oneEntity && createRow('New', newEntity?.name ?? '')}
            {newEntity && createRow('CompanyName', newEntity.company_name)}
            {newEntity &&
              newEntity.registration_number &&
              createRow('Registration', newEntity.registration_number)}
          </GridContainer>
        </StyledModalBody>
        <ModalFooter size={modalSize}>
          <Button
            label={t('Delete')}
            disabled={isSaving}
            onClick={() => {
              setIsSaving(true);
              Api.Entities.deleteEntity(entityData.id, newEntity.id)
                .then(({ data }) => {
                  Api.Entities.getCurrentEntities();
                  modalClose(data);
                })
                .finally(() => setIsSaving(false));
            }}
          />
          <Button
            variant="tertiary"
            label={i18n.t('common:Form.Cancel')}
            onClick={modalDismiss}
          />
        </ModalFooter>
      </div>
    );
  },
  {
    getHeader: () => t('Delete'),
  }
);
