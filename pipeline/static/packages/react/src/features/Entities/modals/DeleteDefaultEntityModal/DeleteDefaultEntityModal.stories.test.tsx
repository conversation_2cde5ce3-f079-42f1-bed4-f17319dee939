import * as stories from './DeleteDefaultEntityModal.stories';
import { composeStories } from '@storybook/react';
import '@testing-library/jest-dom';
import { render, screen, waitFor } from '@testing-library/react';
import React from 'react';

describe('DeleteDefaultEntityModal - ', () => {
  test('DeleteEntity - snapshot should match', async () => {
    const { DeleteEntity: DeleteDefaultEntityModal } = composeStories(stories);
    render(<DeleteDefaultEntityModal />);
    const modalContainer = screen.getByTestId('Modal');
    await waitFor(() => {
      expect(screen.getByText('Delete Entity')).toBeInTheDocument();
    });
    expect(modalContainer).toMatchSnapshot();
  });

  test('DeleteDefaultEntity - snapshot should match', async () => {
    const { DeleteDefaultEntity: DeleteDefaultEntityModal } =
      composeStories(stories);
    render(<DeleteDefaultEntityModal />);
    const modalContainer = screen.getByTestId(
      'DeleteDefaultEntityModal_Container'
    );
    await waitFor(() => {
      expect(screen.getByText('Set new default Entity')).toBeInTheDocument();
    });
    expect(modalContainer).toMatchSnapshot();
  });

  test('EntityCannotBeDeleted - snapshot should match', async () => {
    const { EntityCannotBeDeleted: DeleteDefaultEntityModal } =
      composeStories(stories);
    render(<DeleteDefaultEntityModal />);
    const modalContainer = screen.getByTestId('Modal');
    await waitFor(() => {
      expect(screen.getByText('Delete Entity')).toBeInTheDocument();
    });
    expect(modalContainer).toMatchSnapshot();
  });
});
