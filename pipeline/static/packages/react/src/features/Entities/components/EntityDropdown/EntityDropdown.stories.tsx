/* eslint-disable */
import EntityDropdown from './EntityDropdown';
import React from 'react';

export default {
  title: 'Features/Entities/Components/EntityDropdown',
  component: EntityDropdown,
};

const args = {
  onChange: () => console.log('changing...'),
};

const Template = (args) => <EntityDropdown {...args} />;

export const Default = (args) => Template(args);
Default.args = args;

export const DefaultEntity = (args) => Template(args);
DefaultEntity.args = {
  ...args,
  defaultEntityId: 5,
};

export const ExcludedEntity = (args) => Template(args);
ExcludedEntity.args = {
  ...args,
  excludedEntityId: 5,
};

export const HideLabel = (args) => Template(args);
HideLabel.args = {
  ...args,
  hideLabel: true,
};

export const CustomLabel = (args) => Template(args);
CustomLabel.args = {
  ...args,
  customLabel: 'Set new default Entity',
};
