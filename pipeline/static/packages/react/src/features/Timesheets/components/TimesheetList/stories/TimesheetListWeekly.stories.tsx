import { getTimesheetListStories } from './TimesheetList.story.helpers';
import timesheets from './timesheets-weekly.json';

export default {
  title: 'Features/Timesheets/Components/TimesheetList/Weekly',
};

const {
  Staff,
  Vendor,
  StaffWithPresets,
  VendorWithPresets,
  StaffWithTimeTracking,
  VendorWithTimeTracking,
  StaffZeroState,
} = getTimesheetListStories({
  timesheets,
  taskType: 'week',
});

export {
  Staff,
  Vendor,
  StaffWithPresets,
  VendorWithPresets,
  StaffWithTimeTracking,
  VendorWithTimeTracking,
  StaffZeroState,
};
