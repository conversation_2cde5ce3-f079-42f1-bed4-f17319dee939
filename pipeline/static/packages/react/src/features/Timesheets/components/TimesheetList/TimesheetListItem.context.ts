import { WeeklyTimesheet } from '../../types/timesheets';
import { TimesheetUtils } from './getTimesheetUtils';
import { createContext } from 'react';

interface TimesheetListItemContextType {
  onTimesheetUpdate: (timesheet: WeeklyTimesheet) => void;
  timesheetUtils: TimesheetUtils;
  debouncedDaySave: Array<any>;
  setDebouncedDaySave: React.Dispatch<React.SetStateAction<any[]>>;
  editingId: string;
  setEditingId: React.Dispatch<React.SetStateAction<string>>;
}

export const TimesheetListItemContext =
  createContext<TimesheetListItemContextType>({
    onTimesheetUpdate: () => {
      // void
    },
    timesheetUtils: null,
    /**
     * This is done so in situation when user have focus on day input and clicks on submit timesheet,
     * Api request will not be sent before sending submit action.
     */
    debouncedDaySave: [],
    setDebouncedDaySave: null,
    editingId: null,
    setEditingId: null,
  });
