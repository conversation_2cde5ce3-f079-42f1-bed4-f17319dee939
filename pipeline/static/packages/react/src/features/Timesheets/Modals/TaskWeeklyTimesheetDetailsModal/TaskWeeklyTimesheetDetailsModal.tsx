import Button from '../../../../components/FormElements/Button/Button';
import { formatDate } from '../../../../components/Timesheets/Timesheet.helpers';
import { withModal } from '../../../../components/Utils/Modals/modal.hooks';
import { ModalBody } from '../../../../components/Utils/Modals/modal.styles';
import { ShortlistModal } from '../../../../components/Utils/Modals/modal.types';
import VendorAvatarAndName from '../../../../components/Vendors/VendorAvatarAndName/VendorAvatarAndName';
import i18n from '../../../../i18n/i18n';
import Api from '../../../../services/Api';
import { getReduxTenant } from '../../../../services/Reducers/Tenant.reducer.helper';
import { getReduxSessionUser } from '../../../../services/Reducers/User.reducer.helper';
import { Margins } from '../../../../styles/global.styles';
import {
  AngularStateLink,
  HeaderH3,
  HeaderXLarge,
  Label,
} from '../../../../styles/typography';
import { Task } from '../../../../types/task';
import { Vendor } from '../../../../types/vendor';
import { minutesToHHMM } from '../../../../utils/time.utils';
import TimesheetDetails from '../../components/TimesheetDetails/TimesheetDetails';
import TimesheetStatus from '../../components/TimesheetStatus/TimesheetStatus';
import { TimesheetPermissionsService } from '../../services/TimesheetPermissionsService';
import { WeeklyTimesheet } from '../../types/timesheets';
import { convertMinutesPerDayToWorkDataWithNotes } from '../../utils/timesheets.utils';
import styled from '@emotion/styled';
import React, { useCallback, useState } from 'react';

export interface TaskWeeklyTimesheetDetailsModal extends ShortlistModal {
  type: 'task_weekly_timesheet_details_modal';
  timesheet: WeeklyTimesheet;
  task?: Task;
}

const HeaderStyled = styled('div')({
  display: 'flex',
  justifyContent: 'space-between',
});

const Space = styled('div')({
  paddingBottom: Margins.default,
});

const t = (id: string, params?: { date: string }) =>
  i18n.t(`Timesheets:TaskWeeklyTimesheetDetailsModal.${id}`, params);

export default withModal(
  ({
    modalClose,
    modalDismiss,
    timesheet,
    task,
  }: TaskWeeklyTimesheetDetailsModal) => {
    const timezone = getReduxTenant().timezone;
    const isVendor = getReduxSessionUser().isVendor;
    const [isSaving, setIsSaving] = useState(false);

    const saveTimesheet = useCallback(async () => {
      setIsSaving(true);
      Api.Timesheets.submitTaskWeekTimesheet(task.id, {
        work_data: await convertMinutesPerDayToWorkDataWithNotes(timesheet),
      })
        .then(modalClose)
        .finally(() => setIsSaving(false));
    }, []);

    return (
      <div data-testid="TaskWeeklyTimesheetDetailsModal">
        <ModalBody>
          <TimesheetStatus timesheetStatus={timesheet.status} />
          <Space />

          <HeaderStyled>
            <HeaderH3>{t('Timesheet')}</HeaderH3>
            <HeaderXLarge>
              {minutesToHHMM(timesheet.minutes_total)}
            </HeaderXLarge>
          </HeaderStyled>
          <Space />

          {timesheet.minutes_total > 0 && (
            <>
              <TimesheetDetails timesheet={timesheet} timezone={timezone} />
              <Space />
            </>
          )}

          {!isVendor && (
            <>
              <Label>{t('Partner')}</Label>
              <VendorAvatarAndName
                vendor={timesheet.vendor as unknown as Vendor}
                openNewTab={true}
              />
            </>
          )}

          {!isVendor &&
            timesheet.task &&
            typeof timesheet.task === 'object' && (
              <>
                <Space />

                <Label as="div">{t('Task')}</Label>
                <AngularStateLink
                  stateName="app.tasks.task.view.details"
                  stateParams={{
                    id: timesheet.task.id,
                  }}
                  target="_blank"
                  value={timesheet.task.name}
                />
                <Space />

                <Label as="div">{t('Project')}</Label>
                <AngularStateLink
                  stateName="app.tasks.task-group.view.tasks"
                  stateParams={{
                    id: timesheet.task.task_group,
                  }}
                  target="_blank"
                  value={timesheet.task.task_group_name}
                />
                <Space />
              </>
            )}

          {isVendor &&
            task &&
            TimesheetPermissionsService.canSubmitTimesheet(task, timesheet) && (
              <>
                <Space />
                <Button
                  label={t('Submit')}
                  disabled={isSaving}
                  onClick={saveTimesheet}
                />
                <Button
                  label={i18n.t('Form.Cancel')}
                  onClick={modalDismiss}
                  variant="tertiary"
                  style={{ marginLeft: Margins.small }}
                />
              </>
            )}
        </ModalBody>
      </div>
    );
  },
  {
    getHeader: ({ timesheet }) =>
      t('Title', {
        date: formatDate(timesheet.date_start, timesheet.date_end),
      }),
    defaultModalSize: 'lg',
  }
);
