/* eslint-disable */
import {
  closeModal,
  openModal,
} from '../../../../components/Utils/Modals/modal.helpers';
import { Task } from '../../../../types/task';
import { getApiMockAdapter } from '../../../../utils/storybook-utils';
import { withStorybookTenantManagement } from '../../../../utils/storybook.hooks';
import { WeeklyTimesheet } from '../../types/timesheets';
import task from './fixtures/task.json';
import timesheet from './fixtures/timesheet.json';
import React, { useEffect } from 'react';

export default {
  title: 'Features/Timesheets/Modals/TaskWeeklyTimesheetDetailsModal',
};

const Template = () => {
  useEffect(() => {
    const mock = getApiMockAdapter();
    mock.onGet(/\/api\/time_tracking\/time_entries\//gm).reply(() => {
      return [200, require('./fixtures/time-entries.json')];
    });
    mock.onPut(/\/api\/v\/tasks\/2\/task_time_sheet\//gm).reply(200, {
      '2021-08-02': 240,
      '2021-08-04': 180,
      '2021-08-06': 360,
    });
    openModal({
      type: 'task_weekly_timesheet_details_modal',
      timesheet: timesheet as WeeklyTimesheet,
      task: task as Task,
    })
      .then(() => console.log('ok clicked'))
      .catch(() => console.log('cancel clicked'));
    return () => closeModal('task_weekly_timesheet_details_modal');
  }, []);
  return <></>;
};

export const Default = withStorybookTenantManagement(Template, {
  defaultFeatures: [],
  defaultSessionUser: 'buyer-admin',
});
