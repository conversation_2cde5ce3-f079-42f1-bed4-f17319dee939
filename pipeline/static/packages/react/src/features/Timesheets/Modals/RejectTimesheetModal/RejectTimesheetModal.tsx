import Alert from '../../../../components/Alert/Alert';
import DataTable from '../../../../components/DataTable/DataTable';
import Button from '../../../../components/FormElements/Button/Button';
import InputCheckbox from '../../../../components/FormElements/InputCheckbox';
import InputTextArea from '../../../../components/FormElements/InputTextArea/InputTextArea';
import { AllColumnTypes } from '../../../../components/Utils/List/Column/Column';
import { withModal } from '../../../../components/Utils/Modals/modal.hooks';
import {
  ModalBody,
  ModalFooter,
  ModalSection,
} from '../../../../components/Utils/Modals/modal.styles';
import { ShortlistModal } from '../../../../components/Utils/Modals/modal.types';
import Spinner from '../../../../components/Utils/Spinner/Spinner';
import i18n from '../../../../i18n/i18n';
import Api from '../../../../services/Api';
import {
  isPromiseFulfilled,
  isPromiseRejected,
} from '../../../../services/Api/base';
import {
  displayFailureMessage,
  displaySuccessMessage,
} from '../../../../services/Reducers/Notifications.reducer.helper';
import { Payment } from '../../../../types/payments';
import { makeCancelable } from '../../../../utils/other';
import PaymentStatus from '../../../Payments/components/PaymentStatus/PaymentStatus';
import TimesheetListItem from '../../components/TimesheetList/TimesheetListItem';
import { WeeklyTimesheet } from '../../types/timesheets';
import { isMonthlyTimesheet } from '../../utils/timesheets.utils';
import { TimesheetModalDetails } from '../TimesheetsModal.helpers';
import React, { Reducer, useEffect, useReducer } from 'react';

export interface RejectTimesheetModal extends ShortlistModal {
  type: 'timesheets_reject_timesheet_modal';
  timesheetIds: number[];
}

const t = (id: string) => i18n.t(`Timesheets:RejectTimesheetModal.${id}`);

const columns: AllColumnTypes[] = [
  {
    id: 1,
    type: 'text',
    name: i18n.t('Table.Status'),
    getCellValue: (data: Payment) => (
      <PaymentStatus status={data.status} isFullWidth={true} />
    ),
    styleProps: ['MinWidthNoWrap'],
  },
  {
    id: 2,
    type: 'text',
    name: t('InvoiceToBeRejected'),
    getCellValue: (data: Payment) => data.number,
    styleProps: ['FontBold'],
  },
  {
    id: 3,
    type: 'currency',
    name: t('TotalAmount'),
    getCellCurrencyValue: (data: Payment) => String(data.total_amount),
    getCellCurrencySymbol: (data: Payment) => data.currency,
    variant: 'styled',
    thStyles: { whiteSpace: 'nowrap' },
  },
];

interface RejectTimesheetModalData {
  isLoading: boolean;
  isSaving: boolean;
  hasPayments: boolean;
  payments: Payment[];
  timesheets: WeeklyTimesheet[];
  confirmationCheckbox: boolean;
  reason: string;
  errors: {
    confirmationCheckbox: string[];
  };
  criticalError: string;
}

type RejectPaymentModalReducer = Reducer<
  RejectTimesheetModalData,
  Partial<RejectTimesheetModalData>
>;

const RejectModalBody = ({ body, footer }) => (
  <div data-testid="RejectTimesheetModal">
    <ModalBody withSections={true}>{body}</ModalBody>
    <ModalFooter className="ModalFooter">{footer}</ModalFooter>
  </div>
);

const initialRejectTimesheetModalData: RejectTimesheetModalData = {
  isLoading: true,
  isSaving: false,
  hasPayments: false,
  payments: [],
  timesheets: [],
  confirmationCheckbox: false,
  reason: '',
  errors: {
    confirmationCheckbox: [],
  },
  criticalError: null,
};

export default withModal(
  ({ modalClose, modalDismiss, timesheetIds }: RejectTimesheetModal) => {
    const [
      {
        isLoading,
        isSaving,
        hasPayments,
        payments,
        timesheets: [timesheet],
        confirmationCheckbox,
        reason,
        errors,
        criticalError,
      },
      setRejectModalData,
    ] = useReducer<RejectPaymentModalReducer>(
      (current, previous) => ({
        ...current,
        ...previous,
      }),
      initialRejectTimesheetModalData
    );

    useEffect(() => {
      const cancelablePromise = makeCancelable(
        Promise.allSettled([
          Api.Timesheets.rejectTimesheet({
            id: timesheetIds[0],
            reason: '',
            validateOnly: true,
            handleGuestRequest: true,
          }),
          Api.Timesheets.getTimesheetById(timesheetIds[0], true),
          Api.Payments.getByTimesheets(timesheetIds),
        ])
      );
      cancelablePromise.promise
        .then(([rejectTimesheetCheck, timesheetResponse, paymentsResponse]) => {
          if (isPromiseRejected(rejectTimesheetCheck)) {
            setRejectModalData({
              criticalError: rejectTimesheetCheck.reason.data.status[0],
            });
          }

          if (isPromiseFulfilled(timesheetResponse)) {
            setRejectModalData({
              timesheets: [timesheetResponse.value.data],
              isLoading: false,
            });
          }

          if (isPromiseFulfilled(paymentsResponse)) {
            // @todo - status should be filtered on BE
            const payments = paymentsResponse.value.data.filter(
              (i) => i.status !== 'rejected'
            );
            setRejectModalData({
              hasPayments: payments.length > 0,
              payments,
              isLoading: false,
            });
          }
        })
        .catch(() => {
          // catch
        });
      return () => cancelablePromise.cancel();
    }, []);

    const validate = () => {
      setRejectModalData({
        errors: initialRejectTimesheetModalData.errors,
      });
      if (hasPayments && !confirmationCheckbox) {
        setRejectModalData({
          errors: {
            confirmationCheckbox: [i18n.t('Errors.CheckToProceed')],
          },
        });
        return false;
      }
      return true;
    };

    useEffect(() => {
      if (confirmationCheckbox) {
        validate();
      }
    }, [confirmationCheckbox]);

    if (isLoading) {
      return <Spinner />;
    } else if (criticalError) {
      return (
        <RejectModalBody
          body={
            <Alert
              message={criticalError}
              tag="alert-warning"
              size="large"
              isBold={false}
            />
          }
          footer={
            <Button
              label={i18n.t('common:Actions.Close')}
              onClick={modalDismiss}
            />
          }
        />
      );
    }
    return (
      <RejectModalBody
        body={
          <>
            {!isMonthlyTimesheet(timesheet.task) && (
              <ModalSection>
                <TimesheetListItem
                  timesheet={timesheet}
                  variant="action_modal"
                />
              </ModalSection>
            )}
            <ModalSection>
              <TimesheetModalDetails timesheet={timesheet} />
            </ModalSection>

            {hasPayments && (
              <>
                <ModalSection>
                  <Alert
                    message={t('WarningTimesheet')}
                    tag="alert-warning"
                    size="large"
                    isBold={false}
                  />
                </ModalSection>
                <ModalSection data-testid="RejectTimesheetModal_PaymentList">
                  <DataTable columns={columns} items={payments} />
                </ModalSection>
              </>
            )}

            <div data-testid="RejectTimesheetModal_RejectionReason">
              <InputTextArea
                label={t('Rejection')}
                isOptional={true}
                placeholder={
                  payments.length > 0
                    ? t('RejectionPlaceholder')
                    : t('RejectionPlaceholderNoInvoice')
                }
                onChange={(reason) => setRejectModalData({ reason })}
              />
            </div>

            {hasPayments && (
              <ModalSection data-testid="RejectTimesheetModal_ConfirmationCheckbox">
                <InputCheckbox
                  onChange={(confirmationCheckbox) => {
                    setRejectModalData({
                      confirmationCheckbox,
                    });
                  }}
                  label={t('ConfirmationLabel')}
                  errors={errors.confirmationCheckbox}
                />
              </ModalSection>
            )}
          </>
        }
        footer={
          <>
            <Button
              label={i18n.t('Timesheets:BuyerAllTimesheets.Reject')}
              color="red"
              disabled={isSaving}
              onClick={() => {
                if (validate()) {
                  setRejectModalData({ isSaving: true });
                  Api.Timesheets.rejectTimesheet({
                    id: timesheetIds[0],
                    reason,
                    handleGuestRequest: true,
                  })
                    .then(() => {
                      let message = 'SuccessMessageDefault';
                      if (payments.length > 0) {
                        message = 'SuccessMessageDefaultWithInvoice';
                      }
                      displaySuccessMessage(t(message));
                      modalClose();
                    })
                    .catch(() => {
                      displayFailureMessage(t('ErrorMessage'));
                    })
                    .finally(() => setRejectModalData({ isSaving: false }));
                }
              }}
            />
            <Button
              label={i18n.t('common:Form.Cancel')}
              disabled={isSaving}
              variant="tertiary"
              onClick={modalDismiss}
            />
          </>
        }
      />
    );
  },
  {
    getHeader: () => t('Title'),
    defaultModalSize: 'lg',
  }
);
