import { DayView } from '../../../../components/Timesheets/WeeklyCalendarView/WeeklyCalendarView.styles';
import { Colors, Effects, Margins } from '../../../../styles/global.styles';
import { Body } from '../../../../styles/typography';
import styled from '@emotion/styled';

export const Section = styled('div')({
  display: 'flex',
  flexDirection: 'column',
  borderBottom: `1px solid ${Colors.grey4}`,
  padding: `${Margins.default}px 0`,
  gap: Margins.xsmall,
});

export const Calendar = styled('div')({
  display: 'flex',
  ...Effects.Borders.default,
  ...Effects.Radius.default,
  [`@media (max-width: 768px)`]: {
    flexDirection: 'column',
    padding: Margins.default,
  },
});

export const Total = styled(Body)({
  fontWeight: 600,
});

export const TotalColumn = styled(DayView)(() => ({
  boxSizing: 'border-box',
  width: '100px',
  borderLeft: `1px solid ${Colors.grey3}`,
  alignItems: 'flex-end',
  paddingRight: Margins.default,
  [`@media (max-width: 768px)`]: {
    borderTop: `1px solid ${Colors.grey3}`,
    marginTop: Margins.xsmall,
    padding: `${Margins.xsmall}px 0 0 0`,
  },
}));
