import { generateMissingDays } from '../timesheets.utils';

describe('generateMissingDays Function', () => {
  test.each`
    dateStart       | dateEnd         | weekStartDay | expectedDaysStart                                                         | expectedDaysEnd
    ${'2023-12-01'} | ${'2023-12-09'} | ${'monday'}  | ${['2023-11-27', '2023-11-28', '2023-11-29', '2023-11-30']}               | ${['2023-12-10']}
    ${'2023-12-01'} | ${'2023-12-09'} | ${'sunday'}  | ${['2023-11-26', '2023-11-27', '2023-11-28', '2023-11-29', '2023-11-30']} | ${[]}
    ${'2023-11-01'} | ${'2023-11-30'} | ${'monday'}  | ${['2023-10-30', '2023-10-31']}                                           | ${['2023-12-01', '2023-12-02', '2023-12-03']}
    ${'2023-11-01'} | ${'2023-11-30'} | ${'sunday'}  | ${['2023-10-29', '2023-10-30', '2023-10-31']}                             | ${['2023-12-01', '2023-12-02']}
  `(
    'generateMissingDays with start date $dateStart, end date $dateEnd and week starting on $weekStartDay',
    ({
      dateStart,
      dateEnd,
      weekStartDay,
      expectedDaysStart,
      expectedDaysEnd,
    }) => {
      const result = generateMissingDays(dateStart, dateEnd, weekStartDay);
      expect(result.daysStart).toEqual(expectedDaysStart);
      expect(result.daysEnd).toEqual(expectedDaysEnd);
    }
  );
});
