import { TimesheetPreset } from '../../../services/Api/Timesheets';
import { roundNumber } from '../../../utils/number.utils';
import { minutesToHHMM } from '../../../utils/time.utils';
import { WeeklyTimesheet } from '../types/timesheets';
import { isDayValueEmpty } from './timesheets.utils';
import { endOfMonth, isWithinInterval } from 'date-fns';

export const getPresetValueForDay = (
  value: number,
  allPresets: TimesheetPreset[]
) => {
  if (isDayValueEmpty(value)) {
    return null;
  }
  const formattedMinutes = minutesToHHMM(value);
  const preset = allPresets.find(({ key }) => key === formattedMinutes);
  if (preset) {
    return { label: preset.label, isEmptyFraction: preset.fraction === 0 };
  }
  return { label: formattedMinutes, isEmptyFraction: false };
};

const normalizeDaysForTimesheet = (days: number): number =>
  roundNumber(days, 3);

export const getTotalDaysForTimesheets = (
  timesheets: WeeklyTimesheet[],
  allPresets: TimesheetPreset[]
) => {
  return normalizeDaysForTimesheet(
    timesheets
      .filter((elem) => elem.status !== 'rejected')
      .reduce((p, c) => p + getTotalDaysForTimesheet(c, allPresets), 0)
  );
};

export const getTotalDaysFromTimesheetsInMonth = (
  timesheets: WeeklyTimesheet[],
  allPresets: TimesheetPreset[],
  startDate: string
) => {
  const start = new Date(startDate);
  const end = endOfMonth(new Date(startDate));

  return normalizeDaysForTimesheet(
    timesheets
      .filter((elem) => elem.status !== 'rejected')
      .reduce(
        (p, c) => p + getTotalDaysForTimesheet(c, allPresets, { start, end }),
        0
      )
  );
};

export const getTotalDaysForTimesheet = (
  timesheet: WeeklyTimesheet,
  allPresets: TimesheetPreset[],
  dates?: { start: Date; end: Date }
) => {
  return normalizeDaysForTimesheet(
    Object.entries(timesheet.minutes_per_day).reduce((sum, [date, value]) => {
      const isDateWithinInterval = dates
        ? isWithinInterval(new Date(date), dates)
        : true;
      if (!value) {
        return sum;
      }
      const preset = allPresets.find(({ hours }) => hours === value / 60);
      if (preset?.fraction > 0 && isDateWithinInterval) {
        return sum + preset.fraction / 100;
      }
      return sum;
    }, 0)
  );
};
