import {
  Colors,
  FontFamily,
  FontSize,
  Margins,
} from '../../../styles/global.styles';
import { HeaderH3, LabelSmall } from '../../../styles/typography';
import styled from '@emotion/styled';

export const LabelSmallGray = styled(LabelSmall)({
  color: Colors.grey2,
  fontSize: 12,
  fontStyle: 'italic',
  fontWeight: 400,
});

export const PreviewContainer = styled('div')({
  display: 'flex',
  flexDirection: 'column',
  gap: Margins.default,
  overflowWrap: 'break-word',
});

export const FormContainer = styled('div')({
  padding: '20px',
  border: `1px ${Colors.grey3} solid`,
  borderRadius: '4px',
});

export const Container = styled('div')({
  display: 'flex',
  flexFlow: 'column nowrap',
  maxWidth: '740px',
});

export const SectionTitle = styled(HeaderH3)({
  fontSize: 20,
  lineHeight: '22px',
  marginBottom: Margins.large,
});

export const SectionDivider = styled('div')({
  borderTop: `1px solid ${Colors.blue3}`,
  margin: `${Margins.default}px 0`,
});

export const TypographyWrapper = styled('div')<{ inRow?: boolean }>`
  margin-bottom: ${(props) => (props.inRow ? 0 : `${Margins.large}px`)};
  .value {
    margin-left: ${Margins.xsmall}px;
    margin-bottom: 0;
  }
`;

export const QuestionGroup = styled('div')({
  ['& span']: {
    paddingBottom: Margins.xsmall,
  },
  ['& > div:not(:last-child)']: {
    marginBottom: Margins.small,
  },
  ['& input, select']: {
    minHeight: '45px',
    height: '45px',
  },
});

export const RowStyled = styled('div')({
  display: 'flex',
  flexWrap: 'wrap',
  alignItems: 'flex-start',
  marginBottom: Margins.small,
  ['& > div:first-of-type']: {
    marginRight: Margins.small,
  },
  ['& > div,span']: {
    marginBottom: 0,
  },
  ['& > span,div:nth-of-type(-n + 2)']: {
    flex: 1,
  },
});

export const ButtonWrapper = styled('div')({
  display: 'flex',
  gap: '20px',
});

export const W9ConsentLabels = styled('div')({
  color: Colors.grey1,
  fontFamily: FontFamily.default,
  fontSize: FontSize.default,
  lineHeight: '20px',
  fontWeight: 400,
  marginBottom: Margins.small,
  ['& ol']: {
    margin: 0,
    paddingLeft: '20px',
    ['li:not:first-of-type']: {
      marginTop: '5px',
    },
  },
});

export const TaxInfoOnboardingWrapper = styled.div<{
  isPreview: boolean;
  hasTaxInformation: boolean;
}>`
  display: flex;
  box-sizing: border-box;
  text-decoration: none;
  align-items: center;
  font-weight: normal;
  justify-content: space-between;
  padding: 10px 15px 10px 10px;
  border-radius: 4px;
  border: 1px solid ${Colors.grey3};
  background-color: ${(props) =>
    props.isPreview && !props.hasTaxInformation ? 'white' : Colors.grey4};
  height: 65px;
  font-family: ${FontFamily.default};
  font-size: ${FontSize.default};
  line-height: 20px;
  cursor: ${(props) => (props.isPreview ? 'default' : 'pointer')};
  color: ${(props) =>
    props.isPreview && !props.hasTaxInformation ? Colors.grey2 : Colors.grey1};

  .TaxInfoOnboarding_Content {
    display: flex;
    align-items: center;

    &--optional {
      color: ${Colors.grey2};
      margin-left: ${Margins.xsmall}px;
      font-size: ${FontSize.small};
    }
  }

  .TaxInfoOnboarding_Edit {
    display: flex;
    a {
      text-decoration: none;
      font-size: ${FontSize.small};
      color: ${Colors.blue1};
      font-weight: 600;
    }
  }

  .TaxInfoOnboarding_Status {
    font-size: ${FontSize.small};
    font-style: italic;
    display: flex;
    font-weight: normal;
    &--done {
      color: ${Colors.green1};
    }
  }
`;

export const TableCell = styled('div')({
  display: 'flex',
  alignItems: 'center',

  ['& button']: {
    marginLeft: 'auto',
  },
});
