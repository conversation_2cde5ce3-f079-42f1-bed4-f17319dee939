import { openModal } from '../../../components/Utils/Modals/modal.helpers';
import i18n from '../../../i18n/i18n';
import {
  FormElementConfig,
  FormElementKey,
  FormFieldConfig,
  RadioOption,
  SelectOption,
  StatesType,
} from '../types/TaxInformation.types';
import {
  TaxInformation,
  TaxInformationFormValues,
} from '../types/tax-information';
import { ValidationValueMessage } from 'react-hook-form';

export const T_TAX_INFO_FORM = 'TaxInformation:TaxInformationForm';
export const T_FIELDS = 'TaxInformation:TaxInformationForm.fields';

export const showUnsavedChangesModal = () =>
  openModal({
    type: 'message_modal',
    header: i18n.t('TaxInformation:UnsavedChangesModal.header'),
    message: i18n.t('TaxInformation:UnsavedChangesModal.content'),
    confirmButtonLabel: i18n.t(
      'TaxInformation:UnsavedChangesModal.confirmButton'
    ),
    cancelButtonLabel: i18n.t(
      'TaxInformation:UnsavedChangesModal.cancelButton'
    ),
  });

export const isFieldConfig = (
  elementConfig: FormElementConfig
): elementConfig is FormFieldConfig => elementConfig.type === 'field';

export const isFormEmpty = (taxInformation?: TaxInformationFormValues) =>
  !taxInformation?.is_tax_information_complete;

export const isTypeBusiness = (input: TaxInformationFormValues): boolean =>
  input.type === 'business';

export const isTypeIndividual = (input: TaxInformationFormValues): boolean =>
  input.type === 'individual';

export const isCountryUS = (
  input: TaxInformationFormValues | TaxInformation
): boolean => input.country === 'United States';

export const isCountryCanada = (input: TaxInformationFormValues): boolean =>
  input.country === 'Canada';

export const isCountryOther = (input: TaxInformationFormValues): boolean =>
  input.country === 'Other';

export const isResidentialCountryUS = (
  input: TaxInformationFormValues
): boolean => {
  if (!input.residential_country) {
    return isCountryUS(input);
  }
  return input.residential_country === 'United States';
};

export const isResidentialCountryCanada = (
  input: TaxInformationFormValues
): boolean => {
  if (!input.residential_country) {
    return isCountryCanada(input);
  }
  return input.residential_country === 'Canada';
};

export const getTypeOptions = (isForm: boolean): RadioOption[] =>
  ['individual', 'business'].map((type) => ({
    value: type,
    label: i18n.t(
      `${T_FIELDS}.type.choices.${type}.${isForm ? 'form' : 'profile'}`
    ),
  }));

export const getCountriesOptions = (
  all: string[],
  excluded?: string[]
): SelectOption[] =>
  all
    .filter((country) => !excluded.includes(country))
    .map((country) => ({
      key: country,
      text: country,
    }));

export const getStateOptions = (states?: StatesType): SelectOption[] => {
  if (!states) {
    return [];
  }
  return states.states.map((state) => ({
    key: state.abbr,
    text: `${state.abbr} (${state.name})`,
  }));
};

export const getProvinceOptions = (states?: StatesType): SelectOption[] => {
  if (!states) {
    return [];
  }
  return states.provinces.map((province) => ({
    key: province.abbr,
    text: `${province.abbr} (${province.name})`,
  }));
};

export const getTaxClassificationOptions = (
  data: TaxInformationFormValues
): SelectOption[] => {
  let options: string[] = [];
  if (data.country === 'United States') {
    if (data.type === 'individual') {
      options = ['individual'];
    } else {
      options = [
        'sole_properior',
        'single_llc',
        'c_corp',
        's_corp',
        'parnership',
        'trust',
        'llc_c_corp',
        'llc_s_corp',
        'llc_partnerhip',
        'nonprofit',
      ];
    }
  }
  return options.map((type) => ({
    key: type,
    text: i18n.t(`${T_FIELDS}.tax_classification.choices.${type}`),
  }));
};

export const getDisregardedEntityOptions = (): RadioOption[] => [
  {
    value: 'disregarded-entity-yes',
    label: i18n.t(`${T_FIELDS}.disregarded_entity.choices.yes`),
  },
  {
    value: 'disregarded-entity-no',
    label: i18n.t(`${T_FIELDS}.disregarded_entity.choices.no`),
  },
];

export const showSecondStage = (data: TaxInformationFormValues) =>
  ['United States', 'Canada'].includes(data.country) ||
  (data.country === 'Other' && !!data.country_of_incorporation);

export const getCountry = (data: TaxInformationFormValues) =>
  data.country === 'Other' ? data.country_of_incorporation : data.country;

export const mapToBEPayload = ({
  country_of_incorporation,
  disregarded_entity,
  ...data
}: TaxInformationFormValues): TaxInformation => ({
  ...data,
  country: getCountry({
    country_of_incorporation,
    disregarded_entity,
    ...data,
  }),
  disregarded_entity: disregarded_entity === 'disregarded-entity-yes',
  disregarded_entity_name:
    disregarded_entity === 'disregarded-entity-yes'
      ? data.disregarded_entity_name
      : '',
});

export const mapToFormValues = (taxInformation: TaxInformation) => {
  const formValues: any = {
    ...taxInformation,
  };
  if (
    taxInformation.country !== 'United States' &&
    taxInformation.country !== 'Canada'
  ) {
    formValues.country_of_incorporation = taxInformation.country;
    formValues.country = 'Other';
  }

  formValues.disregarded_entity = taxInformation.disregarded_entity_name
    ? 'disregarded-entity-yes'
    : 'disregarded-entity-no';

  return formValues as TaxInformationFormValues;
};

export const requiredField = (
  key?: string
): { required: ValidationValueMessage<boolean> } => ({
  required: {
    value: true,
    message: i18n.t(key ?? 'Errors.ThisFieldIsRequired'),
  },
});

export const regexField = (
  regex: string,
  message: string
): { pattern: ValidationValueMessage<RegExp> } => ({
  pattern: {
    value: new RegExp(regex),
    message,
  },
});

export const getPropertyTKey = (
  key: FormElementKey | string,
  value: FormElementConfig
) => {
  if (value.type === 'field') {
    return `${value.props.translationInfo.label}.fields.${
      value.props.translationInfo.key ?? key
    }`;
  }

  return `${T_FIELDS}.${key}`;
};

export const getFieldLabel = (label: string, translationLabel?: string) =>
  translationLabel ? `${label} (${translationLabel})` : label;
