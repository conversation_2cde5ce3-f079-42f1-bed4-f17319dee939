import Button from '../../../../components/FormElements/Button/Button';
import { NotificationStateAction } from '../../../../services/Reducers/Notifications.reducer';
import { reduxStore } from '../../../../services/Redux';
import { Margins } from '../../../../styles/global.styles';
import { Body } from '../../../../styles/typography';
import Tools from '../../services/TaxInformation';
import {
  ButtonWrapper,
  FormContainer,
  SectionDivider,
} from '../../styles/TaxInformation.styles';
import { TaxInformationFormProps } from '../../types/TaxInformation.types';
import {
  TaxInformation,
  TaxInformationFormValues,
} from '../../types/tax-information';
import { getFormConfig } from '../../utils/TaxInformation.config';
import {
  getTaxClassificationOptions,
  isCountryOther,
  mapToBEPayload,
  mapToFormValues,
  requiredField,
  showUnsavedChangesModal,
} from '../../utils/TaxInformation.helpers';
import { useFormBuilder } from '../../utils/useFormBuilder';
import React, { useEffect } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

const TaxInformationForm: React.FC<TaxInformationFormProps> = (props) => {
  const {
    onSave,
    hideSaveButton = false,
    triggerSaveAction = 0,
    availableCountries,
    availableStates,
    taxInformation,
    showFirstView,
    config,
  } = props;
  const { t } = useTranslation('TaxInformation');
  const form = useForm<TaxInformationFormValues>({
    criteriaMode: 'all',
    defaultValues: {
      type: 'individual',
      country: 'United States',
      residential_country: 'United States',
      disregarded_entity: 'disregarded-entity-no',
    },
  });

  const watchedData = form.watch();

  const { formConfig, visibleFieldsNames } = getFormConfig({
    taxInformation: watchedData,
    availableCountries,
    availableStates,
    config,
  });

  const formBuilder = useFormBuilder({ formConfig, form });

  const {
    formState: {
      isSubmitting,
      isSubmitSuccessful,
      isDirty,
      dirtyFields,
      isSubmitted,
    },
    setError,
    setValue,
    clearErrors,
    handleSubmit,
    reset,
    unregister,
  } = form;

  const onTaxCountryChange = () => {
    const chosenCountry =
      isCountryOther(watchedData) && watchedData.country_of_incorporation
        ? watchedData.country_of_incorporation
        : watchedData.country;

    if (
      (dirtyFields.country || dirtyFields.country_of_incorporation) &&
      chosenCountry
    ) {
      setValue('residential_country', chosenCountry);
      setValue('state', '');
      setValue('postal_code', '');

      const nationalId = watchedData.national_id;
      unregister('national_id');
      setValue('national_id', nationalId);

      const taxIdentifier = watchedData.tax_identifier;
      unregister('tax_identifier');
      setValue('tax_identifier', taxIdentifier);

      clearErrors();
    }
  };

  const onSubmit: SubmitHandler<TaxInformationFormValues> = async (
    submittedData: TaxInformationFormValues
  ) => {
    clearErrors();
    const { country, disregarded_entity, ...transformedData } =
      mapToBEPayload(submittedData);

    const updatedTaxInformation = Object.keys(transformedData)
      .filter((key: keyof TaxInformationFormValues) =>
        visibleFieldsNames.includes(key)
      )
      .reduce<TaxInformation>(
        (acc, current) => ({ ...acc, [current]: transformedData[current] }),
        {
          country,
          disregarded_entity,
        }
      );

    if (visibleFieldsNames.includes('w9_signed_by')) {
      updatedTaxInformation.w9_signed_at = new Date().toISOString();
    }

    try {
      const response = await Tools.updateTaxInformation(updatedTaxInformation);

      reduxStore.dispatch({
        type: NotificationStateAction.ADD_NOTIFICATION,
        data: {
          tag: 'alert-success',
          message: t('TaxInformationForm.success_message'),
        },
      });

      if (onSave) {
        onSave({ valid: true });
      }

      const taxInformation = mapToFormValues(response.data);
      showFirstView(taxInformation, true);
    } catch ({ data }) {
      Object.entries(data).forEach((singleError) => {
        const [field, errors = []] = singleError as [string, string[]];
        errors.forEach((error) => {
          setError(field as keyof TaxInformationFormValues, {
            type: 'manual',
            message: error,
          });
        });
      });

      reduxStore.dispatch({
        type: NotificationStateAction.ADD_NOTIFICATION,
        data: {
          tag: 'alert-danger',
          message: t('TaxInformationForm.failed_message'),
        },
      });

      if (onSave) {
        onSave({ valid: false });
      }
    }
  };

  const handleCancel = async () => {
    try {
      if (isDirty) {
        await showUnsavedChangesModal();
      } else {
        showFirstView(taxInformation);
      }
    } catch (error) {
      showFirstView(taxInformation);
    }
  };

  useEffect(onTaxCountryChange, [
    watchedData.country,
    watchedData.country_of_incorporation,
    dirtyFields,
  ]);

  useEffect(() => {
    if (taxInformation) {
      reset(taxInformation);
    }
  }, [taxInformation]);

  useEffect(() => {
    if (triggerSaveAction > 0) {
      onSubmit(watchedData);
    }
  }, [triggerSaveAction]);

  useEffect(() => {
    if (taxInformation.is_tax_information_complete) {
      setValue('w9_consent', false);
      setValue('w9_signed_by', '');
    }
  }, [taxInformation.is_tax_information_complete]);

  useEffect(() => {
    const taxClassificationOptions = getTaxClassificationOptions(watchedData);

    const getTaxClassification = () => {
      if (taxClassificationOptions.length === 1) {
        return taxClassificationOptions[0].key.toString();
      }
      if (taxInformation.tax_classification === 'llc') {
        return 'llc';
      }
      if (
        taxClassificationOptions
          .map(({ key }) => key)
          .includes(taxInformation.tax_classification)
      ) {
        return taxInformation.tax_classification;
      }
      return null;
    };

    setValue('tax_classification', getTaxClassification());
  }, [watchedData.type, watchedData.country]);

  useEffect(() => {
    if (watchedData.tax_classification === 'llc' && isSubmitted) {
      setError('tax_classification', {
        message: requiredField().required.message,
      });
    }
  }, [isSubmitted]);

  return (
    <FormContainer>
      {!watchedData.is_tax_information_complete && watchedData.country && (
        <Body style={{ marginBottom: `${Margins.default}px` }}>
          {t('TaxInformationForm.notice')}
        </Body>
      )}
      <form onSubmit={handleSubmit(onSubmit)} noValidate>
        {formBuilder.build()}
        {!hideSaveButton && (
          <>
            <SectionDivider />
            <ButtonWrapper>
              <Button
                style={{ width: 100 }}
                variant="primary"
                label={t('common:Form.Save')}
                disabled={isSubmitting && !isSubmitSuccessful}
                testId="TaxInformationForm_BtnSave"
                type="submit"
              />
              <Button
                style={{ width: 100 }}
                label={t('common:Form.Cancel')}
                variant="tertiary"
                onClick={handleCancel}
                testId="TaxInformationForm_BtnCancel"
                type="button"
              />
            </ButtonWrapper>
          </>
        )}
      </form>
    </FormContainer>
  );
};

export default TaxInformationForm;
