import VendorCompliance from '../../../../i18n/en/components/Vendors/VendorCompliance.json';
import i18n from '../../../../i18n/i18n';
import { RequestedDocument } from '../../../../types/documents';
import { VendorComplianceDetailsItem } from '../../../../types/vendor';
import { ComplianceRowStatus } from './VendorComplianceContainer.types';

i18n.addResourceBundle('en', 'VendorCompliance', VendorCompliance);

export const getStatus = (
  item: VendorComplianceDetailsItem
): ComplianceRowStatus => {
  if (!item.status) {
    return {
      label: i18n.t('VendorCompliance:Status.NotStarted'),
      icon: 'error',
      alertTag: 'alert-danger',
    };
  }
  return {
    label: i18n.t('VendorCompliance:Status.Completed'),
    icon: 'done',
    alertTag: 'alert-success',
  };
};

export const getDocumentStatus = (
  item: VendorComplianceDetailsItem,
  requestedDocument: RequestedDocument
) => {
  const status = getStatus(item);
  if (requestedDocument) {
    if (requestedDocument.expiration_status === 'soon') {
      return {
        ...status,
        label: i18n.t('RequestedDocuments:RequestedDocument.SoonExpires'),
        icon: 'missing',
        alertTag: 'alert-warning' as const,
      };
    }
    const isStatusUploaded = requestedDocument.status === 'uploaded';
    return {
      ...status,
      label: isStatusUploaded
        ? i18n.t('VendorCompliance:Status.Completed')
        : i18n.t(
            `RequestedDocuments:RequestedDocument.Status.${requestedDocument.status}`
          ),
    };
  }
  return status;
};
