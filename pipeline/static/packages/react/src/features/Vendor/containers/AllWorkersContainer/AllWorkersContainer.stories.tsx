import ToastMessage from '../../../../components/Layout/ToastMessage/ToastMessage';
import {
  getApiMockAdapter,
  getRandomWords,
} from '../../../../utils/storybook-utils';
import AllWorkersState from '../../states/AllWorkersState';
import AllWorkersContainer from './AllWorkersContainer';

import produce from 'immer';
import React, { useEffect, useState } from 'react';
import workers from '../../../../fixtures/api/search/workers/workers-page0.json';
import vendorTypes from '../../../../fixtures/api/vendor_types/index.json';
import { withStorybookTenantManagement } from '../../../../utils/storybook.hooks';

export default {
  title: 'Features/People/AllWorkersList',
  component: AllWorkersContainer,
};

const cities = Array(100)
  .fill(true)
  .map(() => getRandomWords(4).join(' '))
  .reduce((p, c) => {
    return {
      ...p,
      [c]: Math.ceil(Math.random() * 1000) + 1,
    };
  }, {});

const DefaultTemplate = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  useEffect(() => {
    const mock = getApiMockAdapter({ delayResponse: 200 });
    mock.onGet(/\/api\/vendor_types\//gm).reply(200, vendorTypes);
    mock.onPost(/\/api\/search\/vendors\//gm).reply(() => [
      200,
      produce(workers, (draft) => {
        draft.terms.city = cities;
      }),
    ]);
    mock.onGet(/\/api\/users\/current\/preferences\/.*\//gm).reply(200, {});
    mock.onPut(/\/api\/users\/current\/preferences\/.*\//gm).reply(200, {});
    setIsLoaded(true);
    return () => mock.reset();
  }, []);
  return isLoaded ? (
    <>
      <ToastMessage />
      <AllWorkersState />
    </>
  ) : null;
};

export const Default = withStorybookTenantManagement(
  () => <DefaultTemplate />,
  {
    defaultFeatures: ['availability_calendar'],
  }
);
