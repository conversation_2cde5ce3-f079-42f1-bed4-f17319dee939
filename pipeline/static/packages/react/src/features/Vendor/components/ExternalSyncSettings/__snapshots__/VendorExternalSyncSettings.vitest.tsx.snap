// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`VendorExternalSyncSettings > should match snapshot: IntegrationSyncDisabled 1`] = `
.emotion-class {
  padding: 20px 20px 15px;
  border-bottom: 1px solid #E4E5EB;
}

.emotion-class>p {
  color: #A6ABBF;
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 11px;
  line-height: 20px;
  color: #A6ABBF;
  font-size: 12px;
  line-height: 14px;
  margin-bottom: 15px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: var(--statuses-status-initiated);
}

.emotion-class {
  position: relative;
}

.emotion-class svg {
  fill: #465AB6;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class {
  fill: #000000;
  width: 20px;
  height: 20px;
  fill-rule: evenodd;
  vertical-align: middle;
}

<div>
  <div
    class="emotion-class"
    data-testid="Vendor_ExternalSyncSettings"
  >
    <p
      class="emotion-class"
    >
      External sync settings
    </p>
    <div
      class="emotion-class"
      data-testid="Vendor_ExternalSyncSettingsIntegrationRow"
    >
      <span
        class="emotion-class"
      >
        Netsuite: 
        <span
          class="emotion-class"
        >
          disabled
        </span>
      </span>
      <a
        class=" emotion-class"
      >
        <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
          class="emotion-class"
          size="20"
        />
      </a>
    </div>
  </div>
</div>
`;

exports[`VendorExternalSyncSettings > should match snapshot: IntegrationSyncEnabled 1`] = `
.emotion-class {
  padding: 20px 20px 15px;
  border-bottom: 1px solid #E4E5EB;
}

.emotion-class>p {
  color: #A6ABBF;
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 11px;
  line-height: 20px;
  color: #A6ABBF;
  font-size: 12px;
  line-height: 14px;
  margin-bottom: 15px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: var(--statuses-status-active);
}

.emotion-class {
  position: relative;
}

.emotion-class svg {
  fill: #465AB6;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class {
  fill: #000000;
  width: 20px;
  height: 20px;
  fill-rule: evenodd;
  vertical-align: middle;
}

<div>
  <div
    class="emotion-class"
    data-testid="Vendor_ExternalSyncSettings"
  >
    <p
      class="emotion-class"
    >
      External sync settings
    </p>
    <div
      class="emotion-class"
      data-testid="Vendor_ExternalSyncSettingsIntegrationRow"
    >
      <span
        class="emotion-class"
      >
        Netsuite: 
        <span
          class="emotion-class"
        >
          enabled
        </span>
      </span>
      <a
        class=" emotion-class"
      >
        <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
          class="emotion-class"
          size="20"
        />
      </a>
    </div>
  </div>
</div>
`;
