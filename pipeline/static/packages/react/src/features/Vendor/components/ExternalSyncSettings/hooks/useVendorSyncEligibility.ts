import { VendorSyncEligibilityPerIntegration } from '@Worksuite/Features/Vendor/components/ExternalSyncSettings/types';
import { useState } from 'react';
import { useAsync } from '../../../../../utils/hooks/useAsync';
import DirectIntegrationApi from '../services/DirectIntegration';

export const useVendorSyncEligibility = (vendorId: number) => {
  // Note: To omit API force refresh of getVendorSyncEligibility, we mutate eligibility in place
  const [mutableSyncEligibility, setMutableSyncEligibility] =
    useState<VendorSyncEligibilityPerIntegration>({});

  const { isLoading, isError } = useAsync(
    {
      promise: () => DirectIntegrationApi.getVendorSyncEligibility(vendorId),
      onResolve: ({ data }) => setMutableSyncEligibility(data),
    },
    []
  );

  return {
    isLoading,
    isError,
    mutableSyncEligibility,
    setMutableSyncEligibility,
  };
};
