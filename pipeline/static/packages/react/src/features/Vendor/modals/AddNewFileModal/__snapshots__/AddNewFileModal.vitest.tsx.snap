// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`AddNewFileModal -  > snapshot should match: Default 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  width: 100%;
  row-gap: var(--spacing-l);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  border: 1px dashed #465AB6;
  border-radius: 4px;
  padding: 20px;
  color: #B4BCE0;
  background: #F5F6FA;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #B4BCE0;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-link-color)!important;
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 20px;
  cursor: pointer;
  font-weight: 600;
}

.emotion-class * {
  fill: var(--text-link-color);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: row nowrap;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  fill: #000000;
  width: 28px;
  height: 28px;
  fill-rule: evenodd;
  vertical-align: middle;
  position: absolute;
  top: 10px;
  right: 5px;
  fill: #465AB6;
  pointer-events: none;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  width: 100%;
  -webkit-column-gap: var(--spacing-l);
  column-gap: var(--spacing-l);
}

.emotion-class>div {
  width: 100%;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  margin-bottom: 5px;
}

.emotion-class>a {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-class {
  position: relative;
}

.emotion-class svg {
  fill: #465AB6;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class {
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
  font-size: 12px;
  color: #465AB6;
  vertical-align: middle;
}

.emotion-class {
  position: relative;
}

.emotion-class:hover fieldset {
  border-color: #B4BCE0;
}

.emotion-class fieldset {
  border-color: #D1D6ED;
}

.emotion-class input {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  padding-left: 9px;
  padding-right: 9px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
  vertical-align: top;
  width: 100%;
  margin: 0;
}

.emotion-class .clearButton {
  opacity: 1;
}

@media (pointer: fine) {
  .emotion-class .clearButton {
    opacity: 0;
  }

  .emotion-class:hover .clearButton,
  .emotion-class:focus-within .clearButton {
    opacity: 1;
  }
}

.emotion-class {
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.4375em;
  letter-spacing: 0.00938em;
  color: rgba(0, 0, 0, 0.87);
  box-sizing: border-box;
  position: relative;
  cursor: text;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  border-radius: 4px;
  padding-right: 14px;
  padding: 5px;
  height: 45px;
  background: #FFFFFF;
}

.emotion-class.Mui-disabled {
  color: rgba(0, 0, 0, 0.38);
  cursor: default;
}

.emotion-class:hover .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.87);
}

@media (hover: none) {
  .emotion-class:hover .MuiOutlinedInput-notchedOutline {
    border-color: rgba(0, 0, 0, 0.23);
  }
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-width: 2px;
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #1976d2;
}

.emotion-class.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: #d32f2f;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.26);
}

.emotion-class input {
  font-size: 14px;
  color: #303757;
  font-family: "Open Sans",sans-serif;
}

.emotion-class input::-webkit-input-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input::-moz-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input:-ms-input-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input::placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class .MuiOutlinedInput-notchedOutline {
  border-color: #D1D6ED;
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline,
.emotion-class:hover.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #465AB6;
}

.emotion-class:hover .MuiOutlinedInput-notchedOutline {
  border-color: #B4BCE0;
  outline: none;
}

.emotion-class.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: #FF7F8A;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: #E4E5EB;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::-webkit-input-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::-moz-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline:-ms-input-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::placeholder {
  color: blue;
}

.emotion-class {
  font: inherit;
  letter-spacing: inherit;
  color: currentColor;
  padding: 4px 0 5px;
  border: 0;
  box-sizing: content-box;
  background: none;
  height: 1.4375em;
  margin: 0;
  -webkit-tap-highlight-color: transparent;
  display: block;
  min-width: 0;
  width: 100%;
  -webkit-animation-name: mui-auto-fill-cancel;
  animation-name: mui-auto-fill-cancel;
  -webkit-animation-duration: 10ms;
  animation-duration: 10ms;
  padding: 16.5px 14px;
  padding-right: 0;
  cursor: pointer;
  padding: 9px 12px 10px;
  width: 100%;
}

.emotion-class::-webkit-input-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class::-moz-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class::-ms-input-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class:focus {
  outline: 0;
}

.emotion-class:invalid {
  box-shadow: none;
}

.emotion-class::-webkit-search-decoration {
  -webkit-appearance: none;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-webkit-input-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-moz-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-ms-input-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-webkit-input-placeholder {
  opacity: 0.42;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-moz-placeholder {
  opacity: 0.42;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-ms-input-placeholder {
  opacity: 0.42;
}

.emotion-class.Mui-disabled {
  opacity: 1;
  -webkit-text-fill-color: rgba(0, 0, 0, 0.38);
}

.emotion-class:-webkit-autofill {
  -webkit-animation-duration: 5000s;
  animation-duration: 5000s;
  -webkit-animation-name: mui-auto-fill;
  animation-name: mui-auto-fill;
}

.emotion-class:-webkit-autofill {
  border-radius: inherit;
}

.emotion-class.Mui-disabled {
  color: #A6ABBF;
  -webkit-text-fill-color: #A6ABBF;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  max-height: 2em;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.54);
  margin-left: 8px;
  position: absolute;
  right: 17px;
}

.emotion-class button {
  color: #465AB6;
  margin-right: -12px!important;
}

.emotion-class button.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class button:hover {
  background-color: transparent;
}

.emotion-class button svg {
  width: 20px;
  height: 20px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-right: -12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: 1.5rem;
}

.emotion-class {
  text-align: left;
  position: absolute;
  bottom: 0;
  right: 0;
  top: -5px;
  left: 0;
  margin: 0;
  padding: 0 8px;
  pointer-events: none;
  border-radius: inherit;
  border-style: solid;
  border-width: 1px;
  overflow: hidden;
  min-width: 0%;
  border-color: rgba(0, 0, 0, 0.23);
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 34px;
  min-height: auto;
  border-width: 2px;
  border-style: solid;
  outline: none;
  top: 0;
  -webkit-transition: border-color ease-in-out 0.15s,box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s,box-shadow ease-in-out 0.15s;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class legend {
  display: none;
}

.emotion-class {
  float: unset;
  width: auto;
  overflow: hidden;
  padding: 0;
  line-height: 11px;
  -webkit-transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
  transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
}

.emotion-class {
  z-index: 1300;
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  outline: 0;
  transform-origin: top center;
  transform-origin: bottom center;
}

.emotion-class {
  display: grid;
  grid-auto-columns: max-content auto max-content;
  grid-auto-rows: max-content auto max-content;
}

.emotion-class .MuiPickersLayout-actionBar {
  grid-column: 1/4;
  grid-row: 3;
}

.emotion-class .MuiPickersLayout-toolbar {
  grid-column: 2/4;
  grid-row: 1;
}

.emotion-class .MuiPickersLayout-shortcuts {
  grid-column: 1;
  grid-row: 2/3;
}

.emotion-class {
  grid-column: 2;
  grid-row: 2;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  overflow: hidden;
  width: 320px;
  max-height: 336px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: 0 auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 336px;
}

.emotion-class>div:first-of-type {
  position: absolute;
  width: calc(100% - 40px);
  box-sizing: border-box;
  background: #fff;
  min-height: initial;
  max-height: initial;
  height: 54px;
  padding: 16px 0 6px;
  margin: 0 20px;
}

.emotion-class>div:first-of-type+div {
  margin-top: 54px;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  font-size: 14px;
  color: #465AB6;
  font-weight: 600;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .MuiButtonBase-root {
  display: none;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .PrivatePickersFadeTransitionGroup-root {
  color: #465AB6;
  font-weight: 600;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .PrivatePickersFadeTransitionGroup-root:nth-of-type(2) div {
  margin-right: 0;
}

.emotion-class>div:first-of-type>div:nth-of-type(2) {
  width: 100%;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-top: 12px;
  margin-bottom: 4px;
  padding-left: 24px;
  padding-right: 12px;
  max-height: 40px;
  min-height: 40px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  margin-right: auto;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 500;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0.00938em;
}

.emotion-class {
  display: block;
  position: relative;
}

.emotion-class {
  margin-right: 6px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  padding: 5px;
  font-size: 1.125rem;
  margin-right: auto;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: 1.5rem;
  will-change: transform;
  -webkit-transition: -webkit-transform 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-right: -12px;
  color: #465AB6;
  padding: 5px;
  width: 34px;
  height: 34px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class svg {
  width: 24px;
  height: 24px;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: inherit;
}

.emotion-class {
  width: 24px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-left: -12px;
  color: #465AB6;
  padding: 5px;
  width: 34px;
  height: 34px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class svg {
  width: 24px;
  height: 24px;
}

.emotion-class {
  display: block;
  position: relative;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  margin: 0;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  font-weight: 600;
  font-size: 12px;
  width: 36px;
  height: 40px;
  margin: 0 2px;
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: rgba(0, 0, 0, 0.6);
}

.emotion-class {
  display: block;
  position: relative;
  overflow-x: hidden;
  min-height: 240px;
}

.emotion-class>* {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
}

.emotion-class .MuiPickersSlideTransition-slideEnter-left {
  will-change: transform;
  -webkit-transform: translate(100%);
  -moz-transform: translate(100%);
  -ms-transform: translate(100%);
  transform: translate(100%);
  z-index: 1;
}

.emotion-class .MuiPickersSlideTransition-slideEnter-right {
  will-change: transform;
  -webkit-transform: translate(-100%);
  -moz-transform: translate(-100%);
  -ms-transform: translate(-100%);
  transform: translate(-100%);
  z-index: 1;
}

.emotion-class .MuiPickersSlideTransition-slideEnterActive {
  -webkit-transform: translate(0%);
  -moz-transform: translate(0%);
  -ms-transform: translate(0%);
  transform: translate(0%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
}

.emotion-class .MuiPickersSlideTransition-slideExit {
  -webkit-transform: translate(0%);
  -moz-transform: translate(0%);
  -ms-transform: translate(0%);
  transform: translate(0%);
}

.emotion-class .MuiPickersSlideTransition-slideExitActiveLeft-left {
  will-change: transform;
  -webkit-transform: translate(-100%);
  -moz-transform: translate(-100%);
  -ms-transform: translate(-100%);
  transform: translate(-100%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  z-index: 0;
}

.emotion-class .MuiPickersSlideTransition-slideExitActiveLeft-right {
  will-change: transform;
  -webkit-transform: translate(100%);
  -moz-transform: translate(100%);
  -ms-transform: translate(100%);
  transform: translate(100%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  z-index: 0;
}

.emotion-class {
  overflow: hidden;
}

.emotion-class button {
  margin-right: 2px!important;
}

.emotion-class {
  margin: 2px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  color: rgba(0, 0, 0, 0.6);
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:not(.Mui-selected) {
  border: 1px solid rgba(0, 0, 0, 0.6);
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class:not(.Mui-selected) {
  -webkit-transition: 220ms all;
  transition: 220ms all;
}

.emotion-class:not(.Mui-selected):before {
  content: '';
  display: inline-block;
  border-left: 7px solid transparent;
  border-bottom: 7px solid #465AB6;
  border-top-color: rgba(0, 0, 0, 0.2);
  position: absolute;
  bottom: 4px;
  right: 4px;
}

.emotion-class:not(.Mui-selected):hover:before {
  display: none;
}

.emotion-class {
  overflow: hidden;
  pointer-events: none;
  position: absolute;
  z-index: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: inherit;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 150%;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  width: 100%;
  box-sizing: border-box;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #A6ABBF;
  text-align: right;
  padding-left: 10px;
  white-space: nowrap;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class:hover .checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class:hover>.checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6!important;
}

.emotion-class {
  margin: 1px 0 -1px 6px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  color: #303757;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  margin-top: 30px;
  border-top: 1px solid #E4E5EB;
  padding-top: 40px;
}

@media (max-width: 768px) {
  .emotion-class {
    padding-top: 15px;
  }
}

.emotion-class button,
.emotion-class a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-base);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: #FFFFFF;
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(1.15);
  filter: brightness(1.15);
}

.emotion-class {
  color: #FFFFFF;
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div
  data-testid="AddNewFileModal_Container"
>
  <div
    class="emotion-class"
  >
    <div>
      <div
        style="margin-bottom: var(--spacing-xs);"
      >
        <span
          class="emotion-class"
          data-testid="InputLabel_UploadFile"
        >
          Upload file
        </span>
      </div>
      <div
        class="emotion-class"
        data-testid="AddNewFileModal_FileInput"
        role="presentation"
        tabindex="0"
      >
        <span
          class="emotion-class"
        >
          <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m33.1667%2036.5c33.3538%2036.4927%2033.5428%2036.5%2033.7333%2036.5c38.0227%2036.5%2041.5%2032.8048%2041.5%2028.2465c41.5%2023.6882%2038.0227%2019.993%2033.7333%2019.993c33.6329%2019.993%2033.5329%2019.9951%2033.4333%2019.9991c33.4333%2015.3025%2029.8516%2011.5%2025.4333%2011.5c21.7579%2011.5%2018.6613%2014.1314%2017.724%2017.7164c16.9359%2017.186%2016.0017%2016.8789%2015%2016.8789c12.1833%2016.8789%209.9%2019.3066%209.9%2022.3014c9.9%2023.225%2010.1172%2024.0947%2010.5002%2024.8559c8.18692%2025.5867%206.5%2027.8616%206.5%2030.5549c6.5%2033.7434%208.86436%2036.3456%2011.8333%2036.4934l21.6518%2036.5v28.4149h17.2143l24%2020.4286l30.7857%2028.4149h26.3482v36.5h33.1667z'/%3e%3c/svg%3e
            classname="css-1ejrr40"
            fill="var(--fills-badge-fills-badge-fill-neutral)"
            size="22"
          />
          Drag and drop file or 
          <a
            class="emotion-class"
          >
            upload
          </a>
          .
        </span>
      </div>
      <div
        class="emotion-class"
      >
        <div />
      </div>
    </div>
    <div>
      <div
        style="margin-bottom: 5px;"
      >
        <span
          class="emotion-class"
          data-testid="InputLabel_DocumentType"
        >
          Document type
        </span>
      </div>
      <div
        class="emotion-class"
      >
        <select
          class="emotion-class"
          data-testid="Select_DocumentType"
        >
          <option
            value=""
          >
            Choose Type
          </option>
          <option
            data-testid="SelectOption_License"
            value="license"
          >
            Professional license
          </option>
          <option
            data-testid="SelectOption_Certificate"
            value="certificate"
          >
            Certificate
          </option>
          <option
            data-testid="SelectOption_Insurance"
            value="insurance"
          >
            General Liability insurance
          </option>
          <option
            data-testid="SelectOption_Nda"
            value="nda"
          >
            Non Disclosure Agreement
          </option>
          <option
            data-testid="SelectOption_Terms"
            value="terms"
          >
            Terms & Conditions
          </option>
          <option
            data-testid="SelectOption_Sa"
            value="sa"
          >
            Services agreement
          </option>
          <option
            data-testid="SelectOption_Resume"
            value="resume"
          >
            Resume/CV
          </option>
          <option
            data-testid="SelectOption_OtherDocument"
            value="other document"
          >
            Other document
          </option>
          <option
            data-testid="SelectOption_TaxForm"
            value="tax form"
          >
            IRS Form
          </option>
          <option
            data-testid="SelectOption_Diploma"
            value="diploma"
          >
            Diploma
          </option>
          <option
            data-testid="SelectOption_Transcripts"
            value="transcripts"
          >
            Transcripts
          </option>
          <option
            data-testid="SelectOption_DriversLicence"
            value="drivers licence"
          >
            Drivers license
          </option>
        </select>
        <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
          class="emotion-class"
          size="28"
        />
      </div>
    </div>
    <div
      class="emotion-class"
    >
      <div>
        <div
          class="emotion-class"
        >
          <div>
            <span
              class="emotion-class"
              data-testid="InputLabel_IssueDate"
            >
              Issue date
              <span
                class="emotion-class"
                style="margin-left: 10px; color: #A6ABBF;"
              >
                Optional
              </span>
            </span>
          </div>
          <a
            class=" emotion-class"
            data-testid="InputActionLabel_Button"
            style="text-align: right;"
          >
            <span
              class="emotion-class"
            >
              Reset
            </span>
          </a>
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="MuiFormControl-root MuiTextField-root emotion-class"
            data-testid="InputDatePicker_AddNewFileModalIssueDateInput"
          >
            <div
              class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd emotion-class"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd emotion-class"
                id=":r1:"
                inputmode="text"
                placeholder="MM/DD/YYYY"
                type="text"
                value=""
              />
              <div
                class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium emotion-class"
              >
                <button
                  aria-label="Choose date"
                  class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium emotion-class"
                  tabindex="0"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium emotion-class"
                    data-testid="CalendarIcon"
                    focusable="false"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
                    />
                  </svg>
                </button>
              </div>
              <fieldset
                aria-hidden="true"
                class="MuiOutlinedInput-notchedOutline emotion-class"
              >
                <legend
                  class="emotion-class"
                >
                  <span
                    aria-hidden="true"
                    class="notranslate"
                  >
                    ​
                  </span>
                </legend>
              </fieldset>
            </div>
          </div>
        </div>
        <div
          class="MuiPopper-root MuiPickersPopper-root emotion-class"
          data-testid="InputDatePickerCalendar_AddNewFileModalIssueDateInput"
          role="dialog"
          style="position: fixed; top: 0px; left: 0px; display: none;"
        >
          <div
            data-testid="sentinelStart"
            tabindex="-1"
          />
          <div
            class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation8 MuiPickersPopper-paper emotion-class"
            style="--Paper-shadow: 0px 5px 5px -3px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12); opacity: 0; transform: scale(0.75, 0.5625); visibility: hidden;"
            tabindex="-1"
          >
            <div
              class="MuiPickersLayout-root emotion-class"
            >
              <div
                class="MuiPickersLayout-contentWrapper emotion-class"
              >
                <div
                  class="MuiDateCalendar-root emotion-class"
                >
                  <div
                    class="MuiPickersCalendarHeader-root emotion-class"
                  >
                    <div
                      aria-live="polite"
                      class="MuiPickersCalendarHeader-labelContainer emotion-class"
                      role="presentation"
                    >
                      <div
                        class="MuiPickersFadeTransitionGroup-root emotion-class"
                      >
                        <div
                          class="MuiPickersCalendarHeader-label emotion-class"
                          id=":r4:-grid-label"
                          style="opacity: 1;"
                        >
                          February 2025
                        </div>
                      </div>
                      <button
                        aria-label="calendar view is open, switch to year view"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall MuiPickersCalendarHeader-switchViewButton emotion-class"
                        tabindex="0"
                        type="button"
                      >
                        <svg
                          aria-hidden="true"
                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiPickersCalendarHeader-switchViewIcon emotion-class"
                          data-testid="ArrowDropDownIcon"
                          focusable="false"
                          viewBox="0 0 24 24"
                        >
                          <path
                            d="M7 10l5 5 5-5z"
                          />
                        </svg>
                      </button>
                    </div>
                    <div
                      class="MuiPickersArrowSwitcher-root emotion-class"
                      style="opacity: 1; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
                    >
                      <button
                        aria-label="Previous month"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-previousIconButton emotion-class"
                        tabindex="0"
                        title="Previous month"
                        type="button"
                      >
                        <svg
                          aria-hidden="true"
                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-leftArrowIcon emotion-class"
                          data-testid="ArrowLeftIcon"
                          focusable="false"
                          viewBox="0 0 24 24"
                        >
                          <path
                            d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"
                          />
                        </svg>
                      </button>
                      <div
                        class="MuiPickersArrowSwitcher-spacer emotion-class"
                      />
                      <button
                        aria-label="Next month"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeStart MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-nextIconButton emotion-class"
                        tabindex="0"
                        title="Next month"
                        type="button"
                      >
                        <svg
                          aria-hidden="true"
                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-rightArrowIcon emotion-class"
                          data-testid="ArrowRightIcon"
                          focusable="false"
                          viewBox="0 0 24 24"
                        >
                          <path
                            d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                  <div
                    class="MuiPickersFadeTransitionGroup-root MuiDateCalendar-viewTransitionContainer emotion-class"
                  >
                    <div
                      style="opacity: 1;"
                    >
                      <div
                        aria-labelledby=":r4:-grid-label"
                        class="MuiDayCalendar-root emotion-class"
                        role="grid"
                      >
                        <div
                          class="MuiDayCalendar-header emotion-class"
                          role="row"
                        >
                          <span
                            aria-label="Sunday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            S
                          </span>
                          <span
                            aria-label="Monday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            M
                          </span>
                          <span
                            aria-label="Tuesday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            T
                          </span>
                          <span
                            aria-label="Wednesday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            W
                          </span>
                          <span
                            aria-label="Thursday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            T
                          </span>
                          <span
                            aria-label="Friday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            F
                          </span>
                          <span
                            aria-label="Saturday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            S
                          </span>
                        </div>
                        <div
                          class="MuiPickersSlideTransition-root MuiDayCalendar-slideTransition emotion-class"
                          role="presentation"
                        >
                          <div
                            class="MuiDayCalendar-monthContainer emotion-class"
                            role="rowgroup"
                          >
                            <div
                              aria-rowindex="1"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1737849600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                26
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1737936000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                27
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738022400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                28
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738108800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                29
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738195200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                30
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738281600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                31
                              </button>
                              <button
                                aria-colindex="7"
                                aria-current="date"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-today emotion-class"
                                data-timestamp="1738368000000"
                                role="gridcell"
                                tabindex="0"
                                type="button"
                              >
                                1
                                <span
                                  class="MuiTouchRipple-root emotion-class"
                                />
                              </button>
                            </div>
                            <div
                              aria-rowindex="2"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738454400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                2
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738540800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                3
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738627200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                4
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738713600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                5
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738800000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                6
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738886400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                7
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738972800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                8
                              </button>
                            </div>
                            <div
                              aria-rowindex="3"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739059200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                9
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739145600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                10
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739232000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                11
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739318400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                12
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739404800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                13
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739491200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                14
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739577600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                15
                              </button>
                            </div>
                            <div
                              aria-rowindex="4"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739664000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                16
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739750400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                17
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739836800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                18
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739923200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                19
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740009600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                20
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740096000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                21
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740182400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                22
                              </button>
                            </div>
                            <div
                              aria-rowindex="5"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740268800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                23
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740355200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                24
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740441600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                25
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740528000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                26
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740614400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                27
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740700800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                28
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1740787200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                1
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            data-testid="sentinelEnd"
            tabindex="-1"
          />
        </div>
      </div>
      <div>
        <div
          class="emotion-class"
        >
          <div>
            <span
              class="emotion-class"
              data-testid="InputLabel_ExpiryDate"
            >
              Expiry date
              <span
                class="emotion-class"
                style="margin-left: 10px; color: #A6ABBF;"
              >
                Optional
              </span>
            </span>
          </div>
          <a
            class=" emotion-class"
            data-testid="InputActionLabel_Button"
            style="text-align: right;"
          >
            <span
              class="emotion-class"
            >
              Reset
            </span>
          </a>
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="MuiFormControl-root MuiTextField-root emotion-class"
            data-testid="InputDatePicker_AddNewFileModalExpiryDateInput"
          >
            <div
              class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd emotion-class"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd emotion-class"
                id=":r8:"
                inputmode="text"
                placeholder="MM/DD/YYYY"
                type="text"
                value=""
              />
              <div
                class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium emotion-class"
              >
                <button
                  aria-label="Choose date"
                  class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium emotion-class"
                  tabindex="0"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium emotion-class"
                    data-testid="CalendarIcon"
                    focusable="false"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
                    />
                  </svg>
                </button>
              </div>
              <fieldset
                aria-hidden="true"
                class="MuiOutlinedInput-notchedOutline emotion-class"
              >
                <legend
                  class="emotion-class"
                >
                  <span
                    aria-hidden="true"
                    class="notranslate"
                  >
                    ​
                  </span>
                </legend>
              </fieldset>
            </div>
          </div>
        </div>
        <div
          class="MuiPopper-root MuiPickersPopper-root emotion-class"
          data-testid="InputDatePickerCalendar_AddNewFileModalExpiryDateInput"
          role="dialog"
          style="position: fixed; top: 0px; left: 0px; display: none;"
        >
          <div
            data-testid="sentinelStart"
            tabindex="-1"
          />
          <div
            class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation8 MuiPickersPopper-paper emotion-class"
            style="--Paper-shadow: 0px 5px 5px -3px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12); opacity: 0; transform: scale(0.75, 0.5625); visibility: hidden;"
            tabindex="-1"
          >
            <div
              class="MuiPickersLayout-root emotion-class"
            >
              <div
                class="MuiPickersLayout-contentWrapper emotion-class"
              >
                <div
                  class="MuiDateCalendar-root emotion-class"
                >
                  <div
                    class="MuiPickersCalendarHeader-root emotion-class"
                  >
                    <div
                      aria-live="polite"
                      class="MuiPickersCalendarHeader-labelContainer emotion-class"
                      role="presentation"
                    >
                      <div
                        class="MuiPickersFadeTransitionGroup-root emotion-class"
                      >
                        <div
                          class="MuiPickersCalendarHeader-label emotion-class"
                          id=":rb:-grid-label"
                          style="opacity: 1;"
                        >
                          February 2025
                        </div>
                      </div>
                      <button
                        aria-label="calendar view is open, switch to year view"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall MuiPickersCalendarHeader-switchViewButton emotion-class"
                        tabindex="0"
                        type="button"
                      >
                        <svg
                          aria-hidden="true"
                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiPickersCalendarHeader-switchViewIcon emotion-class"
                          data-testid="ArrowDropDownIcon"
                          focusable="false"
                          viewBox="0 0 24 24"
                        >
                          <path
                            d="M7 10l5 5 5-5z"
                          />
                        </svg>
                      </button>
                    </div>
                    <div
                      class="MuiPickersArrowSwitcher-root emotion-class"
                      style="opacity: 1; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
                    >
                      <button
                        aria-label="Previous month"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-previousIconButton emotion-class"
                        tabindex="0"
                        title="Previous month"
                        type="button"
                      >
                        <svg
                          aria-hidden="true"
                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-leftArrowIcon emotion-class"
                          data-testid="ArrowLeftIcon"
                          focusable="false"
                          viewBox="0 0 24 24"
                        >
                          <path
                            d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"
                          />
                        </svg>
                      </button>
                      <div
                        class="MuiPickersArrowSwitcher-spacer emotion-class"
                      />
                      <button
                        aria-label="Next month"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeStart MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-nextIconButton emotion-class"
                        tabindex="0"
                        title="Next month"
                        type="button"
                      >
                        <svg
                          aria-hidden="true"
                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-rightArrowIcon emotion-class"
                          data-testid="ArrowRightIcon"
                          focusable="false"
                          viewBox="0 0 24 24"
                        >
                          <path
                            d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                  <div
                    class="MuiPickersFadeTransitionGroup-root MuiDateCalendar-viewTransitionContainer emotion-class"
                  >
                    <div
                      style="opacity: 1;"
                    >
                      <div
                        aria-labelledby=":rb:-grid-label"
                        class="MuiDayCalendar-root emotion-class"
                        role="grid"
                      >
                        <div
                          class="MuiDayCalendar-header emotion-class"
                          role="row"
                        >
                          <span
                            aria-label="Sunday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            S
                          </span>
                          <span
                            aria-label="Monday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            M
                          </span>
                          <span
                            aria-label="Tuesday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            T
                          </span>
                          <span
                            aria-label="Wednesday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            W
                          </span>
                          <span
                            aria-label="Thursday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            T
                          </span>
                          <span
                            aria-label="Friday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            F
                          </span>
                          <span
                            aria-label="Saturday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            S
                          </span>
                        </div>
                        <div
                          class="MuiPickersSlideTransition-root MuiDayCalendar-slideTransition emotion-class"
                          role="presentation"
                        >
                          <div
                            class="MuiDayCalendar-monthContainer emotion-class"
                            role="rowgroup"
                          >
                            <div
                              aria-rowindex="1"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1737849600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                26
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1737936000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                27
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738022400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                28
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738108800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                29
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738195200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                30
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738281600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                31
                              </button>
                              <button
                                aria-colindex="7"
                                aria-current="date"
                                aria-selected="false"
                                class="MuiButtonBase-root Mui-focusVisible MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-today emotion-class"
                                data-timestamp="1738368000000"
                                role="gridcell"
                                tabindex="0"
                                type="button"
                              >
                                1
                              </button>
                            </div>
                            <div
                              aria-rowindex="2"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738454400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                2
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738540800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                3
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738627200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                4
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738713600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                5
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738800000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                6
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738886400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                7
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738972800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                8
                              </button>
                            </div>
                            <div
                              aria-rowindex="3"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739059200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                9
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739145600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                10
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739232000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                11
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739318400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                12
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739404800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                13
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739491200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                14
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739577600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                15
                              </button>
                            </div>
                            <div
                              aria-rowindex="4"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739664000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                16
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739750400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                17
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739836800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                18
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739923200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                19
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740009600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                20
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740096000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                21
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740182400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                22
                              </button>
                            </div>
                            <div
                              aria-rowindex="5"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740268800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                23
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740355200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                24
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740441600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                25
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740528000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                26
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740614400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                27
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740700800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                28
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1740787200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                1
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            data-testid="sentinelEnd"
            tabindex="-1"
          />
        </div>
      </div>
    </div>
    <div>
      <div
        style="margin-bottom: 5px;"
      >
        <span
          class="emotion-class"
          data-testid="InputLabel_Message"
        >
          Message
          <span
            class="emotion-class"
            style="margin-left: 10px; color: #A6ABBF;"
          >
            Optional
          </span>
        </span>
      </div>
      <div
        class="emotion-class"
      >
        <textarea
          class="emotion-class"
          maxlength="300"
          placeholder="Add short note..."
          variant="default"
        />
      </div>
      <div
        class="emotion-class"
      >
        <div />
        <div
          class="emotion-class"
        >
          0
           / 
          300
        </div>
      </div>
    </div>
    <div>
      <div
        class="emotion-class"
      >
        <label
          class="emotion-class"
          data-testid="InputCheckbox"
        >
          <span
            class="emotion-class"
          >
            <input
              type="checkbox"
            />
            <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
              classname="checkboxOffIcon css-1dmky3q"
              fill="#D1D6ED"
            />
          </span>
          <span
            class="emotion-class"
          >
            <span
              aria-label=""
              class=" emotion-class"
              data-mui-internal-clone-element="true"
              data-testid="InputCheckboxLabel_AddNewFileModalVisibleToPartnerCheckbox"
            >
              Visible to partner
            </span>
          </span>
        </label>
      </div>
    </div>
  </div>
  <div
    class="emotion-class"
  >
    <button
      class=" emotion-class"
      data-testid="AddNewFileModal_SaveButton"
    >
      <span
        class="emotion-class"
      >
        Save changes
      </span>
    </button>
    <button
      class=" emotion-class"
      data-testid="AddNewFileModal_CancelButton"
    >
      <span
        class="emotion-class"
      >
        Cancel
      </span>
    </button>
  </div>
</div>
`;

exports[`AddNewFileModal -  > snapshot should match: Edit 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  width: 100%;
  row-gap: var(--spacing-l);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: #303757;
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 20px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  cursor: pointer;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: row nowrap;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  fill: #000000;
  width: 28px;
  height: 28px;
  fill-rule: evenodd;
  vertical-align: middle;
  position: absolute;
  top: 10px;
  right: 5px;
  fill: #465AB6;
  pointer-events: none;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  width: 100%;
  -webkit-column-gap: var(--spacing-l);
  column-gap: var(--spacing-l);
}

.emotion-class>div {
  width: 100%;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  margin-bottom: 5px;
}

.emotion-class>a {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-class {
  position: relative;
}

.emotion-class svg {
  fill: #465AB6;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class {
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
  font-size: 12px;
  color: #465AB6;
  vertical-align: middle;
}

.emotion-class {
  position: relative;
}

.emotion-class:hover fieldset {
  border-color: #B4BCE0;
}

.emotion-class fieldset {
  border-color: #D1D6ED;
}

.emotion-class input {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  padding-left: 9px;
  padding-right: 9px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
  vertical-align: top;
  width: 100%;
  margin: 0;
}

.emotion-class .clearButton {
  opacity: 1;
}

@media (pointer: fine) {
  .emotion-class .clearButton {
    opacity: 0;
  }

  .emotion-class:hover .clearButton,
  .emotion-class:focus-within .clearButton {
    opacity: 1;
  }
}

.emotion-class {
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.4375em;
  letter-spacing: 0.00938em;
  color: rgba(0, 0, 0, 0.87);
  box-sizing: border-box;
  position: relative;
  cursor: text;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  border-radius: 4px;
  padding-right: 14px;
  padding: 5px;
  height: 45px;
  background: #FFFFFF;
}

.emotion-class.Mui-disabled {
  color: rgba(0, 0, 0, 0.38);
  cursor: default;
}

.emotion-class:hover .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.87);
}

@media (hover: none) {
  .emotion-class:hover .MuiOutlinedInput-notchedOutline {
    border-color: rgba(0, 0, 0, 0.23);
  }
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-width: 2px;
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #1976d2;
}

.emotion-class.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: #d32f2f;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.26);
}

.emotion-class input {
  font-size: 14px;
  color: #303757;
  font-family: "Open Sans",sans-serif;
}

.emotion-class input::-webkit-input-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input::-moz-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input:-ms-input-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input::placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class .MuiOutlinedInput-notchedOutline {
  border-color: #D1D6ED;
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline,
.emotion-class:hover.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #465AB6;
}

.emotion-class:hover .MuiOutlinedInput-notchedOutline {
  border-color: #B4BCE0;
  outline: none;
}

.emotion-class.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: #FF7F8A;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: #E4E5EB;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::-webkit-input-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::-moz-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline:-ms-input-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::placeholder {
  color: blue;
}

.emotion-class {
  font: inherit;
  letter-spacing: inherit;
  color: currentColor;
  padding: 4px 0 5px;
  border: 0;
  box-sizing: content-box;
  background: none;
  height: 1.4375em;
  margin: 0;
  -webkit-tap-highlight-color: transparent;
  display: block;
  min-width: 0;
  width: 100%;
  -webkit-animation-name: mui-auto-fill-cancel;
  animation-name: mui-auto-fill-cancel;
  -webkit-animation-duration: 10ms;
  animation-duration: 10ms;
  padding: 16.5px 14px;
  padding-right: 0;
  cursor: pointer;
  padding: 9px 12px 10px;
  width: 100%;
}

.emotion-class::-webkit-input-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class::-moz-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class::-ms-input-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class:focus {
  outline: 0;
}

.emotion-class:invalid {
  box-shadow: none;
}

.emotion-class::-webkit-search-decoration {
  -webkit-appearance: none;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-webkit-input-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-moz-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-ms-input-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-webkit-input-placeholder {
  opacity: 0.42;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-moz-placeholder {
  opacity: 0.42;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-ms-input-placeholder {
  opacity: 0.42;
}

.emotion-class.Mui-disabled {
  opacity: 1;
  -webkit-text-fill-color: rgba(0, 0, 0, 0.38);
}

.emotion-class:-webkit-autofill {
  -webkit-animation-duration: 5000s;
  animation-duration: 5000s;
  -webkit-animation-name: mui-auto-fill;
  animation-name: mui-auto-fill;
}

.emotion-class:-webkit-autofill {
  border-radius: inherit;
}

.emotion-class.Mui-disabled {
  color: #A6ABBF;
  -webkit-text-fill-color: #A6ABBF;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  max-height: 2em;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.54);
  margin-left: 8px;
  position: absolute;
  right: 17px;
}

.emotion-class button {
  color: #465AB6;
  margin-right: -12px!important;
}

.emotion-class button.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class button:hover {
  background-color: transparent;
}

.emotion-class button svg {
  width: 20px;
  height: 20px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-right: -12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: 1.5rem;
}

.emotion-class {
  text-align: left;
  position: absolute;
  bottom: 0;
  right: 0;
  top: -5px;
  left: 0;
  margin: 0;
  padding: 0 8px;
  pointer-events: none;
  border-radius: inherit;
  border-style: solid;
  border-width: 1px;
  overflow: hidden;
  min-width: 0%;
  border-color: rgba(0, 0, 0, 0.23);
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 34px;
  min-height: auto;
  border-width: 2px;
  border-style: solid;
  outline: none;
  top: 0;
  -webkit-transition: border-color ease-in-out 0.15s,box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s,box-shadow ease-in-out 0.15s;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class legend {
  display: none;
}

.emotion-class {
  float: unset;
  width: auto;
  overflow: hidden;
  padding: 0;
  line-height: 11px;
  -webkit-transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
  transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
}

.emotion-class {
  z-index: 1300;
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  outline: 0;
  transform-origin: top center;
  transform-origin: bottom center;
}

.emotion-class {
  display: grid;
  grid-auto-columns: max-content auto max-content;
  grid-auto-rows: max-content auto max-content;
}

.emotion-class .MuiPickersLayout-actionBar {
  grid-column: 1/4;
  grid-row: 3;
}

.emotion-class .MuiPickersLayout-toolbar {
  grid-column: 2/4;
  grid-row: 1;
}

.emotion-class .MuiPickersLayout-shortcuts {
  grid-column: 1;
  grid-row: 2/3;
}

.emotion-class {
  grid-column: 2;
  grid-row: 2;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  overflow: hidden;
  width: 320px;
  max-height: 336px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: 0 auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 336px;
}

.emotion-class>div:first-of-type {
  position: absolute;
  width: calc(100% - 40px);
  box-sizing: border-box;
  background: #fff;
  min-height: initial;
  max-height: initial;
  height: 54px;
  padding: 16px 0 6px;
  margin: 0 20px;
}

.emotion-class>div:first-of-type+div {
  margin-top: 54px;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  font-size: 14px;
  color: #465AB6;
  font-weight: 600;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .MuiButtonBase-root {
  display: none;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .PrivatePickersFadeTransitionGroup-root {
  color: #465AB6;
  font-weight: 600;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .PrivatePickersFadeTransitionGroup-root:nth-of-type(2) div {
  margin-right: 0;
}

.emotion-class>div:first-of-type>div:nth-of-type(2) {
  width: 100%;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-top: 12px;
  margin-bottom: 4px;
  padding-left: 24px;
  padding-right: 12px;
  max-height: 40px;
  min-height: 40px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  margin-right: auto;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 500;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0.00938em;
}

.emotion-class {
  display: block;
  position: relative;
}

.emotion-class {
  margin-right: 6px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  padding: 5px;
  font-size: 1.125rem;
  margin-right: auto;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: 1.5rem;
  will-change: transform;
  -webkit-transition: -webkit-transform 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-right: -12px;
  color: #465AB6;
  padding: 5px;
  width: 34px;
  height: 34px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class svg {
  width: 24px;
  height: 24px;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: inherit;
}

.emotion-class {
  width: 24px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-left: -12px;
  color: #465AB6;
  padding: 5px;
  width: 34px;
  height: 34px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class svg {
  width: 24px;
  height: 24px;
}

.emotion-class {
  display: block;
  position: relative;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  margin: 0;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  font-weight: 600;
  font-size: 12px;
  width: 36px;
  height: 40px;
  margin: 0 2px;
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: rgba(0, 0, 0, 0.6);
}

.emotion-class {
  display: block;
  position: relative;
  overflow-x: hidden;
  min-height: 240px;
}

.emotion-class>* {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
}

.emotion-class .MuiPickersSlideTransition-slideEnter-left {
  will-change: transform;
  -webkit-transform: translate(100%);
  -moz-transform: translate(100%);
  -ms-transform: translate(100%);
  transform: translate(100%);
  z-index: 1;
}

.emotion-class .MuiPickersSlideTransition-slideEnter-right {
  will-change: transform;
  -webkit-transform: translate(-100%);
  -moz-transform: translate(-100%);
  -ms-transform: translate(-100%);
  transform: translate(-100%);
  z-index: 1;
}

.emotion-class .MuiPickersSlideTransition-slideEnterActive {
  -webkit-transform: translate(0%);
  -moz-transform: translate(0%);
  -ms-transform: translate(0%);
  transform: translate(0%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
}

.emotion-class .MuiPickersSlideTransition-slideExit {
  -webkit-transform: translate(0%);
  -moz-transform: translate(0%);
  -ms-transform: translate(0%);
  transform: translate(0%);
}

.emotion-class .MuiPickersSlideTransition-slideExitActiveLeft-left {
  will-change: transform;
  -webkit-transform: translate(-100%);
  -moz-transform: translate(-100%);
  -ms-transform: translate(-100%);
  transform: translate(-100%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  z-index: 0;
}

.emotion-class .MuiPickersSlideTransition-slideExitActiveLeft-right {
  will-change: transform;
  -webkit-transform: translate(100%);
  -moz-transform: translate(100%);
  -ms-transform: translate(100%);
  transform: translate(100%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  z-index: 0;
}

.emotion-class {
  overflow: hidden;
}

.emotion-class button {
  margin-right: 2px!important;
}

.emotion-class {
  margin: 2px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  color: rgba(0, 0, 0, 0.6);
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:not(.Mui-selected) {
  border: 1px solid rgba(0, 0, 0, 0.6);
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class:not(.Mui-selected) {
  -webkit-transition: 220ms all;
  transition: 220ms all;
}

.emotion-class:not(.Mui-selected):before {
  content: '';
  display: inline-block;
  border-left: 7px solid transparent;
  border-bottom: 7px solid #465AB6;
  border-top-color: rgba(0, 0, 0, 0.2);
  position: absolute;
  bottom: 4px;
  right: 4px;
}

.emotion-class:not(.Mui-selected):hover:before {
  display: none;
}

.emotion-class {
  overflow: hidden;
  pointer-events: none;
  position: absolute;
  z-index: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: inherit;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  height: 45px;
}

.emotion-class>*:first-of-type {
  padding-right: 10px;
}

.emotion-class {
  position: relative;
  max-height: -webkit-fit-content;
  max-height: -moz-fit-content;
  max-height: fit-content;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  display: inline-block;
  width: 35px;
  height: 35px;
  line-height: 35px;
  box-sizing: border-box;
  border-radius: 50%;
  text-align: center;
  color: #A6ABBF;
  font-size: 14px;
  font-weight: 700;
  background-color: #E4E5EB;
  background-image: none;
  -webkit-background-size: cover;
  background-size: cover;
  -webkit-background-position: center;
  background-position: center;
  box-shadow: inset 0px 0px 11px rgba(0, 0, 0, 0.1);
}

.emotion-class {
  position: absolute;
  height: 16px;
  bottom: -4px;
  right: 0px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  width: 100%;
  color: #465AB6;
  font-size: 14px;
  cursor: pointer!important;
  white-space: nowrap;
  overflow: hidden;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-overflow: ellipsis;
  font-weight: 600;
  color: #303757;
  cursor: initial!important;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 150%;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  width: 100%;
  box-sizing: border-box;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #A6ABBF;
  text-align: right;
  padding-left: 10px;
  white-space: nowrap;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class:hover .checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class:hover>.checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6!important;
}

.emotion-class {
  margin: 1px 0 -1px 6px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  color: #303757;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  margin-top: 30px;
  border-top: 1px solid #E4E5EB;
  padding-top: 40px;
}

@media (max-width: 768px) {
  .emotion-class {
    padding-top: 15px;
  }
}

.emotion-class button,
.emotion-class a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-base);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: #FFFFFF;
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(1.15);
  filter: brightness(1.15);
}

.emotion-class {
  color: #FFFFFF;
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div
  data-testid="AddNewFileModal_Container"
>
  <div
    class="emotion-class"
  >
    <div>
      <div
        style="margin-bottom: var(--spacing-xs);"
      >
        <span
          class="emotion-class"
          data-testid="InputLabel_UploadFile"
        >
          Upload file
        </span>
      </div>
      <div
        class="emotion-class"
      >
        <div>
          test.pdf
        </div>
        <div
          class="emotion-class"
        >
          <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m21.5%2024l13%2032.5l15.5%2035l24%2026.5l32.5%2035l35%2032.5l26.5%2024l35%2015.5l32.5%2013l24%2021.5l15.5%2013l13%2015.5l21.5%2024z'/%3e%3c/svg%3e
            classname="css-1lp8zdr"
            data-testid="InputFile_RemoveIcon"
            fill="var(--statuses-status-initiated)"
          />
        </div>
      </div>
      <div
        class="emotion-class"
      >
        <div />
      </div>
    </div>
    <div>
      <div
        style="margin-bottom: 5px;"
      >
        <span
          class="emotion-class"
          data-testid="InputLabel_DocumentType"
        >
          Document type
        </span>
      </div>
      <div
        class="emotion-class"
      >
        <select
          class="emotion-class"
          data-testid="Select_DocumentType"
        >
          <option
            value=""
          >
            Choose Type
          </option>
          <option
            data-testid="SelectOption_License"
            value="license"
          >
            Professional license
          </option>
          <option
            data-testid="SelectOption_Certificate"
            value="certificate"
          >
            Certificate
          </option>
          <option
            data-testid="SelectOption_Insurance"
            value="insurance"
          >
            General Liability insurance
          </option>
          <option
            data-testid="SelectOption_Nda"
            value="nda"
          >
            Non Disclosure Agreement
          </option>
          <option
            data-testid="SelectOption_Terms"
            value="terms"
          >
            Terms & Conditions
          </option>
          <option
            data-testid="SelectOption_Sa"
            value="sa"
          >
            Services agreement
          </option>
          <option
            data-testid="SelectOption_Resume"
            value="resume"
          >
            Resume/CV
          </option>
          <option
            data-testid="SelectOption_OtherDocument"
            value="other document"
          >
            Other document
          </option>
          <option
            data-testid="SelectOption_TaxForm"
            value="tax form"
          >
            IRS Form
          </option>
          <option
            data-testid="SelectOption_Diploma"
            value="diploma"
          >
            Diploma
          </option>
          <option
            data-testid="SelectOption_Transcripts"
            value="transcripts"
          >
            Transcripts
          </option>
          <option
            data-testid="SelectOption_DriversLicence"
            value="drivers licence"
          >
            Drivers license
          </option>
        </select>
        <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
          class="emotion-class"
          size="28"
        />
      </div>
    </div>
    <div
      class="emotion-class"
    >
      <div>
        <div
          class="emotion-class"
        >
          <div>
            <span
              class="emotion-class"
              data-testid="InputLabel_IssueDate"
            >
              Issue date
              <span
                class="emotion-class"
                style="margin-left: 10px; color: #A6ABBF;"
              >
                Optional
              </span>
            </span>
          </div>
          <a
            class=" emotion-class"
            data-testid="InputActionLabel_Button"
            style="text-align: right;"
          >
            <span
              class="emotion-class"
            >
              Reset
            </span>
          </a>
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="MuiFormControl-root MuiTextField-root emotion-class"
            data-testid="InputDatePicker_AddNewFileModalIssueDateInput"
          >
            <div
              class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd emotion-class"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd emotion-class"
                id=":rh:"
                inputmode="text"
                placeholder="MM/DD/YYYY"
                type="text"
                value=""
              />
              <div
                class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium emotion-class"
              >
                <button
                  aria-label="Choose date"
                  class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium emotion-class"
                  tabindex="0"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium emotion-class"
                    data-testid="CalendarIcon"
                    focusable="false"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
                    />
                  </svg>
                </button>
              </div>
              <fieldset
                aria-hidden="true"
                class="MuiOutlinedInput-notchedOutline emotion-class"
              >
                <legend
                  class="emotion-class"
                >
                  <span
                    aria-hidden="true"
                    class="notranslate"
                  >
                    ​
                  </span>
                </legend>
              </fieldset>
            </div>
          </div>
        </div>
        <div
          class="MuiPopper-root MuiPickersPopper-root emotion-class"
          data-testid="InputDatePickerCalendar_AddNewFileModalIssueDateInput"
          role="dialog"
          style="position: fixed; top: 0px; left: 0px; display: none;"
        >
          <div
            data-testid="sentinelStart"
            tabindex="-1"
          />
          <div
            class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation8 MuiPickersPopper-paper emotion-class"
            style="--Paper-shadow: 0px 5px 5px -3px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12); opacity: 0; transform: scale(0.75, 0.5625); visibility: hidden;"
            tabindex="-1"
          >
            <div
              class="MuiPickersLayout-root emotion-class"
            >
              <div
                class="MuiPickersLayout-contentWrapper emotion-class"
              >
                <div
                  class="MuiDateCalendar-root emotion-class"
                >
                  <div
                    class="MuiPickersCalendarHeader-root emotion-class"
                  >
                    <div
                      aria-live="polite"
                      class="MuiPickersCalendarHeader-labelContainer emotion-class"
                      role="presentation"
                    >
                      <div
                        class="MuiPickersFadeTransitionGroup-root emotion-class"
                      >
                        <div
                          class="MuiPickersCalendarHeader-label emotion-class"
                          id=":rk:-grid-label"
                          style="opacity: 1;"
                        >
                          February 2025
                        </div>
                      </div>
                      <button
                        aria-label="calendar view is open, switch to year view"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall MuiPickersCalendarHeader-switchViewButton emotion-class"
                        tabindex="0"
                        type="button"
                      >
                        <svg
                          aria-hidden="true"
                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiPickersCalendarHeader-switchViewIcon emotion-class"
                          data-testid="ArrowDropDownIcon"
                          focusable="false"
                          viewBox="0 0 24 24"
                        >
                          <path
                            d="M7 10l5 5 5-5z"
                          />
                        </svg>
                      </button>
                    </div>
                    <div
                      class="MuiPickersArrowSwitcher-root emotion-class"
                      style="opacity: 1; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
                    >
                      <button
                        aria-label="Previous month"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-previousIconButton emotion-class"
                        tabindex="0"
                        title="Previous month"
                        type="button"
                      >
                        <svg
                          aria-hidden="true"
                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-leftArrowIcon emotion-class"
                          data-testid="ArrowLeftIcon"
                          focusable="false"
                          viewBox="0 0 24 24"
                        >
                          <path
                            d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"
                          />
                        </svg>
                      </button>
                      <div
                        class="MuiPickersArrowSwitcher-spacer emotion-class"
                      />
                      <button
                        aria-label="Next month"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeStart MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-nextIconButton emotion-class"
                        tabindex="0"
                        title="Next month"
                        type="button"
                      >
                        <svg
                          aria-hidden="true"
                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-rightArrowIcon emotion-class"
                          data-testid="ArrowRightIcon"
                          focusable="false"
                          viewBox="0 0 24 24"
                        >
                          <path
                            d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                  <div
                    class="MuiPickersFadeTransitionGroup-root MuiDateCalendar-viewTransitionContainer emotion-class"
                  >
                    <div
                      style="opacity: 1;"
                    >
                      <div
                        aria-labelledby=":rk:-grid-label"
                        class="MuiDayCalendar-root emotion-class"
                        role="grid"
                      >
                        <div
                          class="MuiDayCalendar-header emotion-class"
                          role="row"
                        >
                          <span
                            aria-label="Sunday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            S
                          </span>
                          <span
                            aria-label="Monday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            M
                          </span>
                          <span
                            aria-label="Tuesday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            T
                          </span>
                          <span
                            aria-label="Wednesday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            W
                          </span>
                          <span
                            aria-label="Thursday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            T
                          </span>
                          <span
                            aria-label="Friday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            F
                          </span>
                          <span
                            aria-label="Saturday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            S
                          </span>
                        </div>
                        <div
                          class="MuiPickersSlideTransition-root MuiDayCalendar-slideTransition emotion-class"
                          role="presentation"
                        >
                          <div
                            class="MuiDayCalendar-monthContainer emotion-class"
                            role="rowgroup"
                          >
                            <div
                              aria-rowindex="1"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1737849600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                26
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1737936000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                27
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738022400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                28
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738108800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                29
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738195200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                30
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738281600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                31
                              </button>
                              <button
                                aria-colindex="7"
                                aria-current="date"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-today emotion-class"
                                data-timestamp="1738368000000"
                                role="gridcell"
                                tabindex="0"
                                type="button"
                              >
                                1
                                <span
                                  class="MuiTouchRipple-root emotion-class"
                                />
                              </button>
                            </div>
                            <div
                              aria-rowindex="2"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738454400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                2
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738540800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                3
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738627200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                4
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738713600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                5
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738800000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                6
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738886400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                7
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738972800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                8
                              </button>
                            </div>
                            <div
                              aria-rowindex="3"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739059200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                9
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739145600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                10
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739232000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                11
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739318400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                12
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739404800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                13
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739491200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                14
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739577600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                15
                              </button>
                            </div>
                            <div
                              aria-rowindex="4"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739664000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                16
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739750400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                17
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739836800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                18
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739923200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                19
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740009600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                20
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740096000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                21
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740182400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                22
                              </button>
                            </div>
                            <div
                              aria-rowindex="5"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740268800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                23
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740355200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                24
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740441600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                25
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740528000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                26
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740614400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                27
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740700800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                28
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1740787200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                1
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            data-testid="sentinelEnd"
            tabindex="-1"
          />
        </div>
      </div>
      <div>
        <div
          class="emotion-class"
        >
          <div>
            <span
              class="emotion-class"
              data-testid="InputLabel_ExpiryDate"
            >
              Expiry date
              <span
                class="emotion-class"
                style="margin-left: 10px; color: #A6ABBF;"
              >
                Optional
              </span>
            </span>
          </div>
          <a
            class=" emotion-class"
            data-testid="InputActionLabel_Button"
            style="text-align: right;"
          >
            <span
              class="emotion-class"
            >
              Reset
            </span>
          </a>
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="MuiFormControl-root MuiTextField-root emotion-class"
            data-testid="InputDatePicker_AddNewFileModalExpiryDateInput"
          >
            <div
              class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd emotion-class"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd emotion-class"
                id=":ro:"
                inputmode="text"
                placeholder="MM/DD/YYYY"
                type="text"
                value=""
              />
              <div
                class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium emotion-class"
              >
                <button
                  aria-label="Choose date"
                  class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium emotion-class"
                  tabindex="0"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium emotion-class"
                    data-testid="CalendarIcon"
                    focusable="false"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
                    />
                  </svg>
                </button>
              </div>
              <fieldset
                aria-hidden="true"
                class="MuiOutlinedInput-notchedOutline emotion-class"
              >
                <legend
                  class="emotion-class"
                >
                  <span
                    aria-hidden="true"
                    class="notranslate"
                  >
                    ​
                  </span>
                </legend>
              </fieldset>
            </div>
          </div>
        </div>
        <div
          class="MuiPopper-root MuiPickersPopper-root emotion-class"
          data-testid="InputDatePickerCalendar_AddNewFileModalExpiryDateInput"
          role="dialog"
          style="position: fixed; top: 0px; left: 0px; display: none;"
        >
          <div
            data-testid="sentinelStart"
            tabindex="-1"
          />
          <div
            class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation8 MuiPickersPopper-paper emotion-class"
            style="--Paper-shadow: 0px 5px 5px -3px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12); opacity: 0; transform: scale(0.75, 0.5625); visibility: hidden;"
            tabindex="-1"
          >
            <div
              class="MuiPickersLayout-root emotion-class"
            >
              <div
                class="MuiPickersLayout-contentWrapper emotion-class"
              >
                <div
                  class="MuiDateCalendar-root emotion-class"
                >
                  <div
                    class="MuiPickersCalendarHeader-root emotion-class"
                  >
                    <div
                      aria-live="polite"
                      class="MuiPickersCalendarHeader-labelContainer emotion-class"
                      role="presentation"
                    >
                      <div
                        class="MuiPickersFadeTransitionGroup-root emotion-class"
                      >
                        <div
                          class="MuiPickersCalendarHeader-label emotion-class"
                          id=":rr:-grid-label"
                          style="opacity: 1;"
                        >
                          February 2025
                        </div>
                      </div>
                      <button
                        aria-label="calendar view is open, switch to year view"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall MuiPickersCalendarHeader-switchViewButton emotion-class"
                        tabindex="0"
                        type="button"
                      >
                        <svg
                          aria-hidden="true"
                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiPickersCalendarHeader-switchViewIcon emotion-class"
                          data-testid="ArrowDropDownIcon"
                          focusable="false"
                          viewBox="0 0 24 24"
                        >
                          <path
                            d="M7 10l5 5 5-5z"
                          />
                        </svg>
                      </button>
                    </div>
                    <div
                      class="MuiPickersArrowSwitcher-root emotion-class"
                      style="opacity: 1; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
                    >
                      <button
                        aria-label="Previous month"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-previousIconButton emotion-class"
                        tabindex="0"
                        title="Previous month"
                        type="button"
                      >
                        <svg
                          aria-hidden="true"
                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-leftArrowIcon emotion-class"
                          data-testid="ArrowLeftIcon"
                          focusable="false"
                          viewBox="0 0 24 24"
                        >
                          <path
                            d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"
                          />
                        </svg>
                      </button>
                      <div
                        class="MuiPickersArrowSwitcher-spacer emotion-class"
                      />
                      <button
                        aria-label="Next month"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeStart MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-nextIconButton emotion-class"
                        tabindex="0"
                        title="Next month"
                        type="button"
                      >
                        <svg
                          aria-hidden="true"
                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-rightArrowIcon emotion-class"
                          data-testid="ArrowRightIcon"
                          focusable="false"
                          viewBox="0 0 24 24"
                        >
                          <path
                            d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                  <div
                    class="MuiPickersFadeTransitionGroup-root MuiDateCalendar-viewTransitionContainer emotion-class"
                  >
                    <div
                      style="opacity: 1;"
                    >
                      <div
                        aria-labelledby=":rr:-grid-label"
                        class="MuiDayCalendar-root emotion-class"
                        role="grid"
                      >
                        <div
                          class="MuiDayCalendar-header emotion-class"
                          role="row"
                        >
                          <span
                            aria-label="Sunday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            S
                          </span>
                          <span
                            aria-label="Monday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            M
                          </span>
                          <span
                            aria-label="Tuesday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            T
                          </span>
                          <span
                            aria-label="Wednesday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            W
                          </span>
                          <span
                            aria-label="Thursday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            T
                          </span>
                          <span
                            aria-label="Friday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            F
                          </span>
                          <span
                            aria-label="Saturday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            S
                          </span>
                        </div>
                        <div
                          class="MuiPickersSlideTransition-root MuiDayCalendar-slideTransition emotion-class"
                          role="presentation"
                        >
                          <div
                            class="MuiDayCalendar-monthContainer emotion-class"
                            role="rowgroup"
                          >
                            <div
                              aria-rowindex="1"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1737849600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                26
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1737936000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                27
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738022400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                28
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738108800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                29
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738195200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                30
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738281600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                31
                              </button>
                              <button
                                aria-colindex="7"
                                aria-current="date"
                                aria-selected="false"
                                class="MuiButtonBase-root Mui-focusVisible MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-today emotion-class"
                                data-timestamp="1738368000000"
                                role="gridcell"
                                tabindex="0"
                                type="button"
                              >
                                1
                              </button>
                            </div>
                            <div
                              aria-rowindex="2"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738454400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                2
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738540800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                3
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738627200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                4
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738713600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                5
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738800000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                6
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738886400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                7
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738972800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                8
                              </button>
                            </div>
                            <div
                              aria-rowindex="3"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739059200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                9
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739145600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                10
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739232000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                11
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739318400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                12
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739404800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                13
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739491200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                14
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739577600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                15
                              </button>
                            </div>
                            <div
                              aria-rowindex="4"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739664000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                16
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739750400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                17
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739836800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                18
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739923200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                19
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740009600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                20
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740096000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                21
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740182400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                22
                              </button>
                            </div>
                            <div
                              aria-rowindex="5"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740268800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                23
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740355200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                24
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740441600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                25
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740528000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                26
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740614400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                27
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740700800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                28
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1740787200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                1
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            data-testid="sentinelEnd"
            tabindex="-1"
          />
        </div>
      </div>
    </div>
    <div>
      <span
        class="emotion-class"
        data-testid="InputLabel_RelatedPartner"
      >
        Related partner
      </span>
      <div
        style="display: flex; justify-content: space-between; align-items: center; height: 45px;"
      >
        <div
          class="emotion-class"
        >
          <div
            style="display: flex; justify-content: center;"
          >
            <div
              class="emotion-class"
            >
              <p
                class="emotion-class"
              >
                V1
              </p>
              <div
                class="emotion-class"
                size="16"
              >
                <span
                  aria-label=""
                  class=""
                  data-mui-internal-clone-element="true"
                  style="display: flex; align-items: center; justify-content: center; width: 16px; height: 16px; border-radius: 20px; background: #06CC87; padding: 0px; border: 2px solid white; box-sizing: border-box;"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9%2024.5l12%2021.5l20%2029.5l36%2013l39%2016l20%2035.5l9%2024.5z'/%3e%3c/svg%3e
                    classname="css-qnd1lb"
                    fill="white"
                    size="16"
                  />
                </span>
              </div>
            </div>
          </div>
          <span
            class="emotion-class"
          >
            Vendor 1
          </span>
        </div>
      </div>
    </div>
    <div>
      <div
        style="margin-bottom: 5px;"
      >
        <span
          class="emotion-class"
          data-testid="InputLabel_Message"
        >
          Message
          <span
            class="emotion-class"
            style="margin-left: 10px; color: #A6ABBF;"
          >
            Optional
          </span>
        </span>
      </div>
      <div
        class="emotion-class"
      >
        <textarea
          class="emotion-class"
          maxlength="300"
          placeholder="Add short note..."
          variant="default"
        />
      </div>
      <div
        class="emotion-class"
      >
        <div />
        <div
          class="emotion-class"
        >
          0
           / 
          300
        </div>
      </div>
    </div>
    <div>
      <div
        class="emotion-class"
      >
        <label
          class="emotion-class"
          data-testid="InputCheckbox"
        >
          <span
            class="emotion-class"
          >
            <input
              type="checkbox"
            />
            <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
              classname="checkboxOffIcon css-1dmky3q"
              fill="#D1D6ED"
            />
          </span>
          <span
            class="emotion-class"
          >
            <span
              aria-label=""
              class=" emotion-class"
              data-mui-internal-clone-element="true"
              data-testid="InputCheckboxLabel_AddNewFileModalVisibleToPartnerCheckbox"
            >
              Visible to partner
            </span>
          </span>
        </label>
      </div>
    </div>
  </div>
  <div
    class="emotion-class"
  >
    <button
      class=" emotion-class"
      data-testid="AddNewFileModal_SaveButton"
    >
      <span
        class="emotion-class"
      >
        Save changes
      </span>
    </button>
    <button
      class=" emotion-class"
      data-testid="AddNewFileModal_CancelButton"
    >
      <span
        class="emotion-class"
      >
        Cancel
      </span>
    </button>
  </div>
</div>
`;

exports[`AddNewFileModal -  > snapshot should match: Vendor 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  width: 100%;
  row-gap: var(--spacing-l);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  border: 1px dashed #465AB6;
  border-radius: 4px;
  padding: 20px;
  color: #B4BCE0;
  background: #F5F6FA;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #B4BCE0;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-link-color)!important;
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 20px;
  cursor: pointer;
  font-weight: 600;
}

.emotion-class * {
  fill: var(--text-link-color);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: row nowrap;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  fill: #000000;
  width: 28px;
  height: 28px;
  fill-rule: evenodd;
  vertical-align: middle;
  position: absolute;
  top: 10px;
  right: 5px;
  fill: #465AB6;
  pointer-events: none;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  width: 100%;
  -webkit-column-gap: var(--spacing-l);
  column-gap: var(--spacing-l);
}

.emotion-class>div {
  width: 100%;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  margin-bottom: 5px;
}

.emotion-class>a {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-class {
  position: relative;
}

.emotion-class svg {
  fill: #465AB6;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class {
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
  font-size: 12px;
  color: #465AB6;
  vertical-align: middle;
}

.emotion-class {
  position: relative;
}

.emotion-class:hover fieldset {
  border-color: #B4BCE0;
}

.emotion-class fieldset {
  border-color: #D1D6ED;
}

.emotion-class input {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  padding-left: 9px;
  padding-right: 9px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
  vertical-align: top;
  width: 100%;
  margin: 0;
}

.emotion-class .clearButton {
  opacity: 1;
}

@media (pointer: fine) {
  .emotion-class .clearButton {
    opacity: 0;
  }

  .emotion-class:hover .clearButton,
  .emotion-class:focus-within .clearButton {
    opacity: 1;
  }
}

.emotion-class {
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.4375em;
  letter-spacing: 0.00938em;
  color: rgba(0, 0, 0, 0.87);
  box-sizing: border-box;
  position: relative;
  cursor: text;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  border-radius: 4px;
  padding-right: 14px;
  padding: 5px;
  height: 45px;
  background: #FFFFFF;
}

.emotion-class.Mui-disabled {
  color: rgba(0, 0, 0, 0.38);
  cursor: default;
}

.emotion-class:hover .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.87);
}

@media (hover: none) {
  .emotion-class:hover .MuiOutlinedInput-notchedOutline {
    border-color: rgba(0, 0, 0, 0.23);
  }
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-width: 2px;
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #1976d2;
}

.emotion-class.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: #d32f2f;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.26);
}

.emotion-class input {
  font-size: 14px;
  color: #303757;
  font-family: "Open Sans",sans-serif;
}

.emotion-class input::-webkit-input-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input::-moz-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input:-ms-input-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input::placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class .MuiOutlinedInput-notchedOutline {
  border-color: #D1D6ED;
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline,
.emotion-class:hover.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #465AB6;
}

.emotion-class:hover .MuiOutlinedInput-notchedOutline {
  border-color: #B4BCE0;
  outline: none;
}

.emotion-class.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: #FF7F8A;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: #E4E5EB;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::-webkit-input-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::-moz-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline:-ms-input-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::placeholder {
  color: blue;
}

.emotion-class {
  font: inherit;
  letter-spacing: inherit;
  color: currentColor;
  padding: 4px 0 5px;
  border: 0;
  box-sizing: content-box;
  background: none;
  height: 1.4375em;
  margin: 0;
  -webkit-tap-highlight-color: transparent;
  display: block;
  min-width: 0;
  width: 100%;
  -webkit-animation-name: mui-auto-fill-cancel;
  animation-name: mui-auto-fill-cancel;
  -webkit-animation-duration: 10ms;
  animation-duration: 10ms;
  padding: 16.5px 14px;
  padding-right: 0;
  cursor: pointer;
  padding: 9px 12px 10px;
  width: 100%;
}

.emotion-class::-webkit-input-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class::-moz-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class::-ms-input-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class:focus {
  outline: 0;
}

.emotion-class:invalid {
  box-shadow: none;
}

.emotion-class::-webkit-search-decoration {
  -webkit-appearance: none;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-webkit-input-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-moz-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-ms-input-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-webkit-input-placeholder {
  opacity: 0.42;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-moz-placeholder {
  opacity: 0.42;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-ms-input-placeholder {
  opacity: 0.42;
}

.emotion-class.Mui-disabled {
  opacity: 1;
  -webkit-text-fill-color: rgba(0, 0, 0, 0.38);
}

.emotion-class:-webkit-autofill {
  -webkit-animation-duration: 5000s;
  animation-duration: 5000s;
  -webkit-animation-name: mui-auto-fill;
  animation-name: mui-auto-fill;
}

.emotion-class:-webkit-autofill {
  border-radius: inherit;
}

.emotion-class.Mui-disabled {
  color: #A6ABBF;
  -webkit-text-fill-color: #A6ABBF;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  max-height: 2em;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.54);
  margin-left: 8px;
  position: absolute;
  right: 17px;
}

.emotion-class button {
  color: #465AB6;
  margin-right: -12px!important;
}

.emotion-class button.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class button:hover {
  background-color: transparent;
}

.emotion-class button svg {
  width: 20px;
  height: 20px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-right: -12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: 1.5rem;
}

.emotion-class {
  text-align: left;
  position: absolute;
  bottom: 0;
  right: 0;
  top: -5px;
  left: 0;
  margin: 0;
  padding: 0 8px;
  pointer-events: none;
  border-radius: inherit;
  border-style: solid;
  border-width: 1px;
  overflow: hidden;
  min-width: 0%;
  border-color: rgba(0, 0, 0, 0.23);
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 34px;
  min-height: auto;
  border-width: 2px;
  border-style: solid;
  outline: none;
  top: 0;
  -webkit-transition: border-color ease-in-out 0.15s,box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s,box-shadow ease-in-out 0.15s;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class legend {
  display: none;
}

.emotion-class {
  float: unset;
  width: auto;
  overflow: hidden;
  padding: 0;
  line-height: 11px;
  -webkit-transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
  transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
}

.emotion-class {
  z-index: 1300;
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  outline: 0;
  transform-origin: top center;
  transform-origin: bottom center;
}

.emotion-class {
  display: grid;
  grid-auto-columns: max-content auto max-content;
  grid-auto-rows: max-content auto max-content;
}

.emotion-class .MuiPickersLayout-actionBar {
  grid-column: 1/4;
  grid-row: 3;
}

.emotion-class .MuiPickersLayout-toolbar {
  grid-column: 2/4;
  grid-row: 1;
}

.emotion-class .MuiPickersLayout-shortcuts {
  grid-column: 1;
  grid-row: 2/3;
}

.emotion-class {
  grid-column: 2;
  grid-row: 2;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  overflow: hidden;
  width: 320px;
  max-height: 336px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: 0 auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 336px;
}

.emotion-class>div:first-of-type {
  position: absolute;
  width: calc(100% - 40px);
  box-sizing: border-box;
  background: #fff;
  min-height: initial;
  max-height: initial;
  height: 54px;
  padding: 16px 0 6px;
  margin: 0 20px;
}

.emotion-class>div:first-of-type+div {
  margin-top: 54px;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  font-size: 14px;
  color: #465AB6;
  font-weight: 600;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .MuiButtonBase-root {
  display: none;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .PrivatePickersFadeTransitionGroup-root {
  color: #465AB6;
  font-weight: 600;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .PrivatePickersFadeTransitionGroup-root:nth-of-type(2) div {
  margin-right: 0;
}

.emotion-class>div:first-of-type>div:nth-of-type(2) {
  width: 100%;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-top: 12px;
  margin-bottom: 4px;
  padding-left: 24px;
  padding-right: 12px;
  max-height: 40px;
  min-height: 40px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  margin-right: auto;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 500;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0.00938em;
}

.emotion-class {
  display: block;
  position: relative;
}

.emotion-class {
  margin-right: 6px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  padding: 5px;
  font-size: 1.125rem;
  margin-right: auto;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: 1.5rem;
  will-change: transform;
  -webkit-transition: -webkit-transform 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-right: -12px;
  color: #465AB6;
  padding: 5px;
  width: 34px;
  height: 34px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class svg {
  width: 24px;
  height: 24px;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: inherit;
}

.emotion-class {
  width: 24px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-left: -12px;
  color: #465AB6;
  padding: 5px;
  width: 34px;
  height: 34px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class svg {
  width: 24px;
  height: 24px;
}

.emotion-class {
  display: block;
  position: relative;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  margin: 0;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  font-weight: 600;
  font-size: 12px;
  width: 36px;
  height: 40px;
  margin: 0 2px;
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: rgba(0, 0, 0, 0.6);
}

.emotion-class {
  display: block;
  position: relative;
  overflow-x: hidden;
  min-height: 240px;
}

.emotion-class>* {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
}

.emotion-class .MuiPickersSlideTransition-slideEnter-left {
  will-change: transform;
  -webkit-transform: translate(100%);
  -moz-transform: translate(100%);
  -ms-transform: translate(100%);
  transform: translate(100%);
  z-index: 1;
}

.emotion-class .MuiPickersSlideTransition-slideEnter-right {
  will-change: transform;
  -webkit-transform: translate(-100%);
  -moz-transform: translate(-100%);
  -ms-transform: translate(-100%);
  transform: translate(-100%);
  z-index: 1;
}

.emotion-class .MuiPickersSlideTransition-slideEnterActive {
  -webkit-transform: translate(0%);
  -moz-transform: translate(0%);
  -ms-transform: translate(0%);
  transform: translate(0%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
}

.emotion-class .MuiPickersSlideTransition-slideExit {
  -webkit-transform: translate(0%);
  -moz-transform: translate(0%);
  -ms-transform: translate(0%);
  transform: translate(0%);
}

.emotion-class .MuiPickersSlideTransition-slideExitActiveLeft-left {
  will-change: transform;
  -webkit-transform: translate(-100%);
  -moz-transform: translate(-100%);
  -ms-transform: translate(-100%);
  transform: translate(-100%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  z-index: 0;
}

.emotion-class .MuiPickersSlideTransition-slideExitActiveLeft-right {
  will-change: transform;
  -webkit-transform: translate(100%);
  -moz-transform: translate(100%);
  -ms-transform: translate(100%);
  transform: translate(100%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  z-index: 0;
}

.emotion-class {
  overflow: hidden;
}

.emotion-class button {
  margin-right: 2px!important;
}

.emotion-class {
  margin: 2px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  color: rgba(0, 0, 0, 0.6);
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:not(.Mui-selected) {
  border: 1px solid rgba(0, 0, 0, 0.6);
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class:not(.Mui-selected) {
  -webkit-transition: 220ms all;
  transition: 220ms all;
}

.emotion-class:not(.Mui-selected):before {
  content: '';
  display: inline-block;
  border-left: 7px solid transparent;
  border-bottom: 7px solid #465AB6;
  border-top-color: rgba(0, 0, 0, 0.2);
  position: absolute;
  bottom: 4px;
  right: 4px;
}

.emotion-class:not(.Mui-selected):hover:before {
  display: none;
}

.emotion-class {
  overflow: hidden;
  pointer-events: none;
  position: absolute;
  z-index: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: inherit;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 150%;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  width: 100%;
  box-sizing: border-box;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #A6ABBF;
  text-align: right;
  padding-left: 10px;
  white-space: nowrap;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  margin-top: 30px;
  border-top: 1px solid #E4E5EB;
  padding-top: 40px;
}

@media (max-width: 768px) {
  .emotion-class {
    padding-top: 15px;
  }
}

.emotion-class button,
.emotion-class a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-base);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: #FFFFFF;
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(1.15);
  filter: brightness(1.15);
}

.emotion-class {
  color: #FFFFFF;
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div
  data-testid="AddNewFileModal_Container"
>
  <div
    class="emotion-class"
  >
    <div>
      <div
        style="margin-bottom: var(--spacing-xs);"
      >
        <span
          class="emotion-class"
          data-testid="InputLabel_UploadFile"
        >
          Upload file
        </span>
      </div>
      <div
        class="emotion-class"
        data-testid="AddNewFileModal_FileInput"
        role="presentation"
        tabindex="0"
      >
        <span
          class="emotion-class"
        >
          <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m33.1667%2036.5c33.3538%2036.4927%2033.5428%2036.5%2033.7333%2036.5c38.0227%2036.5%2041.5%2032.8048%2041.5%2028.2465c41.5%2023.6882%2038.0227%2019.993%2033.7333%2019.993c33.6329%2019.993%2033.5329%2019.9951%2033.4333%2019.9991c33.4333%2015.3025%2029.8516%2011.5%2025.4333%2011.5c21.7579%2011.5%2018.6613%2014.1314%2017.724%2017.7164c16.9359%2017.186%2016.0017%2016.8789%2015%2016.8789c12.1833%2016.8789%209.9%2019.3066%209.9%2022.3014c9.9%2023.225%2010.1172%2024.0947%2010.5002%2024.8559c8.18692%2025.5867%206.5%2027.8616%206.5%2030.5549c6.5%2033.7434%208.86436%2036.3456%2011.8333%2036.4934l21.6518%2036.5v28.4149h17.2143l24%2020.4286l30.7857%2028.4149h26.3482v36.5h33.1667z'/%3e%3c/svg%3e
            classname="css-1ejrr40"
            fill="var(--fills-badge-fills-badge-fill-neutral)"
            size="22"
          />
          Drag and drop file or 
          <a
            class="emotion-class"
          >
            upload
          </a>
          .
        </span>
      </div>
      <div
        class="emotion-class"
      >
        <div />
      </div>
    </div>
    <div>
      <div
        style="margin-bottom: 5px;"
      >
        <span
          class="emotion-class"
          data-testid="InputLabel_DocumentType"
        >
          Document type
        </span>
      </div>
      <div
        class="emotion-class"
      >
        <select
          class="emotion-class"
          data-testid="Select_DocumentType"
        >
          <option
            value=""
          >
            Choose Type
          </option>
          <option
            data-testid="SelectOption_License"
            value="license"
          >
            Professional license
          </option>
          <option
            data-testid="SelectOption_Certificate"
            value="certificate"
          >
            Certificate
          </option>
          <option
            data-testid="SelectOption_Insurance"
            value="insurance"
          >
            General Liability insurance
          </option>
          <option
            data-testid="SelectOption_Nda"
            value="nda"
          >
            Non Disclosure Agreement
          </option>
          <option
            data-testid="SelectOption_Terms"
            value="terms"
          >
            Terms & Conditions
          </option>
          <option
            data-testid="SelectOption_Sa"
            value="sa"
          >
            Services agreement
          </option>
          <option
            data-testid="SelectOption_Resume"
            value="resume"
          >
            Resume/CV
          </option>
          <option
            data-testid="SelectOption_OtherDocument"
            value="other document"
          >
            Other document
          </option>
          <option
            data-testid="SelectOption_TaxForm"
            value="tax form"
          >
            IRS Form
          </option>
          <option
            data-testid="SelectOption_Diploma"
            value="diploma"
          >
            Diploma
          </option>
          <option
            data-testid="SelectOption_Transcripts"
            value="transcripts"
          >
            Transcripts
          </option>
          <option
            data-testid="SelectOption_DriversLicence"
            value="drivers licence"
          >
            Drivers license
          </option>
        </select>
        <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
          class="emotion-class"
          size="28"
        />
      </div>
    </div>
    <div
      class="emotion-class"
    >
      <div>
        <div
          class="emotion-class"
        >
          <div>
            <span
              class="emotion-class"
              data-testid="InputLabel_IssueDate"
            >
              Issue date
              <span
                class="emotion-class"
                style="margin-left: 10px; color: #A6ABBF;"
              >
                Optional
              </span>
            </span>
          </div>
          <a
            class=" emotion-class"
            data-testid="InputActionLabel_Button"
            style="text-align: right;"
          >
            <span
              class="emotion-class"
            >
              Reset
            </span>
          </a>
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="MuiFormControl-root MuiTextField-root emotion-class"
            data-testid="InputDatePicker_AddNewFileModalIssueDateInput"
          >
            <div
              class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd emotion-class"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd emotion-class"
                id=":r12:"
                inputmode="text"
                placeholder="MM/DD/YYYY"
                type="text"
                value=""
              />
              <div
                class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium emotion-class"
              >
                <button
                  aria-label="Choose date"
                  class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium emotion-class"
                  tabindex="0"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium emotion-class"
                    data-testid="CalendarIcon"
                    focusable="false"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
                    />
                  </svg>
                </button>
              </div>
              <fieldset
                aria-hidden="true"
                class="MuiOutlinedInput-notchedOutline emotion-class"
              >
                <legend
                  class="emotion-class"
                >
                  <span
                    aria-hidden="true"
                    class="notranslate"
                  >
                    ​
                  </span>
                </legend>
              </fieldset>
            </div>
          </div>
        </div>
        <div
          class="MuiPopper-root MuiPickersPopper-root emotion-class"
          data-testid="InputDatePickerCalendar_AddNewFileModalIssueDateInput"
          role="dialog"
          style="position: fixed; top: 0px; left: 0px; display: none;"
        >
          <div
            data-testid="sentinelStart"
            tabindex="-1"
          />
          <div
            class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation8 MuiPickersPopper-paper emotion-class"
            style="--Paper-shadow: 0px 5px 5px -3px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12); opacity: 0; transform: scale(0.75, 0.5625); visibility: hidden;"
            tabindex="-1"
          >
            <div
              class="MuiPickersLayout-root emotion-class"
            >
              <div
                class="MuiPickersLayout-contentWrapper emotion-class"
              >
                <div
                  class="MuiDateCalendar-root emotion-class"
                >
                  <div
                    class="MuiPickersCalendarHeader-root emotion-class"
                  >
                    <div
                      aria-live="polite"
                      class="MuiPickersCalendarHeader-labelContainer emotion-class"
                      role="presentation"
                    >
                      <div
                        class="MuiPickersFadeTransitionGroup-root emotion-class"
                      >
                        <div
                          class="MuiPickersCalendarHeader-label emotion-class"
                          id=":r15:-grid-label"
                          style="opacity: 1;"
                        >
                          February 2025
                        </div>
                      </div>
                      <button
                        aria-label="calendar view is open, switch to year view"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall MuiPickersCalendarHeader-switchViewButton emotion-class"
                        tabindex="0"
                        type="button"
                      >
                        <svg
                          aria-hidden="true"
                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiPickersCalendarHeader-switchViewIcon emotion-class"
                          data-testid="ArrowDropDownIcon"
                          focusable="false"
                          viewBox="0 0 24 24"
                        >
                          <path
                            d="M7 10l5 5 5-5z"
                          />
                        </svg>
                      </button>
                    </div>
                    <div
                      class="MuiPickersArrowSwitcher-root emotion-class"
                      style="opacity: 1; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
                    >
                      <button
                        aria-label="Previous month"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-previousIconButton emotion-class"
                        tabindex="0"
                        title="Previous month"
                        type="button"
                      >
                        <svg
                          aria-hidden="true"
                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-leftArrowIcon emotion-class"
                          data-testid="ArrowLeftIcon"
                          focusable="false"
                          viewBox="0 0 24 24"
                        >
                          <path
                            d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"
                          />
                        </svg>
                      </button>
                      <div
                        class="MuiPickersArrowSwitcher-spacer emotion-class"
                      />
                      <button
                        aria-label="Next month"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeStart MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-nextIconButton emotion-class"
                        tabindex="0"
                        title="Next month"
                        type="button"
                      >
                        <svg
                          aria-hidden="true"
                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-rightArrowIcon emotion-class"
                          data-testid="ArrowRightIcon"
                          focusable="false"
                          viewBox="0 0 24 24"
                        >
                          <path
                            d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                  <div
                    class="MuiPickersFadeTransitionGroup-root MuiDateCalendar-viewTransitionContainer emotion-class"
                  >
                    <div
                      style="opacity: 1;"
                    >
                      <div
                        aria-labelledby=":r15:-grid-label"
                        class="MuiDayCalendar-root emotion-class"
                        role="grid"
                      >
                        <div
                          class="MuiDayCalendar-header emotion-class"
                          role="row"
                        >
                          <span
                            aria-label="Sunday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            S
                          </span>
                          <span
                            aria-label="Monday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            M
                          </span>
                          <span
                            aria-label="Tuesday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            T
                          </span>
                          <span
                            aria-label="Wednesday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            W
                          </span>
                          <span
                            aria-label="Thursday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            T
                          </span>
                          <span
                            aria-label="Friday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            F
                          </span>
                          <span
                            aria-label="Saturday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            S
                          </span>
                        </div>
                        <div
                          class="MuiPickersSlideTransition-root MuiDayCalendar-slideTransition emotion-class"
                          role="presentation"
                        >
                          <div
                            class="MuiDayCalendar-monthContainer emotion-class"
                            role="rowgroup"
                          >
                            <div
                              aria-rowindex="1"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1737849600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                26
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1737936000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                27
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738022400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                28
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738108800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                29
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738195200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                30
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738281600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                31
                              </button>
                              <button
                                aria-colindex="7"
                                aria-current="date"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-today emotion-class"
                                data-timestamp="1738368000000"
                                role="gridcell"
                                tabindex="0"
                                type="button"
                              >
                                1
                                <span
                                  class="MuiTouchRipple-root emotion-class"
                                />
                              </button>
                            </div>
                            <div
                              aria-rowindex="2"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738454400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                2
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738540800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                3
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738627200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                4
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738713600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                5
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738800000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                6
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738886400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                7
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738972800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                8
                              </button>
                            </div>
                            <div
                              aria-rowindex="3"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739059200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                9
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739145600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                10
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739232000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                11
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739318400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                12
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739404800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                13
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739491200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                14
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739577600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                15
                              </button>
                            </div>
                            <div
                              aria-rowindex="4"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739664000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                16
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739750400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                17
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739836800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                18
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739923200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                19
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740009600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                20
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740096000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                21
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740182400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                22
                              </button>
                            </div>
                            <div
                              aria-rowindex="5"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740268800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                23
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740355200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                24
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740441600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                25
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740528000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                26
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740614400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                27
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740700800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                28
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1740787200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                1
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            data-testid="sentinelEnd"
            tabindex="-1"
          />
        </div>
      </div>
      <div>
        <div
          class="emotion-class"
        >
          <div>
            <span
              class="emotion-class"
              data-testid="InputLabel_ExpiryDate"
            >
              Expiry date
              <span
                class="emotion-class"
                style="margin-left: 10px; color: #A6ABBF;"
              >
                Optional
              </span>
            </span>
          </div>
          <a
            class=" emotion-class"
            data-testid="InputActionLabel_Button"
            style="text-align: right;"
          >
            <span
              class="emotion-class"
            >
              Reset
            </span>
          </a>
        </div>
        <div
          class="emotion-class"
        >
          <div
            class="MuiFormControl-root MuiTextField-root emotion-class"
            data-testid="InputDatePicker_AddNewFileModalExpiryDateInput"
          >
            <div
              class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd emotion-class"
            >
              <input
                aria-invalid="false"
                autocomplete="off"
                class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd emotion-class"
                id=":r19:"
                inputmode="text"
                placeholder="MM/DD/YYYY"
                type="text"
                value=""
              />
              <div
                class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium emotion-class"
              >
                <button
                  aria-label="Choose date"
                  class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium emotion-class"
                  tabindex="0"
                  type="button"
                >
                  <svg
                    aria-hidden="true"
                    class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium emotion-class"
                    data-testid="CalendarIcon"
                    focusable="false"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
                    />
                  </svg>
                </button>
              </div>
              <fieldset
                aria-hidden="true"
                class="MuiOutlinedInput-notchedOutline emotion-class"
              >
                <legend
                  class="emotion-class"
                >
                  <span
                    aria-hidden="true"
                    class="notranslate"
                  >
                    ​
                  </span>
                </legend>
              </fieldset>
            </div>
          </div>
        </div>
        <div
          class="MuiPopper-root MuiPickersPopper-root emotion-class"
          data-testid="InputDatePickerCalendar_AddNewFileModalExpiryDateInput"
          role="dialog"
          style="position: fixed; top: 0px; left: 0px; display: none;"
        >
          <div
            data-testid="sentinelStart"
            tabindex="-1"
          />
          <div
            class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation8 MuiPickersPopper-paper emotion-class"
            style="--Paper-shadow: 0px 5px 5px -3px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12); opacity: 0; transform: scale(0.75, 0.5625); visibility: hidden;"
            tabindex="-1"
          >
            <div
              class="MuiPickersLayout-root emotion-class"
            >
              <div
                class="MuiPickersLayout-contentWrapper emotion-class"
              >
                <div
                  class="MuiDateCalendar-root emotion-class"
                >
                  <div
                    class="MuiPickersCalendarHeader-root emotion-class"
                  >
                    <div
                      aria-live="polite"
                      class="MuiPickersCalendarHeader-labelContainer emotion-class"
                      role="presentation"
                    >
                      <div
                        class="MuiPickersFadeTransitionGroup-root emotion-class"
                      >
                        <div
                          class="MuiPickersCalendarHeader-label emotion-class"
                          id=":r1c:-grid-label"
                          style="opacity: 1;"
                        >
                          February 2025
                        </div>
                      </div>
                      <button
                        aria-label="calendar view is open, switch to year view"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall MuiPickersCalendarHeader-switchViewButton emotion-class"
                        tabindex="0"
                        type="button"
                      >
                        <svg
                          aria-hidden="true"
                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiPickersCalendarHeader-switchViewIcon emotion-class"
                          data-testid="ArrowDropDownIcon"
                          focusable="false"
                          viewBox="0 0 24 24"
                        >
                          <path
                            d="M7 10l5 5 5-5z"
                          />
                        </svg>
                      </button>
                    </div>
                    <div
                      class="MuiPickersArrowSwitcher-root emotion-class"
                      style="opacity: 1; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
                    >
                      <button
                        aria-label="Previous month"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-previousIconButton emotion-class"
                        tabindex="0"
                        title="Previous month"
                        type="button"
                      >
                        <svg
                          aria-hidden="true"
                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-leftArrowIcon emotion-class"
                          data-testid="ArrowLeftIcon"
                          focusable="false"
                          viewBox="0 0 24 24"
                        >
                          <path
                            d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"
                          />
                        </svg>
                      </button>
                      <div
                        class="MuiPickersArrowSwitcher-spacer emotion-class"
                      />
                      <button
                        aria-label="Next month"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeStart MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-nextIconButton emotion-class"
                        tabindex="0"
                        title="Next month"
                        type="button"
                      >
                        <svg
                          aria-hidden="true"
                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-rightArrowIcon emotion-class"
                          data-testid="ArrowRightIcon"
                          focusable="false"
                          viewBox="0 0 24 24"
                        >
                          <path
                            d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                  <div
                    class="MuiPickersFadeTransitionGroup-root MuiDateCalendar-viewTransitionContainer emotion-class"
                  >
                    <div
                      style="opacity: 1;"
                    >
                      <div
                        aria-labelledby=":r1c:-grid-label"
                        class="MuiDayCalendar-root emotion-class"
                        role="grid"
                      >
                        <div
                          class="MuiDayCalendar-header emotion-class"
                          role="row"
                        >
                          <span
                            aria-label="Sunday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            S
                          </span>
                          <span
                            aria-label="Monday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            M
                          </span>
                          <span
                            aria-label="Tuesday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            T
                          </span>
                          <span
                            aria-label="Wednesday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            W
                          </span>
                          <span
                            aria-label="Thursday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            T
                          </span>
                          <span
                            aria-label="Friday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            F
                          </span>
                          <span
                            aria-label="Saturday"
                            class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                            role="columnheader"
                          >
                            S
                          </span>
                        </div>
                        <div
                          class="MuiPickersSlideTransition-root MuiDayCalendar-slideTransition emotion-class"
                          role="presentation"
                        >
                          <div
                            class="MuiDayCalendar-monthContainer emotion-class"
                            role="rowgroup"
                          >
                            <div
                              aria-rowindex="1"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1737849600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                26
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1737936000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                27
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738022400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                28
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738108800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                29
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738195200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                30
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1738281600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                31
                              </button>
                              <button
                                aria-colindex="7"
                                aria-current="date"
                                aria-selected="false"
                                class="MuiButtonBase-root Mui-focusVisible MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-today emotion-class"
                                data-timestamp="1738368000000"
                                role="gridcell"
                                tabindex="0"
                                type="button"
                              >
                                1
                              </button>
                            </div>
                            <div
                              aria-rowindex="2"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738454400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                2
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738540800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                3
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738627200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                4
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738713600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                5
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738800000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                6
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738886400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                7
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1738972800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                8
                              </button>
                            </div>
                            <div
                              aria-rowindex="3"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739059200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                9
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739145600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                10
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739232000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                11
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739318400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                12
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739404800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                13
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739491200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                14
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739577600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                15
                              </button>
                            </div>
                            <div
                              aria-rowindex="4"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739664000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                16
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739750400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                17
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739836800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                18
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1739923200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                19
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740009600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                20
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740096000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                21
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740182400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                22
                              </button>
                            </div>
                            <div
                              aria-rowindex="5"
                              class="MuiDayCalendar-weekContainer emotion-class"
                              role="row"
                            >
                              <button
                                aria-colindex="1"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740268800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                23
                              </button>
                              <button
                                aria-colindex="2"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740355200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                24
                              </button>
                              <button
                                aria-colindex="3"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740441600000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                25
                              </button>
                              <button
                                aria-colindex="4"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740528000000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                26
                              </button>
                              <button
                                aria-colindex="5"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740614400000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                27
                              </button>
                              <button
                                aria-colindex="6"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                data-timestamp="1740700800000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                28
                              </button>
                              <button
                                aria-colindex="7"
                                aria-selected="false"
                                class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                data-timestamp="1740787200000"
                                role="gridcell"
                                tabindex="-1"
                                type="button"
                              >
                                1
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            data-testid="sentinelEnd"
            tabindex="-1"
          />
        </div>
      </div>
    </div>
    <div>
      <div
        style="margin-bottom: 5px;"
      >
        <span
          class="emotion-class"
          data-testid="InputLabel_Message"
        >
          Message
          <span
            class="emotion-class"
            style="margin-left: 10px; color: #A6ABBF;"
          >
            Optional
          </span>
        </span>
      </div>
      <div
        class="emotion-class"
      >
        <textarea
          class="emotion-class"
          maxlength="300"
          placeholder="Add short note..."
          variant="default"
        />
      </div>
      <div
        class="emotion-class"
      >
        <div />
        <div
          class="emotion-class"
        >
          0
           / 
          300
        </div>
      </div>
    </div>
  </div>
  <div
    class="emotion-class"
  >
    <button
      class=" emotion-class"
      data-testid="AddNewFileModal_SaveButton"
    >
      <span
        class="emotion-class"
      >
        Save changes
      </span>
    </button>
    <button
      class=" emotion-class"
      data-testid="AddNewFileModal_CancelButton"
    >
      <span
        class="emotion-class"
      >
        Cancel
      </span>
    </button>
  </div>
</div>
`;
