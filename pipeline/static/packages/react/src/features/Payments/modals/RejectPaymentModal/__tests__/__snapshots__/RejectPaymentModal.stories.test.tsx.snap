// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`render payment modal multiple 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 30px;
}

.emotion-class {
  border: 0;
  border-radius: 4px;
  background-color: #F7F7F7;
  display: block;
  padding: 20px;
}

.emotion-class .GridContainerWrapper_GridRow:not(:last-of-type):after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  border-bottom: 1px solid #E4E5EB;
  padding-bottom: 20px;
}

.emotion-class {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  row-gap: 41px;
}

.emotion-class {
  box-sizing: border-box;
  margin: 0;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  max-width: 100%;
}

@media (min-width:600px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

@media (min-width:900px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

@media (min-width:1200px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

@media (min-width:1536px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

.emotion-class {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-top: calc(-1 * 16px);
  width: calc(100% + 16px);
  margin-left: calc(-1 * 16px);
}

.emotion-class>.MuiGrid-item {
  padding-top: 16px;
}

.emotion-class>.MuiGrid-item {
  padding-left: 16px;
}

.emotion-class {
  box-sizing: border-box;
  margin: 0;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  max-width: 100%;
}

@media (min-width:600px) {
  .emotion-class {
    -webkit-flex-basis: 33.333333%;
    -ms-flex-preferred-size: 33.333333%;
    flex-basis: 33.333333%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 33.333333%;
  }
}

@media (min-width:900px) {
  .emotion-class {
    -webkit-flex-basis: 33.333333%;
    -ms-flex-preferred-size: 33.333333%;
    flex-basis: 33.333333%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 33.333333%;
  }
}

@media (min-width:1200px) {
  .emotion-class {
    -webkit-flex-basis: 33.333333%;
    -ms-flex-preferred-size: 33.333333%;
    flex-basis: 33.333333%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 33.333333%;
  }
}

@media (min-width:1536px) {
  .emotion-class {
    -webkit-flex-basis: 33.333333%;
    -ms-flex-preferred-size: 33.333333%;
    flex-basis: 33.333333%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 33.333333%;
  }
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  box-sizing: border-box;
  margin: 0;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  max-width: 100%;
}

@media (min-width:600px) {
  .emotion-class {
    -webkit-flex-basis: 66.666667%;
    -ms-flex-preferred-size: 66.666667%;
    flex-basis: 66.666667%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 66.666667%;
  }
}

@media (min-width:900px) {
  .emotion-class {
    -webkit-flex-basis: 66.666667%;
    -ms-flex-preferred-size: 66.666667%;
    flex-basis: 66.666667%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 66.666667%;
  }
}

@media (min-width:1200px) {
  .emotion-class {
    -webkit-flex-basis: 66.666667%;
    -ms-flex-preferred-size: 66.666667%;
    flex-basis: 66.666667%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 66.666667%;
  }
}

@media (min-width:1536px) {
  .emotion-class {
    -webkit-flex-basis: 66.666667%;
    -ms-flex-preferred-size: 66.666667%;
    flex-basis: 66.666667%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 66.666667%;
  }
}

.emotion-class {
  text-align: left;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  font-weight: 700;
  color: #303757;
  margin-right: 5px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #303757;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 150%;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  width: 100%;
  box-sizing: border-box;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: row nowrap;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  margin-top: 30px;
  border-top: 1px solid #E4E5EB;
  padding-top: 40px;
}

@media (max-width: 768px) {
  .emotion-class {
    padding-top: 15px;
  }
}

.emotion-class button,
.emotion-class a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-negative);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: #FFFFFF;
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: saturate(0.9);
  filter: saturate(0.9);
}

.emotion-class {
  color: #FFFFFF;
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div
  data-testid="RejectPaymentModal"
>
  <div
    class="emotion-class"
  >
    <div
      class="ModalSection"
    >
      <div
        class="emotion-class"
      >
        <div
          class="MuiGrid-root MuiGrid-container emotion-class"
          style="position: relative;"
        >
          <div
            class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 GridContainerWrapper_GridRow emotion-class"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 emotion-class"
            >
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 emotion-class"
              >
                <span
                  class="emotion-class"
                  style="font-weight: 400;"
                >
                  Number of Invoices to be rejected
                </span>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  2
                </span>
              </div>
            </div>
          </div>
          <div
            class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 GridContainerWrapper_GridRow emotion-class"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 emotion-class"
            >
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 emotion-class"
              >
                <span
                  class="emotion-class"
                  style="font-weight: 400;"
                >
                  Amount
                </span>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 emotion-class"
              >
                <span
                  style="display: flex; flex-direction: column; gap: 10px;"
                >
                  <div
                    class="emotion-class"
                  >
                    <span
                      class="emotion-class"
                    >
                      123.45
                    </span>
                    <span
                      class="emotion-class"
                    >
                      USD
                    </span>
                  </div>
                  <div
                    class="emotion-class"
                  >
                    <span
                      class="emotion-class"
                    >
                      1,000.14
                    </span>
                    <span
                      class="emotion-class"
                    >
                      AUD
                    </span>
                  </div>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ModalSection"
    >
      <div
        style="margin-bottom: 5px;"
      >
        <span
          class="emotion-class"
          data-testid="InputLabel_ReasonForRejection"
        >
          Reason for rejection
          <span
            class="emotion-class"
            style="margin-left: 10px; color: rgb(166, 171, 191);"
          >
            Optional
          </span>
        </span>
      </div>
      <div
        class="emotion-class"
      >
        <textarea
          class="emotion-class"
          placeholder="Leave a note why you are rejecting Invoices"
          style="height: -104px;"
          variant="default"
        />
      </div>
      <div
        class="emotion-class"
      >
        <div />
      </div>
    </div>
  </div>
  <div
    class="ModalFooter emotion-class"
  >
    <button
      class=" emotion-class"
      data-testid="Modal_ConfirmAction"
    >
      <span
        class="emotion-class"
      >
        Reject
      </span>
    </button>
    <button
      class=" emotion-class"
      data-testid="Modal_CancelAction"
    >
      <span
        class="emotion-class"
      >
        Cancel
      </span>
    </button>
  </div>
</div>
`;

exports[`render payment modal multiple with timesheets 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 30px;
}

.emotion-class {
  border: 0;
  border-radius: 4px;
  background-color: #F7F7F7;
  display: block;
  padding: 20px;
}

.emotion-class .GridContainerWrapper_GridRow:not(:last-of-type):after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  border-bottom: 1px solid #E4E5EB;
  padding-bottom: 20px;
}

.emotion-class {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  row-gap: 41px;
}

.emotion-class {
  box-sizing: border-box;
  margin: 0;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  max-width: 100%;
}

@media (min-width:600px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

@media (min-width:900px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

@media (min-width:1200px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

@media (min-width:1536px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

.emotion-class {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-top: calc(-1 * 16px);
  width: calc(100% + 16px);
  margin-left: calc(-1 * 16px);
}

.emotion-class>.MuiGrid-item {
  padding-top: 16px;
}

.emotion-class>.MuiGrid-item {
  padding-left: 16px;
}

.emotion-class {
  box-sizing: border-box;
  margin: 0;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  max-width: 100%;
}

@media (min-width:600px) {
  .emotion-class {
    -webkit-flex-basis: 33.333333%;
    -ms-flex-preferred-size: 33.333333%;
    flex-basis: 33.333333%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 33.333333%;
  }
}

@media (min-width:900px) {
  .emotion-class {
    -webkit-flex-basis: 33.333333%;
    -ms-flex-preferred-size: 33.333333%;
    flex-basis: 33.333333%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 33.333333%;
  }
}

@media (min-width:1200px) {
  .emotion-class {
    -webkit-flex-basis: 33.333333%;
    -ms-flex-preferred-size: 33.333333%;
    flex-basis: 33.333333%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 33.333333%;
  }
}

@media (min-width:1536px) {
  .emotion-class {
    -webkit-flex-basis: 33.333333%;
    -ms-flex-preferred-size: 33.333333%;
    flex-basis: 33.333333%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 33.333333%;
  }
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  box-sizing: border-box;
  margin: 0;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  max-width: 100%;
}

@media (min-width:600px) {
  .emotion-class {
    -webkit-flex-basis: 66.666667%;
    -ms-flex-preferred-size: 66.666667%;
    flex-basis: 66.666667%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 66.666667%;
  }
}

@media (min-width:900px) {
  .emotion-class {
    -webkit-flex-basis: 66.666667%;
    -ms-flex-preferred-size: 66.666667%;
    flex-basis: 66.666667%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 66.666667%;
  }
}

@media (min-width:1200px) {
  .emotion-class {
    -webkit-flex-basis: 66.666667%;
    -ms-flex-preferred-size: 66.666667%;
    flex-basis: 66.666667%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 66.666667%;
  }
}

@media (min-width:1536px) {
  .emotion-class {
    -webkit-flex-basis: 66.666667%;
    -ms-flex-preferred-size: 66.666667%;
    flex-basis: 66.666667%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 66.666667%;
  }
}

.emotion-class {
  text-align: left;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  font-weight: 700;
  color: #303757;
  margin-right: 5px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #303757;
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  background-color: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 6px 16px;
  color: rgb(30, 70, 32);
  border: 1px solid #4caf50;
}

.emotion-class .MuiAlert-icon {
  color: #2e7d32;
}

.emotion-class.MuiAlert-root {
  width: 100%;
  max-width: 100%;
  color: #303757;
  background-color: #FFF6ED;
  border-color: #FFD2A6;
  font-family: "Open Sans",sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  padding: 15px 10px 15px 20px;
  margin: 0;
  box-sizing: border-box;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class.MuiAlert-root .MuiAlert-icon {
  padding: 0;
  margin: 0 4px 0 0;
}

.emotion-class.MuiAlert-root .MuiAlert-message {
  padding: 0;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.emotion-class.MuiAlert-root a {
  color: #465AB6;
}

@media (max-width: 768px) {
  .emotion-class.MuiAlert-root {
    padding: 20px;
  }
}

.emotion-class {
  margin-right: 12px;
  padding: 7px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 22px;
  opacity: 0.9;
}

.emotion-class {
  padding: 8px 0;
  min-width: 0;
  overflow: auto;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class>:first-of-type {
  padding: 0;
}

@media (max-width: 768px) {
  .emotion-class {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: start;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: start;
  }

  .emotion-class div {
    margin: 15px 0 0 0;
  }
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 150%;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  width: 100%;
  box-sizing: border-box;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: row nowrap;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class:hover .checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class:hover>.checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6!important;
}

.emotion-class {
  margin: 1px 0 -1px 6px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  color: #303757;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  margin-top: 30px;
  border-top: 1px solid #E4E5EB;
  padding-top: 40px;
}

@media (max-width: 768px) {
  .emotion-class {
    padding-top: 15px;
  }
}

.emotion-class button,
.emotion-class a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-negative);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: #FFFFFF;
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: saturate(0.9);
  filter: saturate(0.9);
}

.emotion-class {
  color: #FFFFFF;
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div
  data-testid="RejectPaymentModal"
>
  <div
    class="emotion-class"
  >
    <div
      class="ModalSection"
    >
      <div
        class="emotion-class"
      >
        <div
          class="MuiGrid-root MuiGrid-container emotion-class"
          style="position: relative;"
        >
          <div
            class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 GridContainerWrapper_GridRow emotion-class"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 emotion-class"
            >
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 emotion-class"
              >
                <span
                  class="emotion-class"
                  style="font-weight: 400;"
                >
                  Number of Invoices to be rejected
                </span>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  2
                </span>
              </div>
            </div>
          </div>
          <div
            class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 GridContainerWrapper_GridRow emotion-class"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 emotion-class"
            >
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 emotion-class"
              >
                <span
                  class="emotion-class"
                  style="font-weight: 400;"
                >
                  Amount
                </span>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 emotion-class"
              >
                <span
                  style="display: flex; flex-direction: column; gap: 10px;"
                >
                  <div
                    class="emotion-class"
                  >
                    <span
                      class="emotion-class"
                    >
                      123.45
                    </span>
                    <span
                      class="emotion-class"
                    >
                      USD
                    </span>
                  </div>
                  <div
                    class="emotion-class"
                  >
                    <span
                      class="emotion-class"
                    >
                      1,000.14
                    </span>
                    <span
                      class="emotion-class"
                    >
                      AUD
                    </span>
                  </div>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ModalSection"
    >
      <div
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 MuiAlert-root MuiAlert-colorSuccess MuiAlert-outlinedSuccess MuiAlert-outlined emotion-class"
        role="alert"
        style="--Paper-shadow: none;"
      >
        <div
          class="MuiAlert-icon emotion-class"
        >
          <icon-mock
            classname="css-hdmw1m"
            fill="#FFA64C"
            fontsize="inherit"
            size="30"
          />
        </div>
        <div
          class="MuiAlert-message emotion-class"
        >
          <div
            class="emotion-class"
          >
            <div>
              The Invoices were created based on a Timesheets. When you reject these Invoices the Timesheets will be rejected as well.
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ModalSection"
    >
      <div
        style="margin-bottom: 5px;"
      >
        <span
          class="emotion-class"
          data-testid="InputLabel_ReasonForRejection"
        >
          Reason for rejection
          <span
            class="emotion-class"
            style="margin-left: 10px; color: rgb(166, 171, 191);"
          >
            Optional
          </span>
        </span>
      </div>
      <div
        class="emotion-class"
      >
        <textarea
          class="emotion-class"
          placeholder="Leave a note why you are rejecting the timesheets and Invoices"
          style="height: -104px;"
          variant="default"
        />
      </div>
      <div
        class="emotion-class"
      >
        <div />
      </div>
    </div>
    <div
      class="ModalSection"
      data-testid="RejectPaymentModal_ConfirmationCheckbox"
    >
      <div
        class="emotion-class"
      >
        <label
          class="emotion-class"
          data-testid="InputCheckbox"
        >
          <span
            class="emotion-class"
          >
            <input
              type="checkbox"
            />
            <icon-mock
              classname="checkboxOffIcon css-1dmky3q"
              fill="#D1D6ED"
            />
          </span>
          <span
            class="emotion-class"
          >
            <span
              aria-label=""
              class=" emotion-class"
              data-mui-internal-clone-element="true"
            >
              I am aware that the Invoices will be rejected and that action can’t be undone.
            </span>
          </span>
        </label>
      </div>
    </div>
  </div>
  <div
    class="ModalFooter emotion-class"
  >
    <button
      class=" emotion-class"
      data-testid="Modal_ConfirmAction"
    >
      <span
        class="emotion-class"
      >
        Reject
      </span>
    </button>
    <button
      class=" emotion-class"
      data-testid="Modal_CancelAction"
    >
      <span
        class="emotion-class"
      >
        Cancel
      </span>
    </button>
  </div>
</div>
`;

exports[`render payment modal multiple with timesheets and errors 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 30px;
}

.emotion-class {
  border: 0;
  border-radius: 4px;
  background-color: #F7F7F7;
  display: block;
  padding: 20px;
}

.emotion-class .GridContainerWrapper_GridRow:not(:last-of-type):after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  border-bottom: 1px solid #E4E5EB;
  padding-bottom: 20px;
}

.emotion-class {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  row-gap: 41px;
}

.emotion-class {
  box-sizing: border-box;
  margin: 0;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  max-width: 100%;
}

@media (min-width:600px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

@media (min-width:900px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

@media (min-width:1200px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

@media (min-width:1536px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

.emotion-class {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-top: calc(-1 * 16px);
  width: calc(100% + 16px);
  margin-left: calc(-1 * 16px);
}

.emotion-class>.MuiGrid-item {
  padding-top: 16px;
}

.emotion-class>.MuiGrid-item {
  padding-left: 16px;
}

.emotion-class {
  box-sizing: border-box;
  margin: 0;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  max-width: 100%;
}

@media (min-width:600px) {
  .emotion-class {
    -webkit-flex-basis: 33.333333%;
    -ms-flex-preferred-size: 33.333333%;
    flex-basis: 33.333333%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 33.333333%;
  }
}

@media (min-width:900px) {
  .emotion-class {
    -webkit-flex-basis: 33.333333%;
    -ms-flex-preferred-size: 33.333333%;
    flex-basis: 33.333333%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 33.333333%;
  }
}

@media (min-width:1200px) {
  .emotion-class {
    -webkit-flex-basis: 33.333333%;
    -ms-flex-preferred-size: 33.333333%;
    flex-basis: 33.333333%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 33.333333%;
  }
}

@media (min-width:1536px) {
  .emotion-class {
    -webkit-flex-basis: 33.333333%;
    -ms-flex-preferred-size: 33.333333%;
    flex-basis: 33.333333%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 33.333333%;
  }
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  box-sizing: border-box;
  margin: 0;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  max-width: 100%;
}

@media (min-width:600px) {
  .emotion-class {
    -webkit-flex-basis: 66.666667%;
    -ms-flex-preferred-size: 66.666667%;
    flex-basis: 66.666667%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 66.666667%;
  }
}

@media (min-width:900px) {
  .emotion-class {
    -webkit-flex-basis: 66.666667%;
    -ms-flex-preferred-size: 66.666667%;
    flex-basis: 66.666667%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 66.666667%;
  }
}

@media (min-width:1200px) {
  .emotion-class {
    -webkit-flex-basis: 66.666667%;
    -ms-flex-preferred-size: 66.666667%;
    flex-basis: 66.666667%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 66.666667%;
  }
}

@media (min-width:1536px) {
  .emotion-class {
    -webkit-flex-basis: 66.666667%;
    -ms-flex-preferred-size: 66.666667%;
    flex-basis: 66.666667%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 66.666667%;
  }
}

.emotion-class {
  text-align: left;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  font-weight: 700;
  color: #303757;
  margin-right: 5px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #303757;
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  background-color: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 6px 16px;
  color: rgb(30, 70, 32);
  border: 1px solid #4caf50;
}

.emotion-class .MuiAlert-icon {
  color: #2e7d32;
}

.emotion-class.MuiAlert-root {
  width: 100%;
  max-width: 100%;
  color: #303757;
  background-color: #FFF6ED;
  border-color: #FFD2A6;
  font-family: "Open Sans",sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  padding: 15px 10px 15px 20px;
  margin: 0;
  box-sizing: border-box;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class.MuiAlert-root .MuiAlert-icon {
  padding: 0;
  margin: 0 4px 0 0;
}

.emotion-class.MuiAlert-root .MuiAlert-message {
  padding: 0;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.emotion-class.MuiAlert-root a {
  color: #465AB6;
}

@media (max-width: 768px) {
  .emotion-class.MuiAlert-root {
    padding: 20px;
  }
}

.emotion-class {
  margin-right: 12px;
  padding: 7px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 22px;
  opacity: 0.9;
}

.emotion-class {
  padding: 8px 0;
  min-width: 0;
  overflow: auto;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class>:first-of-type {
  padding: 0;
}

@media (max-width: 768px) {
  .emotion-class {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: start;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: start;
  }

  .emotion-class div {
    margin: 15px 0 0 0;
  }
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 150%;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  width: 100%;
  box-sizing: border-box;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: row nowrap;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class:hover .checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class:hover>.checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6!important;
}

.emotion-class {
  margin: 1px 0 -1px 6px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  color: #303757;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  margin-top: 30px;
  border-top: 1px solid #E4E5EB;
  padding-top: 40px;
}

@media (max-width: 768px) {
  .emotion-class {
    padding-top: 15px;
  }
}

.emotion-class button,
.emotion-class a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-negative);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: #FFFFFF;
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: saturate(0.9);
  filter: saturate(0.9);
}

.emotion-class {
  color: #FFFFFF;
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div
  data-testid="RejectPaymentModal"
>
  <div
    class="emotion-class"
  >
    <div
      class="ModalSection"
    >
      <div
        class="emotion-class"
      >
        <div
          class="MuiGrid-root MuiGrid-container emotion-class"
          style="position: relative;"
        >
          <div
            class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 GridContainerWrapper_GridRow emotion-class"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 emotion-class"
            >
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 emotion-class"
              >
                <span
                  class="emotion-class"
                  style="font-weight: 400;"
                >
                  Number of Invoices to be rejected
                </span>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  2
                </span>
              </div>
            </div>
          </div>
          <div
            class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 GridContainerWrapper_GridRow emotion-class"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 emotion-class"
            >
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 emotion-class"
              >
                <span
                  class="emotion-class"
                  style="font-weight: 400;"
                >
                  Amount
                </span>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 emotion-class"
              >
                <span
                  style="display: flex; flex-direction: column; gap: 10px;"
                >
                  <div
                    class="emotion-class"
                  >
                    <span
                      class="emotion-class"
                    >
                      123.45
                    </span>
                    <span
                      class="emotion-class"
                    >
                      USD
                    </span>
                  </div>
                  <div
                    class="emotion-class"
                  >
                    <span
                      class="emotion-class"
                    >
                      1,000.14
                    </span>
                    <span
                      class="emotion-class"
                    >
                      AUD
                    </span>
                  </div>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ModalSection"
    >
      <div
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 MuiAlert-root MuiAlert-colorSuccess MuiAlert-outlinedSuccess MuiAlert-outlined emotion-class"
        role="alert"
        style="--Paper-shadow: none;"
      >
        <div
          class="MuiAlert-icon emotion-class"
        >
          <icon-mock
            classname="css-hdmw1m"
            fill="#FFA64C"
            fontsize="inherit"
            size="30"
          />
        </div>
        <div
          class="MuiAlert-message emotion-class"
        >
          <div
            class="emotion-class"
          >
            <div>
              The Invoices were created based on a Timesheets. When you reject these Invoices the Timesheets will be rejected as well.
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ModalSection"
    >
      <div
        style="margin-bottom: 5px;"
      >
        <span
          class="emotion-class"
          data-testid="InputLabel_ReasonForRejection"
        >
          Reason for rejection
          <span
            class="emotion-class"
            style="margin-left: 10px; color: rgb(166, 171, 191);"
          >
            Optional
          </span>
        </span>
      </div>
      <div
        class="emotion-class"
      >
        <textarea
          class="emotion-class"
          placeholder="Leave a note why you are rejecting the timesheets and Invoices"
          style="height: -104px;"
          variant="default"
        />
      </div>
      <div
        class="emotion-class"
      >
        <div />
      </div>
    </div>
    <div
      class="ModalSection"
      data-testid="RejectPaymentModal_ConfirmationCheckbox"
    >
      <div
        class="emotion-class"
      >
        <label
          class="emotion-class"
          data-testid="InputCheckbox"
        >
          <span
            class="emotion-class"
          >
            <input
              type="checkbox"
            />
            <icon-mock
              classname="checkboxOffIcon css-1dmky3q"
              fill="#D1D6ED"
            />
          </span>
          <span
            class="emotion-class"
          >
            <span
              aria-label=""
              class=" emotion-class"
              data-mui-internal-clone-element="true"
            >
              I am aware that the Invoices will be rejected and that action can’t be undone.
            </span>
          </span>
        </label>
      </div>
    </div>
  </div>
  <div
    class="ModalFooter emotion-class"
  >
    <button
      class=" emotion-class"
      data-testid="Modal_ConfirmAction"
    >
      <span
        class="emotion-class"
      >
        Reject
      </span>
    </button>
    <button
      class=" emotion-class"
      data-testid="Modal_CancelAction"
    >
      <span
        class="emotion-class"
      >
        Cancel
      </span>
    </button>
  </div>
</div>
`;

exports[`render payment modal multiple with timesheets and errors 2`] = `
.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  list-style: none;
  padding-left: 0;
  margin: 0;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #FF7F8A;
  width: 100%;
  box-sizing: border-box;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  margin-top: 30px;
  border-top: 1px solid #E4E5EB;
  padding-top: 40px;
}

@media (max-width: 768px) {
  .emotion-class {
    padding-top: 15px;
  }
}

.emotion-class button,
.emotion-class a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-base);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: #FFFFFF;
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(1.15);
  filter: brightness(1.15);
}

.emotion-class {
  color: #FFFFFF;
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div
  data-testid="MessageModal"
>
  <div
    class="emotion-class"
    data-testid="MessageModal_Body"
  >
    <div>
      <h5
        class="emotion-class"
      >
        Errors:
      </h5>
      <div
        style="display: flex; flex-direction: column; gap: 10px; margin-top: 10px;"
      >
        <div>
          <p
            class="emotion-class"
          >
            Payment for Task #1 3 - 9 Oct 2010
          </p>
          <ul
            class="emotion-class"
            data-testid="ErrorList"
          >
            <li
              class="emotion-class"
            >
              Some custom backend error.
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
  <div
    class="emotion-class"
  >
    <button
      class=" emotion-class"
      data-testid="MessageModal_ConfirmButton"
    >
      <span
        class="emotion-class"
      >
        OK
      </span>
    </button>
  </div>
</div>
`;

exports[`render payment modal single 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 30px;
}

.emotion-class {
  border: 0;
  border-radius: 4px;
  background-color: #F7F7F7;
  display: block;
  padding: 20px;
}

.emotion-class .GridContainerWrapper_GridRow:not(:last-of-type):after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  border-bottom: 1px solid #E4E5EB;
  padding-bottom: 20px;
}

.emotion-class {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  row-gap: 41px;
}

.emotion-class {
  box-sizing: border-box;
  margin: 0;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  max-width: 100%;
}

@media (min-width:600px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

@media (min-width:900px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

@media (min-width:1200px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

@media (min-width:1536px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

.emotion-class {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-top: calc(-1 * 16px);
  width: calc(100% + 16px);
  margin-left: calc(-1 * 16px);
}

.emotion-class>.MuiGrid-item {
  padding-top: 16px;
}

.emotion-class>.MuiGrid-item {
  padding-left: 16px;
}

.emotion-class {
  box-sizing: border-box;
  margin: 0;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  max-width: 100%;
}

@media (min-width:600px) {
  .emotion-class {
    -webkit-flex-basis: 33.333333%;
    -ms-flex-preferred-size: 33.333333%;
    flex-basis: 33.333333%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 33.333333%;
  }
}

@media (min-width:900px) {
  .emotion-class {
    -webkit-flex-basis: 33.333333%;
    -ms-flex-preferred-size: 33.333333%;
    flex-basis: 33.333333%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 33.333333%;
  }
}

@media (min-width:1200px) {
  .emotion-class {
    -webkit-flex-basis: 33.333333%;
    -ms-flex-preferred-size: 33.333333%;
    flex-basis: 33.333333%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 33.333333%;
  }
}

@media (min-width:1536px) {
  .emotion-class {
    -webkit-flex-basis: 33.333333%;
    -ms-flex-preferred-size: 33.333333%;
    flex-basis: 33.333333%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 33.333333%;
  }
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  box-sizing: border-box;
  margin: 0;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  max-width: 100%;
}

@media (min-width:600px) {
  .emotion-class {
    -webkit-flex-basis: 66.666667%;
    -ms-flex-preferred-size: 66.666667%;
    flex-basis: 66.666667%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 66.666667%;
  }
}

@media (min-width:900px) {
  .emotion-class {
    -webkit-flex-basis: 66.666667%;
    -ms-flex-preferred-size: 66.666667%;
    flex-basis: 66.666667%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 66.666667%;
  }
}

@media (min-width:1200px) {
  .emotion-class {
    -webkit-flex-basis: 66.666667%;
    -ms-flex-preferred-size: 66.666667%;
    flex-basis: 66.666667%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 66.666667%;
  }
}

@media (min-width:1536px) {
  .emotion-class {
    -webkit-flex-basis: 66.666667%;
    -ms-flex-preferred-size: 66.666667%;
    flex-basis: 66.666667%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 66.666667%;
  }
}

.emotion-class {
  text-align: left;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  font-weight: 700;
  color: #303757;
  margin-right: 5px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #303757;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  height: 45px;
}

.emotion-class>*:first-of-type {
  padding-right: 10px;
}

.emotion-class {
  position: relative;
  max-height: -webkit-fit-content;
  max-height: -moz-fit-content;
  max-height: fit-content;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  display: inline-block;
  width: 35px;
  height: 35px;
  line-height: 35px;
  box-sizing: border-box;
  border-radius: 50%;
  text-align: center;
  color: #A6ABBF;
  font-size: 14px;
  font-weight: 700;
  background-color: #E4E5EB;
  background-image: none;
  -webkit-background-size: cover;
  background-size: cover;
  -webkit-background-position: center;
  background-position: center;
  box-shadow: inset 0px 0px 11px rgba(0, 0, 0, 0.1);
}

.emotion-class {
  position: absolute;
  height: 16px;
  bottom: -4px;
  right: 0px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-link-color)!important;
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 20px;
  cursor: pointer;
  font-weight: 600;
}

.emotion-class * {
  fill: var(--text-link-color);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: row nowrap;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 150%;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  width: 100%;
  box-sizing: border-box;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: row nowrap;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  margin-top: 30px;
  border-top: 1px solid #E4E5EB;
  padding-top: 40px;
}

@media (max-width: 768px) {
  .emotion-class {
    padding-top: 15px;
  }
}

.emotion-class button,
.emotion-class a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-negative);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: #FFFFFF;
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: saturate(0.9);
  filter: saturate(0.9);
}

.emotion-class {
  color: #FFFFFF;
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div
  data-testid="RejectPaymentModal"
>
  <div
    class="emotion-class"
  >
    <div
      class="ModalSection"
    >
      <div
        class="emotion-class"
      >
        <div
          class="MuiGrid-root MuiGrid-container emotion-class"
          style="position: relative;"
        >
          <div
            class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 GridContainerWrapper_GridRow emotion-class"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 emotion-class"
            >
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 emotion-class"
              >
                <span
                  class="emotion-class"
                  style="font-weight: 400;"
                >
                  Invoice
                </span>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  Payment #1239
                </span>
              </div>
            </div>
          </div>
          <div
            class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 GridContainerWrapper_GridRow emotion-class"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 emotion-class"
            >
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 emotion-class"
              >
                <span
                  class="emotion-class"
                  style="font-weight: 400;"
                >
                  Amount
                </span>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 emotion-class"
              >
                <span
                  style="display: flex; flex-direction: column; gap: 10px;"
                >
                  <div
                    class="emotion-class"
                  >
                    <span
                      class="emotion-class"
                    >
                      1,000.14
                    </span>
                    <span
                      class="emotion-class"
                    >
                      AUD
                    </span>
                  </div>
                </span>
              </div>
            </div>
          </div>
          <div
            class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 GridContainerWrapper_GridRow emotion-class"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 emotion-class"
            >
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 emotion-class"
              >
                <span
                  class="emotion-class"
                  style="font-weight: 400;"
                >
                  Partner
                </span>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 emotion-class"
              >
                <div
                  style="display: flex; justify-content: space-between; align-items: center; height: 45px;"
                >
                  <div
                    class="emotion-class"
                  >
                    <div
                      style="display: flex; justify-content: center;"
                    >
                      <div
                        class="emotion-class"
                      >
                        <p
                          class="emotion-class"
                        >
                          GA
                        </p>
                        <div
                          class="emotion-class"
                          size="16"
                        >
                          <span
                            aria-label=""
                            class=""
                            data-mui-internal-clone-element="true"
                            style="display: flex; align-items: center; justify-content: center; width: 16px; height: 16px; border-radius: 20px; background: rgb(6, 204, 135); padding: 0px; border: 2px solid white; box-sizing: border-box;"
                          >
                            <icon-mock
                              classname="css-qnd1lb"
                              fill="white"
                              size="16"
                            />
                          </span>
                        </div>
                      </div>
                    </div>
                    <a
                      class="emotion-class"
                      href="/"
                      style="text-decoration: none; width: 100%; color: rgb(70, 90, 182); font-size: 14px; cursor: pointer !important; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"
                      tabindex="-1"
                      title="George Vendor IV The Greatest Of All"
                    >
                      George Vendor IV The Greatest Of All
                    </a>
                  </div>
                  <div
                    class="emotion-class"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ModalSection"
    >
      <div
        style="margin-bottom: 5px;"
      >
        <span
          class="emotion-class"
          data-testid="InputLabel_ReasonForRejection"
        >
          Reason for rejection
          <span
            class="emotion-class"
            style="margin-left: 10px; color: rgb(166, 171, 191);"
          >
            Optional
          </span>
        </span>
      </div>
      <div
        class="emotion-class"
      >
        <textarea
          class="emotion-class"
          placeholder="Leave a note why you are rejecting the Invoice"
          style="height: -104px;"
          variant="default"
        />
      </div>
      <div
        class="emotion-class"
      >
        <div />
      </div>
    </div>
  </div>
  <div
    class="ModalFooter emotion-class"
  >
    <button
      class=" emotion-class"
      data-testid="Modal_ConfirmAction"
    >
      <span
        class="emotion-class"
      >
        Reject
      </span>
    </button>
    <button
      class=" emotion-class"
      data-testid="Modal_CancelAction"
    >
      <span
        class="emotion-class"
      >
        Cancel
      </span>
    </button>
  </div>
</div>
`;

exports[`render payment modal single with timesheets 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 30px;
}

.emotion-class {
  border: 0;
  border-radius: 4px;
  background-color: #F7F7F7;
  display: block;
  padding: 20px;
}

.emotion-class .GridContainerWrapper_GridRow:not(:last-of-type):after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  border-bottom: 1px solid #E4E5EB;
  padding-bottom: 20px;
}

.emotion-class {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  row-gap: 41px;
}

.emotion-class {
  box-sizing: border-box;
  margin: 0;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  max-width: 100%;
}

@media (min-width:600px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

@media (min-width:900px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

@media (min-width:1200px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

@media (min-width:1536px) {
  .emotion-class {
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 100%;
  }
}

.emotion-class {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 100%;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-top: calc(-1 * 16px);
  width: calc(100% + 16px);
  margin-left: calc(-1 * 16px);
}

.emotion-class>.MuiGrid-item {
  padding-top: 16px;
}

.emotion-class>.MuiGrid-item {
  padding-left: 16px;
}

.emotion-class {
  box-sizing: border-box;
  margin: 0;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  max-width: 100%;
}

@media (min-width:600px) {
  .emotion-class {
    -webkit-flex-basis: 33.333333%;
    -ms-flex-preferred-size: 33.333333%;
    flex-basis: 33.333333%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 33.333333%;
  }
}

@media (min-width:900px) {
  .emotion-class {
    -webkit-flex-basis: 33.333333%;
    -ms-flex-preferred-size: 33.333333%;
    flex-basis: 33.333333%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 33.333333%;
  }
}

@media (min-width:1200px) {
  .emotion-class {
    -webkit-flex-basis: 33.333333%;
    -ms-flex-preferred-size: 33.333333%;
    flex-basis: 33.333333%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 33.333333%;
  }
}

@media (min-width:1536px) {
  .emotion-class {
    -webkit-flex-basis: 33.333333%;
    -ms-flex-preferred-size: 33.333333%;
    flex-basis: 33.333333%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 33.333333%;
  }
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  box-sizing: border-box;
  margin: 0;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-flex-basis: 100%;
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -webkit-box-flex: 0;
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;
  max-width: 100%;
}

@media (min-width:600px) {
  .emotion-class {
    -webkit-flex-basis: 66.666667%;
    -ms-flex-preferred-size: 66.666667%;
    flex-basis: 66.666667%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 66.666667%;
  }
}

@media (min-width:900px) {
  .emotion-class {
    -webkit-flex-basis: 66.666667%;
    -ms-flex-preferred-size: 66.666667%;
    flex-basis: 66.666667%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 66.666667%;
  }
}

@media (min-width:1200px) {
  .emotion-class {
    -webkit-flex-basis: 66.666667%;
    -ms-flex-preferred-size: 66.666667%;
    flex-basis: 66.666667%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 66.666667%;
  }
}

@media (min-width:1536px) {
  .emotion-class {
    -webkit-flex-basis: 66.666667%;
    -ms-flex-preferred-size: 66.666667%;
    flex-basis: 66.666667%;
    -webkit-box-flex: 0;
    -webkit-flex-grow: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    max-width: 66.666667%;
  }
}

.emotion-class {
  text-align: left;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  font-weight: 700;
  color: #303757;
  margin-right: 5px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #303757;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-link-color)!important;
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 20px;
  cursor: pointer;
  font-weight: 600;
}

.emotion-class * {
  fill: var(--text-link-color);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  height: 45px;
}

.emotion-class>*:first-of-type {
  padding-right: 10px;
}

.emotion-class {
  position: relative;
  max-height: -webkit-fit-content;
  max-height: -moz-fit-content;
  max-height: fit-content;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  display: inline-block;
  width: 35px;
  height: 35px;
  line-height: 35px;
  box-sizing: border-box;
  border-radius: 50%;
  text-align: center;
  color: #A6ABBF;
  font-size: 14px;
  font-weight: 700;
  background-color: #E4E5EB;
  background-image: none;
  -webkit-background-size: cover;
  background-size: cover;
  -webkit-background-position: center;
  background-position: center;
  box-shadow: inset 0px 0px 11px rgba(0, 0, 0, 0.1);
}

.emotion-class {
  position: absolute;
  height: 16px;
  bottom: -4px;
  right: 0px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: row nowrap;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  background-color: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 6px 16px;
  color: rgb(30, 70, 32);
  border: 1px solid #4caf50;
}

.emotion-class .MuiAlert-icon {
  color: #2e7d32;
}

.emotion-class.MuiAlert-root {
  width: 100%;
  max-width: 100%;
  color: #303757;
  background-color: #FFF6ED;
  border-color: #FFD2A6;
  font-family: "Open Sans",sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  padding: 15px 10px 15px 20px;
  margin: 0;
  box-sizing: border-box;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class.MuiAlert-root .MuiAlert-icon {
  padding: 0;
  margin: 0 4px 0 0;
}

.emotion-class.MuiAlert-root .MuiAlert-message {
  padding: 0;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.emotion-class.MuiAlert-root a {
  color: #465AB6;
}

@media (max-width: 768px) {
  .emotion-class.MuiAlert-root {
    padding: 20px;
  }
}

.emotion-class {
  margin-right: 12px;
  padding: 7px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 22px;
  opacity: 0.9;
}

.emotion-class {
  padding: 8px 0;
  min-width: 0;
  overflow: auto;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class>:first-of-type {
  padding: 0;
}

@media (max-width: 768px) {
  .emotion-class {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: start;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: start;
  }

  .emotion-class div {
    margin: 15px 0 0 0;
  }
}

.emotion-class {
  position: relative;
}

.emotion-class.tableStart:before,
.emotion-class.tableEnd:after {
  opacity: 1;
  z-index: 2;
}

.emotion-class:before,
.emotion-class:after {
  opacity: 0;
  pointer-events: none;
  content: "";
  display: block;
  position: absolute;
  top: 0;
  height: 100%;
  width: 10px;
  -webkit-transition: opacity 0.3s ease-in-out;
  transition: opacity 0.3s ease-in-out;
}

.emotion-class:before {
  left: 0;
  background: linear-gradient(90deg, rgba(8, 10, 26, 0.1) 0%, rgba(8, 10, 26, 0) 80%);
  border-radius: 4px 0px 0px 4px;
}

.emotion-class:after {
  right: 0;
  background: linear-gradient(270deg, rgba(8, 10, 26, 0.1) 0%, rgba(8, 10, 26, 0) 80%);
  border-radius: 0px 4px 4px 0px;
}

.emotion-class {
  overflow-x: auto;
  border-radius: 4px;
}

.emotion-class {
  border-collapse: separate;
  width: 100%;
  height: 1px;
}

.emotion-class.mobileView tr {
  border: 1px solid #E4E5EB;
  border-color: #E4E5EB;
  border-radius: 4px;
  padding: 20px;
}

.emotion-class.mobileView td {
  min-height: 1px;
  background-color: #FFFFFF;
  border: 0;
  margin: 0;
  padding: 0!important;
  margin-bottom: 12px;
  font-size: 16px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  font-size: 14px;
  text-align: left;
  padding: 10px;
  border-width: 1px 1px 0 0;
  border-style: solid;
  border-color: #E4E5EB;
  background-color: #F7F7F7;
  box-sizing: border-box;
}

.emotion-class:first-of-type {
  border-top-left-radius: 4px;
  border-left: 1px solid #E4E5EB;
}

.emotion-class:last-of-type {
  border-top-right-radius: 4px;
}

.emotion-class:focus-within {
  background-color: #F5F6FA;
}

.emotion-class:first-of-type td {
  border-top: 1px solid #E4E5EB;
}

.emotion-class:last-of-type td:first-of-type {
  border-bottom-left-radius: 4px;
}

.emotion-class:last-of-type td:last-of-type {
  border-bottom-right-radius: 4px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  vertical-align: top;
  border-bottom: 1px solid #E4E5EB;
  border-right: 1px solid #E4E5EB;
  padding: 15px 10px;
  width: 1px;
  white-space: nowrap;
}

.emotion-class:first-of-type {
  border-left: 1px solid #E4E5EB;
}

.emotion-class:last-of-type {
  border-right: 1px solid #E4E5EB;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #FFFFFF;
  background-color: #A400FF;
  font-family: "Montserrat",sans-serif;
  font-weight: 700;
  font-size: 10px;
  line-height: 20px;
  letter-spacing: 0.5px;
  text-align: center;
  text-transform: uppercase;
  border-radius: var(--spacing-2xs);
  margin: 0;
  padding: 0 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  vertical-align: top;
  border-bottom: 1px solid #E4E5EB;
  border-right: 1px solid #E4E5EB;
  padding: 15px 10px;
  font-weight: 600;
}

.emotion-class:first-of-type {
  border-left: 1px solid #E4E5EB;
}

.emotion-class:last-of-type {
  border-right: 1px solid #E4E5EB;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  vertical-align: top;
  border-bottom: 1px solid #E4E5EB;
  border-right: 1px solid #E4E5EB;
  padding: 15px 10px;
  width: 1px;
  white-space: nowrap;
  text-align: right;
  font-weight: 600;
}

.emotion-class:first-of-type {
  border-left: 1px solid #E4E5EB;
}

.emotion-class:last-of-type {
  border-right: 1px solid #E4E5EB;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #FFFFFF;
  background-color: #A6ABBF;
  font-family: "Montserrat",sans-serif;
  font-weight: 700;
  font-size: 10px;
  line-height: 20px;
  letter-spacing: 0.5px;
  text-align: center;
  text-transform: uppercase;
  border-radius: var(--spacing-2xs);
  margin: 0;
  padding: 0 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #FFFFFF;
  background-color: #FF7F8A;
  font-family: "Montserrat",sans-serif;
  font-weight: 700;
  font-size: 10px;
  line-height: 20px;
  letter-spacing: 0.5px;
  text-align: center;
  text-transform: uppercase;
  border-radius: var(--spacing-2xs);
  margin: 0;
  padding: 0 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #FFFFFF;
  background-color: #23B4F5;
  font-family: "Montserrat",sans-serif;
  font-weight: 700;
  font-size: 10px;
  line-height: 20px;
  letter-spacing: 0.5px;
  text-align: center;
  text-transform: uppercase;
  border-radius: var(--spacing-2xs);
  margin: 0;
  padding: 0 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 150%;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  width: 100%;
  box-sizing: border-box;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: row nowrap;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class:hover .checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class:hover>.checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6!important;
}

.emotion-class {
  margin: 1px 0 -1px 6px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  color: #303757;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  margin-top: 30px;
  border-top: 1px solid #E4E5EB;
  padding-top: 40px;
}

@media (max-width: 768px) {
  .emotion-class {
    padding-top: 15px;
  }
}

.emotion-class button,
.emotion-class a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-negative);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: #FFFFFF;
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: saturate(0.9);
  filter: saturate(0.9);
}

.emotion-class {
  color: #FFFFFF;
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div
  data-testid="RejectPaymentModal"
>
  <div
    class="emotion-class"
  >
    <div
      class="ModalSection"
    >
      <div
        class="emotion-class"
      >
        <div
          class="MuiGrid-root MuiGrid-container emotion-class"
          style="position: relative;"
        >
          <div
            class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 GridContainerWrapper_GridRow emotion-class"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 emotion-class"
            >
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 emotion-class"
              >
                <span
                  class="emotion-class"
                  style="font-weight: 400;"
                >
                  Invoice
                </span>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  Payment for Task #1 3 - 9 Oct 2010
                </span>
              </div>
            </div>
          </div>
          <div
            class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 GridContainerWrapper_GridRow emotion-class"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 emotion-class"
            >
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 emotion-class"
              >
                <span
                  class="emotion-class"
                  style="font-weight: 400;"
                >
                  Amount
                </span>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 emotion-class"
              >
                <span
                  style="display: flex; flex-direction: column; gap: 10px;"
                >
                  <div
                    class="emotion-class"
                  >
                    <span
                      class="emotion-class"
                    >
                      123.45
                    </span>
                    <span
                      class="emotion-class"
                    >
                      USD
                    </span>
                  </div>
                </span>
              </div>
            </div>
          </div>
          <div
            class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 GridContainerWrapper_GridRow emotion-class"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 emotion-class"
            >
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 emotion-class"
              >
                <span
                  class="emotion-class"
                  style="font-weight: 400;"
                >
                  Task
                </span>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 emotion-class"
              >
                <a
                  class="emotion-class"
                  href="/"
                  rel="noopener noreferrer"
                  style="text-decoration: none;"
                  tabindex="-1"
                  target="_blank"
                  title="Python backend with 0 tests"
                >
                  Python backend with 0 tests
                </a>
              </div>
            </div>
          </div>
          <div
            class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 GridContainerWrapper_GridRow emotion-class"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 emotion-class"
            >
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 emotion-class"
              >
                <span
                  class="emotion-class"
                  style="font-weight: 400;"
                >
                  Partner
                </span>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 emotion-class"
              >
                <div
                  style="display: flex; justify-content: space-between; align-items: center; height: 45px;"
                >
                  <div
                    class="emotion-class"
                  >
                    <div
                      style="display: flex; justify-content: center;"
                    >
                      <div
                        class="emotion-class"
                      >
                        <p
                          class="emotion-class"
                        >
                          GA
                        </p>
                        <div
                          class="emotion-class"
                          size="16"
                        >
                          <span
                            aria-label=""
                            class=""
                            data-mui-internal-clone-element="true"
                            style="display: flex; align-items: center; justify-content: center; width: 16px; height: 16px; border-radius: 20px; background: rgb(6, 204, 135); padding: 0px; border: 2px solid white; box-sizing: border-box;"
                          >
                            <icon-mock
                              classname="css-qnd1lb"
                              fill="white"
                              size="16"
                            />
                          </span>
                        </div>
                      </div>
                    </div>
                    <a
                      class="emotion-class"
                      href="/"
                      style="text-decoration: none; width: 100%; color: rgb(70, 90, 182); font-size: 14px; cursor: pointer !important; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"
                      tabindex="-1"
                      title="George Vendor IV The Greatest Of All"
                    >
                      George Vendor IV The Greatest Of All
                    </a>
                  </div>
                  <div
                    class="emotion-class"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ModalSection"
    >
      <div
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 MuiAlert-root MuiAlert-colorSuccess MuiAlert-outlinedSuccess MuiAlert-outlined emotion-class"
        role="alert"
        style="--Paper-shadow: none;"
      >
        <div
          class="MuiAlert-icon emotion-class"
        >
          <icon-mock
            classname="css-hdmw1m"
            fill="#FFA64C"
            fontsize="inherit"
            size="30"
          />
        </div>
        <div
          class="MuiAlert-message emotion-class"
        >
          <div
            class="emotion-class"
          >
            <div>
              The Invoice was created based on a Timesheet. When you reject this Invoice the Timesheet will be rejected as well.
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ModalSection"
    >
      <div
        data-testid="Search_DataTable"
      >
        <div
          class=" emotion-class"
          data-testid="Search_DataTable_Table"
        >
          <div
            class="emotion-class"
            style="display: flex;"
          >
            <div
              class="trigger"
              style="position: relative; left: 1px;"
            />
            <table
              cellpadding="0"
              cellspacing="0"
              class=" emotion-class"
            >
              <thead>
                <tr>
                  <th
                    class="emotion-class"
                    data-testid="DataTable_ColumnHeader_Status"
                  >
                    Status
                  </th>
                  <th
                    class="emotion-class"
                    data-testid="DataTable_ColumnHeader_Timesheet"
                  >
                    Timesheet
                  </th>
                  <th
                    class="emotion-class"
                    data-testid="DataTable_ColumnHeader_Total"
                  >
                    Total
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Approved
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    01/01/2023 - 01/07/2023
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    0:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Approved
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/09/2022 - 10/15/2022
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    1488:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/02/2022 - 10/08/2022
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    2976:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/10/2021 - 10/16/2021
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Rejected
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/11/2020 - 10/17/2020
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/11/2020 - 10/17/2020
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/04/2020 - 10/10/2020
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/04/2020 - 10/10/2020
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    3720:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Under review
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/27/2020 - 10/03/2020
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Approved
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/29/2019 - 10/05/2019
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Approved
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/29/2019 - 10/05/2019
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    2232:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/07/2018 - 10/13/2018
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/30/2018 - 10/06/2018
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/30/2018 - 10/06/2018
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    2232:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/01/2017 - 10/07/2017
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    1488:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/01/2017 - 10/07/2017
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Approved
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/09/2016 - 10/15/2016
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/09/2016 - 10/15/2016
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/02/2016 - 10/08/2016
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    1488:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/04/2015 - 10/10/2015
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    1488:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/27/2015 - 10/03/2015
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/27/2015 - 10/03/2015
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/05/2014 - 10/11/2014
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    2976:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/06/2013 - 10/12/2013
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    1488:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/06/2013 - 10/12/2013
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    3720:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/07/2012 - 10/13/2012
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Under review
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/07/2012 - 10/13/2012
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    1488:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/30/2012 - 10/06/2012
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    1488:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/09/2011 - 10/15/2011
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    1488:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/02/2011 - 10/08/2011
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Under review
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/02/2011 - 10/08/2011
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    1488:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Approved
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/03/2010 - 10/09/2010
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/26/2010 - 10/02/2010
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/26/2010 - 10/02/2010
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/04/2009 - 10/10/2009
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/27/2009 - 10/03/2009
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/05/2008 - 10/11/2008
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    2976:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/07/2007 - 10/13/2007
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/30/2007 - 10/06/2007
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    3720:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/08/2006 - 10/14/2006
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Under review
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/08/2006 - 10/14/2006
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Under review
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/01/2006 - 10/07/2006
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    1488:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/09/2005 - 10/15/2005
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    1488:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/02/2005 - 10/08/2005
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/02/2005 - 10/08/2005
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    5208:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Under review
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/25/2005 - 10/01/2005
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/10/2004 - 10/16/2004
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    2232:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/03/2004 - 10/09/2004
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/03/2004 - 10/09/2004
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    1488:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/05/2003 - 10/11/2003
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/28/2003 - 10/04/2003
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    1488:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/06/2002 - 10/12/2002
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/06/2002 - 10/12/2002
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    1488:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/07/2001 - 10/13/2001
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    2232:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/30/2001 - 10/06/2001
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/30/2001 - 10/06/2001
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    2232:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Under review
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/08/2000 - 10/14/2000
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Under review
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/10/1999 - 10/16/1999
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Under review
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/03/1999 - 10/09/1999
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/26/1999 - 10/02/1999
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Under review
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/11/1998 - 10/17/1998
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    1488:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/04/1998 - 10/10/1998
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Under review
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/27/1998 - 10/03/1998
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/12/1997 - 10/18/1997
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    1488:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/05/1997 - 10/11/1997
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Under review
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/05/1997 - 10/11/1997
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    1488:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Under review
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/28/1997 - 10/04/1997
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    2976:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/06/1996 - 10/12/1996
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Under review
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/06/1996 - 10/12/1996
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    1488:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/29/1996 - 10/05/1996
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/29/1996 - 10/05/1996
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/08/1995 - 10/14/1995
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    1488:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/01/1995 - 10/07/1995
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    4464:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Under review
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/09/1994 - 10/15/1994
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/02/1994 - 10/08/1994
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    5208:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/10/1993 - 10/16/1993
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    1488:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/03/1993 - 10/09/1993
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/26/1993 - 10/02/1993
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/11/1992 - 10/17/1992
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/04/1992 - 10/10/1992
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    2232:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/27/1992 - 10/03/1992
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/06/1991 - 10/12/1991
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Under review
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/06/1991 - 10/12/1991
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    3720:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Under review
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/29/1991 - 10/05/1991
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Under review
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/29/1991 - 10/05/1991
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Under review
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/07/1990 - 10/13/1990
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Under review
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    10/07/1990 - 10/13/1990
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    4464:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Under review
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/30/1990 - 10/06/1990
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    744:00
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px;"
                  >
                    <div
                      class="emotion-class"
                      data-testid="Chip"
                    >
                      Unsubmitted
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    09/30/1990 - 10/06/1990
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    1488:00
                  </td>
                </tr>
              </tbody>
            </table>
            <div
              class="trigger"
              style="position: relative; right: 1px;"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="ModalSection"
    >
      <div
        style="margin-bottom: 5px;"
      >
        <span
          class="emotion-class"
          data-testid="InputLabel_ReasonForRejection"
        >
          Reason for rejection
          <span
            class="emotion-class"
            style="margin-left: 10px; color: rgb(166, 171, 191);"
          >
            Optional
          </span>
        </span>
      </div>
      <div
        class="emotion-class"
      >
        <textarea
          class="emotion-class"
          placeholder="Leave a note why you are rejecting the timesheet and Invoice"
          style="height: -104px;"
          variant="default"
        />
      </div>
      <div
        class="emotion-class"
      >
        <div />
      </div>
    </div>
    <div
      class="ModalSection"
      data-testid="RejectPaymentModal_ConfirmationCheckbox"
    >
      <div
        class="emotion-class"
      >
        <label
          class="emotion-class"
          data-testid="InputCheckbox"
        >
          <span
            class="emotion-class"
          >
            <input
              type="checkbox"
            />
            <icon-mock
              classname="checkboxOffIcon css-1dmky3q"
              fill="#D1D6ED"
            />
          </span>
          <span
            class="emotion-class"
          >
            <span
              aria-label=""
              class=" emotion-class"
              data-mui-internal-clone-element="true"
            >
              I am aware that the Invoice will be rejected and that action can’t be undone.
            </span>
          </span>
        </label>
      </div>
    </div>
  </div>
  <div
    class="ModalFooter emotion-class"
  >
    <button
      class=" emotion-class"
      data-testid="Modal_ConfirmAction"
    >
      <span
        class="emotion-class"
      >
        Reject
      </span>
    </button>
    <button
      class=" emotion-class"
      data-testid="Modal_CancelAction"
    >
      <span
        class="emotion-class"
      >
        Cancel
      </span>
    </button>
  </div>
</div>
`;
