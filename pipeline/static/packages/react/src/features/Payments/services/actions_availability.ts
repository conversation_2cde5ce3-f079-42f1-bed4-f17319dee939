import { noop } from 'lodash';
import { DropdownOption } from '../../../components/Utils/Dropdown/Dropdown';
import { openModal } from '../../../components/Utils/Modals/modal.helpers';
import i18n from '../../../i18n/i18n';
import Api from '../../../services/Api';
import { reduxStore } from '../../../services/Redux';
import { UserPermissionsService } from '../../../services/Shortlist/User/UserPermissionsService';
import {
  Payment,
  PaymentAction,
  PaymentBulkActionResponse,
} from '../../../types/payments';
import { formatDateTimeWithUserSettings } from '../../../utils/date';
import { GenericItemActionBlueprint } from '../../../utils/other/item-actions';
import {
  getPaymentActionAvailability,
  getPaymentAvailabilityReasonsTranslated,
} from '../domain/PaymentActionAvailability';
import { runPaymentAction } from './actions';

export type InvoiceActionResult = {
  action: PaymentAction;
  result: Payment | null | 'canceled';
};

type PaymentItemActionBlueprint = GenericItemActionBlueprint<
  Payment,
  PaymentAction,
  InvoiceActionResult
> & {
  getBlockedReason?: (payment: Payment) => Promise<string | null>;
};

type PaymentActionBlueprintsType = {
  [key in PaymentAction]: PaymentItemActionBlueprint;
};

const migratedAction = async (
  payment: Payment,
  action: PaymentAction
): Promise<InvoiceActionResult> => {
  let actionSlug;
  if (action === 'edit') {
    actionSlug = 'PAYMENT_EDIT_MODAL';
  } else if (action === 'add_similar') {
    actionSlug = 'PAYMENT_ADD_SIMILAR_MODAL';
  } else {
    throw new Error('Unknown action');
  }

  return reduxStore
    .getState()
    .angularMigration.actions.payments[actionSlug](payment)
    .then((payment) => {
      if (!payment) {
        return { action, result: 'canceled' };
      }
      return {
        action,
        result: payment,
      };
    });
};

const simpleActionSingle = async (
  payment: Payment,
  action: PaymentAction,
  params: {
    noConfirmation?: boolean;
    isMessageModal?: boolean;
  } = {},
  actionParams: any = {}
): Promise<InvoiceActionResult> => {
  /**
   * Simple wrapper around runPaymentAction to transform result into a form
   * needed by DataTable.
   * This result is needed to refresh changed payment on the list if needed.
   */
  const bulkResult = await runPaymentAction({
    payments: [payment],
    action,
    actionParams,
    noConfirmation: params?.noConfirmation,
  });
  let result = null;
  if (bulkResult?.payload?.refreshed_payments?.length > 0) {
    result = bulkResult.payload.refreshed_payments[0];
  } else if (bulkResult.result === 'canceled') {
    result = 'canceled';
  }
  return { action, result };
};

const simpleActionMany = (
  payments: Payment[],
  action: PaymentAction,
  params: {
    noConfirmation?: boolean;
    isMessageModal?: boolean;
  } = {}
): Promise<PaymentBulkActionResponse> =>
  runPaymentAction({
    payments,
    action,
    ...params,
  });

const isActionAvailable =
  (action: PaymentAction): ((payment: Payment) => Promise<boolean>) =>
  async (payment: Payment): Promise<boolean> =>
    getPaymentActionAvailability(payment, action).is_visible;

const isActionAvailableMany = (
  action: PaymentAction
): ((payments: Payment[]) => Promise<boolean>) => {
  const manyAllowed = [
    'approve',
    'schedule',
    'reject',
    'paid',
    'delete',
    'generate_zip',
  ];
  return async (payments: Payment[]): Promise<boolean> => {
    if (!manyAllowed.includes(action)) {
      return false;
    }
    const allowedForPaymentsCount = payments.filter(
      (p) => getPaymentActionAvailability(p, action).is_visible
    ).length;
    return allowedForPaymentsCount === payments.length;
  };
};

const openUpsellModal = () => {
  openModal({
    type: 'upsell_modal',
  }).catch(noop);
  return null;
};

const getPaymentActionBlockedReason = (
  action: PaymentAction,
  payment: Payment
): string | null => {
  const actionAvailability = getPaymentActionAvailability(payment, action);
  if (actionAvailability.is_allowed) {
    return null;
  }
  return getPaymentAvailabilityReasonsTranslated(
    actionAvailability?.reasons
  )[0];
};

/**
 * Important note: Order of actions inside of this object determines in which
 *                 order they will be shown on user interface, if action
 *                 will be shown for invoice (this is determined by backend
 *                 and Payment.actions_availability property).
 */
export const paymentActionBlueprints: PaymentActionBlueprintsType = {
  approve: {
    id: 'approve',
    title: 'approve',
    icon: 'PaymentBilledIcon',
    isAvailableSingle: isActionAvailable('approve'),
    isAvailableMany: isActionAvailableMany('approve'),
    getBlockedReason: async (payment) =>
      getPaymentActionBlockedReason('approve', payment),
    prepareActionSingle: (payment) => async () =>
      simpleActionSingle(payment, 'approve', {
        noConfirmation: true,
      }),
    prepareActionMany: (payments) => async () =>
      simpleActionMany(payments, 'approve'),
  },
  schedule: {
    id: 'schedule',
    title: 'schedule',
    icon: 'DatePickerIcon',
    isAvailableSingle: isActionAvailable('schedule'),
    isAvailableMany: isActionAvailableMany('schedule'),
    getBlockedReason: async (payment) =>
      getPaymentActionBlockedReason('schedule', payment),
    prepareActionSingle: (payment, params: any) => async () =>
      simpleActionSingle(payment, 'schedule', { noConfirmation: true }, params),
    prepareActionMany: (payments) => async () =>
      simpleActionMany(payments, 'schedule'),
  },
  reject: {
    id: 'reject',
    title: 'reject',
    icon: 'PaymentRejectedIcon',
    isAvailableSingle: isActionAvailable('reject'),
    isAvailableMany: isActionAvailableMany('reject'),
    getBlockedReason: async (payment) =>
      getPaymentActionBlockedReason('reject', payment),
    prepareActionSingle: (payment) => async () =>
      simpleActionSingle(payment, 'reject'),
    prepareActionMany: (payments) => async () =>
      simpleActionMany(payments, 'reject'),
  },
  unrejected: {
    id: 'unrejected',
    title: 'unrejected',
    icon: 'NoBilledIcon',
    isAvailableSingle: isActionAvailable('unrejected'),
    isAvailableMany: isActionAvailableMany('unrejected'),
    getBlockedReason: async (payment) =>
      getPaymentActionBlockedReason('unrejected', payment),
    prepareActionSingle: (payment) => async () =>
      simpleActionSingle(payment, 'unrejected', {
        noConfirmation: true,
      }),
  },
  paid: {
    id: 'paid',
    title: 'paid',
    icon: 'PaymentBilledIcon',
    isAvailableSingle: isActionAvailable('paid'),
    isAvailableMany: isActionAvailableMany('paid'),
    getBlockedReason: async (payment) =>
      getPaymentActionBlockedReason('paid', payment),
    prepareActionSingle: (payment) => async () =>
      simpleActionSingle(payment, 'paid', {
        noConfirmation: true,
      }),
    prepareActionMany: (payments) => async () =>
      simpleActionMany(payments, 'paid'),
  },
  unpaid: {
    id: 'unpaid',
    title: 'unpaid',
    isAvailableSingle: isActionAvailable('unpaid'),
    isAvailableMany: isActionAvailableMany('unpaid'),
    getBlockedReason: async (payment) =>
      getPaymentActionBlockedReason('unpaid', payment),
    prepareActionSingle: (payment) => async () =>
      simpleActionSingle(payment, 'unpaid', {
        noConfirmation: true,
      }),
  },
  unschedule: {
    id: 'unschedule',
    title: 'unschedule',
    isAvailableSingle: isActionAvailable('unschedule'),
    isAvailableMany: isActionAvailableMany('unschedule'),
    getBlockedReason: async (payment) =>
      getPaymentActionBlockedReason('unschedule', payment),
    prepareActionSingle: (payment) => async () =>
      simpleActionSingle(payment, 'unschedule', {
        noConfirmation: true,
      }),
  },
  flag_problem: {
    id: 'flag_problem',
    title: 'flag_problem',
    isAvailableSingle: isActionAvailable('flag_problem'),
    isAvailableMany: isActionAvailableMany('flag_problem'),
    getBlockedReason: async (payment) =>
      getPaymentActionBlockedReason('flag_problem', payment),
    prepareActionSingle: (payment) => async () =>
      simpleActionSingle(payment, 'flag_problem'),
  },
  unflag_problem: {
    id: 'unflag_problem',
    title: 'unflag_problem',
    isAvailableSingle: isActionAvailable('unflag_problem'),
    isAvailableMany: isActionAvailableMany('unflag_problem'),
    getBlockedReason: async (payment) =>
      getPaymentActionBlockedReason('unflag_problem', payment),
    prepareActionSingle: (payment) => async () =>
      simpleActionSingle(payment, 'unflag_problem', {
        noConfirmation: true,
      }),
  },
  edit: {
    id: 'edit',
    title: 'edit',
    isAvailableSingle: isActionAvailable('edit'),
    isAvailableMany: isActionAvailableMany('edit'),
    getBlockedReason: async (payment) =>
      getPaymentActionBlockedReason('edit', payment),
    prepareActionSingle: (payment) => async () =>
      migratedAction(payment, 'edit'),
  },
  generate_pdf: {
    id: 'generate_pdf',
    title: 'generate_pdf',
    isAvailableSingle: isActionAvailable('generate_pdf'),
    getBlockedReason: async (payment) =>
      getPaymentActionBlockedReason('generate_pdf', payment),
    isAvailableMany: async () => false,
    prepareActionSingle: (payment) => async () => {
      window.open(Api.Payments.getGeneratePDFUrl(payment.id), '_blank');
      return null;
    },
  },
  download_attachment: {
    id: 'download_attachment',
    title: 'download_attachment',
    isAvailableSingle: async (payment) =>
      !!payment.filename &&
      (await isActionAvailable('download_attachment')(payment)),
    getBlockedReason: async (payment) =>
      getPaymentActionBlockedReason('download_attachment', payment),
    isAvailableMany: async () => false,
    prepareActionSingle: (payment) => async () => {
      window.open(Api.Payments.getAttachmentUrl(payment.id), '_blank');
      return null;
    },
  },
  add_similar: {
    id: 'add_similar',
    title: 'add_similar',
    isAvailableSingle: isActionAvailable('add_similar'),
    getBlockedReason: async (payment) =>
      getPaymentActionBlockedReason('add_similar', payment),
    isAvailableMany: async () => false,
    prepareActionSingle: (payment) => async () =>
      migratedAction(payment, 'add_similar'),
  },
  delete: {
    id: 'delete',
    title: 'delete',
    icon: 'DeleteIcon',
    isAvailableSingle: isActionAvailable('delete'),
    isAvailableMany: isActionAvailableMany('delete'),
    getBlockedReason: async (payment) =>
      getPaymentActionBlockedReason('delete', payment),
    prepareActionSingle: (payment) => async () =>
      simpleActionSingle(payment, 'delete'),
    prepareActionMany: (payments) => async () =>
      simpleActionMany(payments, 'delete'),
  },
  generate_zip: {
    id: 'generate_zip',
    title: 'generate_zip',
    icon: 'GenerateIcon',
    isAvailableSingle: async () => false,
    getBlockedReason: async (payment) =>
      getPaymentActionBlockedReason('generate_zip', payment),
    isAvailableMany: isActionAvailableMany('generate_zip'),
    prepareActionMany: (payments) => async () =>
      simpleActionMany(payments, 'generate_zip', {
        noConfirmation: true,
        isMessageModal: true,
      }),
  },
  upsell: {
    id: 'upsell',
    title: 'upsell',
    icon: 'GlobalPayIcon',
    isAvailableSingle: async () =>
      UserPermissionsService.canSeePaymentsUpsell(),
    isAvailableMany: async () => UserPermissionsService.canSeePaymentsUpsell(),
    prepareActionSingle: () => async () => openUpsellModal(),
    prepareActionMany: () => async () => openUpsellModal(),
  },
};

export type ComputedInvoiceAction = {
  id: string;
  title: string;
  icon: any;
  isDisabled?: boolean;
  isLabel?: boolean;
  tooltip?: string;
  execute?: () => Promise<InvoiceActionResult>;
  subactions?: ComputedInvoiceAction[];
  group?: string;
};

const getScheduleDropdownOptions = async (
  payment: Payment,
  actionBlueprint: PaymentItemActionBlueprint,
  availableScheduledDates: string[]
): Promise<ComputedInvoiceAction[]> => {
  if (availableScheduledDates.length === 0) {
    return [];
  }
  const scheduleDropdownOptions = [];
  if (payment.status === 'scheduled') {
    scheduleDropdownOptions.push({
      isLabel: true,
      title: i18n.t('Payments:PaymentActionGroup.RescheduleForProcessing'),
    });
  } else {
    scheduleDropdownOptions.push({
      isLabel: true,
      title: i18n.t('Payments:PaymentActionGroup.ScheduleForProcessing'),
    });
  }
  for (const date of availableScheduledDates) {
    scheduleDropdownOptions.push({
      id: `${actionBlueprint.id}_${date}`,
      title: formatDateTimeWithUserSettings(date),
      icon: actionBlueprint.icon,
      group: 'schedule',
      execute: actionBlueprint.prepareActionSingle(payment, {
        schedule_for: date,
      }),
    });
  }
  return scheduleDropdownOptions;
};

export const getInvoiceActionTitle = (
  blueprint: PaymentItemActionBlueprint,
  payment?: Payment
): string => {
  const translationKey =
    blueprint.title === 'schedule' && payment?.status === 'scheduled'
      ? 'reschedule'
      : blueprint.title;
  return i18n.t(`Payments:InvoiceAction.${translationKey}`);
};

export const getSinglePaymentActions = async (
  payment: Payment,
  availableScheduleDates: string[]
): Promise<ComputedInvoiceAction[]> => {
  const actions: ComputedInvoiceAction[] = [];
  for (const blueprint of Object.values(paymentActionBlueprints)) {
    if (!(await blueprint.isAvailableSingle(payment))) {
      continue;
    }
    const blockedReason =
      blueprint.getBlockedReason && (await blueprint.getBlockedReason(payment));
    if (blockedReason) {
      actions.push({
        id: blueprint.id,
        title: getInvoiceActionTitle(blueprint, payment),
        icon: blueprint.icon,
        execute: null,
        isDisabled: true,
        tooltip: blockedReason,
      });
    } else if (blueprint.id === 'schedule') {
      actions.push({
        id: blueprint.id,
        title: getInvoiceActionTitle(blueprint, payment),
        icon: blueprint.icon,
        subactions: await getScheduleDropdownOptions(
          payment,
          blueprint,
          availableScheduleDates
        ),
      });
    } else {
      actions.push({
        id: blueprint.id,
        title: getInvoiceActionTitle(blueprint, payment),
        icon: blueprint.icon,
        execute: blueprint.prepareActionSingle(payment),
      });
    }
  }
  return actions;
};

export const convertActionsToDropdownOptions = (
  actions: ComputedInvoiceAction[]
): DropdownOption[] =>
  actions.reduce((dropdownOptions, action) => {
    if (action?.subactions?.length > 0) {
      return [
        ...dropdownOptions,
        ...convertActionsToDropdownOptions(action.subactions),
      ];
    }
    return [
      ...dropdownOptions,
      {
        id: String(action.id),
        label: action.title,
        actionPromise: action.execute,
        group: action.group,
        isLabel: action.isLabel,
        isDisabled: action.isDisabled,
        tooltip: action.tooltip,
        icon: action.id === 'upsell' ? action.icon : undefined,
        color:
          action.id === 'upsell' ? 'var(--promo-promopurple-100)' : undefined,
      },
    ];
  }, []);
