import i18n from '../../../i18n/i18n';
import { getReduxSessionUser } from '../../../services/Reducers/User.reducer.helper';
import { tenantHasFeature } from '../../../services/Shortlist/feature-flags';
import { InvoiceContract, Payment } from '../../../types/payments';
import { TASK_STATUSES, Task } from '../../../types/task';
import { Vendor } from '../../../types/vendor';

type PaymentPermissionParams = {
  payment?: Payment;
};

type PaymentCondition = ({ payment }: PaymentPermissionParams) => boolean;

type PermissionPromiseResult = {
  result: boolean;
  reason: string;
};

export const PaymentPermissionService = {
  canSessionVendorEditBuyerPayments: (): boolean =>
    getReduxSessionUser()?.isVendor &&
    tenantHasFeature('vendor_can_edit_buyer_payments'),

  canSessionVendorEditPayment: (payment: Payment): boolean => {
    const conditions: PaymentCondition[] = [
      sessionUserIsVendor,
      onlyPaymentStatuses(['new', 'rejected']),
    ];
    if (PaymentPermissionService.canSessionVendorEditBuyerPayments()) {
      conditions.push(onlyPaymentAssignedToSessionUser);
    } else {
      conditions.push(onlyPaymentCreatedBySessionUser);
    }
    return conditions.every((condition) => condition({ payment }) === true);
  },

  canSessionUserAddPayment: async ({
    vendor,
    task,
    contract,
  }: {
    vendor?: Vendor;
    task?: Task;
    contract?: InvoiceContract;
  } = {}): Promise<PermissionPromiseResult> => {
    if (contract) {
      return {
        result: false,
        reason: 'disabled_for_contract',
      };
    }
    if (vendor) {
      return PaymentPermissionService.canSessionUserAddPaymentForVendorPromise(
        vendor
      );
    }
    if (task) {
      return PaymentPermissionService.canSessionUserAddPaymentForVendorAndTaskPromise(
        task.vendor,
        task
      );
    }
    return PaymentPermissionService.canSessionUserAddPaymentPromise();
  },

  canSessionUserAddPaymentPromise:
    async (): Promise<PermissionPromiseResult> => {
      if (
        getReduxSessionUser().isVendor &&
        tenantHasFeature('payments_partners_cannot_create')
      ) {
        return {
          result: false,
          reason: 'flag:payments_partners_cannot_create',
        };
      }
      return {
        result: true,
        reason: '',
      };
    },

  canSessionUserAddPaymentForVendorPromise: async (
    vendor?: Vendor
  ): Promise<PermissionPromiseResult> => {
    const sessionUser = getReduxSessionUser();
    const res =
      await PaymentPermissionService.canSessionUserAddPaymentPromise();
    if (!res.result) {
      return res;
    } else if (vendor?.status === 'archived') {
      return {
        result: false,
        reason: i18n.t(
          'Payments:Permissions.YouCannotAddPaymentToArchivedPartner'
        ),
      };
    } else if (
      !sessionUser.isVendor &&
      tenantHasFeature('payments_only_for_fully_onboarded')
    ) {
      if (vendor?.onboarded === undefined) {
        throw 'Vendor `onboarded` property is not set.';
      } else if (vendor?.onboarded !== 'fully-onboarded') {
        return {
          result: false,
          reason: i18n.t(
            'Payments:Permissions.OnlyFullyOnboardedPartnersCanReceivePayments'
          ),
        };
      }
    }
    return {
      result: true,
      reason: '',
    };
  },

  canSessionUserAddPaymentForVendorAndTaskPromise: (
    vendor: Vendor,
    task: Task
  ): Promise<PermissionPromiseResult> =>
    PaymentPermissionService.canSessionUserAddPaymentForVendorPromise(
      vendor
    ).then((output) => {
      if (
        ![
          TASK_STATUSES.STATUS_PENDING,
          TASK_STATUSES.STATUS_ACCEPTED,
          TASK_STATUSES.STATUS_COMPLETED,
          TASK_STATUSES.STATUS_REJECTED,
          TASK_STATUSES.STATUS_UNDER_REVIEW,
        ].includes(task.status)
      ) {
        return {
          result: false,
          reason: i18n.t(
            'Payments:Permissions.TaskMustBeInOneOfTheFollowingStatuses'
          ),
        };
      }
      return output;
    }),

  canSessionVendorDeletePayment: (payment: Payment): boolean =>
    PaymentPermissionService.canSessionVendorEditPayment(payment),
};

// conditions
const sessionUserIsVendor: PaymentCondition = () =>
  getReduxSessionUser().isVendor;

const onlyPaymentStatuses: (validStatuses: string[]) => PaymentCondition =
  (validStatuses) =>
  ({ payment }) =>
    validStatuses.includes(payment.status);

const onlyPaymentAssignedToSessionUser: PaymentCondition = ({ payment }) => {
  const sessionUser = getReduxSessionUser();
  if (
    sessionUser.isVendor &&
    sessionUser.userData?.vendor?.slug === payment.vendor?.slug
  ) {
    return true;
  }
  return false;
};

const onlyPaymentCreatedBySessionUser: PaymentCondition = ({ payment }) => {
  if (getReduxSessionUser().userData?.slug === payment.created_by?.slug) {
    return true;
  }
  return false;
};
