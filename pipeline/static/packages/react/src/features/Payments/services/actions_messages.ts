import i18n from '../../../i18n/i18n';
import {
  displayFailureMessage,
  displaySuccessMessage,
  displayWarningMessage,
} from '../../../services/Reducers/Notifications.reducer.helper';
import {
  PaymentAction,
  PaymentBulkActionResponse,
} from '../../../types/payments';

const t = (action: PaymentAction, key: string, params = {}) => {
  const translationKey = `PaymentActionResult.${action}.${key}`;
  const translated = i18n.t(`Payments:${translationKey}`, params);
  if (translated === translationKey) {
    // no translation found, use default:
    return i18n.t(`Payments:PaymentActionResult.__default__.${key}`, params);
  }
  return translated;
};

const getModalHelpers = async () =>
  await import('../../../components/Utils/Modals/modal.helpers');

export const displayMessageAfterBulkAction = (
  response: PaymentBulkActionResponse
): void => {
  if (response.result === 'error') {
    displayFailureMessage(
      i18n.t('Payments:PaymentActionResult.FailureBackendUnknown')
    );
  }
  if (response.action === 'reject') {
    // Reject messages/modals are handled at the moment within RejectPaymentModal.
    // Would be nice to move them here?
    return undefined;
  }
  if (response.result !== 'success') {
    return undefined;
  }
  if (!response?.payload) {
    // Case when backend responds with something weird.
    // Shouldn't happen, but...
    displayWarningMessage(
      i18n.t('Payments:PaymentActionResult.SuccessWithBadAPIResponseBody')
    );
    return undefined;
  }
  const { successes, errors } = response.payload;
  if (response.action === 'approve' && errors.length > 0) {
    getModalHelpers().then(({ openModal }) => {
      openModal({
        type: 'payment_approve_error_modal',
        errors: response.payload.errors,
        actionId: response.payload.id,
      });
    });
  } else if (successes.length === 0) {
    displayFailureMessage(
      t(response.action, 'FailureMany', {
        count: errors.length,
        error: errors.length,
      })
    );
  } else if (errors.length > 0) {
    displayWarningMessage(
      t(response.action, 'PartialSuccessMany', {
        affected: successes.length,
        count: successes.length,
        error: errors.length,
      })
    );
  } else {
    displaySuccessMessage(
      t(response.action, 'SuccessMany', {
        affected: successes.length,
        count: successes.length,
      })
    );
  }
};
