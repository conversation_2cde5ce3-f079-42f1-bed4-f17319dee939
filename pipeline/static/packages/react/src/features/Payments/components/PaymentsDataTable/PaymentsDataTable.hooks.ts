import { useCallback, useEffect } from 'react';
import { create } from 'zustand';
import { DataTableResultsUpdate } from '../../../../components/DataTable/DataTable.types';
import { AllColumnTypes } from '../../../../components/Utils/List/Column/Column';
import {
  closeModal,
  openModal,
} from '../../../../components/Utils/Modals/modal.helpers';
import Api from '../../../../services/Api';
import { SearchByFiltersParams } from '../../../../services/Api/Payments';
import { reduxStore } from '../../../../services/Redux';
import { tenantHasFeature } from '../../../../services/Shortlist/feature-flags';
import { InvoiceContract, Payment } from '../../../../types/payments';
import { Task } from '../../../../types/task';
import { Vendor } from '../../../../types/vendor';
import { PaymentsDataTableUpdate } from '../../domain/Payment';
import { PaymentPermissionService } from '../../services/PaymentPermissionService';
import { hasPaymentsProcessingEnabled } from '../../services/processing';
import { getAvailableScheduleDates } from '../../services/scheduling';

const mapAngularUpdatesToDataTableResultsUpdate = async (
  updates: PaymentsDataTableUpdate[]
): Promise<DataTableResultsUpdate[]> => {
  if (!Array.isArray(updates) || updates.length === 0) {
    return [];
  }
  let [added, updated, deleted] = updates.reduce(
    (aggr, cur) => {
      const [what, paymentId] = cur;
      if (what === 'added') {
        aggr[0].push(paymentId);
      } else if (what === 'updated') {
        aggr[1].push(paymentId);
      } else if (what === 'deleted') {
        aggr[2].push(paymentId);
      }
      return aggr;
    },
    [[], [], []]
  );
  // removed duplicates
  added = Array.from(new Set(added));
  updated = Array.from(new Set(updated));
  deleted = Array.from(new Set(deleted));
  // remove deleted from updated as there's no needed to fetch those
  updated = updated.filter((pId) => deleted.indexOf(pId) === -1);
  return Promise.all([
    added.length ? Api.Payments.getByIds(added) : [],
    updated.length ? Api.Payments.getByIds(updated) : [],
    deleted.map((pId) => ({ id: pId })),
  ]).then(([added, updated, deleted]) => {
    if (added.length > 0) {
      return [{ type: 'refresh_table' as const, item: null }];
    }
    return [
      ...updated.map((p) => ({ type: 'update' as const, item: p })),
      ...deleted.map((p) => ({ type: 'delete' as const, item: p })),
    ];
  });
};

const closePaymentDetailsModal = async () => {
  const modalInstance = reduxStore.getState().angularMigration.modalInstance;
  if (!modalInstance) {
    return;
  }
  modalInstance.close();
  // wait for modal to be fully closed...
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(null);
    }, 500);
  });
};

type ReducerState = {
  renderCounter?: number;
  renderError?: null | 'loading_error';
  permissions?: { canAdd: boolean };
  openedPaymentId?: null | number | string;
  visibleColumns?: AllColumnTypes[];
  searchResults?: { items: Payment[]; total: number };
};

type ReducerActions =
  | { type: 'loading' }
  | { type: 'loaded'; payload: ReducerState }
  | { type: 'detailsOpen'; payload: { openedPaymentId: number | string } }
  | { type: 'detailsClose' }
  | { type: 'rerender' };

export type PaymentsDataTableStateProps = {
  listingState: ReducerState;
  onAddInvoice: () => void;
  reloadState: () => void;
};

const reducer = (state: ReducerState, action: ReducerActions) => {
  switch (action.type) {
    case 'loading':
      return { ...state, renderCounter: 0, renderError: null };
    case 'loaded':
      return { ...state, ...action.payload };
    case 'rerender':
      return { ...state, renderCounter: state.renderCounter + 1 };
    case 'detailsOpen':
      return { ...state, openedPaymentId: action.payload.openedPaymentId };
    case 'detailsClose':
      return { ...state, openedPaymentId: null };
    default:
      throw new Error();
  }
};

const initialState = {
  renderCounter: 0,
  renderError: null,
  permissions: { canAdd: false },
  openedPaymentId: null,
  visibleColumns: [],
};

export const usePaymentsDataTableStore = create<{
  state: ReducerState;
  dispatch: (action: ReducerActions) => void;
}>((set) => ({
  state: initialState,
  dispatch: (action: ReducerActions) =>
    set(({ state }) => ({ state: reducer(state, action) })),
}));

export const setInitialState = () => {
  usePaymentsDataTableStore.setState({ state: initialState });
};

export const useInvoiceDetailsModal = () => {
  const { state, dispatch } = usePaymentsDataTableStore((state) => state);

  const openDetailModal = useCallback(
    async (paymentId: number | string): Promise<DataTableResultsUpdate[]> => {
      if (paymentId === state.openedPaymentId) {
        return;
      }
      dispatch({
        type: 'detailsOpen',
        payload: { openedPaymentId: paymentId },
      });
      await closePaymentDetailsModal();
      if (!tenantHasFeature('payments_new_invoice_details')) {
        return await reduxStore
          .getState()
          .angularMigration.actions.payments['PAYMENT_DETAIL_MODAL'](paymentId)
          .then(mapAngularUpdatesToDataTableResultsUpdate)
          .catch(mapAngularUpdatesToDataTableResultsUpdate)
          .finally(() => dispatch({ type: 'detailsClose' }));
      } else {
        return openModal(
          {
            type: 'invoice_details_modal',
            hideClose: true,
            disableScrollLock: true,
            // temporary fix until adding invoice modal is in angular PAY-1893
            disableEnforceFocus: true,
            style: {
              zIndex: 1040,
            },
          },
          undefined,
          'sidepanel-nomargins'
        )
          .then(mapAngularUpdatesToDataTableResultsUpdate)
          .catch(mapAngularUpdatesToDataTableResultsUpdate)
          .finally(() => dispatch({ type: 'detailsClose' }));
      }
    },
    [state.renderCounter, state.openedPaymentId]
  );

  const closeDetailModal = useCallback(
    (onlyLegacy?: boolean) => {
      if (
        !state.openedPaymentId ||
        (tenantHasFeature('payments_new_invoice_details') && onlyLegacy)
      ) {
        return;
      }
      closeModal('invoice_details_modal');
      dispatch({ type: 'detailsClose' });
      return closePaymentDetailsModal();
    },
    [state.renderCounter, state.openedPaymentId]
  );

  return {
    openDetailModal,
    closeDetailModal,
  };
};

export const usePaymentsDataTableState = ({
  getColumns,
  vendor,
  task,
  contract,
  limit = 5,
  runFetchingInvoices = false,
  loadInvoices = true,
}: {
  getColumns: ({
    availableScheduleDates,
    paymentsProcessingEnabled,
    taskId,
    vendorId,
    contractId,
  }: {
    availableScheduleDates?: string[];
    paymentsProcessingEnabled?: boolean;
    taskId?: number;
    vendorId?: number;
    contractId?: number;
  }) => any;
  vendor?: Vendor;
  task?: Task;
  contract?: InvoiceContract;
  limit?: number;
  runFetchingInvoices?: boolean;
  loadInvoices?: boolean;
}): PaymentsDataTableStateProps => {
  const { state, dispatch } = usePaymentsDataTableStore((state) => state);
  const getItems = useCallback(async () => {
    const searchParams: SearchByFiltersParams = {
      page: 0,
      size: limit,
    };
    if (task?.id) {
      searchParams.related_task = [task.id];
    }
    if (contract?.id) {
      searchParams.contract = [contract.id];
    }
    return Api.Payments.searchByFilters(searchParams).then((result) => ({
      items: result.hits,
      total: result.total,
    }));
  }, [task, contract, limit]);

  const loadState = useCallback(() => {
    dispatch({ type: 'loading' });
    const getItemsPromise = runFetchingInvoices
      ? getItems()
      : Promise.resolve(undefined);
    Promise.all([
      getAvailableScheduleDates(),
      hasPaymentsProcessingEnabled(),
      PaymentPermissionService.canSessionUserAddPayment({
        vendor,
        task,
        contract,
      }),
      getItemsPromise,
    ])
      .then(([scheduleDates, processingEnabled, canAdd, searchResults]) => {
        dispatch({
          type: 'loaded',
          payload: {
            permissions: { canAdd: canAdd.result },
            renderCounter: 1,
            visibleColumns: getColumns({
              availableScheduleDates: scheduleDates,
              paymentsProcessingEnabled: processingEnabled,
              taskId: task?.id,
              vendorId: vendor?.id,
              contractId: contract?.id,
            }),
            searchResults,
          },
        });
      })
      .catch(() => {
        dispatch({
          type: 'loaded',
          payload: {
            renderError: 'loading_error',
          },
        });
      });
  }, [vendor, task, contract, runFetchingInvoices, getItems]);

  useEffect(() => {
    if (loadInvoices) {
      loadState();

      return dispatch({ type: 'loading' });
    }
  }, [loadInvoices]);

  const reloadState = useCallback(() => {
    if (runFetchingInvoices) {
      loadState();
    } else {
      // As the control over items loaded from api in DataTable takes place in ElasticSearch,
      // it's a dirty hack to reload the whole DataTable.
      dispatch({ type: 'rerender' });
    }
  }, [state.renderCounter, runFetchingInvoices, loadState]);

  const onAddInvoice = useCallback(() => {
    if (!state.permissions.canAdd) {
      return;
    }
    return reduxStore
      .getState()
      .angularMigration.actions.payments['PAYMENT_ADD_MODAL'](
        vendor,
        null,
        task
      )
      .then((payment) => {
        if (payment) {
          reloadState();
        }
      });
  }, [state.permissions.canAdd]);

  return {
    listingState: state,
    onAddInvoice,
    reloadState,
  };
};
