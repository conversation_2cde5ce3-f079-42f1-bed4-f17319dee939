import Tooltip from '../../../../../components/Utils/Tooltip/Tooltip';
import { formatCurrency } from '../../../../../utils/index';
import styled from '@emotion/styled';
import React, { Fragment } from 'react';

const OverflowTooltipStyles = styled('div')({
  height: '40px',
  position: 'relative',
  lineHeight: '40px',
  flexShrink: 0,
  cursor: 'pointer',
});

const MoreCurrencyListContainer = styled('div')({
  display: 'grid',
  gridTemplateColumns: 'auto max-content',
  '& span': {
    fontWeight: 700,
    marginRight: '3px',
    textAlign: 'right',
  },
});

const MoreCurrencyList = ({ payments }) => {
  return (
    <MoreCurrencyListContainer>
      {payments.map((payment) => {
        return (
          <Fragment key={payment.currency}>
            <span>{formatCurrency(payment.total_amount)}</span>
            {` ${payment.currency} (${payment.count})`}
          </Fragment>
        );
      })}
    </MoreCurrencyListContainer>
  );
};

const OverflowTooltip = ({ children, rests, testId }) => {
  return (
    <Tooltip
      placement="left"
      id={testId ? `${testId}_Tooltip` : ''}
      tooltip={<MoreCurrencyList payments={rests} />}
    >
      <OverflowTooltipStyles data-testid={testId}>
        {children}
      </OverflowTooltipStyles>
    </Tooltip>
  );
};

export default OverflowTooltip;
