import { useEffect } from 'react';
import ToastMessage from '../../../../components/Layout/ToastMessage/ToastMessage';
import {
  closeModal,
  openModal,
} from '../../../../components/Utils/Modals/modal.helpers';
import categories from '../../../../fixtures/api/invoices/__id__/categories.json';
import invoice from '../../../../fixtures/api/invoices/__id__/invoice.json';
import tax_rates from '../../../../fixtures/api/invoices/__id__/tax_rates.json';
import vendorResponse from '../../../../fixtures/api/vendors/__slug__/vendor1.json';
import vendorsResponse from '../../../../fixtures/api/vendors/vendors.json';
import { reduxStore } from '../../../../services/Redux';
import { jestCreateSessionUser } from '../../../../utils/jest/jest.session';
import { getApiMockAdapter } from '../../../../utils/storybook-utils';
import entities from '../../../Entities/fixtures/entities.json';
import { generateInvoice } from '../../fixtures/payments';
import { getFuturePayruns } from '../../fixtures/payruns';
import { usePaymentsDataTableStore } from '../PaymentsDataTable/PaymentsDataTable.hooks';
import { getUsersFromAssignedBuyers } from '../PaymentsDataTable/PaymentsDataTable.stories.helpers';

export default {
  title: 'Features/Payments/Components/InvoiceDetailsSidePanel',
};

const line_items = invoice.items;
const worker = vendorsResponse.hits[1];

const Template = ({ invoiceId, updateProps, vendor = vendorResponse }) => {
  useEffect(() => {
    const mock = getApiMockAdapter();
    reduxStore.dispatch({
      type: 'SET_TENANT',
      tenant: {
        features: [
          'payments_assigned_buyer',
          'projects_and_tasks',
          'payments_new_invoice_details',
        ],
      },
    });
    reduxStore.dispatch({
      type: 'SET_USER',
      data: updateProps?.worker
        ? jestCreateSessionUser('vendor', {
            id: 1,
            vendor: { is_supplier: true },
          })
        : vendor.is_worker
          ? jestCreateSessionUser('vendor', {
              id: 1,
              vendor,
            })
          : jestCreateSessionUser('buyer-admin', { id: 1 }),
    });
    const invoice = generateInvoice(invoiceId, updateProps);
    mock.onGet(/\/api\/invoices\//gm).reply(200, invoice);
    mock.onGet(/\/api\/entities\//gm).reply(200, entities);
    mock.onPost(/\/api\/payments\/v2\/actions\//gm).reply(() => [
      200,
      {
        successes: [
          {
            invoice_id: invoiceId,
          },
        ],
        errors: [],
      },
    ]);
    mock
      .onPost(/\/api\/users\/list_users\//gm)
      .reply(200, getUsersFromAssignedBuyers([invoice]));
    mock
      .onGet(/\/api\/payments\/v2\/future_payruns\//gm)
      .reply(200, getFuturePayruns());
    mock
      .onGet(/\/api\/payments\/line_items_categories\//gm)
      .reply(200, categories);
    mock.onGet(/\/api\/payments\/tax_rates\//gm).reply(200, tax_rates);
    mock.onGet(/\/api\/vendors\/(.+)\//gm).reply(200, vendor);
    mock.onGet(/\/api\/s\/vendors\/(.+)\//gm).reply(200, worker);

    usePaymentsDataTableStore.setState({
      state: {
        openedPaymentId: invoiceId,
      },
    });
    openModal(
      {
        type: 'invoice_details_modal',
        hideClose: true,
      },
      undefined,
      'sidepanel-nomargins'
    )
      .then(console.log)
      .catch(console.log);
    return () => {
      reduxStore.dispatch({
        type: 'SET_USER',
        data: null,
      });
      usePaymentsDataTableStore.setState({
        state: {
          openedPaymentId: null,
        },
      });
      closeModal('invoice_details_modal');
    };
  }, []);

  return <ToastMessage />;
};

export const NewInvoice = Template.bind({});
NewInvoice.args = {
  invoiceId: 1,
  updateProps: {
    line_items: [
      {
        ...line_items[0],
        custom_fields: [
          {
            id: 1,
            label: 'Task ID',
            value: 'TASK-123',
            type: 'text_line',
          },
          {
            id: 2,
            label: 'Billable Hours',
            value: 40,
            type: 'number',
          },
        ],
      },
      {
        ...line_items[1],
        custom_fields: [
          {
            id: 3,
            label: 'Project Phase',
            value: 'Development',
            type: 'text_line',
          },
          {
            id: 4,
            label: 'Is Overtime',
            value: true,
            type: 'boolean',
          },
        ],
      },
      line_items[2],
    ],
    total_amount: 7500,
    total_amount_without_tax: 6097.56,
    total_tax: 1402.44,
    custom_fields: [
      {
        id: 5,
        label: 'Project Code',
        value: 'PRJ-2024-001',
        type: 'text_line',
      },
      {
        id: 6,
        label: 'Is Urgent',
        value: true,
        type: 'boolean',
      },
    ],
  },
};

export const PaidInvoice = Template.bind({});
PaidInvoice.args = {
  invoiceId: 2,
  updateProps: {
    status: 'paid',
    paid_date: '2025-05-06',
    line_items: [
      {
        ...line_items[0],
        custom_fields: [
          {
            id: 1,
            label: 'Cost Center',
            value: 'CC-789',
            type: 'text_line',
          },
          {
            id: 2,
            label: 'Department',
            value: ['Engineering', 'R&D'],
            type: 'select',
          },
        ],
      },
      {
        ...line_items[1],
        custom_fields: [
          {
            id: 3,
            label: 'Equipment ID',
            value: 'EQ-456',
            type: 'text_line',
          },
          {
            id: 4,
            label: 'Warranty Period',
            value: 24,
            type: 'number',
          },
        ],
      },
      line_items[2],
    ],
    total_amount: 4500,
    total_amount_without_tax: 3719.01,
    total_tax: 780.99,
    custom_fields: [
      {
        id: 5,
        label: 'Project Code',
        value: 'PRJ-2024-002',
        type: 'text_line',
      },
      {
        id: 6,
        label: 'Approved By',
        value: ['John Doe', 'Jane Smith'],
        type: 'select',
      },
      {
        id: 7,
        label: 'Budget Code',
        value: 12345,
        type: 'number',
      },
    ],
  },
};

export const RejectedInvoice = Template.bind({});
RejectedInvoice.args = {
  invoiceId: 3,
  updateProps: {
    status: 'rejected',
    reason: 'Invalid invoice details',
    line_items,
    total_amount: 8000,
    total_amount_without_tax: 6666.67,
    total_tax: 1333.33,
    custom_fields: [
      {
        id: 1,
        label: 'Project Code',
        value: 'PRJ-2024-003',
        type: 'text_line',
      },
      {
        id: 2,
        label: 'Is International',
        value: false,
        type: 'boolean',
      },
      {
        id: 3,
        label: 'Cost Centers',
        value: ['CC-456', 'CC-789'],
        type: 'select',
      },
      {
        id: 4,
        label: 'Priority Level',
        value: 1,
        type: 'number',
      },
    ],
  },
};

export const ScheduledInvoice = Template.bind({});
ScheduledInvoice.args = {
  invoiceId: 4,
  updateProps: {
    status: 'scheduled',
    scheduled_for: '2025-05-06',
    line_items,
    total_amount: 6000,
    total_amount_without_tax: 4878.05,
    total_tax: 1121.95,
  },
};

export const ProcessingInvoice = Template.bind({});
ProcessingInvoice.args = {
  invoiceId: 5,
  updateProps: {
    status: 'processing',
    mark_as_paid_on_date: '2025-05-06',
    line_items,
    total_amount: 15000,
    total_amount_without_tax: 12195.12,
    total_tax: 2804.88,
  },
};

export const InFlightInvoice = Template.bind({});
InFlightInvoice.args = {
  invoiceId: 6,
  updateProps: {
    status: 'in_flight',
    mark_as_paid_on_date: '2025-05-06',
    line_items,
    total_amount: 10000,
    total_amount_without_tax: 8264.46,
    total_tax: 1735.54,
  },
};

export const ApprovedInvoice = Template.bind({});
ApprovedInvoice.args = {
  invoiceId: 8,
  updateProps: {
    status: 'approved',
    approved_date: '2025-05-06',
    approved_by: 'John Smith',
    line_items,
    total_amount: 12000,
    total_amount_without_tax: 9756.1,
    total_tax: 2243.9,
    custom_fields: [
      {
        id: 1,
        label: 'Project Code',
        value: 'PRJ-2024-004',
        type: 'text_line',
      },
      {
        id: 2,
        label: 'Is International',
        value: true,
        type: 'boolean',
      },
      {
        id: 3,
        label: 'Approval Chain',
        value: ['Manager', 'Director', 'VP'],
        type: 'select',
      },
      {
        id: 4,
        label: 'Risk Level',
        value: 2,
        type: 'number',
      },
      {
        id: 5,
        label: 'Requires Additional Review',
        value: false,
        type: 'boolean',
      },
    ],
  },
};

export const InvoiceWithProcessingProblems = Template.bind({});
InvoiceWithProcessingProblems.args = {
  invoiceId: 7,
  updateProps: {
    status: 'scheduled',
    processing_problem_reasons: ['not_payable_bank_details'],
    vendor: {
      label: 'Test Vendor',
    },
    line_items,
    total: 1179.38,
    currency: 'YEN',
    total_deductions: 168.39,
    entity: {
      id: 1,
    },
  },
};

export const SessionUserIsWorker = Template.bind({});
SessionUserIsWorker.args = {
  invoiceId: 8,
  vendor: worker,
};

export const SessionUserIsSupplier = Template.bind({});
SessionUserIsSupplier.args = {
  invoiceId: 9,
  updateProps: {
    worker,
  },
};
