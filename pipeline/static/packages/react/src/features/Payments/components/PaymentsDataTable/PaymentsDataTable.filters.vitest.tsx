import {
  getAllowedFilters,
  scheduledForSortValuesFn,
  statusSortValuesFn,
} from './PaymentsDataTable.filters';

describe('PaymentsDataTable.filters.statusSortValuesFn', () => {
  test('no statuses', async () => {
    expect(Object.entries({}).sort(statusSortValuesFn)).toStrictEqual([]);
  });

  test('one status', async () => {
    expect(Object.entries({ new: 123 }).sort(statusSortValuesFn)).toStrictEqual(
      [['new', 123]]
    );
  });

  test('basic statuses', async () => {
    expect(
      Object.entries({
        paid: 2,
        new: 123,
        approved: 321,
        rejected: 3,
      }).sort(statusSortValuesFn)
    ).toStrictEqual([
      ['new', 123],
      ['approved', 321],
      ['paid', 2],
      ['rejected', 3],
    ]);
  });

  test('all statuses', async () => {
    expect(
      Object.entries({
        paid: 2,
        new: 123,
        approved: 321,
        rejected: 3,
        in_flight: 9,
        scheduled: 5,
        processing: 1,
      }).sort(statusSortValuesFn)
    ).toStrictEqual([
      ['new', 123],
      ['approved', 321],
      ['scheduled', 5],
      ['processing', 1],
      ['in_flight', 9],
      ['paid', 2],
      ['rejected', 3],
    ]);
  });

  test('unknown status should be placed at the end', async () => {
    const filterValues = {
      paid: 2,
      new: 123,
      approved: 321,
      rejected: 3,
      in_flight: 9,
      scheduled: 5,
      processing: 1,
      unknown_1: 22,
      archived: 33,
    };
    const expected = [
      ['new', 123],
      ['approved', 321],
      ['scheduled', 5],
      ['processing', 1],
      ['in_flight', 9],
      ['paid', 2],
      ['rejected', 3],
      ['archived', 33],
      ['unknown_1', 22],
    ];
    expect(Object.entries(filterValues).sort(statusSortValuesFn)).toStrictEqual(
      expected
    );
  });
});

describe('PaymentsDataTable.filters.scheduledForSortValuesFn', () => {
  test('zero', async () => {
    expect(Object.entries({}).sort(scheduledForSortValuesFn)).toStrictEqual([]);
  });

  test('one', async () => {
    expect(
      Object.entries({ ['2023-03-30T12:00:00Z']: 1 }).sort(
        scheduledForSortValuesFn
      )
    ).toStrictEqual([['2023-03-30T12:00:00Z', 1]]);
  });

  test('multiple', async () => {
    expect(
      Object.entries({
        ['2023-03-01T12:00:00Z']: 7,
        ['2023-01-01T12:00:00Z']: 3,
        ['2023-05-01T12:00:00Z']: 2,
        ['2023-02-01T12:00:00Z']: 3,
        ['2023-07-01T12:00:00Z']: 9,
        ['bad date']: 20,
        ['empty']: 23,
        ['2023-04-01T12:00:00Z']: 5,
        ['2023-06-01T12:00:00Z']: 1,
      }).sort(scheduledForSortValuesFn)
    ).toStrictEqual([
      ['2023-07-01T12:00:00Z', 9],
      ['2023-06-01T12:00:00Z', 1],
      ['2023-05-01T12:00:00Z', 2],
      ['2023-04-01T12:00:00Z', 5],
      ['2023-03-01T12:00:00Z', 7],
      ['2023-02-01T12:00:00Z', 3],
      ['2023-01-01T12:00:00Z', 3],
      ['bad date', 20],
      ['empty', 23],
    ]);
  });
});

describe('PaymentsDataTable.filters.getAllowedFilters', () => {
  test('basic', async () => {
    const backendFilters = {
      status: {
        new: null,
        approved: null,
      },
      payout_method_type: {
        bank_transfer: 1,
        payoneer: 2,
      },
      scheduled_for: {
        '2024-01-01': 1,
        '2024-01-02': 2,
      },
      currency: {
        USD: null,
        PLN: null,
      },
    };
    const expectedFiltersOrdered = {
      status: {
        new: null,
        approved: null,
      },
      currency: {
        USD: null,
        PLN: null,
      },
      scheduled_for: {
        '2024-01-01': 1,
        '2024-01-02': 2,
      },
      payout_method_type: {
        bank_transfer: 1,
        payoneer: 2,
      },
    };
    expect(getAllowedFilters(backendFilters)).toEqual(expectedFiltersOrdered);
  });

  test('currency and payout_method_type not returned - single values from backend', async () => {
    const backendFilters = {
      status: {
        new: null,
        approved: null,
      },
      currency: {
        USD: 50,
      },
      payout_method_type: {
        bank_transfer: 100,
      },
    };
    const expectedFilters = {
      status: {
        new: null,
        approved: null,
      },
    };
    expect(getAllowedFilters(backendFilters)).toStrictEqual(expectedFilters);
  });

  test('backend with extra filters', async () => {
    const backendFilters = {
      status: {
        new: null,
        approved: null,
      },
      currency: {
        USD: null,
        PLN: null,
      },
      extra_1: {
        option_1: 123,
        option_2: 321,
      },
      extra_2: {
        option_1: 123,
        option_2: 321,
      },
    };
    const expected = {
      status: {
        new: null,
        approved: null,
      },
      currency: {
        USD: null,
        PLN: null,
      },
    };
    expect(getAllowedFilters(backendFilters)).toStrictEqual(expected);
  });
});
