import { useRouterStateHasChanged } from '@Worksuite/Features/Core/hooks/useRouterStateHasChanged';
import { UpsellBanner } from '@Worksuite/Features/Upsell';
import styled from '@emotion/styled';
import { useCallback, useEffect, useState } from 'react';
import ActionLogHistory from '../../../../components/ActionLogHistory/ActionLogHistory';
import HorizontalNavigation from '../../../../components/Utils/HorizontalNavigation/HorizontalNavigation';
import { withModal } from '../../../../components/Utils/Modals/modal.hooks';
import { ShortlistModal } from '../../../../components/Utils/Modals/modal.types';
import Spinner from '../../../../components/Utils/Spinner/Spinner';
import i18n from '../../../../i18n/i18n';
import Invoices from '../../../../services/Api/Invoices';
import { getReduxSessionUser } from '../../../../services/Reducers/User.reducer.helper';
import { withRedux } from '../../../../services/Redux';
import { Invoice } from '../../../../types/invoices';
import { useAsync } from '../../../../utils/hooks/useAsync';
import { generateTestId } from '../../../../utils/test.utils';
import { PaymentsDataTableUpdate } from '../../domain/Payment';
import InvoiceActions from '../InvoiceActions/InvoiceActions';
import { InvoiceDetails } from '../InvoiceDetails/InvoiceDetails';
import { InvoiceDetailsHeader } from '../InvoiceDetailsHeader/InvoiceDetailsHeader';
import { usePaymentsDataTableStore } from '../PaymentsDataTable/PaymentsDataTable.hooks';

export const Content = styled('div')({
  padding: 'var(--spacing-m) var(--spacing-xl)',
  display: 'flex',
  flexDirection: 'column',
  gap: 'var(--spacing-m)',
});

export const Header = styled('div')({
  display: 'flex',
  flexDirection: 'column',
  gap: 'var(--spacing-m)',
  padding: 'var(--spacing-xl) var(--spacing-xl) 0 var(--spacing-xl)',
});

export const Footer = styled('div')({
  borderTop: '1px solid var(--borders-encapsulation-frame-border)',
  padding: 'var(--spacing-xl)',
});

const t = (key: string, params?: { [key: string]: string }) =>
  i18n.t(`Payments:InvoiceDetailsSidePanel.${key}`, params);

export interface InvoiceDetailsModal extends ShortlistModal {
  type: 'invoice_details_modal';
}

const InvoiceDetailsSidePanelHeader = ({
  hasInvoice,
  invoice,
  activeTab,
  setActiveTab,
  onClose,
}) => {
  const currentUser = getReduxSessionUser();
  const menuItems = [
    {
      id: '0',
      label: t('TabDetails'),
      execute: async () => setActiveTab('0'),
    },
    {
      id: '1',
      label: t('TabAuditTrail'),
      execute: async () => setActiveTab('1'),
    },
  ];
  return (
    <Header>
      {hasInvoice && (
        <InvoiceDetailsHeader
          key={invoice.id}
          invoice={invoice}
          onClose={onClose}
        />
      )}
      {currentUser.isUser && (
        <HorizontalNavigation items={menuItems} activeItem={activeTab} />
      )}
    </Header>
  );
};

const InvoiceDetailsSidePanelContent = ({
  modalClose,
  modalDismiss,
  setHeaderData,
}: InvoiceDetailsModal) => {
  const {
    state: { openedPaymentId: invoiceId },
  } = usePaymentsDataTableStore((state) => state);
  useRouterStateHasChanged(modalDismiss);

  const [activeTab, setActiveTab] = useState('0');
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [events, setEvents] = useState<PaymentsDataTableUpdate[]>([]);
  const { isLoading: isInvoiceLoading } = useAsync(
    {
      promise: () => Invoices.getById(invoiceId),
      onResolve: (result) => setInvoice(result),
      skip: !invoiceId,
    },
    [invoiceId]
  );
  const hasInvoice = invoice && !isInvoiceLoading;

  const onClose = (newEvents?: PaymentsDataTableUpdate[]) => () => {
    modalClose(newEvents);
  };

  useEffect(() => {
    setHeaderData({
      invoiceDetailsSidePanelHeader: {
        hasInvoice,
        invoice,
        activeTab,
        setActiveTab,
        onClose: onClose(events),
      },
    });
  }, [activeTab, hasInvoice, invoice, events]);

  const handleActionExecuted = useCallback(
    async (actionResult) => {
      if (actionResult?.result !== 'canceled') {
        const eventsPerAction = {
          delete: ['deleted', invoiceId],
          add_similar: ['added', invoiceId],
        };
        const event = eventsPerAction[actionResult.action] ?? [
          'updated',
          invoiceId,
        ];

        setEvents((previousEvents) => {
          const newEvents = [...previousEvents, event];

          if (event[0] !== 'updated') {
            onClose(newEvents)();
          } else if (event[0] !== 'deleted') {
            setInvoice((value) => ({ ...value, ...actionResult.result }));
          }

          return newEvents;
        });
      }
    },
    [invoiceId]
  );

  return (
    <div {...generateTestId('InvoiceDetailsSidePanel')}>
      <Content>
        {isInvoiceLoading ? (
          <Spinner />
        ) : (
          <>
            {activeTab === '0'
              ? hasInvoice && <InvoiceDetails invoice={invoice} />
              : hasInvoice && (
                  <ActionLogHistory
                    contentType="documents.payment"
                    objectId={invoice.id}
                  />
                )}

            <UpsellBanner variant="payout" />
          </>
        )}
      </Content>
      {hasInvoice && (
        <Footer>
          <InvoiceActions invoice={invoice} onExecuted={handleActionExecuted} />
        </Footer>
      )}
    </div>
  );
};

export default withRedux(
  withModal(InvoiceDetailsSidePanelContent, {
    getHeader: ({ headerData: { invoiceDetailsSidePanelHeader } = {} }) => {
      const { hasInvoice, invoice, activeTab, setActiveTab, onClose } =
        invoiceDetailsSidePanelHeader ?? {};

      return (
        <InvoiceDetailsSidePanelHeader
          hasInvoice={hasInvoice}
          invoice={invoice}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          onClose={onClose}
        />
      );
    },
  })
);
