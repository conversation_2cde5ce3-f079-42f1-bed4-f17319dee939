import InvoiceId, {
  KeyValue,
} from '@Worksuite/Features/Payments/components/InvoiceId/InvoiceId';
import PaymentStatus from '@Worksuite/Features/Payments/components/PaymentStatus/PaymentStatus';
import { Box, Grid } from '@mui/material';
import PropTypes from 'prop-types';
import i18n from '../../../../i18n/i18n';
import { HeaderH3 } from '../../../../styles/typography';
import { Payment } from '../../../../types/payments';
import { formatDateTimeWithUserSettings } from '../../../../utils/date';
import { generateTestId } from '../../../../utils/test.utils';

const t = (column: string) =>
  i18n.t(`Payments:ListingCommon.Columns.${column}`);

const InvoiceDetailsHeader = ({ invoice }: { invoice: Payment }) => (
  <Grid container spacing={2} {...generateTestId('InvoiceDetailsHeader')}>
    <Grid item xs={12} md={8}>
      <>
        <Box mb={2}>
          <HeaderH3 {...generateTestId('Title', 'InvoiceDetailsHeader')}>
            {invoice.number}
          </HeaderH3>
        </Box>
        <Grid container spacing={2} alignItems="center">
          <Grid item {...generateTestId('Status', 'InvoiceDetailsHeader')}>
            <PaymentStatus status={invoice.status} />
          </Grid>
          <Grid item {...generateTestId('CreatedAt', 'InvoiceDetailsHeader')}>
            <KeyValue
              fieldName={t('CreatedAt')}
              fieldValue={formatDateTimeWithUserSettings(invoice.created_at)}
              variant="info"
            />
          </Grid>
        </Grid>
      </>
    </Grid>
    <Grid item xs={12} md={4}>
      <Box display="flex" justifyContent={{ md: 'flex-end', sm: 'flex-start' }}>
        <Box {...generateTestId('InvoiceId', 'InvoiceDetailsHeader')}>
          <InvoiceId invoice={invoice} />
        </Box>
      </Box>
    </Grid>
  </Grid>
);

const InvoiceDetails = ({ invoice }: { invoice: Payment }) => (
  <Box mb={3} {...generateTestId('InvoiceDetails')}>
    <InvoiceDetailsHeader invoice={invoice} />
  </Box>
);

InvoiceDetails.propTypes = {
  invoice: PropTypes.object,
};

export default InvoiceDetails;
