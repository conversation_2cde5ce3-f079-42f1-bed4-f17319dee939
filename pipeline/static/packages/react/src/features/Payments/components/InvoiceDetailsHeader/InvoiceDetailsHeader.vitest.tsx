import { composeStories } from '@storybook/react';
import { render } from '@testing-library/react';
import React from 'react';
import * as stories from './InvoiceDetailsHeader.stories';

const composedStories = composeStories(stories);

describe('InvoiceDetailsHeader', () => {
  test.each(Object.entries(composedStories))(
    'snapshot should match: %s',
    async (_, Story) => {
      const { container } = render(<Story />);
      expect(container).toMatchSnapshot();
    }
  );
});
