import ToastMessage from '../../../../components/Layout/ToastMessage/ToastMessage';
import { generatePayment } from '../../fixtures/payments';
import { getAPIMock } from '../PaymentsDataTable/PaymentsDataTable.stories.helpers';
import InvoiceActions from './InvoiceActions';
import React, { useEffect, useState } from 'react';

export default {
  title: 'Features/Payments/Components/InvoiceActions',
  component: InvoiceActions,
};

const Template = ({ invoice }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  useEffect(() => {
    const mock = getAPIMock([invoice], {});
    setIsLoaded(true);
    return () => mock.reset();
  }, []);
  return isLoaded ? (
    <>
      <InvoiceActions invoice={invoice} />
      <ToastMessage />
    </>
  ) : null;
};

export const Zero = () => {
  return (
    <Template invoice={generatePayment(1, { actions_availability: {} })} />
  );
};

export const Single = () => {
  return (
    <Template
      invoice={generatePayment(1, {
        actions_availability: {
          approve: { slug: 'approve', is_allowed: true },
        },
      })}
    />
  );
};

export const Two = () => {
  return (
    <Template
      invoice={generatePayment(1, {
        actions_availability: {
          approve: { slug: 'approve', is_allowed: true },
          delete: { slug: 'delete', is_allowed: true },
        },
      })}
    />
  );
};

export const Multiple = () => {
  return <Template invoice={generatePayment(1, { status: 'approved' })} />;
};

export const MultipleAllDisabled = () => {
  return (
    <Template
      invoice={generatePayment(1, {
        status: 'approved',
        actions_availability: {
          schedule: {
            slug: 'schedule',
            is_allowed: false,
            reasons: ['vendor_not_payable'],
          },
          reject: {
            slug: 'reject',
            is_allowed: false,
            reasons: ['vendor_not_payable'],
          },
          paid: {
            slug: 'paid',
            is_allowed: false,
            reasons: ['vendor_not_payable'],
          },
          flag_problem: {
            slug: 'flag_problem',
            is_allowed: false,
            reasons: ['vendor_not_payable'],
          },
        },
      })}
    />
  );
};

export const LongLabels = () => {
  return (
    <Template
      invoice={generatePayment(1, {
        status: 'approved',
        actions_availability: {
          download_attachment: {
            slug: 'download_attachment',
            is_allowed: true,
          },
          generate_pdf: {
            slug: 'generate_pdf',
            is_allowed: true,
          },
          delete: {
            slug: 'delete',
            is_allowed: true,
          },
        },
      })}
    />
  );
};

export const RejectPrimaryRed = () => {
  return (
    <Template
      invoice={generatePayment(1, {
        actions_availability: {
          reject: { slug: 'reject', is_allowed: true },
        },
      })}
    />
  );
};

export const RejectSecondaryRed = () => {
  return (
    <Template
      invoice={generatePayment(1, {
        actions_availability: {
          approve: { slug: 'approve', is_allowed: true },
          reject: { slug: 'reject', is_allowed: true },
        },
      })}
    />
  );
};

export const PrimaryActionMultipleChoices = () => {
  return (
    <Template
      invoice={generatePayment(1, {
        actions_availability: {
          schedule: { slug: 'schedule', is_allowed: true },
        },
      })}
    />
  );
};

export const SecondaryActionMultipleChoices = () => {
  return (
    <Template
      invoice={generatePayment(1, {
        actions_availability: {
          approve: { slug: 'approve', is_allowed: true },
          schedule: { slug: 'schedule', is_allowed: true },
        },
      })}
    />
  );
};
