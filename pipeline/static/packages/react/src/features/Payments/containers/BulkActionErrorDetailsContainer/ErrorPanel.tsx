import {
  BodyStyled,
  BulkActionErrorWrapperColumn,
  InvoiceName,
  InvoiceTableCell,
  InvoicesTable,
} from './BulkActionErrorDetailsContainer.styles';
import { Label } from '../../../../styles/typography';
import React from 'react';
import {
  PaymentActionAvailabilityReason,
  PaymentActionOtherError,
  PaymentActionSuccess,
} from '../../../../types/payments';
import i18n from '../../../../i18n/i18n';
import { ErrorPanelType } from './BulkActionErrorDetailsContainer.helpers';

const t = (id: string, params?: object) =>
  i18n.t(`Payments:BulkActionErrorDetailsContainer.${id}`, params);

const isOtherErrorData = (
  data: PaymentActionSuccess | PaymentActionOtherError
): data is PaymentActionOtherError => 'type' in data;

const getInvoicesHeader = (type: ErrorPanelType) => {
  if (type === 'successes') {
    return 'ApprovedInvoices';
  } else if (type === 'other') {
    return 'ErrorDetails';
  }
  return 'InvoicesAffected';
};

const getErrorDesc = (errorType: PaymentActionAvailabilityReason) => (
  <div>
    {t(`Error`, {
      errorDesc: i18n.t(`Payments:PaymentActionBlockedReasons.${errorType}`),
    })}
  </div>
);

const ErrorPanel = ({
  type,
  invoiceDetails,
}: {
  type: ErrorPanelType;
  invoiceDetails: PaymentActionSuccess[] | PaymentActionOtherError[];
}) => (
  <BulkActionErrorWrapperColumn gapSize="xs">
    <Label>{t(getInvoicesHeader(type))}:</Label>
    <InvoicesTable>
      <tbody>
        {invoiceDetails.length > 0 &&
          invoiceDetails.map(
            (invoiceRow: PaymentActionSuccess | PaymentActionOtherError) => (
              <tr key={`row_${type}_${invoiceRow.invoice_id}`}>
                <InvoiceTableCell>
                  <BodyStyled>{invoiceRow.invoice_id}</BodyStyled>
                </InvoiceTableCell>
                <InvoiceTableCell>
                  <InvoiceName>
                    {invoiceRow.invoice_title}
                    {type === 'other' &&
                      isOtherErrorData(invoiceRow) &&
                      getErrorDesc(invoiceRow.type)}
                  </InvoiceName>
                </InvoiceTableCell>
              </tr>
            )
          )}
      </tbody>
    </InvoicesTable>
  </BulkActionErrorWrapperColumn>
);

export default ErrorPanel;
