import { getPayrunTotalAmountData } from '../Payruns.helpers';
import * as stories from '../components/PayrunDetails/PayrunDetails.stories';
import { generatePayruns } from '../fixtures/payruns';
import { composeStories } from '@storybook/react';
import '@testing-library/jest-dom';
import { render } from '@testing-library/react';
import React from 'react';

test('should render default story', async () => {
  const { Default } = composeStories(stories);
  const { container } = render(<Default />);
  expect(container).toMatchSnapshot();
});

describe('Payruns helpers', () => {
  const expected = [
    {
      mainPaymentsGroup: {
        count: 1,
        currency: 'CAD',
        paid_day: '2022-09-10T00:00:00.000Z',
        sum: 10000000,
      },
      amountOfOtherPayments: 0,
    },
    {
      mainPaymentsGroup: {
        count: 1,
        currency: 'CAD',
        paid_day: '2022-09-09T00:00:00.000Z',
        sum: 10000000,
      },
      amountOfOtherPayments: 2,
    },
    {
      mainPaymentsGroup: {
        count: 3,
        currency: 'USD',
        paid_day: '2022-09-08T00:00:00.000Z',
        sum: 10000000,
      },
      amountOfOtherPayments: 3,
    },
    {
      mainPaymentsGroup: {
        count: 3,
        currency: 'USD',
        paid_day: '2022-09-07T00:00:00.000Z',
        sum: 10000000,
      },
      amountOfOtherPayments: 7,
    },
  ];
  const testCases = generatePayruns(4).map((item, i) => [
    item.payments,
    expected[i],
  ]);
  test.each(testCases)(
    'getPayrunTotalAmountData computation',
    async (payments, expected) => {
      expect(getPayrunTotalAmountData(payments, 'USD')).toStrictEqual(expected);
    }
  );
});
