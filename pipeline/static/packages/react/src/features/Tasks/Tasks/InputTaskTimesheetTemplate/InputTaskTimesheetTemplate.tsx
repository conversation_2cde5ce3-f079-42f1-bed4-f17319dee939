import { useMemo } from 'react';
import InputSelect from '../../../../components/FormElements/InputSelect/InputSelect';
import Spinner from '../../../../components/Utils/Spinner/Spinner';
import i18n from '../../../../i18n/i18n';
import Tasks from '../../../../services/Api/Tasks';
import { useAsync } from '../../../../utils/hooks/useAsync';

const t = (id: string) => i18n.t(`Tasks:InputTaskTimesheetTemplate.${id}`);

type InputTaskTimesheetTemplateProps = {
  defaultValue?: number;
  label?: string;
  isOptional?: boolean;
  disabled?: boolean;
  errors?: string[];
  onChange: (value: number) => void;
  testId?: string;
};

const InputTaskTimesheetTemplate = ({
  defaultValue,
  label,
  isOptional = false,
  disabled = false,
  errors = [],
  onChange,
  testId,
}: InputTaskTimesheetTemplateProps) => {
  const { isLoading, data: timesheetTemplates } = useAsync({
    promise: Tasks.loadTimesheetTemplates,
    onResolve: ({ data }) => {
      if (data.length === 1) {
        onChange(data[0].id);
      }
      return data.map(({ id, name }) => ({
        key: id,
        text: name,
      }));
    },
    defaultData: [],
  });

  const selectedValue: number = useMemo(() => {
    const foundTemplate = timesheetTemplates.find(
      ({ key }) => key === defaultValue
    );
    return foundTemplate?.key;
  }, [timesheetTemplates, defaultValue]);

  if (isLoading) {
    return <Spinner />;
  }

  if (timesheetTemplates.length === 1) {
    return <></>;
  }

  return (
    <InputSelect
      label={label}
      placeholder={t('SelectTimesheetTemplate')}
      isOptional={isOptional}
      disabled={disabled}
      options={timesheetTemplates}
      errors={errors}
      defaultValue={selectedValue}
      testId={testId}
      onChange={({ key }) => onChange(key ? Number(key) : null)}
    />
  );
};

export default InputTaskTimesheetTemplate;
