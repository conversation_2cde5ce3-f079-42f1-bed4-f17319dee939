import AutoComplete from '../../../../components/FormElements/AutoComplete/AutoComplete';
import Button from '../../../../components/FormElements/Button/Button';
import InputLabel from '../../../../components/FormElements/InputLabel/InputLabel';
import Select from '../../../../components/FormElements/Select/Select';
import i18n from '../../../../i18n/i18n';
import { goToStateOrOpenInNewTab } from '../../../../services/Reducers/AngularMigration.helpers';
import { Project } from '../../../../types/project';
import { IdNameArray } from './AddTaskDropdown';
import {
  ButtonsContainer,
  Container,
  Space,
} from './AddTaskDropdownInner.styles';
import React, { <PERSON>EventHandler, useEffect, useRef, useState } from 'react';

const convertToSelectValue = (data: IdNameArray) => {
  return data.map((i) => {
    return {
      key: String(i?.id),
      text: i?.name,
    };
  });
};

const convertToDropdownSelectItem = (data: IdNameArray) => {
  return data.map((i) => {
    return {
      key: String(i?.id),
      value: i?.name,
    };
  });
};

const AddTaskDropdownInner = ({
  allProjects,
  allTemplates,
  handleClose,
  project,
  getDropdownHeight,
}: {
  allProjects: IdNameArray;
  allTemplates: IdNameArray;
  handleClose?: MouseEventHandler;
  project?: Project;
  getDropdownHeight?: (height: number) => void;
}) => {
  const [projectId, setProjectId] = useState<string>();
  const [templateId, setTemplateId] = useState<string>();
  const [errors, setErrors] = useState({ projectId: [], templateId: [] });
  const divRef = useRef<HTMLDivElement>();

  useEffect(
    () => getDropdownHeight(divRef?.current?.offsetHeight || 0),
    [divRef.current]
  );

  useEffect(() => {
    if (project) {
      setProjectId(String(project.id));
    } else if (allProjects.length === 1) {
      setProjectId(allProjects[0].id);
    }
    allTemplates?.length === 1 && setTemplateId(allTemplates[0].id);
  }, []);

  useEffect(() => setErrors({ ...errors, projectId: [] }), [projectId]);
  useEffect(() => setErrors({ ...errors, templateId: [] }), [templateId]);

  const onClick = (event: React.MouseEvent) => {
    if (projectId && templateId) {
      goToStateOrOpenInNewTab('app.tasks.create-task', {
        template_id: templateId,
        task_group_id: projectId,
      })(event);
    } else {
      const errors = { projectId: [], templateId: [] };
      if (!projectId) {
        errors['projectId'] = [i18n.t('Errors.ProjectRequired')];
      }
      if (!templateId) {
        errors['templateId'] = [i18n.t('Errors.TaskTemplateRequired')];
      }
      setErrors(errors);
    }
  };

  if (project) {
    return (
      <Container
        ref={divRef}
        singleParam={true}
        addSearch={allTemplates.length > 10}
      >
        {allTemplates.length <= 10 && (
          <InputLabel label={i18n.t('Tasks:TaskTemplate')} />
        )}
        <Select
          options={convertToDropdownSelectItem(allTemplates)}
          onChange={(v) => {
            goToStateOrOpenInNewTab('app.tasks.create-task', {
              template_id: String(v[0].key),
              task_group_id: project.id,
            })();
          }}
          searchInputPlaceholder={i18n.t('Tasks:ChooseTemplate')}
          handleClose={handleClose}
        />
      </Container>
    );
  }

  if (allTemplates.length <= 1) {
    return (
      <Container
        ref={divRef}
        singleParam={true}
        addSearch={allProjects.length > 10}
      >
        {allProjects.length <= 10 && (
          <InputLabel label={i18n.t('Table.Project')} />
        )}
        <Select
          options={convertToDropdownSelectItem(allProjects)}
          onChange={(v) => {
            goToStateOrOpenInNewTab('app.tasks.create-task', {
              template_id: templateId,
              task_group_id: String(v[0].key),
            })();
          }}
          searchInputPlaceholder={i18n.t('Tasks:ChooseProject')}
          handleClose={handleClose}
        />
      </Container>
    );
  }

  return (
    <Container ref={divRef}>
      <AutoComplete
        label={i18n.t('Tasks:TaskTemplate')}
        placeholder={i18n.t('Tasks:ChooseTemplate')}
        options={convertToSelectValue(allTemplates)}
        disablePortal={false}
        onChange={(v) => setTemplateId(String(v))}
        errors={errors['templateId'] ? errors['templateId'] : undefined}
      />
      <Space />
      <AutoComplete
        defaultValue={
          allProjects.length === 1
            ? convertToSelectValue([allProjects[0]])
            : undefined
        }
        label={i18n.t('Table.Project')}
        placeholder={i18n.t('Tasks:ChooseProject')}
        options={convertToSelectValue(allProjects)}
        disablePortal={false}
        onChange={(v) => setProjectId(String(v))}
        errors={errors['projectId'] ? errors['projectId'] : undefined}
      />
      <Space />
      <ButtonsContainer>
        <Button
          size="small"
          rounded={false}
          label={i18n.t('Actions.Proceed')}
          onClick={onClick}
          testId="ProceedButton"
        />
        <Button
          variant="secondary"
          size="small"
          rounded={false}
          label={i18n.t('Form.Cancel')}
          onClick={handleClose}
        />
      </ButtonsContainer>
    </Container>
  );
};

export default AddTaskDropdownInner;
