import AutoComplete, {
  SelectValue,
} from '../../../../components/FormElements/AutoComplete/AutoComplete';
import InputLabel from '../../../../components/FormElements/InputLabel/InputLabel';
import Spinner from '../../../../components/Utils/Spinner/Spinner';
import i18n from '../../../../i18n/i18n';
import TaskGroups from '../../../../services/Api/TaskGroups';
import { Label } from '../../../../styles/typography';
import { Project } from '../../../../types/project';
import { TaskRequestData } from '../../../../types/task';
import { useDebouncedState } from '../../../../utils/hooks';
import { TaskFormFieldsContext } from '../TaskForm/TaskForm';
import {
  generateTemporarilyId,
  isTemporaryId,
} from '@Worksuite/Features/Contracts/components/ContractTasks/ContractTasks.helpers';
import React, { HTMLAttributes, useEffect } from 'react';

const t = (id: string, params?: Record<string, string | number>) =>
  i18n.t(`Tasks:InputTaskGroup.${id}`, params);

const renderOptionDefault = (
  props: HTMLAttributes<HTMLLIElement>,
  option: SelectValue
) => (
  <Label
    {...props}
    key={option.key}
    style={{ color: 'var(--text-link-color)' }}
  >
    {option.text}
    {isTemporaryId(option.key) && ` ${t('NewProject')}`}
  </Label>
);

type InputTaskGroupProps = {
  defaultValue?: TaskRequestData['task_group'];
  label?: string;
  isOptional?: boolean;
  disabled?: boolean;
  errors?: string[];
  onChange?: (task: TaskRequestData['task_group']) => void;
  testId?: string;
  context?: TaskFormFieldsContext;
};

const validateCommonTimeSpan = (
  project: Project,
  context: TaskFormFieldsContext
) => {
  if (!context.minStartDate && !context.maxEndDate) {
    return true;
  } // No time span restrictions
  if (project.date_end && !project.date_start) {
    return false;
  } // Project maxDate requires minDate

  const projectStart = project.date_start ? new Date(project.date_start) : null;
  const projectEnd = project.date_end ? new Date(project.date_end) : null;
  const minDate = new Date(context.minStartDate);
  const maxDate = context.maxEndDate ? new Date(context.maxEndDate) : null;

  if (!projectStart && !projectEnd) {
    return true;
  } // Project spans all time
  if (!projectEnd) {
    return projectStart <= (maxDate || new Date());
  }
  if (!maxDate) {
    return projectEnd >= minDate;
  }

  return projectStart <= maxDate && projectEnd >= minDate;
};

const InputTaskGroup = ({
  defaultValue,
  label,
  isOptional = true,
  disabled = false,
  errors = [],
  onChange,
  testId,
  context,
}: InputTaskGroupProps) => {
  const [value, setValue] = useDebouncedState('', 200);
  const [options, setOptions] = React.useState<SelectValue[]>([]);
  const [newProject, setNewProject] = React.useState<SelectValue>();
  const [selectedValue, setSelectedValue] = React.useState<SelectValue[]>();

  useEffect(() => {
    setOptions([]);
    TaskGroups.getTaskGroups({
      search: value,
      page_size: 10,
      currency: context?.currency,
    }).then(({ data }) => {
      let options = data.results
        .filter((project) => validateCommonTimeSpan(project, context))
        .map((i: Project) => ({
          key: i.id,
          text: i.name,
        }));
      if (value.trim().length > 0) {
        const newProject = { key: generateTemporarilyId(), text: value };
        setNewProject(newProject);
        options = [...options, newProject];
      }
      setOptions(options);
    });
  }, [value]);

  useEffect(() => {
    if (defaultValue?.id) {
      TaskGroups.single(defaultValue.id).then(({ data: { id, name } }) => {
        setSelectedValue([{ key: id, text: name }]);
      });
    } else if (defaultValue?.name) {
      setSelectedValue([
        { key: generateTemporarilyId(), text: defaultValue.name },
      ]);
    }
  }, [JSON.stringify(defaultValue)]);

  useEffect(() => {
    if (Array.isArray(selectedValue) && selectedValue[0].key) {
      if (isTemporaryId(selectedValue[0].key)) {
        onChange?.({
          name:
            newProject && selectedValue[0].key === newProject.key
              ? newProject.text
              : selectedValue[0].text,
        });
      } else {
        onChange?.({
          id: parseInt(String(selectedValue[0].key), 10),
        });
      }
    }
  }, [selectedValue]);

  if (defaultValue && !selectedValue) {
    return (
      <>
        <InputLabel label={label} isOptional={isOptional} />
        <Spinner />
      </>
    );
  }
  return (
    <AutoComplete
      label={label}
      placeholder={t('SelectProject')}
      isOptional={isOptional}
      disabled={disabled}
      options={options}
      errors={errors}
      defaultValue={selectedValue}
      testId={testId}
      showInputOnDisabled={true}
      onChange={([selectedValue]) => {
        if (selectedValue) {
          setSelectedValue([
            {
              key: selectedValue,
              text: '',
            },
          ]);
        } else {
          onChange?.(null);
        }
      }}
      onInputChange={(value) => setValue(value.trim())}
      renderOption={renderOptionDefault}
    />
  );
};

export default InputTaskGroup;
