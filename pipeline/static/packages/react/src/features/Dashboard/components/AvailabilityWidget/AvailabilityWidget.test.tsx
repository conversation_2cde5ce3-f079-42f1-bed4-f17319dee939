import { composeStories } from '@storybook/react';
import * as stories from './AvailabilityWidget.stories';
import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import MockDate from 'mockdate';
import React from 'react';

MockDate.set('2024-03-02');

describe('AvailabilityWidget - ', () => {
  test('renders title', async () => {
    const { Default: AvaiabilityWidget } = composeStories(stories);
    render(<AvaiabilityWidget />);

    expect(await screen.findByText('Set Your Availability')).toBeVisible();
    expect(await screen.findByText('Manage your availability.')).toBeVisible();
  });

  const config = getStories();

  config.forEach((item) => {
    const title = item.title ?? `renders to ${item.story} state`;
    test(title, async () => {
      const AvaiabilityWidget = composeStories(stories)[item.story];

      const { container } = render(<AvaiabilityWidget />);

      const dropdownEl = await screen.findByTestId(
        'AvailabilityWidgetDropdown_TriggerButton'
      );

      expect(dropdownEl).toBeVisible();
      expect(dropdownEl).toHaveTextContent(item.predicate);

      expect(container).toMatchSnapshot();
    });
  });
});

function getStories() {
  return [
    {
      title: 'renders default to `Not Set` state',
      story: 'Default',
      predicate: 'Availability Not Set',
    },
    { story: 'Available', predicate: 'Available' },
    { story: 'AvailablePartially', predicate: 'Available Part-Time' },
    { story: 'NotAvailable', predicate: 'Unavailable' },
    { story: 'SoonAvailable', predicate: 'Available Soon' },
    { story: 'SoonAvailableTomorrow', predicate: 'Available tomorrow' },
    { story: 'SoonAvailableYesterday', predicate: 'Available 1 day ago' },
    { story: 'SoonAvailableThisMonth', predicate: 'Available in 29 days' },
    { story: 'SoonAvailableThisYear', predicate: 'Available from 31 Dec' },
    { story: 'SoonAvailableNextYear', predicate: 'Available from 10 Jan 2025' },
  ];
}
