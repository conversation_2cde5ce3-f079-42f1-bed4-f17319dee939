// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`OnboardingWorkflowWidget > snapshot should match: MultiStageInProgress 1`] = `
.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class:hover .checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class:hover>.checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6!important;
}

.emotion-class {
  margin: 1px 0 -1px 6px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  color: #303757;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  fill: #000000;
  width: 28px;
  height: 28px;
  fill-rule: evenodd;
  vertical-align: middle;
  position: absolute;
  top: 10px;
  right: 5px;
  fill: #465AB6;
  pointer-events: none;
}

.emotion-class {
  border-radius: var(--spacing-s);
  box-shadow: var(--box-shadow-1);
  padding: var(--spacing-m);
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  row-gap: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-sizing: border-box;
}

.emotion-class {
  text-align: center;
}

.emotion-class h3 {
  font-size: 20px;
  margin-bottom: 10px;
}

.emotion-class .spinner-animation {
  display: inline-block;
  width: 36px;
  height: 28px;
}

.emotion-class .spinner-circle {
  position: relative;
  box-sizing: border-box;
  float: left;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background: #B4BCE0;
}

.emotion-class .spinner-circle:not(:last-of-type) {
  margin-right: 6px;
}

.emotion-class .spinner-circle:nth-of-type(1) {
  -webkit-animation: wave 1.2s ease 0s infinite;
  animation: wave 1.2s ease 0s infinite;
}

.emotion-class .spinner-circle:nth-of-type(2) {
  -webkit-animation: wave 1.2s ease 0.2s infinite;
  animation: wave 1.2s ease 0.2s infinite;
}

.emotion-class .spinner-circle:nth-of-type(3) {
  -webkit-animation: wave 1.2s ease 0.4s infinite;
  animation: wave 1.2s ease 0.4s infinite;
}

<div>
  <div
    style="position: sticky; top: 0px; padding: 10px 2rem; width: 100%; z-index: 10000; background-color: white; margin: -32px 0px 10px -2rem; box-shadow: rgb(0 0 0 / 10%) 0px -1px 0px 0px inset; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #E4E5EB;"
  >
    <div
      style="display: flex; justify-content: space-between;"
    >
      <div>
        <span
          class="emotion-class"
        >
          Tenant features
        </span>
        <div
          style="margin-top: 5px;"
        >
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    disable_vendor_star_rating
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    vendor_favorites
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    vendor_bulk_import
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    task_templates_in_job_openings
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    onboarding
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    onboarding_workflows
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    availability_calendar
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    availability
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    payments
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    bulk_task_booking
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    tasks_time_sheet
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    time_tracking_autofill_timesheet
                  </span>
                </span>
              </label>
            </div>
          </span>
        </div>
      </div>
      <div
        style="white-space: nowrap;"
      >
        <span
          class="emotion-class"
        >
          Logged user
        </span>
        <div
          style="margin-top: 5px; width: 130px;"
        >
          <div
            class="emotion-class"
          >
            <select
              class="emotion-class"
            >
              <option
                data-testid="SelectOption_Staff"
                value="staff"
              >
                Staff
              </option>
              <option
                data-testid="SelectOption_BuyerAdmin"
                value="buyer-admin"
              >
                Buyer Admin
              </option>
              <option
                data-testid="SelectOption_BuyerRegular"
                value="buyer-regular"
              >
                Buyer Regular
              </option>
              <option
                data-testid="SelectOption_Vendor"
                value="vendor"
              >
                Vendor
              </option>
            </select>
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
              class="emotion-class"
              size="28"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    style="width: 360px;"
  >
    <div
      class="canvas-base emotion-class"
    >
      <div
        class="emotion-class"
        data-testid="Spinner"
      >
        <div
          class="spinner-animation"
        >
          <div
            class="spinner-circle"
          />
          <div
            class="spinner-circle"
          />
          <div
            class="spinner-circle"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`OnboardingWorkflowWidget > snapshot should match: MultiStageInternalStage 1`] = `
.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class:hover .checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class:hover>.checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6!important;
}

.emotion-class {
  margin: 1px 0 -1px 6px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  color: #303757;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  fill: #000000;
  width: 28px;
  height: 28px;
  fill-rule: evenodd;
  vertical-align: middle;
  position: absolute;
  top: 10px;
  right: 5px;
  fill: #465AB6;
  pointer-events: none;
}

.emotion-class {
  border-radius: var(--spacing-s);
  box-shadow: var(--box-shadow-1);
  padding: var(--spacing-m);
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  row-gap: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-sizing: border-box;
}

.emotion-class {
  text-align: center;
}

.emotion-class h3 {
  font-size: 20px;
  margin-bottom: 10px;
}

.emotion-class .spinner-animation {
  display: inline-block;
  width: 36px;
  height: 28px;
}

.emotion-class .spinner-circle {
  position: relative;
  box-sizing: border-box;
  float: left;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background: #B4BCE0;
}

.emotion-class .spinner-circle:not(:last-of-type) {
  margin-right: 6px;
}

.emotion-class .spinner-circle:nth-of-type(1) {
  -webkit-animation: wave 1.2s ease 0s infinite;
  animation: wave 1.2s ease 0s infinite;
}

.emotion-class .spinner-circle:nth-of-type(2) {
  -webkit-animation: wave 1.2s ease 0.2s infinite;
  animation: wave 1.2s ease 0.2s infinite;
}

.emotion-class .spinner-circle:nth-of-type(3) {
  -webkit-animation: wave 1.2s ease 0.4s infinite;
  animation: wave 1.2s ease 0.4s infinite;
}

<div>
  <div
    style="position: sticky; top: 0px; padding: 10px 2rem; width: 100%; z-index: 10000; background-color: white; margin: -32px 0px 10px -2rem; box-shadow: rgb(0 0 0 / 10%) 0px -1px 0px 0px inset; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #E4E5EB;"
  >
    <div
      style="display: flex; justify-content: space-between;"
    >
      <div>
        <span
          class="emotion-class"
        >
          Tenant features
        </span>
        <div
          style="margin-top: 5px;"
        >
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    disable_vendor_star_rating
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    vendor_favorites
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    vendor_bulk_import
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    task_templates_in_job_openings
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    onboarding
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    onboarding_workflows
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    availability_calendar
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    availability
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    payments
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    bulk_task_booking
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    tasks_time_sheet
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    time_tracking_autofill_timesheet
                  </span>
                </span>
              </label>
            </div>
          </span>
        </div>
      </div>
      <div
        style="white-space: nowrap;"
      >
        <span
          class="emotion-class"
        >
          Logged user
        </span>
        <div
          style="margin-top: 5px; width: 130px;"
        >
          <div
            class="emotion-class"
          >
            <select
              class="emotion-class"
            >
              <option
                data-testid="SelectOption_Staff"
                value="staff"
              >
                Staff
              </option>
              <option
                data-testid="SelectOption_BuyerAdmin"
                value="buyer-admin"
              >
                Buyer Admin
              </option>
              <option
                data-testid="SelectOption_BuyerRegular"
                value="buyer-regular"
              >
                Buyer Regular
              </option>
              <option
                data-testid="SelectOption_Vendor"
                value="vendor"
              >
                Vendor
              </option>
            </select>
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
              class="emotion-class"
              size="28"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    style="width: 360px;"
  >
    <div
      class="canvas-base emotion-class"
    >
      <div
        class="emotion-class"
        data-testid="Spinner"
      >
        <div
          class="spinner-animation"
        >
          <div
            class="spinner-circle"
          />
          <div
            class="spinner-circle"
          />
          <div
            class="spinner-circle"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`OnboardingWorkflowWidget > snapshot should match: MultiStageStart 1`] = `
.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class:hover .checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class:hover>.checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6!important;
}

.emotion-class {
  margin: 1px 0 -1px 6px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  color: #303757;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  fill: #000000;
  width: 28px;
  height: 28px;
  fill-rule: evenodd;
  vertical-align: middle;
  position: absolute;
  top: 10px;
  right: 5px;
  fill: #465AB6;
  pointer-events: none;
}

.emotion-class {
  border-radius: var(--spacing-s);
  box-shadow: var(--box-shadow-1);
  padding: var(--spacing-m);
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  row-gap: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-sizing: border-box;
}

.emotion-class {
  text-align: center;
}

.emotion-class h3 {
  font-size: 20px;
  margin-bottom: 10px;
}

.emotion-class .spinner-animation {
  display: inline-block;
  width: 36px;
  height: 28px;
}

.emotion-class .spinner-circle {
  position: relative;
  box-sizing: border-box;
  float: left;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background: #B4BCE0;
}

.emotion-class .spinner-circle:not(:last-of-type) {
  margin-right: 6px;
}

.emotion-class .spinner-circle:nth-of-type(1) {
  -webkit-animation: wave 1.2s ease 0s infinite;
  animation: wave 1.2s ease 0s infinite;
}

.emotion-class .spinner-circle:nth-of-type(2) {
  -webkit-animation: wave 1.2s ease 0.2s infinite;
  animation: wave 1.2s ease 0.2s infinite;
}

.emotion-class .spinner-circle:nth-of-type(3) {
  -webkit-animation: wave 1.2s ease 0.4s infinite;
  animation: wave 1.2s ease 0.4s infinite;
}

<div>
  <div
    style="position: sticky; top: 0px; padding: 10px 2rem; width: 100%; z-index: 10000; background-color: white; margin: -32px 0px 10px -2rem; box-shadow: rgb(0 0 0 / 10%) 0px -1px 0px 0px inset; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #E4E5EB;"
  >
    <div
      style="display: flex; justify-content: space-between;"
    >
      <div>
        <span
          class="emotion-class"
        >
          Tenant features
        </span>
        <div
          style="margin-top: 5px;"
        >
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    disable_vendor_star_rating
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    vendor_favorites
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    vendor_bulk_import
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    task_templates_in_job_openings
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    onboarding
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    onboarding_workflows
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    availability_calendar
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    availability
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    payments
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    bulk_task_booking
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    tasks_time_sheet
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    time_tracking_autofill_timesheet
                  </span>
                </span>
              </label>
            </div>
          </span>
        </div>
      </div>
      <div
        style="white-space: nowrap;"
      >
        <span
          class="emotion-class"
        >
          Logged user
        </span>
        <div
          style="margin-top: 5px; width: 130px;"
        >
          <div
            class="emotion-class"
          >
            <select
              class="emotion-class"
            >
              <option
                data-testid="SelectOption_Staff"
                value="staff"
              >
                Staff
              </option>
              <option
                data-testid="SelectOption_BuyerAdmin"
                value="buyer-admin"
              >
                Buyer Admin
              </option>
              <option
                data-testid="SelectOption_BuyerRegular"
                value="buyer-regular"
              >
                Buyer Regular
              </option>
              <option
                data-testid="SelectOption_Vendor"
                value="vendor"
              >
                Vendor
              </option>
            </select>
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
              class="emotion-class"
              size="28"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    style="width: 360px;"
  >
    <div
      class="canvas-base emotion-class"
    >
      <div
        class="emotion-class"
        data-testid="Spinner"
      >
        <div
          class="spinner-animation"
        >
          <div
            class="spinner-circle"
          />
          <div
            class="spinner-circle"
          />
          <div
            class="spinner-circle"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`OnboardingWorkflowWidget > snapshot should match: MultiStageStartStage 1`] = `
.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class:hover .checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class:hover>.checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6!important;
}

.emotion-class {
  margin: 1px 0 -1px 6px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  color: #303757;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  fill: #000000;
  width: 28px;
  height: 28px;
  fill-rule: evenodd;
  vertical-align: middle;
  position: absolute;
  top: 10px;
  right: 5px;
  fill: #465AB6;
  pointer-events: none;
}

.emotion-class {
  border-radius: var(--spacing-s);
  box-shadow: var(--box-shadow-1);
  padding: var(--spacing-m);
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  row-gap: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-sizing: border-box;
}

.emotion-class {
  text-align: center;
}

.emotion-class h3 {
  font-size: 20px;
  margin-bottom: 10px;
}

.emotion-class .spinner-animation {
  display: inline-block;
  width: 36px;
  height: 28px;
}

.emotion-class .spinner-circle {
  position: relative;
  box-sizing: border-box;
  float: left;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background: #B4BCE0;
}

.emotion-class .spinner-circle:not(:last-of-type) {
  margin-right: 6px;
}

.emotion-class .spinner-circle:nth-of-type(1) {
  -webkit-animation: wave 1.2s ease 0s infinite;
  animation: wave 1.2s ease 0s infinite;
}

.emotion-class .spinner-circle:nth-of-type(2) {
  -webkit-animation: wave 1.2s ease 0.2s infinite;
  animation: wave 1.2s ease 0.2s infinite;
}

.emotion-class .spinner-circle:nth-of-type(3) {
  -webkit-animation: wave 1.2s ease 0.4s infinite;
  animation: wave 1.2s ease 0.4s infinite;
}

<div>
  <div
    style="position: sticky; top: 0px; padding: 10px 2rem; width: 100%; z-index: 10000; background-color: white; margin: -32px 0px 10px -2rem; box-shadow: rgb(0 0 0 / 10%) 0px -1px 0px 0px inset; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #E4E5EB;"
  >
    <div
      style="display: flex; justify-content: space-between;"
    >
      <div>
        <span
          class="emotion-class"
        >
          Tenant features
        </span>
        <div
          style="margin-top: 5px;"
        >
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    disable_vendor_star_rating
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    vendor_favorites
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    vendor_bulk_import
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    task_templates_in_job_openings
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    onboarding
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    onboarding_workflows
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    availability_calendar
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    availability
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    payments
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    bulk_task_booking
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    tasks_time_sheet
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    time_tracking_autofill_timesheet
                  </span>
                </span>
              </label>
            </div>
          </span>
        </div>
      </div>
      <div
        style="white-space: nowrap;"
      >
        <span
          class="emotion-class"
        >
          Logged user
        </span>
        <div
          style="margin-top: 5px; width: 130px;"
        >
          <div
            class="emotion-class"
          >
            <select
              class="emotion-class"
            >
              <option
                data-testid="SelectOption_Staff"
                value="staff"
              >
                Staff
              </option>
              <option
                data-testid="SelectOption_BuyerAdmin"
                value="buyer-admin"
              >
                Buyer Admin
              </option>
              <option
                data-testid="SelectOption_BuyerRegular"
                value="buyer-regular"
              >
                Buyer Regular
              </option>
              <option
                data-testid="SelectOption_Vendor"
                value="vendor"
              >
                Vendor
              </option>
            </select>
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
              class="emotion-class"
              size="28"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    style="width: 360px;"
  >
    <div
      class="canvas-base emotion-class"
    >
      <div
        class="emotion-class"
        data-testid="Spinner"
      >
        <div
          class="spinner-animation"
        >
          <div
            class="spinner-circle"
          />
          <div
            class="spinner-circle"
          />
          <div
            class="spinner-circle"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`OnboardingWorkflowWidget > snapshot should match: SingleStageInProgress 1`] = `
.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class:hover .checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class:hover>.checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6!important;
}

.emotion-class {
  margin: 1px 0 -1px 6px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  color: #303757;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  fill: #000000;
  width: 28px;
  height: 28px;
  fill-rule: evenodd;
  vertical-align: middle;
  position: absolute;
  top: 10px;
  right: 5px;
  fill: #465AB6;
  pointer-events: none;
}

.emotion-class {
  border-radius: var(--spacing-s);
  box-shadow: var(--box-shadow-1);
  padding: var(--spacing-m);
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  row-gap: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-sizing: border-box;
}

.emotion-class {
  text-align: center;
}

.emotion-class h3 {
  font-size: 20px;
  margin-bottom: 10px;
}

.emotion-class .spinner-animation {
  display: inline-block;
  width: 36px;
  height: 28px;
}

.emotion-class .spinner-circle {
  position: relative;
  box-sizing: border-box;
  float: left;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background: #B4BCE0;
}

.emotion-class .spinner-circle:not(:last-of-type) {
  margin-right: 6px;
}

.emotion-class .spinner-circle:nth-of-type(1) {
  -webkit-animation: wave 1.2s ease 0s infinite;
  animation: wave 1.2s ease 0s infinite;
}

.emotion-class .spinner-circle:nth-of-type(2) {
  -webkit-animation: wave 1.2s ease 0.2s infinite;
  animation: wave 1.2s ease 0.2s infinite;
}

.emotion-class .spinner-circle:nth-of-type(3) {
  -webkit-animation: wave 1.2s ease 0.4s infinite;
  animation: wave 1.2s ease 0.4s infinite;
}

<div>
  <div
    style="position: sticky; top: 0px; padding: 10px 2rem; width: 100%; z-index: 10000; background-color: white; margin: -32px 0px 10px -2rem; box-shadow: rgb(0 0 0 / 10%) 0px -1px 0px 0px inset; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #E4E5EB;"
  >
    <div
      style="display: flex; justify-content: space-between;"
    >
      <div>
        <span
          class="emotion-class"
        >
          Tenant features
        </span>
        <div
          style="margin-top: 5px;"
        >
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    disable_vendor_star_rating
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    vendor_favorites
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    vendor_bulk_import
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    task_templates_in_job_openings
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    onboarding
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    onboarding_workflows
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    availability_calendar
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    availability
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    payments
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    bulk_task_booking
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    tasks_time_sheet
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    time_tracking_autofill_timesheet
                  </span>
                </span>
              </label>
            </div>
          </span>
        </div>
      </div>
      <div
        style="white-space: nowrap;"
      >
        <span
          class="emotion-class"
        >
          Logged user
        </span>
        <div
          style="margin-top: 5px; width: 130px;"
        >
          <div
            class="emotion-class"
          >
            <select
              class="emotion-class"
            >
              <option
                data-testid="SelectOption_Staff"
                value="staff"
              >
                Staff
              </option>
              <option
                data-testid="SelectOption_BuyerAdmin"
                value="buyer-admin"
              >
                Buyer Admin
              </option>
              <option
                data-testid="SelectOption_BuyerRegular"
                value="buyer-regular"
              >
                Buyer Regular
              </option>
              <option
                data-testid="SelectOption_Vendor"
                value="vendor"
              >
                Vendor
              </option>
            </select>
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
              class="emotion-class"
              size="28"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    style="width: 360px;"
  >
    <div
      class="canvas-base emotion-class"
    >
      <div
        class="emotion-class"
        data-testid="Spinner"
      >
        <div
          class="spinner-animation"
        >
          <div
            class="spinner-circle"
          />
          <div
            class="spinner-circle"
          />
          <div
            class="spinner-circle"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`OnboardingWorkflowWidget > snapshot should match: SingleStageStart 1`] = `
.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class:hover .checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class:hover>.checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6!important;
}

.emotion-class {
  margin: 1px 0 -1px 6px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  color: #303757;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  fill: #000000;
  width: 28px;
  height: 28px;
  fill-rule: evenodd;
  vertical-align: middle;
  position: absolute;
  top: 10px;
  right: 5px;
  fill: #465AB6;
  pointer-events: none;
}

.emotion-class {
  border-radius: var(--spacing-s);
  box-shadow: var(--box-shadow-1);
  padding: var(--spacing-m);
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  row-gap: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-sizing: border-box;
}

.emotion-class {
  text-align: center;
}

.emotion-class h3 {
  font-size: 20px;
  margin-bottom: 10px;
}

.emotion-class .spinner-animation {
  display: inline-block;
  width: 36px;
  height: 28px;
}

.emotion-class .spinner-circle {
  position: relative;
  box-sizing: border-box;
  float: left;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background: #B4BCE0;
}

.emotion-class .spinner-circle:not(:last-of-type) {
  margin-right: 6px;
}

.emotion-class .spinner-circle:nth-of-type(1) {
  -webkit-animation: wave 1.2s ease 0s infinite;
  animation: wave 1.2s ease 0s infinite;
}

.emotion-class .spinner-circle:nth-of-type(2) {
  -webkit-animation: wave 1.2s ease 0.2s infinite;
  animation: wave 1.2s ease 0.2s infinite;
}

.emotion-class .spinner-circle:nth-of-type(3) {
  -webkit-animation: wave 1.2s ease 0.4s infinite;
  animation: wave 1.2s ease 0.4s infinite;
}

<div>
  <div
    style="position: sticky; top: 0px; padding: 10px 2rem; width: 100%; z-index: 10000; background-color: white; margin: -32px 0px 10px -2rem; box-shadow: rgb(0 0 0 / 10%) 0px -1px 0px 0px inset; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #E4E5EB;"
  >
    <div
      style="display: flex; justify-content: space-between;"
    >
      <div>
        <span
          class="emotion-class"
        >
          Tenant features
        </span>
        <div
          style="margin-top: 5px;"
        >
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    disable_vendor_star_rating
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    vendor_favorites
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    vendor_bulk_import
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    task_templates_in_job_openings
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    onboarding
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    onboarding_workflows
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    availability_calendar
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    availability
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    payments
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    bulk_task_booking
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    tasks_time_sheet
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                    classname="checkboxOffIcon css-1dmky3q"
                    fill="#D1D6ED"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    time_tracking_autofill_timesheet
                  </span>
                </span>
              </label>
            </div>
          </span>
        </div>
      </div>
      <div
        style="white-space: nowrap;"
      >
        <span
          class="emotion-class"
        >
          Logged user
        </span>
        <div
          style="margin-top: 5px; width: 130px;"
        >
          <div
            class="emotion-class"
          >
            <select
              class="emotion-class"
            >
              <option
                data-testid="SelectOption_Staff"
                value="staff"
              >
                Staff
              </option>
              <option
                data-testid="SelectOption_BuyerAdmin"
                value="buyer-admin"
              >
                Buyer Admin
              </option>
              <option
                data-testid="SelectOption_BuyerRegular"
                value="buyer-regular"
              >
                Buyer Regular
              </option>
              <option
                data-testid="SelectOption_Vendor"
                value="vendor"
              >
                Vendor
              </option>
            </select>
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
              class="emotion-class"
              size="28"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    style="width: 360px;"
  >
    <div
      class="canvas-base emotion-class"
    >
      <div
        class="emotion-class"
        data-testid="Spinner"
      >
        <div
          class="spinner-animation"
        >
          <div
            class="spinner-circle"
          />
          <div
            class="spinner-circle"
          />
          <div
            class="spinner-circle"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;
