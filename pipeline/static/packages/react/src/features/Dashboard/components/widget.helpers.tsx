import { generateTestId } from '../../../utils/test.utils';
import { getBroadcastFileDownloadUrl } from '../dashboard.helpers';
import { Broadcast } from '../dashboard.types';
import { FileList } from '@Worksuite/Features/Core/components/DataDisplay/FileList/FileList';
import { SeparatorLine } from '@Worksuite/Features/Core/components/Separator/Separator';
import {
  DateSeparatorWrapper,
  DateString,
} from '@Worksuite/Features/Core/widgets/widget.styles';
import React from 'react';

export const WidgetSpacer = ({ title }: { title?: string }) => (
  <DateSeparatorWrapper>
    {title && (
      <DateString {...generateTestId('Date', 'BroadcastFactory_WidgetSpacer')}>
        {title}
      </DateString>
    )}
    <SeparatorLine />
  </DateSeparatorWrapper>
);

export const BroadcastFiles = ({ broadcast }: { broadcast: Broadcast }) => (
  <FileList
    files={broadcast.files}
    onRemove={null}
    variant="small"
    getFileDownloadUrl={(file) =>
      getBroadcastFileDownloadUrl(
        {
          id: Number(broadcast.key.split('#')[1]),
          display_on: broadcast.broadcast_type,
        },
        file
      )
    }
  />
);
