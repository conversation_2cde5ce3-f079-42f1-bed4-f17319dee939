import { WidgetWrapperBaseFlexColumn } from '@Worksuite/Features/Core/widgets/widget.styles';
import React from 'react';
import { AnnouncementBannerIcon } from '../../../../assets/icons/large';
import { HeaderXLarge } from '../../../../styles/typography';
import { useAsync } from '../../../../utils/hooks/useAsync';
import { safeMessage } from '../../../../utils/html';
import { generateTestId } from '../../../../utils/test.utils';
import { Dashboards } from '../../services/Api/Dashboards';
import { BroadcastFiles } from '../widget.helpers';
import {
  AnnouncementBannerContainer,
  AnnouncementBody,
  AnnouncementTitle,
} from './AnnouncementBanner.styles';

export function AnnouncementBanner() {
  const {
    data: { banners },
    isLoading,
  } = useAsync({
    promise: () => Dashboards.VendorAnnouncements.getAnnouncements(),
    onResolve: (response) => ({
      banners: response.data.results,
    }),
  });

  if (isLoading) {
    return null;
  }
  return (
    banners?.length > 0 && (
      <AnnouncementBannerContainer>
        {banners.map((banner) => (
          <WidgetWrapperBaseFlexColumn
            key={banner.key}
            className="canvas-fill-neutral"
            {...generateTestId('AnnouncementBannerItem', 'Dashboard')}
          >
            <AnnouncementTitle>
              <HeaderXLarge style={{ flexGrow: 1 }}>
                {banner.header}
              </HeaderXLarge>
              <AnnouncementBannerIcon size={40} />
            </AnnouncementTitle>
            <AnnouncementBody
              dangerouslySetInnerHTML={{
                __html: safeMessage(banner.html, ['data-list']),
              }}
            />
            <BroadcastFiles broadcast={banner} />
          </WidgetWrapperBaseFlexColumn>
        ))}
      </AnnouncementBannerContainer>
    )
  );
}
