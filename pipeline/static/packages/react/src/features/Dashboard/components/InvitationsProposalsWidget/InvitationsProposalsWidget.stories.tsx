import invitationsOnlyData from '../../../../fixtures/api/projects_waiting/invitations_only.json';
import invitationsProposalsData from '../../../../fixtures/api/projects_waiting/invitations_proposals.json';
import proposalsOnlyData from '../../../../fixtures/api/projects_waiting/proposals_only.json';
import { TENANT_FEATURE } from '../../../../services/Shortlist/feature-flags';
import { getApiMockAdapter } from '../../../../utils/storybook-utils';
import { withStorybookTenantManagement } from '../../../../utils/storybook.hooks';
import { InvitationsProposalsWidget } from './InvitationsProposalsWidget';
import React, { useEffect, useState } from 'react';

export default {
  title: 'Features/Dashboard/Components/InvitationsProposalsWidget',
  component: InvitationsProposalsWidget,
};

const Template = ({
  hasInvitations = false,
  hasProposals = false,
}: {
  hasInvitations?: boolean;
  hasProposals?: boolean;
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  useEffect(() => {
    const mock = getApiMockAdapter({ delayResponse: 200 });
    mock
      .onGet(/\/api\/v\/projects_waiting\//gm)
      .reply(200, getData(hasInvitations, hasProposals));
    setIsLoaded(true);
    return () => mock.reset();
  }, []);
  return isLoaded ? (
    <div style={{ width: '400px' }}>
      <InvitationsProposalsWidget />
    </div>
  ) : null;
};

function getData(hasInvitations: boolean, hasProposals: boolean) {
  if (hasInvitations && hasProposals) {
    return invitationsProposalsData;
  }
  if (hasInvitations && !hasProposals) {
    return invitationsOnlyData;
  }
  if (!hasInvitations && hasProposals) {
    return proposalsOnlyData;
  }
}

const TenantConfig = {
  defaultFeatures: ['requests'] as TENANT_FEATURE[],
  changeableFeatures: ['requests'] as TENANT_FEATURE[],
  defaultSessionUser: 'vendor' as const,
};

export const Default = withStorybookTenantManagement(
  () => <Template hasInvitations hasProposals />,
  TenantConfig
);

export const OnlyInvitations = withStorybookTenantManagement(
  () => <Template hasInvitations />,
  TenantConfig
);

export const OnlyProposals = withStorybookTenantManagement(
  () => <Template hasProposals />,
  TenantConfig
);
