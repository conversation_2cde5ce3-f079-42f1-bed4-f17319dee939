import { CustomField } from '../../components/CustomFields/CustomFields';
import { BackendFile } from '../../utils/files';

export type AnnouncementDisplayType =
  | 'announcement'
  | 'pinned_on_feed'
  | 'unpinned_on_feed';

export type AnnouncementStatus = 'draft' | 'published';

export interface Announcement {
  id?: number;
  title: string;
  description: string;
  vendor_group: number;
  display_on: AnnouncementDisplayType;
  status: AnnouncementStatus;
  files: BackendFile[];
}

export type ActivityType =
  | 'DashboardAnnouncement'
  | 'ActivityFromEvent'
  | 'VendorActivationOnVendorAccount';

export interface Broadcast {
  key: string;
  header: string;
  html: string;
  time: string;
  activity_type: ActivityType;
  user: number | null;
  broadcast_type: 'pinned_on_feed' | 'unpinned_on_feed' | 'announcement';
  files: BackendFile[];
}

export const ANNOUNCEMENT_DISPLAY_TYPES = new Map<
  AnnouncementDisplayType,
  string
>([
  ['announcement', 'Announcement widget'],
  ['pinned_on_feed', 'Pinned to notifications feed'],
  ['unpinned_on_feed', 'Notifications feed'],
]);

export type Manager = {
  avatar_color: string;
  slug: string;
  full_name: string;
  initials: string;
  profile_picture_path: string | null;
  buyer: {
    id: string;
    custom_fields: CustomField[];
  };
};
