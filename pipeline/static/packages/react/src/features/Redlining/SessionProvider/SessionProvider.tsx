import { atom, useAtom } from 'jotai';
import { createContext, useContext, useEffect, useRef } from 'react';
import { useRefreshTimer } from '../../../utils/hooks/useRefreshTimer';
import { SessionExpiry } from './SessionExpiry';
import {
  CreateSessionAtoms,
  SessionAtoms,
  SessionProviderProps,
} from './SessionProvider.types';

const createSessionAtoms = ({
  sessionManager,
}: CreateSessionAtoms): SessionAtoms => {
  const currentLockAtom = atom<{
    isLocked: boolean;
    isLockedByCurrentUser: boolean;
    expiresInSec: number;
    expiresAt: string | null;
  }>({
    isLocked: false,
    isLockedByCurrentUser: false,
    expiresInSec: 0,
    expiresAt: null,
  });

  const lockSessionAtom = atom<null, [undefined], Promise<boolean>>(
    null,
    async (_, set) => {
      const { isLockSuccessful, expiresInSec, expiresAt } =
        await sessionManager.lock();
      set(currentLockAtom, {
        isLocked: true,
        isLockedByCurrentUser: isLockSuccessful,
        expiresInSec,
        expiresAt,
      });
      return isLockSuccessful;
    }
  );

  const unlockSessionAtom = atom<null, [undefined], Promise<void>>(
    null,
    async (_, set) => {
      await sessionManager.unlock();
      set(currentLockAtom, {
        isLocked: false,
        isLockedByCurrentUser: false,
        expiresInSec: 0,
        expiresAt: null,
      });
    }
  );

  return {
    currentLockAtom,
    lockSessionAtom,
    unlockSessionAtom,
  };
};

const SessionContext = createContext<ReturnType<
  typeof createSessionAtoms
> | null>(null);

export const SessionProvider = ({
  children,
  sessionManager,
}: SessionProviderProps) => {
  const sessionManagerRef = useRef(sessionManager);
  const currentLockRef = useRef<{
    isLocked: boolean;
    isLockedByCurrentUser: boolean;
    expiresInSec: number;
    expiresAt: string | null;
  }>({
    isLocked: false,
    isLockedByCurrentUser: false,
    expiresInSec: 0,
    expiresAt: null,
  });

  const handleCurrentSessionState = () => {
    sessionManager
      .getCurrentLock()
      .then(({ isLocked, isLockedByCurrentUser, expiresInSec, expiresAt }) => {
        setCurrentLock({
          isLocked,
          isLockedByCurrentUser,
          expiresInSec,
          expiresAt,
        });
      });
  };

  useRefreshTimer({
    refreshInterval: 30_000,
    onInit: handleCurrentSessionState,
    onRefresh: handleCurrentSessionState,
    onDestroy: () => {
      if (currentLockRef.current.isLockedByCurrentUser) {
        unlock(null);
      }
    },
  });

  useEffect(() => {
    sessionManagerRef.current = sessionManager;
  }, [sessionManager]);

  const atomsRef = useRef(
    createSessionAtoms({
      sessionManager: sessionManagerRef.current,
    })
  );
  const atoms = atomsRef.current;
  const [currentLock, setCurrentLock] = useAtom(atoms.currentLockAtom);
  const [, unlock] = useAtom(atoms.unlockSessionAtom);

  useEffect(() => {
    currentLockRef.current = currentLock;
  }, [currentLock]);

  return (
    <SessionContext.Provider value={atoms}>
      <SessionExpiry saveSessionPromise={sessionManager.saveSessionPromise}>
        {children}
      </SessionExpiry>
    </SessionContext.Provider>
  );
};

export const useSessionContext = () => {
  const context = useContext(SessionContext);
  if (!context) {
    throw new Error('useSessionContext must be used within a SessionProvider');
  }
  return context;
};
