import {
  getCommentContentFromHtml,
  htmlToSlate,
  slateToHtml,
} from './Redlining.helpers';

describe('htmlToSlate > slateToHtml', () => {
  test.each([
    'test',
    '<p>ad</p><p indent="3">ads</p>',
    '<p>E<comment comment-id="190"></comment>xcepteur</p>',
    `<p><u><em><strong>asd</strong></em></u></p><blockquote><u><em><strong>test</strong></em></u></blockquote><p></p>`,
    '<h1 style="text-align: left;">h1</h1><h2 style="text-align: right;">h2</h2><h3 style="text-align: justify;">h3</h3>',
    '<p><comment comment-id="a1">aaa</comment><comment comment-id="c2"><comment comment-id="b2">bbb</comment></comment><comment comment-id="a1">ccc</comment></p>',
    '<p><comment comment-id="aaa"><comment comment-id="bbb">bbb</comment></comment></p>',
    '<p><comment comment-id="xxx"><comment comment-id="aaa">bbb</comment></comment></p>',
    '<p>a</p><p>a<comment comment-id="PevjD6ZN8u78FHXVbGb_z">b</comment><comment comment-id="PevjD6ZN8u78FHXVbGb_z"><strong>c</strong></comment><comment comment-id="PevjD6ZN8u78FHXVbGb_z"><em><strong>d</strong></em></comment><comment comment-id="PevjD6ZN8u78FHXVbGb_z"><em>e</em></comment><comment comment-id="PevjD6ZN8u78FHXVbGb_z"><u><em>f</em></u></comment><comment comment-id="PevjD6ZN8u78FHXVbGb_z"><u>g</u></comment><comment comment-id="PevjD6ZN8u78FHXVbGb_z">h</comment></p><p><comment comment-id="PevjD6ZN8u78FHXVbGb_z">i</comment><comment comment-id="325"><comment comment-id="PevjD6ZN8u78FHXVbGb_z">j</comment></comment></p><p>a</p><p>k<strong>jasdlkj</strong>l</p>',
    '<p><mention mention-id="current date"></mention><mention mention-id="name"></mention></p>',
    '<ul indent="1"><li>asd</li></ul><ul indent="1"><li>asd</li></ul><ul indent="1"><li>asd</li></ul><ol indent="1" start="1"><li>123</li></ol><ol indent="1" start="2"><li>123</li></ol><ol indent="1" start="3"><li>123</li></ol><ol indent="1" start="4"><li></li></ol>',
    '<ol indent="1"><li>asd</li></ol><ol indent="1" start="2"><li>asd</li></ol><ol indent="2"><li>asd</li></ol><ol indent="2" start="2"><li>asd</li></ol><ol indent="2" start="3"><li>asd</li></ol><ol indent="1" start="3"><li>asdasd</li></ol><ol indent="1" start="4"><li>asd</li></ol>',
    '<ul indent="1"><li>asdasd</li></ul><ul indent="1"><li>asdasd</li></ul><ul indent="1"><li>asdad</li></ul><ul indent="1"><li>asdasd</li></ul><ul indent="2"><li>asdasd</li></ul><ul indent="2"><li>asdasd</li></ul><ul indent="1"><li>asdadsad</li></ul><ul indent="1"><li>asdadasd</li></ul><ul indent="1"><li>asdasd</li></ul>',
    '<p indent="2">asds</p><p>asdasdasd</p><p indent="1">sadasd</p>',
    '<p><a href="http://onet.pl">ASD</a></p>',
    '<p><a href="http://onet.pl" target="_blank">ASD</a></p>',
    '<p></p><table border="1" style="width: 100%;"><tbody><tr><td><p>column1-row1</p></td><td><p>column2-row1</p></td></tr><tr><td><p>column1-row2</p></td><td><p>column2-row2</p></td></tr></tbody></table><p></p>',
    '<p>Text Before</p><table border="1" style="width: 100%;"><tbody><tr><td><p>column-1-row-1</p></td><td><p>column-2-row-1</p></td></tr><tr><td><p>column-1-row-2</p></td><td><p>column-2-row-2</p></td></tr></tbody></table><p indent="3">Text after</p>',
    '<p>Text Before</p><table border="1" style="width: 100%;"><tbody><tr><td><p>column-1-row-1</p></td><td><p>column-2-row-1</p></td></tr><tr><td><p>column-1-row-2</p></td><td><p>column-2-row-2</p></td></tr></tbody></table><p indent="3">Text after</p>',
    '<p><comment comment-id="KuWK8CChOns3dPh-9C741">Simple </comment><comment comment-id="535"><comment comment-id="KuWK8CChOns3dPh-9C741">Redl</comment></comment><comment comment-id="535">ining</comment></p>',
    '<p></p><table border="1" style="width: 100%;"><tbody><tr><td data-borders="left:0;bottom:0"><p>asd</p></td><td data-borders="top:0"><p>asdasd</p></td></tr><tr><td data-borders="bottom:0"><p>asd</p></td><td data-borders="right:0"><p>asdasd</p></td></tr></tbody></table><p></p>',
    '<h1 indent="1">h1</h1><h2 indent="3">h2</h2><h3 indent="5">h3</h3>',
    '<blockquote indent="1"><u><em><strong>TEST 123</strong></em></u></blockquote><blockquote>TEST 123</blockquote><blockquote indent="4">TEST 123</blockquote>',
    '<p style="text-align: justify;">List:</p><ul indent="1"><li>A</li></ul><ul indent="1"><li>B</li></ul><ul indent="1"><li>C</li></ul><ul indent="1"><li>D</li></ul><p style="text-align: justify;"></p><p style="text-align: justify;">List:</p><ol indent="1"><li>A</li></ol><ol indent="1" start="2"><li>B</li></ol><ol indent="1" start="3"><li>C</li></ol><p style="text-align: justify;"></p><p style="text-align: justify;">Indents:</p><p indent="1" style="text-align: justify;">A</p><p indent="2" style="text-align: justify;">B</p><p indent="3" style="text-align: justify;">C</p><p indent="4" style="text-align: justify;">D</p><p indent="4" style="text-align: justify;"></p>',
    '<p></p><table border="1" style="width: 100%;"><tbody><tr><td data-borders="top:0;left:0"><p></p></td><td><p></p></td></tr><tr><td><p></p></td><td data-borders="bottom:0;right:0"><p>1</p></td></tr></tbody></table><p></p>',
  ])('%s', (html) => {
    const slate = htmlToSlate(html);
    const convertedHtml = slateToHtml(slate);
    expect(convertedHtml).toBe(html);
  });
});

describe('XSS', () => {
  test.each([
    [
      '<h1 style="text-align: left;">h1</h1><h2 style="text-align: right;">h2</h2><h3 style="text-align: justify;">h3</h3><img src=x onerror=alert(\'XSS\')>',
      '<h1 style="text-align: left;">h1</h1><h2 style="text-align: right;">h2</h2><h3 style="text-align: justify;">h3</h3>',
    ],
  ])('%s', (html, expected) => {
    const slate = htmlToSlate(html);
    const convertedHtml = slateToHtml(slate);
    expect(convertedHtml).toBe(expected);
  });
});

describe('slateToHtml', () => {
  test.each([
    [
      '[{"type":"p","children":[{"text":""},{"type":"mention","children":[{"text":""}],"value":"current date"},{"text":""},{"type":"mention","children":[{"text":""}],"value":"name"},{"text":""}]}]',
      '<p><mention mention-id="current date"></mention><mention mention-id="name"></mention></p>',
    ],
    [
      '[{"type":"p","children":[{"text":"!"},{"type":"mention","children":[{"text":""}],"value":"managers"},{"text":""},{"type":"mention","children":[{"text":""}],"value":"name"},{"text":"!"}]}]',
      '<p>!<mention mention-id="managers"></mention><mention mention-id="name"></mention>!</p>',
    ],
    [
      '[{"type":"p","children":[{"text":"asd"}],"indent":1}]',
      '<p indent="1">asd</p>',
    ],
    [
      '[{"indent":1,"type":"p","listStyleType":"decimal","children":[{"text":"asd"}]},{"indent":1,"type":"p","listStyleType":"decimal","children":[{"text":"23","listStart":"2"}],"listStart":2},{"indent":1,"type":"p","listStyleType":"decimal","children":[{"text":"123","listStart":"3"}],"listStart":3},{"type":"p","children":[{"text":""}]},{"type":"p","children":[{"text":"indent 0"}]},{"type":"p","children":[{"text":"indent 1"}]},{"type":"p","children":[{"text":"indent 2"}]},{"type":"p","children":[{"text":""}]}]',
      '<ol indent="1"><li>asd</li></ol><ol indent="1" start="2"><li>23</li></ol><ol indent="1" start="3"><li>123</li></ol><p></p><p>indent 0</p><p>indent 1</p><p>indent 2</p><p></p>',
    ],
  ])('%s', (slate, expected) => {
    const convertedHtml = slateToHtml(JSON.parse(slate));
    expect(convertedHtml).toBe(expected);
  });
});

describe('getCommentContentFromHtml', () => {
  test.each([
    [
      'cZlh5TAWsa3uzQ3RFEuRe',
      '<h2>Lorem <u><em>ipsu</em></u><comment comment-id="cZlh5TAWsa3uzQ3RFEuRe"><u><em>m</em></u></comment><comment comment-id="cZlh5TAWsa3uzQ3RFEuRe"> dolor sit amet.</comment></h2><p><comment comment-id="cZlh5TAWsa3uzQ3RFEuRe">Consectetur adipiscing elit, sed do eiusmod tempor incididunt ST</comment><comment comment-id="cZlh5TAWsa3uzQ3RFEuRe"><strong>RO</strong></comment><comment comment-id="cZlh5TAWsa3uzQ3RFEuRe">NG</comment> et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. </p><p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>',
      'm dolor sit amet.Consectetur adipiscing elit, sed do eiusmod tempor incididunt STRONG',
    ],
  ])('%s', (id, html, expected) => {
    const convertedHtml = getCommentContentFromHtml(id, html);
    expect(convertedHtml).toBe(expected);
  });
});
