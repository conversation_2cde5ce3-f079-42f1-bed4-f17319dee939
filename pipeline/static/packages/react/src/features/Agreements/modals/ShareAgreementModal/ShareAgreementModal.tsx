import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import Button from '../../../../components/FormElements/Button/Button';
import ErrorList from '../../../../components/FormElements/ErrorList/ErrorList';
import InputCheckbox from '../../../../components/FormElements/InputCheckbox';
import InputText from '../../../../components/FormElements/InputText/InputText';
import { withModal } from '../../../../components/Utils/Modals/modal.hooks';
import {
  ModalBody,
  ModalFooter,
} from '../../../../components/Utils/Modals/modal.styles';
import Spinner from '../../../../components/Utils/Spinner/Spinner';
import i18n from '../../../../i18n/i18n';
import {
  SignRequestVendorGuest,
  addAgreementVendorGuest,
  getAgreementVendorGuests,
  removeAgreementVendorGuest,
  resendAgreementVendorGuestInvitation,
} from '../../../../services/Agreements/Agreements';
import {
  displayFailureMessage,
  displaySuccessMessage,
} from '../../../../services/Reducers/Notifications.reducer.helper';
import { Info, Label } from '../../../../styles/typography';
import {
  FORM_RULE_REQUIRED,
  FORM_RULE_REQUIRED_EMAIL,
} from '../../../../utils/forms';
import { useAsync } from '../../../../utils/hooks/useAsync';
import { generateTestId } from '../../../../utils/test.utils';
import { getFirstError } from '../../AgreementValidators/AgreementValidators';
import {
  BodySmallStyled,
  ColumnStyled,
  EmailsWithAccessStyled,
  ExpandedColumnStyled,
  FormContainerStyled,
  LabelStyled,
  RowStyled,
} from './ShareAgreementModal.styles';
import {
  ShareAgreementFormValues,
  ShareAgreementModal,
} from './ShareAgreementModal.types';

const t = (id: string) => i18n.t(`Agreements:Modals.ShareAgreement.${id}`);

export default withModal(
  ({
    modalClose,
    modalDismiss,
    modalSize,
    signRequest,
  }: ShareAgreementModal) => {
    const [isSaving, setIsSaving] = useState(false);
    const [error, setError] = useState<string[]>([]);
    const [guests, setGuests] = useState<SignRequestVendorGuest[]>([]);
    const [resentInvitations, setResentInvitations] = useState<
      SignRequestVendorGuest['id'][]
    >([]);

    const { control, reset, trigger, getValues } =
      useForm<ShareAgreementFormValues>({
        defaultValues: {
          name: '',
          email: '',
          acknowledge: false,
        },
      });

    const handleShareAgreement = async () => {
      const { name, email } = getValues();
      const isValid = await trigger();
      if (isValid) {
        setIsSaving(true);
        try {
          await addAgreementVendorGuest(signRequest.id, name, email);
          reset();
          modalClose();
        } catch (error) {
          setError([getFirstError(error)]);
          setIsSaving(false);
        }
      }
    };

    const handleRemoveGuest = async (guestId: SignRequestVendorGuest['id']) => {
      try {
        await removeAgreementVendorGuest(signRequest.id, guestId);
        setGuests(guests.filter(({ id }) => id !== guestId));
        displaySuccessMessage(t('RevokeAccessSuccessMessage'));
      } catch {
        displayFailureMessage(t('RevokeAccessErrorMessage'));
      }
    };

    const handleResendUrl = async (guestId: SignRequestVendorGuest['id']) => {
      try {
        await resendAgreementVendorGuestInvitation(signRequest.id, guestId);
        setResentInvitations([...resentInvitations, guestId]);
        displaySuccessMessage(t('InvitationSentSuccessMessage'));
      } catch {
        displayFailureMessage(t('ResendUrlErrorMessage'));
      }
    };

    const { isLoading } = useAsync({
      promise: () => getAgreementVendorGuests(signRequest.id),
      onResolve: ({ data }) => {
        setGuests(data);
      },
    });

    return (
      <>
        <ModalBody {...generateTestId('Container', 'ShareAgreementModal')}>
          <FormContainerStyled>
            {isLoading && <Spinner />}
            {guests.length > 0 && (
              <EmailsWithAccessStyled className="canvas-fill-neutral">
                <FormContainerStyled>
                  <Label>{t('AlreadyWithAccessLabel')}</Label>
                  {guests.map((guest) => (
                    <RowStyled key={guest.user.email}>
                      <ExpandedColumnStyled>
                        <LabelStyled>{guest.name}</LabelStyled>
                        <BodySmallStyled>{guest.user.email}</BodySmallStyled>
                      </ExpandedColumnStyled>
                      <ColumnStyled>
                        {!resentInvitations.includes(guest.id) ? (
                          <Button
                            label={t('ResendUrlButtonLabel')}
                            onClick={() => handleResendUrl(guest.id)}
                            variant="tertiary"
                            iconStart="EmailIcon"
                            color="blue"
                            size="small"
                            rounded={false}
                          />
                        ) : (
                          <Info>{t('InvitationSentLabel')}</Info>
                        )}
                      </ColumnStyled>
                      <ColumnStyled>
                        <Button
                          label={t('RevokeAccessButtonLabel')}
                          onClick={() => handleRemoveGuest(guest.id)}
                          variant="tertiary"
                          iconStart="RemoveIcon"
                          color="red"
                          size="small"
                          rounded={false}
                        />
                      </ColumnStyled>
                    </RowStyled>
                  ))}
                </FormContainerStyled>
              </EmailsWithAccessStyled>
            )}
            <RowStyled>
              <ExpandedColumnStyled>
                <Controller
                  name="name"
                  control={control}
                  rules={FORM_RULE_REQUIRED}
                  render={({ field, fieldState }) => (
                    <InputText
                      label={t('NameLabel')}
                      defaultValue={field.value}
                      onChange={field.onChange}
                      placeholder={t('NamePlaceholder')}
                      errors={
                        fieldState.error ? [fieldState.error.message] : []
                      }
                    />
                  )}
                />
              </ExpandedColumnStyled>
              <ExpandedColumnStyled>
                <Controller
                  name="email"
                  control={control}
                  rules={FORM_RULE_REQUIRED_EMAIL}
                  render={({ field, fieldState }) => (
                    <InputText
                      label={t('EmailLabel')}
                      defaultValue={field.value}
                      onChange={field.onChange}
                      placeholder={t('EmailPlaceholder')}
                      errors={
                        fieldState.error ? [fieldState.error.message] : []
                      }
                    />
                  )}
                />
              </ExpandedColumnStyled>
            </RowStyled>
            <Controller
              name="acknowledge"
              control={control}
              rules={FORM_RULE_REQUIRED}
              render={({ field, fieldState }) => (
                <InputCheckbox
                  label={t('AcknowledgementLabel')}
                  checked={field.value}
                  onChange={field.onChange}
                  errors={
                    fieldState.error ? [i18n.t('Errors.CheckToProceed')] : []
                  }
                />
              )}
            />
            <ErrorList errors={error} />
          </FormContainerStyled>
        </ModalBody>
        <ModalFooter size={modalSize}>
          <Button
            label={t('ShareButtonLabel')}
            onClick={handleShareAgreement}
            disabled={isSaving}
          />
          <Button
            label={i18n.t('common:Actions.Close')}
            onClick={modalDismiss}
            variant="secondary"
            disabled={isSaving}
          />
        </ModalFooter>
      </>
    );
  },
  {
    getHeader: () => t('Header'),
  }
);
