import { openModal } from '../../../components/Utils/Modals/modal.helpers';
import i18n from '../../../i18n/i18n';
import { reloadAngularState } from '../../../services/Reducers/AngularMigration.helpers';
import { noop } from '../../../utils';
import { getFirstItem } from '../../../utils/arrays';
import { ValidatorExecutor } from './AgreementValidators.types';

const hasField = <T extends object, K extends string>(
  value: unknown,
  field: K
): value is T & Record<K, unknown> =>
  typeof value === 'object' && value !== null && field in value;

export const getFirstError = (errorResponse: unknown) => {
  if (hasField(errorResponse, 'data')) {
    const { data } = errorResponse;
    if (Array.isArray(data)) {
      return getFirstItem(data);
    } else if (
      hasField(data, 'non_field_errors') &&
      Array.isArray(data.non_field_errors)
    ) {
      return getFirstItem(data.non_field_errors);
    }
  }
  return '';
};

const t = (id: string) => i18n.t(`Agreements:AgreementValidators.${id}`);

export const genericPostValidator: ValidatorExecutor = async (
  errorResponse: unknown
) => {
  const error = getFirstError(errorResponse);

  let confirmButtonLabel = i18n.t('Actions.Back');
  let resolveAction = noop;
  let header = 'Error';

  if (error) {
    if (error.startsWith('Agreement is not ready to be signed -')) {
      header = t('NotReady');
    } else if (error.startsWith('Some signatures are missing.')) {
      header = t('MissingSignatures');
    } else if (error.startsWith('Document is locked by another user')) {
      header = t('Locked');
    } else if (
      error.startsWith(
        'Signing content is not consistent with the document content'
      )
    ) {
      header = t('Changed');
      confirmButtonLabel = i18n.t('Actions.Reload');
      resolveAction = reloadAngularState;
    }
  }

  await openModal({
    type: 'message_modal',
    header,
    message: <p>{error}</p>,
    confirmButtonLabel,
  })
    .then(resolveAction)
    .catch(noop);
  return { success: false };
};
