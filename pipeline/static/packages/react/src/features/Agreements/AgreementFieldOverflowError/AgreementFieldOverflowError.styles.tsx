import { Colors, FontFamily, Margins } from '../../../styles/global.styles';
import { Body } from '../../../styles/typography';
import styled from '@emotion/styled';

export const ErrorContainer = styled('div')({
  marginTop: 200,
  textAlign: 'center',
});

export const HeaderFrame = styled('div')({
  border: `1px dashed ${Colors.blue2}`,
  borderRadius: 8,
  boxSizing: 'border-box',
  display: 'inline-block',
  margin: `${Margins.large}px auto`,
  padding: '8px 18px',
  position: 'relative',
  WebkitFontSmoothing: 'antialiased',
  width: 'auto',
});

export const HeaderBackground = styled('div')({
  background: Colors.blue4,
  borderRadius: 8,
  boxSizing: 'border-box',
  border: `1px solid ${Colors.blue2}`,
  height: 76,
  left: -1,
  maxWidth: 'calc(100% + 2px)',
  position: 'absolute',
  top: -1,
  width: 328,
  zIndex: -100,
});

export const Header = styled('div')({
  color: Colors.grey2,
  display: 'inline',
  fontFamily: FontFamily.default,
  fontSize: 36,
  fontWeight: 300,
  lineHeight: '58px',
  MozOsxFontSmoothing: 'grayscale',
  textAlign: 'center',
  WebkitFontSmoothing: 'antialiased',
  width: 'auto',
  zIndex: 100,
});

export const HeaderOutsideText = styled('span')({
  color: Colors.grey3,
});

export const Text = styled(Body)({
  maxWidth: 680,
  margin: `${Margins.small}px auto ${Margins.large}px auto`,
});
