import { AlertAction } from '../../components/Alert/Alert.types';

export class MessageAction {
  type: 'external_url';
  label: string;
  config: any;
}

export class Message {
  id: number;
  type: string;
  text: string;
  actions: MessageAction[];

  constructor(data) {
    Object.assign(this, data);
  }

  getFirstAlertAction(): AlertAction | null {
    if (!Array.isArray(this.actions) || this.actions.length === 0) {
      return null;
    }
    const onActionClick = (action: MessageAction): (() => void | null) => {
      return () => {
        if (action.type === 'external_url') {
          window.open(action.config.location, '_blank').focus();
        }
        return null;
      };
    };
    // at the moment we're handling only first action returned by API
    const action = this.actions[0];
    const allowedActionTypes = ['external_url'];
    if (!allowedActionTypes.includes(action.type)) {
      return null;
    }
    return {
      label: action.label,
      onClick: onActionClick(action),
    };
  }
}
