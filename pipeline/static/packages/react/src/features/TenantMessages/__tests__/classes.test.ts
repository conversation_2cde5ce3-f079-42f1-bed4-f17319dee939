import { Message } from '../classes';

test.each([{ id: 1 }, { id: 1, text: 'Test test', type: 'alert-warning' }])(
  'Message instance from %s',
  (data) => {
    const message = new Message(data);

    for (const field in data) {
      expect(message[field]).toBe(data[field]);
    }
  }
);

describe('Message.getFirstAlertAction', () => {
  let mockedWindowOpen;

  beforeEach(() => {
    mockedWindowOpen = jest.fn();
    mockedWindowOpen.mockImplementation(() => ({
      focus: jest.fn(),
    }));
    const windowSpy = jest.spyOn(global, 'window', 'get');
    // @ts-ignore:
    windowSpy.mockImplementation(() => ({
      open: mockedWindowOpen,
    }));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('no actions property', () => {
    const message = new Message({});
    expect(message.getFirstAlertAction()).toBeNull();
  });

  test('empty actions', () => {
    const message = new Message({ actions: [] });
    expect(message.getFirstAlertAction()).toBeNull();
  });

  test.each([false, true, undefined, {}, 1, 'broken'])(
    'bad actions type for %s',
    (actions) => {
      const message = new Message({ actions });
      expect(message.getFirstAlertAction()).toBeNull();
    }
  );

  test('correct one action', () => {
    const actions = [
      {
        type: 'external_url',
        label: 'Test',
        config: { location: 'http://shortlist-test.net' },
      },
    ];
    const message = new Message({ actions });

    const result = message.getFirstAlertAction();
    result.onClick();

    expect(result.label).toBe('Test');
    expect(mockedWindowOpen).toHaveBeenCalledTimes(1);
    expect(mockedWindowOpen).toHaveBeenCalledWith(
      'http://shortlist-test.net',
      '_blank'
    );
  });

  test('correct multiple', () => {
    const actions = [
      { type: 'external_url', label: 'Test1', config: { location: 'test' } },
      { type: 'external_url', label: 'Test2', config: {} },
      { type: 'external_url', label: 'Test3', config: {} },
    ];
    const message = new Message({ actions });

    const result = message.getFirstAlertAction();
    result.onClick();

    expect(result.label).toBe('Test1');
    expect(mockedWindowOpen).toHaveBeenCalledTimes(1);
    expect(mockedWindowOpen).toHaveBeenCalledWith('test', '_blank');
  });

  test('unknown action', () => {
    const actions = [
      {
        type: 'unknown_action',
        label: 'Test',
        config: { location: 'http://shortlist-test.net' },
      },
    ];
    const message = new Message({ actions });

    const result = message.getFirstAlertAction();

    expect(result).toBeNull();
  });
});
