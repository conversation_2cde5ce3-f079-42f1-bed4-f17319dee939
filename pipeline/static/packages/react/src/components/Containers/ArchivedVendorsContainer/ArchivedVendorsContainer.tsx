import i18n from '../../../i18n/i18n';
import Vendors from '../../../services/Api/Vendors';
import { NotificationStateAction } from '../../../services/Reducers/Notifications.reducer';
import { reduxStore } from '../../../services/Redux';
import { UserPermissionsService } from '../../../services/Shortlist/User/UserPermissionsService';
import DataTable from '../../DataTable/DataTable';
import { DataTableContextType } from '../../DataTable/DataTable.contexts';
import DataTableZeroState from '../../DataTable/DataTableZeroState';
import Button from '../../FormElements/Button/Button';
import InputSearch from '../../FormElements/InputSearch/InputSearch';
import ElasticSearch from '../../Search/ElasticSearch/ElasticSearch';
import { SearchParams } from '../../Search/ElasticSearch/ElasticSearch.types';
import { AllColumnTypes } from '../../Utils/List/Column/Column';
import { ListRow } from '../../Utils/List/List.types';
import {
  getVendorLocation,
  getVendorName,
} from '../../Vendors/vendors.helpers';
import React from 'react';

const goToVendorDetailsPage = (data: ListRow) => {
  reduxStore.dispatch({
    type: 'STATE_GOTO',
    stateName: 'app.partner-details.profile',
    stateParams: {
      slug: data.slug,
    },
  });
};

const ArchivedVendorsContainer = () => {
  const tableColumns = [
    {
      id: 1,
      type: 'vendor',
      name: i18n.t('Table.Partner'),
      getVendor: (data) => data,
      getVendorLink: goToVendorDetailsPage,
      styleProps: ['Header--Mobile'],
    },
    {
      id: 2,
      type: 'vendor_type',
      name: i18n.t('Table.Type'),
      getVendorType: (data) => data.vendor_type,
    },
    {
      id: 3,
      type: 'text',
      name: i18n.t('Table.Location'),
      getCellValue: getVendorLocation,
      styleProps: ['MinWidth100'],
    },
    {
      id: 4,
      type: 'vendor_skills',
      name: i18n.t('Table.Skills'),
      getVendorSkills: (data) => data.services,
    },
    {
      id: 5,
      type: 'text',
      name: i18n.t('Table.Groups'),
      getCellValue: (data) => data.groups?.map((i) => i.name).join(', '),
      styleProps: ['MinWidth100'],
    },
    ...(UserPermissionsService.canArchiveVendors()
      ? [
          {
            id: 6,
            type: 'button',
            name: '',
            button: (
              <Button
                label={i18n.t('Actions.Restore')}
                iconStart="ArchivedNoIcon"
                variant="secondary"
                size="small"
                rounded={false}
              />
            ),
            getCellButtonOnClick: (
              data,
              event,
              context: DataTableContextType
            ) => {
              Vendors.restore(data.slug);
              context.removeItemById(data.id);
              reduxStore.dispatch({
                type: NotificationStateAction.ADD_NOTIFICATION,
                data: {
                  tag: 'alert-success',
                  message: i18n.t('Messages.PartnerRestored', {
                    vendorName: getVendorName(data),
                  }),
                },
              });
              event.stopPropagation();
            },
            styleProps: ['MinWidth100'],
          },
        ]
      : []),
  ];

  return (
    <ElasticSearch
      initSearchParams={{
        archived: true,
        query: '',
        sort: 'alphabetical',
        filters: [],
        page: 0,
        terms: false,
        size: 12,
      }}
      searchCallback={(params: SearchParams) =>
        Vendors.getSearchResults(params).then((data) => data)
      }
      inputSearchComponent={({ initialValue, onChange, totalItems }) => (
        <InputSearch
          style={{ width: 300 }}
          initialValue={initialValue}
          placeholder={i18n.t('Table.SearchPartners', { count: totalItems })}
          onChange={onChange}
        />
      )}
      listComponent={
        <DataTable
          items={[]}
          columns={tableColumns as AllColumnTypes[]}
          onRowClick={goToVendorDetailsPage}
        />
      }
      zeroStateComponent={
        <DataTableZeroState
          header={i18n.t('Table.NoArchivedPartners')}
          icon="SearchResults"
        />
      }
      noSearchResultsComponent={
        <DataTableZeroState
          header={i18n.t('Table.NoResultsFound')}
          icon="SearchResults"
        />
      }
    />
  );
};

export default ArchivedVendorsContainer;
