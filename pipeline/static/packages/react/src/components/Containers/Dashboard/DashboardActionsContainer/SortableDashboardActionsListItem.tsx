import { Colors } from '../../../../styles/global.styles';
import CellFactory from '../../../Utils/List/Cell/CellFactory';
import { AllColumnTypes } from '../../../Utils/List/Column/Column';
import { getItemStyle } from '../../../Utils/List/DroppableTableBody';
import { InfoBoxStyled } from '../../../Utils/List/InfoBox';
import { Tr } from '../../../Utils/List/List.styles';
import { ListRow } from '../../../Utils/List/List.types';
import { DashboardTemplate, DashboardWidgetProps } from '../dashboard.types';
import DashboardEditItem from './DashboardEditItem';
import React from 'react';
import { DraggableProvided, DraggableStateSnapshot } from '@hello-pangea/dnd';

const SortableDashboardActionsListItem = ({
  item,
  provided,
  snapshot,
  columns,
  onRowClick,
  editingItemId,
  templateList = [],
  onChange,
  onCancel,
}: {
  item: DashboardWidgetProps;
  provided: DraggableProvided;
  snapshot: DraggableStateSnapshot;
  columns: AllColumnTypes[];
  onRowClick: (rowData: ListRow) => void;
  editingItemId?: number;
  templateList: DashboardTemplate[];
  onChange: (data: DashboardWidgetProps) => void;
  onCancel: () => void;
}) => (
  <>
    <Tr
      onClick={() => onRowClick?.(item)}
      ref={provided.innerRef}
      {...provided.draggableProps}
      style={getItemStyle(snapshot.isDragging, provided.draggableProps.style)}
    >
      {columns.map((column: AllColumnTypes) => (
        <React.Fragment key={`${column.id}__${item.id}`}>
          <CellFactory column={column} item={item} />
        </React.Fragment>
      ))}
    </Tr>
    {item.id === editingItemId && (
      <tr>
        <td colSpan={99}>
          <InfoBoxStyled style={{ backgroundColor: Colors.white }}>
            <DashboardEditItem
              data={item}
              templateList={templateList}
              onChange={onChange}
              onCancel={onCancel}
            />
          </InfoBoxStyled>
        </td>
      </tr>
    )}
    <tr>
      <td style={{ height: 10 }} />
    </tr>
  </>
);
export default SortableDashboardActionsListItem;
