import Lists from '../../../services/Api/Lists';
import Vendors from '../../../services/Api/Vendors';
import { Vendor } from '../../../types/vendor';
import { makeCancelable } from '../../../utils/other';
import { SortDir } from '../../Search/ElasticSearch/ElasticSearch.types';
import { openModal } from '../../Utils/Modals/modal.helpers';
import Spinner from '../../Utils/Spinner/Spinner';
import React, { useEffect, useState } from 'react';
import { goToErrorState } from '../../../services/Reducers/AngularMigration.helpers';

const SharedProfileContainer = ({
  listKey,
  vendorSlug,
}: {
  listKey: string;
  vendorSlug?: string;
}) => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const cancelablePromise = makeCancelable(Lists.getListByKey(listKey));
    cancelablePromise.promise
      .then(({ data: vendorList }) => {
        setIsLoading(false);
        Vendors.getSearchResults(
          {
            query: '',
            sort: { alphabetical: SortDir.ASC },
            filters: [],
            page: 0,
            terms: false,
            size: 24,
            key: listKey,
          },
          true
        )
          .then(({ hits }) => {
            openModal(
              {
                type: 'quick_view',
                vendor: (vendorSlug
                  ? hits.find((i) => i.slug === vendorSlug)
                  : hits[0]) as Vendor,
                vendorList,
                publicListKey: listKey,
              },
              undefined,
              'full-screen'
            );
          })
          .catch(() => {
            //
          });
      })
      .catch(goToErrorState);
    return () => cancelablePromise.cancel();
  }, []);

  if (isLoading) {
    return <Spinner />;
  }

  return null;
};

export default SharedProfileContainer;
