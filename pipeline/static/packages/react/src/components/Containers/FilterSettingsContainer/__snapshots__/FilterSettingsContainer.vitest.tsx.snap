// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`FilterSettingsContainer -  >  check with Snapshot 1`] = `
.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class:hover .checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class {
  margin: 1px 0 -1px 6px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  color: #303757;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  fill: #000000;
  width: 28px;
  height: 28px;
  fill-rule: evenodd;
  vertical-align: middle;
  position: absolute;
  top: 10px;
  right: 5px;
  fill: #465AB6;
  pointer-events: none;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 20px;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

@media (max-width: 768px) {
  .emotion-class {
    -webkit-box-flex-flow: row wrap;
    -webkit-flex-flow: row wrap;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    margin-bottom: 0;
  }
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 30px;
  line-height: 40px;
  overflow-wrap: anywhere;
  margin-right: 20px;
}

@media (max-width: 768px) {
  .emotion-class {
    margin-bottom: 10px;
  }
}

.emotion-class {
  width: 100%;
  padding-top: 40px;
  margin-bottom: 180px;
  border-top: 1px solid #E4E5EB;
}

.emotion-class {
  margin-bottom: 40px;
}

.emotion-class {
  border-bottom: 1px solid #E4E5EB;
}

.emotion-class {
  white-space: nowrap;
  overflow-x: hidden;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  min-height: 24px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}

.emotion-class ul {
  white-space: nowrap;
  list-style: none;
  padding: 0;
  margin: 0;
  overflow-x: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class ul li {
  display: inline-block;
}

.emotion-class .dropdown {
  text-align: right;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  height: 50px;
  cursor: pointer;
  margin-right: 25px;
  overflow-x: hidden;
}

.emotion-class::after {
  content: "";
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 3px;
  background-color: #465AB6;
}

.emotion-class .tabBarLockIcon {
  fill: #303757;
}

.emotion-class .tabBarTitleBadgeLabel {
  background-color: #465AB6;
}

.emotion-class:hover .tabBarLockIcon {
  fill: #465AB6;
}

.emotion-class:hover .tabBarTitleLabel {
  color: #465AB6;
}

.emotion-class:hover .tabBarTitleBadgeLabel {
  background-color: #465AB6;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #303757;
  font-family: "Montserrat",sans-serif;
  text-transform: uppercase;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  height: 50px;
  cursor: pointer;
  margin-right: 25px;
  overflow-x: hidden;
}

.emotion-class:hover .tabBarLockIcon {
  fill: #465AB6;
}

.emotion-class:hover .tabBarTitleLabel {
  color: #465AB6;
}

.emotion-class:hover .tabBarTitleBadgeLabel {
  background-color: #465AB6;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #A6ABBF;
  font-family: "Montserrat",sans-serif;
  text-transform: uppercase;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 16px;
  line-height: 25px;
  color: #000000;
  margin-bottom: 40px;
}

.emotion-class {
  position: relative;
}

.emotion-class.tableStart:before,
.emotion-class.tableEnd:after {
  opacity: 1;
  z-index: 2;
}

.emotion-class:before,
.emotion-class:after {
  opacity: 0;
  pointer-events: none;
  content: "";
  display: block;
  position: absolute;
  top: 0;
  height: 100%;
  width: 10px;
  -webkit-transition: opacity 0.3s ease-in-out;
  transition: opacity 0.3s ease-in-out;
}

.emotion-class:before {
  left: 0;
  background: linear-gradient(90deg, rgba(8, 10, 26, 0.1) 0%, rgba(8, 10, 26, 0) 80%);
  border-radius: 4px 0px 0px 4px;
}

.emotion-class:after {
  right: 0;
  background: linear-gradient(270deg, rgba(8, 10, 26, 0.1) 0%, rgba(8, 10, 26, 0) 80%);
  border-radius: 0px 4px 4px 0px;
}

.emotion-class {
  overflow-x: auto;
  border-radius: 4px;
}

.emotion-class {
  border-collapse: separate;
  width: 100%;
  height: 1px;
}

.emotion-class tr:hover td {
  cursor: pointer;
  background-color: #F5F6FA;
}

.emotion-class tr:hover td .displayOnTrHover {
  visibility: visible;
}

.emotion-class tr:hover td .displayOnTrHover svg {
  visibility: visible;
}

.emotion-class tr:hover td .inlineEditing {
  opacity: 1;
  visibility: visible;
}

.emotion-class tr td {
  height: 100%;
}

.emotion-class tr td .inlineEditing {
  -webkit-transition: opacity 0.3s ease-in-out 0.1s;
  transition: opacity 0.3s ease-in-out 0.1s;
}

.emotion-class.mobileView tr {
  border: 1px solid #E4E5EB;
  border-color: #E4E5EB;
  border-radius: 4px;
  padding: 20px;
}

.emotion-class.mobileView td {
  min-height: 1px;
  background-color: #FFFFFF;
  border: 0;
  margin: 0;
  padding: 0!important;
  margin-bottom: 12px;
  font-size: 16px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  font-size: 14px;
  text-align: left;
  padding: 10px;
  border-width: 1px 1px 0 0;
  border-style: solid;
  border-color: #E4E5EB;
  background-color: #F7F7F7;
  box-sizing: border-box;
}

.emotion-class:first-of-type {
  border-top-left-radius: 4px;
  border-left: 1px solid #E4E5EB;
}

.emotion-class:last-of-type {
  border-top-right-radius: 4px;
}

.emotion-class:focus-within {
  background-color: #F5F6FA;
}

.emotion-class:first-of-type td {
  border-top: 1px solid #E4E5EB;
}

.emotion-class:last-of-type td:first-of-type {
  border-bottom-left-radius: 4px;
}

.emotion-class:last-of-type td:last-of-type {
  border-bottom-right-radius: 4px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  vertical-align: top;
  border-bottom: 1px solid #E4E5EB;
  border-right: 1px solid #E4E5EB;
  padding: 15px 10px;
}

.emotion-class:first-of-type {
  border-left: 1px solid #E4E5EB;
}

.emotion-class:last-of-type {
  border-right: 1px solid #E4E5EB;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  vertical-align: top;
  border-bottom: 1px solid #E4E5EB;
  border-right: 1px solid #E4E5EB;
  padding: 15px 10px;
  cursor: text;
}

.emotion-class:first-of-type {
  border-left: 1px solid #E4E5EB;
}

.emotion-class:last-of-type {
  border-right: 1px solid #E4E5EB;
}

.emotion-class p {
  position: relative;
}

.emotion-class p svg {
  position: absolute;
  right: 3px;
  top: -5px;
  display: none;
  cursor: pointer;
}

.emotion-class:hover p svg {
  display: block;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  display: inline-block;
  width: 100%;
  color: #A6ABBF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class:hover {
  color: #A6ABBF;
}

.emotion-class {
  fill: #465AB6;
  width: 29px;
  height: 29px;
  fill-rule: evenodd;
  vertical-align: middle;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  vertical-align: middle;
  border-bottom: 1px solid #E4E5EB;
  border-right: 1px solid #E4E5EB;
  padding: 15px 10px;
  width: 150px;
}

.emotion-class:first-of-type {
  border-left: 1px solid #E4E5EB;
}

.emotion-class:last-of-type {
  border-right: 1px solid #E4E5EB;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  min-height: 25px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class:hover>.checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6!important;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
  cursor: not-allowed;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class[disabled] svg path:nth-of-type(1) {
  fill: #FFFFFF;
}

.emotion-class>.checkboxOffIcon path:nth-of-type(2) {
  fill: #E4E5EB;
}

.emotion-class>.checkboxOnIcon path:nth-of-type(1) {
  fill: #E4E5EB;
}

.emotion-class>.checkboxOnIcon path:nth-of-type(1) {
  fill: #C5C8D5!important;
}

.emotion-class>.checkboxOnIcon path:nth-of-type(2) {
  fill: #FFFFFF;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  display: inline-block;
  width: 100%;
  color: #303757;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class:hover {
  color: #303757;
}

.emotion-class {
  position: fixed;
  bottom: 0;
  width: 100%;
  padding: 50px 0;
  background-color: #FFFFFF;
  border-top: 1px solid #E4E5EB;
}

.emotion-class button:first-of-type {
  margin-right: 20px;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-base);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: #FFFFFF;
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(1.15);
  filter: brightness(1.15);
}

.emotion-class {
  color: #FFFFFF;
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div>
  <div
    style="position: sticky; top: 0px; padding: 10px 2rem; width: 100%; z-index: 10000; background-color: white; margin: -32px 0px 10px -2rem; box-shadow: rgb(0 0 0 / 10%) 0px -1px 0px 0px inset; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #E4E5EB;"
  >
    <div
      style="display: flex; justify-content: space-between;"
    >
      <div>
        <span
          class="emotion-class"
        >
          Tenant features
        </span>
        <div
          style="margin-top: 5px;"
        >
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    checked=""
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                    classname="checkboxOnIcon css-1c1jprp"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    availability_calendar
                  </span>
                </span>
              </label>
            </div>
          </span>
          <span
            style="margin-right: 10px;"
          >
            <div
              class="emotion-class"
            >
              <label
                class="emotion-class"
                data-testid="InputCheckbox"
              >
                <span
                  class="emotion-class"
                >
                  <input
                    checked=""
                    type="checkbox"
                  />
                  <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                    classname="checkboxOnIcon css-1c1jprp"
                  />
                </span>
                <span
                  class="emotion-class"
                >
                  <span
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    projects_and_tasks
                  </span>
                </span>
              </label>
            </div>
          </span>
        </div>
      </div>
      <div
        style="white-space: nowrap;"
      >
        <span
          class="emotion-class"
        >
          Logged user
        </span>
        <div
          style="margin-top: 5px; width: 130px;"
        >
          <div
            class="emotion-class"
          >
            <select
              class="emotion-class"
            >
              <option
                data-testid="SelectOption_Staff"
                value="staff"
              >
                Staff
              </option>
              <option
                data-testid="SelectOption_BuyerAdmin"
                value="buyer-admin"
              >
                Buyer Admin
              </option>
              <option
                data-testid="SelectOption_BuyerRegular"
                value="buyer-regular"
              >
                Buyer Regular
              </option>
              <option
                data-testid="SelectOption_Vendor"
                value="vendor"
              >
                Vendor
              </option>
            </select>
            <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
              class="emotion-class"
              size="28"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="container"
    data-testid="State"
  >
    <div
      class="emotion-class"
      data-testid="ActionBar"
    >
      <h2
        class="emotion-class"
        data-testid="ActionBar_Title"
      >
        Filters
      </h2>
    </div>
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      >
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <ul>
              <li>
                <ul>
                  <a
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    <span
                      class="emotion-class"
                    >
                      <span
                        class="tabBarTitleLabel emotion-class"
                      >
                        Directory
                      </span>
                    </span>
                  </a>
                </ul>
              </li>
              <li>
                <ul>
                  <a
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    <span
                      class="emotion-class"
                    >
                      <span
                        class="tabBarTitleLabel emotion-class"
                      >
                        Tasks
                      </span>
                    </span>
                  </a>
                </ul>
              </li>
              <li>
                <ul>
                  <a
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    <span
                      class="emotion-class"
                    >
                      <span
                        class="tabBarTitleLabel emotion-class"
                      >
                        Job openings
                      </span>
                    </span>
                  </a>
                </ul>
              </li>
              <li>
                <ul>
                  <a
                    aria-label=""
                    class=" emotion-class"
                    data-mui-internal-clone-element="true"
                  >
                    <span
                      class="emotion-class"
                    >
                      <span
                        class="tabBarTitleLabel emotion-class"
                      >
                        Contracts
                      </span>
                    </span>
                  </a>
                </ul>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <p
        class="emotion-class"
      >
        Choose which partner properties should be available as filters
      </p>
      <div
        data-testid="Search_DataTable"
      >
        <div
          class=" emotion-class"
          data-testid="Search_DataTable_Table"
        >
          <div
            class="emotion-class"
            style="display: flex;"
          >
            <div
              class="trigger"
              style="position: relative; left: 1px;"
            />
            <table
              cellpadding="0"
              cellspacing="0"
              class=" emotion-class"
            >
              <thead>
                <tr>
                  <th
                    class="emotion-class"
                    data-testid="DataTable_ColumnHeader_Field name"
                    style="color: var(--statuses-status-processed);"
                  >
                    Field name
                  </th>
                  <th
                    class="emotion-class"
                    data-testid="DataTable_ColumnHeader_Filter custom label"
                    style="color: var(--statuses-status-processed); width: 300px;"
                  >
                    Filter custom label
                  </th>
                  <th
                    class="emotion-class"
                    data-testid="DataTable_ColumnHeader_Enable"
                    style="color: var(--statuses-status-processed); text-align: center;"
                  >
                    Enable
                  </th>
                  <th
                    class="emotion-class"
                    data-testid="DataTable_ColumnHeader_Always visible"
                    style="color: var(--statuses-status-processed); text-align: center;"
                  >
                    Always visible
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    Radius
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                          classname="checkboxOffIcon css-1dmky3q"
                          fill="#D1D6ED"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    Compliance
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                        disabled=""
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    Staffing supplier
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                        disabled=""
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    Rank
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                          classname="checkboxOffIcon css-1dmky3q"
                          fill="#D1D6ED"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    Postal code
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                        disabled=""
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    Current availability
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                          classname="checkboxOffIcon css-1dmky3q"
                          fill="#D1D6ED"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    City
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                        disabled=""
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    State
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                          classname="checkboxOffIcon css-1dmky3q"
                          fill="#D1D6ED"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    Job opening
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                        disabled=""
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    Administrative
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Administrative
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                        disabled=""
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    Status
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                        disabled=""
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    Tags
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                          classname="checkboxOffIcon css-1dmky3q"
                          fill="#D1D6ED"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    Groups
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                          classname="checkboxOffIcon css-1dmky3q"
                          fill="#D1D6ED"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    Skills
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                          classname="checkboxOffIcon css-1dmky3q"
                          fill="#D1D6ED"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    Partner type
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                        disabled=""
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    Country
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                          classname="checkboxOffIcon css-1dmky3q"
                          fill="#D1D6ED"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    Address
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                          classname="checkboxOffIcon css-1dmky3q"
                          fill="#D1D6ED"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    CF convert_text
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      CF convert_text
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                        disabled=""
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    CF skopiowany
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      CF skopiowany
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                        disabled=""
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    Do you have a drone?
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Do you have a drone?
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                          classname="checkboxOffIcon css-1dmky3q"
                          fill="#D1D6ED"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    Working Hours
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                          classname="checkboxOffIcon css-1dmky3q"
                          fill="#D1D6ED"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    Dodaj obrazek
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                          classname="checkboxOffIcon css-1dmky3q"
                          fill="#D1D6ED"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    CV
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                          classname="checkboxOffIcon css-1dmky3q"
                          fill="#D1D6ED"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    Kopia prawa jazdy
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                          classname="checkboxOffIcon css-1dmky3q"
                          fill="#D1D6ED"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    3 dokument
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                          classname="checkboxOffIcon css-1dmky3q"
                          fill="#D1D6ED"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    4 dokument
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                          classname="checkboxOffIcon css-1dmky3q"
                          fill="#D1D6ED"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 100px;"
                  >
                    Availability
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <p
                      class="emotion-class"
                    >
                      Add text...
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m31.5002%2012.1213l29.1215%2014.5l33.6215%2019l36.0002%2016.6213c37.1718%2015.4497%2037.1718%2013.5502%2036.0002%2012.3787l35.7428%2012.1213c34.5713%2010.9497%2032.6718%2010.9497%2031.5002%2012.1213z'/%3e%3cpath%20d='m17.1215%2035.5l31.6215%2020.9989l27.1215%2016.5l12.6215%2031l11.1215%2037l17.1215%2035.5z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#465AB6"
                      />
                    </p>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          checked=""
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='%23465ab6'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m8.57535%2013.5l5.20996%2010.1346l6.15227%209.19231l8.57535%2011.6154l13.6907%206.5l14.633%207.44231l8.57535%2013.5z'%20fill='white'/%3e%3c/svg%3e
                          classname="checkboxOnIcon css-1c1jprp"
                        />
                      </span>
                    </div>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 10px;"
                  >
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="emotion-class"
                      >
                        <input
                          type="checkbox"
                        />
                        <data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewbox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201z'%20fill='white'/%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m2.9908%201h17.0092c18.1087%201%2019%201.89821%2019%202.9908v17.0092c19%2018.1087%2018.1018%2019%2017.0092%2019h2.9908c1.89131%2019%201%2018.1018%201%2017.0092v2.9908c1%201.89131%201.89821%201%202.9908%201zm17%203v17h3v3h17z'/%3e%3c/svg%3e
                          classname="checkboxOffIcon css-1dmky3q"
                          fill="#D1D6ED"
                        />
                      </span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
            <div
              class="trigger"
              style="position: relative; right: 1px;"
            />
          </div>
        </div>
      </div>
      <div
        class="emotion-class"
      >
        <button
          class=" emotion-class"
        >
          <span
            class="emotion-class"
          >
            Save
          </span>
        </button>
      </div>
    </div>
  </div>
</div>
`;
