import { composeStories } from '@storybook/react';
import { fireEvent, render, screen, within } from '@testing-library/react';
import { viMockIntersectionObserver } from '../../../../utils/vitest.helpers';
import * as stories from '../AllPartnersContainer.stories';

/**
 * Use AllPartners story
 * - click 1st row add to group
 * - expect group name to be displayed in the 1st row
 * - click 2nd row add to group
 * - expect group name to be displayed in the 2nd row
 * - expect group name to be still displayed in the 1st row
 */

const addPartnerToGroup = async (rows: HTMLElement[], rowIndex: number) => {
  const row = rows[rowIndex];
  fireEvent.mouseOver(row);

  const rowSelect = await within(row).findByTestId('CellRowSelect');
  fireEvent.click(rowSelect);

  expect(await screen.findByText('1 partner selected')).toBeVisible();

  const bulkAddToGroup = within(
    await screen.findByTestId('BulkActions')
  ).getByText('Add to group');
  fireEvent.click(bulkAddToGroup);

  const modalContainer = screen.getByTestId('AddToGroupModal_Container');
  expect(modalContainer).toBeVisible();

  const autocomplete = (await screen.findByRole(
    'combobox'
  )) as HTMLSelectElement;
  expect(autocomplete.value).toEqual('');

  fireEvent.keyDown(autocomplete, { key: 'ArrowDown' });
  expect(await screen.findByText('Grupa Testowa')).toBeVisible();
  fireEvent.keyDown(autocomplete, { key: 'Enter' });

  const addButton = screen.getByTestId('AddToGroupModal_AddButton');
  fireEvent.click(addButton);

  expect(await within(row).findByText(/Grupa Testowa/)).toBeVisible();
};

test('On AllPartners story - check for persistent multiple row updates', async () => {
  viMockIntersectionObserver();
  const { Default: AllPartners } = composeStories(stories);
  render(<AllPartners />);
  const listView = await screen.findByTestId('IconRadioGroup-List');
  fireEvent.click(listView);
  const rows = await screen.findAllByRole('row');

  // Add first partner (row 2) to group
  await addPartnerToGroup(rows, 2);

  // Add second partner (row 3) to group
  await addPartnerToGroup(rows, 3);

  // Check both rows have the group name
  expect(await within(rows[2]).findByText(/Grupa Testowa/)).toBeVisible();
  expect(await within(rows[3]).findByText(/Grupa Testowa/)).toBeVisible();
});
