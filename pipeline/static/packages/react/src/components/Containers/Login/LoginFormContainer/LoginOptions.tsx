import i18n from '../../../../i18n/i18n';
import { Colors } from '../../../../styles/global.styles';
import { DisplaySmall } from '../../../../styles/typography';
import { LoginOptionsData } from '../../../../types/user';
import { generateTestId } from '../../../../utils/test.utils';
import Button from '../../../FormElements/Button/Button';
import styled from '@emotion/styled';
import React from 'react';
import { create } from 'zustand';

const t = (id: string) => i18n.t(`Login:LoginOptions.${id}`);

const Divider = styled('div')({
  display: 'flex',
  width: '100%',
  alignItems: 'center',
});

const DividerLine = styled('div')({
  height: '1px',
  background: Colors.grey3,
  width: '100%',
});

const DividerWord = styled(DisplaySmall)({
  margin: '0 10px',
  color: Colors.grey2,
});

const ButtonStyled = styled('div')({
  button: {
    width: '100%',
  },
});

const useLoginOptionsStore = create<{
  hoverItemId: string;
  setHoverItemId: (hoverItemId: string) => void;
}>((set) => ({
  hoverItemId: null,
  setHoverItemId: (hoverItemId) => set({ hoverItemId }),
}));

const LoginOptions = ({ loginOptions }: { loginOptions: LoginOptionsData }) => {
  const hoverItemId = useLoginOptionsStore((state) => state.hoverItemId);
  const setHoverItemId = useLoginOptionsStore(
    ({ setHoverItemId }) => setHoverItemId
  );
  return (
    <>
      {loginOptions.google_oauth_url && (
        <ButtonStyled
          onMouseOver={() => setHoverItemId('google')}
          onMouseLeave={() => setHoverItemId('')}
        >
          <Button
            label={`${t('ContinueWith')} Google`}
            variant={hoverItemId === 'google' ? 'primary' : 'secondary'}
            iconStart={
              hoverItemId === 'google' ? 'GoogleWhiteIcon' : 'GoogleColorIcon'
            }
            onClick={() =>
              window.location.replace(loginOptions.google_oauth_url)
            }
            testId={generateTestId('Google', 'LoginOption')['data-testid']}
          />
        </ButtonStyled>
      )}

      {loginOptions.sso?.map((ssoOption) => {
        const key = ssoOption.name.toLowerCase().replace(/\s/g, '-');
        const isButtonHover = hoverItemId === key;

        return (
          <ButtonStyled
            key={key}
            onMouseOver={() => setHoverItemId(key)}
            onMouseLeave={() => setHoverItemId('')}
          >
            <a href={ssoOption.url} target="_self">
              <Button
                label={`${t('LoginWith')} ${ssoOption.name}`}
                variant={isButtonHover ? 'primary' : 'secondary'}
                customImgUrl={
                  isButtonHover ? ssoOption.icon_hover : ssoOption.icon
                }
              />
            </a>
          </ButtonStyled>
        );
      })}

      {loginOptions.password &&
        (loginOptions.google_oauth_url || loginOptions.sso.length > 0) && (
          <Divider>
            <DividerLine />
            <DividerWord>{t('Or')}</DividerWord>
            <DividerLine />
          </Divider>
        )}
    </>
  );
};

export default LoginOptions;
