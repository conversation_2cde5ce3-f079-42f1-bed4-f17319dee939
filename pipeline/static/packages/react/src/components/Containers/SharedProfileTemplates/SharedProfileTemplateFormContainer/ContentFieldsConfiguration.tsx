import {
  CustomField,
  CustomFieldType,
} from '../../../CustomFields/CustomFields';
import { mapCustomFieldToColumn } from '../../../Utils/List/List.helpers';
import {
  composeSharableDummyFieldKey,
  composeSharableField<PERSON>ey,
  isSharableDummyFieldKey,
  isSharableFieldKey,
} from '../../../Utils/SharableProfiles/SharableProfiles.helpers';
import SharableContentConfiguration from './SharableContentConfiguration';
import produce from 'immer';
import React, { useEffect, useState } from 'react';

interface ContentConfiguration {
  id: string;
  isVisible: boolean;
  coreVariableName: string;
  _translatedName: string;
  type?: CustomFieldType;
}

export const ContentFieldsConfiguration = ({
  fields: initialFields,
  defaultFields,
  customFields,
  sharableFields,
  onFieldsChange,
  fieldLabels,
}: {
  fields: string[];
  defaultFields: string[];
  customFields: CustomField[];
  sharableFields: CustomField[];
  onFieldsChange: (fields: ContentConfiguration[]) => void;
  fieldLabels: { [key: string]: string };
}) => {
  const [allAvailableFields, setAllAvailableFields] =
    useState<ContentConfiguration[]>();

  useEffect(() => {
    if (Array.isArray(customFields)) {
      const elements: ContentConfiguration[] = initialFields.map((i) => {
        return {
          id: i,
          isVisible: defaultFields.includes(i),
          coreVariableName: i,
          _translatedName: fieldLabels[i] || i,
        };
      });

      customFields.forEach((customField: CustomField) => {
        const id = `custom_${customField.id}`;
        try {
          // This will throw error if CF is not supported
          mapCustomFieldToColumn(customField);
          elements.push({
            id,
            isVisible: defaultFields.includes(id),
            coreVariableName: id,
            _translatedName: `${customField.brief_label || customField.label}`,
          });
        } catch (e) {
          //
        }
      });

      let dummyIndex = 0;
      sharableFields.forEach((sharableField: CustomField) => {
        let id = '';
        if (sharableField.id) {
          id = composeSharableFieldKey(sharableField.id);
        } else {
          if (sharableField.description) {
            id = sharableField.description;
          } else {
            id = composeSharableDummyFieldKey(dummyIndex);
            dummyIndex++;
          }
        }
        elements.push({
          id,
          isVisible: true,
          coreVariableName: id,
          _translatedName: `${
            sharableField.brief_label || sharableField.label
          }`,
          type: sharableField.type,
        });
      });

      sortVisibleFields(defaultFields, elements);
    }
  }, [
    customFields,
    JSON.stringify(sharableFields),
    JSON.stringify(defaultFields),
  ]);

  const sortVisibleFields = (defaultFields, elements) => {
    const sortedElements = [];
    defaultFields.forEach((i) => {
      const element = elements.find((ii) => ii.id === i);
      if (element) {
        sortedElements.push(element);
        elements = elements.filter((ii) => ii.id !== i);
      }
    });
    setAllAvailableFields([...sortedElements, ...elements]);
  };

  useEffect(() => {
    if (Array.isArray(allAvailableFields)) {
      onFieldsChange(allAvailableFields);
    }
  }, [allAvailableFields]);

  return (
    <SharableContentConfiguration
      fields={allAvailableFields}
      onHide={(item) => {
        setAllAvailableFields((items) =>
          produce(items, (draft) => {
            if (isSharableFieldKey(item.id)) {
              return draft.filter((i) => i.id !== item.id);
            } else {
              draft.find((i) => i.id === item.id).isVisible = false;
            }
          })
        );
      }}
      onShow={(item) => {
        const newAllAvailableFields = produce(allAvailableFields, (draft) => {
          draft.find((i) => i.id === item.id).isVisible = true;
        });
        sortVisibleFields(
          newAllAvailableFields.filter((i) => i.isVisible).map((i) => i.id),
          newAllAvailableFields
        );
      }}
      onOrderChange={(sortedItems) => {
        setAllAvailableFields((items) =>
          sortedItems.map((id) => items.find((ii) => id === ii.id))
        );
      }}
      onAdd={(type) => {
        const nextDummyIndex = allAvailableFields.filter((i) =>
          isSharableDummyFieldKey(i.id)
        ).length;
        setAllAvailableFields([
          ...allAvailableFields,
          {
            coreVariableName: `sharable_dummy_${nextDummyIndex}`,
            id: `sharable_dummy_${nextDummyIndex}`,
            isVisible: true,
            _translatedName: '',
            type,
          },
        ]);
      }}
      onNameChange={(fieldId, newName) => {
        setAllAvailableFields(
          produce(allAvailableFields, (draft) => {
            draft.find((i) => i.id === fieldId)._translatedName = newName;
          })
        );
      }}
    />
  );
};
