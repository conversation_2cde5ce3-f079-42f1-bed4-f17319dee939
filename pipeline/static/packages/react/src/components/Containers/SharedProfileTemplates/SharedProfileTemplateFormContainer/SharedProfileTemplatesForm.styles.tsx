import styled from '@emotion/styled';
import { Colors, Margins } from '../../../../styles/global.styles';
import { CheckboxWrapper } from '../../../Utils/Modals/Modals/Documents/AddDocumentTemplateModal.styles';

export const TemplateFormStyled = styled('div')({
  borderTop: `1px solid ${Colors.grey3}`,
  paddingTop: '40px',
});

export const TemplateFormHolderStyled = styled('div')({
  width: '100%',
  maxWidth: '740px',
  marginBottom: '180px',
});

export const TemplateNameInputHolderStyled = styled('div')({
  marginBottom: '-25px',
});

export const CheckboxWrapperStyled = styled(CheckboxWrapper)({
  paddingTop: '11px',
  marginBottom: '-12px',

  '& > div': {
    width: '100%',
    '@media (min-width: 769px)': {
      width: '50%',
    },

    label: {
      padding: 0,
      marginBottom: '12px',
      width: '100%',
      paddingRight: Margins.default,
      boxSizing: 'border-box',
    },
  },
});

export const TemplateFormSectionStyled = styled('div')({
  marginBottom: '30px',
});

export const TemplateFormFooterStyled = styled('div')({
  position: 'fixed',
  bottom: 0,
  width: '100%',
  maxWidth: '740px',
  padding: '50px 0',
  backgroundColor: '#fff',
  borderTop: `1px solid ${Colors.grey3}`,

  'button:first-of-type': {
    marginRight: '10px',
  },
});

export const LogoUploadWrapper = styled('div')({
  '& > div': {
    border: `1px solid ${Colors.blue3}`,
    borderRadius: '4px',
    marginTop: '5px',
    position: 'relative',
    padding: '10px 0',
    height: '140px',
    boxSizing: 'border-box',
    justifyContent: 'center',
  },

  '.uploaded-image': {
    marginBottom: '60px',

    img: {
      border: 0,
      maxWidth: '200px',
      maxHeight: '80px',
    },
  },

  '.actions': {
    position: 'absolute',
    top: '100%',
    left: 0,
  },
});
