import {
  EmailIcon,
  LocationIcon,
  PhoneNumberIcon,
  SocialWebsiteIcon,
} from '../../../assets/icons/small';
import i18n from '../../../i18n/i18n';
import Api from '../../../services/Api';
import { Colors } from '../../../styles/global.styles';
import { makeCancelable } from '../../../utils/other';
import { generateTestId } from '../../../utils/test.utils';
import ListLogo from '../../Utils/ListLogo/ListLogo';
import Spinner from '../../Utils/Spinner/Spinner';
import VendorAvatarAndName from '../../Vendors/VendorAvatarAndName/VendorAvatarAndName';
import VendorsListContainer from '../VendorsListContainer/VendorsListContainer';
import {
  SharedListContactItemStyled,
  SharedListContactWrapperStyled,
  SharedListContainerStyled,
  SharedListHeader2Styled,
  SharedListHeaderContainerStyled,
  SharedListHeaderStyled,
  SharedListLabelStyled,
  SharedListManagersWrapperStyled,
  SharedListStyled,
} from './SharedListContainer.styles.ts';
import { Container } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { goToErrorState } from '../../../services/Reducers/AngularMigration.helpers';

const SharedListContainer = ({ listKey }: { listKey: string }) => {
  const [sharedList, setSharedList] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const cancelablePromise = makeCancelable(Api.Lists.getListByKey(listKey));
    cancelablePromise.promise
      .then(({ data }) => {
        setIsLoading(false);
        setSharedList(data);
      })
      .catch(goToErrorState);
    return () => cancelablePromise.cancel();
  }, []);

  const listLogo = sharedList?.logo || sharedList?.sharable_template?.logo;
  const listCoverPhoto =
    sharedList?.cover_photo || sharedList?.sharable_template?.cover_photo;

  return (
    <SharedListStyled {...generateTestId('SharedListContainer')}>
      {isLoading ? (
        <Spinner />
      ) : (
        sharedList && (
          <>
            <SharedListHeaderStyled backgroundImage={listCoverPhoto}>
              <Container maxWidth="lg">
                <SharedListHeaderContainerStyled>
                  <SharedListHeader2Styled>
                    {sharedList.name}
                  </SharedListHeader2Styled>
                  {listLogo && <ListLogo logo={listLogo} />}
                </SharedListHeaderContainerStyled>
              </Container>
            </SharedListHeaderStyled>

            <SharedListContainerStyled maxWidth="lg">
              <div>{sharedList.description}</div>

              {sharedList.group_managers?.length > 0 && (
                <div>
                  <SharedListLabelStyled>
                    {i18n.t('SharedProfiles:SharedListContainer.ListManagers')}
                  </SharedListLabelStyled>
                  <SharedListManagersWrapperStyled>
                    {sharedList.group_managers.map((manager, index) => (
                      <VendorAvatarAndName
                        key={`manager_${index}`}
                        vendor={manager}
                        disableLink={true}
                      />
                    ))}
                  </SharedListManagersWrapperStyled>
                </div>
              )}

              <div>
                {sharedList.location &&
                  sharedList.email &&
                  sharedList.phone_number &&
                  sharedList.website && (
                    <SharedListLabelStyled>
                      {i18n.t(
                        'SharedProfiles:SharedListContainer.ContactDetails'
                      )}
                    </SharedListLabelStyled>
                  )}
                <SharedListContactWrapperStyled>
                  <div>
                    {sharedList.location && sharedList.location.query && (
                      <SharedListContactItemStyled>
                        <LocationIcon
                          size={24}
                          fill={Colors.grey1}
                          style={{ marginLeft: '-4px' }}
                        />
                        {sharedList.location.query}
                      </SharedListContactItemStyled>
                    )}
                    {sharedList.email && (
                      <SharedListContactItemStyled>
                        <EmailIcon
                          size={24}
                          fill={Colors.grey1}
                          style={{ marginLeft: '-4px' }}
                        />
                        <a
                          href={`mailto:${sharedList.email}`}
                          target="_blank"
                          title={sharedList.email}
                          rel="noopener noreferrer"
                        >
                          {sharedList.email}
                        </a>
                      </SharedListContactItemStyled>
                    )}
                  </div>

                  <div>
                    {sharedList.phone_number && (
                      <SharedListContactItemStyled>
                        <PhoneNumberIcon
                          size={24}
                          fill={Colors.grey1}
                          style={{ marginLeft: '-4px' }}
                        />
                        {sharedList.phone_number}
                      </SharedListContactItemStyled>
                    )}
                    {sharedList.website && (
                      <SharedListContactItemStyled>
                        <SocialWebsiteIcon
                          size={24}
                          fill={Colors.grey1}
                          style={{ marginLeft: '-4px' }}
                        />
                        <a
                          href={sharedList.website}
                          target="_blank"
                          title={sharedList.website}
                          rel="noopener noreferrer"
                        >
                          {sharedList.website}
                        </a>
                      </SharedListContactItemStyled>
                    )}
                  </div>
                </SharedListContactWrapperStyled>
              </div>
            </SharedListContainerStyled>

            <SharedListContainerStyled maxWidth="lg">
              <div>
                <VendorsListContainer
                  vendorList={sharedList}
                  publicListKey={listKey}
                />
              </div>
            </SharedListContainerStyled>
          </>
        )
      )}
    </SharedListStyled>
  );
};

export default SharedListContainer;
