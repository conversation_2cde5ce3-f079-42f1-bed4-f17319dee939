import Meta, * as stories from '../StatusPreview.stories';
import { composeStory } from '@storybook/react';
import '@testing-library/jest-dom';
import { render } from '@testing-library/react';
import React from 'react';

test('rendering status previews list', async () => {
  const StatusesList = composeStory(stories.Default, Meta);
  const { container } = await render(<StatusesList />);

  expect(container).toMatchSnapshot();
});
