import React from 'react';
import { getApiMockAdapter } from '../../../utils/storybook-utils';
import EmailUnsubscribeForm from './EmailUnsubscribeForm';

const mock = getApiMockAdapter({ delayResponse: 1500 });

export default {
  title: 'Core/Form Elements/Email Unsubscribe',
};

export const Default = ({ token }: { token: string }) => {
  mock
    .onPost(/\/api\/sendgrid\/unsubscribe\/valid-token\//gm)
    .reply(200)
    .onPost(/\/api\/sendgrid\/unsubscribe\/invalid-token\//gm)
    .reply(400);

  return <EmailUnsubscribeForm token={token} />;
};

Default.args = {
  token: 'valid-token',
};
