[{"message": "The timesheet was rejected by 4q 4q", "object_id": "352", "app_label": "tasks", "model": "task", "data": {"task": {"pk": 352, "app_label": "tasks", "model_name": "task"}, "tenant": {"pk": 656, "app_label": "clients", "model_name": "client"}, "executor": {"pk": 170, "app_label": "users", "model_name": "user"}, "timesheet": {"pk": 36, "app_label": "tasks", "model_name": "tasktimesheetperiod"}, "timestamp": "2022-07-22T12:08:16.340989+00:00", "url_account": "https://m66.worksuite.com/", "business_name": "niech będzie <PERSON>", "executor_name": "4q 4q", "executor_email": "<EMAIL>", "business_currency": "USD", "business_timezone": "Pacific/Noumea", "business_plan_name": "Internal/Testing", "business_plan_type": "internal", "executor_last_name": "4q", "executor_first_name": "4q", "url_projects_section": "https://m66.worksuite.com/projects/", "url_shortlist_section": "https://m66.worksuite.com/partners/shortlist/"}, "created_at": "2022-07-22T12:08:16.500532Z", "actor": {"avatar_color": "#334d5c", "slug": "marzena-fus-989823sdds7836-shortlist-co", "full_name": "4q 4q", "initials": "44", "profile_picture_path": null, "deleted": false}}, {"message": "The timesheet was approved by 4q 4q", "object_id": "352", "app_label": "tasks", "model": "task", "data": {"task": {"pk": 352, "app_label": "tasks", "model_name": "task"}, "tenant": {"pk": 656, "app_label": "clients", "model_name": "client"}, "executor": {"pk": 170, "app_label": "users", "model_name": "user"}, "timesheet": {"pk": 36, "app_label": "tasks", "model_name": "tasktimesheetperiod"}, "timestamp": "2022-07-21T14:09:16.588730+00:00", "url_account": "https://m66.worksuite.com/", "business_name": "niech będzie <PERSON>", "executor_name": "4q 4q", "executor_email": "<EMAIL>", "business_currency": "USD", "business_timezone": "Pacific/Noumea", "business_plan_name": "Internal/Testing", "business_plan_type": "internal", "executor_last_name": "4q", "executor_first_name": "4q", "url_projects_section": "https://m66.worksuite.com/projects/", "url_shortlist_section": "https://m66.worksuite.com/partners/shortlist/"}, "created_at": "2022-07-21T14:09:16.761379Z", "actor": {"avatar_color": "#334d5c", "slug": "marzena-fus-989823sdds7836-shortlist-co", "full_name": "4q 4q", "initials": "44", "profile_picture_path": null, "deleted": false}}, {"message": "Worksuite Support changed the status to completed", "object_id": "352", "app_label": "tasks", "model": "task", "data": {"task": {"pk": 352, "app_label": "tasks", "model_name": "task"}, "tenant": {"pk": 656, "app_label": "clients", "model_name": "client"}, "buttons": ["view task"], "executor": {"pk": 32, "app_label": "users", "model_name": "user"}, "timestamp": "2022-07-21T14:04:21.808796+00:00", "task_status": "completed", "url_account": "https://m66.worksuite.com/", "business_name": "niech będzie <PERSON>", "executor_name": "Worksuite Support", "executor_email": "<EMAIL>", "business_currency": "USD", "business_timezone": "Pacific/Noumea", "business_plan_name": "Internal/Testing", "business_plan_type": "internal", "executor_last_name": "Support", "executor_first_name": "Worksuite", "url_projects_section": "https://m66.worksuite.com/projects/", "url_shortlist_section": "https://m66.worksuite.com/partners/shortlist/"}, "created_at": "2022-07-21T14:04:21.914718Z", "actor": {"slug": null, "first_name": "Worksuite", "last_name": "Support", "email": "<EMAIL>", "full_name": "Worksuite Support", "initials": "W S", "avatar_color": "#d6d6d6", "vendor": false, "company_name": "", "id": null, "is_staff": true, "profile_picture_path": ["//s3.amazonaws.com/shortlist-logos/support_avatar.svg"]}}, {"message": "<PERSON>uh<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> marked task as completed", "object_id": "352", "app_label": "tasks", "model": "task", "data": {"task": {"pk": 352, "app_label": "tasks", "model_name": "task"}, "tenant": {"pk": 656, "app_label": "clients", "model_name": "client"}, "executor": {"pk": 934, "app_label": "users", "model_name": "user"}, "timestamp": "2022-07-21T14:03:30.930037+00:00", "description": ["status changed to under review"], "url_account": "https://m66.worksuite.com/", "business_name": "niech będzie <PERSON>", "executor_name": "juhu w<PERSON>ło", "executor_email": "marzena.fus+j<PERSON><PERSON><PERSON>je<PERSON><PERSON>@shortlist.co", "business_currency": "USD", "business_timezone": "Pacific/Noumea", "business_plan_name": "Internal/Testing", "business_plan_type": "internal", "executor_last_name": "w<PERSON><PERSON>ło", "executor_first_name": "juhu", "url_projects_section": "https://m66.worksuite.com/projects/", "url_shortlist_section": "https://m66.worksuite.com/partners/shortlist/"}, "created_at": "2022-07-21T14:03:30.969327Z", "actor": {"avatar_color": "#8cbeb2", "slug": "u-7sr9o", "full_name": "juhu w<PERSON>ło", "initials": "JW", "vendor_logo": null, "profile_picture_path": null, "vendor_slug": "marzena-fus-j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-shortlist-co", "deleted": false}}, {"message": "The timesheet was submitted by juh<PERSON> <PERSON><PERSON><PERSON><PERSON>o", "object_id": "352", "app_label": "tasks", "model": "task", "data": {"task": {"pk": 352, "app_label": "tasks", "model_name": "task"}, "tenant": {"pk": 656, "app_label": "clients", "model_name": "client"}, "executor": {"pk": 934, "app_label": "users", "model_name": "user"}, "timesheet": null, "timestamp": "2022-07-21T14:03:26.290037+00:00", "url_account": "https://m66.worksuite.com/", "business_name": "niech będzie <PERSON>", "executor_name": "juhu w<PERSON>ło", "executor_email": "marzena.fus+j<PERSON><PERSON><PERSON>je<PERSON><PERSON>@shortlist.co", "business_currency": "USD", "business_timezone": "Pacific/Noumea", "business_plan_name": "Internal/Testing", "business_plan_type": "internal", "executor_last_name": "w<PERSON><PERSON>ło", "executor_first_name": "juhu", "url_projects_section": "https://m66.worksuite.com/projects/", "url_shortlist_section": "https://m66.worksuite.com/partners/shortlist/"}, "created_at": "2022-07-21T14:03:26.412148Z", "actor": {"avatar_color": "#8cbeb2", "slug": "u-7sr9o", "full_name": "juhu w<PERSON>ło", "initials": "JW", "vendor_logo": null, "profile_picture_path": null, "vendor_slug": "marzena-fus-j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-shortlist-co", "deleted": false}}, {"message": "<PERSON>uh<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> accepted task", "object_id": "352", "app_label": "tasks", "model": "task", "data": {"task": {"pk": 352, "app_label": "tasks", "model_name": "task"}, "tenant": {"pk": 656, "app_label": "clients", "model_name": "client"}, "buttons": ["view task"], "executor": {"pk": 934, "app_label": "users", "model_name": "user"}, "timestamp": "2022-07-21T14:01:58.852147+00:00", "description": ["status changed to accepted"], "url_account": "https://m66.worksuite.com/", "business_name": "niech będzie <PERSON>", "executor_name": "juhu w<PERSON>ło", "executor_email": "marzena.fus+j<PERSON><PERSON><PERSON>je<PERSON><PERSON>@shortlist.co", "business_currency": "USD", "business_timezone": "Pacific/Noumea", "business_plan_name": "Internal/Testing", "business_plan_type": "internal", "executor_last_name": "w<PERSON><PERSON>ło", "executor_first_name": "juhu", "url_projects_section": "https://m66.worksuite.com/projects/", "url_shortlist_section": "https://m66.worksuite.com/partners/shortlist/"}, "created_at": "2022-07-21T14:01:59.049088Z", "actor": {"avatar_color": "#8cbeb2", "slug": "u-7sr9o", "full_name": "juhu w<PERSON>ło", "initials": "JW", "vendor_logo": null, "profile_picture_path": null, "vendor_slug": "marzena-fus-j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-shortlist-co", "deleted": false}}, {"message": "Worksuite Support changed the status to pending", "object_id": "352", "app_label": "tasks", "model": "task", "data": {"task": {"pk": 352, "app_label": "tasks", "model_name": "task"}, "tenant": {"pk": 656, "app_label": "clients", "model_name": "client"}, "buttons": ["view task", "accept"], "executor": {"pk": 32, "app_label": "users", "model_name": "user"}, "timestamp": "2022-07-21T14:01:30.275088+00:00", "task_status": "pending", "url_account": "https://m66.worksuite.com/", "business_name": "niech będzie <PERSON>", "executor_name": "Worksuite Support", "executor_email": "<EMAIL>", "business_currency": "USD", "business_timezone": "Pacific/Noumea", "business_plan_name": "Internal/Testing", "business_plan_type": "internal", "executor_last_name": "Support", "executor_first_name": "Worksuite", "url_projects_section": "https://m66.worksuite.com/projects/", "url_shortlist_section": "https://m66.worksuite.com/partners/shortlist/"}, "created_at": "2022-07-21T14:01:30.364888Z", "actor": {"slug": null, "first_name": "Worksuite", "last_name": "Support", "email": "<EMAIL>", "full_name": "Worksuite Support", "initials": "W S", "avatar_color": "#d6d6d6", "vendor": false, "company_name": "", "id": null, "is_staff": true, "profile_picture_path": ["//s3.amazonaws.com/shortlist-logos/support_avatar.svg"]}}, {"message": "Task was created by Worksuite Support", "object_id": "352", "app_label": "tasks", "model": "task", "data": {"task": {"pk": 352, "app_label": "tasks", "model_name": "task"}, "tenant": {"pk": 656, "app_label": "clients", "model_name": "client"}, "executor": {"pk": 32, "app_label": "users", "model_name": "user"}, "timestamp": "2022-07-21T13:56:38.460416+00:00", "task_group": {"pk": 103, "app_label": "tasks", "model_name": "taskgroup"}, "url_account": "https://m66.worksuite.com/", "business_name": "niech będzie <PERSON>", "executor_name": "Worksuite Support", "executor_email": "<EMAIL>", "business_currency": "USD", "business_timezone": "Pacific/Noumea", "business_plan_name": "Internal/Testing", "business_plan_type": "internal", "executor_last_name": "Support", "executor_first_name": "Worksuite", "url_projects_section": "https://m66.worksuite.com/projects/", "url_shortlist_section": "https://m66.worksuite.com/partners/shortlist/"}, "created_at": "2022-07-21T13:56:38.573198Z", "actor": {"slug": null, "first_name": "Worksuite", "last_name": "Support", "email": "<EMAIL>", "full_name": "Worksuite Support", "initials": "W S", "avatar_color": "#d6d6d6", "vendor": false, "company_name": "", "id": null, "is_staff": true, "profile_picture_path": ["//s3.amazonaws.com/shortlist-logos/support_avatar.svg"]}}]