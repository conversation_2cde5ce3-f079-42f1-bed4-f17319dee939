import React, { useEffect, useState } from 'react';
/* eslint-disable */
import { getApiMockAdapter } from '../../../utils/storybook-utils';
import { withStorybookTenantManagement } from '../../../utils/storybook.hooks';
import PublicJobOpeningsConfigList from './PublicJobOpeningsConfigList';

export default {
  title: 'Features/JobOpenings/PublicJobOpeningsConfigList',
  component: PublicJobOpeningsConfigList,
};

const DefaultTemplate = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  useEffect(() => {
    const mock = getApiMockAdapter({ delayResponse: 200 });
    mock
      .onGet(/\/api\/global_fields_templates\//gm)
      .reply(
        200,
        require(
          `../../../fixtures/api/custom_fields_templates/global_fields_templates.json`
        )
      );
    setIsLoaded(true);
    return () => mock.reset();
  }, []);
  return isLoaded ? (
    <PublicJobOpeningsConfigList
      items={[
        { id: 'location' },
        { id: 'not_existing_id' },
        { id: 'custom_5' },
        { id: 'custom_9' },
      ]}
      onChange={(items) => {
        console.log('onChange', items);
      }}
    />
  ) : null;
};

export const Default = withStorybookTenantManagement(DefaultTemplate, {
  defaultFeatures: ['task_templates_in_job_openings'],
});
