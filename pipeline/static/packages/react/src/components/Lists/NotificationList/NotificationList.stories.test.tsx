import { jestMockIntersectionObserver } from '../../../utils/jest/jest.helpers';
import * as stories from './NotificationList.stories';
import { Default, NoNotifications } from './NotificationList.stories';
import { composeStories } from '@storybook/react';
import '@testing-library/jest-dom';
import { render, screen, waitFor } from '@testing-library/react';
import React from 'react';

describe('On NotificationList - ', () => {
  beforeEach(async () => {
    jestMockIntersectionObserver();
  });

  test('Show "You don\'t have any notifications" for an empty list of notifications', async () => {
    const { NoNotifications: Notifications } = composeStories(stories);
    render(<Notifications />);

    await waitFor(async () => {
      const loading = await screen.getByText('Loading...');
      return expect(loading).toBeVisible();
    });

    await waitFor(
      async () => {
        const noNotification = await screen.getByText(
          "You don't have any notifications"
        );
        return expect(noNotification).toBeVisible();
      },
      { timeout: 2000 }
    );

    expect(NoNotifications).toMatchSnapshot();
  });

  test('Check if first notifiaction message is visible', async () => {
    const { Default: Notifications } = composeStories(stories);
    render(<Notifications />);

    await waitFor(async () => {
      const loading = await screen.getByText('Loading...');
      return expect(loading).toBeVisible();
    });

    await waitFor(
      async () => {
        const firstNotification = await screen.getByText(
          'Request Aamian test 2 RFP is now closed for proposals'
        );
        return expect(firstNotification).toBeVisible();
      },
      { timeout: 2000 }
    );

    expect(Default).toMatchSnapshot();
  });
});
