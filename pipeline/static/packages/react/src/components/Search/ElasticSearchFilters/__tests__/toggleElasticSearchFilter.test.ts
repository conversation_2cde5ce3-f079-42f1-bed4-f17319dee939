import { toggleElasticSearchFilter } from '../ElasticSearchFilters.helpers';

test.each`
  selectedFilters                          | selectedFilterKey | selectedFilterValue | output
  ${[]}                                    | ${'teams'}        | ${'1'}              | ${[['teams', '1']]}
  ${[['teams', '1'], ['created_by', '1']]} | ${'teams'}        | ${'1'}              | ${[['created_by', '1']]}
  ${[['created_by', '1'], ['teams', '1']]} | ${'teams'}        | ${'1'}              | ${[['created_by', '1']]}
  ${[['teams', '1'], ['created_by', '1']]} | ${'created_by'}   | ${'1'}              | ${[['teams', '1']]}
  ${[['created_by', '1'], ['teams', '1']]} | ${'created_by'}   | ${'1'}              | ${[['teams', '1']]}
  ${[['created_by', '1']]}                 | ${'created_by'}   | ${'1'}              | ${[]}
`(
  'toggleElasticSearchFilter',
  ({ selectedFilters, selectedFilterKey, selectedFilterValue, output }) => {
    const result = toggleElasticSearchFilter(
      selectedFilters,
      selectedFilterKey,
      selectedFilterValue,
      {}
    );
    expect(result).toStrictEqual(output);
  }
);

const filterOperators: any = {
  'parent.slug': 'and',
  postal_code: 'and',
  city: 'and',
  job_opening: 'or',
  administrative: 'and',
  status: 'or',
  services: 'and',
  vendor_type: 'or',
};

test('toggleElasticSearchFilter ', () => {
  let data: any = [
    ['parent.slug', 'agencja-agencja'],
    ['parent.slug', 'asiek-company'],
    ['city', 'P|P'],
    ['city', 'K,K'],
    ['vendor_type', 'worker'],
    ['administrative', 'Kalifornia'],
    ['or', [{ status: 'not-onboarded' }, { status: 'invited' }]],
    ['or', [{ job_opening: '11' }, { job_opening: '12' }]],
    ['postal_code', '94306'],
  ];
  data = toggleElasticSearchFilter(
    data,
    'postal_code',
    '94306',
    filterOperators
  );
  expect(data).toMatchSnapshot();

  data = toggleElasticSearchFilter(
    data,
    'parent.slug',
    'agencja-agencja',
    filterOperators
  );
  expect(data).toMatchSnapshot();

  data = toggleElasticSearchFilter(
    data,
    'services',
    'agencja-agencja',
    filterOperators
  );
  expect(data).toMatchSnapshot();

  data = toggleElasticSearchFilter(
    data,
    'services',
    'agencja-agencja',
    filterOperators
  );
  expect(data).toMatchSnapshot();

  data = toggleElasticSearchFilter(
    data,
    'status',
    'not-onboarded',
    filterOperators
  );
  expect(data).toMatchSnapshot();

  data = toggleElasticSearchFilter(data, 'job_opening', '11', filterOperators);
  expect(data).toMatchSnapshot();

  data = toggleElasticSearchFilter(data, 'job_opening', 'xxx', filterOperators);
  expect(data).toMatchSnapshot();

  data = toggleElasticSearchFilter(data, 'status', 'xxx', filterOperators);
  expect(data).toMatchSnapshot();
});
