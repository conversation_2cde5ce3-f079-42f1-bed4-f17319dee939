import reviewsI18n from '../../../../i18n/en/components/Search/Search.json';
import i18n from '../../../../i18n/i18n';
import {
  Colors,
  FontFamily,
  FontSize,
  Margins,
} from '../../../../styles/global.styles';
import InputSearch from '../../../FormElements/InputSearch/InputSearch';
import { ElasticSearchFiltersContextType } from '../ElasticSearchFilters.contexts';
import {
  ElasticFilterOperator,
  ElasticFilterValue,
  SearchFilterSingleValueDisplayFn,
} from '../ElasticSearchFilters.types';
import { FilterValueList } from './FilterValueList';
import styled from '@emotion/styled';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

i18n.addResourceBundle('en', 'Search', reviewsI18n);

export const FilterValueListWithSearch = ({
  values = [],
  selectedValues = [],
  onFilterToggle,
  defaultViewLimit = 25,
  filterKey,
  filterOperator,
  onFilterOperatorChange,
  hideCounters,
  operators,
  hideOperators,
  promotedValues,
  singleValueDisplayFn,
}: {
  values: ElasticFilterValue[];
  selectedValues: string[];
  onFilterToggle: (filterValue: string) => void;
  defaultViewLimit?: number;
  filterKey: string;
  filterOperator: ElasticFilterOperator;
  onFilterOperatorChange: ElasticSearchFiltersContextType['onFilterOperatorChange'];
  hideCounters?: boolean;
  operators?: ElasticFilterOperator[];
  hideOperators?: boolean;
  promotedValues?: ElasticFilterValue[];
  singleValueDisplayFn?: SearchFilterSingleValueDisplayFn;
}) => {
  const [filteredOptions, setFilteredOptions] = useState(values);
  const [searchedPhrase, setSearchedPhrase] = useState('');
  const [viewLimit, setViewLimit] = useState(defaultViewLimit);
  const { t } = useTranslation('Search');

  useEffect(() => {
    if (searchedPhrase) {
      setFilteredOptions(
        values.filter(
          (i) =>
            singleValueDisplayFn(i[0])
              .toLowerCase()
              .indexOf(searchedPhrase.toLowerCase()) !== -1
        )
      );
    } else {
      setFilteredOptions(values);
    }
  }, [searchedPhrase, viewLimit]);

  const handleChange = (value) => {
    setSearchedPhrase(value);
    setViewLimit(defaultViewLimit);
  };

  const onShowMore = useCallback(() => {
    setViewLimit((prevState) => prevState + defaultViewLimit);
  }, []);

  const disableMostPopularHeader =
    hideCounters || !!searchedPhrase || promotedValues?.length;

  return (
    <FilterValueListWithSearchStyles>
      <SearchStyled data-testid={'FilterList__Search'}>
        <InputSearch
          placeholder={t(`SEARCH_VALUES`, { number: values.length })}
          onChange={handleChange}
          autoFocus={true}
        />
      </SearchStyled>
      {disableMostPopularHeader ? null : (
        <FilterNameStyled>
          {t(`MOST_POPULAR`, { number: values.length })}
        </FilterNameStyled>
      )}
      <FilterValueListHolder>
        <FilterValueList
          values={filteredOptions.slice(0, viewLimit)}
          selectedValues={selectedValues}
          onFilterToggle={onFilterToggle}
          filterKey={filterKey}
          filterOperator={filterOperator}
          onFilterOperatorChange={onFilterOperatorChange}
          hideCounters={hideCounters}
          operators={operators}
          hideOperators={hideOperators}
          promotedValues={promotedValues}
          singleValueDisplayFn={singleValueDisplayFn}
          onShowMore={
            filteredOptions.length > viewLimit ? onShowMore : undefined
          }
        />
      </FilterValueListHolder>
    </FilterValueListWithSearchStyles>
  );
};

const FilterValueListWithSearchStyles = styled.div`
  display: flex;
  flex-flow: column nowrap;
`;

const FilterValueListHolder = styled.div``;

const SearchStyled = styled.div`
  padding-left: 6px;
  border-bottom: 1px solid ${Colors.grey3};
  max-height: 45px;
  position: sticky;
  top: 0;
  background-color: ${Colors.white};
  z-index: 1;
`;

const FilterNameStyled = styled.div`
  color: ${Colors.grey1};
  font-family: ${FontFamily.default};
  font-size: ${FontSize.default};
  font-weight: 600;
  line-height: 28px;
  margin: 0 ${Margins.default}px -${Margins.xsmall}px;
  padding: 10px 0 0 0;
`;
