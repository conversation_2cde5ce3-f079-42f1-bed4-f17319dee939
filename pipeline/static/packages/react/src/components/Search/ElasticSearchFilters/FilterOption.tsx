import { Colors, FontFamily, Margins } from '../../../styles/global.styles';
import Checkbox from '../../Checkbox/Checkbox';
import { SearchFilterSingleValueDisplayFn } from './ElasticSearchFilters.types';
import styled from '@emotion/styled';
import React from 'react';

const FilterOptionStyled = styled('div')({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  margin: `15px ${Margins.default}px`,
  cursor: 'pointer',
});

const NameStyled = styled('span')({
  fontFamily: FontFamily.default,
  fontSize: '12px',
  fontWeight: 600,
  color: Colors.blue1,
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
});

const CountStyled = styled('span')({
  fontFamily: FontFamily.default,
  fontSize: '12px',
  fontWeight: 600,
  color: Colors.grey2,
});

const DropdownTextStyled = styled('span')({
  display: 'flex',
  overflow: 'hidden',
  ['& > *:not(:first-of-type)']: {
    marginLeft: '3px',
  },
});

const DropdownElementStyled = styled('span')({
  display: 'flex',
  alignItems: 'center',
  marginLeft: '3px',
});

export const FilterOption = ({
  name,
  count,
  isSelected = false,
  onClick,
  hideCounters,
  hideCheckbox = false,
  displayValueFn,
}: {
  name: string;
  count: number | null;
  isSelected: boolean;
  onClick: () => void;
  hideCounters?: boolean;
  hideCheckbox?: boolean;
  displayValueFn?: SearchFilterSingleValueDisplayFn;
}) => {
  return (
    <FilterOptionStyled
      data-testid={'FilterList__FilterOption'}
      onClick={onClick}
    >
      <DropdownTextStyled>
        <NameStyled>{displayValueFn(name)}</NameStyled>
        {!hideCounters && count > 0 && <CountStyled>({count})</CountStyled>}
      </DropdownTextStyled>
      {!hideCheckbox && (
        <DropdownElementStyled>
          <Checkbox checked={isSelected} />
        </DropdownElementStyled>
      )}
    </FilterOptionStyled>
  );
};
