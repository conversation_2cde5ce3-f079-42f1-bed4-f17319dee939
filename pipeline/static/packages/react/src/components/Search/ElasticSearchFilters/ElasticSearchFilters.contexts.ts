import { createContext } from 'react';
import { FilterConfiguration } from '../../Containers/FilterSettingsContainer/FilterSettings.d';
import { CustomField } from '../../CustomFields/CustomFields';
import { LocationWithRadius } from '../../FormElements/InputGooglePlaces/InputGooglePlaces';
import { DropdownControl } from './ElasticSearchFilterFactory/Dropdown';
import {
  ElasticFilterOperator,
  ElasticFiltersConfig,
  SelectedFiltersType,
} from './ElasticSearchFilters.types';
import { FilterHelperTypes } from './SearchFilters/ElasticFilterHelper';
import { ElasticAvailabilityCalendarRange } from './SearchFilters/SearchAvailabilityCalendarFilter';
import { ElasticDateRange } from './SearchFilters/SearchDateFilter';
import { ElasticNumberRange } from './SearchFilters/SearchNumberRangeFilter';

export type OnFilterChangeValue =
  | string
  | LocationWithRadius
  | ElasticNumberRange
  | ElasticDateRange
  | ElasticAvailabilityCalendarRange;

interface DropdownControls {
  [filterKey: string]: DropdownControl;
}

export type ElasticSearchFiltersContextType = {
  selectedFilters: SelectedFiltersType;
  customFields: CustomField[];
  toggleFilter: (filterKey: string, filterValue: string) => void;
  toggleMultipleFilters: (filterKey: string, filterValues: string[]) => void;
  onFilterChange: (
    filterKey: string,
    filterValue: OnFilterChangeValue,
    type?: FilterHelperTypes
  ) => void;
  onFilterOperatorChange: (
    filterKey: string,
    filterOperator: ElasticFilterOperator
  ) => void;
  filtersConfiguration?: FilterConfiguration[];
  dropdownControls?: DropdownControls;
  setDropdownControls?: (data) => void;
  requestedDocumentLabels?: Record<string, string>;
  filterHeaders?: Record<string, string>;
};

export const ElasticSearchFiltersContexts = {
  default: {} as ElasticSearchFiltersContextType,
};

export const ElasticSearchFiltersContext = createContext(
  ElasticSearchFiltersContexts.default
);

export const ElasticSearchFiltersConfig = createContext<{
  filtersConfig: ElasticFiltersConfig;
}>({ filtersConfig: {} });
