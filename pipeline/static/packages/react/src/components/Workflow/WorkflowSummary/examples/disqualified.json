{"workflowForVendor": {"id": 30117, "completed": false, "workflow": 77, "current_stage": 191, "current_stage_for_vendor": 28671, "context": "jobopening:120", "disqualified": true}, "stagesForVendor": [{"stage": {"id": 190, "name": "Disqualified  - request data", "stage_type": "request_data", "vendors_current_count": 0, "auto_proceed_to_next_stage": true, "is_internal": false, "entry_condition": null}, "vendorInStage": {"template": {"id": 190, "name": "Disqualified  - request data", "stage_type": "request_data", "stage_order": 1, "current_progress": 0, "workflow": 77, "manual_notifications": false}, "progress": 100, "created_at": "2022-06-21T11:02:20.646582Z", "completed_at": "2022-06-21T11:08:00.523271Z", "items_count": 1, "items_filled": 1, "context": {"name": "jobopening:120", "label": "Single-candidate job opening #3"}, "id": 28670, "is_locked": true, "vendor_completed": true, "manual_notifications_sent": false, "workflow_for_vendor": 30117, "stage": {"id": 190, "name": "Disqualified  - request data", "stage_type": "request_data", "stage_order": 1, "current_progress": 0, "workflow": 77, "manual_notifications": false}}, "status": "stage_done"}, {"stage": {"id": 191, "name": "Disqualified - internal information", "stage_type": "information", "vendors_current_count": 0, "auto_proceed_to_next_stage": true, "is_internal": true, "entry_condition": null}, "vendorInStage": {"template": {"id": 191, "name": "Disqualified - internal information", "stage_type": "information", "stage_order": 2, "current_progress": 0, "workflow": 77, "manual_notifications": false}, "progress": 0, "created_at": "2022-06-21T11:08:00.876942Z", "completed_at": null, "items_count": 0, "items_filled": 0, "context": {"name": "jobopening:120", "label": "Single-candidate job opening #3"}, "id": 28671, "is_locked": true, "vendor_completed": false, "manual_notifications_sent": false, "workflow_for_vendor": 30117, "stage": {"id": 191, "name": "Disqualified - internal information", "stage_type": "information", "stage_order": 2, "current_progress": 0, "workflow": 77, "manual_notifications": false}}, "status": "not_started"}, {"stage": {"id": 192, "name": "Disqualified - information", "stage_type": "information", "vendors_current_count": 0, "auto_proceed_to_next_stage": true, "is_internal": false, "entry_condition": null}, "status": "not_in_stage"}]}