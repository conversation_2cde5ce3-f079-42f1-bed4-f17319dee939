import * as stories from './WorkflowSummary.stories';
import { composeStories } from '@storybook/react';
import '@testing-library/jest-dom';
import { render } from '@testing-library/react';
import React from 'react';

describe('ContractWorkflow - ', () => {
  test('snapshot should match', async () => {
    const { ContractWorkflow: WorkflowSummary } = composeStories(stories);
    const { container } = render(<WorkflowSummary />);

    expect(container).toMatchSnapshot();
  });
});

describe('Job Opening Summary - ', () => {
  test.each`
    story
    ${'JOSummaryRequestData'}
    ${'JOSummaryAction'}
    ${'JOSummaryExample2'}
    ${'JOSummaryExample3'}
    ${'JOSummaryExample4'}
    ${'JOSummaryExample5'}
    ${'JOSummaryExample6'}
    ${'JOSummaryExample7'}
    ${'JOSummaryExample8'}
    ${'Disqualified'}
  `('$story - snapshot should match', ({ story }) => {
    const composedStories = composeStories(stories);
    const { container } = render(React.createElement(composedStories[story]));
    expect(container).toMatchSnapshot();
  });
});
