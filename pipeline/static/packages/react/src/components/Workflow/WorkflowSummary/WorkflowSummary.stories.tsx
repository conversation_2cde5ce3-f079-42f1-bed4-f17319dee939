import React from 'react';
/* eslint-disable */
import { contractSummaryExample } from './ContractSummaryExample';
import { mapJobOpeningsToWorkflowSummaryStages } from './JobOpeningSummary.helper';
import WorkflowSummary from './WorkflowSummary';

export default {
  title: 'Core/Components/WorkflowSummary',
  component: WorkflowSummary,
};

const DefaultTemplate = (args) => {
  return <WorkflowSummary {...args} />;
};

export const ContractWorkflow = (args) => DefaultTemplate(args);
ContractWorkflow.args = {
  stages: contractSummaryExample,
};

const onButtonClick = (i) => console.log(i);

const getStageTypeData = (source, stageType) => {
  const defaultData = require(`./examples/${source}`);
  const stageTypeX = !stageType
    ? null
    : {
        stage_type: stageType,
      };
  return {
    ...defaultData,
    stagesForVendor: defaultData.stagesForVendor.map((ii) => ({
      ...ii,
      stage: {
        ...ii.stage,
        ...stageTypeX,
      },
    })),
  };
};
const getStages = (source, stageType = '') => {
  const stagesForVendor = getStageTypeData(source, stageType).stagesForVendor;
  const workflowForVendor = getStageTypeData(
    source,
    stageType
  ).workflowForVendor;
  return mapJobOpeningsToWorkflowSummaryStages(
    stagesForVendor,
    workflowForVendor,
    onButtonClick
  );
};

export const JOSummaryRequestData = (args) => DefaultTemplate(args);
JOSummaryRequestData.args = {
  stages: getStages('example1', 'request_data'),
};

export const JOSummaryAction = (args) => DefaultTemplate(args);
JOSummaryAction.args = {
  stages: getStages('example1', 'action'),
};

export const JOSummaryInformation = (args) => DefaultTemplate(args);
JOSummaryInformation.args = {
  stages: getStages('example1', 'information'),
};

export const JOSummaryExample2 = (args) => DefaultTemplate(args);
JOSummaryExample2.args = {
  stages: getStages('example2'),
};

export const JOSummaryExample3 = (args) => DefaultTemplate(args);
JOSummaryExample3.args = {
  stages: getStages('example3'),
};
export const JOSummaryExample4 = (args) => DefaultTemplate(args);
JOSummaryExample4.args = {
  stages: getStages('example4'),
};

export const JOSummaryExample5 = (args) => DefaultTemplate(args);
JOSummaryExample5.args = {
  stages: getStages('example5'),
};

export const JOSummaryExample6 = (args) => DefaultTemplate(args);
JOSummaryExample6.args = {
  stages: getStages('example6'),
};

export const JOSummaryExample7 = (args) => DefaultTemplate(args);
JOSummaryExample7.args = {
  stages: getStages('example7'),
};

export const JOSummaryExample8 = (args) => DefaultTemplate(args);
JOSummaryExample8.args = {
  stages: getStages('example8'),
};

export const Disqualified = (args) => DefaultTemplate(args);
Disqualified.args = {
  stages: getStages('disqualified'),
};
