import InputSearch from './InputSearch';
import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';

describe('InputSearch close button - ', () => {
  test('should be hidden when no text in input', async () => {
    const { container } = render(<InputSearch />);
    expect(screen.queryByTestId('close')).not.toBeInTheDocument();

    expect(container).toMatchSnapshot();
  });
  test('should be displayed when text in input', async () => {
    const { container } = render(<InputSearch initialValue={'Bob'} />);
    expect(screen.getByTestId('close')).toBeInTheDocument();

    expect(container).toMatchSnapshot();
  });

  test('should clear input field when clicked', async () => {
    const { container } = render(<InputSearch initialValue={'Bob'} />);

    fireEvent.click(screen.getByTestId('close'));
    expect(screen.getByRole('textbox')).not.toHaveValue();
    expect(screen.queryByTestId('close')).not.toBeInTheDocument();

    expect(container).toMatchSnapshot();
  });
});
