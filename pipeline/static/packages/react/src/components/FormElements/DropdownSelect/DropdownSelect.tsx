import * as Icon from '../../../assets/icons';
import { MenuDownIcon } from '../../../assets/icons/small';
import FormElementsI18n from '../../../i18n/en/components/FormElements/FormElements.json';
import i18n from '../../../i18n/i18n';
import { Colors } from '../../../styles/global.styles';
import Select from '../Select/Select';
import { DropdownWrapper, LabelWrapper } from './DropdownSelect.styles';
import type {
  DropdownSelectFooterAction,
  DropdownSelectItem,
} from './DropdownSelect.types';
import { Popover } from '@mui/material';
import React, { useState } from 'react';

i18n.addResourceBundle('en', 'FormElements', FormElementsI18n);

const DropdownSelect = ({
  label,
  options = [],
  onChange,
  footerAction,
  iconStart,
  searchInputPlaceholder,
  multiChoice = false,
  variant,
  closeOnChange = false,
}: {
  label: string;
  options: DropdownSelectItem[];
  onChange: (
    checkedOptions: DropdownSelectItem[],
    item?: DropdownSelectItem
  ) => void;
  footerAction?: DropdownSelectFooterAction;
  iconStart?: string;
  searchInputPlaceholder?: string;
  multiChoice?: boolean;
  variant?: string;
  closeOnChange?: boolean;
}) => {
  const IconStart = iconStart ? Icon.Small[iconStart] : false;
  const [anchorEl, setAnchorEl] = useState<HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleDropdownOpen = (event: any) => {
    setAnchorEl(event.currentTarget);
    event.stopPropagation();
  };

  const handleClose = (event = null) => {
    setAnchorEl(null);
    if (event) {
      event.stopPropagation();
    }
  };

  return (
    <div>
      <DropdownWrapper title={label} onClick={handleDropdownOpen}>
        {IconStart && (
          <IconStart
            size={variant !== 'filters' ? 28 : 24}
            fill={Colors.blue1}
          />
        )}
        <LabelWrapper data-testid="Dropdown_Label">{label}</LabelWrapper>
        {variant !== 'filters' && (
          <MenuDownIcon
            fill={variant !== 'filters' ? Colors.blue1 : Colors.grey1}
            size={29}
          />
        )}
      </DropdownWrapper>
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
      >
        <Select
          options={options}
          onChange={onChange}
          footerAction={footerAction}
          searchInputPlaceholder={searchInputPlaceholder}
          multiChoice={multiChoice}
          closeOnChange={closeOnChange}
          handleClose={handleClose}
        />
      </Popover>
    </div>
  );
};

export default DropdownSelect;
