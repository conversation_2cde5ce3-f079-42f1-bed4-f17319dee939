import {
  Colors,
  FontFamily,
  FontSize,
  Margins,
} from '../../../styles/global.styles';
import styled from '@emotion/styled';

export const DropdownWrapper = styled.div({
  display: 'flex',
  flexFlow: 'row nowrap',
  justifyContent: 'flex-start',
  alignItems: 'center',
  cursor: 'pointer',
  position: 'relative',
  padding: '5px',
  fontSize: FontSize.default,
  borderRadius: 4,
  ['> div']: {
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    maxWidth: 300,
  },
  ':hover': {
    backgroundColor: Colors.blue4,
  },
});

export const LabelWrapper = styled('div')({
  justifySelf: 'center',
  fontFamily: FontFamily.default,
  fontSize: 14,
  fontWeight: 600,
  lineHeight: '30px',
  color: Colors.blue1,
});

export const SearchStyled = styled('div')({
  paddingLeft: '6px',
  paddingRight: '15px',
  borderBottom: `1px solid ${Colors.grey3}`,
  maxHeight: '45px',
});

export const FooterActionWrapper = styled('div')(
  ({ variant }: { variant: string }) => ({
    padding: '20px',
    borderTop: `solid 1px ${Colors.grey3}`,
    textAlign: variant === 'filters' ? 'center' : 'inherit',
  })
);

export const DropdownSelectItems = styled('div')({
  maxHeight: '400px',
  minWidth: '270px',
  overflow: 'hidden',
  overflowY: 'auto',
  padding: `${Margins.small}px 0`,
});

export const NoFilteredOptionsFound = styled('div')({
  padding: '20px',
  fontFamily: FontFamily.default,
  fontSize: '12px',
  color: Colors.grey1,
});
