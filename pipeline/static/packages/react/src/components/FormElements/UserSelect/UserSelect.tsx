import { isSimpleUser } from '@Worksuite/Features/Core/components/InputPartners/InputPartners.guard';
import {
  DragDropContext,
  Draggable,
  DropResult,
  Droppable,
} from '@hello-pangea/dnd';
import { HTMLAttributes, useEffect, useMemo, useState } from 'react';
import * as I from '../../../assets/icons';
import { CustomIcon } from '../../../assets/icons';
import { MoveDragIcon, RemoveIcon } from '../../../assets/icons/small';
import i18n from '../../../i18n/i18n';
import { Colors, Effects } from '../../../styles/global.styles';
import * as T from '../../../styles/typography';
import { SessionUser, SimpleUser } from '../../../types/user';
import { generateTestId } from '../../../utils/test.utils';
import { isValidEmail } from '../../../utils/validators/other';
import UserAvatar from '../../Utils/UserAvatar/UserAvatar';
import Avatar from '../../Vendors/Avatar/Avatar';
import { getVendorName } from '../../Vendors/vendors.helpers';
import { AvatarSize } from '../../Vendors/vendors.types';
import AutoComplete, {
  RenderSelectedOptionsType,
  SelectValue,
} from '../AutoComplete/AutoComplete';
import { SearchIcon } from '../AutoComplete/AutoComplete.styles';
import {
  AutoCompleteWrapper,
  AvatarLabelWrapper,
  PartnerAvatar,
  RemoveWrapper,
  SelectedValueWrapper,
  UserContainer,
  UserSelectAvatar,
  UserSelectOption,
} from './UserSelect.styles';

type AllowedUserTypes = SimpleUser | SessionUser;

export type DefaultValueType = SelectValue & { canRemove?: boolean };
export type AdditionalOptionType = {
  label: string;
  icon?: CustomIcon & {
    name?: string;
  };
};
export const ADDITIONAL_OPTION_ID = -1;

const UserSelect = ({
  label = '',
  labelIcon,
  labelTooltip,
  additionalInfo,
  isOptional = false,
  placeholder = '',
  users = [],
  disabled,
  onChange,
  errors,
  renderIcon,
  defaultValues = [],
  multiChoice = false,
  hidePopper,
  onInputChange,
  disableFiltering,
  testId,
  additionalOption,
  isDragAndDropDisabled = false,
  showInputOnDisabled = true,
  forceRemovalOnMultiChoice = false,
  renderUserStatus = false,
  optionLabelRenderer = (option) => option.text,
  summaryRowRenderer = (option, user) => (
    <T.Label style={{ color: 'var(--statuses-status-processed)' }}>
      {optionLabelRenderer(option, user)}
    </T.Label>
  ),
  avatarWithStatusSize,
}: {
  placeholder?: string;
  labelIcon?: () => React.ReactNode;
  additionalInfo?: string;
  isOptional?: boolean;
  label?: string;
  labelTooltip?: string;
  users?: SimpleUser[] | SessionUser[];
  disabled?: boolean;
  onChange?: (values: (string | number)[]) => void;
  errors?: string[];
  renderIcon?: (selectedValues: SelectValue[], disabled: boolean) => any;
  defaultValues?: DefaultValueType[];
  multiChoice?: boolean;
  hidePopper?: boolean;
  onInputChange?: (value: string) => void;
  disableFiltering?: boolean;
  testId?: string;
  additionalOption?: AdditionalOptionType;
  isDragAndDropDisabled?: boolean;
  showInputOnDisabled?: boolean;
  /**
   * Force removal if `disabled = true` and `multiChoice = true`
   * Without this flag it is impossible to remove when `disabled = true`
   * Once omitted or set to `false` makes it `read-only` while `disabled = true`
   */
  forceRemovalOnMultiChoice?: boolean;
  renderUserStatus?: boolean;
  optionLabelRenderer?: (
    option: DefaultValueType,
    user: AllowedUserTypes
  ) => React.ReactNode;
  summaryRowRenderer?: (
    option: DefaultValueType,
    user: AllowedUserTypes
  ) => React.ReactNode;
  avatarWithStatusSize?: AvatarSize;
}) => {
  const filteredValues = useMemo(() => {
    if (multiChoice) {
      return defaultValues.filter(
        (i) =>
          i.canRemove === false ||
          users
            .map(({ id, slug }: { id?: number; slug: string }) =>
              String(id ?? slug)
            )
            .includes(String(i.key))
      );
    }
    return defaultValues;
  }, []);

  const getDefaultValuesOrder = () => filteredValues.map((value) => value.key);

  const [selectedValuesOrder, setSelectedValuesOrder] = useState<
    SelectValue['key'][]
  >(getDefaultValuesOrder());

  const showAdditionalOption = !multiChoice && additionalOption;
  const getOptions = () => {
    const options = users.map((user: AllowedUserTypes) => ({
      key: user.id ?? user.slug,
      text: UserSelect.getUserName(user),
    }));

    return [
      ...options,
      ...(showAdditionalOption
        ? [
            {
              key: ADDITIONAL_OPTION_ID,
              text: additionalOption.label,
            },
          ]
        : []),
    ];
  };

  const unremovableUserKeys = defaultValues
    .filter((i) => i.canRemove === false)
    .map((i) => i.key);

  useEffect(() => {
    setSelectedValuesOrder(getDefaultValuesOrder());
  }, []);

  useEffect(() => {
    onChange && onChange(selectedValuesOrder);
  }, [selectedValuesOrder]);

  const getUser = (option: DefaultValueType) =>
    users.find((user) => 'slug' in user && user.id === option.key);

  const renderAvatar = (option: DefaultValueType, size: AvatarSize = 45) => {
    const user = getUser(option);
    if (user) {
      if (renderUserStatus) {
        return (
          <Avatar
            avatarSize={avatarWithStatusSize ?? size}
            vendor={user}
            showStatus
          />
        );
      }
      return <UserAvatar avatarSize={size} user={user} />;
    }
    if (showAdditionalOption) {
      const Icon = I.Small[additionalOption?.icon?.name || 'AddNoCircleIcon'];
      return (
        <PartnerAvatar avatarSize={size}>
          <Icon
            fill={additionalOption?.icon?.fill ?? Colors.grey2}
            size={additionalOption?.icon?.size ?? size * 0.8}
            style={additionalOption?.icon?.style ?? {}}
          />
        </PartnerAvatar>
      );
    }

    const isCustomEmailOption =
      option.key === option.text && isValidEmail(option.text);
    const Icon = I.Small[isCustomEmailOption ? 'EmailIcon' : 'PartnerIcon'];

    return (
      <PartnerAvatar avatarSize={size}>
        <Icon fill={Colors.grey2} size={size * 0.7} />
      </PartnerAvatar>
    );
  };

  const renderIconForSingleChoice = (selectedValues: SelectValue[]) => {
    const value = Array.isArray(selectedValues)
      ? selectedValues[0]
      : selectedValues;

    return (
      <>
        <SearchIcon size={24} fill={Colors.grey2} />
        {value && (
          <UserSelectAvatar>{renderAvatar(value, 30)}</UserSelectAvatar>
        )}
      </>
    );
  };

  const renderOption = (
    props: HTMLAttributes<HTMLLIElement>,
    option: DefaultValueType
  ) => (
    <div key={`option_${option.key}`}>
      <T.Label {...props} style={{ width: '100%', color: Colors.blue1 }}>
        <UserSelectOption>
          {renderAvatar(option)}
          {optionLabelRenderer(option, getUser(option))}
        </UserSelectOption>
      </T.Label>
    </div>
  );

  const getItemStyle = (isDragging, draggableStyle) => {
    if (isDragging) {
      return {
        backgroundColor: 'white',
        transition: 'box-shadow 0.3s ease-in-out',
        ...Effects.Shadows.dropdown,
        ...draggableStyle,
      };
    }
    return {
      ...draggableStyle,
    };
  };

  const onDragEnd = (result: DropResult) => {
    if (!result.destination) {
      return;
    }

    const startIndex = result.source.index;
    const endIndex = result.destination.index;

    const newValues = [...selectedValuesOrder];
    const [removed] = newValues.splice(startIndex, 1);
    newValues.splice(endIndex, 0, removed);

    handleOnChange(newValues);
  };

  const handleOnChange = (values) => {
    setSelectedValuesOrder((prevState) => {
      if (prevState.length > values.length) {
        return prevState.filter((key) => values.includes(key));
      } else if (prevState.length < values.length) {
        const newKey = values.find((key) => !prevState.includes(key));
        return [...prevState, newKey];
      } else {
        return values;
      }
    });
  };

  const renderSelectedOptions: RenderSelectedOptionsType = (
    options,
    disabled,
    onRemove
  ) => {
    const sortedOptions = selectedValuesOrder.map((key) =>
      options.find((option) => option.key === key)
    );
    const showDragAndDropIndicators = !disabled && !isDragAndDropDisabled;
    const isRemovable = (key: SelectValue['key']) => {
      if (forceRemovalOnMultiChoice) {
        return !unremovableUserKeys.includes(key);
      }
      return !disabled && !unremovableUserKeys.includes(key);
    };

    return (
      <div data-testid="UserSelect_selectedValues">
        <DragDropContext onDragEnd={onDragEnd}>
          <Droppable droppableId="droppable">
            {(provided) => (
              <div
                ref={provided.innerRef}
                {...provided.droppableProps}
                style={{ marginTop: '5px' }}
              >
                {sortedOptions.map((option, index: number) => (
                  <Draggable
                    key={`selectedOption_${option.key}`}
                    draggableId={String(option.key)}
                    index={index}
                    isDragDisabled={!showDragAndDropIndicators}
                  >
                    {(provided, snapshot) => (
                      <UserContainer
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        style={getItemStyle(
                          snapshot.isDragging,
                          provided.draggableProps.style
                        )}
                      >
                        <SelectedValueWrapper key={option.key}>
                          <AvatarLabelWrapper
                            {...generateTestId('SelectedUser', 'UserSelect')}
                          >
                            {showDragAndDropIndicators && (
                              <div {...provided.dragHandleProps}>
                                <MoveDragIcon fill={Colors.grey2} />
                              </div>
                            )}

                            {renderAvatar(option, 45)}
                            {summaryRowRenderer(option, getUser(option))}
                          </AvatarLabelWrapper>

                          {isRemovable(option.key) && (
                            <RemoveWrapper>
                              <RemoveIcon
                                onClick={() => onRemove(option)}
                                fill={Colors.grey2}
                              />
                            </RemoveWrapper>
                          )}
                        </SelectedValueWrapper>
                      </UserContainer>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      </div>
    );
  };

  return (
    <AutoCompleteWrapper
      hidePopper={hidePopper}
      isFilled={selectedValuesOrder?.length > 0}
      isMultiChoice={multiChoice}
    >
      <AutoComplete
        label={label}
        labelTooltip={labelTooltip}
        labelIcon={labelIcon}
        additionalInfo={additionalInfo}
        isOptional={isOptional}
        placeholder={placeholder}
        multiple={multiChoice}
        options={getOptions()}
        disabled={disabled}
        onChange={handleOnChange}
        errors={errors}
        renderIcon={multiChoice ? renderIcon : renderIconForSingleChoice}
        renderOption={renderOption}
        renderSelectedOptions={renderSelectedOptions}
        defaultValue={filteredValues}
        onInputChange={onInputChange}
        disableFiltering={disableFiltering}
        showInputOnDisabled={showInputOnDisabled}
        testId={testId}
      />
    </AutoCompleteWrapper>
  );
};

UserSelect.getUserName = (user: AllowedUserTypes) =>
  `${getVendorName(user)}${
    isSimpleUser(user, 'is_staff') && 'is_staff' in user && user.is_staff
      ? ` ${i18n.t('UserSelect.StaffLabel')}`
      : ''
  }`;

export default UserSelect;
