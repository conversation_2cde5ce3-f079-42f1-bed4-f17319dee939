import { EMPTY_EDITOR_CONTENT, sanitizeContent } from './InputEditor.helpers';
import { XSS_STRING_1, XSS_STRING_2 } from './InputEditor.stories.helpers';

describe('sanitizeContent', () => {
  test('should remove malicious attributes and sanitize input #1', () => {
    const input = sanitizeContent(XSS_STRING_1);
    const output = 'x<br>';
    expect(input).toBe(output);
  });
  test('should remove malicious attributes and sanitize input #2', () => {
    const input = sanitizeContent(XSS_STRING_2).replace(/\n/g, '').trim();
    const output =
      'Click me<a rel="noopener noreferrer" target="_blank">Click this link</a>Hover';
    expect(input).toBe(output);
  });
  test('should return empty editor content unchanged', () => {
    const input = sanitizeContent(EMPTY_EDITOR_CONTENT);
    expect(input).toBe(EMPTY_EDITOR_CONTENT);
  });
});
