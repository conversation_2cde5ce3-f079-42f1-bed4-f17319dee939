// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`InputCurrency can have defaultValue 1`] = `
.emotion-class {
  position: relative;
}

.emotion-class input {
  padding-right: 50px;
  max-height: var(--spacing-2xl);
  min-height: var(--spacing-2xl);
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  box-sizing: border-box;
  width: 100%;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--glyphs-forms-glyphs-form-placeholder);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  position: absolute;
  bottom: 14px;
  right: var(--spacing-m);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: row nowrap;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

<div>
  <div
    class="emotion-class"
  >
    <div
      class="emotion-class"
    >
      <input
        class="emotion-class"
        placeholder="default placeholder"
        type="text"
        value="123.45"
      />
      <span
        class="emotion-class"
      >
        EUR
      </span>
    </div>
    <div
      class="emotion-class"
    >
      <div />
    </div>
  </div>
</div>
`;

exports[`InputCurrency display placeholder 1`] = `
.emotion-class {
  position: relative;
}

.emotion-class input {
  padding-right: 50px;
  max-height: var(--spacing-2xl);
  min-height: var(--spacing-2xl);
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  box-sizing: border-box;
  width: 100%;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--glyphs-forms-glyphs-form-placeholder);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  position: absolute;
  bottom: 14px;
  right: var(--spacing-m);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: row nowrap;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

<div>
  <div
    class="emotion-class"
  >
    <div
      class="emotion-class"
    >
      <input
        class="emotion-class"
        placeholder="default placeholder"
        type="text"
        value="123.45"
      />
      <span
        class="emotion-class"
      >
        EUR
      </span>
    </div>
    <div
      class="emotion-class"
    >
      <div />
    </div>
  </div>
</div>
`;

exports[`InputCurrency renders currency addon 1`] = `
.emotion-class {
  position: relative;
}

.emotion-class input {
  padding-right: 50px;
  max-height: var(--spacing-2xl);
  min-height: var(--spacing-2xl);
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  box-sizing: border-box;
  width: 100%;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--glyphs-forms-glyphs-form-placeholder);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  position: absolute;
  bottom: 14px;
  right: var(--spacing-m);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: row nowrap;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

<div>
  <div
    class="emotion-class"
  >
    <div
      class="emotion-class"
    >
      <input
        class="emotion-class"
        placeholder="default placeholder"
        type="text"
        value="123.45"
      />
      <span
        class="emotion-class"
      >
        EUR
      </span>
    </div>
    <div
      class="emotion-class"
    >
      <div />
    </div>
  </div>
</div>
`;

exports[`InputCurrency triggers \`null\` onChange with non-numeric value and value is not visible in the component 1`] = `
.emotion-class {
  position: relative;
}

.emotion-class input {
  padding-right: 50px;
  max-height: var(--spacing-2xl);
  min-height: var(--spacing-2xl);
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  box-sizing: border-box;
  width: 100%;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--glyphs-forms-glyphs-form-placeholder);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  position: absolute;
  bottom: 14px;
  right: var(--spacing-m);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: row nowrap;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

<div>
  <div
    class="emotion-class"
  >
    <div
      class="emotion-class"
    >
      <input
        class="emotion-class"
        placeholder="default placeholder"
        type="text"
        value=""
      />
      <span
        class="emotion-class"
      >
        EUR
      </span>
    </div>
    <div
      class="emotion-class"
    >
      <div />
    </div>
  </div>
</div>
`;

exports[`InputCurrency triggers onChange with numeric value 1`] = `
.emotion-class {
  position: relative;
}

.emotion-class input {
  padding-right: 50px;
  max-height: var(--spacing-2xl);
  min-height: var(--spacing-2xl);
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  box-sizing: border-box;
  width: 100%;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--glyphs-forms-glyphs-form-placeholder);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  position: absolute;
  bottom: 14px;
  right: var(--spacing-m);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: row nowrap;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

<div>
  <div
    class="emotion-class"
  >
    <div
      class="emotion-class"
    >
      <input
        class="emotion-class"
        placeholder="default placeholder"
        type="text"
        value="123456"
      />
      <span
        class="emotion-class"
      >
        EUR
      </span>
    </div>
    <div
      class="emotion-class"
    >
      <div />
    </div>
  </div>
</div>
`;
