import styled from '@emotion/styled';
import { useEffect, useRef, useState } from 'react';
import { createNumberMask } from 'text-mask-addons';
import { convertToNumber } from '../../../utils/number.utils';
import InputText, { InputTextProps } from '../InputText/InputText';

type Props = {
  onChange?: (value: number) => void;
  onBlur?: (value: number) => void;
  defaultValue: string | number;
  currency: string;
} & Omit<
  InputTextProps,
  | 'onChange'
  | 'onBlur'
  | 'maxLength'
  | 'type'
  | 'onFocus'
  | 'autoFocus'
  | 'variant'
>;

const InputCurrency = (props: Props) => {
  const defaultValue = useRef(props.defaultValue ?? null);
  const [value, setValue] = useState<string | null>(() =>
    parseValue(props.defaultValue)
  );

  const onInputChange = (rawValue: string) => {
    const parsedValue = String(rawValue).replace(/,/g, '');
    setValue(parsedValue);
    props.onChange?.(convertToNumber(parsedValue));
  };

  useEffect(() => {
    defaultValue.current = props.defaultValue;
    setValue(parseValue(defaultValue.current));
  }, [props.defaultValue]);

  useEffect(() => {
    if (value !== defaultValue.current) {
      if (defaultValue.current !== null) {
        setValue(parseValue(defaultValue.current));
      } else {
        setValue(null);
      }
    }
  }, [defaultValue.current]);

  return (
    <CurrencyWrapper>
      <InputText
        {...props}
        onChange={onInputChange}
        onBlur={onInputChange}
        defaultValue={value}
        mask={currencyMask}
        placeholder={props.placeholder}
        unit={props.currency}
      />
    </CurrencyWrapper>
  );
};

function parseValue(value: string | number) {
  if (value || value === 0) {
    return typeof value === 'string' ? value : value?.toString() ?? null;
  }
  return null;
}

const CurrencyWrapper = styled.div`
  position: relative;

  input {
    padding-right: 50px;
    max-height: var(--spacing-2xl);
    min-height: var(--spacing-2xl);
  }
`;

const currencyMask = createNumberMask({
  prefix: '',
  allowDecimal: true,
  decimalLimit: 2,
  suffix: '',
});

export default InputCurrency;
