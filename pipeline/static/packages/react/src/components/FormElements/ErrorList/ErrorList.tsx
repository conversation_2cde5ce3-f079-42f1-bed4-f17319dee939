import { Colors } from '../../../styles/global.styles';
import * as T from '../../../styles/typography';
import styled from '@emotion/styled';
import PropTypes from 'prop-types';
import React from 'react';

const Ul = styled('ul')({
  listStyle: 'none',
  paddingLeft: 0,
  margin: 0,
});
const Li = styled(T.LabelSmall)({
  color: Colors.pink1,
  width: '100%',
  boxSizing: 'border-box',
});

const ErrorList = ({ errors }: { errors?: string[] }) => (
  <>
    {errors?.length ? (
      <Ul data-testid={'ErrorList'}>
        {errors.map((i, ii) => (
          <Li key={ii} as="li">
            {i}
          </Li>
        ))}
      </Ul>
    ) : null}
  </>
);

ErrorList.propTypes = {
  errors: PropTypes.array,
};

export default ErrorList;
