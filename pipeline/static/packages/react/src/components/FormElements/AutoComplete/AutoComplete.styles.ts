import styled from '@emotion/styled';
import { Paper, Popper } from '@mui/material';
import * as I from '../../../assets/icons';
import {
  Colors,
  FontFamily,
  FontSize,
  Inputs,
} from '../../../styles/global.styles';

export const RemoveWrapper = styled('span')({
  cursor: 'pointer',
  position: 'absolute',
  top: '15px',
  right: 0,
});

export const SelectWrapper = styled.div<{
  disabled?: boolean;
  error?: boolean;
}>(({ disabled, error }) => ({
  position: 'relative',
  ['legend, .MuiChip-root, .MuiAutocomplete-endAdornment']: {
    display: 'none',
  },
  '.MuiAutocomplete-inputRoot[class*="MuiOutlinedInput-root"]': {
    padding: '5px',
    '.MuiAutocomplete-input': {
      fontSize: FontSize.default,
      color: Colors.grey1,
      paddingLeft: disabled ? '8px' : '35px',
      fontFamily: FontFamily.default,
    },
  },
  '.MuiAutocomplete-hasPopupIcon.MuiAutocomplete-hasClearIcon .MuiAutocomplete-inputRoot[class*="MuiOutlinedInput-root"]':
    {
      paddingRight: 0,
    },
  '.MuiInputBase-root:hover, .MuiInputBase-root:active, .MuiInputBase-root:focus':
    {
      '.MuiOutlinedInput-notchedOutline': {
        borderColor: error ? Colors.pink1 : Colors.blue2,
        outline: 'none',
      },
      '.MuiOutlinedInput-notchedOutline:focus': {
        borderColor: error ? Colors.pink1 : Colors.blue1,
        outline: 'none',
      },
    },
  '.MuiOutlinedInput-root.Mui-focused': {
    '.MuiOutlinedInput-notchedOutline': {
      borderColor: error ? Colors.pink1 : Colors.blue1,
    },
  },
  '.MuiOutlinedInput-notchedOutline': {
    ...Inputs.default,
    top: 0,
    minHeight: 'auto',
    borderColor: error ? Colors.pink1 : Colors.blue3,
  },
  '.MuiOutlinedInput-root': {
    '&.Mui-disabled': {
      '.MuiOutlinedInput-notchedOutline': {
        borderColor: Colors.grey3,
      },
    },
  },
  '.MuiAutocomplete-root': {
    backgroundColor: Colors.white,
    position: 'relative',
  },
}));

export const StyledPopper = styled(Popper)({
  '.MuiAutocomplete-paper': {
    marginTop: 0,
  },
  '.MuiPaper-rounded': {
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
  },
  '.MuiAutocomplete-listbox': {
    paddingTop: '5px',
  },
  '.MuiAutocomplete-option': {
    padding: '8px 20px',
  },
  '.MuiAutocomplete-option[data-focus="true"]': {
    backgroundColor: Colors.blue4,
  },
  '.MuiPaper-elevation1': {
    boxShadow: '0px 2px 6px rgba(48, 59, 87, 0.38)',
  },
  '.MuiAutocomplete-noOptions': {
    color: Colors.grey2,
    fontFamily: FontFamily.default,
    fontSize: FontSize.default,
  },
});

export const SearchIcon = styled(I.Small.SearchIcon)({
  position: 'absolute',
  top: '11px',
  left: '8px',
});

export const SelectedValueWrapper = styled('div')({
  position: 'relative',
  fontSize: FontSize.default,
  borderBottom: `solid 1px ${Colors.grey3}`,
  padding: '20px 0',
  ':last-child': {
    border: 'none',
  },
});

export const AutoCompleteStyledPaper = styled(Paper)(() => ({
  position: 'relative',
  '& .footer': {
    padding: `var(--spacing-s) var(--spacing-m)`,
    borderTop: `1px solid var(--borders-canvas-borders-canvas-border-base)`,
    textAlign: 'center',
  },
}));
