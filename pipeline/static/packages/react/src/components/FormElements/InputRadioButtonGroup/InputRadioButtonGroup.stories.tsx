import styled from '@emotion/styled';
import React from 'react';
/* eslint-disable */
import InputRadioButtonGroup from './InputRadioButtonGroup';

export default {
  title: 'Core/Form Elements/InputRadioButtonGroup',
  component: InputRadioButtonGroup,
};

const args = {
  options: [
    {
      label: 'Single-candidate JobOpening',
      value: 'single',
    },
    {
      label: 'Multi-candidate JobOpening',
      value: 'multi',
    },
  ],
  onChange: console.log,
};

const FormField = styled.div`
  padding: 0 0 20px 0;
`;

const Template = (args) => {
  return <InputRadioButtonGroup {...args} label={'Label'} />;
};

export const Default = (args) => Template(args);
Default.args = args;

export const WithError = (args) => Template(args);
WithError.args = {
  ...args,
  errors: ['Invoice name is required.'],
};

const Template2 = (args) => {
  return (
    <>
      <p>Enabled</p>
      <FormField>
        <InputRadioButtonGroup {...args} />
      </FormField>

      <p>Checked</p>
      <FormField>
        <InputRadioButtonGroup {...args} defaultValue={'single'} />
      </FormField>

      <p>Disabled</p>
      <FormField>
        <InputRadioButtonGroup
          {...args}
          defaultValue={'single'}
          disabled={true}
        />
      </FormField>

      <p>Edge cases</p>
      <FormField>
        <InputRadioButtonGroup
          options={[
            {
              label:
                'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Praesent non elit nunc. Nullam sit amet lacus vel massa sodales dictum. Ut ipsum elit, hendrerit sagittis turpis non, sollicitudin ornare arcu. In scelerisque eros eget varius dignissim. Morbi sit amet lorem urna. Nullam in tincidunt sapien. Etiam interdum id mi eget tincidunt. Proin cursus lectus sit amet purus rutrum rutrum. Cras eu mauris augue. Cras posuere gravida iaculis. Praesent fringilla laoreet tellus. Donec nec placerat purus. Aenean tincidunt justo at turpis bibendum, in faucibus erat pharetra. Vivamus vel condimentum justo. Etiam sodales placerat nisi mattis venenatis. Fusce vitae tempus metus, quis congue sem. Donec et ante semper, semper ex at, consequat ligula. Mauris semper, ipsum ac mollis pretium, justo odio ornare sem, eget tristique risus arcu eget lectus. Vivamus blandit vel nunc at efficitur.',
              value: 'single',
            },
            {
              label:
                'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Praesent non elit nunc. Nullam sit amet lacus vel massa sodales dictum. Ut ipsum elit, hendrerit sagittis turpis non, sollicitudin ornare arcu. In scelerisque eros eget varius dignissim. Morbi sit amet lorem urna. Nullam in tincidunt sapien. Etiam interdum id mi eget tincidunt. Proin cursus lectus sit amet purus rutrum rutrum. Cras eu mauris augue. Cras posuere gravida iaculis. Praesent fringilla laoreet tellus. Donec nec placerat purus. Aenean tincidunt justo at turpis bibendum, in faucibus erat pharetra. Vivamus vel condimentum justo. Etiam sodales placerat nisi mattis venenatis. Fusce vitae tempus metus, quis congue sem. Donec et ante semper, semper ex at, consequat ligula. Mauris semper, ipsum ac mollis pretium, justo odio ornare sem, eget tristique risus arcu eget lectus. Vivamus blandit vel nunc at efficitur.',
              value: 'multi',
            },
          ]}
          onChange={console.log}
        />
      </FormField>

      <p>With Error</p>
      <FormField>
        <InputRadioButtonGroup
          {...args}
          errors={[
            'Invoice name is required.',
            'Long Error #2 message, Long Error #2 message, Long Error #2 message, Long Error #2 message, Long Error #2 message, Long Error #2 message, Long Error #2 message, Long Error #2 message.',
          ]}
        />
      </FormField>
    </>
  );
};

export const SingleOptionDisabled = (args) => Template(args);
SingleOptionDisabled.args = {
  options: [
    {
      label: 'Option 1',
      value: 'option1',
    },
    {
      label: 'Option 2 Disabled',
      value: 'option2',
      disabled: true,
    },
    {
      label: 'Option 3',
      value: 'option3',
    },
  ],
  onChange: console.log,
};

export const AdditionalDescription = (args) => Template(args);
AdditionalDescription.args = {
  options: [
    {
      label: 'Option 1',
      value: 'option1',
      description: 'Some additional description for option 1',
    },
    {
      label: 'Option 2',
      value: 'option2',
      description: 'Some additional description for option 2',
    },
  ],
  onChange: console.log,
};

export const AllParams = (args) => Template2(args);
AllParams.args = args;
