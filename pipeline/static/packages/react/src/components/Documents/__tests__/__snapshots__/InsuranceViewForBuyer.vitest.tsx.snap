// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`InsuranceViewForBuyer > should rander InsuranceView with all data 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-l);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-s-plus);
}

.emotion-class {
  position: relative;
}

.emotion-class.tableStart:before,
.emotion-class.tableEnd:after {
  opacity: 1;
  z-index: 2;
}

.emotion-class:before,
.emotion-class:after {
  opacity: 0;
  pointer-events: none;
  content: "";
  display: block;
  position: absolute;
  top: 0;
  height: 100%;
  width: 10px;
  -webkit-transition: opacity 0.3s ease-in-out;
  transition: opacity 0.3s ease-in-out;
}

.emotion-class:before {
  left: 0;
  background: linear-gradient(90deg, rgba(8, 10, 26, 0.1) 0%, rgba(8, 10, 26, 0) 80%);
  border-radius: 4px 0px 0px 4px;
}

.emotion-class:after {
  right: 0;
  background: linear-gradient(270deg, rgba(8, 10, 26, 0.1) 0%, rgba(8, 10, 26, 0) 80%);
  border-radius: 0px 4px 4px 0px;
}

.emotion-class {
  overflow-x: auto;
  border-radius: 4px;
}

.emotion-class {
  border-collapse: separate;
  width: 100%;
  height: 1px;
}

.emotion-class.mobileView tr {
  border: 1px solid #E4E5EB;
  border-color: #E4E5EB;
  border-radius: 4px;
  padding: 20px;
}

.emotion-class.mobileView td {
  min-height: 1px;
  background-color: #FFFFFF;
  border: 0;
  margin: 0;
  padding: 0!important;
  margin-bottom: 12px;
  font-size: 16px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  font-size: 14px;
  text-align: left;
  padding: 10px;
  border-width: 1px 1px 0 0;
  border-style: solid;
  border-color: #E4E5EB;
  background-color: #F7F7F7;
  box-sizing: border-box;
}

.emotion-class:first-of-type {
  border-top-left-radius: 4px;
  border-left: 1px solid #E4E5EB;
}

.emotion-class:last-of-type {
  border-top-right-radius: 4px;
}

.emotion-class:focus-within {
  background-color: #F5F6FA;
}

.emotion-class:first-of-type td {
  border-top: 1px solid #E4E5EB;
}

.emotion-class:last-of-type td:first-of-type {
  border-bottom-left-radius: 4px;
}

.emotion-class:last-of-type td:last-of-type {
  border-bottom-right-radius: 4px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  vertical-align: top;
  border-bottom: 1px solid #E4E5EB;
  border-right: 1px solid #E4E5EB;
  padding: 15px 10px;
}

.emotion-class:first-of-type {
  border-left: 1px solid #E4E5EB;
}

.emotion-class:last-of-type {
  border-right: 1px solid #E4E5EB;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #FFFFFF;
  background-color: var(--statuses-status-active);
  font-family: "Montserrat",sans-serif;
  font-weight: 700;
  font-size: 10px;
  line-height: 20px;
  letter-spacing: 0.5px;
  text-align: center;
  text-transform: uppercase;
  border-radius: var(--spacing-2xs);
  margin: 0;
  padding: 0 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #465AB6;
  cursor: pointer;
  font-weight: 600;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #303757;
  display: inline-block;
  font-weight: 400;
  margin-right: 15px;
  padding-left: 25px;
  position: relative;
  padding-top: 2px;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #303757;
  font-size: 14px;
  padding: 0;
  margin: 0;
  height: 20px;
}

.emotion-class svg {
  left: 0;
  top: 0;
  position: absolute;
}

.emotion-class a {
  color: #A6ABBF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class a:hover {
  color: #465AB6;
}

.emotion-class>svg {
  display: none;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #FFFFFF;
  background-color: var(--statuses-status-inactive);
  font-family: "Montserrat",sans-serif;
  font-weight: 700;
  font-size: 10px;
  line-height: 20px;
  letter-spacing: 0.5px;
  text-align: center;
  text-transform: uppercase;
  border-radius: var(--spacing-2xs);
  margin: 0;
  padding: 0 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: 20px;
  height: 100%;
  overflow: hidden;
}

.emotion-class svg {
  margin: -5px -6px 0 -6px;
}

.emotion-class {
  fill: #E4E5EB;
  width: 29px;
  height: 29px;
  fill-rule: evenodd;
  vertical-align: middle;
}

<div>
  <div
    class="emotion-class"
    data-testid="InsuranceTableWrapper"
  >
    <h3
      class="emotion-class"
    >
      Insurances
    </h3>
    <div
      class="emotion-class"
    >
      <div
        data-testid="Search_DataTable"
      >
        <div
          class=" emotion-class"
          data-testid="Search_DataTable_Table"
        >
          <div
            class="emotion-class"
            style="display: flex;"
          >
            <div
              class="trigger"
              style="position: relative; left: 1px;"
            />
            <table
              cellpadding="0"
              cellspacing="0"
              class=" emotion-class"
            >
              <thead>
                <tr>
                  <th
                    class="emotion-class"
                    data-testid="DataTable_ColumnHeader_Status"
                    style="color: var(--statuses-status-processed);"
                  >
                    Status
                  </th>
                  <th
                    class="emotion-class"
                    data-testid="DataTable_ColumnHeader_Name"
                    style="color: var(--statuses-status-processed);"
                  >
                    Name
                  </th>
                  <th
                    class="emotion-class"
                    data-testid="DataTable_ColumnHeader_Source"
                    style="color: var(--statuses-status-processed);"
                  >
                    Source
                  </th>
                  <th
                    class="emotion-class"
                    data-testid="DataTable_ColumnHeader_Type"
                    style="color: var(--statuses-status-processed);"
                  >
                    Type
                  </th>
                  <th
                    class="emotion-class"
                    data-testid="DataTable_ColumnHeader_Uploaded"
                    style="color: var(--statuses-status-processed);"
                  >
                    Uploaded
                  </th>
                  <th
                    class="emotion-class"
                    data-testid="DataTable_ColumnHeader_Expiry date"
                    style="color: var(--statuses-status-processed);"
                  >
                    Expiry date
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    data-testid="CellFactory_CellStatus"
                    style="padding: 13px 10px 12px;"
                  >
                    <span
                      style="width: 115px; display: inline-block; vertical-align: top;"
                    >
                      <div
                        class="emotion-class"
                        data-testid="Chip"
                      >
                        approved
                      </div>
                    </span>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 35%; width: 35%;"
                  >
                    <a
                      class="emotion-class"
                      href="http://localhost:3000/api/insurances/2/download/"
                      rel="noopener noreferrer"
                      style="text-decoration: none;"
                      tabindex="-1"
                      target="_blank"
                    >
                      Proof of insurance: Workers' Compensation
                    </a>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 300px;"
                  >
                    Job opening: How to eat healthy talk
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    Proof of insurance
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <span
                      class="emotion-class"
                    >
                      07/18/2024
                    </span>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <span
                      class="emotion-class"
                    >
                      08/01/2024
                    </span>
                  </td>
                </tr>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    data-testid="CellFactory_CellStatus"
                    style="padding: 13px 10px 12px;"
                  >
                    <span
                      style="width: 115px; display: inline-block; vertical-align: top;"
                    >
                      <div
                        class="emotion-class"
                        data-testid="Chip"
                      >
                        rejected
                      </div>
                    </span>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 35%; width: 35%;"
                  >
                    <a
                      class="emotion-class"
                      href="http://localhost:3000/api/insurances/1/download/"
                      rel="noopener noreferrer"
                      style="text-decoration: none;"
                      tabindex="-1"
                      target="_blank"
                    >
                      Proof of insurance: General Liability
                    </a>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 300px;"
                  >
                    Job opening: Translation of the book
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    Proof of insurance
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <span
                      class="emotion-class"
                    >
                      07/18/2024
                    </span>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 11px 10px 10px;"
                  >
                    <span
                      class="emotion-class"
                    >
                      <span
                        class="EmptyIconGrey emotion-class"
                      >
                        <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m14%2027c12.3431%2027%2011%2025.6569%2011%2024c11%2022.3431%2012.3431%2021%2014%2021h34c35.6569%2021%2037%2022.3431%2037%2024c37%2025.6569%2035.6569%2027%2034%2027h14z'/%3e%3c/svg%3e
                          class="emotion-class"
                          fill="#E4E5EB"
                        />
                      </span>
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
            <div
              class="trigger"
              style="position: relative; right: 1px;"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`InsuranceViewForBuyer > should rander InsuranceView with partial data (paginated) 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-l);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-s-plus);
}

.emotion-class {
  position: relative;
}

.emotion-class.tableStart:before,
.emotion-class.tableEnd:after {
  opacity: 1;
  z-index: 2;
}

.emotion-class:before,
.emotion-class:after {
  opacity: 0;
  pointer-events: none;
  content: "";
  display: block;
  position: absolute;
  top: 0;
  height: 100%;
  width: 10px;
  -webkit-transition: opacity 0.3s ease-in-out;
  transition: opacity 0.3s ease-in-out;
}

.emotion-class:before {
  left: 0;
  background: linear-gradient(90deg, rgba(8, 10, 26, 0.1) 0%, rgba(8, 10, 26, 0) 80%);
  border-radius: 4px 0px 0px 4px;
}

.emotion-class:after {
  right: 0;
  background: linear-gradient(270deg, rgba(8, 10, 26, 0.1) 0%, rgba(8, 10, 26, 0) 80%);
  border-radius: 0px 4px 4px 0px;
}

.emotion-class {
  overflow-x: auto;
  border-radius: 4px;
}

.emotion-class {
  border-collapse: separate;
  width: 100%;
  height: 1px;
}

.emotion-class.mobileView tr {
  border: 1px solid #E4E5EB;
  border-color: #E4E5EB;
  border-radius: 4px;
  padding: 20px;
}

.emotion-class.mobileView td {
  min-height: 1px;
  background-color: #FFFFFF;
  border: 0;
  margin: 0;
  padding: 0!important;
  margin-bottom: 12px;
  font-size: 16px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  font-size: 14px;
  text-align: left;
  padding: 10px;
  border-width: 1px 1px 0 0;
  border-style: solid;
  border-color: #E4E5EB;
  background-color: #F7F7F7;
  box-sizing: border-box;
}

.emotion-class:first-of-type {
  border-top-left-radius: 4px;
  border-left: 1px solid #E4E5EB;
}

.emotion-class:last-of-type {
  border-top-right-radius: 4px;
}

.emotion-class:focus-within {
  background-color: #F5F6FA;
}

.emotion-class:first-of-type td {
  border-top: 1px solid #E4E5EB;
}

.emotion-class:last-of-type td:first-of-type {
  border-bottom-left-radius: 4px;
}

.emotion-class:last-of-type td:last-of-type {
  border-bottom-right-radius: 4px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  vertical-align: top;
  border-bottom: 1px solid #E4E5EB;
  border-right: 1px solid #E4E5EB;
  padding: 15px 10px;
}

.emotion-class:first-of-type {
  border-left: 1px solid #E4E5EB;
}

.emotion-class:last-of-type {
  border-right: 1px solid #E4E5EB;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #FFFFFF;
  background-color: var(--statuses-status-active);
  font-family: "Montserrat",sans-serif;
  font-weight: 700;
  font-size: 10px;
  line-height: 20px;
  letter-spacing: 0.5px;
  text-align: center;
  text-transform: uppercase;
  border-radius: var(--spacing-2xs);
  margin: 0;
  padding: 0 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #465AB6;
  cursor: pointer;
  font-weight: 600;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #303757;
  display: inline-block;
  font-weight: 400;
  margin-right: 15px;
  padding-left: 25px;
  position: relative;
  padding-top: 2px;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #303757;
  font-size: 14px;
  padding: 0;
  margin: 0;
  height: 20px;
}

.emotion-class svg {
  left: 0;
  top: 0;
  position: absolute;
}

.emotion-class a {
  color: #A6ABBF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class a:hover {
  color: #465AB6;
}

.emotion-class>svg {
  display: none;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-link-color)!important;
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 20px;
  cursor: pointer;
  font-weight: 600;
}

.emotion-class * {
  fill: var(--text-link-color);
}

<div>
  <div
    class="emotion-class"
    data-testid="InsuranceTableWrapper"
  >
    <h3
      class="emotion-class"
    >
      Insurances
    </h3>
    <div
      class="emotion-class"
    >
      <div
        data-testid="Search_DataTable"
      >
        <div
          class=" emotion-class"
          data-testid="Search_DataTable_Table"
        >
          <div
            class="emotion-class"
            style="display: flex;"
          >
            <div
              class="trigger"
              style="position: relative; left: 1px;"
            />
            <table
              cellpadding="0"
              cellspacing="0"
              class=" emotion-class"
            >
              <thead>
                <tr>
                  <th
                    class="emotion-class"
                    data-testid="DataTable_ColumnHeader_Status"
                    style="color: var(--statuses-status-processed);"
                  >
                    Status
                  </th>
                  <th
                    class="emotion-class"
                    data-testid="DataTable_ColumnHeader_Name"
                    style="color: var(--statuses-status-processed);"
                  >
                    Name
                  </th>
                  <th
                    class="emotion-class"
                    data-testid="DataTable_ColumnHeader_Source"
                    style="color: var(--statuses-status-processed);"
                  >
                    Source
                  </th>
                  <th
                    class="emotion-class"
                    data-testid="DataTable_ColumnHeader_Type"
                    style="color: var(--statuses-status-processed);"
                  >
                    Type
                  </th>
                  <th
                    class="emotion-class"
                    data-testid="DataTable_ColumnHeader_Uploaded"
                    style="color: var(--statuses-status-processed);"
                  >
                    Uploaded
                  </th>
                  <th
                    class="emotion-class"
                    data-testid="DataTable_ColumnHeader_Expiry date"
                    style="color: var(--statuses-status-processed);"
                  >
                    Expiry date
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  class="emotion-class"
                >
                  <td
                    class="emotion-class"
                    data-testid="CellFactory_CellStatus"
                    style="padding: 13px 10px 12px;"
                  >
                    <span
                      style="width: 115px; display: inline-block; vertical-align: top;"
                    >
                      <div
                        class="emotion-class"
                        data-testid="Chip"
                      >
                        approved
                      </div>
                    </span>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 35%; width: 35%;"
                  >
                    <a
                      class="emotion-class"
                      href="http://localhost:3000/api/insurances/2/download/"
                      rel="noopener noreferrer"
                      style="text-decoration: none;"
                      tabindex="-1"
                      target="_blank"
                    >
                      Proof of insurance: Workers' Compensation
                    </a>
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 300px;"
                  >
                    Job opening: How to eat healthy talk
                  </td>
                  <td
                    class="emotion-class"
                    colspan="1"
                    style="padding: 13px 10px 12px; min-width: 200px;"
                  >
                    Proof of insurance
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <span
                      class="emotion-class"
                    >
                      07/18/2024
                    </span>
                  </td>
                  <td
                    class="emotion-class"
                    style="padding: 13px 10px 12px;"
                  >
                    <span
                      class="emotion-class"
                    >
                      08/01/2024
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
            <div
              class="trigger"
              style="position: relative; right: 1px;"
            />
          </div>
        </div>
      </div>
      <span
        class="emotion-class"
        style="align-self: flex-end;"
      >
        Show more
      </span>
    </div>
  </div>
</div>
`;

exports[`InsuranceViewForBuyer > should rander empty InsuranceView 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-l);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
}

.emotion-class {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  position: relative;
}

.emotion-class::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--borders-encapsulation-separator-divider);
  top: 50%;
}

.emotion-class {
  margin: 0 auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: center;
  padding: 60px 0;
  margin: 0;
}

.emotion-class a span {
  font-size: 14px!important;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
}

<div>
  <div
    class="emotion-class"
    data-testid="InsuranceTableWrapper"
  >
    <h3
      class="emotion-class"
    >
      Insurances
    </h3>
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      />
      <div
        class="emotion-class"
        data-testid="ZeroState"
      >
        <data:image/svg+xml,%3csvg%20width='80'%20height='80'%20viewbox='0%200%2080%2080'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2017.5h32c32%2015.2909%2030.2091%2013.5%2028%2013.5c25.7909%2013.5%2024%2015.2909%2024%2017.5z'%20fill='%23f5f6fa'/%3e%3cpath%20d='m56%2017.5c56%2015.2909%2054.2091%2013.5%2052%2013.5c49.7909%2013.5%2048%2015.2909%2048%2017.5h56z'%20fill='%23f5f6fa'/%3e%3cpath%20d='m24%2017.5h32c32%2015.2909%2030.2091%2013.5%2028%2013.5c25.7909%2013.5%2024%2015.2909%2024%2017.5z'%20stroke='%23b4bce0'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='m56%2017.5c56%2015.2909%2054.2091%2013.5%2052%2013.5c49.7909%2013.5%2048%2015.2909%2048%2017.5h56z'%20stroke='%23b4bce0'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3ccircle%20cx='60'%20cy='47.5'%20r='19'%20fill='%23d1d6ed'%20stroke='%23b4bce0'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3ccircle%20cx='60'%20cy='47.5'%20r='13'%20fill='%23f5f6fa'%20stroke='%23b4bce0'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='m60.319%2040.9399c56.453%2040.9399%2053.319%2044.0739%2053.319%2047.9399'%20stroke='%23b4bce0'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3ccircle%20cx='20'%20cy='47.5'%20r='19'%20fill='%23d1d6ed'%20stroke='%23b4bce0'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='m35.4938%2058.5c37.7017%2055.3957%2039%2051.5995%2039%2047.5c39%2043.4005%2037.7017%2039.6043%2035.4938%2036.5h44.5062c42.2983%2039.6043%2041%2043.4005%2041%2047.5c41%2051.5995%2042.2983%2055.3957%2044.5062%2058.5h35.4938z'%20fill='%23f5f6fa'%20stroke='%23b4bce0'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='m6.04761%2034.603l20%2017.5h35l40%2022.5l45%2017.5h60l73.9525%2034.603c70.4816%2030.8499%2065.5152%2028.5%2060%2028.5c53.6061%2028.5%2047.9499%2031.6583%2044.5063%2036.5h35.4938c32.0502%2031.6583%2026.394%2028.5%2020%2028.5c14.4849%2028.5%209.51852%2030.8499%206.04761%2034.603z'%20fill='%23f5f6fa'%20stroke='%23b4bce0'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3ccircle%20cx='20'%20cy='47.5'%20r='13'%20fill='%23f5f6fa'%20stroke='%23b4bce0'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='m20.319%2040.9399c16.453%2040.9399%2013.319%2044.0739%2013.319%2047.9399'%20stroke='%23b4bce0'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3c/svg%3e
          classname="css-1pmfpj8"
          size="60"
        />
        <h5
          class="emotion-class"
          data-testid="ZeroState_Header"
          style="color: #A6ABBF; margin-top: 0px; font-weight: 600;"
        >
          Partner doesn't have any insurances yet
        </h5>
      </div>
      <div
        class="emotion-class"
      />
    </div>
  </div>
</div>
`;

exports[`InsuranceViewForBuyer > should rander error InsuranceView 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-l);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 22px;
  line-height: 30px;
}

.emotion-class {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  position: relative;
}

.emotion-class::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--borders-encapsulation-separator-divider);
  top: 50%;
}

.emotion-class {
  margin: 0 auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  text-align: center;
  padding: 60px 0;
  margin: 0;
}

.emotion-class a span {
  font-size: 14px!important;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
}

<div>
  <div
    class="emotion-class"
    data-testid="InsuranceTableWrapper"
  >
    <h3
      class="emotion-class"
    >
      Insurances
    </h3>
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      />
      <div
        class="emotion-class"
        data-testid="ZeroState"
      >
        <data:image/svg+xml,%3csvg%20width='80'%20height='80'%20viewbox='0%200%2080%2080'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2017.5h32c32%2015.2909%2030.2091%2013.5%2028%2013.5c25.7909%2013.5%2024%2015.2909%2024%2017.5z'%20fill='%23f5f6fa'/%3e%3cpath%20d='m56%2017.5c56%2015.2909%2054.2091%2013.5%2052%2013.5c49.7909%2013.5%2048%2015.2909%2048%2017.5h56z'%20fill='%23f5f6fa'/%3e%3cpath%20d='m24%2017.5h32c32%2015.2909%2030.2091%2013.5%2028%2013.5c25.7909%2013.5%2024%2015.2909%2024%2017.5z'%20stroke='%23b4bce0'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='m56%2017.5c56%2015.2909%2054.2091%2013.5%2052%2013.5c49.7909%2013.5%2048%2015.2909%2048%2017.5h56z'%20stroke='%23b4bce0'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3ccircle%20cx='60'%20cy='47.5'%20r='19'%20fill='%23d1d6ed'%20stroke='%23b4bce0'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3ccircle%20cx='60'%20cy='47.5'%20r='13'%20fill='%23f5f6fa'%20stroke='%23b4bce0'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='m60.319%2040.9399c56.453%2040.9399%2053.319%2044.0739%2053.319%2047.9399'%20stroke='%23b4bce0'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3ccircle%20cx='20'%20cy='47.5'%20r='19'%20fill='%23d1d6ed'%20stroke='%23b4bce0'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='m35.4938%2058.5c37.7017%2055.3957%2039%2051.5995%2039%2047.5c39%2043.4005%2037.7017%2039.6043%2035.4938%2036.5h44.5062c42.2983%2039.6043%2041%2043.4005%2041%2047.5c41%2051.5995%2042.2983%2055.3957%2044.5062%2058.5h35.4938z'%20fill='%23f5f6fa'%20stroke='%23b4bce0'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='m6.04761%2034.603l20%2017.5h35l40%2022.5l45%2017.5h60l73.9525%2034.603c70.4816%2030.8499%2065.5152%2028.5%2060%2028.5c53.6061%2028.5%2047.9499%2031.6583%2044.5063%2036.5h35.4938c32.0502%2031.6583%2026.394%2028.5%2020%2028.5c14.4849%2028.5%209.51852%2030.8499%206.04761%2034.603z'%20fill='%23f5f6fa'%20stroke='%23b4bce0'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3ccircle%20cx='20'%20cy='47.5'%20r='13'%20fill='%23f5f6fa'%20stroke='%23b4bce0'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='m20.319%2040.9399c16.453%2040.9399%2013.319%2044.0739%2013.319%2047.9399'%20stroke='%23b4bce0'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3c/svg%3e
          classname="css-1pmfpj8"
          size="60"
        />
        <h5
          class="emotion-class"
          data-testid="ZeroState_Header"
          style="color: #A6ABBF; margin-top: 0px; font-weight: 600;"
        >
          Something went wrong when loading insurances
        </h5>
        <p
          class="emotion-class"
          data-testid="ZeroState_Copy"
          style="color: #A6ABBF; max-width: 800px;"
        >
          Try again later or contact us if the problem persists
        </p>
      </div>
      <div
        class="emotion-class"
      />
    </div>
  </div>
</div>
`;
