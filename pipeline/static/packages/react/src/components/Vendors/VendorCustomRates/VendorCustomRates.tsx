import * as I from '../../../assets/icons';
import i18n from '../../../i18n/i18n';
import Api from '../../../services/Api';
import { getReduxSessionUser } from '../../../services/Reducers/User.reducer.helper';
import { Colors } from '../../../styles/global.styles';
import { Vendor } from '../../../types/vendor';
import { formatCurrency } from '../../../utils/index';
import { makeCancelable } from '../../../utils/other';
import Tooltip from '../../Utils/Tooltip/Tooltip';
import {
  getVendorCustomRates,
  getVendorHourlyRate,
} from './VendorCustomRates.helpers';
import {
  CustomRateLabelTypeStyled,
  CustomRateRowStyled,
  LockIconHolderStyled,
  VendorCustomRatesLabelStyled,
  VendorCustomRatesNoValueStyled,
  VendorCustomRatesStyled,
  VendorCustomRatesValueStyled,
} from './VendorCustomRates.styles';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';

export type VendorCustomRatesVariantType =
  | 'raw'
  | 'quickView'
  | 'vendorProfile';

const VendorCustomRates = ({
  vendor,
  variant = 'raw',
  publicListKey,
}: {
  vendor: Vendor;
  variant: VendorCustomRatesVariantType;
  publicListKey?: string;
}) => {
  const [rates, setRates] = useState([]);
  const sessionUser = getReduxSessionUser();

  useEffect(() => {
    const cancelablePromise = makeCancelable(
      publicListKey
        ? Api.CustomRates.getPublic(publicListKey, vendor.slug)
        : Api.CustomRates.get(vendor.vendor_type)
    );
    cancelablePromise.promise
      .then(({ data: { custom_rates } }) => {
        setRates(custom_rates);
      })
      .catch(() => {
        // do nothing
      });
    return () => cancelablePromise.cancel();
  }, []);

  if (variant === 'raw') {
    return (
      <>
        {rates.map((rate) => (
          <div key={rate.custom_rate_type.id}>
            {getVendorCustomRates(vendor)[rate.custom_rate_type.id] && (
              <>
                {rate.custom_rate_type.label}&nbsp;
                {formatCurrency(
                  getVendorCustomRates(vendor)[rate.custom_rate_type.id]
                )}
                {getVendorHourlyRate(vendor) && (
                  <>&nbsp;{getVendorHourlyRate(vendor)}</>
                )}
              </>
            )}
          </div>
        ))}
      </>
    );
  }
  if (!rates.length) {
    return null;
  }
  return (
    <VendorCustomRatesStyled>
      <VendorCustomRatesLabelStyled variant={variant}>
        {i18n.t('CustomRates.Rates')}
      </VendorCustomRatesLabelStyled>

      {rates.map((rate) => (
        <div key={rate.custom_rate_type.id}>
          <CustomRateRowStyled key={rate.custom_rate_type.id} variant={variant}>
            {!rate.visible_to_vendor &&
              sessionUser.isUser &&
              variant !== 'quickView' && (
                <Tooltip tooltip={i18n.t('CustomRates.NotVisibleToPartners')}>
                  <LockIconHolderStyled>
                    <I.Small.LockIcon fill={Colors.grey2} />
                  </LockIconHolderStyled>
                </Tooltip>
              )}

            <CustomRateLabelTypeStyled variant={variant}>
              {rate.custom_rate_type.label}
            </CustomRateLabelTypeStyled>

            <span>
              {getVendorCustomRates(vendor)[rate.custom_rate_type.id] ? (
                <>
                  <VendorCustomRatesValueStyled variant={variant}>
                    {formatCurrency(
                      getVendorCustomRates(vendor)[rate.custom_rate_type.id]
                    )}
                  </VendorCustomRatesValueStyled>
                  {getVendorHourlyRate(vendor) && (
                    <span>&nbsp;{getVendorHourlyRate(vendor)}</span>
                  )}
                </>
              ) : (
                <VendorCustomRatesNoValueStyled>
                  {i18n.t('CustomRates.NoResponse')}
                </VendorCustomRatesNoValueStyled>
              )}
            </span>
          </CustomRateRowStyled>
        </div>
      ))}
    </VendorCustomRatesStyled>
  );
};

VendorCustomRates.propTypes = {
  vendor: PropTypes.object,
  variant: PropTypes.string,
};

export default VendorCustomRates;
