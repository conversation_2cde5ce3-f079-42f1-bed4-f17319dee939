import { Colors, Margins } from '../../../styles/global.styles';
import styled from '@emotion/styled';
import React from 'react';

export const VendorCardGridWrapper = styled('div')(
  ({ style }: { style: React.CSSProperties }) => ({
    backgroundColor: Colors.white,
    padding: Margins.default,
    ...style,
  })
);

export const VendorCardGridLeftCol = styled('div')({
  display: 'flex',
  flexDirection: 'row',
  alignItems: 'center',
  gap: '10px',
});

export const VendorNameStatusWrapper = styled('div')({
  a: {
    whiteSpace: 'initial !important' as any,
  },
});

export const VendorPersonalInfoWrapper = styled('div')({
  span: {
    color: Colors.grey1,
  },
});
