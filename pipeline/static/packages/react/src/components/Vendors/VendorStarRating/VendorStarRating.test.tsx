import { getReduxTenant } from '../../../services/Reducers/Tenant.reducer.helper';
import { Vendor } from '../../../types/vendor';
import { jestSetupTenant } from '../../../utils/jest/jest.session';
import VendorStarRating from './VendorStarRating';
import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import React from 'react';

const getVendor = (count: number, score: number) => ({
  score: {
    review_count: count,
    score_average: score,
  },
});

describe('VendorStarRating should NOT BE visible when', () => {
  test('disable_vendor_star_rating flag is ON, and vendor.score.review_count > 0', async () => {
    jestSetupTenant(['disable_vendor_star_rating']);
    const tenant = getReduxTenant();
    expect(tenant.features).toEqual(
      expect.arrayContaining(['disable_vendor_star_rating'])
    );
    const { container } = render(
      <VendorStarRating vendor={getVendor(2, 4.5) as Vendor} />
    );
    expect(container).toMatchSnapshot();
  });
  test('disable_vendor_star_rating flag is OFF, and vendor.score.review_count === 0', async () => {
    jestSetupTenant([]);
    const tenant = getReduxTenant();
    expect(tenant.features).toHaveLength(0);

    const { container } = render(
      <VendorStarRating vendor={getVendor(0, 0) as Vendor} />
    );
    expect(container).toMatchSnapshot();
  });
});

describe('VendorStarRating should BE visible when', () => {
  test('disable_vendor_star_rating flag is OFF, and vendor.score.review_count > 0', async () => {
    jestSetupTenant([]);
    const tenant = getReduxTenant();
    expect(tenant.features).toHaveLength(0);
    const { container } = render(
      <VendorStarRating vendor={getVendor(2, 4.5) as Vendor} />
    );
    expect(container).toMatchSnapshot();
  });
  test('disable_vendor_star_rating flag is OFF, vendor.score.score_average is 4.5', async () => {
    jestSetupTenant([]);
    const tenant = getReduxTenant();
    expect(tenant.features).toHaveLength(0);
    const { container } = render(
      <VendorStarRating vendor={getVendor(2, 4.5) as Vendor} />
    );
    const label = await screen.findByText('4.5');
    expect(label).toBeInTheDocument();
    expect(container).toMatchSnapshot();
  });
});
