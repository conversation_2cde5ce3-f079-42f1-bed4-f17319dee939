import React from 'react';
import { FavoriteIcon } from '../../../assets/icons/small';
import { withPermissions } from '../../../services/Shortlist/ShortlistPay/withPermissions';
import { tenantHasFeature } from '../../../services/Shortlist/feature-flags';
import { Colors } from '../../../styles/global.styles';
import { Vendor } from '../../../types/vendor';
import { isFavouriteForSessionUser } from '../vendors.helpers';

// eslint-disable-next-line
const VendorFavoriteIcon = ({ vendor }: { vendor: Vendor }) => (
  <FavoriteIcon fill={Colors.pink1} />
);

export default withPermissions(VendorFavoriteIcon, {
  condition: ({ vendor }) => {
    if (!tenantHasFeature('vendor_favorites')) {
      return false;
    } else if (isFavouriteForSessionUser(vendor)) {
      return true;
    }
    return false;
  },
});
