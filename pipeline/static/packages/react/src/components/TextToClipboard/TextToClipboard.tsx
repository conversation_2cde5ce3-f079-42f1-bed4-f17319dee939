import styled from '@emotion/styled';
import { Box } from '@mui/system';
import i18n from '../../i18n/i18n';
import {
  displayFailureMessage,
  displaySuccessMessage,
} from '../../services/Reducers/Notifications.reducer.helper';
import { BodySmall } from '../../styles/typography';
import { generateTestId } from '../../utils/test.utils';
import Button from '../FormElements/Button/Button';

const t = (key: string, params?: { text: string }) =>
  i18n.t(`TextToClipboard:${key}`, params);

const TextToClipboardContent = styled(BodySmall)({
  color: 'var(--glyphs-basic-glyphs-subdued)',
  verticalAlign: 'middle',
});

const TextToClipboard = ({ text, label }: { text: string; label?: string }) => {
  const copyToClipboard = async () => {
    const shortenedText =
      text.length > 30 ? `${text.substring(0, 30)}...` : text;

    try {
      await navigator.clipboard.writeText(text);
      displaySuccessMessage(
        t('ClipboardSuccessMessage', { text: shortenedText })
      );
    } catch (error) {
      displayFailureMessage(t('ClipboardErrorMessage'));
    }
  };
  if (!text) {
    return <>-</>;
  }
  return (
    <Box display="flex" alignItems="center" gap="var(--spacing-s)">
      <TextToClipboardContent {...generateTestId('TextToClipboard')}>
        {label ?? text}
      </TextToClipboardContent>
      <Button
        variant="tertiary"
        iconStart="CopyIcon"
        tooltipValue={t('ClickToCopy')}
        rounded={false}
        onClick={copyToClipboard}
        size="small"
      />
    </Box>
  );
};

export default TextToClipboard;
