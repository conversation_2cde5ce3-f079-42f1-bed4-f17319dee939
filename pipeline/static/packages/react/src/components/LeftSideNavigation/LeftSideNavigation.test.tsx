import * as stories from './LeftSideNavigation.stories';
import { composeStories } from '@storybook/react';
import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';

describe('LeftSideNavigation - ', () => {
  test('check with snapshot', async () => {
    const { Default: LeftSideNavigation } = composeStories(stories);
    render(<LeftSideNavigation />);
    expect(screen.getByTestId('Stories_LeftSideNavigation')).toMatchSnapshot();
  });

  test('check hover with snapshot', async () => {
    const { Default: LeftSideNavigation } = composeStories(stories);
    render(<LeftSideNavigation />);
    fireEvent.mouseOver(screen.getByTestId('LeftSideNavItem_2'));
    expect(screen.getByTestId('LeftSideNavItem_2')).toMatchSnapshot();
  });
});
