import React, { useState } from 'react';
/* eslint-disable */
import { getLoremIpsumParagraphs } from '../../utils/storybook-utils';
import LeftSideNavigation, { NavItemType } from './LeftSideNavigation';

export default {
  title: 'Core/Navigation/LeftSideNavigation',
  component: LeftSideNavigation,
};

const Template = () => {
  const items: NavItemType[] = [
    {
      id: 1,
      label: 'Contract Setup Form',
      isActive: true,
      onClick: () => setActiveElementId(1),
    },
    {
      id: 2,
      label: 'Contract Workflow',
      onClick: () => setActiveElementId(2),
    },
    {
      id: 3,
      label: 'Contract Acceptance',
      hasSeparator: true,
      onClick: () => setActiveElementId(3),
    },
    {
      id: 4,
      label: 'Automations',
      iconName: 'AutomationsIcon',
      onClick: () => setActiveElementId(4),
    },
    {
      id: 5,
      label: 'Contract Settings',
      onClick: () => setActiveElementId(5),
    },
  ];
  const [activeElementId, setActiveElementId] = useState(1);
  return (
    <>
      <h1>Name of the Contract Template</h1>
      <div
        style={{
          display: 'flex',
        }}
      >
        <div data-testid={'Stories_LeftSideNavigation'}>
          <LeftSideNavigation
            navItems={items.map((item) => ({
              ...item,
              isActive: item.id === activeElementId,
            }))}
            hasShadow={true}
          />
        </div>
        <div
          style={{
            padding: '24px 40px',
          }}
        >
          {getLoremIpsumParagraphs(2)}
        </div>
      </div>
    </>
  );
};

export const Default = Template;
