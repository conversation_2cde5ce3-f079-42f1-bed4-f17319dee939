// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TabBar - to match snapshot 1`] = `
.emotion-class {
  border-bottom: 1px solid #E4E5EB;
}

.emotion-class {
  white-space: nowrap;
  overflow-x: hidden;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  min-height: 24px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}

.emotion-class ul {
  white-space: nowrap;
  list-style: none;
  padding: 0;
  margin: 0;
  overflow-x: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class ul li {
  display: inline-block;
}

.emotion-class .dropdown {
  text-align: right;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  height: 50px;
  cursor: pointer;
  margin-right: 25px;
  overflow-x: hidden;
}

.emotion-class::after {
  content: "";
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 3px;
  background-color: #465AB6;
}

.emotion-class .tabBarLockIcon {
  fill: #303757;
}

.emotion-class .tabBarTitleBadgeLabel {
  background-color: #465AB6;
}

.emotion-class:hover .tabBarLockIcon {
  fill: #465AB6;
}

.emotion-class:hover .tabBarTitleLabel {
  color: #465AB6;
}

.emotion-class:hover .tabBarTitleBadgeLabel {
  background-color: #465AB6;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #303757;
  font-family: "Montserrat",sans-serif;
  text-transform: uppercase;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  height: 50px;
  cursor: pointer;
  margin-right: 25px;
  overflow-x: hidden;
}

.emotion-class:hover .tabBarLockIcon {
  fill: #465AB6;
}

.emotion-class:hover .tabBarTitleLabel {
  color: #465AB6;
}

.emotion-class:hover .tabBarTitleBadgeLabel {
  background-color: #465AB6;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #A6ABBF;
  font-family: "Montserrat",sans-serif;
  text-transform: uppercase;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  background-color: #A6ABBF;
  color: #FFFFFF;
  font-family: "Montserrat",sans-serif;
  border-radius: 10px;
  font-weight: 700;
  padding: 2px 5px;
  margin-left: 5px;
  min-width: 8px;
  text-align: center;
  line-height: 14px;
  box-sizing: content-box;
}

.emotion-class {
  position: relative;
  width: 9px;
  height: 24px;
  padding-right: 5px;
  box-sizing: content-box;
}

<div>
  <div
    style="width: 100%;"
  >
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      >
        <ul>
          <li>
            <ul>
              <a
                aria-label=""
                class=" emotion-class"
                data-mui-internal-clone-element="true"
              >
                <span
                  class="emotion-class"
                >
                  <span
                    class="tabBarTitleLabel emotion-class"
                  >
                    First item
                  </span>
                </span>
              </a>
            </ul>
          </li>
          <li>
            <ul>
              <a
                aria-label="Tooltip here"
                class=" emotion-class"
                data-mui-internal-clone-element="true"
              >
                <span
                  class="emotion-class"
                >
                  <span
                    class="tabBarTitleLabel emotion-class"
                  >
                    Second item
                  </span>
                </span>
              </a>
            </ul>
          </li>
          <li>
            <ul>
              <a
                aria-label=""
                class=" emotion-class"
                data-mui-internal-clone-element="true"
              >
                <span
                  class="emotion-class"
                >
                  <span
                    class="tabBarTitleLabel emotion-class"
                  >
                    Third item
                  </span>
                </span>
              </a>
            </ul>
          </li>
          <li>
            <ul>
              <a
                aria-label=""
                class=" emotion-class"
                data-mui-internal-clone-element="true"
              >
                <span
                  class="emotion-class"
                >
                  <span
                    class="tabBarTitleLabel emotion-class"
                  >
                    Fourth item
                  </span>
                  <span
                    class="tabBarTitleBadgeLabel emotion-class"
                  >
                    1
                  </span>
                </span>
              </a>
            </ul>
          </li>
          <li>
            <ul>
              <a
                aria-label=""
                class=" emotion-class"
                data-mui-internal-clone-element="true"
              >
                <span
                  class="emotion-class"
                >
                  <span
                    class="tabBarTitleLabel emotion-class"
                  >
                    Fifth item
                  </span>
                  <span
                    class="tabBarTitleBadgeLabel emotion-class"
                  >
                    5
                  </span>
                </span>
              </a>
            </ul>
          </li>
          <li>
            <ul>
              <a
                aria-label=""
                class=" emotion-class"
                data-mui-internal-clone-element="true"
              >
                <span
                  class="emotion-class"
                >
                  <div
                    class="emotion-class"
                  >
                    <icon-mock
                      classname="tabBarLockIcon css-1r0hosi"
                      fill="#A6ABBF"
                      size="24"
                    />
                  </div>
                  <span
                    class="tabBarTitleLabel emotion-class"
                  >
                    Sixth item
                  </span>
                  <span
                    class="tabBarTitleBadgeLabel emotion-class"
                  >
                    16
                  </span>
                </span>
              </a>
            </ul>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
`;
