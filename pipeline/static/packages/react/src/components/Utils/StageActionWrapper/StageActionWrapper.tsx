import React, { ForwardedRef, forwardRef, ReactNode } from 'react';
import { Label } from '../../../styles/typography';
import { generateTestId } from '../../../utils/test.utils';
import {
  RemoveIconStyled,
  StyledStageActionContainer,
  StyledStageActionRow,
} from './StageActionWrapper.styles';

const StageActionWrapper = forwardRef(
  (
    {
      label,
      children,
      onRemove,
      testId,
    }: {
      label: string;
      children?: ReactNode;
      onRemove?: () => void;
      testId?: string;
    },
    ref: ForwardedRef<HTMLDivElement>
  ) => (
    <StyledStageActionContainer
      ref={ref}
      {...generateTestId(testId, 'StageActionWrapper')}
    >
      <StyledStageActionRow>
        <Label>{label}</Label>
        {onRemove && (
          <RemoveIconStyled
            fill="var(--actions-subtle-actions-action-subtle)"
            size={28}
            onClick={onRemove}
          />
        )}
      </StyledStageActionRow>

      {children}
    </StyledStageActionContainer>
  )
);

export default StageActionWrapper;
