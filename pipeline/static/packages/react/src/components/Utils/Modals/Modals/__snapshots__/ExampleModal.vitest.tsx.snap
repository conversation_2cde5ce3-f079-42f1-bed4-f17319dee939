// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`ExampleModal -  > SidePanel modal snapshot should match 1`] = `
.emotion-class {
  position: absolute;
  right: 100px;
  top: 27px;
}

.emotion-class span {
  font-size: 14px;
  margin: 0 5px;
}

.emotion-class {
  position: relative;
}

.emotion-class svg {
  fill: #465AB6;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class {
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
  font-size: 13px;
  color: #465AB6;
  vertical-align: middle;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  margin-top: 30px;
  border-top: 1px solid #E4E5EB;
  padding-top: 40px;
}

@media (max-width: 768px) {
  .emotion-class {
    padding-top: 15px;
  }
}

.emotion-class button,
.emotion-class a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-base);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: #FFFFFF;
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(1.15);
  filter: brightness(1.15);
}

.emotion-class {
  color: #FFFFFF;
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: #F7F7F7;
  border: 1px solid var(--action-base);
  padding-top: 0;
  padding-right: 19px;
  padding-bottom: 0;
  padding-left: 19px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 33px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

<div
  data-testid="ExampleModal_Container"
  style="border: 2px dashed darkgrey;"
>
  <div
    class="ModalBody emotion-class"
  >
    <div
      class="emotion-class"
    >
      <a
        class=" emotion-class"
      >
        <span
          class="emotion-class"
        >
          Header button
        </span>
        <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m22%206c22%204.89543%2022.8954%204%2024%204c25.1046%204%2026%204.89543%2026%206v8h22v6zm10%2037c10%2038.6569%2011.3431%2040%2013%2040h27.6402c27.2045%2039.0705%2026.9153%2038.0604%2026.8028%2037h13v12h35v26.5827c35.4093%2026.5282%2035.8265%2026.5%2036.25%2026.5c36.8473%2026.5%2037.4323%2026.556%2038%2026.6631v11c38%209.34315%2036.6569%208%2035%208h27c27%209.65685%2025.6569%2011%2024%2011c22.3431%2011%2021%209.65685%2021%208h13c11.3431%208%2010%209.34315%2010%2011v37zm27.6402%2032c28.3608%2030.4627%2029.482%2029.1458%2030.8668%2028.1864c30.0747%2026.2388%2025.9481%2025.0741%2023.8444%2025.0741c21.5111%2025.0741%2017%2026.507%2017%2028.8519v32h27.6402zm28.2%2019.0926c28.2%2021.4117%2026.4474%2023.1852%2024.1556%2023.1852c21.8637%2023.1852%2020.1111%2021.4117%2020.1111%2019.0926c20.1111%2016.7735%2021.8637%2015%2024.1556%2015c26.4474%2015%2028.2%2016.7735%2028.2%2019.0926zm36.25%2028c31.85%2028%2028.25%2031.6%2028.25%2036c28.25%2040.4%2031.85%2044%2036.25%2044c40.65%2044%2044.25%2040.4%2044.25%2036c44.25%2031.6%2040.65%2028%2036.25%2028zm32.1455%2034.5873l30.9751%2035.7577l35.1465%2039.9291l41.5248%2033.5508l40.3544%2032.3804l35.1465%2037.5883l32.1455%2034.5873z'/%3e%3c/svg%3e
          classname="css-12ecxzt"
          size="20"
          style="margin: 0px -2px 0px 5px;"
        />
      </a>
    </div>
    [content]
  </div>
  <div
    class="ModalFooter emotion-class"
  >
    <button
      class=" emotion-class"
    >
      <span
        class="emotion-class"
      >
        Save and Exit
      </span>
    </button>
    <button
      class=" emotion-class"
    >
      <span
        class="emotion-class"
      >
        Save and Add
      </span>
    </button>
    <button
      class=" emotion-class"
    >
      <span
        class="emotion-class"
      >
        Cancel
      </span>
    </button>
  </div>
</div>
`;

exports[`ExampleModal -  > SidePanel with hidden close button should not display close button 1`] = `
.emotion-class {
  position: absolute;
  right: 100px;
  top: 27px;
}

.emotion-class span {
  font-size: 14px;
  margin: 0 5px;
}

.emotion-class {
  position: relative;
}

.emotion-class svg {
  fill: #465AB6;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class {
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
  font-size: 13px;
  color: #465AB6;
  vertical-align: middle;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  margin-top: 30px;
  border-top: 1px solid #E4E5EB;
  padding-top: 40px;
}

@media (max-width: 768px) {
  .emotion-class {
    padding-top: 15px;
  }
}

.emotion-class button,
.emotion-class a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-base);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: #FFFFFF;
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(1.15);
  filter: brightness(1.15);
}

.emotion-class {
  color: #FFFFFF;
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: #F7F7F7;
  border: 1px solid var(--action-base);
  padding-top: 0;
  padding-right: 19px;
  padding-bottom: 0;
  padding-left: 19px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 33px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

<div
  data-testid="ExampleModal_Container"
  style="border: 2px dashed darkgrey;"
>
  <div
    class="ModalBody emotion-class"
  >
    <div
      class="emotion-class"
    >
      <a
        class=" emotion-class"
      >
        <span
          class="emotion-class"
        >
          Header button
        </span>
        <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m22%206c22%204.89543%2022.8954%204%2024%204c25.1046%204%2026%204.89543%2026%206v8h22v6zm10%2037c10%2038.6569%2011.3431%2040%2013%2040h27.6402c27.2045%2039.0705%2026.9153%2038.0604%2026.8028%2037h13v12h35v26.5827c35.4093%2026.5282%2035.8265%2026.5%2036.25%2026.5c36.8473%2026.5%2037.4323%2026.556%2038%2026.6631v11c38%209.34315%2036.6569%208%2035%208h27c27%209.65685%2025.6569%2011%2024%2011c22.3431%2011%2021%209.65685%2021%208h13c11.3431%208%2010%209.34315%2010%2011v37zm27.6402%2032c28.3608%2030.4627%2029.482%2029.1458%2030.8668%2028.1864c30.0747%2026.2388%2025.9481%2025.0741%2023.8444%2025.0741c21.5111%2025.0741%2017%2026.507%2017%2028.8519v32h27.6402zm28.2%2019.0926c28.2%2021.4117%2026.4474%2023.1852%2024.1556%2023.1852c21.8637%2023.1852%2020.1111%2021.4117%2020.1111%2019.0926c20.1111%2016.7735%2021.8637%2015%2024.1556%2015c26.4474%2015%2028.2%2016.7735%2028.2%2019.0926zm36.25%2028c31.85%2028%2028.25%2031.6%2028.25%2036c28.25%2040.4%2031.85%2044%2036.25%2044c40.65%2044%2044.25%2040.4%2044.25%2036c44.25%2031.6%2040.65%2028%2036.25%2028zm32.1455%2034.5873l30.9751%2035.7577l35.1465%2039.9291l41.5248%2033.5508l40.3544%2032.3804l35.1465%2037.5883l32.1455%2034.5873z'/%3e%3c/svg%3e
          classname="css-12ecxzt"
          size="20"
          style="margin: 0px -2px 0px 5px;"
        />
      </a>
    </div>
    [content]
  </div>
  <div
    class="ModalFooter emotion-class"
  >
    <button
      class=" emotion-class"
    >
      <span
        class="emotion-class"
      >
        Save and Exit
      </span>
    </button>
    <button
      class=" emotion-class"
    >
      <span
        class="emotion-class"
      >
        Save and Add
      </span>
    </button>
    <button
      class=" emotion-class"
    >
      <span
        class="emotion-class"
      >
        Cancel
      </span>
    </button>
  </div>
</div>
`;
