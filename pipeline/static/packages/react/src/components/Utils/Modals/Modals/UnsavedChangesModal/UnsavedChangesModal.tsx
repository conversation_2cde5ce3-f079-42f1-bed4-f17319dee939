import React from 'react';
import Alert from '../../../../Alert/Alert';
import Button from '../../../../FormElements/Button/Button';
import { withModal } from '../../modal.hooks';
import { <PERSON>dal<PERSON>ody, ModalFooter } from '../../modal.styles';
import { ShortlistModal } from '../../modal.types';
import { Body } from '../../../../../styles/typography';
import i18n from '../../../../../i18n/i18n';

export interface UnsavedChangesModal extends ShortlistModal {
  type: 'unsaved_changes_modal';
}

const t = (id: string) => i18n.t(`Modals.UnsavedChangesModal.${id}`);

export default withModal(
  ({ modalClose, modalDismiss, modalSize }: UnsavedChangesModal) => (
    <div>
      <ModalBody
        style={{
          display: 'flex',
          gap: 'var(--spacing-s)',
          flexDirection: 'column',
        }}
      >
        <Alert tag="alert-danger" message={t('BtnPrimary')} size="large" />
        <Body>{t('MainCopy')}</Body>
      </ModalBody>
      <ModalFooter size={modalSize}>
        <Button label={t('DiscardAndLeave')} onClick={modalClose} />
        <Button
          label={t('BtnSecondary')}
          onClick={modalDismiss}
          variant="tertiary"
        />
      </ModalFooter>
    </div>
  ),
  {
    getHeader: () => t('Header'),
  }
);
