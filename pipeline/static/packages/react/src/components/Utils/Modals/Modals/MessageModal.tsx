import { safeMessage } from '../../../../utils/html';
import { generateTestId } from '../../../../utils/test.utils';
import Button from '../../../FormElements/Button/Button';
import { withModal } from '../modal.hooks';
import { ModalBody, ModalFooter } from '../modal.styles';
import { ModalSizes, ShortlistModal } from '../modal.types';
import React from 'react';

export interface MessageModal extends ShortlistModal {
  type: 'message_modal';
  header: string;
  message: string | React.ReactNode;
  confirmButtonLabel?: string;
  cancelButtonLabel?: string;
  modalSize?: ModalSizes;
  renderMessageAsHtml?: boolean;
}

export default withModal(
  ({
    modalClose,
    modalDismiss,
    modalSize,
    message,
    confirmButtonLabel,
    cancelButtonLabel,
    renderMessageAsHtml = false,
  }: MessageModal) => {
    return (
      <div data-testid="MessageModal">
        <ModalBody {...generateTestId('Body', 'MessageModal')}>
          {React.isValidElement(message) ? (
            message
          ) : renderMessageAsHtml ? (
            <div
              dangerouslySetInnerHTML={{
                __html: safeMessage(message as string),
              }}
            ></div>
          ) : (
            <div>{message}</div>
          )}
        </ModalBody>
        <ModalFooter size={modalSize}>
          {confirmButtonLabel && (
            <Button
              label={confirmButtonLabel}
              onClick={modalClose}
              testId={
                generateTestId('ConfirmButton', 'MessageModal')['data-testid']
              }
            />
          )}
          {cancelButtonLabel && (
            <Button
              label={cancelButtonLabel}
              onClick={modalDismiss}
              variant="tertiary"
              testId={
                generateTestId('CancelButton', 'MessageModal')['data-testid']
              }
            />
          )}
        </ModalFooter>
      </div>
    );
  },
  {
    getHeader: (props) => props.header,
  }
);
