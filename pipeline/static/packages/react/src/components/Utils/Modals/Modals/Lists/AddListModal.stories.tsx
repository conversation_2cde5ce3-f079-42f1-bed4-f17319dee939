import React, { useEffect } from 'react';
/* eslint-disable */
import { reduxStore } from '../../../../../services/Redux';
import { jestCreateSessionUser } from '../../../../../utils/jest/jest.session';
import { getApiMockAdapter } from '../../../../../utils/storybook-utils';
import ToastMessage from '../../../../Layout/ToastMessage/ToastMessage';
import { closeModal, openModal } from '../../modal.helpers';

export default {
  title: 'Core/Modals/Lists/AddListModal',
};

export const Default = () => {
  useEffect(() => {
    reduxStore.dispatch({
      type: 'SET_USER',
      data: jestCreateSessionUser('buyer-regular', { id: 2 }),
    });
    const mock = getApiMockAdapter();
    mock
      .onPost(/\/api\/vendor_groups\//gm)
      .reply(
        200,
        require(`../../../../../fixtures/api2/vendor_groups/list_save.json`)
      )
      .onGet(/\/api\/sharable_profiles\/templates\//gm)
      .reply(
        200,
        require(
          `../../../../../fixtures/api2/sharable_profiles/template_list.json`
        )
      );
    openModal({
      type: 'add_list_modal',
    })
      .then((data) => console.log('modal closed...', data))
      .catch((data) => console.log('modal canceled...', data));
    return () => {
      mock.reset();
      closeModal('add_list_modal');
    };
  }, []);
  return <ToastMessage />;
};
