import { Colors, Inputs } from '../../../../../styles/global.styles';
import { LabelSmall } from '../../../../../styles/typography';
import styled from '@emotion/styled';

export const SharedProfileLinkContainer = styled('div')({
  marginBottom: 15,
});

export const SharedProfileLinkWrapper = styled('div')({
  ...Inputs.default,
  backgroundColor: Colors.grey4,
  borderColor: Colors.grey3,
  height: 45,
  padding: '0 10px',
  borderWidth: 1,
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  gap: 10,
  ':hover': {
    borderColor: `${Colors.grey3}`,
  },
  marginTop: 3,
});

export const SharedProfileLink = styled('div')({
  flexGrow: 1,
  textOverflow: 'ellipsis',
  overflow: 'hidden',
  whiteSpace: 'nowrap',
});

export const SharedProfileLinkDescription = styled(LabelSmall)({
  color: Colors.grey2,
});
