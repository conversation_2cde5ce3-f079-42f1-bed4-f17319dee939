import i18n from '../../../../../i18n/i18n';
import Groups from '../../../../../services/Api/Groups';
import { displaySuccessMessage } from '../../../../../services/Reducers/Notifications.reducer.helper';
import { getReduxSessionUser } from '../../../../../services/Reducers/User.reducer.helper';
import { reduxStore } from '../../../../../services/Redux';
import { Group } from '../../../../../types/group';
import Button from '../../../../FormElements/Button/Button';
import InputText from '../../../../FormElements/InputText/InputText';
import SharableTemplateSelect from '../../../SharableProfiles/SharableTemplateSelect';
import { withModal } from '../../modal.hooks';
import { <PERSON>dal<PERSON><PERSON>, ModalFooter } from '../../modal.styles';
import { ShortlistModal } from '../../modal.types';
import flatten from 'lodash/flatten';
import React, { useState } from 'react';

export interface AddListModal extends ShortlistModal {
  type: 'add_list_modal';
}

export default withModal(
  ({ modalClose, modalDismiss, modalSize }: AddListModal) => {
    const [listData, setListData] = useState({
      name: '',
      sharable_template: null,
    });
    const [errors, setErrors] = useState({ template: [], name: [] });
    const [selectedTemplate, setSelectedTemplate] = useState(null);
    const sessionUser = getReduxSessionUser();

    const validate = () => {
      const errors = { template: [], name: [] };
      if (!listData.name) {
        errors.name.push(i18n.t('Modals.AddListModal.ListNameMissingError'));
      }
      if (!selectedTemplate) {
        errors.template.push(
          i18n.t('Modals.AddListModal.ListTemplateMissingError')
        );
      }
      setErrors(errors);
      return !flatten(Object.values(errors)).length;
    };

    return (
      <div data-testid="AddListModal_Container">
        <ModalBody>
          <InputText
            label={i18n.t('Modals.AddListModal.ListNameLabel')}
            placeholder={i18n.t('Modals.AddListModal.ListNamePlaceholder')}
            autoFocus={true}
            errors={errors.name}
            maxLength={100}
            onChange={(name) => {
              setListData((prevState) => ({
                ...prevState,
                name,
              }));
            }}
          />
          <SharableTemplateSelect
            onChange={setSelectedTemplate}
            errors={errors.template}
            onSetupClick={modalClose}
          />
        </ModalBody>
        <ModalFooter size={modalSize}>
          <Button
            label={i18n.t('Modals.AddListModal.CreateButton')}
            onClick={() => {
              if (!validate()) {
                return;
              }

              Groups.saveGroup({
                name: listData.name,
                type: 'private',
                sharable_template: selectedTemplate.id,
                group_admins: [sessionUser.userData.id],
              } as Group).then(({ data }) => {
                displaySuccessMessage(
                  i18n.t('Modals.AddListModal.ListCreatedToastMessage')
                );

                reduxStore.dispatch({
                  type: 'STATE_GOTO',
                  stateName: 'app.partners.list-details',
                  stateParams: {
                    slug: data.slug,
                  },
                });

                modalClose();
              });
            }}
          />
          <Button
            variant={'secondary'}
            label={i18n.t('Modals.AddListModal.MoreOptionsButton')}
            onClick={() => {
              if (!validate()) {
                return;
              }

              reduxStore.dispatch({
                type: 'STATE_GOTO',
                stateName: 'app.list.create',
                stateParams: {
                  group: {
                    name: listData.name,
                    sharable_template: selectedTemplate,
                  },
                },
              });
              modalClose();
            }}
          />
          <Button
            variant={'tertiary'}
            label={i18n.t('Form.Cancel')}
            onClick={modalDismiss}
          />
        </ModalFooter>
      </div>
    );
  },
  {
    getHeader: () => i18n.t('Modals.AddListModal.Header'),
  }
);
