// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AddDocumentTemplateModal -  should send request when confirm button clicked 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  margin-bottom: 5px;
}

.emotion-class>a {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  box-sizing: border-box;
  width: 100%;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: row nowrap;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  white-space: nowrap;
  color: #A6ABBF;
  text-align: right;
  padding-left: 10px;
  margin-top: 5px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 150%;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  width: 100%;
  box-sizing: border-box;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #A6ABBF;
  text-align: right;
  padding-left: 10px;
  white-space: nowrap;
}

.emotion-class {
  margin-bottom: 20px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-class label {
  padding-bottom: 10px;
  padding-top: 10px;
  width: 50%;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class:hover .checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class:hover>.checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6!important;
}

.emotion-class {
  margin: 1px 0 -1px 6px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  color: #303757;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class {
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
  cursor: not-allowed;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class[disabled] svg path:nth-of-type(1) {
  fill: #FFFFFF;
}

.emotion-class>.checkboxOffIcon path:nth-of-type(2) {
  fill: #E4E5EB;
}

.emotion-class>.checkboxOnIcon path:nth-of-type(1) {
  fill: #E4E5EB;
}

.emotion-class>.checkboxOnIcon path:nth-of-type(1) {
  fill: #C5C8D5!important;
}

.emotion-class>.checkboxOnIcon path:nth-of-type(2) {
  fill: #FFFFFF;
}

.emotion-class {
  color: #A6ABBF;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.emotion-class {
  position: relative;
  width: 24px;
  height: 15px;
  display: inline-block;
}

.emotion-class svg {
  position: absolute;
  top: 1px;
  left: 3px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  margin-top: 30px;
  border-top: 1px solid #E4E5EB;
  padding-top: 40px;
}

@media (max-width: 768px) {
  .emotion-class {
    padding-top: 15px;
  }
}

.emotion-class button,
.emotion-class a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: #C5C8D5;
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: default;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: #FFFFFF;
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class {
  color: #FFFFFF;
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div
  data-testid="AddDocumentTemplateModal_Container"
>
  <div
    class="emotion-class"
  >
    <div
      class="emotion-class"
    >
      <div>
        <span
          class="emotion-class"
          data-testid="InputLabel_DocumentName"
        >
          Document name
        </span>
      </div>
    </div>
    <div
      class="emotion-class"
    >
      <input
        class="emotion-class"
        maxlength="80"
        placeholder="Document name"
        type="text"
        value="Very important document"
      />
    </div>
    <div
      class="emotion-class"
    >
      <div />
      <div
        class="emotion-class"
        data-testid="InputMaxLength"
      >
        23
         / 
        80
      </div>
    </div>
    <div
      style="margin-bottom: 5px;"
    >
      <span
        class="emotion-class"
        data-testid="InputLabel_Description"
      >
        Description
        <span
          class="emotion-class"
          style="margin-left: 10px; color: rgb(166, 171, 191);"
        >
          Optional
        </span>
      </span>
    </div>
    <div
      class="emotion-class"
    >
      <textarea
        class="emotion-class"
        maxlength="200"
        placeholder="Description"
        style="height: -104px;"
        variant="default"
      />
    </div>
    <div
      class="emotion-class"
    >
      <div />
      <div
        class="emotion-class"
      >
        0
         / 
        200
      </div>
    </div>
    <div
      class="emotion-class"
    >
      <div
        style="margin-bottom: 5px;"
      >
        <span
          class="emotion-class"
          data-testid="InputLabel_DocumentType"
        >
          Document type
        </span>
      </div>
      <div
        class="emotion-class"
      >
        <select
          class="emotion-class"
          data-testid="Select_DocumentType"
        >
          <option
            value=""
          >
            Select document type
          </option>
          <option
            data-testid="SelectOption_License"
            value="license"
          >
            Professional license
          </option>
          <option
            data-testid="SelectOption_Certificate"
            value="certificate"
          >
            Certificate
          </option>
          <option
            data-testid="SelectOption_Insurance"
            value="insurance"
          >
            General Liability insurance
          </option>
          <option
            data-testid="SelectOption_WorkersCompensation"
            value="workers compensation"
          >
            Workers compensation
          </option>
          <option
            data-testid="SelectOption_LetterOfRecommendation"
            value="letter of recommendation"
          >
            Letter of recommendation
          </option>
          <option
            data-testid="SelectOption_Sa"
            value="sa"
          >
            Services agreement
          </option>
          <option
            data-testid="SelectOption_Resume"
            value="resume"
          >
            Resume/CV
          </option>
          <option
            data-testid="SelectOption_Diploma"
            value="diploma"
          >
            Diploma
          </option>
          <option
            data-testid="SelectOption_Transcripts"
            value="transcripts"
          >
            Transcripts
          </option>
          <option
            data-testid="SelectOption_DriversLicence"
            value="drivers licence"
          >
            Drivers license
          </option>
          <option
            data-testid="SelectOption_OtherDocument"
            value="other document"
          >
            Other document
          </option>
        </select>
        <icon-mock
          classname="css-1qbf8bs"
          disabled="false"
          size="28"
        />
      </div>
    </div>
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      >
        <label
          class="emotion-class"
          data-testid="InputCheckbox"
        >
          <span
            class="emotion-class"
          >
            <input
              type="checkbox"
            />
            <icon-mock
              classname="checkboxOffIcon css-1dmky3q"
              fill="#D1D6ED"
            />
          </span>
          <span
            class="emotion-class"
          >
            <span
              aria-label=""
              class=" emotion-class"
              data-mui-internal-clone-element="true"
            >
              Verification required
            </span>
          </span>
        </label>
      </div>
      <div
        class="emotion-class"
      >
        <label
          class="emotion-class"
          data-testid="InputCheckbox"
        >
          <span
            class="emotion-class"
          >
            <input
              type="checkbox"
            />
            <icon-mock
              classname="checkboxOffIcon css-1dmky3q"
              fill="#D1D6ED"
            />
          </span>
          <span
            class="emotion-class"
          >
            <span
              aria-label=""
              class=" emotion-class"
              data-mui-internal-clone-element="true"
            >
              Expiry date required
            </span>
          </span>
        </label>
      </div>
      <div
        class="emotion-class"
      >
        <label
          class="emotion-class"
          data-testid="InputCheckbox"
        >
          <span
            class="emotion-class"
          >
            <input
              checked=""
              type="checkbox"
            />
            <icon-mock
              classname="checkboxOnIcon css-1c1jprp"
            />
          </span>
          <span
            class="emotion-class"
          >
            <span
              aria-label=""
              class=" emotion-class"
              data-mui-internal-clone-element="true"
            >
              Mandatory document
            </span>
          </span>
        </label>
      </div>
      <div
        class="emotion-class"
      >
        <label
          class="emotion-class"
          data-testid="InputCheckbox"
          disabled=""
        >
          <span
            class="emotion-class"
            disabled=""
          >
            <input
              checked=""
              type="checkbox"
            />
            <icon-mock
              classname="checkboxOnIcon css-1c1jprp"
            />
          </span>
          <span
            class="emotion-class"
          >
            <span
              aria-label=""
              class=" emotion-class"
              data-mui-internal-clone-element="true"
              disabled=""
            >
              Visible for partners
            </span>
          </span>
        </label>
      </div>
      <div
        class="emotion-class"
      >
        <label
          class="emotion-class"
          data-testid="InputCheckbox"
        >
          <span
            class="emotion-class"
          >
            <input
              checked=""
              type="checkbox"
            />
            <icon-mock
              classname="checkboxOnIcon css-1c1jprp"
            />
          </span>
          <span
            class="emotion-class"
          >
            <span
              aria-label=""
              class=" emotion-class"
              data-mui-internal-clone-element="true"
            >
              Confidential document
            </span>
            <span
              aria-label="Only administrator and team mates with special permission will see this document"
              class=" emotion-class"
              data-mui-internal-clone-element="true"
            >
              <icon-mock
                classname="css-fezp"
                fill="#A6ABBF"
                size="17"
              />
            </span>
          </span>
        </label>
      </div>
    </div>
  </div>
  <div
    class="emotion-class"
  >
    <button
      class=" emotion-class"
      disabled=""
    >
      <span
        class="emotion-class"
        disabled=""
      >
        Save
      </span>
    </button>
    <button
      class=" emotion-class"
    >
      <span
        class="emotion-class"
      >
        Cancel
      </span>
    </button>
  </div>
</div>
`;

exports[`AddDocumentTemplateModal -  should show error on save if mandatory fields are empty 1`] = `
.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  margin-bottom: 5px;
}

.emotion-class>a {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  box-sizing: border-box;
  width: 100%;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: row nowrap;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  white-space: nowrap;
  color: #A6ABBF;
  text-align: right;
  padding-left: 10px;
  margin-top: 5px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 150%;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  width: 100%;
  box-sizing: border-box;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #A6ABBF;
  text-align: right;
  padding-left: 10px;
  white-space: nowrap;
}

.emotion-class {
  margin-bottom: 20px;
}

.emotion-class {
  position: relative;
}

.emotion-class select,
.emotion-class select:focus,
.emotion-class select:hover {
  border-color: #FF7F8A;
  outline: none;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  list-style: none;
  padding-left: 0;
  margin: 0;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #FF7F8A;
  width: 100%;
  box-sizing: border-box;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.emotion-class label {
  padding-bottom: 10px;
  padding-top: 10px;
  width: 50%;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  cursor: pointer;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class:hover .checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6;
}

.emotion-class {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.emotion-class svg {
  width: inherit;
  height: inherit;
}

.emotion-class input {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  cursor: inherit;
  width: inherit;
  height: inherit;
  padding: 0;
  margin: 0;
  border: 0;
  z-index: 1;
}

.emotion-class:hover>.checkboxOffIcon path:nth-of-type(2) {
  fill: #465AB6!important;
}

.emotion-class {
  margin: 1px 0 -1px 6px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  color: #303757;
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.emotion-class {
  position: relative;
  width: 24px;
  height: 15px;
  display: inline-block;
}

.emotion-class svg {
  position: absolute;
  top: 1px;
  left: 3px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
  margin-top: 30px;
  border-top: 1px solid #E4E5EB;
  padding-top: 40px;
}

@media (max-width: 768px) {
  .emotion-class {
    padding-top: 15px;
  }
}

.emotion-class button,
.emotion-class a {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-base);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: #FFFFFF;
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(1.15);
  filter: brightness(1.15);
}

.emotion-class {
  color: #FFFFFF;
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 100px;
  height: 35px;
  background-color: var(--action-base-background);
  border: 0;
  padding-top: 0;
  padding-right: 20px;
  padding-bottom: 0;
  padding-left: 20px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 35px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 13px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div
  data-testid="AddDocumentTemplateModal_Container"
>
  <div
    class="emotion-class"
  >
    <div
      class="emotion-class"
    >
      <div>
        <span
          class="emotion-class"
          data-testid="InputLabel_DocumentName"
        >
          Document name
        </span>
      </div>
    </div>
    <div
      class="emotion-class"
    >
      <input
        class="emotion-class"
        maxlength="80"
        placeholder="Document name"
        type="text"
        value="Random name"
      />
    </div>
    <div
      class="emotion-class"
    >
      <div />
      <div
        class="emotion-class"
        data-testid="InputMaxLength"
      >
        11
         / 
        80
      </div>
    </div>
    <div
      style="margin-bottom: 5px;"
    >
      <span
        class="emotion-class"
        data-testid="InputLabel_Description"
      >
        Description
        <span
          class="emotion-class"
          style="margin-left: 10px; color: rgb(166, 171, 191);"
        >
          Optional
        </span>
      </span>
    </div>
    <div
      class="emotion-class"
    >
      <textarea
        class="emotion-class"
        maxlength="200"
        placeholder="Description"
        style="height: -104px;"
        variant="default"
      />
    </div>
    <div
      class="emotion-class"
    >
      <div />
      <div
        class="emotion-class"
      >
        0
         / 
        200
      </div>
    </div>
    <div
      class="emotion-class"
    >
      <div
        style="margin-bottom: 5px;"
      >
        <span
          class="emotion-class"
          data-testid="InputLabel_DocumentType"
        >
          Document type
        </span>
      </div>
      <div
        class="emotion-class"
      >
        <select
          class="emotion-class"
          data-testid="Select_DocumentType"
        >
          <option
            value=""
          >
            Select document type
          </option>
          <option
            data-testid="SelectOption_License"
            value="license"
          >
            Professional license
          </option>
          <option
            data-testid="SelectOption_Certificate"
            value="certificate"
          >
            Certificate
          </option>
          <option
            data-testid="SelectOption_Insurance"
            value="insurance"
          >
            General Liability insurance
          </option>
          <option
            data-testid="SelectOption_WorkersCompensation"
            value="workers compensation"
          >
            Workers compensation
          </option>
          <option
            data-testid="SelectOption_LetterOfRecommendation"
            value="letter of recommendation"
          >
            Letter of recommendation
          </option>
          <option
            data-testid="SelectOption_Sa"
            value="sa"
          >
            Services agreement
          </option>
          <option
            data-testid="SelectOption_Resume"
            value="resume"
          >
            Resume/CV
          </option>
          <option
            data-testid="SelectOption_Diploma"
            value="diploma"
          >
            Diploma
          </option>
          <option
            data-testid="SelectOption_Transcripts"
            value="transcripts"
          >
            Transcripts
          </option>
          <option
            data-testid="SelectOption_DriversLicence"
            value="drivers licence"
          >
            Drivers license
          </option>
          <option
            data-testid="SelectOption_OtherDocument"
            value="other document"
          >
            Other document
          </option>
        </select>
        <icon-mock
          classname="css-1qbf8bs"
          disabled="false"
          size="28"
        />
      </div>
      <div
        style="margin-top: 5px;"
      >
        <ul
          class="emotion-class"
          data-testid="ErrorList"
        >
          <li
            class="emotion-class"
          >
            Document type should be selected
          </li>
        </ul>
      </div>
    </div>
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      >
        <label
          class="emotion-class"
          data-testid="InputCheckbox"
        >
          <span
            class="emotion-class"
          >
            <input
              type="checkbox"
            />
            <icon-mock
              classname="checkboxOffIcon css-1dmky3q"
              fill="#D1D6ED"
            />
          </span>
          <span
            class="emotion-class"
          >
            <span
              aria-label=""
              class=" emotion-class"
              data-mui-internal-clone-element="true"
            >
              Verification required
            </span>
          </span>
        </label>
      </div>
      <div
        class="emotion-class"
      >
        <label
          class="emotion-class"
          data-testid="InputCheckbox"
        >
          <span
            class="emotion-class"
          >
            <input
              type="checkbox"
            />
            <icon-mock
              classname="checkboxOffIcon css-1dmky3q"
              fill="#D1D6ED"
            />
          </span>
          <span
            class="emotion-class"
          >
            <span
              aria-label=""
              class=" emotion-class"
              data-mui-internal-clone-element="true"
            >
              Expiry date required
            </span>
          </span>
        </label>
      </div>
      <div
        class="emotion-class"
      >
        <label
          class="emotion-class"
          data-testid="InputCheckbox"
        >
          <span
            class="emotion-class"
          >
            <input
              type="checkbox"
            />
            <icon-mock
              classname="checkboxOffIcon css-1dmky3q"
              fill="#D1D6ED"
            />
          </span>
          <span
            class="emotion-class"
          >
            <span
              aria-label=""
              class=" emotion-class"
              data-mui-internal-clone-element="true"
            >
              Mandatory document
            </span>
          </span>
        </label>
      </div>
      <div
        class="emotion-class"
      >
        <label
          class="emotion-class"
          data-testid="InputCheckbox"
        >
          <span
            class="emotion-class"
          >
            <input
              type="checkbox"
            />
            <icon-mock
              classname="checkboxOffIcon css-1dmky3q"
              fill="#D1D6ED"
            />
          </span>
          <span
            class="emotion-class"
          >
            <span
              aria-label=""
              class=" emotion-class"
              data-mui-internal-clone-element="true"
            >
              Visible for partners
            </span>
          </span>
        </label>
      </div>
      <div
        class="emotion-class"
      >
        <label
          class="emotion-class"
          data-testid="InputCheckbox"
        >
          <span
            class="emotion-class"
          >
            <input
              type="checkbox"
            />
            <icon-mock
              classname="checkboxOffIcon css-1dmky3q"
              fill="#D1D6ED"
            />
          </span>
          <span
            class="emotion-class"
          >
            <span
              aria-label=""
              class=" emotion-class"
              data-mui-internal-clone-element="true"
            >
              Confidential document
            </span>
            <span
              aria-label="Only administrator and team mates with special permission will see this document"
              class=" emotion-class"
              data-mui-internal-clone-element="true"
            >
              <icon-mock
                classname="css-fezp"
                fill="#A6ABBF"
                size="17"
              />
            </span>
          </span>
        </label>
      </div>
    </div>
  </div>
  <div
    class="emotion-class"
  >
    <button
      class=" emotion-class"
    >
      <span
        class="emotion-class"
      >
        Save
      </span>
    </button>
    <button
      class=" emotion-class"
    >
      <span
        class="emotion-class"
      >
        Cancel
      </span>
    </button>
  </div>
</div>
`;
