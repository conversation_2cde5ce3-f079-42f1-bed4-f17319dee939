import { AgreementErrorModalType } from '@Worksuite/Features/Agreements/modals/AgreementErrorModal/AgreementErrorModal';
import { SendCustomAgreementModal } from '@Worksuite/Features/Agreements/modals/SendCustomAgreementModal/SendCustomAgreementModal';
import { ShareAgreementModal } from '@Worksuite/Features/Agreements/modals/ShareAgreementModal/ShareAgreementModal.types';
import { ComplianceOverrideOutcomeModal } from '@Worksuite/Features/Compliance/modals/ComplianceOverrideOutcomeModal/ComplianceOverrideOutcomeModal';
import { AddContractModal } from '@Worksuite/Features/Contracts/modals/AddContractModal/AddContractModal';
import { ChooseContractTemplateModal } from '@Worksuite/Features/Contracts/modals/ChooseContractTemplateModal/ChooseContractTemplateModal';
import { EndContractModal } from '@Worksuite/Features/Contracts/modals/EndContractModal/EndContractModal';
import { RejectContractAgreementModal } from '@Worksuite/Features/Contracts/modals/RejectContractAgreementModal/RejectContractAgreementModal';
import { RejectContractModal } from '@Worksuite/Features/Contracts/modals/RejectContractModal/RejectContractModal';
import { MessageDeletionConfirmation } from '@Worksuite/Features/Core/components/MessageBoard/MessageBoardBase/MessageDeletionConfirmationModal';
import { PreviewAndSignModal } from '@Worksuite/Features/Core/components/PreviewAndSign/PreviewAndSignModal';
import { SendForSigning } from '@Worksuite/Features/Core/components/SendForSigning/SendForSigning';
import { BroadcastModal } from '@Worksuite/Features/Dashboard/modals/BroadcastModal/BroadcastModal';
import { DeleteDefaultEntityModal } from '@Worksuite/Features/Entities/modals/DeleteDefaultEntityModal/DeleteDefaultEntityModal';
import { EntityFormModal } from '@Worksuite/Features/Entities/modals/EntityFormModal/EntityFormModal';
import { ExpenseDetailsModal } from '@Worksuite/Features/Expenses/ExpenseDetailsModal/ExpenseDetailsModal';
import { InvoiceDetailsModal } from '@Worksuite/Features/Payments/components/InvoiceDetailsSidePanel/InvoiceDetailsSidePanel';
import { DownloadInvoicesModal } from '@Worksuite/Features/Payments/modals/DownloadInvoicesModal/DownloadInvoicesModal';
import { PaymentActionModalType } from '@Worksuite/Features/Payments/modals/PaymentActionModal/PaymentActionModal';
import { PaymentApproveErrorModal } from '@Worksuite/Features/Payments/modals/PaymentApproveErrorModal/PaymentApproveErrorModal';
import { RejectPaymentModal } from '@Worksuite/Features/Payments/modals/RejectPaymentModal/RejectPaymentModal';
import { PrimaryPayoutMethodChangeModal } from '@Worksuite/Features/PayoutMethods/components/PrimaryPayoutMethodChangeModal/PrimaryPayoutMethodChangeModal';
import { RemovePayoutMethodModal } from '@Worksuite/Features/PayoutMethods/components/RemovePayoutMethodModal/RemovePayoutMethodModal';
import { AddTaskNewFileModal } from '@Worksuite/Features/Tasks/Modals/AddTaskNewFileModal/AddTaskNewFileModal';
import { CompleteTaskModal } from '@Worksuite/Features/Tasks/Modals/CompleteTaskModal/CompleteTaskModal';
import { DigitalDeliveryConsentModal } from '@Worksuite/Features/TaxFiling/components/DigitalDeliveryConsentModal';
import { PastTen99NecTaxFormsModal } from '@Worksuite/Features/TaxFiling/components/PastTen99NecTaxFormsModal';
import { W9HistoryModalType } from '@Worksuite/Features/TaxInformation/components/W9HistoryModal/W9HistoryModal';
import { AddTimeEntryModal } from '@Worksuite/Features/TimeTracking/components/AddTimeEntryModal/AddTimeEntryModal';
import { ApproveTimesheetModal } from '@Worksuite/Features/Timesheets/Modals/ApproveTimesheetModal/ApproveTimesheetModal';
import { RejectTimesheetModal } from '@Worksuite/Features/Timesheets/Modals/RejectTimesheetModal/RejectTimesheetModal';
import { TaskWeeklyTimesheetDetailsModal } from '@Worksuite/Features/Timesheets/Modals/TaskWeeklyTimesheetDetailsModal/TaskWeeklyTimesheetDetailsModal';
import { TimesheetDetailsModal } from '@Worksuite/Features/Timesheets/Modals/TimesheetDetailsModal/TimesheetDetailsModal';
import { TwoFactorAuthBackupCodesModal } from '@Worksuite/Features/TwoFactorAuth/modals/TwoFactorAuthBackupCodesModal/TwoFactorAuthBackupCodesModal';
import { TwoFactorAuthRemoveDeviceModal } from '@Worksuite/Features/TwoFactorAuth/modals/TwoFactorAuthRemoveDeviceModal/TwoFactorAuthRemoveDeviceModal';
import { TwoFactorAuthSetupModal } from '@Worksuite/Features/TwoFactorAuth/modals/TwoFactorAuthSetupModal/TwoFactorAuthSetupModal';
import { UpsellModal } from '@Worksuite/Features/Upsell/UpsellModal/UpsellModal';
import { VendorExternalSyncEligibilityModal } from '@Worksuite/Features/Vendor/components/ExternalSyncSettings/ExternalSyncEligibilityModal';
import { AddNewFileModal } from '@Worksuite/Features/Vendor/modals/AddNewFileModal/AddNewFileModal';
import { AddVendorTagsModal } from '@Worksuite/Features/Vendor/modals/AddVendorTagsModal/AddVendorTagsModal';
import { UploadDocumentModal } from '@Worksuite/Features/Vendor/modals/UploadDocumentModal/UploadDocumentModal';
import { DownloadActivityLogModal } from '@Worksuite/Features/Workflows/modals/DownloadActivityLogModal/DownloadActivityLogModal';
import { OnboardingConfirmChangesModal } from '@Worksuite/Features/Workflows/modals/OnboardingConfirmChangesModal/OnboardingConfirmChangesModal';
import { Invoice } from '../../../types/invoices';
import { ChooseAgreementTypeModal } from './Modals/Agreements/ChooseAgreementTypeModal';
import { SessionExpiryModal } from './Modals/Agreements/SessionExpiryModal.types';
import { UploadSignedAgreementModal } from './Modals/Agreements/UploadSignedAgreementModal';
import { RemoveCompliantModal } from './Modals/Compliance/RemoveCompliantModal';
import { SetAsCompliantModal } from './Modals/Compliance/SetAsCompliantModal';
import { AddDocumentTemplateModal } from './Modals/Documents/AddDocumentTemplateModal';
import { ConfirmChangesModal } from './Modals/Documents/ConfirmChangesModal';
import { ExampleModal } from './Modals/ExampleModal';
import { SaveSearchModal } from './Modals/Filters/SaveSearchModal';
import { AddToJobOpeningModal } from './Modals/JobOpenings/AddToJobOpeningModal';
import { AddListModal } from './Modals/Lists/AddListModal';
import { AddToGroupModal } from './Modals/Lists/AddToGroupModal';
import { ShareListModal } from './Modals/Lists/ShareListModal';
import { ShareProfileModal } from './Modals/Lists/ShareProfileModal';
import { MessageModal } from './Modals/MessageModal';
import { QuickViewModal } from './Modals/QuickView/QuickViewModal';
import { UnsavedChangesModal } from './Modals/UnsavedChangesModal/UnsavedChangesModal';

export interface ShortlistModal {
  hideClose?: boolean;
  type: string;
  // @todo - remove this, BUT! modals should be separated and prefixed by features
  // type: `${Features}_${string}`;
  modalClose?: (data?: any) => void;
  modalDismiss?: (data?: any) => void;
  headerData?: HeaderDataType;
  setHeaderData?: (data: any) => void;
  modalSize?: ModalSizes;
  variant?: ModalVariants;
  disableScrollLock?: boolean;
  disableEnforceFocus?: boolean;
  style?: React.CSSProperties;
}

export interface HeaderDataType {
  quickViewModalHeader?: {
    coverPhotoUrl: string;
    headerColor: string;
    headerLogo: string;
  };
  invoiceDetailsSidePanelHeader?: {
    hasInvoice: boolean;
    invoice: Invoice;
    activeTab: string;
    setActiveTab: (tab: string) => void;
    onClose: () => void;
  };
  headerText?: string;
  closeIconColor?: string;
}
export type ModalVariants =
  | 'default'
  | 'sidepanel'
  | 'sidepanel-nomargins'
  | 'full-screen';

export type ModalSizes = 'sm' | 'md' | 'lg' | 'xl';

export type ModalTypes =
  | AddContractModal
  | AddDocumentTemplateModal
  | AddListModal
  | AddTaskNewFileModal
  | AddTimeEntryModal
  | AddToJobOpeningModal
  | AddToGroupModal
  | InvoiceDetailsModal
  | AddNewFileModal
  | AddVendorTagsModal
  | AgreementErrorModalType
  | ApproveTimesheetModal
  | BroadcastModal
  | ChooseAgreementTypeModal
  | ChooseContractTemplateModal
  | ComplianceOverrideOutcomeModal
  | CompleteTaskModal
  | ConfirmChangesModal
  | DeleteDefaultEntityModal
  | DownloadActivityLogModal
  | EndContractModal
  | EntityFormModal
  | ExampleModal
  | MessageModal
  | SessionExpiryModal
  | SendForSigning
  | OnboardingConfirmChangesModal
  | PaymentApproveErrorModal
  | PaymentActionModalType
  | QuickViewModal
  | RejectContractModal
  | RejectContractAgreementModal
  | RejectPaymentModal
  | RejectTimesheetModal
  | RemoveCompliantModal
  | SaveSearchModal
  | SetAsCompliantModal
  | ShareListModal
  | ShareProfileModal
  | ShareAgreementModal
  | TaskWeeklyTimesheetDetailsModal
  | W9HistoryModalType
  | TimesheetDetailsModal
  | UploadDocumentModal
  | UploadSignedAgreementModal
  | UnsavedChangesModal
  | TwoFactorAuthBackupCodesModal
  | TwoFactorAuthRemoveDeviceModal
  | TwoFactorAuthSetupModal
  | PrimaryPayoutMethodChangeModal
  | RemovePayoutMethodModal
  | MessageDeletionConfirmation
  | PreviewAndSignModal
  | DownloadInvoicesModal
  | UpsellModal
  | DigitalDeliveryConsentModal
  | PastTen99NecTaxFormsModal
  | SendCustomAgreementModal
  | VendorExternalSyncEligibilityModal
  | ExpenseDetailsModal;
