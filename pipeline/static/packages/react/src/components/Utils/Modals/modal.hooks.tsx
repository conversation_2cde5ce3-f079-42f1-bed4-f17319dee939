import { Dialog, Slide } from '@mui/material';
import React, { useCallback, useEffect, useState } from 'react';
import { RemoveIcon } from '../../../assets/icons/small';
import { Colors } from '../../../styles/global.styles';
import * as T from '../../../styles/typography';
import {
  CustomModal,
  FullScreenContainer,
  ModalBackdrop,
  ModalContainer,
  ModalDialog,
  ModalHeader,
  SideModalHeader,
  SlideContainer,
} from './modal.styles';
import { HeaderDataType, ModalSizes, ShortlistModal } from './modal.types';

const MODAL_TRANSITION_DURATION = 500;

export const withModal =
  <P extends ShortlistModal>(
    Component: React.ComponentType<P>,
    {
      getHeader,
      defaultModalSize,
    }: {
      getHeader?: (props: P) => React.ReactNode;
      defaultModalSize?: ModalSizes;
    }
  ): React.FC<P> =>
  ({ ...props }) => {
    const [headerData, setHeaderData] = useState<HeaderDataType>({});
    const [header, setHeader] = React.useState(
      () =>
        getHeader &&
        getHeader({
          ...props,
          headerData,
        })
    );

    useEffect(() => {
      setHeader(
        getHeader &&
          getHeader({
            ...props,
            headerData,
          })
      );
    }, [JSON.stringify(headerData)]);

    const [isOpen, setOpen] = React.useState(true);
    const handleClose = useCallback(
      (data = null) => {
        setOpen(false);
        setTimeout(() => props.modalClose(data), MODAL_TRANSITION_DURATION);
      },
      [props.modalClose]
    );
    const handleDismiss = useCallback(
      (data = null) => {
        setOpen(false);
        setTimeout(() => props.modalDismiss(data), MODAL_TRANSITION_DURATION);
      },
      [props.modalDismiss]
    );

    if (props.variant === 'full-screen') {
      return (
        <CustomModal
          variant={props.variant}
          open={isOpen}
          onClose={props.hideClose ? null : handleClose}
          closeAfterTransition
          hideBackdrop
          disableEscapeKeyDown={!!props.hideClose}
        >
          <FullScreenContainer>
            <SideModalHeader variant={props.variant}>
              {header && typeof header === 'string' ? (
                <T.HeaderH3>{header}</T.HeaderH3>
              ) : (
                header
              )}
            </SideModalHeader>
            <Component
              {...({
                ...props,
                modalClose: (data) => handleClose(data),
                modalDismiss: (data) => handleDismiss(data),
                headerData,
                setHeaderData,
              } as P)}
            />
          </FullScreenContainer>
        </CustomModal>
      );
    }

    return props.variant === 'sidepanel' ||
      props.variant === 'sidepanel-nomargins' ? (
      <CustomModal
        variant={props.variant}
        open={isOpen}
        onClose={props.hideClose ? null : handleDismiss}
        closeAfterTransition
        hideBackdrop
        disableEscapeKeyDown={!!props.hideClose}
        disableScrollLock={props.disableScrollLock}
        disableEnforceFocus={props.disableEnforceFocus}
        style={props.style}
      >
        <Slide in={isOpen} direction="left" mountOnEnter unmountOnExit>
          <SlideContainer variant={props.variant}>
            <SideModalHeader variant={props.variant}>
              {!props.hideClose && (
                <button onClick={handleDismiss} className="CloseIcon">
                  <RemoveIcon
                    fill={headerData.closeIconColor || Colors.grey3}
                  />
                </button>
              )}
              {header && typeof header === 'string' ? (
                <T.HeaderH3>{header}</T.HeaderH3>
              ) : (
                header
              )}
            </SideModalHeader>
            <Component
              {...({
                ...props,
                modalClose: (data) => handleClose(data),
                modalDismiss: (data) => handleDismiss(data),
                headerData,
                setHeaderData,
              } as P)}
            />
          </SlideContainer>
        </Slide>
      </CustomModal>
    ) : (
      <Dialog
        open={isOpen}
        scroll="body"
        onClose={props.hideClose ? null : handleDismiss}
        PaperComponent={ModalDialog}
        BackdropComponent={ModalBackdrop}
        transitionDuration={MODAL_TRANSITION_DURATION}
        maxWidth={props.modalSize || defaultModalSize || 'md'}
        disableEscapeKeyDown={!!props.hideClose}
      >
        <ModalContainer
          size={props.modalSize || defaultModalSize || 'md'}
          data-testid="Modal"
        >
          {!props.hideClose && (
            <button
              data-testid="Modal_Close"
              onClick={handleDismiss}
              className="CloseIcon"
            >
              <RemoveIcon fill={Colors.grey3} size={37} />
            </button>
          )}
          {header && (
            <ModalHeader data-testid="Modal_Header">
              <h1>{header}</h1>
            </ModalHeader>
          )}
          <Component
            {...({
              ...props,
              modalClose: (data) => handleClose(data),
              modalDismiss: (data) => handleDismiss(data),
              headerData,
              setHeaderData,
            } as P)}
          />
        </ModalContainer>
      </Dialog>
    );
  };
