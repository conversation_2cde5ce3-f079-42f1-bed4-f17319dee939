// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SocialLinks should display links with icons. First icon should link to https://pl.pinterest.com/testowy/ 1`] = `
.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  border-bottom: 1px solid #E4E5EB;
  border-top: 1px solid #E4E5EB;
  padding: 10px 10px 10px 0;
}

.emotion-class:first-of-type {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  border-left: 1px solid #E4E5EB;
  padding: 10px;
}

.emotion-class:last-of-type {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  border-right: 1px solid #E4E5EB;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #465AB6;
  cursor: pointer;
  font-weight: 600;
}

<tr
  class="emotion-class"
  data-rfd-draggable-context-id=":r0:"
  data-rfd-draggable-id="1"
>
  <td
    class="emotion-class"
    style="width: 1px; padding: 8px 10px;"
  >
    <a
      class="emotion-class"
      href="https://pl.pinterest.com/testowy/"
      role="link"
      style="text-decoration: none;"
      tabindex="-1"
      target="_self"
    >
      <icon-mock
        classname="css-1sdjxf8"
        fill="#465AB6"
      />
    </a>
    <a
      class="emotion-class"
      href="https://twitter.com/testmagazine?lang=en"
      role="link"
      style="text-decoration: none;"
      tabindex="-1"
      target="_self"
    >
      <icon-mock
        classname="css-1sdjxf8"
        fill="#465AB6"
      />
    </a>
    <a
      class="emotion-class"
      href="https://www.youtube.com/watch?v=XwFWAWY4ucw"
      role="link"
      style="text-decoration: none;"
      tabindex="-1"
      target="_self"
    >
      <icon-mock
        classname="css-1sdjxf8"
        fill="#465AB6"
      />
    </a>
    <a
      class="emotion-class"
      href="https://www.linkedin.com/in/test-testowy-b7ba8994/?originalSubdomain=pl"
      role="link"
      style="text-decoration: none;"
      tabindex="-1"
      target="_self"
    >
      <icon-mock
        classname="css-1sdjxf8"
        fill="#465AB6"
      />
    </a>
    <a
      class="emotion-class"
      href="https://www.facebook.com/profile.php?id=100063619821376"
      role="link"
      style="text-decoration: none;"
      tabindex="-1"
      target="_self"
    >
      <icon-mock
        classname="css-1sdjxf8"
        fill="#465AB6"
      />
    </a>
    <a
      class="emotion-class"
      href="https://www.instagram.com/p/Cv3DOQNsuS7/?hl=en"
      role="link"
      style="text-decoration: none;"
      tabindex="-1"
      target="_self"
    >
      <icon-mock
        classname="css-1sdjxf8"
        fill="#465AB6"
      />
    </a>
  </td>
</tr>
`;
