import { TaskGroupBudgetProgressBar } from '../../../../../features/Tasks/TaskGroup/TaskGroupBudgetProgressBar/TaskGroupBudgetProgressBar';
import { Project } from '../../../../../types/project';
import { TaskGroupBudgetColumn } from '../../Column/Column';
import { Td } from '../../List.styles';
import { ListRow } from '../../List.types';
import React from 'react';

const CellTaskGroupBudget = React.memo(
  ({ column, item }: { column: TaskGroupBudgetColumn; item: ListRow }) => (
    <Td
      as="td"
      columnStyles={column.styleProps}
      style={{ padding: '18px 10px 17px' }}
    >
      <TaskGroupBudgetProgressBar taskGroup={item as Project} />
    </Td>
  )
);

export default CellTaskGroupBudget;
