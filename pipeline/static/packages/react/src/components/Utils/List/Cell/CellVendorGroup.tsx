import VendorListAvatar from '../../../Vendors/VendorListAvatar/VendorListAvatar';
import { Td } from '../List.styles';
import { ListRow } from '../List.types';
import React from 'react';
import { TdPaddings } from './ListCell.helpers';

const CellVendorGroup = ({ item }: { item: ListRow }) => (
  <Td as="td" style={{ padding: TdPaddings.xsmall }}>
    <VendorListAvatar listId={item.vendor_group} variant="cell" />
  </Td>
);

export default CellVendorGroup;
