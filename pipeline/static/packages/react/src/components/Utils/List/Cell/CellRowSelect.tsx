import { useContext } from 'react';
import { BulkActionsContext } from '../../../BulkActions/BulkActions.context';
import Checkbox from '../../../Checkbox/Checkbox';
import { TdRowSelect } from '../List.styles';
import { ListRow } from '../List.types';
import { useShiftSelect } from './ListCell.helpers';

const CellRowSelect = ({ item }: { item: ListRow }) => {
  const { isItemSelected, selectedItemCount } = useContext(BulkActionsContext);
  const isSelected = isItemSelected(item.id);
  const { handleClick } = useShiftSelect({
    itemId: item.id,
  });

  return (
    <TdRowSelect as="td">
      <div data-testid="CellRowSelect" onClick={handleClick}>
        <span
          className="inlineEditing"
          style={
            selectedItemCount ? { visibility: 'visible', opacity: '1' } : null
          }
        >
          <Checkbox checked={isSelected} />
        </span>
      </div>
    </TdRowSelect>
  );
};

export default CellRowSelect;
