import React from 'react';
/* eslint-disable */
import List from '../../List';
import { CellStoryTemplate } from './CellStories.helper';

const cellVendorSkillsSettings = {
  id: 0,
  type: 'vendor_skills',
  name: 'Vendor skills',
  getVendorSkills: (data) => ['javascript'],
};

export default {
  title: 'Core/Tables/Cells/CellVendorSkills',
  component: List,
};

export const Default = (args) => CellStoryTemplate(args);
Default.args = {
  columns: [cellVendorSkillsSettings],
  onRowClick: (data) => {
    console.log('row click:', data);
  },
};

export const MultipleSkills = (args) => CellStoryTemplate(args);
MultipleSkills.args = {
  columns: [
    {
      ...cellVendorSkillsSettings,
      getVendorSkills: () => ['javascript', 'java', 'c++', 'python', 'react'],
    },
  ],
  onRowClick: (data) => {
    console.log('row click:', data);
  },
};
