import { jestMockIntersectionObserver } from '../../../utils/jest/jest.helpers';
import * as stories from './TenantLogo.stories';
import { composeStories } from '@storybook/react';
import '@testing-library/jest-dom';
import { render, screen, waitFor } from '@testing-library/react';
import React from 'react';

describe('On TenantLogo - ', () => {
  beforeEach(async () => {
    jestMockIntersectionObserver();
  });

  test('Check if tenant name is in document', async () => {
    const { Default: TenantLogo } = composeStories(stories);

    const container = render(<TenantLogo />).container;
    await waitFor(async () => {
      expect(screen.getByText('Tenant Name & Placeholder')).toBeInTheDocument();
    });

    expect(container).toMatchSnapshot();
  });

  test('Check if tenant name is visible in NoBackgroundImage variant', async () => {
    const { NoImage: TenantLogo } = composeStories(stories);

    const container = render(<TenantLogo />).container;
    await waitFor(async () => {
      expect(screen.getByText('Tenant Name & Placeholder')).toBeInTheDocument();
    });

    expect(container).toMatchSnapshot();
  });
});
