import { FontFamily } from '../../../styles/global.styles';
import { HeaderH3 } from '../../../styles/typography';
import { TenantLogoVariant } from './TenantLogo';
import styled from '@emotion/styled';

export const RootUrl = styled('a')({
  cursor: 'pointer',
  maxWidth: '200px',
  display: 'block',
});

export const NoUrl = styled('div')({
  maxWidth: '200px',
});

export const LogoHolder = styled('span')(({
  logoUrl,
  variant,
}: {
  logoUrl: string;
  variant: TenantLogoVariant;
}) => {
  const styles = {
    backgroundImage: `url('${logoUrl}')`,
    backgroundSize: 'contain',
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'center',
    width: '190px',
    height: '45px',
    position: 'relative' as any,
    display: 'inline-block',
    textIndent: '-9999px',
  };
  if (variant === 'side-navigation') {
    return {
      ...styles,
      top: '9px',
    };
  }
  if (variant === 'new-side-navigation') {
    return {
      ...styles,
      width: '150px',
      height: '42px',
      backgroundPosition: 'left',
      top: '0',
    };
  }
  if (variant === 'quick-view') {
    return {
      ...styles,
      width: '200px',
      height: '80px',
    };
  }
  return styles;
});

export const TenantNameHolder = styled(HeaderH3)(({
  variant,
}: {
  variant: TenantLogoVariant;
}) => {
  if (variant === 'new-side-navigation') {
    return {
      fontFamily: FontFamily.montserrat,
      fontSize: '17px',
      lineHeight: '20px',
      textTransform: 'uppercase',
    };
  }
  return {};
});
