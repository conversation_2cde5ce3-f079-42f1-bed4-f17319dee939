import i18n from '../../../i18n/i18n';
import { Colors, FontFamily } from '../../../styles/global.styles';
import { SimpleUser } from '../../../types/user';
import { Vendor } from '../../../types/vendor';
import { getVendorName } from '../../Vendors/vendors.helpers';
import Tooltip from '../Tooltip/Tooltip';
import styled from '@emotion/styled';
import React from 'react';

const borderWidth = 2;

export const getNameForTooltip = (user: SimpleUser | Vendor): string => {
  const name = getVendorName(user);

  if ((user as SimpleUser).deleted) {
    const suffix = i18n.t('Users.RemovedSuffix');
    return `${name} (${suffix})`;
  }

  return name;
};

const Logo = styled('div')(({ size, logo, avatarColor, variant }: any) => {
  const styles = {
    width: size + 2 * borderWidth,
    height: size + 2 * borderWidth,
    lineHeight: `${size}px`,
    borderRadius: '50%',
    textAlign: 'center' as any,
    border: `${borderWidth}px solid ${Colors.white}`,
    color: avatarColor ? Colors.white : Colors.grey2,
    fontFamily: FontFamily.montserrat,
    fontSize: size / 2.5,
    fontWeight: 700,
    backgroundColor: avatarColor || Colors.grey3,
    backgroundImage: logo ? `url('${logo}')` : 'none',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    display: 'inline-block',
    verticalAlign: 'middle',
    boxSizing: 'border-box' as any,
    boxShadow: 'inset 0px 0px 11px rgba(0, 0, 0, 0.1)',
    marginTop: `-${borderWidth}px`,
    marginBottom: `-${borderWidth}px`,
    [':not(:first-of-type)']: {
      marginLeft: (size / 2) * -1 - borderWidth,
    },
  };

  if (variant === 'single') {
    return {
      ...styles,
      width: size,
      height: size,
      border: 0,
      marginBottom: 0,
      marginTop: 0,
    };
  }

  return styles;
});

export const LogoWithTooltip = ({
  avatarColor,
  child,
  logo,
  size,
  tooltip,
  variant,
}: {
  avatarColor: String;
  child: string | number;
  logo: string;
  size: number;
  tooltip: string;
  variant?: '' | 'single';
}) => (
  <Tooltip tooltip={tooltip}>
    <Logo avatarColor={avatarColor} logo={logo} size={size} variant={variant}>
      {child}
    </Logo>
  </Tooltip>
);
