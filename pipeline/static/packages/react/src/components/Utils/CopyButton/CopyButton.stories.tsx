import React from 'react';
/* eslint-disable */
import CopyButton from './CopyButton';

export default {
  title: 'Core/Components/CopyButton',
  component: CopyButton,
};

const args = {
  label: 'Copy',
  successLabel: 'Copied!',
  pendingLabel: 'Generating...',
  getValuePromise: () =>
    new Promise((res) => {
      setTimeout(() => res(`random text - ${Math.random()}`), 1750);
    }),
};

const DefaultTemplate = (args) => {
  return <CopyButton {...args} />;
};

export const Default = (args) => DefaultTemplate(args);
Default.args = args;

const ButtonVariantTemplate = (args) => {
  return (
    <CopyButton
      {...args}
      buttonVariantDefault={'primary'}
      buttonVariantSuccess={'primary'}
      buttonColorSuccess={'green'}
    />
  );
};

export const ButtonVariant = (args) => ButtonVariantTemplate(args);
ButtonVariant.args = args;
