import React from 'react';
/* eslint-disable */
import * as T from '../../../assets/icons/index';
import ConfirmationBox from './ConfirmationBox';

export default {
  title: 'Core/Components/ConfirmationBox',
  component: ConfirmationBox,
};

const args = {
  header: 'Thank you for your feedback',
};
const argTypes = {
  icon: { control: { type: 'select', options: ['', ...Object.keys(T.Large)] } },
};

const Template = (args) => {
  return <ConfirmationBox {...args} />;
};

export const Default = (args) => Template(args);
Default.args = args;
Default.argTypes = argTypes;

export const Wrapped = (args) => Template(args);
Wrapped.argTypes = argTypes;
Wrapped.args = {
  ...args,
  wrapper: true,
};

export const WrappedAllParams = (args) => Template(args);
WrappedAllParams.argTypes = argTypes;
WrappedAllParams.args = {
  ...args,
  wrapper: true,
  icon: 'FeedbackIcon',
  copy: 'Your feedback has now been submitted. You can view it on <strong>Shortlist & </strong> or close this window.',
  button: {
    label: 'Sign up',
    onClick: () => console.log('Button clicked...'),
  },
};
