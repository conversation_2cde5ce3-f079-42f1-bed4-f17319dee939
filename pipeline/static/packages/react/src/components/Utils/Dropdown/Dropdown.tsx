import PropTypes from 'prop-types';
import React from 'react';
import { Small } from '../../../assets/icons';
import { RemoveIcon } from '../../../assets/icons/small';
import i18n from '../../../i18n/i18n';
import { Colors } from '../../../styles/global.styles';
import { LabelSmall } from '../../../styles/typography';
import InputCheckbox from '../../FormElements/InputCheckbox/InputCheckbox';
import DropdownWrapper from '../DropdownWrapper/DropdownWrapper';
import Tooltip from '../Tooltip/Tooltip';
import { NoActions, StyledMenuItem } from './Dropdown.styles';

export type DropdownOption = {
  id: string;
  label: string;
  actionPromise?: () => void;
  href?: string;
  hrefTarget?: '_blank';
  isCheckbox?: boolean;
  checkboxOnChange?: (selected: boolean) => void;
  isRemove?: boolean;
  removeOnClick?: (id: string) => void;
  isLabel?: boolean;
  group?: string;
  icon?: string;
  color?: string;
  isIconMedium?: boolean;
  isDisabled?: boolean;
  tooltip?: string;
  wrapper?: React.ComponentType<{ children?: React.ReactNode }>;
  testId?: string;
};

export interface DropdownThemeOptions {
  popoverMarginTop?: number;
  absoluteLeft?: number;
  hideScroll?: boolean;
  height?: number;
  fixHeight?: boolean;
  customVariant?: 'sideNavNotifications' | 'sideNavNotificationsMobile';
}

const DropdownOptionLabel = ({
  option,
  onClick,
}: {
  option: DropdownOption;
  onClick: any;
}) => {
  const Icon = option.icon ? Small[option.icon] : null;
  const menuContent = (
    <LabelSmall
      data-testid={`DropdownOptionLabel${
        option?.isDisabled ? '_Disabled' : ''
      }`}
      style={{
        color: option.color,
      }}
    >
      {Icon && (
        <Icon
          fill={option.color ?? Colors.blue1}
          size={option.isIconMedium ? 55 : 24}
          style={{ marginLeft: '-4px', paddingRight: '3px' }}
        />
      )}
      {option.label}
    </LabelSmall>
  );
  return (
    <StyledMenuItem disabled={option.isLabel} onClick={onClick} option={option}>
      {option.tooltip ? (
        <Tooltip tooltip={option.tooltip}>{menuContent}</Tooltip>
      ) : (
        <>{menuContent}</>
      )}
      {option.isRemove && (
        <RemoveIcon
          fill={Colors.grey2}
          size={24}
          onClick={option.removeOnClick(option.id)}
        />
      )}
      {option.isCheckbox && (
        <InputCheckbox onChange={option.checkboxOnChange} />
      )}
    </StyledMenuItem>
  );
};

const DropdownOptionWrapper = ({
  option,
  onClick,
  divided = false,
}: {
  option: DropdownOption;
  onClick?: any;
  divided?: boolean;
}) => {
  const DropdownOption = (
    <div
      style={
        divided
          ? {
              borderTop: `1px solid ${Colors.blue3}`,
              marginTop: 7,
              paddingTop: 7,
            }
          : {}
      }
      data-testid={option.testId}
    >
      {(() => {
        if (option.href) {
          return (
            <a
              style={{ textDecoration: 'none' }}
              href={option.href}
              target={option.hrefTarget}
            >
              <DropdownOptionLabel option={option} onClick={onClick} />
            </a>
          );
        }
        return <DropdownOptionLabel option={option} onClick={onClick} />;
      })()}
    </div>
  );
  return (
    <>
      {option.wrapper
        ? React.createElement(option.wrapper, {}, DropdownOption)
        : DropdownOption}
    </>
  );
};

const Dropdown = ({
  options = [],
  children,
  testId,
  triggerTestId,
  themeOptions,
  onDropdownClose,
}: {
  options: DropdownOption[];
  children?: React.ReactElement;
  testId?: string;
  triggerTestId?: string;
  themeOptions?: DropdownThemeOptions;
  onDropdownClose?: () => void;
}) => (
  <DropdownWrapper
    options={{ withMargins: false }}
    trigger={children}
    testId={triggerTestId}
    content={({ handleClose }) => {
      if (options.length) {
        return (
          <div style={{ padding: '10px 0' }} data-testid={testId}>
            {options.map((option: DropdownOption, ii: number) => {
              const prevElement = options[ii - 1];
              let onClick;
              if (option.href) {
                onClick = handleClose;
              } else if (option.actionPromise) {
                onClick = (event) => {
                  option.actionPromise();
                  handleClose(event);
                };
              } else {
                onClick = (event) => {
                  event.stopPropagation();
                };
              }
              return (
                <DropdownOptionWrapper
                  key={`${option.id}__${ii}`}
                  divided={
                    prevElement?.group && prevElement.group !== option.group
                  }
                  option={option}
                  onClick={onClick}
                  data-testid={option?.testId}
                />
              );
            })}
          </div>
        );
      }
      return (
        <NoActions data-testid={testId}>
          <LabelSmall>{i18n.t('common:Dropdown.NoActions')}</LabelSmall>
        </NoActions>
      );
    }}
    themeOptions={themeOptions}
    onDropdownClose={onDropdownClose}
  />
);

Dropdown.propTypes = {
  children: PropTypes.node,
  options: PropTypes.array,
};

export default React.memo(Dropdown);
