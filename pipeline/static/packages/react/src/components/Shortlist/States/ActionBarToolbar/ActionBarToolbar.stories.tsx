import i18n from '../../../../i18n/i18n';
import { ButtonVariant } from '../../../FormElements/Button/Button.types';
import ActionBarToolbar from './ActionBarToolbar';

export default {
  title: 'Core/Components/ActionBarToolbar',
  component: ActionBarToolbar,
};

const Variant = {
  'All Partners': {
    buttons: [
      {
        label: i18n.t('UserActions.AddPartner'),
      },
      {
        label: i18n.t('UserActions.GetInviteLink'),
        variant: 'secondary' as ButtonVariant,
      },
    ],
    dropdownOptions: [
      {
        label: i18n.t('UserActions.AddNewGroup'),
      },
      {
        label: i18n.t('UserActions.InviteAllPartners'),
      },
      {
        label: i18n.t('UserActions.ManageInviteLinks'),
      },
    ],
  },
  'Staffing supplier': {
    buttons: [
      {
        label: i18n.t('UserActions.AddStaffingSupplier'),
      },
      {
        label: i18n.t('UserActions.GetInviteLink'),
        variant: 'secondary' as ButtonVariant,
      },
    ],
    dropdownOptions: [
      {
        label: i18n.t('UserActions.AddNewGroup'),
      },
      {
        label: i18n.t('UserActions.InviteAllPartners'),
      },
      {
        label: i18n.t('UserActions.ManageInviteLinks'),
      },
    ],
  },
  Groups: {
    buttons: [
      {
        label: i18n.t('UserActions.AddGroup'),
      },
    ],
  },
  Lists: {
    buttons: [
      {
        label: i18n.t('UserActions.AddList'),
      },
    ],
  },
  'All Job Openings': {
    buttons: [
      {
        label: i18n.t('UserActions.NewJobOpening'),
      },
    ],
  },
  Projects: {
    buttons: [
      {
        label: i18n.t('UserActions.CreateNewProject'),
      },
    ],
  },
  'All Requests': {
    buttons: [
      {
        label: i18n.t('UserActions.AddNewRequest'),
      },
    ],
    dropdownOptions: [
      {
        label: i18n.t('DropdownActions.CreateNewCampaign'),
      },
    ],
  },
  'All Payments': {
    buttons: [
      {
        label: i18n.t('UserActions.AddNewExpense'),
      },
    ],
    dropdownOptions: [
      {
        label: i18n.t('DropdownActions.ExportPayments'),
      },
    ],
  },
  Workflows: {
    buttons: [
      {
        label: i18n.t('UserActions.CreateNewWorkflow'),
      },
    ],
  },
  Files: {
    buttons: [
      {
        label: i18n.t('UserActions.UploadFile'),
      },
    ],
  },
};

const allPartners = Variant['All Partners'];
const staffingSupplier = Variant['Staffing supplier'];
const groups = Variant['Groups'];
const lists = Variant['Lists'];
const allJobOpenings = Variant['All Job Openings'];
const projects = Variant['Projects'];
const allRequests = Variant['All Requests'];
const allPayments = Variant['All Payments'];
const workflows = Variant['Workflows'];
const files = Variant['Files'];

const Template = (args) => <ActionBarToolbar {...args} />;

export const AllPartners = (args) => Template(args);
AllPartners.args = allPartners;

export const StaffingSupplier = (args) => Template(args);
StaffingSupplier.args = staffingSupplier;

export const Groups = (args) => Template(args);
Groups.args = groups;

export const Lists = (args) => Template(args);
Lists.args = lists;

export const AllJobOpenings = (args) => Template(args);
AllJobOpenings.args = allJobOpenings;

export const Projects = (args) => Template(args);
Projects.args = projects;

export const AllRequests = (args) => Template(args);
AllRequests.args = allRequests;

export const AllPayments = (args) => Template(args);
AllPayments.args = allPayments;

export const Workflows = (args) => Template(args);
Workflows.args = workflows;

export const Files = (args) => Template(args);
Files.args = files;
