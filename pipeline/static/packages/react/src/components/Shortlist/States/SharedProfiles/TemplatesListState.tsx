import i18n from '../../../../i18n/i18n';
import { reduxStore } from '../../../../services/Redux';
import SharedProfileTemplatesContainer from '../../../Containers/SharedProfileTemplates/SharedProfileTemplatesContainer/SharedProfileTemplatesContainer';
import { ButtonType } from '../../../FormElements/Button/Button.types';
import { ShortlistState } from '../../State/ShortlistState';
import ActionBarToolbar from '../ActionBarToolbar/ActionBarToolbar';
import React from 'react';

const TemplatesListState = () => {
  const buttons: ButtonType[] = [
    {
      label: i18n.t(
        'SharedProfiles:SharedProfileTemplatesContainer.AddTemplateButtonText'
      ),
      onClick: () => {
        reduxStore.dispatch({
          type: 'STATE_GOTO',
          stateName: 'app.templates.shared-profiles-template-form',
        });
      },
    },
  ];

  return (
    <ShortlistState
      stateName={'SHARED_TEMPLATES_LIST'}
      headerEndComponent={<ActionBarToolbar buttons={buttons} />}
    >
      <SharedProfileTemplatesContainer />
    </ShortlistState>
  );
};

TemplatesListState.propTypes = {};

export default TemplatesListState;
