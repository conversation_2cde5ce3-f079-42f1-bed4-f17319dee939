import { SearchResultsIcon } from '../../assets/icons/large';
import { Colors, Effects } from '../../styles/global.styles';
import { HeaderLarge } from '../../styles/typography';
import { AppWideSearchVariant } from './AppWideSearch.types';
import styled from '@emotion/styled';

const INPUT_TOP_BORDER = 35;

export const Container = styled('div')(
  ({ variant }: { variant: AppWideSearchVariant }) => ({
    ...Effects.Shadows.dropdown,
    position: 'relative',
    backgroundColor: variant === 'new-nav' ? Colors.grey4 : Colors.white,
    borderRadius: '4px',
    '> div': {
      display: 'block',
    },

    ...(variant === 'new-nav' && {
      borderRadius: 0,
      boxShadow: 'none',

      '.AppWideSearchItem': {
        paddingLeft: '10px',
        boxSizing: 'border-box',

        '&:hover': {
          backgroundColor: Colors.white,
        },
      },
    }),
  })
);

export const SearchInputContainer = styled('div')(
  ({ variant }: { variant: AppWideSearchVariant }) => ({
    padding: '0 10px',
    ...(variant === 'new-nav' && {
      borderBottom: `solid 1px ${Colors.grey3}`,
      // border instead of padding/margin due to better positioning of the spinner and clear button
      borderTop: `${INPUT_TOP_BORDER}px solid ${Colors.white}`,
      paddingBottom: '7px',
      backgroundColor: Colors.white,
    }),
  })
);

export const InputSearchWrapper = styled('div')(
  ({ variant }: { variant: AppWideSearchVariant }) => ({
    '& > div': {
      verticalAlign: 'middle',
    },

    input: {
      minHeight: '36px',
      ...(variant === 'new-nav' && {
        paddingLeft: '10px !important',
        fontSize: '16px',
      }),
    },
    svg: {
      top: '4px',
    },
    '.SearchInput-remove': {
      width: '28px',
      height: '28px',
    },

    ...(variant === 'new-nav' && {
      'svg:not(.SearchInput-remove)': {
        display: 'none',
      },
    }),
  })
);

export const SpinnerContainer = styled('div')(({
  variant,
}: {
  variant: AppWideSearchVariant;
}) => {
  const top = variant === 'new-nav' ? INPUT_TOP_BORDER + 4 : 4;
  return {
    position: 'absolute',
    top: `${top}px`,
    right: '10px',
    backgroundColor: Colors.white,

    '.spinner-animation': {
      transform: 'scale(0.7)',
      verticalAlign: 'middle',
    },
  };
});

export const SearchResultsContainer = styled('div')({
  padding: 0,
  borderTop: `solid 1px ${Colors.grey3}`,
  maxHeight: '500px',
  overflow: 'hidden',
  overflowY: 'auto',
  backgroundColor: Colors.white,
});

export const NoSearchResultsContainer = styled('div')({
  textAlign: 'center',
});

export const NoSearchResults = styled('div')({
  padding: '65px 0',
});

export const StyledSearchResultsIcon = styled(SearchResultsIcon)({
  margin: '150px 0',
});

export const StyledHeaderLarge = styled(HeaderLarge)({
  color: Colors.grey2,
});
