import { CustomIcon } from '../../assets/icons';
import { Vendor } from '../../types/vendor';

export enum SearchResultItemType {
  VENDOR = 'vendor',
  TASK = 'task',
  TASK_GROUP = 'task_group',
  JOB_OPENING = 'job_opening',
  DOCUMENT = 'document',
  AGREEMENT = 'agreement',
  REQUESTED_DOCUMENT = 'requested_document',
  TEAMMATE = 'teammate',
  ERROR = 'error',
}

export type SearchResultItem = {
  id: number;
  type: SearchResultItemType;
  title: string;
  onClick: (event) => void;
  vendor?: Vendor;
};

export type SearchResultItemTypeRecord = Record<
  SearchResultItemType,
  {
    category: string;
    Icon: React.FunctionComponent<CustomIcon>;
  }
>;

export type AppWideSearchVariant = 'default' | 'new-nav';
