import React from 'react';
/* eslint-disable */
import vendor from '../../../fixtures/vendor/vendor.json';
import { Colors } from '../../../styles/global.styles';
import { Vendor } from '../../../types/vendor';
import { SearchResultItemType } from '../AppWideSearch.types';
import AppWideSearchItem from './AppWideSearchItem';

const types = [
  'vendor',
  'task',
  'task_group',
  'job_opening',
  'document',
  'agreement',
  'requested_document',
  'teammate',
  'error',
];

export default {
  title: 'Core/Components/AppWideSearch/AppWideSearchItem',
  component: AppWideSearchItem,
};

export const Default = () => (
  <div style={{ width: '300px', border: `1px solid ${Colors.grey3}` }}>
    {types.map((type, i) => (
      <AppWideSearchItem
        key={i}
        data={{
          id: i,
          title: 'Project Cobra Kai Anaconda Ninja Turtles',
          type: type as SearchResultItemType,
          vendor: vendor as unknown as Vendor,
          onClick: () => console.log('click'),
        }}
      />
    ))}
  </div>
);

export const List = () => (
  <div style={{ width: '740px', border: `1px solid ${Colors.grey3}` }}>
    {types.map((type, i) => (
      <AppWideSearchItem
        key={i}
        data={{
          id: i,
          title: 'Project Cobra Kai Anaconda Ninja Turtles',
          type: type as SearchResultItemType,
          vendor: vendor as unknown as Vendor,
          onClick: () => console.log('click'),
        }}
        variant={'list'}
      />
    ))}
  </div>
);

const TestTemplate = (args) => (
  <div style={{ width: '300px', border: `1px solid ${Colors.grey3}` }}>
    <AppWideSearchItem key={1} {...args} />
  </div>
);

export const LongValueList = (args) => TestTemplate(args);
LongValueList.args = {
  data: {
    id: 1,
    title: 'Project Cobra Kai Anaconda Ninja Turtles',
    type: SearchResultItemType.JOB_OPENING as SearchResultItemType,
    vendor: vendor as unknown as Vendor,
    onClick: () => console.log('click'),
  },
};
