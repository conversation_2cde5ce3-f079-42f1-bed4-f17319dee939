// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Rendering 1099p stage for buyer: Active 1099P insurance 1`] = `
.emotion-class {
  max-width: 768px;
  width: 100%;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
}

.emotion-class {
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: var(--spacing-xs);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
  margin-bottom: var(--spacing-m);
  max-width: 768px;
  padding: var(--spacing-m);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
  text-wrap: wrap;
  overflow-wrap: break-word;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background: #FFFFFF;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  font-size: 14px;
  min-height: 65px;
  padding: 0 20px;
  padding: var(--spacing-s) var(--spacing-m);
}

.emotion-class a {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-link-color)!important;
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 20px;
  cursor: pointer;
  font-weight: 600;
}

.emotion-class * {
  fill: var(--text-link-color);
}

.emotion-class {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: var(--spacing-m);
  margin-left: auto;
}

.emotion-class {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  color: var(--statuses-status-active);
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  font-size: 12px;
  font-weight: 600;
  margin-left: auto;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 30px;
  background-color: #FFFFFF;
  border: 1px solid var(--action-base);
  padding-top: 0;
  padding-right: 14px;
  padding-bottom: 0;
  padding-left: 14px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 28px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 12px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div>
  <div
    class="emotion-class"
    data-testid="Ten99PStageForBuyer"
  >
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            General Liability
          </h5>
          <p
            class="emotion-class"
          >
            Partner has successfully purchased this insurance via 1099Policy.
          </p>
        </div>
        <div>
          <div
            class="status-preview emotion-class"
          >
            <a
              class="emotion-class"
              href="http://localhost/api/insurances/1/download/"
              target="_blank"
            >
              <icon-mock
                classname="css-vheie4"
              />
              Insurance from 1099Policy: General Liability
            </a>
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                color="var(--statuses-status-active)"
              >
                <icon-mock
                  classname="css-8sv2oy"
                  fill="var(--statuses-status-active)"
                  size="24"
                />
                Approved
              </div>
              <button
                class=" emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  Preview
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            Workers' Compensation
          </h5>
          <p
            class="emotion-class"
          >
            Partner has successfully purchased this insurance via 1099Policy.
          </p>
        </div>
        <div>
          <div
            class="status-preview emotion-class"
          >
            <a
              class="emotion-class"
              href="http://localhost/api/insurances/2/download/"
              target="_blank"
            >
              <icon-mock
                classname="css-vheie4"
              />
              Insurance from 1099Policy: Workers' Compensation
            </a>
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                color="var(--statuses-status-active)"
              >
                <icon-mock
                  classname="css-8sv2oy"
                  fill="var(--statuses-status-active)"
                  size="24"
                />
                Approved
              </div>
              <button
                class=" emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  Preview
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Rendering 1099p stage for buyer: Approved uploaded insurance 1`] = `
.emotion-class {
  max-width: 768px;
  width: 100%;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
}

.emotion-class {
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: var(--spacing-xs);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
  margin-bottom: var(--spacing-m);
  max-width: 768px;
  padding: var(--spacing-m);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
  text-wrap: wrap;
  overflow-wrap: break-word;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background: #FFFFFF;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  font-size: 14px;
  min-height: 65px;
  padding: 0 20px;
  padding: var(--spacing-s) var(--spacing-m);
}

.emotion-class a {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-link-color)!important;
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 20px;
  cursor: pointer;
  font-weight: 600;
}

.emotion-class * {
  fill: var(--text-link-color);
}

.emotion-class {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: var(--spacing-m);
  margin-left: auto;
}

.emotion-class {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  color: var(--statuses-status-active);
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  font-size: 12px;
  font-weight: 600;
  margin-left: auto;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 30px;
  background-color: #FFFFFF;
  border: 1px solid var(--action-base);
  padding-top: 0;
  padding-right: 14px;
  padding-bottom: 0;
  padding-left: 14px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 28px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 12px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div>
  <div
    class="emotion-class"
    data-testid="Ten99PStageForBuyer"
  >
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            General Liability
          </h5>
          <p
            class="emotion-class"
          />
        </div>
        <div>
          <div
            class="status-preview emotion-class"
          >
            <a
              class="emotion-class"
              href="http://localhost/api/insurances/1/download/"
              target="_blank"
            >
              <icon-mock
                classname="css-vheie4"
              />
              Proof of insurance: General Liability
            </a>
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                color="var(--statuses-status-active)"
              >
                <icon-mock
                  classname="css-8sv2oy"
                  fill="var(--statuses-status-active)"
                  size="24"
                />
                Approved
              </div>
              <button
                class=" emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  Preview
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            Workers' Compensation
          </h5>
          <p
            class="emotion-class"
          />
        </div>
        <div>
          <div
            class="status-preview emotion-class"
          >
            <a
              class="emotion-class"
              href="http://localhost/api/insurances/2/download/"
              target="_blank"
            >
              <icon-mock
                classname="css-vheie4"
              />
              Proof of insurance: Workers' Compensation
            </a>
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                color="var(--statuses-status-active)"
              >
                <icon-mock
                  classname="css-8sv2oy"
                  fill="var(--statuses-status-active)"
                  size="24"
                />
                Approved
              </div>
              <button
                class=" emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  Preview
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Rendering 1099p stage for buyer: Eligible to reuse 1099P insurance 1`] = `
.emotion-class {
  max-width: 768px;
  width: 100%;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
}

.emotion-class {
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: var(--spacing-xs);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
  margin-bottom: var(--spacing-m);
  max-width: 768px;
  padding: var(--spacing-m);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
  text-wrap: wrap;
  overflow-wrap: break-word;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background: #FFFFFF;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  font-size: 14px;
  min-height: 65px;
  padding: 0 20px;
  padding: var(--spacing-s) var(--spacing-m);
}

.emotion-class a {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-link-color)!important;
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 20px;
  cursor: pointer;
  font-weight: 600;
}

.emotion-class * {
  fill: var(--text-link-color);
}

.emotion-class {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: var(--spacing-m);
  margin-left: auto;
}

.emotion-class {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  color: var(--statuses-status-active);
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  font-size: 12px;
  font-weight: 600;
  margin-left: auto;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 30px;
  background-color: #FFFFFF;
  border: 1px solid var(--action-base);
  padding-top: 0;
  padding-right: 14px;
  padding-bottom: 0;
  padding-left: 14px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 28px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 12px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div>
  <div
    class="emotion-class"
    data-testid="Ten99PStageForBuyer"
  >
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            General Liability
          </h5>
          <p
            class="emotion-class"
          >
            Partner has successfully purchased this insurance via 1099Policy.
          </p>
        </div>
        <div>
          <div
            class="status-preview emotion-class"
          >
            <a
              class="emotion-class"
              href="http://localhost/api/insurances/1/download/"
              target="_blank"
            >
              <icon-mock
                classname="css-vheie4"
              />
              Insurance from 1099Policy: General Liability
            </a>
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                color="var(--statuses-status-active)"
              >
                <icon-mock
                  classname="css-8sv2oy"
                  fill="var(--statuses-status-active)"
                  size="24"
                />
                Approved
              </div>
              <button
                class=" emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  Preview
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            Workers' Compensation
          </h5>
          <p
            class="emotion-class"
          >
            Partner has successfully purchased this insurance via 1099Policy.
          </p>
        </div>
        <div>
          <div
            class="status-preview emotion-class"
          >
            <a
              class="emotion-class"
              href="http://localhost/api/insurances/2/download/"
              target="_blank"
            >
              <icon-mock
                classname="css-vheie4"
              />
              Insurance from 1099Policy: Workers' Compensation
            </a>
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                color="var(--statuses-status-active)"
              >
                <icon-mock
                  classname="css-8sv2oy"
                  fill="var(--statuses-status-active)"
                  size="24"
                />
                Approved
              </div>
              <button
                class=" emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  Preview
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Rendering 1099p stage for buyer: Mixed active 1099P insurance and processing uploaded insurance 1`] = `
.emotion-class {
  max-width: 768px;
  width: 100%;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
}

.emotion-class {
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: var(--spacing-xs);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
  margin-bottom: var(--spacing-m);
  max-width: 768px;
  padding: var(--spacing-m);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
  text-wrap: wrap;
  overflow-wrap: break-word;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background: #FFFFFF;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  font-size: 14px;
  min-height: 65px;
  padding: 0 20px;
  padding: var(--spacing-s) var(--spacing-m);
}

.emotion-class a {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-link-color)!important;
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 20px;
  cursor: pointer;
  font-weight: 600;
}

.emotion-class * {
  fill: var(--text-link-color);
}

.emotion-class {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: var(--spacing-m);
  margin-left: auto;
}

.emotion-class {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  color: var(--statuses-status-active);
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  font-size: 12px;
  font-weight: 600;
  margin-left: auto;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 30px;
  background-color: #FFFFFF;
  border: 1px solid var(--action-base);
  padding-top: 0;
  padding-right: 14px;
  padding-bottom: 0;
  padding-left: 14px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 28px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 12px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  margin-bottom: var(--spacing-m);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 20px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  vertical-align: middle;
  -webkit-tap-highlight-color: transparent;
  margin-left: -11px;
  margin-right: 16px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  margin: 6px 0;
  font-size: 10px;
}

.emotion-class.Mui-disabled {
  cursor: default;
}

.emotion-class .MuiFormControlLabel-label.Mui-disabled {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class span {
  padding: 0;
  display: block;
}

.emotion-class svg {
  margin: -6px;
}

.emotion-class .radioButtonOnIcon path:nth-of-type(1) {
  fill: #465AB6;
}

.emotion-class .radioButtonOffIcon path:nth-of-type(1) {
  fill: #D1D6ED;
}

.emotion-class:hover .radioButtonOffIcon path:nth-of-type(1) {
  fill: #465AB6;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  padding: 9px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.6);
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class.Mui-disabled {
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class:hover {
  background-color: transparent;
}

.emotion-class {
  cursor: inherit;
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  z-index: 1;
}

.emotion-class {
  margin: 0;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0.00938em;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  margin-left: 6px;
  color: #303757;
  font-weight: 400;
}

.emotion-class {
  max-width: 50%;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  margin-bottom: 5px;
}

.emotion-class>a {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  position: relative;
}

.emotion-class svg {
  fill: #465AB6;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class {
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
  font-size: 12px;
  color: #465AB6;
  vertical-align: middle;
}

.emotion-class {
  position: relative;
}

.emotion-class:hover fieldset {
  border-color: #B4BCE0;
}

.emotion-class fieldset {
  border-color: #D1D6ED;
}

.emotion-class input {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  padding-left: 9px;
  padding-right: 9px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
  vertical-align: top;
  width: 100%;
  margin: 0;
}

.emotion-class .clearButton {
  opacity: 1;
}

@media (pointer: fine) {
  .emotion-class .clearButton {
    opacity: 0;
  }

  .emotion-class:hover .clearButton,
  .emotion-class:focus-within .clearButton {
    opacity: 1;
  }
}

.emotion-class {
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.4375em;
  letter-spacing: 0.00938em;
  color: rgba(0, 0, 0, 0.87);
  box-sizing: border-box;
  position: relative;
  cursor: text;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  border-radius: 4px;
  padding-right: 14px;
  padding: 5px;
  height: 45px;
  background: #FFFFFF;
}

.emotion-class.Mui-disabled {
  color: rgba(0, 0, 0, 0.38);
  cursor: default;
}

.emotion-class:hover .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.87);
}

@media (hover: none) {
  .emotion-class:hover .MuiOutlinedInput-notchedOutline {
    border-color: rgba(0, 0, 0, 0.23);
  }
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-width: 2px;
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #1976d2;
}

.emotion-class.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: #d32f2f;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.26);
}

.emotion-class input {
  font-size: 14px;
  color: #303757;
  font-family: "Open Sans",sans-serif;
}

.emotion-class input::-webkit-input-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input::-moz-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input:-ms-input-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input::placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class .MuiOutlinedInput-notchedOutline {
  border-color: #D1D6ED;
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline,
.emotion-class:hover.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #465AB6;
}

.emotion-class:hover .MuiOutlinedInput-notchedOutline {
  border-color: #B4BCE0;
  outline: none;
}

.emotion-class.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: #FF7F8A;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: #E4E5EB;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::-webkit-input-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::-moz-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline:-ms-input-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::placeholder {
  color: blue;
}

.emotion-class {
  font: inherit;
  letter-spacing: inherit;
  color: currentColor;
  padding: 4px 0 5px;
  border: 0;
  box-sizing: content-box;
  background: none;
  height: 1.4375em;
  margin: 0;
  -webkit-tap-highlight-color: transparent;
  display: block;
  min-width: 0;
  width: 100%;
  -webkit-animation-name: mui-auto-fill-cancel;
  animation-name: mui-auto-fill-cancel;
  -webkit-animation-duration: 10ms;
  animation-duration: 10ms;
  padding: 16.5px 14px;
  padding-right: 0;
  cursor: pointer;
  padding: 9px 12px 10px;
  width: 100%;
}

.emotion-class::-webkit-input-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class::-moz-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class::-ms-input-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class:focus {
  outline: 0;
}

.emotion-class:invalid {
  box-shadow: none;
}

.emotion-class::-webkit-search-decoration {
  -webkit-appearance: none;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-webkit-input-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-moz-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-ms-input-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-webkit-input-placeholder {
  opacity: 0.42;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-moz-placeholder {
  opacity: 0.42;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-ms-input-placeholder {
  opacity: 0.42;
}

.emotion-class.Mui-disabled {
  opacity: 1;
  -webkit-text-fill-color: rgba(0, 0, 0, 0.38);
}

.emotion-class:-webkit-autofill {
  -webkit-animation-duration: 5000s;
  animation-duration: 5000s;
  -webkit-animation-name: mui-auto-fill;
  animation-name: mui-auto-fill;
}

.emotion-class:-webkit-autofill {
  border-radius: inherit;
}

.emotion-class.Mui-disabled {
  color: #A6ABBF;
  -webkit-text-fill-color: #A6ABBF;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  max-height: 2em;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.54);
  margin-left: 8px;
  position: absolute;
  right: 17px;
}

.emotion-class button {
  color: #465AB6;
  margin-right: -12px!important;
}

.emotion-class button.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class button:hover {
  background-color: transparent;
}

.emotion-class button svg {
  width: 20px;
  height: 20px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-right: -12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: 1.5rem;
}

.emotion-class {
  text-align: left;
  position: absolute;
  bottom: 0;
  right: 0;
  top: -5px;
  left: 0;
  margin: 0;
  padding: 0 8px;
  pointer-events: none;
  border-radius: inherit;
  border-style: solid;
  border-width: 1px;
  overflow: hidden;
  min-width: 0%;
  border-color: rgba(0, 0, 0, 0.23);
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 34px;
  min-height: auto;
  border-width: 2px;
  border-style: solid;
  outline: none;
  top: 0;
  -webkit-transition: border-color ease-in-out 0.15s,box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s,box-shadow ease-in-out 0.15s;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class legend {
  display: none;
}

.emotion-class {
  float: unset;
  width: auto;
  overflow: hidden;
  padding: 0;
  line-height: 11px;
  -webkit-transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
  transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
}

.emotion-class {
  z-index: 1300;
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  outline: 0;
  transform-origin: top center;
  transform-origin: bottom center;
}

.emotion-class {
  display: grid;
  grid-auto-columns: max-content auto max-content;
  grid-auto-rows: max-content auto max-content;
}

.emotion-class .MuiPickersLayout-actionBar {
  grid-column: 1/4;
  grid-row: 3;
}

.emotion-class .MuiPickersLayout-toolbar {
  grid-column: 2/4;
  grid-row: 1;
}

.emotion-class .MuiPickersLayout-shortcuts {
  grid-column: 1;
  grid-row: 2/3;
}

.emotion-class {
  grid-column: 2;
  grid-row: 2;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  overflow: hidden;
  width: 320px;
  max-height: 336px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: 0 auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 336px;
}

.emotion-class>div:first-of-type {
  position: absolute;
  width: calc(100% - 40px);
  box-sizing: border-box;
  background: #fff;
  min-height: initial;
  max-height: initial;
  height: 54px;
  padding: 16px 0 6px;
  margin: 0 20px;
}

.emotion-class>div:first-of-type+div {
  margin-top: 54px;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  font-size: 14px;
  color: #465AB6;
  font-weight: 600;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .MuiButtonBase-root {
  display: none;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .PrivatePickersFadeTransitionGroup-root {
  color: #465AB6;
  font-weight: 600;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .PrivatePickersFadeTransitionGroup-root:nth-of-type(2) div {
  margin-right: 0;
}

.emotion-class>div:first-of-type>div:nth-of-type(2) {
  width: 100%;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-top: 12px;
  margin-bottom: 4px;
  padding-left: 24px;
  padding-right: 12px;
  max-height: 40px;
  min-height: 40px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  margin-right: auto;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 500;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0.00938em;
}

.emotion-class {
  display: block;
  position: relative;
}

.emotion-class {
  margin-right: 6px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  padding: 5px;
  font-size: 1.125rem;
  margin-right: auto;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: 1.5rem;
  will-change: transform;
  -webkit-transition: -webkit-transform 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-right: -12px;
  color: #465AB6;
  padding: 5px;
  width: 34px;
  height: 34px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class svg {
  width: 24px;
  height: 24px;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: inherit;
}

.emotion-class {
  width: 24px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-left: -12px;
  color: #465AB6;
  padding: 5px;
  width: 34px;
  height: 34px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class svg {
  width: 24px;
  height: 24px;
}

.emotion-class {
  display: block;
  position: relative;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  margin: 0;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  font-weight: 600;
  font-size: 12px;
  width: 36px;
  height: 40px;
  margin: 0 2px;
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: rgba(0, 0, 0, 0.6);
}

.emotion-class {
  display: block;
  position: relative;
  overflow-x: hidden;
  min-height: 240px;
}

.emotion-class>* {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
}

.emotion-class .MuiPickersSlideTransition-slideEnter-left {
  will-change: transform;
  -webkit-transform: translate(100%);
  -moz-transform: translate(100%);
  -ms-transform: translate(100%);
  transform: translate(100%);
  z-index: 1;
}

.emotion-class .MuiPickersSlideTransition-slideEnter-right {
  will-change: transform;
  -webkit-transform: translate(-100%);
  -moz-transform: translate(-100%);
  -ms-transform: translate(-100%);
  transform: translate(-100%);
  z-index: 1;
}

.emotion-class .MuiPickersSlideTransition-slideEnterActive {
  -webkit-transform: translate(0%);
  -moz-transform: translate(0%);
  -ms-transform: translate(0%);
  transform: translate(0%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
}

.emotion-class .MuiPickersSlideTransition-slideExit {
  -webkit-transform: translate(0%);
  -moz-transform: translate(0%);
  -ms-transform: translate(0%);
  transform: translate(0%);
}

.emotion-class .MuiPickersSlideTransition-slideExitActiveLeft-left {
  will-change: transform;
  -webkit-transform: translate(-100%);
  -moz-transform: translate(-100%);
  -ms-transform: translate(-100%);
  transform: translate(-100%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  z-index: 0;
}

.emotion-class .MuiPickersSlideTransition-slideExitActiveLeft-right {
  will-change: transform;
  -webkit-transform: translate(100%);
  -moz-transform: translate(100%);
  -ms-transform: translate(100%);
  transform: translate(100%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  z-index: 0;
}

.emotion-class {
  overflow: hidden;
}

.emotion-class button {
  margin-right: 2px!important;
}

.emotion-class {
  margin: 2px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  color: rgba(0, 0, 0, 0.6);
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:not(.Mui-selected) {
  border: 1px solid rgba(0, 0, 0, 0.6);
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class:not(.Mui-selected) {
  -webkit-transition: 220ms all;
  transition: 220ms all;
}

.emotion-class:not(.Mui-selected):before {
  content: '';
  display: inline-block;
  border-left: 7px solid transparent;
  border-bottom: 7px solid #465AB6;
  border-top-color: rgba(0, 0, 0, 0.2);
  position: absolute;
  bottom: 4px;
  right: 4px;
}

.emotion-class:not(.Mui-selected):hover:before {
  display: none;
}

<div>
  <div
    class="emotion-class"
    data-testid="Ten99PStageForBuyer"
  >
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            General Liability
          </h5>
          <p
            class="emotion-class"
          >
            Partner has successfully purchased this insurance via 1099Policy.
          </p>
        </div>
        <div>
          <div
            class="status-preview emotion-class"
          >
            <a
              class="emotion-class"
              href="http://localhost/api/insurances/1/download/"
              target="_blank"
            >
              <icon-mock
                classname="css-vheie4"
              />
              Insurance from 1099Policy: General Liability
            </a>
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                color="var(--statuses-status-active)"
              >
                <icon-mock
                  classname="css-8sv2oy"
                  fill="var(--statuses-status-active)"
                  size="24"
                />
                Approved
              </div>
              <button
                class=" emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  Preview
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            Workers' Compensation
          </h5>
          <p
            class="emotion-class"
          >
            Please review the proof of insurance.
          </p>
        </div>
        <div>
          <div
            class="status-preview emotion-class"
          >
            <a
              class="emotion-class"
              href="http://localhost/api/insurances/2/download/"
              target="_blank"
            >
              <icon-mock
                classname="css-vheie4"
              />
              Proof of insurance: Workers' Compensation
            </a>
            <div
              class="emotion-class"
            >
              <button
                class=" emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  Preview
                </span>
              </button>
            </div>
          </div>
          <p
            class="emotion-class"
          >
            <div
              class="MuiFormGroup-root MuiRadioGroup-root emotion-class"
              role="radiogroup"
            >
              <div
                data-testid="InputRadioButtonGroupOption_approved"
                style="max-width: 100%;"
              >
                <label
                  class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd emotion-class"
                >
                  <span
                    class="MuiButtonBase-root MuiRadio-root MuiRadio-colorDefault PrivateSwitchBase-root MuiRadio-root MuiRadio-colorDefault Mui-checked MuiRadio-root MuiRadio-colorDefault emotion-class"
                  >
                    <input
                      checked=""
                      class="PrivateSwitchBase-input emotion-class"
                      name=":ro:"
                      type="radio"
                      value="approved"
                    />
                    <span
                      class="radioButtonOnIcon"
                      font-size="medium"
                    >
                      <icon-mock
                        classname="css-1qetkxy"
                        size="32"
                      />
                    </span>
                  </span>
                  <span
                    class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label emotion-class"
                  >
                    <span
                      class="emotion-class"
                    >
                      Approve
                    </span>
                  </span>
                </label>
              </div>
              <div
                data-testid="InputRadioButtonGroupOption_rejected"
                style="max-width: 100%;"
              >
                <label
                  class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd emotion-class"
                >
                  <span
                    class="MuiButtonBase-root MuiRadio-root MuiRadio-colorDefault PrivateSwitchBase-root MuiRadio-root MuiRadio-colorDefault MuiRadio-root MuiRadio-colorDefault emotion-class"
                  >
                    <input
                      class="PrivateSwitchBase-input emotion-class"
                      name=":ro:"
                      type="radio"
                      value="rejected"
                    />
                    <span
                      class="radioButtonOffIcon"
                      font-size="medium"
                    >
                      <icon-mock
                        classname="css-1qetkxy"
                        size="32"
                      />
                    </span>
                  </span>
                  <span
                    class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label emotion-class"
                  >
                    <span
                      class="emotion-class"
                    >
                      Reject
                    </span>
                  </span>
                </label>
              </div>
            </div>
          </p>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <div>
                <span
                  class="emotion-class"
                  data-testid="InputLabel_ConfirmPolicyExpirationDate"
                >
                  Confirm policy expiration date
                </span>
              </div>
              <a
                class=" emotion-class"
                data-testid="InputActionLabel_Button"
                style="text-align: right;"
              >
                <span
                  class="emotion-class"
                >
                  Reset
                </span>
              </a>
            </div>
            <div
              class="emotion-class"
            >
              <div
                class="MuiFormControl-root MuiTextField-root emotion-class"
              >
                <div
                  class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd emotion-class"
                >
                  <input
                    aria-invalid="false"
                    autocomplete="off"
                    class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd emotion-class"
                    id=":rp:"
                    inputmode="text"
                    placeholder="MM/DD/YYYY"
                    type="text"
                    value=""
                  />
                  <div
                    class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium emotion-class"
                  >
                    <button
                      aria-label="Choose date"
                      class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium emotion-class"
                      tabindex="0"
                      type="button"
                    >
                      <svg
                        aria-hidden="true"
                        class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium emotion-class"
                        data-testid="CalendarIcon"
                        focusable="false"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
                        />
                      </svg>
                    </button>
                  </div>
                  <fieldset
                    aria-hidden="true"
                    class="MuiOutlinedInput-notchedOutline emotion-class"
                  >
                    <legend
                      class="emotion-class"
                    >
                      <span
                        aria-hidden="true"
                        class="notranslate"
                      >
                        ​
                      </span>
                    </legend>
                  </fieldset>
                </div>
              </div>
            </div>
            <div
              class="MuiPopper-root MuiPickersPopper-root emotion-class"
              role="dialog"
              style="position: fixed; top: 0px; left: 0px; display: none;"
            >
              <div
                data-testid="sentinelStart"
                tabindex="-1"
              />
              <div
                class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation8 MuiPickersPopper-paper emotion-class"
                style="--Paper-shadow: 0px 5px 5px -3px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12); opacity: 0; transform: scale(0.75, 0.5625); visibility: hidden;"
                tabindex="-1"
              >
                <div
                  class="MuiPickersLayout-root emotion-class"
                >
                  <div
                    class="MuiPickersLayout-contentWrapper emotion-class"
                  >
                    <div
                      class="MuiDateCalendar-root emotion-class"
                    >
                      <div
                        class="MuiPickersCalendarHeader-root emotion-class"
                      >
                        <div
                          aria-live="polite"
                          class="MuiPickersCalendarHeader-labelContainer emotion-class"
                          role="presentation"
                        >
                          <div
                            class="MuiPickersFadeTransitionGroup-root emotion-class"
                          >
                            <div
                              class="MuiPickersCalendarHeader-label emotion-class"
                              id=":rs:-grid-label"
                              style="opacity: 1;"
                            >
                              May 2024
                            </div>
                          </div>
                          <button
                            aria-label="calendar view is open, switch to year view"
                            class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall MuiPickersCalendarHeader-switchViewButton emotion-class"
                            tabindex="0"
                            type="button"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiPickersCalendarHeader-switchViewIcon emotion-class"
                              data-testid="ArrowDropDownIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M7 10l5 5 5-5z"
                              />
                            </svg>
                          </button>
                        </div>
                        <div
                          class="MuiPickersArrowSwitcher-root emotion-class"
                          style="opacity: 1; webkit-transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
                        >
                          <button
                            aria-label="Previous month"
                            class="MuiButtonBase-root Mui-disabled MuiIconButton-root Mui-disabled MuiIconButton-edgeEnd MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-previousIconButton emotion-class"
                            disabled=""
                            tabindex="-1"
                            title="Previous month"
                            type="button"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-leftArrowIcon emotion-class"
                              data-testid="ArrowLeftIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"
                              />
                            </svg>
                          </button>
                          <div
                            class="MuiPickersArrowSwitcher-spacer emotion-class"
                          />
                          <button
                            aria-label="Next month"
                            class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeStart MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-nextIconButton emotion-class"
                            tabindex="0"
                            title="Next month"
                            type="button"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-rightArrowIcon emotion-class"
                              data-testid="ArrowRightIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                      <div
                        class="MuiPickersFadeTransitionGroup-root MuiDateCalendar-viewTransitionContainer emotion-class"
                      >
                        <div
                          style="opacity: 1;"
                        >
                          <div
                            aria-labelledby=":rs:-grid-label"
                            class="MuiDayCalendar-root emotion-class"
                            role="grid"
                          >
                            <div
                              class="MuiDayCalendar-header emotion-class"
                              role="row"
                            >
                              <span
                                aria-label="Sunday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                S
                              </span>
                              <span
                                aria-label="Monday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                M
                              </span>
                              <span
                                aria-label="Tuesday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                T
                              </span>
                              <span
                                aria-label="Wednesday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                W
                              </span>
                              <span
                                aria-label="Thursday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                T
                              </span>
                              <span
                                aria-label="Friday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                F
                              </span>
                              <span
                                aria-label="Saturday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                S
                              </span>
                            </div>
                            <div
                              class="MuiPickersSlideTransition-root MuiDayCalendar-slideTransition emotion-class"
                              role="presentation"
                            >
                              <div
                                class="MuiDayCalendar-monthContainer emotion-class"
                                role="rowgroup"
                              >
                                <div
                                  aria-rowindex="1"
                                  class="MuiDayCalendar-weekContainer emotion-class"
                                  role="row"
                                >
                                  <button
                                    aria-colindex="1"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                    data-timestamp="1714262400000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    28
                                  </button>
                                  <button
                                    aria-colindex="2"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                    data-timestamp="1714348800000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    29
                                  </button>
                                  <button
                                    aria-colindex="3"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                    data-timestamp="1714435200000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    30
                                  </button>
                                  <button
                                    aria-colindex="4"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714521600000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    1
                                  </button>
                                  <button
                                    aria-colindex="5"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714608000000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    2
                                  </button>
                                  <button
                                    aria-colindex="6"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714694400000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    3
                                  </button>
                                  <button
                                    aria-colindex="7"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714780800000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    4
                                  </button>
                                </div>
                                <div
                                  aria-rowindex="2"
                                  class="MuiDayCalendar-weekContainer emotion-class"
                                  role="row"
                                >
                                  <button
                                    aria-colindex="1"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714867200000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    5
                                  </button>
                                  <button
                                    aria-colindex="2"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714953600000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    6
                                  </button>
                                  <button
                                    aria-colindex="3"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715040000000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    7
                                  </button>
                                  <button
                                    aria-colindex="4"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715126400000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    8
                                  </button>
                                  <button
                                    aria-colindex="5"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715212800000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    9
                                  </button>
                                  <button
                                    aria-colindex="6"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715299200000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    10
                                  </button>
                                  <button
                                    aria-colindex="7"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715385600000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    11
                                  </button>
                                </div>
                                <div
                                  aria-rowindex="3"
                                  class="MuiDayCalendar-weekContainer emotion-class"
                                  role="row"
                                >
                                  <button
                                    aria-colindex="1"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715472000000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    12
                                  </button>
                                  <button
                                    aria-colindex="2"
                                    aria-current="date"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin MuiPickersDay-today emotion-class"
                                    data-timestamp="1715558400000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    13
                                  </button>
                                  <button
                                    aria-colindex="3"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715644800000"
                                    role="gridcell"
                                    tabindex="0"
                                    type="button"
                                  >
                                    14
                                  </button>
                                  <button
                                    aria-colindex="4"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715731200000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    15
                                  </button>
                                  <button
                                    aria-colindex="5"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715817600000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    16
                                  </button>
                                  <button
                                    aria-colindex="6"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715904000000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    17
                                  </button>
                                  <button
                                    aria-colindex="7"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715990400000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    18
                                  </button>
                                </div>
                                <div
                                  aria-rowindex="4"
                                  class="MuiDayCalendar-weekContainer emotion-class"
                                  role="row"
                                >
                                  <button
                                    aria-colindex="1"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716076800000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    19
                                  </button>
                                  <button
                                    aria-colindex="2"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716163200000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    20
                                  </button>
                                  <button
                                    aria-colindex="3"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716249600000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    21
                                  </button>
                                  <button
                                    aria-colindex="4"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716336000000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    22
                                  </button>
                                  <button
                                    aria-colindex="5"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716422400000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    23
                                  </button>
                                  <button
                                    aria-colindex="6"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716508800000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    24
                                  </button>
                                  <button
                                    aria-colindex="7"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716595200000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    25
                                  </button>
                                </div>
                                <div
                                  aria-rowindex="5"
                                  class="MuiDayCalendar-weekContainer emotion-class"
                                  role="row"
                                >
                                  <button
                                    aria-colindex="1"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716681600000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    26
                                  </button>
                                  <button
                                    aria-colindex="2"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716768000000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    27
                                  </button>
                                  <button
                                    aria-colindex="3"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716854400000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    28
                                  </button>
                                  <button
                                    aria-colindex="4"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716940800000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    29
                                  </button>
                                  <button
                                    aria-colindex="5"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1717027200000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    30
                                  </button>
                                  <button
                                    aria-colindex="6"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1717113600000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    31
                                  </button>
                                  <button
                                    aria-colindex="7"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                    data-timestamp="1717200000000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    1
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                data-testid="sentinelEnd"
                tabindex="-1"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Rendering 1099p stage for buyer: Not completed 1`] = `
.emotion-class {
  max-width: 768px;
  width: 100%;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
}

.emotion-class {
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: var(--spacing-xs);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
  margin-bottom: var(--spacing-m);
  max-width: 768px;
  padding: var(--spacing-m);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
  text-wrap: wrap;
  overflow-wrap: break-word;
}

<div>
  <div
    class="emotion-class"
    data-testid="Ten99PStageForBuyer"
  >
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            General Liability
          </h5>
          <p
            class="emotion-class"
          >
            Coverage required.
          </p>
        </div>
      </div>
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            Workers' Compensation
          </h5>
          <p
            class="emotion-class"
          >
            Coverage required.
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Rendering 1099p stage for buyer: Not started 1`] = `
.emotion-class {
  max-width: 768px;
  width: 100%;
}

.emotion-class {
  margin-bottom: 20px;
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  background-color: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 6px 16px;
  color: rgb(30, 70, 32);
  border: 1px solid #4caf50;
}

.emotion-class .MuiAlert-icon {
  color: #2e7d32;
}

.emotion-class.MuiAlert-root {
  width: 100%;
  max-width: 100%;
  color: #303757;
  background-color: #FFF6ED;
  border-color: #FFD2A6;
  font-family: "Open Sans",sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  padding: 15px 10px 15px 20px;
  margin: 0;
  box-sizing: border-box;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class.MuiAlert-root .MuiAlert-icon {
  padding: 0;
  margin: 0 4px 0 0;
}

.emotion-class.MuiAlert-root .MuiAlert-message {
  padding: 0;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.emotion-class.MuiAlert-root a {
  color: #465AB6;
}

@media (max-width: 768px) {
  .emotion-class.MuiAlert-root {
    padding: 20px;
  }
}

.emotion-class {
  margin-right: 12px;
  padding: 7px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 22px;
  opacity: 0.9;
}

.emotion-class {
  padding: 8px 0;
  min-width: 0;
  overflow: auto;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class>:first-of-type {
  padding: 0;
}

@media (max-width: 768px) {
  .emotion-class {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: start;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: start;
  }

  .emotion-class div {
    margin: 15px 0 0 0;
  }
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
}

.emotion-class {
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: var(--spacing-xs);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
  margin-bottom: var(--spacing-m);
  max-width: 768px;
  padding: var(--spacing-m);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
  text-wrap: wrap;
  overflow-wrap: break-word;
}

<div>
  <div
    class="emotion-class"
    data-testid="Ten99PStageForBuyer"
  >
    <div
      class="emotion-class"
    >
      <div
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 MuiAlert-root MuiAlert-colorSuccess MuiAlert-outlinedSuccess MuiAlert-outlined emotion-class"
        role="alert"
        style="--Paper-shadow: none;"
      >
        <div
          class="MuiAlert-icon emotion-class"
        >
          <icon-mock
            classname="css-hdmw1m"
            fill="#FFA64C"
            fontsize="inherit"
            size="30"
          />
        </div>
        <div
          class="MuiAlert-message emotion-class"
        >
          <div
            class="emotion-class"
          >
            <div>
              Pending partner actions.
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            General Liability
          </h5>
          <p
            class="emotion-class"
          >
            Coverage required.
          </p>
        </div>
      </div>
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            Workers' Compensation
          </h5>
          <p
            class="emotion-class"
          >
            Coverage required.
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Rendering 1099p stage for buyer: Not started single insurance 1`] = `
.emotion-class {
  max-width: 768px;
  width: 100%;
}

.emotion-class {
  margin-bottom: 20px;
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  background-color: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 6px 16px;
  color: rgb(30, 70, 32);
  border: 1px solid #4caf50;
}

.emotion-class .MuiAlert-icon {
  color: #2e7d32;
}

.emotion-class.MuiAlert-root {
  width: 100%;
  max-width: 100%;
  color: #303757;
  background-color: #FFF6ED;
  border-color: #FFD2A6;
  font-family: "Open Sans",sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  padding: 15px 10px 15px 20px;
  margin: 0;
  box-sizing: border-box;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class.MuiAlert-root .MuiAlert-icon {
  padding: 0;
  margin: 0 4px 0 0;
}

.emotion-class.MuiAlert-root .MuiAlert-message {
  padding: 0;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.emotion-class.MuiAlert-root a {
  color: #465AB6;
}

@media (max-width: 768px) {
  .emotion-class.MuiAlert-root {
    padding: 20px;
  }
}

.emotion-class {
  margin-right: 12px;
  padding: 7px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 22px;
  opacity: 0.9;
}

.emotion-class {
  padding: 8px 0;
  min-width: 0;
  overflow: auto;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class>:first-of-type {
  padding: 0;
}

@media (max-width: 768px) {
  .emotion-class {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: start;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: start;
  }

  .emotion-class div {
    margin: 15px 0 0 0;
  }
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
}

.emotion-class {
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: var(--spacing-xs);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
  margin-bottom: var(--spacing-m);
  max-width: 768px;
  padding: var(--spacing-m);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
  text-wrap: wrap;
  overflow-wrap: break-word;
}

<div>
  <div
    class="emotion-class"
    data-testid="Ten99PStageForBuyer"
  >
    <div
      class="emotion-class"
    >
      <div
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 MuiAlert-root MuiAlert-colorSuccess MuiAlert-outlinedSuccess MuiAlert-outlined emotion-class"
        role="alert"
        style="--Paper-shadow: none;"
      >
        <div
          class="MuiAlert-icon emotion-class"
        >
          <icon-mock
            classname="css-hdmw1m"
            fill="#FFA64C"
            fontsize="inherit"
            size="30"
          />
        </div>
        <div
          class="MuiAlert-message emotion-class"
        >
          <div
            class="emotion-class"
          >
            <div>
              Pending partner actions.
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            Workers' Compensation
          </h5>
          <p
            class="emotion-class"
          >
            Coverage required.
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Rendering 1099p stage for buyer: Processing mixed 1`] = `
.emotion-class {
  max-width: 768px;
  width: 100%;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
}

.emotion-class {
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: var(--spacing-xs);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
  margin-bottom: var(--spacing-m);
  max-width: 768px;
  padding: var(--spacing-m);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
  text-wrap: wrap;
  overflow-wrap: break-word;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background: #FFFFFF;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  font-size: 14px;
  min-height: 65px;
  padding: 0 20px;
  padding: var(--spacing-s) var(--spacing-m);
}

.emotion-class a {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-link-color)!important;
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 20px;
  cursor: pointer;
  font-weight: 600;
}

.emotion-class * {
  fill: var(--text-link-color);
}

.emotion-class {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: var(--spacing-m);
  margin-left: auto;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 30px;
  background-color: #FFFFFF;
  border: 1px solid var(--action-base);
  padding-top: 0;
  padding-right: 14px;
  padding-bottom: 0;
  padding-left: 14px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 28px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 12px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  margin-bottom: var(--spacing-m);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 20px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  vertical-align: middle;
  -webkit-tap-highlight-color: transparent;
  margin-left: -11px;
  margin-right: 16px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  margin: 6px 0;
  font-size: 10px;
}

.emotion-class.Mui-disabled {
  cursor: default;
}

.emotion-class .MuiFormControlLabel-label.Mui-disabled {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class span {
  padding: 0;
  display: block;
}

.emotion-class svg {
  margin: -6px;
}

.emotion-class .radioButtonOnIcon path:nth-of-type(1) {
  fill: #465AB6;
}

.emotion-class .radioButtonOffIcon path:nth-of-type(1) {
  fill: #D1D6ED;
}

.emotion-class:hover .radioButtonOffIcon path:nth-of-type(1) {
  fill: #465AB6;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  padding: 9px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.6);
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class.Mui-disabled {
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class:hover {
  background-color: transparent;
}

.emotion-class {
  cursor: inherit;
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  z-index: 1;
}

.emotion-class {
  margin: 0;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0.00938em;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  margin-left: 6px;
  color: #303757;
  font-weight: 400;
}

.emotion-class {
  max-width: 50%;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  margin-bottom: 5px;
}

.emotion-class>a {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  position: relative;
}

.emotion-class svg {
  fill: #465AB6;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class {
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
  font-size: 12px;
  color: #465AB6;
  vertical-align: middle;
}

.emotion-class {
  position: relative;
}

.emotion-class:hover fieldset {
  border-color: #B4BCE0;
}

.emotion-class fieldset {
  border-color: #D1D6ED;
}

.emotion-class input {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  padding-left: 9px;
  padding-right: 9px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
  vertical-align: top;
  width: 100%;
  margin: 0;
}

.emotion-class .clearButton {
  opacity: 1;
}

@media (pointer: fine) {
  .emotion-class .clearButton {
    opacity: 0;
  }

  .emotion-class:hover .clearButton,
  .emotion-class:focus-within .clearButton {
    opacity: 1;
  }
}

.emotion-class {
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.4375em;
  letter-spacing: 0.00938em;
  color: rgba(0, 0, 0, 0.87);
  box-sizing: border-box;
  position: relative;
  cursor: text;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  border-radius: 4px;
  padding-right: 14px;
  padding: 5px;
  height: 45px;
  background: #FFFFFF;
}

.emotion-class.Mui-disabled {
  color: rgba(0, 0, 0, 0.38);
  cursor: default;
}

.emotion-class:hover .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.87);
}

@media (hover: none) {
  .emotion-class:hover .MuiOutlinedInput-notchedOutline {
    border-color: rgba(0, 0, 0, 0.23);
  }
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-width: 2px;
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #1976d2;
}

.emotion-class.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: #d32f2f;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.26);
}

.emotion-class input {
  font-size: 14px;
  color: #303757;
  font-family: "Open Sans",sans-serif;
}

.emotion-class input::-webkit-input-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input::-moz-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input:-ms-input-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input::placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class .MuiOutlinedInput-notchedOutline {
  border-color: #D1D6ED;
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline,
.emotion-class:hover.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #465AB6;
}

.emotion-class:hover .MuiOutlinedInput-notchedOutline {
  border-color: #B4BCE0;
  outline: none;
}

.emotion-class.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: #FF7F8A;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: #E4E5EB;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::-webkit-input-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::-moz-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline:-ms-input-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::placeholder {
  color: blue;
}

.emotion-class {
  font: inherit;
  letter-spacing: inherit;
  color: currentColor;
  padding: 4px 0 5px;
  border: 0;
  box-sizing: content-box;
  background: none;
  height: 1.4375em;
  margin: 0;
  -webkit-tap-highlight-color: transparent;
  display: block;
  min-width: 0;
  width: 100%;
  -webkit-animation-name: mui-auto-fill-cancel;
  animation-name: mui-auto-fill-cancel;
  -webkit-animation-duration: 10ms;
  animation-duration: 10ms;
  padding: 16.5px 14px;
  padding-right: 0;
  cursor: pointer;
  padding: 9px 12px 10px;
  width: 100%;
}

.emotion-class::-webkit-input-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class::-moz-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class::-ms-input-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class:focus {
  outline: 0;
}

.emotion-class:invalid {
  box-shadow: none;
}

.emotion-class::-webkit-search-decoration {
  -webkit-appearance: none;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-webkit-input-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-moz-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-ms-input-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-webkit-input-placeholder {
  opacity: 0.42;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-moz-placeholder {
  opacity: 0.42;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-ms-input-placeholder {
  opacity: 0.42;
}

.emotion-class.Mui-disabled {
  opacity: 1;
  -webkit-text-fill-color: rgba(0, 0, 0, 0.38);
}

.emotion-class:-webkit-autofill {
  -webkit-animation-duration: 5000s;
  animation-duration: 5000s;
  -webkit-animation-name: mui-auto-fill;
  animation-name: mui-auto-fill;
}

.emotion-class:-webkit-autofill {
  border-radius: inherit;
}

.emotion-class.Mui-disabled {
  color: #A6ABBF;
  -webkit-text-fill-color: #A6ABBF;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  max-height: 2em;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.54);
  margin-left: 8px;
  position: absolute;
  right: 17px;
}

.emotion-class button {
  color: #465AB6;
  margin-right: -12px!important;
}

.emotion-class button.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class button:hover {
  background-color: transparent;
}

.emotion-class button svg {
  width: 20px;
  height: 20px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-right: -12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: 1.5rem;
}

.emotion-class {
  text-align: left;
  position: absolute;
  bottom: 0;
  right: 0;
  top: -5px;
  left: 0;
  margin: 0;
  padding: 0 8px;
  pointer-events: none;
  border-radius: inherit;
  border-style: solid;
  border-width: 1px;
  overflow: hidden;
  min-width: 0%;
  border-color: rgba(0, 0, 0, 0.23);
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 34px;
  min-height: auto;
  border-width: 2px;
  border-style: solid;
  outline: none;
  top: 0;
  -webkit-transition: border-color ease-in-out 0.15s,box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s,box-shadow ease-in-out 0.15s;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class legend {
  display: none;
}

.emotion-class {
  float: unset;
  width: auto;
  overflow: hidden;
  padding: 0;
  line-height: 11px;
  -webkit-transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
  transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
}

.emotion-class {
  z-index: 1300;
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  outline: 0;
  transform-origin: top center;
  transform-origin: bottom center;
}

.emotion-class {
  display: grid;
  grid-auto-columns: max-content auto max-content;
  grid-auto-rows: max-content auto max-content;
}

.emotion-class .MuiPickersLayout-actionBar {
  grid-column: 1/4;
  grid-row: 3;
}

.emotion-class .MuiPickersLayout-toolbar {
  grid-column: 2/4;
  grid-row: 1;
}

.emotion-class .MuiPickersLayout-shortcuts {
  grid-column: 1;
  grid-row: 2/3;
}

.emotion-class {
  grid-column: 2;
  grid-row: 2;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  overflow: hidden;
  width: 320px;
  max-height: 336px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: 0 auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 336px;
}

.emotion-class>div:first-of-type {
  position: absolute;
  width: calc(100% - 40px);
  box-sizing: border-box;
  background: #fff;
  min-height: initial;
  max-height: initial;
  height: 54px;
  padding: 16px 0 6px;
  margin: 0 20px;
}

.emotion-class>div:first-of-type+div {
  margin-top: 54px;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  font-size: 14px;
  color: #465AB6;
  font-weight: 600;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .MuiButtonBase-root {
  display: none;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .PrivatePickersFadeTransitionGroup-root {
  color: #465AB6;
  font-weight: 600;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .PrivatePickersFadeTransitionGroup-root:nth-of-type(2) div {
  margin-right: 0;
}

.emotion-class>div:first-of-type>div:nth-of-type(2) {
  width: 100%;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-top: 12px;
  margin-bottom: 4px;
  padding-left: 24px;
  padding-right: 12px;
  max-height: 40px;
  min-height: 40px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  margin-right: auto;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 500;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0.00938em;
}

.emotion-class {
  display: block;
  position: relative;
}

.emotion-class {
  margin-right: 6px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  padding: 5px;
  font-size: 1.125rem;
  margin-right: auto;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: 1.5rem;
  will-change: transform;
  -webkit-transition: -webkit-transform 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-right: -12px;
  color: #465AB6;
  padding: 5px;
  width: 34px;
  height: 34px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class svg {
  width: 24px;
  height: 24px;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: inherit;
}

.emotion-class {
  width: 24px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-left: -12px;
  color: #465AB6;
  padding: 5px;
  width: 34px;
  height: 34px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class svg {
  width: 24px;
  height: 24px;
}

.emotion-class {
  display: block;
  position: relative;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  margin: 0;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  font-weight: 600;
  font-size: 12px;
  width: 36px;
  height: 40px;
  margin: 0 2px;
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: rgba(0, 0, 0, 0.6);
}

.emotion-class {
  display: block;
  position: relative;
  overflow-x: hidden;
  min-height: 240px;
}

.emotion-class>* {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
}

.emotion-class .MuiPickersSlideTransition-slideEnter-left {
  will-change: transform;
  -webkit-transform: translate(100%);
  -moz-transform: translate(100%);
  -ms-transform: translate(100%);
  transform: translate(100%);
  z-index: 1;
}

.emotion-class .MuiPickersSlideTransition-slideEnter-right {
  will-change: transform;
  -webkit-transform: translate(-100%);
  -moz-transform: translate(-100%);
  -ms-transform: translate(-100%);
  transform: translate(-100%);
  z-index: 1;
}

.emotion-class .MuiPickersSlideTransition-slideEnterActive {
  -webkit-transform: translate(0%);
  -moz-transform: translate(0%);
  -ms-transform: translate(0%);
  transform: translate(0%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
}

.emotion-class .MuiPickersSlideTransition-slideExit {
  -webkit-transform: translate(0%);
  -moz-transform: translate(0%);
  -ms-transform: translate(0%);
  transform: translate(0%);
}

.emotion-class .MuiPickersSlideTransition-slideExitActiveLeft-left {
  will-change: transform;
  -webkit-transform: translate(-100%);
  -moz-transform: translate(-100%);
  -ms-transform: translate(-100%);
  transform: translate(-100%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  z-index: 0;
}

.emotion-class .MuiPickersSlideTransition-slideExitActiveLeft-right {
  will-change: transform;
  -webkit-transform: translate(100%);
  -moz-transform: translate(100%);
  -ms-transform: translate(100%);
  transform: translate(100%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  z-index: 0;
}

.emotion-class {
  overflow: hidden;
}

.emotion-class button {
  margin-right: 2px!important;
}

.emotion-class {
  margin: 2px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  color: rgba(0, 0, 0, 0.6);
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:not(.Mui-selected) {
  border: 1px solid rgba(0, 0, 0, 0.6);
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class:not(.Mui-selected) {
  -webkit-transition: 220ms all;
  transition: 220ms all;
}

.emotion-class:not(.Mui-selected):before {
  content: '';
  display: inline-block;
  border-left: 7px solid transparent;
  border-bottom: 7px solid #465AB6;
  border-top-color: rgba(0, 0, 0, 0.2);
  position: absolute;
  bottom: 4px;
  right: 4px;
}

.emotion-class:not(.Mui-selected):hover:before {
  display: none;
}

<div>
  <div
    class="emotion-class"
    data-testid="Ten99PStageForBuyer"
  >
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            General Liability
          </h5>
          <p
            class="emotion-class"
          >
            Please review the proof of insurance.
          </p>
        </div>
        <div>
          <div
            class="status-preview emotion-class"
          >
            <a
              class="emotion-class"
              href="http://localhost/api/insurances/1/download/"
              target="_blank"
            >
              <icon-mock
                classname="css-vheie4"
              />
              Proof of insurance: General Liability
            </a>
            <div
              class="emotion-class"
            >
              <button
                class=" emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  Preview
                </span>
              </button>
            </div>
          </div>
          <p
            class="emotion-class"
          >
            <div
              class="MuiFormGroup-root MuiRadioGroup-root emotion-class"
              role="radiogroup"
            >
              <div
                data-testid="InputRadioButtonGroupOption_approved"
                style="max-width: 100%;"
              >
                <label
                  class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd emotion-class"
                >
                  <span
                    class="MuiButtonBase-root MuiRadio-root MuiRadio-colorDefault PrivateSwitchBase-root MuiRadio-root MuiRadio-colorDefault Mui-checked MuiRadio-root MuiRadio-colorDefault emotion-class"
                  >
                    <input
                      checked=""
                      class="PrivateSwitchBase-input emotion-class"
                      name=":rg:"
                      type="radio"
                      value="approved"
                    />
                    <span
                      class="radioButtonOnIcon"
                      font-size="medium"
                    >
                      <icon-mock
                        classname="css-1qetkxy"
                        size="32"
                      />
                    </span>
                  </span>
                  <span
                    class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label emotion-class"
                  >
                    <span
                      class="emotion-class"
                    >
                      Approve
                    </span>
                  </span>
                </label>
              </div>
              <div
                data-testid="InputRadioButtonGroupOption_rejected"
                style="max-width: 100%;"
              >
                <label
                  class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd emotion-class"
                >
                  <span
                    class="MuiButtonBase-root MuiRadio-root MuiRadio-colorDefault PrivateSwitchBase-root MuiRadio-root MuiRadio-colorDefault MuiRadio-root MuiRadio-colorDefault emotion-class"
                  >
                    <input
                      class="PrivateSwitchBase-input emotion-class"
                      name=":rg:"
                      type="radio"
                      value="rejected"
                    />
                    <span
                      class="radioButtonOffIcon"
                      font-size="medium"
                    >
                      <icon-mock
                        classname="css-1qetkxy"
                        size="32"
                      />
                    </span>
                  </span>
                  <span
                    class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label emotion-class"
                  >
                    <span
                      class="emotion-class"
                    >
                      Reject
                    </span>
                  </span>
                </label>
              </div>
            </div>
          </p>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <div>
                <span
                  class="emotion-class"
                  data-testid="InputLabel_ConfirmPolicyExpirationDate"
                >
                  Confirm policy expiration date
                </span>
              </div>
              <a
                class=" emotion-class"
                data-testid="InputActionLabel_Button"
                style="text-align: right;"
              >
                <span
                  class="emotion-class"
                >
                  Reset
                </span>
              </a>
            </div>
            <div
              class="emotion-class"
            >
              <div
                class="MuiFormControl-root MuiTextField-root emotion-class"
              >
                <div
                  class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd emotion-class"
                >
                  <input
                    aria-invalid="false"
                    autocomplete="off"
                    class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd emotion-class"
                    id=":rh:"
                    inputmode="text"
                    placeholder="MM/DD/YYYY"
                    type="text"
                    value=""
                  />
                  <div
                    class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium emotion-class"
                  >
                    <button
                      aria-label="Choose date"
                      class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium emotion-class"
                      tabindex="0"
                      type="button"
                    >
                      <svg
                        aria-hidden="true"
                        class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium emotion-class"
                        data-testid="CalendarIcon"
                        focusable="false"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
                        />
                      </svg>
                    </button>
                  </div>
                  <fieldset
                    aria-hidden="true"
                    class="MuiOutlinedInput-notchedOutline emotion-class"
                  >
                    <legend
                      class="emotion-class"
                    >
                      <span
                        aria-hidden="true"
                        class="notranslate"
                      >
                        ​
                      </span>
                    </legend>
                  </fieldset>
                </div>
              </div>
            </div>
            <div
              class="MuiPopper-root MuiPickersPopper-root emotion-class"
              role="dialog"
              style="position: fixed; top: 0px; left: 0px; display: none;"
            >
              <div
                data-testid="sentinelStart"
                tabindex="-1"
              />
              <div
                class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation8 MuiPickersPopper-paper emotion-class"
                style="--Paper-shadow: 0px 5px 5px -3px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12); opacity: 0; transform: scale(0.75, 0.5625); visibility: hidden;"
                tabindex="-1"
              >
                <div
                  class="MuiPickersLayout-root emotion-class"
                >
                  <div
                    class="MuiPickersLayout-contentWrapper emotion-class"
                  >
                    <div
                      class="MuiDateCalendar-root emotion-class"
                    >
                      <div
                        class="MuiPickersCalendarHeader-root emotion-class"
                      >
                        <div
                          aria-live="polite"
                          class="MuiPickersCalendarHeader-labelContainer emotion-class"
                          role="presentation"
                        >
                          <div
                            class="MuiPickersFadeTransitionGroup-root emotion-class"
                          >
                            <div
                              class="MuiPickersCalendarHeader-label emotion-class"
                              id=":rk:-grid-label"
                              style="opacity: 1;"
                            >
                              May 2024
                            </div>
                          </div>
                          <button
                            aria-label="calendar view is open, switch to year view"
                            class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall MuiPickersCalendarHeader-switchViewButton emotion-class"
                            tabindex="0"
                            type="button"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiPickersCalendarHeader-switchViewIcon emotion-class"
                              data-testid="ArrowDropDownIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M7 10l5 5 5-5z"
                              />
                            </svg>
                          </button>
                        </div>
                        <div
                          class="MuiPickersArrowSwitcher-root emotion-class"
                          style="opacity: 1; webkit-transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
                        >
                          <button
                            aria-label="Previous month"
                            class="MuiButtonBase-root Mui-disabled MuiIconButton-root Mui-disabled MuiIconButton-edgeEnd MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-previousIconButton emotion-class"
                            disabled=""
                            tabindex="-1"
                            title="Previous month"
                            type="button"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-leftArrowIcon emotion-class"
                              data-testid="ArrowLeftIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"
                              />
                            </svg>
                          </button>
                          <div
                            class="MuiPickersArrowSwitcher-spacer emotion-class"
                          />
                          <button
                            aria-label="Next month"
                            class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeStart MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-nextIconButton emotion-class"
                            tabindex="0"
                            title="Next month"
                            type="button"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-rightArrowIcon emotion-class"
                              data-testid="ArrowRightIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                      <div
                        class="MuiPickersFadeTransitionGroup-root MuiDateCalendar-viewTransitionContainer emotion-class"
                      >
                        <div
                          style="opacity: 1;"
                        >
                          <div
                            aria-labelledby=":rk:-grid-label"
                            class="MuiDayCalendar-root emotion-class"
                            role="grid"
                          >
                            <div
                              class="MuiDayCalendar-header emotion-class"
                              role="row"
                            >
                              <span
                                aria-label="Sunday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                S
                              </span>
                              <span
                                aria-label="Monday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                M
                              </span>
                              <span
                                aria-label="Tuesday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                T
                              </span>
                              <span
                                aria-label="Wednesday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                W
                              </span>
                              <span
                                aria-label="Thursday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                T
                              </span>
                              <span
                                aria-label="Friday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                F
                              </span>
                              <span
                                aria-label="Saturday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                S
                              </span>
                            </div>
                            <div
                              class="MuiPickersSlideTransition-root MuiDayCalendar-slideTransition emotion-class"
                              role="presentation"
                            >
                              <div
                                class="MuiDayCalendar-monthContainer emotion-class"
                                role="rowgroup"
                              >
                                <div
                                  aria-rowindex="1"
                                  class="MuiDayCalendar-weekContainer emotion-class"
                                  role="row"
                                >
                                  <button
                                    aria-colindex="1"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                    data-timestamp="1714262400000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    28
                                  </button>
                                  <button
                                    aria-colindex="2"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                    data-timestamp="1714348800000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    29
                                  </button>
                                  <button
                                    aria-colindex="3"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                    data-timestamp="1714435200000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    30
                                  </button>
                                  <button
                                    aria-colindex="4"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714521600000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    1
                                  </button>
                                  <button
                                    aria-colindex="5"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714608000000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    2
                                  </button>
                                  <button
                                    aria-colindex="6"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714694400000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    3
                                  </button>
                                  <button
                                    aria-colindex="7"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714780800000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    4
                                  </button>
                                </div>
                                <div
                                  aria-rowindex="2"
                                  class="MuiDayCalendar-weekContainer emotion-class"
                                  role="row"
                                >
                                  <button
                                    aria-colindex="1"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714867200000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    5
                                  </button>
                                  <button
                                    aria-colindex="2"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714953600000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    6
                                  </button>
                                  <button
                                    aria-colindex="3"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715040000000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    7
                                  </button>
                                  <button
                                    aria-colindex="4"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715126400000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    8
                                  </button>
                                  <button
                                    aria-colindex="5"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715212800000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    9
                                  </button>
                                  <button
                                    aria-colindex="6"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715299200000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    10
                                  </button>
                                  <button
                                    aria-colindex="7"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715385600000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    11
                                  </button>
                                </div>
                                <div
                                  aria-rowindex="3"
                                  class="MuiDayCalendar-weekContainer emotion-class"
                                  role="row"
                                >
                                  <button
                                    aria-colindex="1"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715472000000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    12
                                  </button>
                                  <button
                                    aria-colindex="2"
                                    aria-current="date"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin MuiPickersDay-today emotion-class"
                                    data-timestamp="1715558400000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    13
                                  </button>
                                  <button
                                    aria-colindex="3"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715644800000"
                                    role="gridcell"
                                    tabindex="0"
                                    type="button"
                                  >
                                    14
                                  </button>
                                  <button
                                    aria-colindex="4"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715731200000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    15
                                  </button>
                                  <button
                                    aria-colindex="5"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715817600000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    16
                                  </button>
                                  <button
                                    aria-colindex="6"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715904000000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    17
                                  </button>
                                  <button
                                    aria-colindex="7"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715990400000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    18
                                  </button>
                                </div>
                                <div
                                  aria-rowindex="4"
                                  class="MuiDayCalendar-weekContainer emotion-class"
                                  role="row"
                                >
                                  <button
                                    aria-colindex="1"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716076800000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    19
                                  </button>
                                  <button
                                    aria-colindex="2"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716163200000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    20
                                  </button>
                                  <button
                                    aria-colindex="3"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716249600000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    21
                                  </button>
                                  <button
                                    aria-colindex="4"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716336000000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    22
                                  </button>
                                  <button
                                    aria-colindex="5"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716422400000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    23
                                  </button>
                                  <button
                                    aria-colindex="6"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716508800000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    24
                                  </button>
                                  <button
                                    aria-colindex="7"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716595200000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    25
                                  </button>
                                </div>
                                <div
                                  aria-rowindex="5"
                                  class="MuiDayCalendar-weekContainer emotion-class"
                                  role="row"
                                >
                                  <button
                                    aria-colindex="1"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716681600000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    26
                                  </button>
                                  <button
                                    aria-colindex="2"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716768000000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    27
                                  </button>
                                  <button
                                    aria-colindex="3"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716854400000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    28
                                  </button>
                                  <button
                                    aria-colindex="4"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716940800000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    29
                                  </button>
                                  <button
                                    aria-colindex="5"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1717027200000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    30
                                  </button>
                                  <button
                                    aria-colindex="6"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1717113600000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    31
                                  </button>
                                  <button
                                    aria-colindex="7"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                    data-timestamp="1717200000000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    1
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                data-testid="sentinelEnd"
                tabindex="-1"
              />
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            Workers' Compensation
          </h5>
          <p
            class="emotion-class"
          >
            Partner has opted in for this insurance via 1099Policy. The purchase is in progress.
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Rendering 1099p stage for buyer: Processing uploaded insurances 1`] = `
.emotion-class {
  max-width: 768px;
  width: 100%;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
}

.emotion-class {
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: var(--spacing-xs);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
  margin-bottom: var(--spacing-m);
  max-width: 768px;
  padding: var(--spacing-m);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
  text-wrap: wrap;
  overflow-wrap: break-word;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background: #FFFFFF;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  font-size: 14px;
  min-height: 65px;
  padding: 0 20px;
  padding: var(--spacing-s) var(--spacing-m);
}

.emotion-class a {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-link-color)!important;
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 20px;
  cursor: pointer;
  font-weight: 600;
}

.emotion-class * {
  fill: var(--text-link-color);
}

.emotion-class {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: var(--spacing-m);
  margin-left: auto;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 30px;
  background-color: #FFFFFF;
  border: 1px solid var(--action-base);
  padding-top: 0;
  padding-right: 14px;
  padding-bottom: 0;
  padding-left: 14px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 28px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 12px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  margin-bottom: var(--spacing-m);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 20px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  vertical-align: middle;
  -webkit-tap-highlight-color: transparent;
  margin-left: -11px;
  margin-right: 16px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  vertical-align: middle;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  margin: 6px 0;
  font-size: 10px;
}

.emotion-class.Mui-disabled {
  cursor: default;
}

.emotion-class .MuiFormControlLabel-label.Mui-disabled {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class span {
  padding: 0;
  display: block;
}

.emotion-class svg {
  margin: -6px;
}

.emotion-class .radioButtonOnIcon path:nth-of-type(1) {
  fill: #465AB6;
}

.emotion-class .radioButtonOffIcon path:nth-of-type(1) {
  fill: #D1D6ED;
}

.emotion-class:hover .radioButtonOffIcon path:nth-of-type(1) {
  fill: #465AB6;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  padding: 9px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.6);
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class.Mui-disabled {
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class:hover {
  background-color: transparent;
}

.emotion-class {
  cursor: inherit;
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
  z-index: 1;
}

.emotion-class {
  margin: 0;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0.00938em;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  margin-left: 6px;
  color: #303757;
  font-weight: 400;
}

.emotion-class {
  max-width: 50%;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  margin-bottom: 5px;
}

.emotion-class>a {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  position: relative;
}

.emotion-class svg {
  fill: #465AB6;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class {
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
  font-size: 12px;
  color: #465AB6;
  vertical-align: middle;
}

.emotion-class {
  position: relative;
}

.emotion-class:hover fieldset {
  border-color: #B4BCE0;
}

.emotion-class fieldset {
  border-color: #D1D6ED;
}

.emotion-class input {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  padding-left: 9px;
  padding-right: 9px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
  vertical-align: top;
  width: 100%;
  margin: 0;
}

.emotion-class .clearButton {
  opacity: 1;
}

@media (pointer: fine) {
  .emotion-class .clearButton {
    opacity: 0;
  }

  .emotion-class:hover .clearButton,
  .emotion-class:focus-within .clearButton {
    opacity: 1;
  }
}

.emotion-class {
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.4375em;
  letter-spacing: 0.00938em;
  color: rgba(0, 0, 0, 0.87);
  box-sizing: border-box;
  position: relative;
  cursor: text;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  border-radius: 4px;
  padding-right: 14px;
  padding: 5px;
  height: 45px;
  background: #FFFFFF;
}

.emotion-class.Mui-disabled {
  color: rgba(0, 0, 0, 0.38);
  cursor: default;
}

.emotion-class:hover .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.87);
}

@media (hover: none) {
  .emotion-class:hover .MuiOutlinedInput-notchedOutline {
    border-color: rgba(0, 0, 0, 0.23);
  }
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-width: 2px;
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #1976d2;
}

.emotion-class.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: #d32f2f;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.26);
}

.emotion-class input {
  font-size: 14px;
  color: #303757;
  font-family: "Open Sans",sans-serif;
}

.emotion-class input::-webkit-input-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input::-moz-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input:-ms-input-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input::placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class .MuiOutlinedInput-notchedOutline {
  border-color: #D1D6ED;
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline,
.emotion-class:hover.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #465AB6;
}

.emotion-class:hover .MuiOutlinedInput-notchedOutline {
  border-color: #B4BCE0;
  outline: none;
}

.emotion-class.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: #FF7F8A;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: #E4E5EB;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::-webkit-input-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::-moz-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline:-ms-input-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::placeholder {
  color: blue;
}

.emotion-class {
  font: inherit;
  letter-spacing: inherit;
  color: currentColor;
  padding: 4px 0 5px;
  border: 0;
  box-sizing: content-box;
  background: none;
  height: 1.4375em;
  margin: 0;
  -webkit-tap-highlight-color: transparent;
  display: block;
  min-width: 0;
  width: 100%;
  -webkit-animation-name: mui-auto-fill-cancel;
  animation-name: mui-auto-fill-cancel;
  -webkit-animation-duration: 10ms;
  animation-duration: 10ms;
  padding: 16.5px 14px;
  padding-right: 0;
  cursor: pointer;
  padding: 9px 12px 10px;
  width: 100%;
}

.emotion-class::-webkit-input-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class::-moz-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class::-ms-input-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class:focus {
  outline: 0;
}

.emotion-class:invalid {
  box-shadow: none;
}

.emotion-class::-webkit-search-decoration {
  -webkit-appearance: none;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-webkit-input-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-moz-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-ms-input-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-webkit-input-placeholder {
  opacity: 0.42;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-moz-placeholder {
  opacity: 0.42;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-ms-input-placeholder {
  opacity: 0.42;
}

.emotion-class.Mui-disabled {
  opacity: 1;
  -webkit-text-fill-color: rgba(0, 0, 0, 0.38);
}

.emotion-class:-webkit-autofill {
  -webkit-animation-duration: 5000s;
  animation-duration: 5000s;
  -webkit-animation-name: mui-auto-fill;
  animation-name: mui-auto-fill;
}

.emotion-class:-webkit-autofill {
  border-radius: inherit;
}

.emotion-class.Mui-disabled {
  color: #A6ABBF;
  -webkit-text-fill-color: #A6ABBF;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  max-height: 2em;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.54);
  margin-left: 8px;
  position: absolute;
  right: 17px;
}

.emotion-class button {
  color: #465AB6;
  margin-right: -12px!important;
}

.emotion-class button.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class button:hover {
  background-color: transparent;
}

.emotion-class button svg {
  width: 20px;
  height: 20px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-right: -12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: 1.5rem;
}

.emotion-class {
  text-align: left;
  position: absolute;
  bottom: 0;
  right: 0;
  top: -5px;
  left: 0;
  margin: 0;
  padding: 0 8px;
  pointer-events: none;
  border-radius: inherit;
  border-style: solid;
  border-width: 1px;
  overflow: hidden;
  min-width: 0%;
  border-color: rgba(0, 0, 0, 0.23);
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 34px;
  min-height: auto;
  border-width: 2px;
  border-style: solid;
  outline: none;
  top: 0;
  -webkit-transition: border-color ease-in-out 0.15s,box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s,box-shadow ease-in-out 0.15s;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class legend {
  display: none;
}

.emotion-class {
  float: unset;
  width: auto;
  overflow: hidden;
  padding: 0;
  line-height: 11px;
  -webkit-transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
  transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
}

.emotion-class {
  z-index: 1300;
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  outline: 0;
  transform-origin: top center;
  transform-origin: bottom center;
}

.emotion-class {
  display: grid;
  grid-auto-columns: max-content auto max-content;
  grid-auto-rows: max-content auto max-content;
}

.emotion-class .MuiPickersLayout-actionBar {
  grid-column: 1/4;
  grid-row: 3;
}

.emotion-class .MuiPickersLayout-toolbar {
  grid-column: 2/4;
  grid-row: 1;
}

.emotion-class .MuiPickersLayout-shortcuts {
  grid-column: 1;
  grid-row: 2/3;
}

.emotion-class {
  grid-column: 2;
  grid-row: 2;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  overflow: hidden;
  width: 320px;
  max-height: 336px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: 0 auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 336px;
}

.emotion-class>div:first-of-type {
  position: absolute;
  width: calc(100% - 40px);
  box-sizing: border-box;
  background: #fff;
  min-height: initial;
  max-height: initial;
  height: 54px;
  padding: 16px 0 6px;
  margin: 0 20px;
}

.emotion-class>div:first-of-type+div {
  margin-top: 54px;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  font-size: 14px;
  color: #465AB6;
  font-weight: 600;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .MuiButtonBase-root {
  display: none;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .PrivatePickersFadeTransitionGroup-root {
  color: #465AB6;
  font-weight: 600;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .PrivatePickersFadeTransitionGroup-root:nth-of-type(2) div {
  margin-right: 0;
}

.emotion-class>div:first-of-type>div:nth-of-type(2) {
  width: 100%;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-top: 12px;
  margin-bottom: 4px;
  padding-left: 24px;
  padding-right: 12px;
  max-height: 40px;
  min-height: 40px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  margin-right: auto;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 500;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0.00938em;
}

.emotion-class {
  display: block;
  position: relative;
}

.emotion-class {
  margin-right: 6px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  padding: 5px;
  font-size: 1.125rem;
  margin-right: auto;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: 1.5rem;
  will-change: transform;
  -webkit-transition: -webkit-transform 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-right: -12px;
  color: #465AB6;
  padding: 5px;
  width: 34px;
  height: 34px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class svg {
  width: 24px;
  height: 24px;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: inherit;
}

.emotion-class {
  width: 24px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-left: -12px;
  color: #465AB6;
  padding: 5px;
  width: 34px;
  height: 34px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class svg {
  width: 24px;
  height: 24px;
}

.emotion-class {
  display: block;
  position: relative;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  margin: 0;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  font-weight: 600;
  font-size: 12px;
  width: 36px;
  height: 40px;
  margin: 0 2px;
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: rgba(0, 0, 0, 0.6);
}

.emotion-class {
  display: block;
  position: relative;
  overflow-x: hidden;
  min-height: 240px;
}

.emotion-class>* {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
}

.emotion-class .MuiPickersSlideTransition-slideEnter-left {
  will-change: transform;
  -webkit-transform: translate(100%);
  -moz-transform: translate(100%);
  -ms-transform: translate(100%);
  transform: translate(100%);
  z-index: 1;
}

.emotion-class .MuiPickersSlideTransition-slideEnter-right {
  will-change: transform;
  -webkit-transform: translate(-100%);
  -moz-transform: translate(-100%);
  -ms-transform: translate(-100%);
  transform: translate(-100%);
  z-index: 1;
}

.emotion-class .MuiPickersSlideTransition-slideEnterActive {
  -webkit-transform: translate(0%);
  -moz-transform: translate(0%);
  -ms-transform: translate(0%);
  transform: translate(0%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
}

.emotion-class .MuiPickersSlideTransition-slideExit {
  -webkit-transform: translate(0%);
  -moz-transform: translate(0%);
  -ms-transform: translate(0%);
  transform: translate(0%);
}

.emotion-class .MuiPickersSlideTransition-slideExitActiveLeft-left {
  will-change: transform;
  -webkit-transform: translate(-100%);
  -moz-transform: translate(-100%);
  -ms-transform: translate(-100%);
  transform: translate(-100%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  z-index: 0;
}

.emotion-class .MuiPickersSlideTransition-slideExitActiveLeft-right {
  will-change: transform;
  -webkit-transform: translate(100%);
  -moz-transform: translate(100%);
  -ms-transform: translate(100%);
  transform: translate(100%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  z-index: 0;
}

.emotion-class {
  overflow: hidden;
}

.emotion-class button {
  margin-right: 2px!important;
}

.emotion-class {
  margin: 2px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  color: rgba(0, 0, 0, 0.6);
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:not(.Mui-selected) {
  border: 1px solid rgba(0, 0, 0, 0.6);
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class:not(.Mui-selected) {
  -webkit-transition: 220ms all;
  transition: 220ms all;
}

.emotion-class:not(.Mui-selected):before {
  content: '';
  display: inline-block;
  border-left: 7px solid transparent;
  border-bottom: 7px solid #465AB6;
  border-top-color: rgba(0, 0, 0, 0.2);
  position: absolute;
  bottom: 4px;
  right: 4px;
}

.emotion-class:not(.Mui-selected):hover:before {
  display: none;
}

.emotion-class {
  overflow: hidden;
  pointer-events: none;
  position: absolute;
  z-index: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: inherit;
}

<div>
  <div
    class="emotion-class"
    data-testid="Ten99PStageForBuyer"
  >
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            General Liability
          </h5>
          <p
            class="emotion-class"
          >
            Please review the proof of insurance.
          </p>
        </div>
        <div>
          <div
            class="status-preview emotion-class"
          >
            <a
              class="emotion-class"
              href="http://localhost/api/insurances/1/download/"
              target="_blank"
            >
              <icon-mock
                classname="css-vheie4"
              />
              Proof of insurance: General Liability
            </a>
            <div
              class="emotion-class"
            >
              <button
                class=" emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  Preview
                </span>
              </button>
            </div>
          </div>
          <p
            class="emotion-class"
          >
            <div
              class="MuiFormGroup-root MuiRadioGroup-root emotion-class"
              role="radiogroup"
            >
              <div
                data-testid="InputRadioButtonGroupOption_approved"
                style="max-width: 100%;"
              >
                <label
                  class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd emotion-class"
                >
                  <span
                    class="MuiButtonBase-root MuiRadio-root MuiRadio-colorDefault PrivateSwitchBase-root MuiRadio-root MuiRadio-colorDefault Mui-checked MuiRadio-root MuiRadio-colorDefault emotion-class"
                  >
                    <input
                      checked=""
                      class="PrivateSwitchBase-input emotion-class"
                      name=":r0:"
                      type="radio"
                      value="approved"
                    />
                    <span
                      class="radioButtonOnIcon"
                      font-size="medium"
                    >
                      <icon-mock
                        classname="css-1qetkxy"
                        size="32"
                      />
                    </span>
                  </span>
                  <span
                    class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label emotion-class"
                  >
                    <span
                      class="emotion-class"
                    >
                      Approve
                    </span>
                  </span>
                </label>
              </div>
              <div
                data-testid="InputRadioButtonGroupOption_rejected"
                style="max-width: 100%;"
              >
                <label
                  class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd emotion-class"
                >
                  <span
                    class="MuiButtonBase-root MuiRadio-root MuiRadio-colorDefault PrivateSwitchBase-root MuiRadio-root MuiRadio-colorDefault MuiRadio-root MuiRadio-colorDefault emotion-class"
                  >
                    <input
                      class="PrivateSwitchBase-input emotion-class"
                      name=":r0:"
                      type="radio"
                      value="rejected"
                    />
                    <span
                      class="radioButtonOffIcon"
                      font-size="medium"
                    >
                      <icon-mock
                        classname="css-1qetkxy"
                        size="32"
                      />
                    </span>
                  </span>
                  <span
                    class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label emotion-class"
                  >
                    <span
                      class="emotion-class"
                    >
                      Reject
                    </span>
                  </span>
                </label>
              </div>
            </div>
          </p>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <div>
                <span
                  class="emotion-class"
                  data-testid="InputLabel_ConfirmPolicyExpirationDate"
                >
                  Confirm policy expiration date
                </span>
              </div>
              <a
                class=" emotion-class"
                data-testid="InputActionLabel_Button"
                style="text-align: right;"
              >
                <span
                  class="emotion-class"
                >
                  Reset
                </span>
              </a>
            </div>
            <div
              class="emotion-class"
            >
              <div
                class="MuiFormControl-root MuiTextField-root emotion-class"
              >
                <div
                  class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd emotion-class"
                >
                  <input
                    aria-invalid="false"
                    autocomplete="off"
                    class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd emotion-class"
                    id=":r1:"
                    inputmode="text"
                    placeholder="MM/DD/YYYY"
                    type="text"
                    value=""
                  />
                  <div
                    class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium emotion-class"
                  >
                    <button
                      aria-label="Choose date"
                      class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium emotion-class"
                      tabindex="0"
                      type="button"
                    >
                      <svg
                        aria-hidden="true"
                        class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium emotion-class"
                        data-testid="CalendarIcon"
                        focusable="false"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
                        />
                      </svg>
                    </button>
                  </div>
                  <fieldset
                    aria-hidden="true"
                    class="MuiOutlinedInput-notchedOutline emotion-class"
                  >
                    <legend
                      class="emotion-class"
                    >
                      <span
                        aria-hidden="true"
                        class="notranslate"
                      >
                        ​
                      </span>
                    </legend>
                  </fieldset>
                </div>
              </div>
            </div>
            <div
              class="MuiPopper-root MuiPickersPopper-root emotion-class"
              role="dialog"
              style="position: fixed; top: 0px; left: 0px; display: none;"
            >
              <div
                data-testid="sentinelStart"
                tabindex="-1"
              />
              <div
                class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation8 MuiPickersPopper-paper emotion-class"
                style="--Paper-shadow: 0px 5px 5px -3px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12); opacity: 0; transform: scale(0.75, 0.5625); visibility: hidden;"
                tabindex="-1"
              >
                <div
                  class="MuiPickersLayout-root emotion-class"
                >
                  <div
                    class="MuiPickersLayout-contentWrapper emotion-class"
                  >
                    <div
                      class="MuiDateCalendar-root emotion-class"
                    >
                      <div
                        class="MuiPickersCalendarHeader-root emotion-class"
                      >
                        <div
                          aria-live="polite"
                          class="MuiPickersCalendarHeader-labelContainer emotion-class"
                          role="presentation"
                        >
                          <div
                            class="MuiPickersFadeTransitionGroup-root emotion-class"
                          >
                            <div
                              class="MuiPickersCalendarHeader-label emotion-class"
                              id=":r4:-grid-label"
                              style="opacity: 1;"
                            >
                              May 2024
                            </div>
                          </div>
                          <button
                            aria-label="calendar view is open, switch to year view"
                            class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall MuiPickersCalendarHeader-switchViewButton emotion-class"
                            tabindex="0"
                            type="button"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiPickersCalendarHeader-switchViewIcon emotion-class"
                              data-testid="ArrowDropDownIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M7 10l5 5 5-5z"
                              />
                            </svg>
                          </button>
                        </div>
                        <div
                          class="MuiPickersArrowSwitcher-root emotion-class"
                          style="opacity: 1; webkit-transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
                        >
                          <button
                            aria-label="Previous month"
                            class="MuiButtonBase-root Mui-disabled MuiIconButton-root Mui-disabled MuiIconButton-edgeEnd MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-previousIconButton emotion-class"
                            disabled=""
                            tabindex="-1"
                            title="Previous month"
                            type="button"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-leftArrowIcon emotion-class"
                              data-testid="ArrowLeftIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"
                              />
                            </svg>
                          </button>
                          <div
                            class="MuiPickersArrowSwitcher-spacer emotion-class"
                          />
                          <button
                            aria-label="Next month"
                            class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeStart MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-nextIconButton emotion-class"
                            tabindex="0"
                            title="Next month"
                            type="button"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-rightArrowIcon emotion-class"
                              data-testid="ArrowRightIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                      <div
                        class="MuiPickersFadeTransitionGroup-root MuiDateCalendar-viewTransitionContainer emotion-class"
                      >
                        <div
                          style="opacity: 1;"
                        >
                          <div
                            aria-labelledby=":r4:-grid-label"
                            class="MuiDayCalendar-root emotion-class"
                            role="grid"
                          >
                            <div
                              class="MuiDayCalendar-header emotion-class"
                              role="row"
                            >
                              <span
                                aria-label="Sunday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                S
                              </span>
                              <span
                                aria-label="Monday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                M
                              </span>
                              <span
                                aria-label="Tuesday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                T
                              </span>
                              <span
                                aria-label="Wednesday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                W
                              </span>
                              <span
                                aria-label="Thursday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                T
                              </span>
                              <span
                                aria-label="Friday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                F
                              </span>
                              <span
                                aria-label="Saturday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                S
                              </span>
                            </div>
                            <div
                              class="MuiPickersSlideTransition-root MuiDayCalendar-slideTransition emotion-class"
                              role="presentation"
                            >
                              <div
                                class="MuiDayCalendar-monthContainer emotion-class"
                                role="rowgroup"
                              >
                                <div
                                  aria-rowindex="1"
                                  class="MuiDayCalendar-weekContainer emotion-class"
                                  role="row"
                                >
                                  <button
                                    aria-colindex="1"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                    data-timestamp="1714262400000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    28
                                  </button>
                                  <button
                                    aria-colindex="2"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                    data-timestamp="1714348800000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    29
                                  </button>
                                  <button
                                    aria-colindex="3"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                    data-timestamp="1714435200000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    30
                                  </button>
                                  <button
                                    aria-colindex="4"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714521600000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    1
                                  </button>
                                  <button
                                    aria-colindex="5"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714608000000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    2
                                  </button>
                                  <button
                                    aria-colindex="6"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714694400000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    3
                                  </button>
                                  <button
                                    aria-colindex="7"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714780800000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    4
                                  </button>
                                </div>
                                <div
                                  aria-rowindex="2"
                                  class="MuiDayCalendar-weekContainer emotion-class"
                                  role="row"
                                >
                                  <button
                                    aria-colindex="1"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714867200000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    5
                                  </button>
                                  <button
                                    aria-colindex="2"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714953600000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    6
                                  </button>
                                  <button
                                    aria-colindex="3"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715040000000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    7
                                  </button>
                                  <button
                                    aria-colindex="4"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715126400000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    8
                                  </button>
                                  <button
                                    aria-colindex="5"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715212800000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    9
                                  </button>
                                  <button
                                    aria-colindex="6"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715299200000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    10
                                  </button>
                                  <button
                                    aria-colindex="7"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715385600000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    11
                                  </button>
                                </div>
                                <div
                                  aria-rowindex="3"
                                  class="MuiDayCalendar-weekContainer emotion-class"
                                  role="row"
                                >
                                  <button
                                    aria-colindex="1"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715472000000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    12
                                  </button>
                                  <button
                                    aria-colindex="2"
                                    aria-current="date"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin MuiPickersDay-today emotion-class"
                                    data-timestamp="1715558400000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    13
                                  </button>
                                  <button
                                    aria-colindex="3"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715644800000"
                                    role="gridcell"
                                    tabindex="0"
                                    type="button"
                                  >
                                    14
                                    <span
                                      class="MuiTouchRipple-root emotion-class"
                                    />
                                  </button>
                                  <button
                                    aria-colindex="4"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715731200000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    15
                                  </button>
                                  <button
                                    aria-colindex="5"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715817600000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    16
                                  </button>
                                  <button
                                    aria-colindex="6"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715904000000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    17
                                  </button>
                                  <button
                                    aria-colindex="7"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715990400000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    18
                                  </button>
                                </div>
                                <div
                                  aria-rowindex="4"
                                  class="MuiDayCalendar-weekContainer emotion-class"
                                  role="row"
                                >
                                  <button
                                    aria-colindex="1"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716076800000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    19
                                  </button>
                                  <button
                                    aria-colindex="2"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716163200000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    20
                                  </button>
                                  <button
                                    aria-colindex="3"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716249600000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    21
                                  </button>
                                  <button
                                    aria-colindex="4"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716336000000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    22
                                  </button>
                                  <button
                                    aria-colindex="5"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716422400000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    23
                                  </button>
                                  <button
                                    aria-colindex="6"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716508800000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    24
                                  </button>
                                  <button
                                    aria-colindex="7"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716595200000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    25
                                  </button>
                                </div>
                                <div
                                  aria-rowindex="5"
                                  class="MuiDayCalendar-weekContainer emotion-class"
                                  role="row"
                                >
                                  <button
                                    aria-colindex="1"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716681600000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    26
                                  </button>
                                  <button
                                    aria-colindex="2"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716768000000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    27
                                  </button>
                                  <button
                                    aria-colindex="3"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716854400000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    28
                                  </button>
                                  <button
                                    aria-colindex="4"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716940800000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    29
                                  </button>
                                  <button
                                    aria-colindex="5"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1717027200000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    30
                                  </button>
                                  <button
                                    aria-colindex="6"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1717113600000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    31
                                  </button>
                                  <button
                                    aria-colindex="7"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                    data-timestamp="1717200000000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    1
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                data-testid="sentinelEnd"
                tabindex="-1"
              />
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            Workers' Compensation
          </h5>
          <p
            class="emotion-class"
          >
            Please review the proof of insurance.
          </p>
        </div>
        <div>
          <div
            class="status-preview emotion-class"
          >
            <a
              class="emotion-class"
              href="http://localhost/api/insurances/2/download/"
              target="_blank"
            >
              <icon-mock
                classname="css-vheie4"
              />
              Proof of insurance: Workers' Compensation
            </a>
            <div
              class="emotion-class"
            >
              <button
                class=" emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  Preview
                </span>
              </button>
            </div>
          </div>
          <p
            class="emotion-class"
          >
            <div
              class="MuiFormGroup-root MuiRadioGroup-root emotion-class"
              role="radiogroup"
            >
              <div
                data-testid="InputRadioButtonGroupOption_approved"
                style="max-width: 100%;"
              >
                <label
                  class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd emotion-class"
                >
                  <span
                    class="MuiButtonBase-root MuiRadio-root MuiRadio-colorDefault PrivateSwitchBase-root MuiRadio-root MuiRadio-colorDefault Mui-checked MuiRadio-root MuiRadio-colorDefault emotion-class"
                  >
                    <input
                      checked=""
                      class="PrivateSwitchBase-input emotion-class"
                      name=":r8:"
                      type="radio"
                      value="approved"
                    />
                    <span
                      class="radioButtonOnIcon"
                      font-size="medium"
                    >
                      <icon-mock
                        classname="css-1qetkxy"
                        size="32"
                      />
                    </span>
                  </span>
                  <span
                    class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label emotion-class"
                  >
                    <span
                      class="emotion-class"
                    >
                      Approve
                    </span>
                  </span>
                </label>
              </div>
              <div
                data-testid="InputRadioButtonGroupOption_rejected"
                style="max-width: 100%;"
              >
                <label
                  class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd emotion-class"
                >
                  <span
                    class="MuiButtonBase-root MuiRadio-root MuiRadio-colorDefault PrivateSwitchBase-root MuiRadio-root MuiRadio-colorDefault MuiRadio-root MuiRadio-colorDefault emotion-class"
                  >
                    <input
                      class="PrivateSwitchBase-input emotion-class"
                      name=":r8:"
                      type="radio"
                      value="rejected"
                    />
                    <span
                      class="radioButtonOffIcon"
                      font-size="medium"
                    >
                      <icon-mock
                        classname="css-1qetkxy"
                        size="32"
                      />
                    </span>
                  </span>
                  <span
                    class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label emotion-class"
                  >
                    <span
                      class="emotion-class"
                    >
                      Reject
                    </span>
                  </span>
                </label>
              </div>
            </div>
          </p>
          <div
            class="emotion-class"
          >
            <div
              class="emotion-class"
            >
              <div>
                <span
                  class="emotion-class"
                  data-testid="InputLabel_ConfirmPolicyExpirationDate"
                >
                  Confirm policy expiration date
                </span>
              </div>
              <a
                class=" emotion-class"
                data-testid="InputActionLabel_Button"
                style="text-align: right;"
              >
                <span
                  class="emotion-class"
                >
                  Reset
                </span>
              </a>
            </div>
            <div
              class="emotion-class"
            >
              <div
                class="MuiFormControl-root MuiTextField-root emotion-class"
              >
                <div
                  class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd emotion-class"
                >
                  <input
                    aria-invalid="false"
                    autocomplete="off"
                    class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd emotion-class"
                    id=":r9:"
                    inputmode="text"
                    placeholder="MM/DD/YYYY"
                    type="text"
                    value=""
                  />
                  <div
                    class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium emotion-class"
                  >
                    <button
                      aria-label="Choose date"
                      class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium emotion-class"
                      tabindex="0"
                      type="button"
                    >
                      <svg
                        aria-hidden="true"
                        class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium emotion-class"
                        data-testid="CalendarIcon"
                        focusable="false"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
                        />
                      </svg>
                    </button>
                  </div>
                  <fieldset
                    aria-hidden="true"
                    class="MuiOutlinedInput-notchedOutline emotion-class"
                  >
                    <legend
                      class="emotion-class"
                    >
                      <span
                        aria-hidden="true"
                        class="notranslate"
                      >
                        ​
                      </span>
                    </legend>
                  </fieldset>
                </div>
              </div>
            </div>
            <div
              class="MuiPopper-root MuiPickersPopper-root emotion-class"
              role="dialog"
              style="position: fixed; top: 0px; left: 0px; display: none;"
            >
              <div
                data-testid="sentinelStart"
                tabindex="-1"
              />
              <div
                class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation8 MuiPickersPopper-paper emotion-class"
                style="--Paper-shadow: 0px 5px 5px -3px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12); opacity: 0; transform: scale(0.75, 0.5625); visibility: hidden;"
                tabindex="-1"
              >
                <div
                  class="MuiPickersLayout-root emotion-class"
                >
                  <div
                    class="MuiPickersLayout-contentWrapper emotion-class"
                  >
                    <div
                      class="MuiDateCalendar-root emotion-class"
                    >
                      <div
                        class="MuiPickersCalendarHeader-root emotion-class"
                      >
                        <div
                          aria-live="polite"
                          class="MuiPickersCalendarHeader-labelContainer emotion-class"
                          role="presentation"
                        >
                          <div
                            class="MuiPickersFadeTransitionGroup-root emotion-class"
                          >
                            <div
                              class="MuiPickersCalendarHeader-label emotion-class"
                              id=":rc:-grid-label"
                              style="opacity: 1;"
                            >
                              May 2024
                            </div>
                          </div>
                          <button
                            aria-label="calendar view is open, switch to year view"
                            class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall MuiPickersCalendarHeader-switchViewButton emotion-class"
                            tabindex="0"
                            type="button"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiPickersCalendarHeader-switchViewIcon emotion-class"
                              data-testid="ArrowDropDownIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M7 10l5 5 5-5z"
                              />
                            </svg>
                          </button>
                        </div>
                        <div
                          class="MuiPickersArrowSwitcher-root emotion-class"
                          style="opacity: 1; webkit-transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
                        >
                          <button
                            aria-label="Previous month"
                            class="MuiButtonBase-root Mui-disabled MuiIconButton-root Mui-disabled MuiIconButton-edgeEnd MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-previousIconButton emotion-class"
                            disabled=""
                            tabindex="-1"
                            title="Previous month"
                            type="button"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-leftArrowIcon emotion-class"
                              data-testid="ArrowLeftIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"
                              />
                            </svg>
                          </button>
                          <div
                            class="MuiPickersArrowSwitcher-spacer emotion-class"
                          />
                          <button
                            aria-label="Next month"
                            class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeStart MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-nextIconButton emotion-class"
                            tabindex="0"
                            title="Next month"
                            type="button"
                          >
                            <svg
                              aria-hidden="true"
                              class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-rightArrowIcon emotion-class"
                              data-testid="ArrowRightIcon"
                              focusable="false"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                      <div
                        class="MuiPickersFadeTransitionGroup-root MuiDateCalendar-viewTransitionContainer emotion-class"
                      >
                        <div
                          style="opacity: 1;"
                        >
                          <div
                            aria-labelledby=":rc:-grid-label"
                            class="MuiDayCalendar-root emotion-class"
                            role="grid"
                          >
                            <div
                              class="MuiDayCalendar-header emotion-class"
                              role="row"
                            >
                              <span
                                aria-label="Sunday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                S
                              </span>
                              <span
                                aria-label="Monday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                M
                              </span>
                              <span
                                aria-label="Tuesday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                T
                              </span>
                              <span
                                aria-label="Wednesday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                W
                              </span>
                              <span
                                aria-label="Thursday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                T
                              </span>
                              <span
                                aria-label="Friday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                F
                              </span>
                              <span
                                aria-label="Saturday"
                                class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                                role="columnheader"
                              >
                                S
                              </span>
                            </div>
                            <div
                              class="MuiPickersSlideTransition-root MuiDayCalendar-slideTransition emotion-class"
                              role="presentation"
                            >
                              <div
                                class="MuiDayCalendar-monthContainer emotion-class"
                                role="rowgroup"
                              >
                                <div
                                  aria-rowindex="1"
                                  class="MuiDayCalendar-weekContainer emotion-class"
                                  role="row"
                                >
                                  <button
                                    aria-colindex="1"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                    data-timestamp="1714262400000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    28
                                  </button>
                                  <button
                                    aria-colindex="2"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                    data-timestamp="1714348800000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    29
                                  </button>
                                  <button
                                    aria-colindex="3"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                    data-timestamp="1714435200000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    30
                                  </button>
                                  <button
                                    aria-colindex="4"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714521600000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    1
                                  </button>
                                  <button
                                    aria-colindex="5"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714608000000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    2
                                  </button>
                                  <button
                                    aria-colindex="6"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714694400000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    3
                                  </button>
                                  <button
                                    aria-colindex="7"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714780800000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    4
                                  </button>
                                </div>
                                <div
                                  aria-rowindex="2"
                                  class="MuiDayCalendar-weekContainer emotion-class"
                                  role="row"
                                >
                                  <button
                                    aria-colindex="1"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714867200000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    5
                                  </button>
                                  <button
                                    aria-colindex="2"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1714953600000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    6
                                  </button>
                                  <button
                                    aria-colindex="3"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715040000000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    7
                                  </button>
                                  <button
                                    aria-colindex="4"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715126400000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    8
                                  </button>
                                  <button
                                    aria-colindex="5"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715212800000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    9
                                  </button>
                                  <button
                                    aria-colindex="6"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715299200000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    10
                                  </button>
                                  <button
                                    aria-colindex="7"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715385600000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    11
                                  </button>
                                </div>
                                <div
                                  aria-rowindex="3"
                                  class="MuiDayCalendar-weekContainer emotion-class"
                                  role="row"
                                >
                                  <button
                                    aria-colindex="1"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715472000000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    12
                                  </button>
                                  <button
                                    aria-colindex="2"
                                    aria-current="date"
                                    aria-selected="false"
                                    class="MuiButtonBase-root Mui-disabled MuiPickersDay-root Mui-disabled MuiPickersDay-dayWithMargin MuiPickersDay-today emotion-class"
                                    data-timestamp="1715558400000"
                                    disabled=""
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    13
                                  </button>
                                  <button
                                    aria-colindex="3"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715644800000"
                                    role="gridcell"
                                    tabindex="0"
                                    type="button"
                                  >
                                    14
                                  </button>
                                  <button
                                    aria-colindex="4"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715731200000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    15
                                  </button>
                                  <button
                                    aria-colindex="5"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715817600000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    16
                                  </button>
                                  <button
                                    aria-colindex="6"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715904000000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    17
                                  </button>
                                  <button
                                    aria-colindex="7"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1715990400000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    18
                                  </button>
                                </div>
                                <div
                                  aria-rowindex="4"
                                  class="MuiDayCalendar-weekContainer emotion-class"
                                  role="row"
                                >
                                  <button
                                    aria-colindex="1"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716076800000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    19
                                  </button>
                                  <button
                                    aria-colindex="2"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716163200000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    20
                                  </button>
                                  <button
                                    aria-colindex="3"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716249600000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    21
                                  </button>
                                  <button
                                    aria-colindex="4"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716336000000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    22
                                  </button>
                                  <button
                                    aria-colindex="5"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716422400000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    23
                                  </button>
                                  <button
                                    aria-colindex="6"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716508800000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    24
                                  </button>
                                  <button
                                    aria-colindex="7"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716595200000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    25
                                  </button>
                                </div>
                                <div
                                  aria-rowindex="5"
                                  class="MuiDayCalendar-weekContainer emotion-class"
                                  role="row"
                                >
                                  <button
                                    aria-colindex="1"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716681600000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    26
                                  </button>
                                  <button
                                    aria-colindex="2"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716768000000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    27
                                  </button>
                                  <button
                                    aria-colindex="3"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716854400000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    28
                                  </button>
                                  <button
                                    aria-colindex="4"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1716940800000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    29
                                  </button>
                                  <button
                                    aria-colindex="5"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1717027200000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    30
                                  </button>
                                  <button
                                    aria-colindex="6"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                                    data-timestamp="1717113600000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    31
                                  </button>
                                  <button
                                    aria-colindex="7"
                                    aria-selected="false"
                                    class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                                    data-timestamp="1717200000000"
                                    role="gridcell"
                                    tabindex="-1"
                                    type="button"
                                  >
                                    1
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                data-testid="sentinelEnd"
                tabindex="-1"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Rendering 1099p stage for buyer: Rejected uploaded insurance 1`] = `
.emotion-class {
  max-width: 768px;
  width: 100%;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
}

.emotion-class {
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: var(--spacing-xs);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
  margin-bottom: var(--spacing-m);
  max-width: 768px;
  padding: var(--spacing-m);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
  text-wrap: wrap;
  overflow-wrap: break-word;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background: #FFFFFF;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  font-size: 14px;
  min-height: 65px;
  padding: 0 20px;
  padding: var(--spacing-s) var(--spacing-m);
}

.emotion-class a {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-link-color)!important;
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 20px;
  cursor: pointer;
  font-weight: 600;
}

.emotion-class * {
  fill: var(--text-link-color);
}

.emotion-class {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: var(--spacing-m);
  margin-left: auto;
}

.emotion-class {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  color: var(--statuses-status-inactive);
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  font-size: 12px;
  font-weight: 600;
  margin-left: auto;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 30px;
  background-color: #FFFFFF;
  border: 1px solid var(--action-base);
  padding-top: 0;
  padding-right: 14px;
  padding-bottom: 0;
  padding-left: 14px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 28px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 12px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  margin-top: 20px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 18px;
  line-height: 30px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 15px;
  margin-top: 20px;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 30px;
  background-color: var(--action-base);
  border: 0;
  padding-top: 0;
  padding-right: 15px;
  padding-bottom: 0;
  padding-left: 15px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: #FFFFFF;
}

.emotion-class span {
  line-height: 30px;
}

.emotion-class:hover {
  -webkit-filter: brightness(1.15);
  filter: brightness(1.15);
}

.emotion-class {
  color: #FFFFFF;
  font-size: 12px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 30px;
  background-color: #FFFFFF;
  border: 1px solid var(--action-negative);
  padding-top: 0;
  padding-right: 14px;
  padding-bottom: 0;
  padding-left: 14px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-negative);
}

.emotion-class span {
  line-height: 28px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-negative);
  font-size: 12px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

<div>
  <div
    class="emotion-class"
    data-testid="Ten99PStageForBuyer"
  >
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            General Liability
          </h5>
          <p
            class="emotion-class"
          />
        </div>
        <div>
          <div
            class="status-preview emotion-class"
          >
            <a
              class="emotion-class"
              href="http://localhost/api/insurances/1/download/"
              target="_blank"
            >
              <icon-mock
                classname="css-vheie4"
              />
              Proof of insurance: General Liability
            </a>
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                color="var(--statuses-status-inactive)"
              >
                <icon-mock
                  classname="css-1s425qw"
                  fill="var(--statuses-status-inactive)"
                  size="24"
                />
                Rejected
              </div>
              <button
                class=" emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  Preview
                </span>
              </button>
            </div>
          </div>
          <div>
            <span
              class="emotion-class"
            >
              Rejection reason
            </span>
            <p
              class="emotion-class"
            >
              It is not possible to read file
            </p>
          </div>
        </div>
      </div>
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            Workers' Compensation
          </h5>
          <p
            class="emotion-class"
          />
        </div>
        <div>
          <div
            class="status-preview emotion-class"
          >
            <a
              class="emotion-class"
              href="http://localhost/api/insurances/2/download/"
              target="_blank"
            >
              <icon-mock
                classname="css-vheie4"
              />
              Proof of insurance: Workers' Compensation
            </a>
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                color="var(--statuses-status-inactive)"
              >
                <icon-mock
                  classname="css-1s425qw"
                  fill="var(--statuses-status-inactive)"
                  size="24"
                />
                Rejected
              </div>
              <button
                class=" emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  Preview
                </span>
              </button>
            </div>
          </div>
          <div>
            <span
              class="emotion-class"
            >
              Rejection reason
            </span>
            <p
              class="emotion-class"
            >
              Missing insurance dates on the document
            </p>
          </div>
        </div>
      </div>
    </div>
    <div
      class="emotion-class"
    >
      <h4
        class="emotion-class"
      >
        What can you do next?
      </h4>
      <div
        class="emotion-class"
      >
        <button
          class=" emotion-class"
        >
          <span
            class="emotion-class"
          >
            Move to retry
          </span>
        </button>
        <button
          class=" emotion-class"
        >
          <span
            class="emotion-class"
          >
            Disqualify
          </span>
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`Rendering 1099p stage for buyer: Reupload allowed with rejected and approved insurances 1`] = `
.emotion-class {
  max-width: 768px;
  width: 100%;
}

.emotion-class {
  margin-bottom: 20px;
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  background-color: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 6px 16px;
  color: rgb(30, 70, 32);
  border: 1px solid #4caf50;
}

.emotion-class .MuiAlert-icon {
  color: #2e7d32;
}

.emotion-class.MuiAlert-root {
  width: 100%;
  max-width: 100%;
  color: #303757;
  background-color: #FFF6ED;
  border-color: #FFD2A6;
  font-family: "Open Sans",sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  padding: 15px 10px 15px 20px;
  margin: 0;
  box-sizing: border-box;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class.MuiAlert-root .MuiAlert-icon {
  padding: 0;
  margin: 0 4px 0 0;
}

.emotion-class.MuiAlert-root .MuiAlert-message {
  padding: 0;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.emotion-class.MuiAlert-root a {
  color: #465AB6;
}

@media (max-width: 768px) {
  .emotion-class.MuiAlert-root {
    padding: 20px;
  }
}

.emotion-class {
  margin-right: 12px;
  padding: 7px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 22px;
  opacity: 0.9;
}

.emotion-class {
  padding: 8px 0;
  min-width: 0;
  overflow: auto;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class>:first-of-type {
  padding: 0;
}

@media (max-width: 768px) {
  .emotion-class {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: start;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: start;
  }

  .emotion-class div {
    margin: 15px 0 0 0;
  }
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
}

.emotion-class {
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: var(--spacing-xs);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
  margin-bottom: var(--spacing-m);
  max-width: 768px;
  padding: var(--spacing-m);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
  text-wrap: wrap;
  overflow-wrap: break-word;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background: #FFFFFF;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  font-size: 14px;
  min-height: 65px;
  padding: 0 20px;
  padding: var(--spacing-s) var(--spacing-m);
}

.emotion-class a {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-link-color)!important;
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 20px;
  cursor: pointer;
  font-weight: 600;
}

.emotion-class * {
  fill: var(--text-link-color);
}

.emotion-class {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: var(--spacing-m);
  margin-left: auto;
}

.emotion-class {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  color: var(--statuses-status-active);
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  font-size: 12px;
  font-weight: 600;
  margin-left: auto;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 30px;
  background-color: #FFFFFF;
  border: 1px solid var(--action-base);
  padding-top: 0;
  padding-right: 14px;
  padding-bottom: 0;
  padding-left: 14px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 28px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 12px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  color: var(--statuses-status-inactive);
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  font-size: 12px;
  font-weight: 600;
  margin-left: auto;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

<div>
  <div
    class="emotion-class"
    data-testid="Ten99PStageForBuyer"
  >
    <div
      class="emotion-class"
    >
      <div
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 MuiAlert-root MuiAlert-colorSuccess MuiAlert-outlinedSuccess MuiAlert-outlined emotion-class"
        role="alert"
        style="--Paper-shadow: none;"
      >
        <div
          class="MuiAlert-icon emotion-class"
        >
          <icon-mock
            classname="css-hdmw1m"
            fill="#FFA64C"
            fontsize="inherit"
            size="30"
          />
        </div>
        <div
          class="MuiAlert-message emotion-class"
        >
          <div
            class="emotion-class"
          >
            <div>
              Rejected - awaiting reupload
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            General Liability
          </h5>
          <p
            class="emotion-class"
          />
        </div>
        <div>
          <div
            class="status-preview emotion-class"
          >
            <a
              class="emotion-class"
              href="http://localhost/api/insurances/1/download/"
              target="_blank"
            >
              <icon-mock
                classname="css-vheie4"
              />
              Proof of insurance: General Liability
            </a>
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                color="var(--statuses-status-active)"
              >
                <icon-mock
                  classname="css-8sv2oy"
                  fill="var(--statuses-status-active)"
                  size="24"
                />
                Approved
              </div>
              <button
                class=" emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  Preview
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            Workers' Compensation
          </h5>
          <p
            class="emotion-class"
          />
        </div>
        <div>
          <div
            class="status-preview emotion-class"
          >
            <a
              class="emotion-class"
              href="http://localhost/api/insurances/2/download/"
              target="_blank"
            >
              <icon-mock
                classname="css-vheie4"
              />
              Proof of insurance: Workers' Compensation
            </a>
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                color="var(--statuses-status-inactive)"
              >
                <icon-mock
                  classname="css-1s425qw"
                  fill="var(--statuses-status-inactive)"
                  size="24"
                />
                Rejected
              </div>
              <button
                class=" emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  Preview
                </span>
              </button>
            </div>
          </div>
          <div>
            <span
              class="emotion-class"
            >
              Rejection reason
            </span>
            <p
              class="emotion-class"
            >
              Missing insurance dates on the document
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Rendering 1099p stage for buyer: Reupload allowed with rejected insurances 1`] = `
.emotion-class {
  max-width: 768px;
  width: 100%;
}

.emotion-class {
  margin-bottom: 20px;
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.43;
  letter-spacing: 0.01071em;
  background-color: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 6px 16px;
  color: rgb(30, 70, 32);
  border: 1px solid #4caf50;
}

.emotion-class .MuiAlert-icon {
  color: #2e7d32;
}

.emotion-class.MuiAlert-root {
  width: 100%;
  max-width: 100%;
  color: #303757;
  background-color: #FFF6ED;
  border-color: #FFD2A6;
  font-family: "Open Sans",sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  padding: 15px 10px 15px 20px;
  margin: 0;
  box-sizing: border-box;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-class.MuiAlert-root .MuiAlert-icon {
  padding: 0;
  margin: 0 4px 0 0;
}

.emotion-class.MuiAlert-root .MuiAlert-message {
  padding: 0;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.emotion-class.MuiAlert-root a {
  color: #465AB6;
}

@media (max-width: 768px) {
  .emotion-class.MuiAlert-root {
    padding: 20px;
  }
}

.emotion-class {
  margin-right: 12px;
  padding: 7px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  font-size: 22px;
  opacity: 0.9;
}

.emotion-class {
  padding: 8px 0;
  min-width: 0;
  overflow: auto;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class>:first-of-type {
  padding: 0;
}

@media (max-width: 768px) {
  .emotion-class {
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: start;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: start;
  }

  .emotion-class div {
    margin: 15px 0 0 0;
  }
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
}

.emotion-class {
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: var(--spacing-xs);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: var(--spacing-m);
  margin-bottom: var(--spacing-m);
  max-width: 768px;
  padding: var(--spacing-m);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 16px;
  line-height: 25px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
  text-wrap: wrap;
  overflow-wrap: break-word;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background: #FFFFFF;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  font-size: 14px;
  min-height: 65px;
  padding: 0 20px;
  padding: var(--spacing-s) var(--spacing-m);
}

.emotion-class a {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-link-color)!important;
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 20px;
  cursor: pointer;
  font-weight: 600;
}

.emotion-class * {
  fill: var(--text-link-color);
}

.emotion-class {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: var(--spacing-m);
  margin-left: auto;
}

.emotion-class {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  color: var(--statuses-status-inactive);
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  font-size: 12px;
  font-weight: 600;
  margin-left: auto;
}

.emotion-class {
  position: relative;
  outline: none;
  white-space: nowrap;
  border-radius: 4px;
  height: 30px;
  background-color: #FFFFFF;
  border: 1px solid var(--action-base);
  padding-top: 0;
  padding-right: 14px;
  padding-bottom: 0;
  padding-left: 14px;
  min-width: auto;
  box-sizing: border-box;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class.mouseDown,
.emotion-class.mouseDown:hover {
  -webkit-filter: none;
  filter: none;
}

.emotion-class svg,
.emotion-class span {
  vertical-align: middle;
  display: inline-block;
}

.emotion-class svg {
  fill: var(--action-base);
}

.emotion-class span {
  line-height: 28px;
}

.emotion-class:hover {
  -webkit-filter: brightness(0.98);
  filter: brightness(0.98);
}

.emotion-class {
  color: var(--action-base);
  font-size: 12px;
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

<div>
  <div
    class="emotion-class"
    data-testid="Ten99PStageForBuyer"
  >
    <div
      class="emotion-class"
    >
      <div
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation0 MuiAlert-root MuiAlert-colorSuccess MuiAlert-outlinedSuccess MuiAlert-outlined emotion-class"
        role="alert"
        style="--Paper-shadow: none;"
      >
        <div
          class="MuiAlert-icon emotion-class"
        >
          <icon-mock
            classname="css-hdmw1m"
            fill="#FFA64C"
            fontsize="inherit"
            size="30"
          />
        </div>
        <div
          class="MuiAlert-message emotion-class"
        >
          <div
            class="emotion-class"
          >
            <div>
              Rejected - awaiting reupload
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            General Liability
          </h5>
          <p
            class="emotion-class"
          />
        </div>
        <div>
          <div
            class="status-preview emotion-class"
          >
            <a
              class="emotion-class"
              href="http://localhost/api/insurances/1/download/"
              target="_blank"
            >
              <icon-mock
                classname="css-vheie4"
              />
              Proof of insurance: General Liability
            </a>
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                color="var(--statuses-status-inactive)"
              >
                <icon-mock
                  classname="css-1s425qw"
                  fill="var(--statuses-status-inactive)"
                  size="24"
                />
                Rejected
              </div>
              <button
                class=" emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  Preview
                </span>
              </button>
            </div>
          </div>
          <div>
            <span
              class="emotion-class"
            >
              Rejection reason
            </span>
            <p
              class="emotion-class"
            >
              It is not possible to read file
            </p>
          </div>
        </div>
      </div>
      <div
        class="emotion-class"
      >
        <div>
          <h5
            class="emotion-class"
          >
            Workers' Compensation
          </h5>
          <p
            class="emotion-class"
          />
        </div>
        <div>
          <div
            class="status-preview emotion-class"
          >
            <a
              class="emotion-class"
              href="http://localhost/api/insurances/2/download/"
              target="_blank"
            >
              <icon-mock
                classname="css-vheie4"
              />
              Proof of insurance: Workers' Compensation
            </a>
            <div
              class="emotion-class"
            >
              <div
                class="emotion-class"
                color="var(--statuses-status-inactive)"
              >
                <icon-mock
                  classname="css-1s425qw"
                  fill="var(--statuses-status-inactive)"
                  size="24"
                />
                Rejected
              </div>
              <button
                class=" emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  Preview
                </span>
              </button>
            </div>
          </div>
          <div>
            <span
              class="emotion-class"
            >
              Rejection reason
            </span>
            <p
              class="emotion-class"
            >
              Missing insurance dates on the document
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
