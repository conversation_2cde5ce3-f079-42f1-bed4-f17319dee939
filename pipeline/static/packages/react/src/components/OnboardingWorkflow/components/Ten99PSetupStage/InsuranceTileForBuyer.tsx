import React, { useEffect, useState } from 'react';
import ten99Pi18n from '../../../../i18n/en/components/Ten99P/Ten99P.json';
import i18n from '../../../../i18n/i18n';
import {
  BuyerDecisionFormData,
  CoverageType,
  OwnInsuranceStatus,
  Ten99InsuranceStatus,
} from '../../../../services/Ten99P/Ten99P';
import { HeaderLarge, Label } from '../../../../styles/typography';
import InputDatePicker from '../../../FormElements/InputDatePicker/InputDatePicker';
import InputRadioButtonGroup from '../../../FormElements/InputRadioButtonGroup/InputRadioButtonGroup';
import InputText from '../../../FormElements/InputText/InputText';
import { InsuranceStatusTile } from './InsuranceStatusTile';
import {
  DatePickerContainer,
  DecisionContainer,
  Description,
  InsuranceTileContainer,
} from './InsuranceTileForBuyer.styles';

i18n.addResourceBundle('en', 'Ten99P', ten99Pi18n);

export interface BuyerFormErrors {
  expiration_date?: string[];
  rejection_reason?: string[];
}

const getMinInsuranceApprovalDate = () => {
  const today = new Date();
  return new Date(today.setDate(today.getDate() + 1));
};

const getStatusDescriptionText = (
  status: OwnInsuranceStatus | Ten99InsuranceStatus,
  hasOwnInsurance: boolean | null
): string | null => {
  if (status === 'not_started' || status === 'not_completed') {
    return i18n.t('Ten99P:BuyerStatusDescription.NotStarted');
  }

  if (status === 'processing') {
    return i18n.t(
      `Ten99P:BuyerStatusDescription.${
        hasOwnInsurance ? 'ProcessingOwn' : 'ProcessingPurchase'
      }`
    );
  }

  if (status === 'active' || status === 'eligible') {
    return i18n.t('Ten99P:BuyerStatusDescription.Active');
  }

  return null;
};

const shouldShowStatusPreview = (
  status: OwnInsuranceStatus | Ten99InsuranceStatus,
  hasOwnInsurance: boolean | null
): boolean => {
  if (hasOwnInsurance && status === 'processing') {
    return true;
  }

  return ['active', 'approved', 'eligible', 'rejected'].includes(status);
};

const BuyerDecisionForm = ({
  errors,
  onChange,
}: {
  errors?: BuyerFormErrors;
  onChange: (decision: BuyerDecisionFormData) => void;
}) => {
  const [decision, setDecision] = useState<BuyerDecisionFormData>({
    end_date: null,
    status: 'approved',
  });
  const [expirationDateErrors, setExpirationDateErrors] = useState(
    errors?.expiration_date || []
  );
  const [rejectionReasonErrors, setRejectionReasonErrors] = useState(
    errors?.rejection_reason || []
  );

  useEffect(() => {
    onChange(decision);
  }, [decision]);

  useEffect(() => {
    setExpirationDateErrors(errors?.expiration_date || []);
    setRejectionReasonErrors(errors?.rejection_reason || []);
  }, [errors]);

  const onDecisionChange = (value: BuyerDecisionFormData['status']): void => {
    if (value === 'approved') {
      setDecision({
        end_date: null,
        status: value,
      });
    } else {
      setDecision({
        rejection_reason: null,
        status: value,
      });
    }
    setExpirationDateErrors([]);
    setRejectionReasonErrors([]);
  };
  const onExpirationDateChange = (value: string): void => {
    const selectedDate = new Date(value);
    const currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);

    if (selectedDate < currentDate) {
      setExpirationDateErrors([i18n.t('Ten99P:Error.NoPastDate')]);
    } else {
      setDecision({
        ...decision,
        end_date: value,
        status: 'approved',
      });
      setExpirationDateErrors([]);
    }
  };
  const onRejectionReasonChange = (value: string): void => {
    setDecision({
      rejection_reason: value,
      status: 'rejected',
    });
    setRejectionReasonErrors([]);
  };

  return (
    <React.Fragment>
      <DecisionContainer>
        <InputRadioButtonGroup
          defaultValue={decision.status}
          isHorizontal={true}
          options={[
            {
              label: i18n.t('Ten99P:BuyerForm.Approve'),
              value: 'approved',
            },
            {
              label: i18n.t('Ten99P:BuyerForm.Reject'),
              value: 'rejected',
            },
          ]}
          onChange={onDecisionChange}
        />
      </DecisionContainer>
      {decision.status === 'approved' && (
        <DatePickerContainer>
          <InputDatePicker
            errors={expirationDateErrors}
            label={i18n.t('Ten99P:BuyerForm.ExpirationDate')}
            onChange={onExpirationDateChange}
            minAvailableDate={getMinInsuranceApprovalDate()}
          />
        </DatePickerContainer>
      )}
      {decision.status === 'rejected' && (
        <div>
          <InputText
            errors={rejectionReasonErrors}
            label={i18n.t('Ten99P:BuyerForm.RejectionReason')}
            onChange={onRejectionReasonChange}
            maxLength={100}
          />
        </div>
      )}
    </React.Fragment>
  );
};

const InsuranceTileForBuyer = ({
  errors,
  hasOwnInsurance,
  onChange,
  documentId,
  rejectionReason,
  status,
  type,
}: {
  errors?: BuyerFormErrors;
  hasOwnInsurance?: boolean;
  onChange: (decision: BuyerDecisionFormData) => void;
  documentId: number | null;
  rejectionReason?: string;
  status: OwnInsuranceStatus | Ten99InsuranceStatus;
  type: CoverageType;
}) => {
  const isDecisionFormVisible = hasOwnInsurance && status === 'processing';
  const isRejectionReasonVisible = rejectionReason && status === 'rejected';

  return (
    <InsuranceTileContainer>
      <div>
        <HeaderLarge>{i18n.t(`Ten99P:Types.${type}`)}</HeaderLarge>
        <Description>
          {getStatusDescriptionText(status, hasOwnInsurance)}
        </Description>
      </div>
      {shouldShowStatusPreview(status, hasOwnInsurance) && (
        <div>
          <InsuranceStatusTile
            hasOwnInsurance={hasOwnInsurance}
            documentId={documentId}
            status={status}
            type={type}
          />
          {isDecisionFormVisible && (
            <BuyerDecisionForm errors={errors} onChange={onChange} />
          )}
          {isRejectionReasonVisible && (
            <div>
              <Label>{i18n.t('Ten99P:BuyerForm.RejectionReason')}</Label>
              <Description>{rejectionReason}</Description>
            </div>
          )}
        </div>
      )}
    </InsuranceTileContainer>
  );
};

export default InsuranceTileForBuyer;
