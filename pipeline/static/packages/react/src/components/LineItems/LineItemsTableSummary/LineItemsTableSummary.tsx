import Currency from '@Worksuite/Features/Core/components/Currency/Currency';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Label } from '../../../styles/typography';
import { generateTestId } from '../../../utils/test.utils';
import InputSelect from '../../FormElements/InputSelect/InputSelect';
import { Invoice } from '../LineItems';
import { showDeduction } from '../LineItems.helpers';
import {
  LineItemNote,
  LineItemSummary,
  LineItemSummaryDivider,
  LineItemSummaryLabel,
  LineItemSummaryValue,
  LineItemTotal,
  LineItemsNoteWrapper,
  LineItemsSummary,
  LineItemsSummaryWrapper,
  TableInputSelectWrapper,
} from '../LineItems.styles';

interface LineItemsTableSummaryProps {
  invoice: Invoice;
  currencies: string[];
  editInvoice: (fieldName: string, value: Invoice[keyof Invoice]) => void;
  hasTaxRates: boolean;
  readOnly: boolean;
  defaultCurrency: string;
  hasInvoiceError: (fieldName: string) => boolean;
  testId?: string;
  isNoteVisible: boolean;
}

export const LineItemsTableSummary = ({
  invoice,
  currencies,
  editInvoice,
  hasTaxRates,
  readOnly,
  defaultCurrency,
  hasInvoiceError,
  testId = generateTestId('LineItemsTableSummary')['data-testid'],
  isNoteVisible,
}: LineItemsTableSummaryProps) => {
  const { t } = useTranslation('LineItems');
  const optionsCurrencies = useMemo(
    () => currencies.map((currency) => ({ key: currency, text: currency })),
    [currencies]
  );

  return (
    <LineItemsSummaryWrapper data-testid={testId}>
      <LineItemsNoteWrapper {...generateTestId('Note', 'InvoiceDetails')}>
        {isNoteVisible && (
          <div>
            <Label>{t('LineItemsTable.Footer.Note')}</Label>
            <LineItemNote>
              {invoice.note || t('LineItemsTable.Footer.NoNote')}
            </LineItemNote>
          </div>
        )}
      </LineItemsNoteWrapper>
      <LineItemsSummary hasError={hasInvoiceError('total')}>
        {showDeduction(readOnly, invoice.total_deductions) && (
          <LineItemSummary>
            <LineItemSummaryLabel>
              {t('LineItemsTable.Footer.TotalDeductions')}
            </LineItemSummaryLabel>
            <LineItemSummaryValue
              {...generateTestId('TotalDeductions', 'LineItemsTable')}
            >
              <Currency
                currency={invoice.currency}
                value={invoice.total_deductions ?? 0}
                variant="raw"
              />
            </LineItemSummaryValue>
          </LineItemSummary>
        )}
        {hasTaxRates && (
          <LineItemSummary>
            <LineItemSummaryLabel>
              {t('LineItemsTable.Footer.Subtotal')}
            </LineItemSummaryLabel>
            <LineItemSummaryValue
              {...generateTestId('Subtotal', 'LineItemsTable')}
            >
              <Currency
                currency={invoice.currency}
                value={invoice.total_amount_without_tax}
                variant="raw"
              />
            </LineItemSummaryValue>
          </LineItemSummary>
        )}
        {hasTaxRates && (
          <LineItemSummary>
            <LineItemSummaryLabel>
              {t('LineItemsTable.Footer.TotalTax')}
            </LineItemSummaryLabel>
            <LineItemSummaryValue
              {...generateTestId('TotalTax', 'LineItemsTable')}
            >
              <Currency
                currency={invoice.currency}
                value={invoice.total_tax}
                variant="raw"
              />
            </LineItemSummaryValue>
          </LineItemSummary>
        )}
        {(hasTaxRates || showDeduction(readOnly, invoice.total_deductions)) && (
          <LineItemSummaryDivider />
        )}
        <LineItemSummary>
          {!readOnly && (
            <TableInputSelectWrapper>
              <InputSelect
                defaultValue={invoice.currency ?? defaultCurrency}
                options={optionsCurrencies}
                onChange={({ key }) => editInvoice('currency', key.toString())}
              />
            </TableInputSelectWrapper>
          )}
          <LineItemTotal>{t('LineItemsTable.Footer.Total')}</LineItemTotal>
          <LineItemTotal {...generateTestId('Total', 'LineItemsTable')}>
            <Currency
              currency={invoice.currency}
              value={invoice.total}
              variant="raw"
            />
          </LineItemTotal>
        </LineItemSummary>
      </LineItemsSummary>
    </LineItemsSummaryWrapper>
  );
};
