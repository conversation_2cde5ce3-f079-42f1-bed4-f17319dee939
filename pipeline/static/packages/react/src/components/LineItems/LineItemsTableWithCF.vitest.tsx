import { composeStories } from '@storybook/react';
import * as stories from './LineItemsTableWithCF/LineItemsTableWithCF.stories';
import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import React from 'react';

const composedStories = composeStories(stories);

describe('LineItemsTableWithCF', () => {
  test.each(Object.entries(composedStories))(
    'snapshot should match: %s',
    async (key, StoryItem) => {
      const { container } = render(<StoryItem />);

      await screen.findByText('Payment_Line_Items_Item', { exact: false });
      if (!key.includes('Empty')) {
        await screen.findAllByText('s.png');
      }

      expect(container).toMatchSnapshot();
    }
  );
});
