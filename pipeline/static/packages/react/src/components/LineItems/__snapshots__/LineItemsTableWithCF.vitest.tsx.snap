// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`LineItemsTableWithCF > snapshot should match: EditEmpty 1`] = `
.emotion-class {
  padding: 0 var(--spacing-m) var(--spacing-m);
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: var(--spacing-xs) var(--spacing-xs) 0 var(--spacing-xs);
  overflow-x: auto;
}

.emotion-class {
  display: table;
  width: 100%;
  border-spacing: 0 var(--spacing-s);
}

.emotion-class {
  display: table-row;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  padding: var(--spacing-s) var(--spacing-m);
  font-weight: 600;
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border: none;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class:first-of-type {
  border: none;
}

.emotion-class:last-of-type {
  border: none;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  position: relative;
  background-color: transparent;
  padding: 0;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class a {
  display: block;
  padding: var(--spacing-l);
  text-align: center;
}

.emotion-class {
  position: relative;
}

.emotion-class svg {
  fill: #465AB6;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class {
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
  font-size: 13px;
  color: #465AB6;
  vertical-align: middle;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-top: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: 0 0 var(--spacing-xs) var(--spacing-xs);
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  width: 100%;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  height: 1px;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  border-radius: var(--spacing-s-plus);
  background-color: var(--borders-encapsulation-separator-divider);
}

.emotion-class>div {
  display: inline-block;
  margin-right: var(--spacing-s-plus);
}

.emotion-class select {
  font-size: var(--font-size-s);
  width: auto;
  padding: 0;
  min-height: auto;
  outline-width: 0;
  color: var(--actions-basic-actions-action-base);
  font-weight: 600;
  border: 0;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  fill: #000000;
  width: 28px;
  height: 28px;
  fill-rule: evenodd;
  vertical-align: middle;
  position: absolute;
  top: 10px;
  right: 5px;
  fill: #465AB6;
  pointer-events: none;
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 24px;
  line-height: 30px;
  color: var(--glyphs-basic-glyphs-base);
}

<div>
  <div
    style="margin: 40px;"
  >
    <div>
      <div
        class="emotion-class"
      >
        <table
          cellpadding="0"
          cellspacing="0"
          class="emotion-class"
          data-testid="LineItemsTableWithCf"
        >
          <tbody>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                style="text-align: left; width: 100%;"
              >
                <span
                  class="emotion-class"
                >
                  Payment_Line_Items_Item
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: left; white-space: nowrap; min-width: 128px;"
              >
                <span
                  class="emotion-class"
                >
                  Category
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 28px;"
              >
                <span
                  class="emotion-class"
                >
                  Qty
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 88px;"
              >
                <span
                  class="emotion-class"
                >
                  Price
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: center; min-width: 160px;"
              >
                <span
                  class="emotion-class"
                >
                  Deduction
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 140px;"
              >
                <span
                  class="emotion-class"
                >
                  Amount
                </span>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <span
                  class="emotion-class"
                >
                  <a
                    class=" emotion-class"
                  >
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m24%209.5c16.025%209.5%209.5%2016.025%209.5%2024c9.5%2031.975%2016.025%2038.5%2024%2038.5c31.975%2038.5%2038.5%2031.975%2038.5%2024c38.5%2016.025%2031.975%209.5%2024%209.5zm22.4513%2022.4513h15.6371v25.5487h22.4513v32.3628h25.5486v25.5487h32.3628v22.4513h25.5486v15.6372h22.4513v22.4513z'/%3e%3c/svg%3e
                      classname="css-12ecxzt"
                      size="20"
                      style="margin: 0px 5px 0px -2px;"
                    />
                    <span
                      class="emotion-class"
                    >
                      Add new item
                    </span>
                  </a>
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        class="emotion-class"
        data-testid="LineItemsTableWithCf_Footer"
      >
        <div
          class="emotion-class"
          data-testid="InvoiceDetails_Note"
        />
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
            >
              Total Deductions
            </span>
            <p
              class="emotion-class"
              data-testid="LineItemsTable_TotalDeductions"
            >
              0
               
              YEN
            </p>
          </div>
          <div
            class="emotion-class"
          />
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
            >
              <div
                class="emotion-class"
              >
                <select
                  class="emotion-class"
                >
                  <option
                    data-testid="SelectOption_Aud"
                    value="AUD"
                  >
                    AUD
                  </option>
                  <option
                    data-testid="SelectOption_Cny"
                    value="CNY"
                  >
                    CNY
                  </option>
                  <option
                    data-testid="SelectOption_Gbp"
                    value="GBP"
                  >
                    GBP
                  </option>
                  <option
                    data-testid="SelectOption_Jpy"
                    value="JPY"
                  >
                    JPY
                  </option>
                  <option
                    data-testid="SelectOption_Yen"
                    value="YEN"
                  >
                    YEN
                  </option>
                  <option
                    data-testid="SelectOption_Usd"
                    value="USD"
                  >
                    USD
                  </option>
                </select>
                <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                  class="emotion-class"
                  size="28"
                />
              </div>
            </span>
            <p
              class="emotion-class"
            >
              Total
            </p>
            <p
              class="emotion-class"
              data-testid="LineItemsTable_Total"
            >
              0
               
              YEN
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`LineItemsTableWithCF > snapshot should match: EditEmptyWithTax 1`] = `
.emotion-class {
  padding: 0 var(--spacing-m) var(--spacing-m);
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: var(--spacing-xs) var(--spacing-xs) 0 var(--spacing-xs);
  overflow-x: auto;
}

.emotion-class {
  display: table;
  width: 100%;
  border-spacing: 0 var(--spacing-s);
}

.emotion-class {
  display: table-row;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  padding: var(--spacing-s) var(--spacing-m);
  font-weight: 600;
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border: none;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class:first-of-type {
  border: none;
}

.emotion-class:last-of-type {
  border: none;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  position: relative;
  background-color: transparent;
  padding: 0;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class a {
  display: block;
  padding: var(--spacing-l);
  text-align: center;
}

.emotion-class {
  position: relative;
}

.emotion-class svg {
  fill: #465AB6;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class {
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
  font-size: 13px;
  color: #465AB6;
  vertical-align: middle;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-top: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: 0 0 var(--spacing-xs) var(--spacing-xs);
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  width: 100%;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  height: 1px;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  border-radius: var(--spacing-s-plus);
  background-color: var(--borders-encapsulation-separator-divider);
}

.emotion-class>div {
  display: inline-block;
  margin-right: var(--spacing-s-plus);
}

.emotion-class select {
  font-size: var(--font-size-s);
  width: auto;
  padding: 0;
  min-height: auto;
  outline-width: 0;
  color: var(--actions-basic-actions-action-base);
  font-weight: 600;
  border: 0;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  fill: #000000;
  width: 28px;
  height: 28px;
  fill-rule: evenodd;
  vertical-align: middle;
  position: absolute;
  top: 10px;
  right: 5px;
  fill: #465AB6;
  pointer-events: none;
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 24px;
  line-height: 30px;
  color: var(--glyphs-basic-glyphs-base);
}

<div>
  <div
    style="margin: 40px;"
  >
    <div>
      <div
        class="emotion-class"
      >
        <table
          cellpadding="0"
          cellspacing="0"
          class="emotion-class"
          data-testid="LineItemsTableWithCf"
        >
          <tbody>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                style="text-align: left; width: 100%;"
              >
                <span
                  class="emotion-class"
                >
                  Payment_Line_Items_Item
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: left; white-space: nowrap; min-width: 128px;"
              >
                <span
                  class="emotion-class"
                >
                  Category
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 28px;"
              >
                <span
                  class="emotion-class"
                >
                  Qty
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 88px;"
              >
                <span
                  class="emotion-class"
                >
                  Price
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: center; min-width: 160px;"
              >
                <span
                  class="emotion-class"
                >
                  Deduction
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; white-space: nowrap; min-width: 80px;"
              >
                <span
                  class="emotion-class"
                >
                  Tax
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 140px;"
              >
                <span
                  class="emotion-class"
                >
                  Amount
                </span>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <span
                  class="emotion-class"
                >
                  <a
                    class=" emotion-class"
                  >
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m24%209.5c16.025%209.5%209.5%2016.025%209.5%2024c9.5%2031.975%2016.025%2038.5%2024%2038.5c31.975%2038.5%2038.5%2031.975%2038.5%2024c38.5%2016.025%2031.975%209.5%2024%209.5zm22.4513%2022.4513h15.6371v25.5487h22.4513v32.3628h25.5486v25.5487h32.3628v22.4513h25.5486v15.6372h22.4513v22.4513z'/%3e%3c/svg%3e
                      classname="css-12ecxzt"
                      size="20"
                      style="margin: 0px 5px 0px -2px;"
                    />
                    <span
                      class="emotion-class"
                    >
                      Add new item
                    </span>
                  </a>
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        class="emotion-class"
        data-testid="LineItemsTableWithCf_Footer"
      >
        <div
          class="emotion-class"
          data-testid="InvoiceDetails_Note"
        />
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
            >
              Total Deductions
            </span>
            <p
              class="emotion-class"
              data-testid="LineItemsTable_TotalDeductions"
            >
              0
               
              YEN
            </p>
          </div>
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
            >
              Subtotal (excl. tax)
            </span>
            <p
              class="emotion-class"
              data-testid="LineItemsTable_Subtotal"
            >
              0
               
              YEN
            </p>
          </div>
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
            >
              Total Tax
            </span>
            <p
              class="emotion-class"
              data-testid="LineItemsTable_TotalTax"
            >
              0
               
              YEN
            </p>
          </div>
          <div
            class="emotion-class"
          />
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
            >
              <div
                class="emotion-class"
              >
                <select
                  class="emotion-class"
                >
                  <option
                    data-testid="SelectOption_Aud"
                    value="AUD"
                  >
                    AUD
                  </option>
                  <option
                    data-testid="SelectOption_Cny"
                    value="CNY"
                  >
                    CNY
                  </option>
                  <option
                    data-testid="SelectOption_Gbp"
                    value="GBP"
                  >
                    GBP
                  </option>
                  <option
                    data-testid="SelectOption_Jpy"
                    value="JPY"
                  >
                    JPY
                  </option>
                  <option
                    data-testid="SelectOption_Yen"
                    value="YEN"
                  >
                    YEN
                  </option>
                  <option
                    data-testid="SelectOption_Usd"
                    value="USD"
                  >
                    USD
                  </option>
                </select>
                <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                  class="emotion-class"
                  size="28"
                />
              </div>
            </span>
            <p
              class="emotion-class"
            >
              Total
            </p>
            <p
              class="emotion-class"
              data-testid="LineItemsTable_Total"
            >
              0
               
              YEN
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`LineItemsTableWithCF > snapshot should match: EditPrefilled 1`] = `
.emotion-class {
  padding: 0 var(--spacing-m) var(--spacing-m);
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: var(--spacing-xs) var(--spacing-xs) 0 var(--spacing-xs);
  overflow-x: auto;
}

.emotion-class {
  display: table;
  width: 100%;
  border-spacing: 0 var(--spacing-s);
}

.emotion-class {
  display: table-row;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  padding: var(--spacing-s) var(--spacing-m);
  font-weight: 600;
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border: none;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class:first-of-type {
  border: none;
}

.emotion-class:last-of-type {
  border: none;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  display: table-row;
  cursor: pointer;
}

.emotion-class:hover td,
.emotion-class:focus-within td {
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  position: relative;
  padding: var(--spacing-xs);
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class:focus-within.emotion-class::before {
  content: "";
  position: absolute;
  border: 1px solid var(--borders-canvas-borders-canvas-border-neutral);
  left: 0;
  right: 0;
  bottom: 0;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  border-radius: calc(var(--spacing-xs) / 2);
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  border: 0;
  outline-width: 0;
  padding: var(--spacing-s-plus);
  background-color: transparent;
  text-align: right;
}

.emotion-class::-webkit-input-placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class::-moz-placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class:-ms-input-placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class::placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-table-fills-table-fill-error)!important;
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  position: relative;
  padding: var(--spacing-xs);
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class:focus-within.emotion-class::before {
  content: "";
  position: absolute;
  border: 1px solid var(--borders-canvas-borders-canvas-border-neutral);
  left: 0;
  right: 0;
  bottom: 0;
}

.emotion-class select {
  border: 0;
  outline-width: 0;
  background-color: transparent;
  padding-left: var(--spacing-xs);
  padding-right: var(--spacing-xs);
  min-height: 40px;
  min-width: 90px;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  fill: #000000;
  width: 28px;
  height: 28px;
  fill-rule: evenodd;
  vertical-align: middle;
  position: absolute;
  top: 10px;
  right: 5px;
  fill: #465AB6;
  pointer-events: none;
}

.emotion-class {
  padding: 0 10px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  visibility: hidden;
  max-height: 0;
  white-space: nowrap;
  overflow-x: hidden;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  border-radius: calc(var(--spacing-xs) / 2);
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  border: 0;
  outline-width: 0;
  padding: var(--spacing-s-plus);
  background-color: transparent;
  text-align: right;
}

.emotion-class::-webkit-input-placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class::-moz-placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class:-ms-input-placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class::placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class::-webkit-input-placeholder {
  color: var(--glyphs-tables-glyphs-table-placeholder);
}

.emotion-class::-moz-placeholder {
  color: var(--glyphs-tables-glyphs-table-placeholder);
}

.emotion-class:-ms-input-placeholder {
  color: var(--glyphs-tables-glyphs-table-placeholder);
}

.emotion-class::placeholder {
  color: var(--glyphs-tables-glyphs-table-placeholder);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-right: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-right-radius: var(--spacing-xs);
  border-bottom-right-radius: var(--spacing-xs);
  position: relative;
  padding: var(--spacing-xs);
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class:focus-within.emotion-class::before {
  content: "";
  position: absolute;
  border: 1px solid var(--borders-canvas-borders-canvas-border-neutral);
  left: 0;
  right: 0;
  bottom: 0;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  background-color: transparent!important;
  cursor: pointer;
  position: absolute;
  border-width: 0!important;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class div {
  display: inline-block;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-table-fills-table-fill-error)!important;
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  position: relative;
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-danger)!important;
  padding: 10px;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #303757;
  display: inline-block;
  font-weight: 400;
  margin-right: 15px;
  padding-left: 25px;
  position: relative;
  padding-top: 2px;
  max-width: 400px;
}

.emotion-class svg {
  left: 0;
  top: 0;
  position: absolute;
}

.emotion-class a {
  color: #A6ABBF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class a:hover {
  color: #465AB6;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #303757;
  display: inline-block;
  font-weight: 400;
  margin-right: 15px;
  position: relative;
  padding-top: 2px;
  max-width: 400px;
}

.emotion-class svg {
  left: 0;
  top: 0;
  position: absolute;
}

.emotion-class a {
  color: #A6ABBF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class a:hover {
  color: #465AB6;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #303757;
  display: inline-block;
  font-weight: 400;
  margin-right: 15px;
  padding-left: 25px;
  position: relative;
  padding-top: 2px;
}

.emotion-class svg {
  left: 0;
  top: 0;
  position: absolute;
}

.emotion-class a {
  color: #A6ABBF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class a:hover {
  color: #465AB6;
}

.emotion-class>a,
.emotion-class>span {
  position: relative;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
  padding-right: 5px;
  margin-right: 5px;
}

.emotion-class>a span,
.emotion-class>span span {
  color: #A6ABBF;
  position: absolute;
  top: 0;
  right: 0;
}

.emotion-class {
  fill: #A6ABBF;
  width: 24px;
  height: 24px;
  fill-rule: evenodd;
  vertical-align: middle;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 35px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: 20px;
  height: 100%;
  overflow: hidden;
}

.emotion-class svg {
  margin: -5px -6px 0 -6px;
}

.emotion-class {
  fill: #E4E5EB;
  width: 29px;
  height: 29px;
  fill-rule: evenodd;
  vertical-align: middle;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  position: relative;
  background-color: transparent;
  padding: 0;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class a {
  display: block;
  padding: var(--spacing-l);
  text-align: center;
}

.emotion-class {
  position: relative;
}

.emotion-class svg {
  fill: #465AB6;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class {
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
  font-size: 13px;
  color: #465AB6;
  vertical-align: middle;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-top: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: 0 0 var(--spacing-xs) var(--spacing-xs);
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  width: 100%;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  height: 1px;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  border-radius: var(--spacing-s-plus);
  background-color: var(--borders-encapsulation-separator-divider);
}

.emotion-class>div {
  display: inline-block;
  margin-right: var(--spacing-s-plus);
}

.emotion-class select {
  font-size: var(--font-size-s);
  width: auto;
  padding: 0;
  min-height: auto;
  outline-width: 0;
  color: var(--actions-basic-actions-action-base);
  font-weight: 600;
  border: 0;
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 24px;
  line-height: 30px;
  color: var(--glyphs-basic-glyphs-base);
}

<div>
  <div
    style="margin: 40px;"
  >
    <div>
      <div
        class="emotion-class"
      >
        <table
          cellpadding="0"
          cellspacing="0"
          class="emotion-class"
          data-testid="LineItemsTableWithCf"
        >
          <tbody>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                style="text-align: left; width: 100%;"
              >
                <span
                  class="emotion-class"
                >
                  Payment_Line_Items_Item
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: left; white-space: nowrap; min-width: 128px;"
              >
                <span
                  class="emotion-class"
                >
                  Category
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 28px;"
              >
                <span
                  class="emotion-class"
                >
                  Qty
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 88px;"
              >
                <span
                  class="emotion-class"
                >
                  Price
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: center; min-width: 160px;"
              >
                <span
                  class="emotion-class"
                >
                  Deduction
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 140px;"
              >
                <span
                  class="emotion-class"
                >
                  Amount
                </span>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  maxlength="250"
                  placeholder="Add Invoice item..."
                  style="text-align: left;"
                  value="Line item #1"
                />
              </td>
              <td
                class="emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  <div
                    class="emotion-class"
                  >
                    <select
                      class="emotion-class"
                    >
                      <option
                        value=""
                      >
                        Select category...
                      </option>
                      <option
                        data-testid="SelectOption_1"
                        value="1"
                      >
                        Root Category 1
                      </option>
                      <option
                        data-testid="SelectOption_2"
                        value="2"
                      >
                        Work
                      </option>
                      <option
                        data-testid="SelectOption_3"
                        value="3"
                      >
                        Child Category 1.2
                      </option>
                      <option
                        data-testid="SelectOption_4"
                        value="4"
                      >
                        Root Category 2
                      </option>
                      <option
                        data-testid="SelectOption_5"
                        value="5"
                      >
                        Long category Name 33333333333
                      </option>
                    </select>
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                      class="emotion-class"
                      size="28"
                    />
                  </div>
                </span>
                <div
                  class="emotion-class"
                />
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0"
                  value="1.1111"
                />
                <div
                  class="emotion-class"
                >
                  1.11110
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0"
                  value="122"
                />
                <div
                  class="emotion-class"
                >
                  122
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <div
                  class="emotion-class"
                >
                  <input
                    class="emotion-class"
                    placeholder="0"
                    value="50"
                  />
                  <span
                    class="emotion-class"
                  >
                    <div
                      class="emotion-class"
                    >
                      <select
                        class="emotion-class"
                      >
                        <option
                          data-testid="SelectOption_"
                          value="%"
                        >
                          %
                        </option>
                        <option
                          data-testid="SelectOption_Yen"
                          value="YEN"
                        >
                          YEN
                        </option>
                      </select>
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                        class="emotion-class"
                        size="28"
                      />
                    </div>
                  </span>
                </div>
                <div
                  class="emotion-class"
                >
                  50
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0 YEN"
                  style="font-weight: 600;"
                  value="68"
                />
              </td>
              <td
                class="LineItemContent_Delete emotion-class"
                data-testid="LineItemEdit_Delete"
              >
                <div>
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m21.5%2024l13%2032.5l15.5%2035l24%2026.5l32.5%2035l35%2032.5l26.5%2024l35%2015.5l32.5%2013l24%2021.5l15.5%2013l13%2015.5l21.5%2024z'/%3e%3c/svg%3e
                    classname="css-1lp8zdr"
                    fill="var(--statuses-status-initiated)"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <div
                  style="display: flex; justify-content: center;"
                >
                  <div
                    style="width: 100%;"
                  >
                    <p
                      class="emotion-class"
                      style="color: #FF7F8A; font-weight: 600;"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9.5%2023.9167c9.5%2031.8917%2016.025%2038.4167%2024%2038.4167c31.975%2038.4167%2038.5%2031.8917%2038.5%2023.9167c38.5%2015.9417%2031.975%209.41669%2024%209.41669c16.025%209.41669%209.5%2015.9417%209.5%2023.9167zm26.4997%2014.4167v17.9167l25.4997%2025.9167h22.4997l21.4997%2017.9167v14.4167h26.4997zm22.2242%2028.6411c21.7412%2029.1241%2021.4997%2029.7159%2021.4997%2030.4167c21.4997%2031.1175%2021.7412%2031.7093%2022.2242%2032.1923c22.7071%2032.6752%2023.299%2032.9167%2023.9997%2032.9167c24.7005%2032.9167%2025.2924%2032.6752%2025.7753%2032.1923c26.2583%2031.7093%2026.4997%2031.1175%2026.4997%2030.4167c26.4997%2029.7159%2026.2583%2029.1241%2025.7753%2028.6411c25.2924%2028.1582%2024.7005%2027.9167%2023.9997%2027.9167c23.299%2027.9167%2022.7071%2028.1582%2022.2242%2028.6411z'%20/%3e%3c/svg%3e
                        classname="css-1qygcm5"
                        fill="#FF7F8A"
                        size="24"
                      />
                      Fields required.
                    </p>
                    <span
                      class="emotion-class"
                      title="text line"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37%2019h11v22h37v19zm30.5%2025.5h11v28.5h30.5v25.5z'/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      text1
                    </span>
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                      title="text area"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m11%2012.5h37v15.5h11v12.5zm37%2019h11v22h37v19zm37%2025.5h11v28.5h37v25.5zm30.5%2032h11v35h30.5v32z'/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin vehicula erat venenatis odio pellentesque dignissim vitae ac nunc. Donec molestie dignissim lorem, quis volutpat sapien pretium ut. Phasellus sit amet nulla finibus, hendrerit urna id, lacinia est. Donec vitae neque id odio laoreet venenatis quis quis est. Ut congue elementum scelerisque. Vivamus ullamcorper libero eu consectetur efficitur. Morbi eu lobortis mauris. Mauris molestie, dui in scelerisque egestas, ante elit faucibus risus, sit amet dictum massa sem eu dolor. Cras feugiat sagittis lorem et viverra.

Nam in est lectus. Integer nec elit nulla. Sed diam elit, lobortis quis tincidunt nec, convallis in mi. Phasellus finibus id justo et sodales. Maecenas molestie velit eget nisi commodo, id molestie mi iaculis. Nulla hendrerit mi purus, ultricies ultricies nisi convallis at. Donec non maximus justo. Donec auctor tellus id nisi vehicula gravida ac non justo. Praesent nec volutpat mauris. Vivamus vel felis ut lorem aliquam imperdiet ut eu risus. Donec et consequat dui, vulputate hendrerit justo. Aliquam erat volutpat. Mauris eu odio ante.

Duis sit amet mi at ligula rutrum eleifend id et risus. Quisque dignissim nec orci semper dictum. Etiam malesuada eu orci ut tincidunt. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Nam a ex augue. Aliquam erat volutpat. Quisque elit lorem, mollis eu metus iaculis, faucibus viverra mi. Curabitur egestas sem et lacinia elementum. Maecenas eu sollicitudin nunc.
                    </span>
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                      title="dropdown"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m10%2035c10%2036.6569%2011.3431%2038%2013%2038h35c36.6569%2038%2038%2036.6569%2038%2035v13c38%2011.3431%2036.6569%2010%2035%2010h13c11.3431%2010%2010%2011.3431%2010%2013v35zm35%2013v35h13v13h35zm28%2025.5l24%2032.5l20%2025.5h28zm24%2015.5l28%2022.5h20l24%2015.5z'/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      Option F
                    </span>
                    <span
                      class="emotion-class"
                      title="Date"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m27%2010.5v8.5h31v10.5h34.5c36.1569%2010.5%2037.5%2011.8431%2037.5%2013.5v34.5c37.5%2036.1569%2036.1569%2037.5%2034.5%2037.5h13.5c11.8431%2037.5%2010.5%2036.1569%2010.5%2034.5v13.5c10.5%2011.8431%2011.8431%2010.5%2013.5%2010.5h17.5v8.5h21.5v10.5h27zm34.5%2034.5h13.5v18h34.5v34.5zm31.5%2025v32h22.5v25h31.5z'%20/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      01/26/2021
                    </span>
                    <span
                      class="emotion-class"
                      title="files"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      <span>
                        2.2.png
                        <span>
                          , 
                        </span>
                      </span>
                      <span>
                        long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-2.2.png
                        <span>
                          , 
                        </span>
                      </span>
                      <span>
                        9ZLqmuYwRIOCMJfG3vOk_33123.png
                      </span>
                    </span>
                    <span
                      class="emotion-class"
                      title="files"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      <span>
                        s.png
                      </span>
                    </span>
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="EmptyIconGrey emotion-class"
                      >
                        <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m14%2027c12.3431%2027%2011%2025.6569%2011%2024c11%2022.3431%2012.3431%2021%2014%2021h34c35.6569%2021%2037%2022.3431%2037%2024c37%2025.6569%2035.6569%2027%2034%2027h14z'/%3e%3c/svg%3e
                          class="emotion-class"
                          fill="#E4E5EB"
                        />
                      </span>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  maxlength="250"
                  placeholder="Add Invoice item..."
                  style="text-align: left;"
                  value="Line item #2"
                />
              </td>
              <td
                class="emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  <div
                    class="emotion-class"
                  >
                    <select
                      class="emotion-class"
                    >
                      <option
                        value=""
                      >
                        Select category...
                      </option>
                      <option
                        data-testid="SelectOption_1"
                        value="1"
                      >
                        Root Category 1
                      </option>
                      <option
                        data-testid="SelectOption_2"
                        value="2"
                      >
                        Work
                      </option>
                      <option
                        data-testid="SelectOption_3"
                        value="3"
                      >
                        Child Category 1.2
                      </option>
                      <option
                        data-testid="SelectOption_4"
                        value="4"
                      >
                        Root Category 2
                      </option>
                      <option
                        data-testid="SelectOption_5"
                        value="5"
                      >
                        Long category Name 33333333333
                      </option>
                    </select>
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                      class="emotion-class"
                      size="28"
                    />
                  </div>
                </span>
                <div
                  class="emotion-class"
                >
                  Root Category 1
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0"
                  value="2.11"
                />
                <div
                  class="emotion-class"
                >
                  2.11000
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0"
                  value="100"
                />
                <div
                  class="emotion-class"
                >
                  100
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <div
                  class="emotion-class"
                >
                  <input
                    class="emotion-class"
                    placeholder="0"
                    value=""
                  />
                  <span
                    class="emotion-class"
                  >
                    <div
                      class="emotion-class"
                    >
                      <select
                        class="emotion-class"
                      >
                        <option
                          data-testid="SelectOption_"
                          value="%"
                        >
                          %
                        </option>
                        <option
                          data-testid="SelectOption_Yen"
                          value="YEN"
                        >
                          YEN
                        </option>
                      </select>
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                        class="emotion-class"
                        size="28"
                      />
                    </div>
                  </span>
                </div>
                <div
                  class="emotion-class"
                />
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0 YEN"
                  style="font-weight: 600;"
                  value="211"
                />
              </td>
              <td
                class="LineItemContent_Delete emotion-class"
                data-testid="LineItemEdit_Delete"
              >
                <div>
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m21.5%2024l13%2032.5l15.5%2035l24%2026.5l32.5%2035l35%2032.5l26.5%2024l35%2015.5l32.5%2013l24%2021.5l15.5%2013l13%2015.5l21.5%2024z'/%3e%3c/svg%3e
                    classname="css-1lp8zdr"
                    fill="var(--statuses-status-initiated)"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <div
                  style="display: flex; justify-content: center;"
                >
                  <div
                    style="width: 100%;"
                  >
                    <p
                      class="emotion-class"
                      style="color: #FF7F8A; font-weight: 600;"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9.5%2023.9167c9.5%2031.8917%2016.025%2038.4167%2024%2038.4167c31.975%2038.4167%2038.5%2031.8917%2038.5%2023.9167c38.5%2015.9417%2031.975%209.41669%2024%209.41669c16.025%209.41669%209.5%2015.9417%209.5%2023.9167zm26.4997%2014.4167v17.9167l25.4997%2025.9167h22.4997l21.4997%2017.9167v14.4167h26.4997zm22.2242%2028.6411c21.7412%2029.1241%2021.4997%2029.7159%2021.4997%2030.4167c21.4997%2031.1175%2021.7412%2031.7093%2022.2242%2032.1923c22.7071%2032.6752%2023.299%2032.9167%2023.9997%2032.9167c24.7005%2032.9167%2025.2924%2032.6752%2025.7753%2032.1923c26.2583%2031.7093%2026.4997%2031.1175%2026.4997%2030.4167c26.4997%2029.7159%2026.2583%2029.1241%2025.7753%2028.6411c25.2924%2028.1582%2024.7005%2027.9167%2023.9997%2027.9167c23.299%2027.9167%2022.7071%2028.1582%2022.2242%2028.6411z'%20/%3e%3c/svg%3e
                        classname="css-1qygcm5"
                        fill="#FF7F8A"
                        size="24"
                      />
                      Fields required.
                    </p>
                    <span
                      class="emotion-class"
                      title="text line"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37%2019h11v22h37v19zm30.5%2025.5h11v28.5h30.5v25.5z'/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      text1
                    </span>
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                      title="text area"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m11%2012.5h37v15.5h11v12.5zm37%2019h11v22h37v19zm37%2025.5h11v28.5h37v25.5zm30.5%2032h11v35h30.5v32z'/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin vehicula erat venenatis odio pellentesque dignissim vitae ac nunc. Donec molestie dignissim lorem, quis volutpat sapien pretium ut. Phasellus sit amet nulla finibus, hendrerit urna id, lacinia est. Donec vitae neque id odio laoreet venenatis quis quis est. Ut congue elementum scelerisque. Vivamus ullamcorper libero eu consectetur efficitur. Morbi eu lobortis mauris. Mauris molestie, dui in scelerisque egestas, ante elit faucibus risus, sit amet dictum massa sem eu dolor. Cras feugiat sagittis lorem et viverra.

Nam in est lectus. Integer nec elit nulla. Sed diam elit, lobortis quis tincidunt nec, convallis in mi. Phasellus finibus id justo et sodales. Maecenas molestie velit eget nisi commodo, id molestie mi iaculis. Nulla hendrerit mi purus, ultricies ultricies nisi convallis at. Donec non maximus justo. Donec auctor tellus id nisi vehicula gravida ac non justo. Praesent nec volutpat mauris. Vivamus vel felis ut lorem aliquam imperdiet ut eu risus. Donec et consequat dui, vulputate hendrerit justo. Aliquam erat volutpat. Mauris eu odio ante.

Duis sit amet mi at ligula rutrum eleifend id et risus. Quisque dignissim nec orci semper dictum. Etiam malesuada eu orci ut tincidunt. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Nam a ex augue. Aliquam erat volutpat. Quisque elit lorem, mollis eu metus iaculis, faucibus viverra mi. Curabitur egestas sem et lacinia elementum. Maecenas eu sollicitudin nunc.
                    </span>
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                      title="dropdown"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m10%2035c10%2036.6569%2011.3431%2038%2013%2038h35c36.6569%2038%2038%2036.6569%2038%2035v13c38%2011.3431%2036.6569%2010%2035%2010h13c11.3431%2010%2010%2011.3431%2010%2013v35zm35%2013v35h13v13h35zm28%2025.5l24%2032.5l20%2025.5h28zm24%2015.5l28%2022.5h20l24%2015.5z'/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      Option F
                    </span>
                    <span
                      class="emotion-class"
                      title="Date"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m27%2010.5v8.5h31v10.5h34.5c36.1569%2010.5%2037.5%2011.8431%2037.5%2013.5v34.5c37.5%2036.1569%2036.1569%2037.5%2034.5%2037.5h13.5c11.8431%2037.5%2010.5%2036.1569%2010.5%2034.5v13.5c10.5%2011.8431%2011.8431%2010.5%2013.5%2010.5h17.5v8.5h21.5v10.5h27zm34.5%2034.5h13.5v18h34.5v34.5zm31.5%2025v32h22.5v25h31.5z'%20/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      01/26/2021
                    </span>
                    <span
                      class="emotion-class"
                      title="files"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      <span>
                        2.2.png
                        <span>
                          , 
                        </span>
                      </span>
                      <span>
                        long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-2.2.png
                        <span>
                          , 
                        </span>
                      </span>
                      <span>
                        9ZLqmuYwRIOCMJfG3vOk_33123.png
                      </span>
                    </span>
                    <span
                      class="emotion-class"
                      title="files"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      <span>
                        s.png
                      </span>
                    </span>
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="EmptyIconGrey emotion-class"
                      >
                        <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m14%2027c12.3431%2027%2011%2025.6569%2011%2024c11%2022.3431%2012.3431%2021%2014%2021h34c35.6569%2021%2037%2022.3431%2037%2024c37%2025.6569%2035.6569%2027%2034%2027h14z'/%3e%3c/svg%3e
                          class="emotion-class"
                          fill="#E4E5EB"
                        />
                      </span>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  maxlength="250"
                  placeholder="Add Invoice item..."
                  style="text-align: left;"
                  value="Line item #3"
                />
              </td>
              <td
                class="emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  <div
                    class="emotion-class"
                  >
                    <select
                      class="emotion-class"
                    >
                      <option
                        value=""
                      >
                        Select category...
                      </option>
                      <option
                        data-testid="SelectOption_1"
                        value="1"
                      >
                        Root Category 1
                      </option>
                      <option
                        data-testid="SelectOption_2"
                        value="2"
                      >
                        Work
                      </option>
                      <option
                        data-testid="SelectOption_3"
                        value="3"
                      >
                        Child Category 1.2
                      </option>
                      <option
                        data-testid="SelectOption_4"
                        value="4"
                      >
                        Root Category 2
                      </option>
                      <option
                        data-testid="SelectOption_5"
                        value="5"
                      >
                        Long category Name 33333333333
                      </option>
                    </select>
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                      class="emotion-class"
                      size="28"
                    />
                  </div>
                </span>
                <div
                  class="emotion-class"
                >
                  Work
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0"
                  value="10"
                />
                <div
                  class="emotion-class"
                >
                  10.00000
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0"
                  value="100"
                />
                <div
                  class="emotion-class"
                >
                  100
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <div
                  class="emotion-class"
                >
                  <input
                    class="emotion-class"
                    placeholder="0"
                    value="101"
                  />
                  <span
                    class="emotion-class"
                  >
                    <div
                      class="emotion-class"
                    >
                      <select
                        class="emotion-class"
                      >
                        <option
                          data-testid="SelectOption_"
                          value="%"
                        >
                          %
                        </option>
                        <option
                          data-testid="SelectOption_Yen"
                          value="YEN"
                        >
                          YEN
                        </option>
                      </select>
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                        class="emotion-class"
                        size="28"
                      />
                    </div>
                  </span>
                </div>
                <div
                  class="emotion-class"
                >
                  101
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0 YEN"
                  style="font-weight: 600;"
                  value="900"
                />
              </td>
              <td
                class="LineItemContent_Delete emotion-class"
                data-testid="LineItemEdit_Delete"
              >
                <div>
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m21.5%2024l13%2032.5l15.5%2035l24%2026.5l32.5%2035l35%2032.5l26.5%2024l35%2015.5l32.5%2013l24%2021.5l15.5%2013l13%2015.5l21.5%2024z'/%3e%3c/svg%3e
                    classname="css-1lp8zdr"
                    fill="var(--statuses-status-initiated)"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <div
                  style="display: flex; justify-content: center;"
                >
                  <div
                    style="width: 100%;"
                  >
                    <p
                      class="emotion-class"
                      style="color: #FF7F8A; font-weight: 600;"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9.5%2023.9167c9.5%2031.8917%2016.025%2038.4167%2024%2038.4167c31.975%2038.4167%2038.5%2031.8917%2038.5%2023.9167c38.5%2015.9417%2031.975%209.41669%2024%209.41669c16.025%209.41669%209.5%2015.9417%209.5%2023.9167zm26.4997%2014.4167v17.9167l25.4997%2025.9167h22.4997l21.4997%2017.9167v14.4167h26.4997zm22.2242%2028.6411c21.7412%2029.1241%2021.4997%2029.7159%2021.4997%2030.4167c21.4997%2031.1175%2021.7412%2031.7093%2022.2242%2032.1923c22.7071%2032.6752%2023.299%2032.9167%2023.9997%2032.9167c24.7005%2032.9167%2025.2924%2032.6752%2025.7753%2032.1923c26.2583%2031.7093%2026.4997%2031.1175%2026.4997%2030.4167c26.4997%2029.7159%2026.2583%2029.1241%2025.7753%2028.6411c25.2924%2028.1582%2024.7005%2027.9167%2023.9997%2027.9167c23.299%2027.9167%2022.7071%2028.1582%2022.2242%2028.6411z'%20/%3e%3c/svg%3e
                        classname="css-1qygcm5"
                        fill="#FF7F8A"
                        size="24"
                      />
                      Fields required.
                    </p>
                    <span
                      class="emotion-class"
                      title="text line"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37%2019h11v22h37v19zm30.5%2025.5h11v28.5h30.5v25.5z'/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      text1
                    </span>
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                      title="text area"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m11%2012.5h37v15.5h11v12.5zm37%2019h11v22h37v19zm37%2025.5h11v28.5h37v25.5zm30.5%2032h11v35h30.5v32z'/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin vehicula erat venenatis odio pellentesque dignissim vitae ac nunc. Donec molestie dignissim lorem, quis volutpat sapien pretium ut. Phasellus sit amet nulla finibus, hendrerit urna id, lacinia est. Donec vitae neque id odio laoreet venenatis quis quis est. Ut congue elementum scelerisque. Vivamus ullamcorper libero eu consectetur efficitur. Morbi eu lobortis mauris. Mauris molestie, dui in scelerisque egestas, ante elit faucibus risus, sit amet dictum massa sem eu dolor. Cras feugiat sagittis lorem et viverra.

Nam in est lectus. Integer nec elit nulla. Sed diam elit, lobortis quis tincidunt nec, convallis in mi. Phasellus finibus id justo et sodales. Maecenas molestie velit eget nisi commodo, id molestie mi iaculis. Nulla hendrerit mi purus, ultricies ultricies nisi convallis at. Donec non maximus justo. Donec auctor tellus id nisi vehicula gravida ac non justo. Praesent nec volutpat mauris. Vivamus vel felis ut lorem aliquam imperdiet ut eu risus. Donec et consequat dui, vulputate hendrerit justo. Aliquam erat volutpat. Mauris eu odio ante.

Duis sit amet mi at ligula rutrum eleifend id et risus. Quisque dignissim nec orci semper dictum. Etiam malesuada eu orci ut tincidunt. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Nam a ex augue. Aliquam erat volutpat. Quisque elit lorem, mollis eu metus iaculis, faucibus viverra mi. Curabitur egestas sem et lacinia elementum. Maecenas eu sollicitudin nunc.
                    </span>
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                      title="dropdown"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m10%2035c10%2036.6569%2011.3431%2038%2013%2038h35c36.6569%2038%2038%2036.6569%2038%2035v13c38%2011.3431%2036.6569%2010%2035%2010h13c11.3431%2010%2010%2011.3431%2010%2013v35zm35%2013v35h13v13h35zm28%2025.5l24%2032.5l20%2025.5h28zm24%2015.5l28%2022.5h20l24%2015.5z'/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      Option F
                    </span>
                    <span
                      class="emotion-class"
                      title="Date"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m27%2010.5v8.5h31v10.5h34.5c36.1569%2010.5%2037.5%2011.8431%2037.5%2013.5v34.5c37.5%2036.1569%2036.1569%2037.5%2034.5%2037.5h13.5c11.8431%2037.5%2010.5%2036.1569%2010.5%2034.5v13.5c10.5%2011.8431%2011.8431%2010.5%2013.5%2010.5h17.5v8.5h21.5v10.5h27zm34.5%2034.5h13.5v18h34.5v34.5zm31.5%2025v32h22.5v25h31.5z'%20/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      01/26/2021
                    </span>
                    <span
                      class="emotion-class"
                      title="files"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      <span>
                        2.2.png
                        <span>
                          , 
                        </span>
                      </span>
                      <span>
                        long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-2.2.png
                        <span>
                          , 
                        </span>
                      </span>
                      <span>
                        9ZLqmuYwRIOCMJfG3vOk_33123.png
                      </span>
                    </span>
                    <span
                      class="emotion-class"
                      title="files"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      <span>
                        s.png
                      </span>
                    </span>
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="EmptyIconGrey emotion-class"
                      >
                        <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m14%2027c12.3431%2027%2011%2025.6569%2011%2024c11%2022.3431%2012.3431%2021%2014%2021h34c35.6569%2021%2037%2022.3431%2037%2024c37%2025.6569%2035.6569%2027%2034%2027h14z'/%3e%3c/svg%3e
                          class="emotion-class"
                          fill="#E4E5EB"
                        />
                      </span>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <span
                  class="emotion-class"
                >
                  <a
                    class=" emotion-class"
                  >
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m24%209.5c16.025%209.5%209.5%2016.025%209.5%2024c9.5%2031.975%2016.025%2038.5%2024%2038.5c31.975%2038.5%2038.5%2031.975%2038.5%2024c38.5%2016.025%2031.975%209.5%2024%209.5zm22.4513%2022.4513h15.6371v25.5487h22.4513v32.3628h25.5486v25.5487h32.3628v22.4513h25.5486v15.6372h22.4513v22.4513z'/%3e%3c/svg%3e
                      classname="css-12ecxzt"
                      size="20"
                      style="margin: 0px 5px 0px -2px;"
                    />
                    <span
                      class="emotion-class"
                    >
                      Add new item
                    </span>
                  </a>
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        class="emotion-class"
        data-testid="LineItemsTableWithCf_Footer"
      >
        <div
          class="emotion-class"
          data-testid="InvoiceDetails_Note"
        />
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
            >
              Total Deductions
            </span>
            <p
              class="emotion-class"
              data-testid="LineItemsTable_TotalDeductions"
            >
              169
               
              YEN
            </p>
          </div>
          <div
            class="emotion-class"
          />
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
            >
              <div
                class="emotion-class"
              >
                <select
                  class="emotion-class"
                >
                  <option
                    data-testid="SelectOption_Aud"
                    value="AUD"
                  >
                    AUD
                  </option>
                  <option
                    data-testid="SelectOption_Cny"
                    value="CNY"
                  >
                    CNY
                  </option>
                  <option
                    data-testid="SelectOption_Gbp"
                    value="GBP"
                  >
                    GBP
                  </option>
                  <option
                    data-testid="SelectOption_Jpy"
                    value="JPY"
                  >
                    JPY
                  </option>
                  <option
                    data-testid="SelectOption_Yen"
                    value="YEN"
                  >
                    YEN
                  </option>
                  <option
                    data-testid="SelectOption_Usd"
                    value="USD"
                  >
                    USD
                  </option>
                </select>
                <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                  class="emotion-class"
                  size="28"
                />
              </div>
            </span>
            <p
              class="emotion-class"
            >
              Total
            </p>
            <p
              class="emotion-class"
              data-testid="LineItemsTable_Total"
            >
              1,179
               
              YEN
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`LineItemsTableWithCF > snapshot should match: EditPrefilledEmptyValues 1`] = `
.emotion-class {
  padding: 0 var(--spacing-m) var(--spacing-m);
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: var(--spacing-xs) var(--spacing-xs) 0 var(--spacing-xs);
  overflow-x: auto;
}

.emotion-class {
  display: table;
  width: 100%;
  border-spacing: 0 var(--spacing-s);
}

.emotion-class {
  display: table-row;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  padding: var(--spacing-s) var(--spacing-m);
  font-weight: 600;
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border: none;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class:first-of-type {
  border: none;
}

.emotion-class:last-of-type {
  border: none;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  display: table-row;
  cursor: pointer;
}

.emotion-class:hover td,
.emotion-class:focus-within td {
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  position: relative;
  padding: var(--spacing-xs);
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class:focus-within.emotion-class::before {
  content: "";
  position: absolute;
  border: 1px solid var(--borders-canvas-borders-canvas-border-neutral);
  left: 0;
  right: 0;
  bottom: 0;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  border-radius: calc(var(--spacing-xs) / 2);
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  border: 0;
  outline-width: 0;
  padding: var(--spacing-s-plus);
  background-color: transparent;
  text-align: right;
}

.emotion-class::-webkit-input-placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class::-moz-placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class:-ms-input-placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class::placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-table-fills-table-fill-error)!important;
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  position: relative;
  padding: var(--spacing-xs);
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class:focus-within.emotion-class::before {
  content: "";
  position: absolute;
  border: 1px solid var(--borders-canvas-borders-canvas-border-neutral);
  left: 0;
  right: 0;
  bottom: 0;
}

.emotion-class select {
  border: 0;
  outline-width: 0;
  background-color: transparent;
  padding-left: var(--spacing-xs);
  padding-right: var(--spacing-xs);
  min-height: 40px;
  min-width: 90px;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  fill: #000000;
  width: 28px;
  height: 28px;
  fill-rule: evenodd;
  vertical-align: middle;
  position: absolute;
  top: 10px;
  right: 5px;
  fill: #465AB6;
  pointer-events: none;
}

.emotion-class {
  padding: 0 10px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  visibility: hidden;
  max-height: 0;
  white-space: nowrap;
  overflow-x: hidden;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  border-radius: calc(var(--spacing-xs) / 2);
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  border: 0;
  outline-width: 0;
  padding: var(--spacing-s-plus);
  background-color: transparent;
  text-align: right;
}

.emotion-class::-webkit-input-placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class::-moz-placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class:-ms-input-placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class::placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class::-webkit-input-placeholder {
  color: var(--glyphs-tables-glyphs-table-placeholder);
}

.emotion-class::-moz-placeholder {
  color: var(--glyphs-tables-glyphs-table-placeholder);
}

.emotion-class:-ms-input-placeholder {
  color: var(--glyphs-tables-glyphs-table-placeholder);
}

.emotion-class::placeholder {
  color: var(--glyphs-tables-glyphs-table-placeholder);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-right: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-right-radius: var(--spacing-xs);
  border-bottom-right-radius: var(--spacing-xs);
  position: relative;
  padding: var(--spacing-xs);
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class:focus-within.emotion-class::before {
  content: "";
  position: absolute;
  border: 1px solid var(--borders-canvas-borders-canvas-border-neutral);
  left: 0;
  right: 0;
  bottom: 0;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  background-color: transparent!important;
  cursor: pointer;
  position: absolute;
  border-width: 0!important;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class div {
  display: inline-block;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-table-fills-table-fill-error)!important;
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  position: relative;
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-danger)!important;
  padding: 10px;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #303757;
  display: inline-block;
  font-weight: 400;
  margin-right: 15px;
  position: relative;
  padding-top: 2px;
  max-width: 400px;
}

.emotion-class svg {
  left: 0;
  top: 0;
  position: absolute;
}

.emotion-class a {
  color: #A6ABBF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class a:hover {
  color: #465AB6;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 35px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: 20px;
  height: 100%;
  overflow: hidden;
}

.emotion-class svg {
  margin: -5px -6px 0 -6px;
}

.emotion-class {
  fill: #E4E5EB;
  width: 29px;
  height: 29px;
  fill-rule: evenodd;
  vertical-align: middle;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  position: relative;
  background-color: transparent;
  padding: 0;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class a {
  display: block;
  padding: var(--spacing-l);
  text-align: center;
}

.emotion-class {
  position: relative;
}

.emotion-class svg {
  fill: #465AB6;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class {
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
  font-size: 13px;
  color: #465AB6;
  vertical-align: middle;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-top: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: 0 0 var(--spacing-xs) var(--spacing-xs);
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  width: 100%;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  height: 1px;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  border-radius: var(--spacing-s-plus);
  background-color: var(--borders-encapsulation-separator-divider);
}

.emotion-class>div {
  display: inline-block;
  margin-right: var(--spacing-s-plus);
}

.emotion-class select {
  font-size: var(--font-size-s);
  width: auto;
  padding: 0;
  min-height: auto;
  outline-width: 0;
  color: var(--actions-basic-actions-action-base);
  font-weight: 600;
  border: 0;
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 24px;
  line-height: 30px;
  color: var(--glyphs-basic-glyphs-base);
}

<div>
  <div
    style="margin: 40px;"
  >
    <div>
      <div
        class="emotion-class"
      >
        <table
          cellpadding="0"
          cellspacing="0"
          class="emotion-class"
          data-testid="LineItemsTableWithCf"
        >
          <tbody>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                style="text-align: left; width: 100%;"
              >
                <span
                  class="emotion-class"
                >
                  Payment_Line_Items_Item
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: left; white-space: nowrap; min-width: 128px;"
              >
                <span
                  class="emotion-class"
                >
                  Category
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 28px;"
              >
                <span
                  class="emotion-class"
                >
                  Qty
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 88px;"
              >
                <span
                  class="emotion-class"
                >
                  Price
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: center; min-width: 160px;"
              >
                <span
                  class="emotion-class"
                >
                  Deduction
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 140px;"
              >
                <span
                  class="emotion-class"
                >
                  Amount
                </span>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  maxlength="250"
                  placeholder="Add Invoice item..."
                  style="text-align: left;"
                  value="Line item #1"
                />
              </td>
              <td
                class="emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  <div
                    class="emotion-class"
                  >
                    <select
                      class="emotion-class"
                    >
                      <option
                        value=""
                      >
                        Select category...
                      </option>
                      <option
                        data-testid="SelectOption_1"
                        value="1"
                      >
                        Root Category 1
                      </option>
                      <option
                        data-testid="SelectOption_2"
                        value="2"
                      >
                        Work
                      </option>
                      <option
                        data-testid="SelectOption_3"
                        value="3"
                      >
                        Child Category 1.2
                      </option>
                      <option
                        data-testid="SelectOption_4"
                        value="4"
                      >
                        Root Category 2
                      </option>
                      <option
                        data-testid="SelectOption_5"
                        value="5"
                      >
                        Long category Name 33333333333
                      </option>
                    </select>
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                      class="emotion-class"
                      size="28"
                    />
                  </div>
                </span>
                <div
                  class="emotion-class"
                />
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0"
                  value="1.1111"
                />
                <div
                  class="emotion-class"
                >
                  1.11110
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0"
                  value="122"
                />
                <div
                  class="emotion-class"
                >
                  122
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <div
                  class="emotion-class"
                >
                  <input
                    class="emotion-class"
                    placeholder="0"
                    value="50"
                  />
                  <span
                    class="emotion-class"
                  >
                    <div
                      class="emotion-class"
                    >
                      <select
                        class="emotion-class"
                      >
                        <option
                          data-testid="SelectOption_"
                          value="%"
                        >
                          %
                        </option>
                        <option
                          data-testid="SelectOption_Yen"
                          value="YEN"
                        >
                          YEN
                        </option>
                      </select>
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                        class="emotion-class"
                        size="28"
                      />
                    </div>
                  </span>
                </div>
                <div
                  class="emotion-class"
                >
                  50
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0 YEN"
                  style="font-weight: 600;"
                  value="68"
                />
              </td>
              <td
                class="LineItemContent_Delete emotion-class"
                data-testid="LineItemEdit_Delete"
              >
                <div>
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m21.5%2024l13%2032.5l15.5%2035l24%2026.5l32.5%2035l35%2032.5l26.5%2024l35%2015.5l32.5%2013l24%2021.5l15.5%2013l13%2015.5l21.5%2024z'/%3e%3c/svg%3e
                    classname="css-1lp8zdr"
                    fill="var(--statuses-status-initiated)"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <div
                  style="display: flex; justify-content: center;"
                >
                  <div
                    style="width: 100%;"
                  >
                    <p
                      class="emotion-class"
                      style="color: #FF7F8A; font-weight: 600;"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9.5%2023.9167c9.5%2031.8917%2016.025%2038.4167%2024%2038.4167c31.975%2038.4167%2038.5%2031.8917%2038.5%2023.9167c38.5%2015.9417%2031.975%209.41669%2024%209.41669c16.025%209.41669%209.5%2015.9417%209.5%2023.9167zm26.4997%2014.4167v17.9167l25.4997%2025.9167h22.4997l21.4997%2017.9167v14.4167h26.4997zm22.2242%2028.6411c21.7412%2029.1241%2021.4997%2029.7159%2021.4997%2030.4167c21.4997%2031.1175%2021.7412%2031.7093%2022.2242%2032.1923c22.7071%2032.6752%2023.299%2032.9167%2023.9997%2032.9167c24.7005%2032.9167%2025.2924%2032.6752%2025.7753%2032.1923c26.2583%2031.7093%2026.4997%2031.1175%2026.4997%2030.4167c26.4997%2029.7159%2026.2583%2029.1241%2025.7753%2028.6411c25.2924%2028.1582%2024.7005%2027.9167%2023.9997%2027.9167c23.299%2027.9167%2022.7071%2028.1582%2022.2242%2028.6411z'%20/%3e%3c/svg%3e
                        classname="css-1qygcm5"
                        fill="#FF7F8A"
                        size="24"
                      />
                      Fields required.
                    </p>
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                    />
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="EmptyIconGrey emotion-class"
                      >
                        <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m14%2027c12.3431%2027%2011%2025.6569%2011%2024c11%2022.3431%2012.3431%2021%2014%2021h34c35.6569%2021%2037%2022.3431%2037%2024c37%2025.6569%2035.6569%2027%2034%2027h14z'/%3e%3c/svg%3e
                          class="emotion-class"
                          fill="#E4E5EB"
                        />
                      </span>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  maxlength="250"
                  placeholder="Add Invoice item..."
                  style="text-align: left;"
                  value="Line item #2"
                />
              </td>
              <td
                class="emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  <div
                    class="emotion-class"
                  >
                    <select
                      class="emotion-class"
                    >
                      <option
                        value=""
                      >
                        Select category...
                      </option>
                      <option
                        data-testid="SelectOption_1"
                        value="1"
                      >
                        Root Category 1
                      </option>
                      <option
                        data-testid="SelectOption_2"
                        value="2"
                      >
                        Work
                      </option>
                      <option
                        data-testid="SelectOption_3"
                        value="3"
                      >
                        Child Category 1.2
                      </option>
                      <option
                        data-testid="SelectOption_4"
                        value="4"
                      >
                        Root Category 2
                      </option>
                      <option
                        data-testid="SelectOption_5"
                        value="5"
                      >
                        Long category Name 33333333333
                      </option>
                    </select>
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                      class="emotion-class"
                      size="28"
                    />
                  </div>
                </span>
                <div
                  class="emotion-class"
                >
                  Root Category 1
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0"
                  value="2.11"
                />
                <div
                  class="emotion-class"
                >
                  2.11000
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0"
                  value="100"
                />
                <div
                  class="emotion-class"
                >
                  100
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <div
                  class="emotion-class"
                >
                  <input
                    class="emotion-class"
                    placeholder="0"
                    value=""
                  />
                  <span
                    class="emotion-class"
                  >
                    <div
                      class="emotion-class"
                    >
                      <select
                        class="emotion-class"
                      >
                        <option
                          data-testid="SelectOption_"
                          value="%"
                        >
                          %
                        </option>
                        <option
                          data-testid="SelectOption_Yen"
                          value="YEN"
                        >
                          YEN
                        </option>
                      </select>
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                        class="emotion-class"
                        size="28"
                      />
                    </div>
                  </span>
                </div>
                <div
                  class="emotion-class"
                />
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0 YEN"
                  style="font-weight: 600;"
                  value="211"
                />
              </td>
              <td
                class="LineItemContent_Delete emotion-class"
                data-testid="LineItemEdit_Delete"
              >
                <div>
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m21.5%2024l13%2032.5l15.5%2035l24%2026.5l32.5%2035l35%2032.5l26.5%2024l35%2015.5l32.5%2013l24%2021.5l15.5%2013l13%2015.5l21.5%2024z'/%3e%3c/svg%3e
                    classname="css-1lp8zdr"
                    fill="var(--statuses-status-initiated)"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <div
                  style="display: flex; justify-content: center;"
                >
                  <div
                    style="width: 100%;"
                  >
                    <p
                      class="emotion-class"
                      style="color: #FF7F8A; font-weight: 600;"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9.5%2023.9167c9.5%2031.8917%2016.025%2038.4167%2024%2038.4167c31.975%2038.4167%2038.5%2031.8917%2038.5%2023.9167c38.5%2015.9417%2031.975%209.41669%2024%209.41669c16.025%209.41669%209.5%2015.9417%209.5%2023.9167zm26.4997%2014.4167v17.9167l25.4997%2025.9167h22.4997l21.4997%2017.9167v14.4167h26.4997zm22.2242%2028.6411c21.7412%2029.1241%2021.4997%2029.7159%2021.4997%2030.4167c21.4997%2031.1175%2021.7412%2031.7093%2022.2242%2032.1923c22.7071%2032.6752%2023.299%2032.9167%2023.9997%2032.9167c24.7005%2032.9167%2025.2924%2032.6752%2025.7753%2032.1923c26.2583%2031.7093%2026.4997%2031.1175%2026.4997%2030.4167c26.4997%2029.7159%2026.2583%2029.1241%2025.7753%2028.6411c25.2924%2028.1582%2024.7005%2027.9167%2023.9997%2027.9167c23.299%2027.9167%2022.7071%2028.1582%2022.2242%2028.6411z'%20/%3e%3c/svg%3e
                        classname="css-1qygcm5"
                        fill="#FF7F8A"
                        size="24"
                      />
                      Fields required.
                    </p>
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                    />
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="EmptyIconGrey emotion-class"
                      >
                        <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m14%2027c12.3431%2027%2011%2025.6569%2011%2024c11%2022.3431%2012.3431%2021%2014%2021h34c35.6569%2021%2037%2022.3431%2037%2024c37%2025.6569%2035.6569%2027%2034%2027h14z'/%3e%3c/svg%3e
                          class="emotion-class"
                          fill="#E4E5EB"
                        />
                      </span>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  maxlength="250"
                  placeholder="Add Invoice item..."
                  style="text-align: left;"
                  value="Line item #3"
                />
              </td>
              <td
                class="emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  <div
                    class="emotion-class"
                  >
                    <select
                      class="emotion-class"
                    >
                      <option
                        value=""
                      >
                        Select category...
                      </option>
                      <option
                        data-testid="SelectOption_1"
                        value="1"
                      >
                        Root Category 1
                      </option>
                      <option
                        data-testid="SelectOption_2"
                        value="2"
                      >
                        Work
                      </option>
                      <option
                        data-testid="SelectOption_3"
                        value="3"
                      >
                        Child Category 1.2
                      </option>
                      <option
                        data-testid="SelectOption_4"
                        value="4"
                      >
                        Root Category 2
                      </option>
                      <option
                        data-testid="SelectOption_5"
                        value="5"
                      >
                        Long category Name 33333333333
                      </option>
                    </select>
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                      class="emotion-class"
                      size="28"
                    />
                  </div>
                </span>
                <div
                  class="emotion-class"
                >
                  Work
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0"
                  value="10"
                />
                <div
                  class="emotion-class"
                >
                  10.00000
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0"
                  value="100"
                />
                <div
                  class="emotion-class"
                >
                  100
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <div
                  class="emotion-class"
                >
                  <input
                    class="emotion-class"
                    placeholder="0"
                    value="101"
                  />
                  <span
                    class="emotion-class"
                  >
                    <div
                      class="emotion-class"
                    >
                      <select
                        class="emotion-class"
                      >
                        <option
                          data-testid="SelectOption_"
                          value="%"
                        >
                          %
                        </option>
                        <option
                          data-testid="SelectOption_Yen"
                          value="YEN"
                        >
                          YEN
                        </option>
                      </select>
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                        class="emotion-class"
                        size="28"
                      />
                    </div>
                  </span>
                </div>
                <div
                  class="emotion-class"
                >
                  101
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0 YEN"
                  style="font-weight: 600;"
                  value="900"
                />
              </td>
              <td
                class="LineItemContent_Delete emotion-class"
                data-testid="LineItemEdit_Delete"
              >
                <div>
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m21.5%2024l13%2032.5l15.5%2035l24%2026.5l32.5%2035l35%2032.5l26.5%2024l35%2015.5l32.5%2013l24%2021.5l15.5%2013l13%2015.5l21.5%2024z'/%3e%3c/svg%3e
                    classname="css-1lp8zdr"
                    fill="var(--statuses-status-initiated)"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <div
                  style="display: flex; justify-content: center;"
                >
                  <div
                    style="width: 100%;"
                  >
                    <p
                      class="emotion-class"
                      style="color: #FF7F8A; font-weight: 600;"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9.5%2023.9167c9.5%2031.8917%2016.025%2038.4167%2024%2038.4167c31.975%2038.4167%2038.5%2031.8917%2038.5%2023.9167c38.5%2015.9417%2031.975%209.41669%2024%209.41669c16.025%209.41669%209.5%2015.9417%209.5%2023.9167zm26.4997%2014.4167v17.9167l25.4997%2025.9167h22.4997l21.4997%2017.9167v14.4167h26.4997zm22.2242%2028.6411c21.7412%2029.1241%2021.4997%2029.7159%2021.4997%2030.4167c21.4997%2031.1175%2021.7412%2031.7093%2022.2242%2032.1923c22.7071%2032.6752%2023.299%2032.9167%2023.9997%2032.9167c24.7005%2032.9167%2025.2924%2032.6752%2025.7753%2032.1923c26.2583%2031.7093%2026.4997%2031.1175%2026.4997%2030.4167c26.4997%2029.7159%2026.2583%2029.1241%2025.7753%2028.6411c25.2924%2028.1582%2024.7005%2027.9167%2023.9997%2027.9167c23.299%2027.9167%2022.7071%2028.1582%2022.2242%2028.6411z'%20/%3e%3c/svg%3e
                        classname="css-1qygcm5"
                        fill="#FF7F8A"
                        size="24"
                      />
                      Fields required.
                    </p>
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                    />
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="EmptyIconGrey emotion-class"
                      >
                        <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m14%2027c12.3431%2027%2011%2025.6569%2011%2024c11%2022.3431%2012.3431%2021%2014%2021h34c35.6569%2021%2037%2022.3431%2037%2024c37%2025.6569%2035.6569%2027%2034%2027h14z'/%3e%3c/svg%3e
                          class="emotion-class"
                          fill="#E4E5EB"
                        />
                      </span>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <span
                  class="emotion-class"
                >
                  <a
                    class=" emotion-class"
                  >
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m24%209.5c16.025%209.5%209.5%2016.025%209.5%2024c9.5%2031.975%2016.025%2038.5%2024%2038.5c31.975%2038.5%2038.5%2031.975%2038.5%2024c38.5%2016.025%2031.975%209.5%2024%209.5zm22.4513%2022.4513h15.6371v25.5487h22.4513v32.3628h25.5486v25.5487h32.3628v22.4513h25.5486v15.6372h22.4513v22.4513z'/%3e%3c/svg%3e
                      classname="css-12ecxzt"
                      size="20"
                      style="margin: 0px 5px 0px -2px;"
                    />
                    <span
                      class="emotion-class"
                    >
                      Add new item
                    </span>
                  </a>
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        class="emotion-class"
        data-testid="LineItemsTableWithCf_Footer"
      >
        <div
          class="emotion-class"
          data-testid="InvoiceDetails_Note"
        />
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
            >
              Total Deductions
            </span>
            <p
              class="emotion-class"
              data-testid="LineItemsTable_TotalDeductions"
            >
              169
               
              YEN
            </p>
          </div>
          <div
            class="emotion-class"
          />
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
            >
              <div
                class="emotion-class"
              >
                <select
                  class="emotion-class"
                >
                  <option
                    data-testid="SelectOption_Aud"
                    value="AUD"
                  >
                    AUD
                  </option>
                  <option
                    data-testid="SelectOption_Cny"
                    value="CNY"
                  >
                    CNY
                  </option>
                  <option
                    data-testid="SelectOption_Gbp"
                    value="GBP"
                  >
                    GBP
                  </option>
                  <option
                    data-testid="SelectOption_Jpy"
                    value="JPY"
                  >
                    JPY
                  </option>
                  <option
                    data-testid="SelectOption_Yen"
                    value="YEN"
                  >
                    YEN
                  </option>
                  <option
                    data-testid="SelectOption_Usd"
                    value="USD"
                  >
                    USD
                  </option>
                </select>
                <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                  class="emotion-class"
                  size="28"
                />
              </div>
            </span>
            <p
              class="emotion-class"
            >
              Total
            </p>
            <p
              class="emotion-class"
              data-testid="LineItemsTable_Total"
            >
              1,179
               
              YEN
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`LineItemsTableWithCF > snapshot should match: EditPrefilledWithTax 1`] = `
.emotion-class {
  padding: 0 var(--spacing-m) var(--spacing-m);
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: var(--spacing-xs) var(--spacing-xs) 0 var(--spacing-xs);
  overflow-x: auto;
}

.emotion-class {
  display: table;
  width: 100%;
  border-spacing: 0 var(--spacing-s);
}

.emotion-class {
  display: table-row;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  padding: var(--spacing-s) var(--spacing-m);
  font-weight: 600;
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border: none;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class:first-of-type {
  border: none;
}

.emotion-class:last-of-type {
  border: none;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  display: table-row;
  cursor: pointer;
}

.emotion-class:hover td,
.emotion-class:focus-within td {
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  position: relative;
  padding: var(--spacing-xs);
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class:focus-within.emotion-class::before {
  content: "";
  position: absolute;
  border: 1px solid var(--borders-canvas-borders-canvas-border-neutral);
  left: 0;
  right: 0;
  bottom: 0;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  border-radius: calc(var(--spacing-xs) / 2);
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  border: 0;
  outline-width: 0;
  padding: var(--spacing-s-plus);
  background-color: transparent;
  text-align: right;
}

.emotion-class::-webkit-input-placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class::-moz-placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class:-ms-input-placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class::placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-table-fills-table-fill-error)!important;
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  position: relative;
  padding: var(--spacing-xs);
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class:focus-within.emotion-class::before {
  content: "";
  position: absolute;
  border: 1px solid var(--borders-canvas-borders-canvas-border-neutral);
  left: 0;
  right: 0;
  bottom: 0;
}

.emotion-class select {
  border: 0;
  outline-width: 0;
  background-color: transparent;
  padding-left: var(--spacing-xs);
  padding-right: var(--spacing-xs);
  min-height: 40px;
  min-width: 90px;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  fill: #000000;
  width: 28px;
  height: 28px;
  fill-rule: evenodd;
  vertical-align: middle;
  position: absolute;
  top: 10px;
  right: 5px;
  fill: #465AB6;
  pointer-events: none;
}

.emotion-class {
  padding: 0 10px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  visibility: hidden;
  max-height: 0;
  white-space: nowrap;
  overflow-x: hidden;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  border-radius: calc(var(--spacing-xs) / 2);
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  border: 0;
  outline-width: 0;
  padding: var(--spacing-s-plus);
  background-color: transparent;
  text-align: right;
}

.emotion-class::-webkit-input-placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class::-moz-placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class:-ms-input-placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class::placeholder {
  color: var(--glyphs-forms-glyphs-form-placeholder);
}

.emotion-class::-webkit-input-placeholder {
  color: var(--glyphs-tables-glyphs-table-placeholder);
}

.emotion-class::-moz-placeholder {
  color: var(--glyphs-tables-glyphs-table-placeholder);
}

.emotion-class:-ms-input-placeholder {
  color: var(--glyphs-tables-glyphs-table-placeholder);
}

.emotion-class::placeholder {
  color: var(--glyphs-tables-glyphs-table-placeholder);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-right: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-right-radius: var(--spacing-xs);
  border-bottom-right-radius: var(--spacing-xs);
  position: relative;
  padding: var(--spacing-xs);
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class:focus-within.emotion-class::before {
  content: "";
  position: absolute;
  border: 1px solid var(--borders-canvas-borders-canvas-border-neutral);
  left: 0;
  right: 0;
  bottom: 0;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  background-color: transparent!important;
  cursor: pointer;
  position: absolute;
  border-width: 0!important;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class div {
  display: inline-block;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-table-fills-table-fill-error)!important;
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  position: relative;
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-danger)!important;
  padding: 10px;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #303757;
  display: inline-block;
  font-weight: 400;
  margin-right: 15px;
  padding-left: 25px;
  position: relative;
  padding-top: 2px;
  max-width: 400px;
}

.emotion-class svg {
  left: 0;
  top: 0;
  position: absolute;
}

.emotion-class a {
  color: #A6ABBF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class a:hover {
  color: #465AB6;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #303757;
  display: inline-block;
  font-weight: 400;
  margin-right: 15px;
  position: relative;
  padding-top: 2px;
  max-width: 400px;
}

.emotion-class svg {
  left: 0;
  top: 0;
  position: absolute;
}

.emotion-class a {
  color: #A6ABBF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class a:hover {
  color: #465AB6;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #303757;
  display: inline-block;
  font-weight: 400;
  margin-right: 15px;
  padding-left: 25px;
  position: relative;
  padding-top: 2px;
}

.emotion-class svg {
  left: 0;
  top: 0;
  position: absolute;
}

.emotion-class a {
  color: #A6ABBF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class a:hover {
  color: #465AB6;
}

.emotion-class>a,
.emotion-class>span {
  position: relative;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
  padding-right: 5px;
  margin-right: 5px;
}

.emotion-class>a span,
.emotion-class>span span {
  color: #A6ABBF;
  position: absolute;
  top: 0;
  right: 0;
}

.emotion-class {
  fill: #A6ABBF;
  width: 24px;
  height: 24px;
  fill-rule: evenodd;
  vertical-align: middle;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 35px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: 20px;
  height: 100%;
  overflow: hidden;
}

.emotion-class svg {
  margin: -5px -6px 0 -6px;
}

.emotion-class {
  fill: #E4E5EB;
  width: 29px;
  height: 29px;
  fill-rule: evenodd;
  vertical-align: middle;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  position: relative;
  background-color: transparent;
  padding: 0;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class a {
  display: block;
  padding: var(--spacing-l);
  text-align: center;
}

.emotion-class {
  position: relative;
}

.emotion-class svg {
  fill: #465AB6;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class {
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
  font-size: 13px;
  color: #465AB6;
  vertical-align: middle;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-top: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: 0 0 var(--spacing-xs) var(--spacing-xs);
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  width: 100%;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  height: 1px;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  border-radius: var(--spacing-s-plus);
  background-color: var(--borders-encapsulation-separator-divider);
}

.emotion-class>div {
  display: inline-block;
  margin-right: var(--spacing-s-plus);
}

.emotion-class select {
  font-size: var(--font-size-s);
  width: auto;
  padding: 0;
  min-height: auto;
  outline-width: 0;
  color: var(--actions-basic-actions-action-base);
  font-weight: 600;
  border: 0;
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 24px;
  line-height: 30px;
  color: var(--glyphs-basic-glyphs-base);
}

<div>
  <div
    style="margin: 40px;"
  >
    <div>
      <div
        class="emotion-class"
      >
        <table
          cellpadding="0"
          cellspacing="0"
          class="emotion-class"
          data-testid="LineItemsTableWithCf"
        >
          <tbody>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                style="text-align: left; width: 100%;"
              >
                <span
                  class="emotion-class"
                >
                  Payment_Line_Items_Item
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: left; white-space: nowrap; min-width: 128px;"
              >
                <span
                  class="emotion-class"
                >
                  Category
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 28px;"
              >
                <span
                  class="emotion-class"
                >
                  Qty
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 88px;"
              >
                <span
                  class="emotion-class"
                >
                  Price
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: center; min-width: 160px;"
              >
                <span
                  class="emotion-class"
                >
                  Deduction
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; white-space: nowrap; min-width: 80px;"
              >
                <span
                  class="emotion-class"
                >
                  Tax
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 140px;"
              >
                <span
                  class="emotion-class"
                >
                  Amount
                </span>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  maxlength="250"
                  placeholder="Add Invoice item..."
                  style="text-align: left;"
                  value="Line item #1"
                />
              </td>
              <td
                class="emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  <div
                    class="emotion-class"
                  >
                    <select
                      class="emotion-class"
                    >
                      <option
                        value=""
                      >
                        Select category...
                      </option>
                      <option
                        data-testid="SelectOption_1"
                        value="1"
                      >
                        Root Category 1
                      </option>
                      <option
                        data-testid="SelectOption_2"
                        value="2"
                      >
                        Work
                      </option>
                      <option
                        data-testid="SelectOption_3"
                        value="3"
                      >
                        Child Category 1.2
                      </option>
                      <option
                        data-testid="SelectOption_4"
                        value="4"
                      >
                        Root Category 2
                      </option>
                      <option
                        data-testid="SelectOption_5"
                        value="5"
                      >
                        Long category Name 33333333333
                      </option>
                    </select>
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                      class="emotion-class"
                      size="28"
                    />
                  </div>
                </span>
                <div
                  class="emotion-class"
                />
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0"
                  value="1.1111"
                />
                <div
                  class="emotion-class"
                >
                  1.11110
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0"
                  value="122"
                />
                <div
                  class="emotion-class"
                >
                  122
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <div
                  class="emotion-class"
                >
                  <input
                    class="emotion-class"
                    placeholder="0"
                    value="50"
                  />
                  <span
                    class="emotion-class"
                  >
                    <div
                      class="emotion-class"
                    >
                      <select
                        class="emotion-class"
                      >
                        <option
                          data-testid="SelectOption_"
                          value="%"
                        >
                          %
                        </option>
                        <option
                          data-testid="SelectOption_Yen"
                          value="YEN"
                        >
                          YEN
                        </option>
                      </select>
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                        class="emotion-class"
                        size="28"
                      />
                    </div>
                  </span>
                </div>
                <div
                  class="emotion-class"
                >
                  50
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  <div
                    class="emotion-class"
                  >
                    <select
                      class="emotion-class"
                    >
                      <option
                        data-testid="SelectOption_1"
                        value="1"
                      >
                        zw.
                      </option>
                      <option
                        data-testid="SelectOption_2"
                        value="2"
                      >
                        vat 5%
                      </option>
                      <option
                        data-testid="SelectOption_3"
                        value="3"
                      >
                        vat 23%
                      </option>
                      <option
                        data-testid="SelectOption_4"
                        value="4"
                      >
                        vat 7%
                      </option>
                      <option
                        data-testid="SelectOption_5"
                        value="5"
                      >
                        10%
                      </option>
                    </select>
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                      class="emotion-class"
                      size="28"
                    />
                  </div>
                </span>
                <div
                  class="emotion-class"
                >
                  vat 23%
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0 YEN"
                  style="font-weight: 600;"
                  value="68"
                />
              </td>
              <td
                class="LineItemContent_Delete emotion-class"
                data-testid="LineItemEdit_Delete"
              >
                <div>
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m21.5%2024l13%2032.5l15.5%2035l24%2026.5l32.5%2035l35%2032.5l26.5%2024l35%2015.5l32.5%2013l24%2021.5l15.5%2013l13%2015.5l21.5%2024z'/%3e%3c/svg%3e
                    classname="css-1lp8zdr"
                    fill="var(--statuses-status-initiated)"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <div
                  style="display: flex; justify-content: center;"
                >
                  <div
                    style="width: 100%;"
                  >
                    <p
                      class="emotion-class"
                      style="color: #FF7F8A; font-weight: 600;"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9.5%2023.9167c9.5%2031.8917%2016.025%2038.4167%2024%2038.4167c31.975%2038.4167%2038.5%2031.8917%2038.5%2023.9167c38.5%2015.9417%2031.975%209.41669%2024%209.41669c16.025%209.41669%209.5%2015.9417%209.5%2023.9167zm26.4997%2014.4167v17.9167l25.4997%2025.9167h22.4997l21.4997%2017.9167v14.4167h26.4997zm22.2242%2028.6411c21.7412%2029.1241%2021.4997%2029.7159%2021.4997%2030.4167c21.4997%2031.1175%2021.7412%2031.7093%2022.2242%2032.1923c22.7071%2032.6752%2023.299%2032.9167%2023.9997%2032.9167c24.7005%2032.9167%2025.2924%2032.6752%2025.7753%2032.1923c26.2583%2031.7093%2026.4997%2031.1175%2026.4997%2030.4167c26.4997%2029.7159%2026.2583%2029.1241%2025.7753%2028.6411c25.2924%2028.1582%2024.7005%2027.9167%2023.9997%2027.9167c23.299%2027.9167%2022.7071%2028.1582%2022.2242%2028.6411z'%20/%3e%3c/svg%3e
                        classname="css-1qygcm5"
                        fill="#FF7F8A"
                        size="24"
                      />
                      Fields required.
                    </p>
                    <span
                      class="emotion-class"
                      title="text line"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37%2019h11v22h37v19zm30.5%2025.5h11v28.5h30.5v25.5z'/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      text1
                    </span>
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                      title="text area"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m11%2012.5h37v15.5h11v12.5zm37%2019h11v22h37v19zm37%2025.5h11v28.5h37v25.5zm30.5%2032h11v35h30.5v32z'/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin vehicula erat venenatis odio pellentesque dignissim vitae ac nunc. Donec molestie dignissim lorem, quis volutpat sapien pretium ut. Phasellus sit amet nulla finibus, hendrerit urna id, lacinia est. Donec vitae neque id odio laoreet venenatis quis quis est. Ut congue elementum scelerisque. Vivamus ullamcorper libero eu consectetur efficitur. Morbi eu lobortis mauris. Mauris molestie, dui in scelerisque egestas, ante elit faucibus risus, sit amet dictum massa sem eu dolor. Cras feugiat sagittis lorem et viverra.

Nam in est lectus. Integer nec elit nulla. Sed diam elit, lobortis quis tincidunt nec, convallis in mi. Phasellus finibus id justo et sodales. Maecenas molestie velit eget nisi commodo, id molestie mi iaculis. Nulla hendrerit mi purus, ultricies ultricies nisi convallis at. Donec non maximus justo. Donec auctor tellus id nisi vehicula gravida ac non justo. Praesent nec volutpat mauris. Vivamus vel felis ut lorem aliquam imperdiet ut eu risus. Donec et consequat dui, vulputate hendrerit justo. Aliquam erat volutpat. Mauris eu odio ante.

Duis sit amet mi at ligula rutrum eleifend id et risus. Quisque dignissim nec orci semper dictum. Etiam malesuada eu orci ut tincidunt. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Nam a ex augue. Aliquam erat volutpat. Quisque elit lorem, mollis eu metus iaculis, faucibus viverra mi. Curabitur egestas sem et lacinia elementum. Maecenas eu sollicitudin nunc.
                    </span>
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                      title="dropdown"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m10%2035c10%2036.6569%2011.3431%2038%2013%2038h35c36.6569%2038%2038%2036.6569%2038%2035v13c38%2011.3431%2036.6569%2010%2035%2010h13c11.3431%2010%2010%2011.3431%2010%2013v35zm35%2013v35h13v13h35zm28%2025.5l24%2032.5l20%2025.5h28zm24%2015.5l28%2022.5h20l24%2015.5z'/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      Option F
                    </span>
                    <span
                      class="emotion-class"
                      title="Date"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m27%2010.5v8.5h31v10.5h34.5c36.1569%2010.5%2037.5%2011.8431%2037.5%2013.5v34.5c37.5%2036.1569%2036.1569%2037.5%2034.5%2037.5h13.5c11.8431%2037.5%2010.5%2036.1569%2010.5%2034.5v13.5c10.5%2011.8431%2011.8431%2010.5%2013.5%2010.5h17.5v8.5h21.5v10.5h27zm34.5%2034.5h13.5v18h34.5v34.5zm31.5%2025v32h22.5v25h31.5z'%20/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      01/26/2021
                    </span>
                    <span
                      class="emotion-class"
                      title="files"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      <span>
                        2.2.png
                        <span>
                          , 
                        </span>
                      </span>
                      <span>
                        long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-2.2.png
                        <span>
                          , 
                        </span>
                      </span>
                      <span>
                        9ZLqmuYwRIOCMJfG3vOk_33123.png
                      </span>
                    </span>
                    <span
                      class="emotion-class"
                      title="files"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      <span>
                        s.png
                      </span>
                    </span>
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="EmptyIconGrey emotion-class"
                      >
                        <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m14%2027c12.3431%2027%2011%2025.6569%2011%2024c11%2022.3431%2012.3431%2021%2014%2021h34c35.6569%2021%2037%2022.3431%2037%2024c37%2025.6569%2035.6569%2027%2034%2027h14z'/%3e%3c/svg%3e
                          class="emotion-class"
                          fill="#E4E5EB"
                        />
                      </span>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  maxlength="250"
                  placeholder="Add Invoice item..."
                  style="text-align: left;"
                  value="Line item #2"
                />
              </td>
              <td
                class="emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  <div
                    class="emotion-class"
                  >
                    <select
                      class="emotion-class"
                    >
                      <option
                        value=""
                      >
                        Select category...
                      </option>
                      <option
                        data-testid="SelectOption_1"
                        value="1"
                      >
                        Root Category 1
                      </option>
                      <option
                        data-testid="SelectOption_2"
                        value="2"
                      >
                        Work
                      </option>
                      <option
                        data-testid="SelectOption_3"
                        value="3"
                      >
                        Child Category 1.2
                      </option>
                      <option
                        data-testid="SelectOption_4"
                        value="4"
                      >
                        Root Category 2
                      </option>
                      <option
                        data-testid="SelectOption_5"
                        value="5"
                      >
                        Long category Name 33333333333
                      </option>
                    </select>
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                      class="emotion-class"
                      size="28"
                    />
                  </div>
                </span>
                <div
                  class="emotion-class"
                >
                  Root Category 1
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0"
                  value="2.11"
                />
                <div
                  class="emotion-class"
                >
                  2.11000
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0"
                  value="100"
                />
                <div
                  class="emotion-class"
                >
                  100
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <div
                  class="emotion-class"
                >
                  <input
                    class="emotion-class"
                    placeholder="0"
                    value=""
                  />
                  <span
                    class="emotion-class"
                  >
                    <div
                      class="emotion-class"
                    >
                      <select
                        class="emotion-class"
                      >
                        <option
                          data-testid="SelectOption_"
                          value="%"
                        >
                          %
                        </option>
                        <option
                          data-testid="SelectOption_Yen"
                          value="YEN"
                        >
                          YEN
                        </option>
                      </select>
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                        class="emotion-class"
                        size="28"
                      />
                    </div>
                  </span>
                </div>
                <div
                  class="emotion-class"
                />
              </td>
              <td
                class="emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  <div
                    class="emotion-class"
                  >
                    <select
                      class="emotion-class"
                    >
                      <option
                        data-testid="SelectOption_1"
                        value="1"
                      >
                        zw.
                      </option>
                      <option
                        data-testid="SelectOption_2"
                        value="2"
                      >
                        vat 5%
                      </option>
                      <option
                        data-testid="SelectOption_3"
                        value="3"
                      >
                        vat 23%
                      </option>
                      <option
                        data-testid="SelectOption_4"
                        value="4"
                      >
                        vat 7%
                      </option>
                      <option
                        data-testid="SelectOption_5"
                        value="5"
                      >
                        10%
                      </option>
                    </select>
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                      class="emotion-class"
                      size="28"
                    />
                  </div>
                </span>
                <div
                  class="emotion-class"
                >
                  zw.
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0 YEN"
                  style="font-weight: 600;"
                  value="211"
                />
              </td>
              <td
                class="LineItemContent_Delete emotion-class"
                data-testid="LineItemEdit_Delete"
              >
                <div>
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m21.5%2024l13%2032.5l15.5%2035l24%2026.5l32.5%2035l35%2032.5l26.5%2024l35%2015.5l32.5%2013l24%2021.5l15.5%2013l13%2015.5l21.5%2024z'/%3e%3c/svg%3e
                    classname="css-1lp8zdr"
                    fill="var(--statuses-status-initiated)"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <div
                  style="display: flex; justify-content: center;"
                >
                  <div
                    style="width: 100%;"
                  >
                    <p
                      class="emotion-class"
                      style="color: #FF7F8A; font-weight: 600;"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9.5%2023.9167c9.5%2031.8917%2016.025%2038.4167%2024%2038.4167c31.975%2038.4167%2038.5%2031.8917%2038.5%2023.9167c38.5%2015.9417%2031.975%209.41669%2024%209.41669c16.025%209.41669%209.5%2015.9417%209.5%2023.9167zm26.4997%2014.4167v17.9167l25.4997%2025.9167h22.4997l21.4997%2017.9167v14.4167h26.4997zm22.2242%2028.6411c21.7412%2029.1241%2021.4997%2029.7159%2021.4997%2030.4167c21.4997%2031.1175%2021.7412%2031.7093%2022.2242%2032.1923c22.7071%2032.6752%2023.299%2032.9167%2023.9997%2032.9167c24.7005%2032.9167%2025.2924%2032.6752%2025.7753%2032.1923c26.2583%2031.7093%2026.4997%2031.1175%2026.4997%2030.4167c26.4997%2029.7159%2026.2583%2029.1241%2025.7753%2028.6411c25.2924%2028.1582%2024.7005%2027.9167%2023.9997%2027.9167c23.299%2027.9167%2022.7071%2028.1582%2022.2242%2028.6411z'%20/%3e%3c/svg%3e
                        classname="css-1qygcm5"
                        fill="#FF7F8A"
                        size="24"
                      />
                      Fields required.
                    </p>
                    <span
                      class="emotion-class"
                      title="text line"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37%2019h11v22h37v19zm30.5%2025.5h11v28.5h30.5v25.5z'/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      text1
                    </span>
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                      title="text area"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m11%2012.5h37v15.5h11v12.5zm37%2019h11v22h37v19zm37%2025.5h11v28.5h37v25.5zm30.5%2032h11v35h30.5v32z'/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin vehicula erat venenatis odio pellentesque dignissim vitae ac nunc. Donec molestie dignissim lorem, quis volutpat sapien pretium ut. Phasellus sit amet nulla finibus, hendrerit urna id, lacinia est. Donec vitae neque id odio laoreet venenatis quis quis est. Ut congue elementum scelerisque. Vivamus ullamcorper libero eu consectetur efficitur. Morbi eu lobortis mauris. Mauris molestie, dui in scelerisque egestas, ante elit faucibus risus, sit amet dictum massa sem eu dolor. Cras feugiat sagittis lorem et viverra.

Nam in est lectus. Integer nec elit nulla. Sed diam elit, lobortis quis tincidunt nec, convallis in mi. Phasellus finibus id justo et sodales. Maecenas molestie velit eget nisi commodo, id molestie mi iaculis. Nulla hendrerit mi purus, ultricies ultricies nisi convallis at. Donec non maximus justo. Donec auctor tellus id nisi vehicula gravida ac non justo. Praesent nec volutpat mauris. Vivamus vel felis ut lorem aliquam imperdiet ut eu risus. Donec et consequat dui, vulputate hendrerit justo. Aliquam erat volutpat. Mauris eu odio ante.

Duis sit amet mi at ligula rutrum eleifend id et risus. Quisque dignissim nec orci semper dictum. Etiam malesuada eu orci ut tincidunt. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Nam a ex augue. Aliquam erat volutpat. Quisque elit lorem, mollis eu metus iaculis, faucibus viverra mi. Curabitur egestas sem et lacinia elementum. Maecenas eu sollicitudin nunc.
                    </span>
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                      title="dropdown"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m10%2035c10%2036.6569%2011.3431%2038%2013%2038h35c36.6569%2038%2038%2036.6569%2038%2035v13c38%2011.3431%2036.6569%2010%2035%2010h13c11.3431%2010%2010%2011.3431%2010%2013v35zm35%2013v35h13v13h35zm28%2025.5l24%2032.5l20%2025.5h28zm24%2015.5l28%2022.5h20l24%2015.5z'/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      Option F
                    </span>
                    <span
                      class="emotion-class"
                      title="Date"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m27%2010.5v8.5h31v10.5h34.5c36.1569%2010.5%2037.5%2011.8431%2037.5%2013.5v34.5c37.5%2036.1569%2036.1569%2037.5%2034.5%2037.5h13.5c11.8431%2037.5%2010.5%2036.1569%2010.5%2034.5v13.5c10.5%2011.8431%2011.8431%2010.5%2013.5%2010.5h17.5v8.5h21.5v10.5h27zm34.5%2034.5h13.5v18h34.5v34.5zm31.5%2025v32h22.5v25h31.5z'%20/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      01/26/2021
                    </span>
                    <span
                      class="emotion-class"
                      title="files"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      <span>
                        2.2.png
                        <span>
                          , 
                        </span>
                      </span>
                      <span>
                        long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-2.2.png
                        <span>
                          , 
                        </span>
                      </span>
                      <span>
                        9ZLqmuYwRIOCMJfG3vOk_33123.png
                      </span>
                    </span>
                    <span
                      class="emotion-class"
                      title="files"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      <span>
                        s.png
                      </span>
                    </span>
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="EmptyIconGrey emotion-class"
                      >
                        <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m14%2027c12.3431%2027%2011%2025.6569%2011%2024c11%2022.3431%2012.3431%2021%2014%2021h34c35.6569%2021%2037%2022.3431%2037%2024c37%2025.6569%2035.6569%2027%2034%2027h14z'/%3e%3c/svg%3e
                          class="emotion-class"
                          fill="#E4E5EB"
                        />
                      </span>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  maxlength="250"
                  placeholder="Add Invoice item..."
                  style="text-align: left;"
                  value="Line item #3"
                />
              </td>
              <td
                class="emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  <div
                    class="emotion-class"
                  >
                    <select
                      class="emotion-class"
                    >
                      <option
                        value=""
                      >
                        Select category...
                      </option>
                      <option
                        data-testid="SelectOption_1"
                        value="1"
                      >
                        Root Category 1
                      </option>
                      <option
                        data-testid="SelectOption_2"
                        value="2"
                      >
                        Work
                      </option>
                      <option
                        data-testid="SelectOption_3"
                        value="3"
                      >
                        Child Category 1.2
                      </option>
                      <option
                        data-testid="SelectOption_4"
                        value="4"
                      >
                        Root Category 2
                      </option>
                      <option
                        data-testid="SelectOption_5"
                        value="5"
                      >
                        Long category Name 33333333333
                      </option>
                    </select>
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                      class="emotion-class"
                      size="28"
                    />
                  </div>
                </span>
                <div
                  class="emotion-class"
                >
                  Work
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0"
                  value="10"
                />
                <div
                  class="emotion-class"
                >
                  10.00000
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0"
                  value="100"
                />
                <div
                  class="emotion-class"
                >
                  100
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <div
                  class="emotion-class"
                >
                  <input
                    class="emotion-class"
                    placeholder="0"
                    value="101"
                  />
                  <span
                    class="emotion-class"
                  >
                    <div
                      class="emotion-class"
                    >
                      <select
                        class="emotion-class"
                      >
                        <option
                          data-testid="SelectOption_"
                          value="%"
                        >
                          %
                        </option>
                        <option
                          data-testid="SelectOption_Yen"
                          value="YEN"
                        >
                          YEN
                        </option>
                      </select>
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                        class="emotion-class"
                        size="28"
                      />
                    </div>
                  </span>
                </div>
                <div
                  class="emotion-class"
                >
                  101
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <span
                  class="emotion-class"
                >
                  <div
                    class="emotion-class"
                  >
                    <select
                      class="emotion-class"
                    >
                      <option
                        data-testid="SelectOption_1"
                        value="1"
                      >
                        zw.
                      </option>
                      <option
                        data-testid="SelectOption_2"
                        value="2"
                      >
                        vat 5%
                      </option>
                      <option
                        data-testid="SelectOption_3"
                        value="3"
                      >
                        vat 23%
                      </option>
                      <option
                        data-testid="SelectOption_4"
                        value="4"
                      >
                        vat 7%
                      </option>
                      <option
                        data-testid="SelectOption_5"
                        value="5"
                      >
                        10%
                      </option>
                    </select>
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                      class="emotion-class"
                      size="28"
                    />
                  </div>
                </span>
                <div
                  class="emotion-class"
                >
                  zw.
                </div>
              </td>
              <td
                class="emotion-class"
              >
                <input
                  class="emotion-class"
                  placeholder="0 YEN"
                  style="font-weight: 600;"
                  value="900"
                />
              </td>
              <td
                class="LineItemContent_Delete emotion-class"
                data-testid="LineItemEdit_Delete"
              >
                <div>
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m21.5%2024l13%2032.5l15.5%2035l24%2026.5l32.5%2035l35%2032.5l26.5%2024l35%2015.5l32.5%2013l24%2021.5l15.5%2013l13%2015.5l21.5%2024z'/%3e%3c/svg%3e
                    classname="css-1lp8zdr"
                    fill="var(--statuses-status-initiated)"
                  />
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <div
                  style="display: flex; justify-content: center;"
                >
                  <div
                    style="width: 100%;"
                  >
                    <p
                      class="emotion-class"
                      style="color: #FF7F8A; font-weight: 600;"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m9.5%2023.9167c9.5%2031.8917%2016.025%2038.4167%2024%2038.4167c31.975%2038.4167%2038.5%2031.8917%2038.5%2023.9167c38.5%2015.9417%2031.975%209.41669%2024%209.41669c16.025%209.41669%209.5%2015.9417%209.5%2023.9167zm26.4997%2014.4167v17.9167l25.4997%2025.9167h22.4997l21.4997%2017.9167v14.4167h26.4997zm22.2242%2028.6411c21.7412%2029.1241%2021.4997%2029.7159%2021.4997%2030.4167c21.4997%2031.1175%2021.7412%2031.7093%2022.2242%2032.1923c22.7071%2032.6752%2023.299%2032.9167%2023.9997%2032.9167c24.7005%2032.9167%2025.2924%2032.6752%2025.7753%2032.1923c26.2583%2031.7093%2026.4997%2031.1175%2026.4997%2030.4167c26.4997%2029.7159%2026.2583%2029.1241%2025.7753%2028.6411c25.2924%2028.1582%2024.7005%2027.9167%2023.9997%2027.9167c23.299%2027.9167%2022.7071%2028.1582%2022.2242%2028.6411z'%20/%3e%3c/svg%3e
                        classname="css-1qygcm5"
                        fill="#FF7F8A"
                        size="24"
                      />
                      Fields required.
                    </p>
                    <span
                      class="emotion-class"
                      title="text line"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37%2019h11v22h37v19zm30.5%2025.5h11v28.5h30.5v25.5z'/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      text1
                    </span>
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                      title="text area"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m11%2012.5h37v15.5h11v12.5zm37%2019h11v22h37v19zm37%2025.5h11v28.5h37v25.5zm30.5%2032h11v35h30.5v32z'/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin vehicula erat venenatis odio pellentesque dignissim vitae ac nunc. Donec molestie dignissim lorem, quis volutpat sapien pretium ut. Phasellus sit amet nulla finibus, hendrerit urna id, lacinia est. Donec vitae neque id odio laoreet venenatis quis quis est. Ut congue elementum scelerisque. Vivamus ullamcorper libero eu consectetur efficitur. Morbi eu lobortis mauris. Mauris molestie, dui in scelerisque egestas, ante elit faucibus risus, sit amet dictum massa sem eu dolor. Cras feugiat sagittis lorem et viverra.

Nam in est lectus. Integer nec elit nulla. Sed diam elit, lobortis quis tincidunt nec, convallis in mi. Phasellus finibus id justo et sodales. Maecenas molestie velit eget nisi commodo, id molestie mi iaculis. Nulla hendrerit mi purus, ultricies ultricies nisi convallis at. Donec non maximus justo. Donec auctor tellus id nisi vehicula gravida ac non justo. Praesent nec volutpat mauris. Vivamus vel felis ut lorem aliquam imperdiet ut eu risus. Donec et consequat dui, vulputate hendrerit justo. Aliquam erat volutpat. Mauris eu odio ante.

Duis sit amet mi at ligula rutrum eleifend id et risus. Quisque dignissim nec orci semper dictum. Etiam malesuada eu orci ut tincidunt. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Nam a ex augue. Aliquam erat volutpat. Quisque elit lorem, mollis eu metus iaculis, faucibus viverra mi. Curabitur egestas sem et lacinia elementum. Maecenas eu sollicitudin nunc.
                    </span>
                    <span
                      class="emotion-class"
                    />
                    <span
                      class="emotion-class"
                      title="dropdown"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m10%2035c10%2036.6569%2011.3431%2038%2013%2038h35c36.6569%2038%2038%2036.6569%2038%2035v13c38%2011.3431%2036.6569%2010%2035%2010h13c11.3431%2010%2010%2011.3431%2010%2013v35zm35%2013v35h13v13h35zm28%2025.5l24%2032.5l20%2025.5h28zm24%2015.5l28%2022.5h20l24%2015.5z'/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      Option F
                    </span>
                    <span
                      class="emotion-class"
                      title="Date"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m27%2010.5v8.5h31v10.5h34.5c36.1569%2010.5%2037.5%2011.8431%2037.5%2013.5v34.5c37.5%2036.1569%2036.1569%2037.5%2034.5%2037.5h13.5c11.8431%2037.5%2010.5%2036.1569%2010.5%2034.5v13.5c10.5%2011.8431%2011.8431%2010.5%2013.5%2010.5h17.5v8.5h21.5v10.5h27zm34.5%2034.5h13.5v18h34.5v34.5zm31.5%2025v32h22.5v25h31.5z'%20/%3e%3c/svg%3e
                        classname="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      01/26/2021
                    </span>
                    <span
                      class="emotion-class"
                      title="files"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      <span>
                        2.2.png
                        <span>
                          , 
                        </span>
                      </span>
                      <span>
                        long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-2.2.png
                        <span>
                          , 
                        </span>
                      </span>
                      <span>
                        9ZLqmuYwRIOCMJfG3vOk_33123.png
                      </span>
                    </span>
                    <span
                      class="emotion-class"
                      title="files"
                    >
                      <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                        class="emotion-class"
                        fill="#A6ABBF"
                        size="24"
                      />
                      <span>
                        s.png
                      </span>
                    </span>
                    <div
                      class="emotion-class"
                    >
                      <span
                        class="EmptyIconGrey emotion-class"
                      >
                        <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m14%2027c12.3431%2027%2011%2025.6569%2011%2024c11%2022.3431%2012.3431%2021%2014%2021h34c35.6569%2021%2037%2022.3431%2037%2024c37%2025.6569%2035.6569%2027%2034%2027h14z'/%3e%3c/svg%3e
                          class="emotion-class"
                          fill="#E4E5EB"
                        />
                      </span>
                    </div>
                  </div>
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <span
                  class="emotion-class"
                >
                  <a
                    class=" emotion-class"
                  >
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m24%209.5c16.025%209.5%209.5%2016.025%209.5%2024c9.5%2031.975%2016.025%2038.5%2024%2038.5c31.975%2038.5%2038.5%2031.975%2038.5%2024c38.5%2016.025%2031.975%209.5%2024%209.5zm22.4513%2022.4513h15.6371v25.5487h22.4513v32.3628h25.5486v25.5487h32.3628v22.4513h25.5486v15.6372h22.4513v22.4513z'/%3e%3c/svg%3e
                      classname="css-12ecxzt"
                      size="20"
                      style="margin: 0px 5px 0px -2px;"
                    />
                    <span
                      class="emotion-class"
                    >
                      Add new item
                    </span>
                  </a>
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        class="emotion-class"
        data-testid="LineItemsTableWithCf_Footer"
      >
        <div
          class="emotion-class"
          data-testid="InvoiceDetails_Note"
        />
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
            >
              Total Deductions
            </span>
            <p
              class="emotion-class"
              data-testid="LineItemsTable_TotalDeductions"
            >
              169
               
              YEN
            </p>
          </div>
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
            >
              Subtotal (excl. tax)
            </span>
            <p
              class="emotion-class"
              data-testid="LineItemsTable_Subtotal"
            >
              1,179
               
              YEN
            </p>
          </div>
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
            >
              Total Tax
            </span>
            <p
              class="emotion-class"
              data-testid="LineItemsTable_TotalTax"
            >
              16
               
              YEN
            </p>
          </div>
          <div
            class="emotion-class"
          />
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
            >
              <div
                class="emotion-class"
              >
                <select
                  class="emotion-class"
                >
                  <option
                    data-testid="SelectOption_Aud"
                    value="AUD"
                  >
                    AUD
                  </option>
                  <option
                    data-testid="SelectOption_Cny"
                    value="CNY"
                  >
                    CNY
                  </option>
                  <option
                    data-testid="SelectOption_Gbp"
                    value="GBP"
                  >
                    GBP
                  </option>
                  <option
                    data-testid="SelectOption_Jpy"
                    value="JPY"
                  >
                    JPY
                  </option>
                  <option
                    data-testid="SelectOption_Yen"
                    value="YEN"
                  >
                    YEN
                  </option>
                  <option
                    data-testid="SelectOption_Usd"
                    value="USD"
                  >
                    USD
                  </option>
                </select>
                <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m24%2012l29.75%2021h18.25l24%2012z'%20/%3e%3cpath%20d='m24%2036l29.75%2027h18.25l24%2036z'%20/%3e%3c/svg%3e
                  class="emotion-class"
                  size="28"
                />
              </div>
            </span>
            <p
              class="emotion-class"
            >
              Total
            </p>
            <p
              class="emotion-class"
              data-testid="LineItemsTable_Total"
            >
              1,195
               
              YEN
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`LineItemsTableWithCF > snapshot should match: ReadOnly 1`] = `
.emotion-class {
  padding: 0 var(--spacing-m) var(--spacing-m);
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: var(--spacing-xs) var(--spacing-xs) 0 var(--spacing-xs);
  overflow-x: auto;
}

.emotion-class {
  display: table;
  width: 100%;
  border-spacing: 0 var(--spacing-s);
}

.emotion-class {
  display: table-row;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  padding: var(--spacing-s) var(--spacing-m);
  font-weight: 600;
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border: none;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class:first-of-type {
  border: none;
}

.emotion-class:last-of-type {
  border: none;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class {
  padding: 0 10px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  visibility: hidden;
  max-height: 0;
  white-space: nowrap;
  overflow-x: hidden;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: right;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: right;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-right: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-right-radius: var(--spacing-xs);
  border-bottom-right-radius: var(--spacing-xs);
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  position: relative;
  background-color: transparent;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #303757;
  display: inline-block;
  font-weight: 400;
  margin-right: 15px;
  padding-left: 25px;
  position: relative;
  padding-top: 2px;
  max-width: 400px;
}

.emotion-class svg {
  left: 0;
  top: 0;
  position: absolute;
}

.emotion-class a {
  color: #A6ABBF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class a:hover {
  color: #465AB6;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #303757;
  display: inline-block;
  font-weight: 400;
  margin-right: 15px;
  position: relative;
  padding-top: 2px;
  max-width: 400px;
}

.emotion-class svg {
  left: 0;
  top: 0;
  position: absolute;
}

.emotion-class a {
  color: #A6ABBF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class a:hover {
  color: #465AB6;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #303757;
  display: inline-block;
  font-weight: 400;
  margin-right: 15px;
  padding-left: 25px;
  position: relative;
  padding-top: 2px;
}

.emotion-class svg {
  left: 0;
  top: 0;
  position: absolute;
}

.emotion-class a {
  color: #A6ABBF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class a:hover {
  color: #465AB6;
}

.emotion-class>a,
.emotion-class>span {
  position: relative;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
  padding-right: 5px;
  margin-right: 5px;
}

.emotion-class>a span,
.emotion-class>span span {
  color: #A6ABBF;
  position: absolute;
  top: 0;
  right: 0;
}

.emotion-class {
  fill: #A6ABBF;
  width: 24px;
  height: 24px;
  fill-rule: evenodd;
  vertical-align: middle;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 35px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: 20px;
  height: 100%;
  overflow: hidden;
}

.emotion-class svg {
  margin: -5px -6px 0 -6px;
}

.emotion-class {
  fill: #E4E5EB;
  width: 29px;
  height: 29px;
  fill-rule: evenodd;
  vertical-align: middle;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-top: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: 0 0 var(--spacing-xs) var(--spacing-xs);
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  width: 100%;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  height: 1px;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  border-radius: var(--spacing-s-plus);
  background-color: var(--borders-encapsulation-separator-divider);
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 24px;
  line-height: 30px;
  color: var(--glyphs-basic-glyphs-base);
}

<div>
  <div
    style="margin: 40px;"
  >
    <div>
      <div
        class="emotion-class"
      >
        <table
          cellpadding="0"
          cellspacing="0"
          class="emotion-class"
          data-testid="LineItemsTableWithCf"
        >
          <tbody>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                style="text-align: left; width: 100%;"
              >
                <span
                  class="emotion-class"
                >
                  Payment_Line_Items_Item
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: left; white-space: nowrap; min-width: 128px;"
              >
                <span
                  class="emotion-class"
                >
                  Category
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 28px;"
              >
                <span
                  class="emotion-class"
                >
                  Qty
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 88px;"
              >
                <span
                  class="emotion-class"
                >
                  Price
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 80px;"
              >
                <span
                  class="emotion-class"
                >
                  Deduction
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 88px;"
              >
                <span
                  class="emotion-class"
                >
                  Amount
                </span>
              </td>
            </tr>
            <tr
              class="emotion-class"
              data-testid="LineItem"
            >
              <td
                class="emotion-class"
              >
                Line item #1
              </td>
              <td
                class="emotion-class"
              >
                <div
                  class="emotion-class"
                />
              </td>
              <td
                class="emotion-class"
              >
                1.1111
                <div
                  class="emotion-class"
                >
                  1.11110
                </div>
              </td>
              <td
                class="emotion-class"
              >
                122
                <div
                  class="emotion-class"
                >
                  122
                </div>
              </td>
              <td
                class="emotion-class"
              >
                50%
              </td>
              <td
                class="emotion-class"
              >
                68
                <div
                  class="emotion-class"
                >
                  68
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <span
                  class="emotion-class"
                  title="text line"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37%2019h11v22h37v19zm30.5%2025.5h11v28.5h30.5v25.5z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  text1
                </span>
                <span
                  class="emotion-class"
                />
                <span
                  class="emotion-class"
                  title="text area"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m11%2012.5h37v15.5h11v12.5zm37%2019h11v22h37v19zm37%2025.5h11v28.5h37v25.5zm30.5%2032h11v35h30.5v32z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin vehicula erat venenatis odio pellentesque dignissim vitae ac nunc. Donec molestie dignissim lorem, quis volutpat sapien pretium ut. Phasellus sit amet nulla finibus, hendrerit urna id, lacinia est. Donec vitae neque id odio laoreet venenatis quis quis est. Ut congue elementum scelerisque. Vivamus ullamcorper libero eu consectetur efficitur. Morbi eu lobortis mauris. Mauris molestie, dui in scelerisque egestas, ante elit faucibus risus, sit amet dictum massa sem eu dolor. Cras feugiat sagittis lorem et viverra.

Nam in est lectus. Integer nec elit nulla. Sed diam elit, lobortis quis tincidunt nec, convallis in mi. Phasellus finibus id justo et sodales. Maecenas molestie velit eget nisi commodo, id molestie mi iaculis. Nulla hendrerit mi purus, ultricies ultricies nisi convallis at. Donec non maximus justo. Donec auctor tellus id nisi vehicula gravida ac non justo. Praesent nec volutpat mauris. Vivamus vel felis ut lorem aliquam imperdiet ut eu risus. Donec et consequat dui, vulputate hendrerit justo. Aliquam erat volutpat. Mauris eu odio ante.

Duis sit amet mi at ligula rutrum eleifend id et risus. Quisque dignissim nec orci semper dictum. Etiam malesuada eu orci ut tincidunt. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Nam a ex augue. Aliquam erat volutpat. Quisque elit lorem, mollis eu metus iaculis, faucibus viverra mi. Curabitur egestas sem et lacinia elementum. Maecenas eu sollicitudin nunc.
                </span>
                <span
                  class="emotion-class"
                />
                <span
                  class="emotion-class"
                  title="dropdown"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m10%2035c10%2036.6569%2011.3431%2038%2013%2038h35c36.6569%2038%2038%2036.6569%2038%2035v13c38%2011.3431%2036.6569%2010%2035%2010h13c11.3431%2010%2010%2011.3431%2010%2013v35zm35%2013v35h13v13h35zm28%2025.5l24%2032.5l20%2025.5h28zm24%2015.5l28%2022.5h20l24%2015.5z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  Option F
                </span>
                <span
                  class="emotion-class"
                  title="Date"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m27%2010.5v8.5h31v10.5h34.5c36.1569%2010.5%2037.5%2011.8431%2037.5%2013.5v34.5c37.5%2036.1569%2036.1569%2037.5%2034.5%2037.5h13.5c11.8431%2037.5%2010.5%2036.1569%2010.5%2034.5v13.5c10.5%2011.8431%2011.8431%2010.5%2013.5%2010.5h17.5v8.5h21.5v10.5h27zm34.5%2034.5h13.5v18h34.5v34.5zm31.5%2025v32h22.5v25h31.5z'%20/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  01/26/2021
                </span>
                <span
                  class="emotion-class"
                  title="files"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                    class="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/1/download/?inline=0"
                    rel="noreferrer"
                  >
                    2.2.png
                    <span>
                      , 
                    </span>
                  </a>
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/2/download/?inline=0"
                    rel="noreferrer"
                  >
                    long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-2.2.png
                    <span>
                      , 
                    </span>
                  </a>
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/4/download/?inline=0"
                    rel="noreferrer"
                  >
                    9ZLqmuYwRIOCMJfG3vOk_33123.png
                  </a>
                </span>
                <span
                  class="emotion-class"
                  title="files"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                    class="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/3/download/?inline=0"
                    rel="noreferrer"
                  >
                    s.png
                  </a>
                </span>
                <div
                  class="emotion-class"
                >
                  <span
                    class="EmptyIconGrey emotion-class"
                  >
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m14%2027c12.3431%2027%2011%2025.6569%2011%2024c11%2022.3431%2012.3431%2021%2014%2021h34c35.6569%2021%2037%2022.3431%2037%2024c37%2025.6569%2035.6569%2027%2034%2027h14z'/%3e%3c/svg%3e
                      class="emotion-class"
                      fill="#E4E5EB"
                    />
                  </span>
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
              data-testid="LineItem"
            >
              <td
                class="emotion-class"
              >
                Line item #2
              </td>
              <td
                class="emotion-class"
              >
                Root Category 1
                <div
                  class="emotion-class"
                >
                  Root Category 1
                </div>
              </td>
              <td
                class="emotion-class"
              >
                2.11
                <div
                  class="emotion-class"
                >
                  2.11000
                </div>
              </td>
              <td
                class="emotion-class"
              >
                100
                <div
                  class="emotion-class"
                >
                  100
                </div>
              </td>
              <td
                class="emotion-class"
              >
                -
              </td>
              <td
                class="emotion-class"
              >
                211
                <div
                  class="emotion-class"
                >
                  211
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <span
                  class="emotion-class"
                  title="text line"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37%2019h11v22h37v19zm30.5%2025.5h11v28.5h30.5v25.5z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  text1
                </span>
                <span
                  class="emotion-class"
                />
                <span
                  class="emotion-class"
                  title="text area"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m11%2012.5h37v15.5h11v12.5zm37%2019h11v22h37v19zm37%2025.5h11v28.5h37v25.5zm30.5%2032h11v35h30.5v32z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin vehicula erat venenatis odio pellentesque dignissim vitae ac nunc. Donec molestie dignissim lorem, quis volutpat sapien pretium ut. Phasellus sit amet nulla finibus, hendrerit urna id, lacinia est. Donec vitae neque id odio laoreet venenatis quis quis est. Ut congue elementum scelerisque. Vivamus ullamcorper libero eu consectetur efficitur. Morbi eu lobortis mauris. Mauris molestie, dui in scelerisque egestas, ante elit faucibus risus, sit amet dictum massa sem eu dolor. Cras feugiat sagittis lorem et viverra.

Nam in est lectus. Integer nec elit nulla. Sed diam elit, lobortis quis tincidunt nec, convallis in mi. Phasellus finibus id justo et sodales. Maecenas molestie velit eget nisi commodo, id molestie mi iaculis. Nulla hendrerit mi purus, ultricies ultricies nisi convallis at. Donec non maximus justo. Donec auctor tellus id nisi vehicula gravida ac non justo. Praesent nec volutpat mauris. Vivamus vel felis ut lorem aliquam imperdiet ut eu risus. Donec et consequat dui, vulputate hendrerit justo. Aliquam erat volutpat. Mauris eu odio ante.

Duis sit amet mi at ligula rutrum eleifend id et risus. Quisque dignissim nec orci semper dictum. Etiam malesuada eu orci ut tincidunt. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Nam a ex augue. Aliquam erat volutpat. Quisque elit lorem, mollis eu metus iaculis, faucibus viverra mi. Curabitur egestas sem et lacinia elementum. Maecenas eu sollicitudin nunc.
                </span>
                <span
                  class="emotion-class"
                />
                <span
                  class="emotion-class"
                  title="dropdown"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m10%2035c10%2036.6569%2011.3431%2038%2013%2038h35c36.6569%2038%2038%2036.6569%2038%2035v13c38%2011.3431%2036.6569%2010%2035%2010h13c11.3431%2010%2010%2011.3431%2010%2013v35zm35%2013v35h13v13h35zm28%2025.5l24%2032.5l20%2025.5h28zm24%2015.5l28%2022.5h20l24%2015.5z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  Option F
                </span>
                <span
                  class="emotion-class"
                  title="Date"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m27%2010.5v8.5h31v10.5h34.5c36.1569%2010.5%2037.5%2011.8431%2037.5%2013.5v34.5c37.5%2036.1569%2036.1569%2037.5%2034.5%2037.5h13.5c11.8431%2037.5%2010.5%2036.1569%2010.5%2034.5v13.5c10.5%2011.8431%2011.8431%2010.5%2013.5%2010.5h17.5v8.5h21.5v10.5h27zm34.5%2034.5h13.5v18h34.5v34.5zm31.5%2025v32h22.5v25h31.5z'%20/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  01/26/2021
                </span>
                <span
                  class="emotion-class"
                  title="files"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                    class="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/1/download/?inline=0"
                    rel="noreferrer"
                  >
                    2.2.png
                    <span>
                      , 
                    </span>
                  </a>
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/2/download/?inline=0"
                    rel="noreferrer"
                  >
                    long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-2.2.png
                    <span>
                      , 
                    </span>
                  </a>
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/4/download/?inline=0"
                    rel="noreferrer"
                  >
                    9ZLqmuYwRIOCMJfG3vOk_33123.png
                  </a>
                </span>
                <span
                  class="emotion-class"
                  title="files"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                    class="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/3/download/?inline=0"
                    rel="noreferrer"
                  >
                    s.png
                  </a>
                </span>
                <div
                  class="emotion-class"
                >
                  <span
                    class="EmptyIconGrey emotion-class"
                  >
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m14%2027c12.3431%2027%2011%2025.6569%2011%2024c11%2022.3431%2012.3431%2021%2014%2021h34c35.6569%2021%2037%2022.3431%2037%2024c37%2025.6569%2035.6569%2027%2034%2027h14z'/%3e%3c/svg%3e
                      class="emotion-class"
                      fill="#E4E5EB"
                    />
                  </span>
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
              data-testid="LineItem"
            >
              <td
                class="emotion-class"
              >
                Line item #3
              </td>
              <td
                class="emotion-class"
              >
                Work
                <div
                  class="emotion-class"
                >
                  Work
                </div>
              </td>
              <td
                class="emotion-class"
              >
                10
                <div
                  class="emotion-class"
                >
                  10.00000
                </div>
              </td>
              <td
                class="emotion-class"
              >
                100
                <div
                  class="emotion-class"
                >
                  100
                </div>
              </td>
              <td
                class="emotion-class"
              >
                101
              </td>
              <td
                class="emotion-class"
              >
                900
                <div
                  class="emotion-class"
                >
                  900
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <span
                  class="emotion-class"
                  title="text line"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37%2019h11v22h37v19zm30.5%2025.5h11v28.5h30.5v25.5z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  text1
                </span>
                <span
                  class="emotion-class"
                />
                <span
                  class="emotion-class"
                  title="text area"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m11%2012.5h37v15.5h11v12.5zm37%2019h11v22h37v19zm37%2025.5h11v28.5h37v25.5zm30.5%2032h11v35h30.5v32z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin vehicula erat venenatis odio pellentesque dignissim vitae ac nunc. Donec molestie dignissim lorem, quis volutpat sapien pretium ut. Phasellus sit amet nulla finibus, hendrerit urna id, lacinia est. Donec vitae neque id odio laoreet venenatis quis quis est. Ut congue elementum scelerisque. Vivamus ullamcorper libero eu consectetur efficitur. Morbi eu lobortis mauris. Mauris molestie, dui in scelerisque egestas, ante elit faucibus risus, sit amet dictum massa sem eu dolor. Cras feugiat sagittis lorem et viverra.

Nam in est lectus. Integer nec elit nulla. Sed diam elit, lobortis quis tincidunt nec, convallis in mi. Phasellus finibus id justo et sodales. Maecenas molestie velit eget nisi commodo, id molestie mi iaculis. Nulla hendrerit mi purus, ultricies ultricies nisi convallis at. Donec non maximus justo. Donec auctor tellus id nisi vehicula gravida ac non justo. Praesent nec volutpat mauris. Vivamus vel felis ut lorem aliquam imperdiet ut eu risus. Donec et consequat dui, vulputate hendrerit justo. Aliquam erat volutpat. Mauris eu odio ante.

Duis sit amet mi at ligula rutrum eleifend id et risus. Quisque dignissim nec orci semper dictum. Etiam malesuada eu orci ut tincidunt. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Nam a ex augue. Aliquam erat volutpat. Quisque elit lorem, mollis eu metus iaculis, faucibus viverra mi. Curabitur egestas sem et lacinia elementum. Maecenas eu sollicitudin nunc.
                </span>
                <span
                  class="emotion-class"
                />
                <span
                  class="emotion-class"
                  title="dropdown"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m10%2035c10%2036.6569%2011.3431%2038%2013%2038h35c36.6569%2038%2038%2036.6569%2038%2035v13c38%2011.3431%2036.6569%2010%2035%2010h13c11.3431%2010%2010%2011.3431%2010%2013v35zm35%2013v35h13v13h35zm28%2025.5l24%2032.5l20%2025.5h28zm24%2015.5l28%2022.5h20l24%2015.5z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  Option F
                </span>
                <span
                  class="emotion-class"
                  title="Date"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m27%2010.5v8.5h31v10.5h34.5c36.1569%2010.5%2037.5%2011.8431%2037.5%2013.5v34.5c37.5%2036.1569%2036.1569%2037.5%2034.5%2037.5h13.5c11.8431%2037.5%2010.5%2036.1569%2010.5%2034.5v13.5c10.5%2011.8431%2011.8431%2010.5%2013.5%2010.5h17.5v8.5h21.5v10.5h27zm34.5%2034.5h13.5v18h34.5v34.5zm31.5%2025v32h22.5v25h31.5z'%20/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  01/26/2021
                </span>
                <span
                  class="emotion-class"
                  title="files"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                    class="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/1/download/?inline=0"
                    rel="noreferrer"
                  >
                    2.2.png
                    <span>
                      , 
                    </span>
                  </a>
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/2/download/?inline=0"
                    rel="noreferrer"
                  >
                    long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-2.2.png
                    <span>
                      , 
                    </span>
                  </a>
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/4/download/?inline=0"
                    rel="noreferrer"
                  >
                    9ZLqmuYwRIOCMJfG3vOk_33123.png
                  </a>
                </span>
                <span
                  class="emotion-class"
                  title="files"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                    class="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/3/download/?inline=0"
                    rel="noreferrer"
                  >
                    s.png
                  </a>
                </span>
                <div
                  class="emotion-class"
                >
                  <span
                    class="EmptyIconGrey emotion-class"
                  >
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m14%2027c12.3431%2027%2011%2025.6569%2011%2024c11%2022.3431%2012.3431%2021%2014%2021h34c35.6569%2021%2037%2022.3431%2037%2024c37%2025.6569%2035.6569%2027%2034%2027h14z'/%3e%3c/svg%3e
                      class="emotion-class"
                      fill="#E4E5EB"
                    />
                  </span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        class="emotion-class"
        data-testid="LineItemsTableWithCf_Footer"
      >
        <div
          class="emotion-class"
          data-testid="InvoiceDetails_Note"
        />
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
            >
              Total Deductions
            </span>
            <p
              class="emotion-class"
              data-testid="LineItemsTable_TotalDeductions"
            >
              169
               
              YEN
            </p>
          </div>
          <div
            class="emotion-class"
          />
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
            >
              Total
            </p>
            <p
              class="emotion-class"
              data-testid="LineItemsTable_Total"
            >
              1,179
               
              YEN
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`LineItemsTableWithCF > snapshot should match: ReadOnlyNoDeduction 1`] = `
.emotion-class {
  padding: 0 var(--spacing-m) var(--spacing-m);
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: var(--spacing-xs) var(--spacing-xs) 0 var(--spacing-xs);
  overflow-x: auto;
}

.emotion-class {
  display: table;
  width: 100%;
  border-spacing: 0 var(--spacing-s);
}

.emotion-class {
  display: table-row;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  padding: var(--spacing-s) var(--spacing-m);
  font-weight: 600;
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border: none;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class:first-of-type {
  border: none;
}

.emotion-class:last-of-type {
  border: none;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class {
  padding: 0 10px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  visibility: hidden;
  max-height: 0;
  white-space: nowrap;
  overflow-x: hidden;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: right;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: right;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-right: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-right-radius: var(--spacing-xs);
  border-bottom-right-radius: var(--spacing-xs);
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  position: relative;
  background-color: transparent;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #303757;
  display: inline-block;
  font-weight: 400;
  margin-right: 15px;
  padding-left: 25px;
  position: relative;
  padding-top: 2px;
  max-width: 400px;
}

.emotion-class svg {
  left: 0;
  top: 0;
  position: absolute;
}

.emotion-class a {
  color: #A6ABBF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class a:hover {
  color: #465AB6;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #303757;
  display: inline-block;
  font-weight: 400;
  margin-right: 15px;
  position: relative;
  padding-top: 2px;
  max-width: 400px;
}

.emotion-class svg {
  left: 0;
  top: 0;
  position: absolute;
}

.emotion-class a {
  color: #A6ABBF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class a:hover {
  color: #465AB6;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #303757;
  display: inline-block;
  font-weight: 400;
  margin-right: 15px;
  padding-left: 25px;
  position: relative;
  padding-top: 2px;
}

.emotion-class svg {
  left: 0;
  top: 0;
  position: absolute;
}

.emotion-class a {
  color: #A6ABBF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class a:hover {
  color: #465AB6;
}

.emotion-class>a,
.emotion-class>span {
  position: relative;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
  padding-right: 5px;
  margin-right: 5px;
}

.emotion-class>a span,
.emotion-class>span span {
  color: #A6ABBF;
  position: absolute;
  top: 0;
  right: 0;
}

.emotion-class {
  fill: #A6ABBF;
  width: 24px;
  height: 24px;
  fill-rule: evenodd;
  vertical-align: middle;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 35px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: 20px;
  height: 100%;
  overflow: hidden;
}

.emotion-class svg {
  margin: -5px -6px 0 -6px;
}

.emotion-class {
  fill: #E4E5EB;
  width: 29px;
  height: 29px;
  fill-rule: evenodd;
  vertical-align: middle;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-top: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: 0 0 var(--spacing-xs) var(--spacing-xs);
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  width: 100%;
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 24px;
  line-height: 30px;
  color: var(--glyphs-basic-glyphs-base);
}

<div>
  <div
    style="margin: 40px;"
  >
    <div>
      <div
        class="emotion-class"
      >
        <table
          cellpadding="0"
          cellspacing="0"
          class="emotion-class"
          data-testid="LineItemsTableWithCf"
        >
          <tbody>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                style="text-align: left; width: 100%;"
              >
                <span
                  class="emotion-class"
                >
                  Payment_Line_Items_Item
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: left; white-space: nowrap; min-width: 128px;"
              >
                <span
                  class="emotion-class"
                >
                  Category
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 28px;"
              >
                <span
                  class="emotion-class"
                >
                  Qty
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 88px;"
              >
                <span
                  class="emotion-class"
                >
                  Price
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 88px;"
              >
                <span
                  class="emotion-class"
                >
                  Amount
                </span>
              </td>
            </tr>
            <tr
              class="emotion-class"
              data-testid="LineItem"
            >
              <td
                class="emotion-class"
              >
                Line item #1
              </td>
              <td
                class="emotion-class"
              >
                <div
                  class="emotion-class"
                />
              </td>
              <td
                class="emotion-class"
              >
                1.1111
                <div
                  class="emotion-class"
                >
                  1.11110
                </div>
              </td>
              <td
                class="emotion-class"
              >
                122
                <div
                  class="emotion-class"
                >
                  122
                </div>
              </td>
              <td
                class="emotion-class"
              >
                68
                <div
                  class="emotion-class"
                >
                  68
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <span
                  class="emotion-class"
                  title="text line"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37%2019h11v22h37v19zm30.5%2025.5h11v28.5h30.5v25.5z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  text1
                </span>
                <span
                  class="emotion-class"
                />
                <span
                  class="emotion-class"
                  title="text area"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m11%2012.5h37v15.5h11v12.5zm37%2019h11v22h37v19zm37%2025.5h11v28.5h37v25.5zm30.5%2032h11v35h30.5v32z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin vehicula erat venenatis odio pellentesque dignissim vitae ac nunc. Donec molestie dignissim lorem, quis volutpat sapien pretium ut. Phasellus sit amet nulla finibus, hendrerit urna id, lacinia est. Donec vitae neque id odio laoreet venenatis quis quis est. Ut congue elementum scelerisque. Vivamus ullamcorper libero eu consectetur efficitur. Morbi eu lobortis mauris. Mauris molestie, dui in scelerisque egestas, ante elit faucibus risus, sit amet dictum massa sem eu dolor. Cras feugiat sagittis lorem et viverra.

Nam in est lectus. Integer nec elit nulla. Sed diam elit, lobortis quis tincidunt nec, convallis in mi. Phasellus finibus id justo et sodales. Maecenas molestie velit eget nisi commodo, id molestie mi iaculis. Nulla hendrerit mi purus, ultricies ultricies nisi convallis at. Donec non maximus justo. Donec auctor tellus id nisi vehicula gravida ac non justo. Praesent nec volutpat mauris. Vivamus vel felis ut lorem aliquam imperdiet ut eu risus. Donec et consequat dui, vulputate hendrerit justo. Aliquam erat volutpat. Mauris eu odio ante.

Duis sit amet mi at ligula rutrum eleifend id et risus. Quisque dignissim nec orci semper dictum. Etiam malesuada eu orci ut tincidunt. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Nam a ex augue. Aliquam erat volutpat. Quisque elit lorem, mollis eu metus iaculis, faucibus viverra mi. Curabitur egestas sem et lacinia elementum. Maecenas eu sollicitudin nunc.
                </span>
                <span
                  class="emotion-class"
                />
                <span
                  class="emotion-class"
                  title="dropdown"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m10%2035c10%2036.6569%2011.3431%2038%2013%2038h35c36.6569%2038%2038%2036.6569%2038%2035v13c38%2011.3431%2036.6569%2010%2035%2010h13c11.3431%2010%2010%2011.3431%2010%2013v35zm35%2013v35h13v13h35zm28%2025.5l24%2032.5l20%2025.5h28zm24%2015.5l28%2022.5h20l24%2015.5z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  Option F
                </span>
                <span
                  class="emotion-class"
                  title="Date"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m27%2010.5v8.5h31v10.5h34.5c36.1569%2010.5%2037.5%2011.8431%2037.5%2013.5v34.5c37.5%2036.1569%2036.1569%2037.5%2034.5%2037.5h13.5c11.8431%2037.5%2010.5%2036.1569%2010.5%2034.5v13.5c10.5%2011.8431%2011.8431%2010.5%2013.5%2010.5h17.5v8.5h21.5v10.5h27zm34.5%2034.5h13.5v18h34.5v34.5zm31.5%2025v32h22.5v25h31.5z'%20/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  01/26/2021
                </span>
                <span
                  class="emotion-class"
                  title="files"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                    class="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/1/download/?inline=0"
                    rel="noreferrer"
                  >
                    2.2.png
                    <span>
                      , 
                    </span>
                  </a>
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/2/download/?inline=0"
                    rel="noreferrer"
                  >
                    long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-2.2.png
                    <span>
                      , 
                    </span>
                  </a>
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/4/download/?inline=0"
                    rel="noreferrer"
                  >
                    9ZLqmuYwRIOCMJfG3vOk_33123.png
                  </a>
                </span>
                <span
                  class="emotion-class"
                  title="files"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                    class="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/3/download/?inline=0"
                    rel="noreferrer"
                  >
                    s.png
                  </a>
                </span>
                <div
                  class="emotion-class"
                >
                  <span
                    class="EmptyIconGrey emotion-class"
                  >
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m14%2027c12.3431%2027%2011%2025.6569%2011%2024c11%2022.3431%2012.3431%2021%2014%2021h34c35.6569%2021%2037%2022.3431%2037%2024c37%2025.6569%2035.6569%2027%2034%2027h14z'/%3e%3c/svg%3e
                      class="emotion-class"
                      fill="#E4E5EB"
                    />
                  </span>
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
              data-testid="LineItem"
            >
              <td
                class="emotion-class"
              >
                Line item #2
              </td>
              <td
                class="emotion-class"
              >
                Root Category 1
                <div
                  class="emotion-class"
                >
                  Root Category 1
                </div>
              </td>
              <td
                class="emotion-class"
              >
                2.11
                <div
                  class="emotion-class"
                >
                  2.11000
                </div>
              </td>
              <td
                class="emotion-class"
              >
                100
                <div
                  class="emotion-class"
                >
                  100
                </div>
              </td>
              <td
                class="emotion-class"
              >
                211
                <div
                  class="emotion-class"
                >
                  211
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <span
                  class="emotion-class"
                  title="text line"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37%2019h11v22h37v19zm30.5%2025.5h11v28.5h30.5v25.5z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  text1
                </span>
                <span
                  class="emotion-class"
                />
                <span
                  class="emotion-class"
                  title="text area"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m11%2012.5h37v15.5h11v12.5zm37%2019h11v22h37v19zm37%2025.5h11v28.5h37v25.5zm30.5%2032h11v35h30.5v32z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin vehicula erat venenatis odio pellentesque dignissim vitae ac nunc. Donec molestie dignissim lorem, quis volutpat sapien pretium ut. Phasellus sit amet nulla finibus, hendrerit urna id, lacinia est. Donec vitae neque id odio laoreet venenatis quis quis est. Ut congue elementum scelerisque. Vivamus ullamcorper libero eu consectetur efficitur. Morbi eu lobortis mauris. Mauris molestie, dui in scelerisque egestas, ante elit faucibus risus, sit amet dictum massa sem eu dolor. Cras feugiat sagittis lorem et viverra.

Nam in est lectus. Integer nec elit nulla. Sed diam elit, lobortis quis tincidunt nec, convallis in mi. Phasellus finibus id justo et sodales. Maecenas molestie velit eget nisi commodo, id molestie mi iaculis. Nulla hendrerit mi purus, ultricies ultricies nisi convallis at. Donec non maximus justo. Donec auctor tellus id nisi vehicula gravida ac non justo. Praesent nec volutpat mauris. Vivamus vel felis ut lorem aliquam imperdiet ut eu risus. Donec et consequat dui, vulputate hendrerit justo. Aliquam erat volutpat. Mauris eu odio ante.

Duis sit amet mi at ligula rutrum eleifend id et risus. Quisque dignissim nec orci semper dictum. Etiam malesuada eu orci ut tincidunt. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Nam a ex augue. Aliquam erat volutpat. Quisque elit lorem, mollis eu metus iaculis, faucibus viverra mi. Curabitur egestas sem et lacinia elementum. Maecenas eu sollicitudin nunc.
                </span>
                <span
                  class="emotion-class"
                />
                <span
                  class="emotion-class"
                  title="dropdown"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m10%2035c10%2036.6569%2011.3431%2038%2013%2038h35c36.6569%2038%2038%2036.6569%2038%2035v13c38%2011.3431%2036.6569%2010%2035%2010h13c11.3431%2010%2010%2011.3431%2010%2013v35zm35%2013v35h13v13h35zm28%2025.5l24%2032.5l20%2025.5h28zm24%2015.5l28%2022.5h20l24%2015.5z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  Option F
                </span>
                <span
                  class="emotion-class"
                  title="Date"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m27%2010.5v8.5h31v10.5h34.5c36.1569%2010.5%2037.5%2011.8431%2037.5%2013.5v34.5c37.5%2036.1569%2036.1569%2037.5%2034.5%2037.5h13.5c11.8431%2037.5%2010.5%2036.1569%2010.5%2034.5v13.5c10.5%2011.8431%2011.8431%2010.5%2013.5%2010.5h17.5v8.5h21.5v10.5h27zm34.5%2034.5h13.5v18h34.5v34.5zm31.5%2025v32h22.5v25h31.5z'%20/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  01/26/2021
                </span>
                <span
                  class="emotion-class"
                  title="files"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                    class="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/1/download/?inline=0"
                    rel="noreferrer"
                  >
                    2.2.png
                    <span>
                      , 
                    </span>
                  </a>
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/2/download/?inline=0"
                    rel="noreferrer"
                  >
                    long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-2.2.png
                    <span>
                      , 
                    </span>
                  </a>
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/4/download/?inline=0"
                    rel="noreferrer"
                  >
                    9ZLqmuYwRIOCMJfG3vOk_33123.png
                  </a>
                </span>
                <span
                  class="emotion-class"
                  title="files"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                    class="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/3/download/?inline=0"
                    rel="noreferrer"
                  >
                    s.png
                  </a>
                </span>
                <div
                  class="emotion-class"
                >
                  <span
                    class="EmptyIconGrey emotion-class"
                  >
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m14%2027c12.3431%2027%2011%2025.6569%2011%2024c11%2022.3431%2012.3431%2021%2014%2021h34c35.6569%2021%2037%2022.3431%2037%2024c37%2025.6569%2035.6569%2027%2034%2027h14z'/%3e%3c/svg%3e
                      class="emotion-class"
                      fill="#E4E5EB"
                    />
                  </span>
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
              data-testid="LineItem"
            >
              <td
                class="emotion-class"
              >
                Line item #3
              </td>
              <td
                class="emotion-class"
              >
                Work
                <div
                  class="emotion-class"
                >
                  Work
                </div>
              </td>
              <td
                class="emotion-class"
              >
                10
                <div
                  class="emotion-class"
                >
                  10.00000
                </div>
              </td>
              <td
                class="emotion-class"
              >
                100
                <div
                  class="emotion-class"
                >
                  100
                </div>
              </td>
              <td
                class="emotion-class"
              >
                900
                <div
                  class="emotion-class"
                >
                  900
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <span
                  class="emotion-class"
                  title="text line"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37%2019h11v22h37v19zm30.5%2025.5h11v28.5h30.5v25.5z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  text1
                </span>
                <span
                  class="emotion-class"
                />
                <span
                  class="emotion-class"
                  title="text area"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m11%2012.5h37v15.5h11v12.5zm37%2019h11v22h37v19zm37%2025.5h11v28.5h37v25.5zm30.5%2032h11v35h30.5v32z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin vehicula erat venenatis odio pellentesque dignissim vitae ac nunc. Donec molestie dignissim lorem, quis volutpat sapien pretium ut. Phasellus sit amet nulla finibus, hendrerit urna id, lacinia est. Donec vitae neque id odio laoreet venenatis quis quis est. Ut congue elementum scelerisque. Vivamus ullamcorper libero eu consectetur efficitur. Morbi eu lobortis mauris. Mauris molestie, dui in scelerisque egestas, ante elit faucibus risus, sit amet dictum massa sem eu dolor. Cras feugiat sagittis lorem et viverra.

Nam in est lectus. Integer nec elit nulla. Sed diam elit, lobortis quis tincidunt nec, convallis in mi. Phasellus finibus id justo et sodales. Maecenas molestie velit eget nisi commodo, id molestie mi iaculis. Nulla hendrerit mi purus, ultricies ultricies nisi convallis at. Donec non maximus justo. Donec auctor tellus id nisi vehicula gravida ac non justo. Praesent nec volutpat mauris. Vivamus vel felis ut lorem aliquam imperdiet ut eu risus. Donec et consequat dui, vulputate hendrerit justo. Aliquam erat volutpat. Mauris eu odio ante.

Duis sit amet mi at ligula rutrum eleifend id et risus. Quisque dignissim nec orci semper dictum. Etiam malesuada eu orci ut tincidunt. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Nam a ex augue. Aliquam erat volutpat. Quisque elit lorem, mollis eu metus iaculis, faucibus viverra mi. Curabitur egestas sem et lacinia elementum. Maecenas eu sollicitudin nunc.
                </span>
                <span
                  class="emotion-class"
                />
                <span
                  class="emotion-class"
                  title="dropdown"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m10%2035c10%2036.6569%2011.3431%2038%2013%2038h35c36.6569%2038%2038%2036.6569%2038%2035v13c38%2011.3431%2036.6569%2010%2035%2010h13c11.3431%2010%2010%2011.3431%2010%2013v35zm35%2013v35h13v13h35zm28%2025.5l24%2032.5l20%2025.5h28zm24%2015.5l28%2022.5h20l24%2015.5z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  Option F
                </span>
                <span
                  class="emotion-class"
                  title="Date"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m27%2010.5v8.5h31v10.5h34.5c36.1569%2010.5%2037.5%2011.8431%2037.5%2013.5v34.5c37.5%2036.1569%2036.1569%2037.5%2034.5%2037.5h13.5c11.8431%2037.5%2010.5%2036.1569%2010.5%2034.5v13.5c10.5%2011.8431%2011.8431%2010.5%2013.5%2010.5h17.5v8.5h21.5v10.5h27zm34.5%2034.5h13.5v18h34.5v34.5zm31.5%2025v32h22.5v25h31.5z'%20/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  01/26/2021
                </span>
                <span
                  class="emotion-class"
                  title="files"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                    class="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/1/download/?inline=0"
                    rel="noreferrer"
                  >
                    2.2.png
                    <span>
                      , 
                    </span>
                  </a>
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/2/download/?inline=0"
                    rel="noreferrer"
                  >
                    long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-2.2.png
                    <span>
                      , 
                    </span>
                  </a>
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/4/download/?inline=0"
                    rel="noreferrer"
                  >
                    9ZLqmuYwRIOCMJfG3vOk_33123.png
                  </a>
                </span>
                <span
                  class="emotion-class"
                  title="files"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                    class="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/3/download/?inline=0"
                    rel="noreferrer"
                  >
                    s.png
                  </a>
                </span>
                <div
                  class="emotion-class"
                >
                  <span
                    class="EmptyIconGrey emotion-class"
                  >
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m14%2027c12.3431%2027%2011%2025.6569%2011%2024c11%2022.3431%2012.3431%2021%2014%2021h34c35.6569%2021%2037%2022.3431%2037%2024c37%2025.6569%2035.6569%2027%2034%2027h14z'/%3e%3c/svg%3e
                      class="emotion-class"
                      fill="#E4E5EB"
                    />
                  </span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        class="emotion-class"
        data-testid="LineItemsTableWithCf_Footer"
      >
        <div
          class="emotion-class"
          data-testid="InvoiceDetails_Note"
        />
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
            >
              Total
            </p>
            <p
              class="emotion-class"
              data-testid="LineItemsTable_Total"
            >
              1,179
               
              YEN
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`LineItemsTableWithCF > snapshot should match: ReadOnlyWithTax 1`] = `
.emotion-class {
  padding: 0 var(--spacing-m) var(--spacing-m);
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: var(--spacing-xs) var(--spacing-xs) 0 var(--spacing-xs);
  overflow-x: auto;
}

.emotion-class {
  display: table;
  width: 100%;
  border-spacing: 0 var(--spacing-s);
}

.emotion-class {
  display: table-row;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  padding: var(--spacing-s) var(--spacing-m);
  font-weight: 600;
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border: none;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class:first-of-type {
  border: none;
}

.emotion-class:last-of-type {
  border: none;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class {
  padding: 0 10px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  visibility: hidden;
  max-height: 0;
  white-space: nowrap;
  overflow-x: hidden;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: right;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: right;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-right: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-right-radius: var(--spacing-xs);
  border-bottom-right-radius: var(--spacing-xs);
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  color: var(--glyphs-basic-glyphs-base);
  display: table-cell;
  background-color: var(--fills-canvas-fills-basic-canvas-fill-base);
  padding: var(--spacing-m);
  border-top: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-bottom: 1px solid var(--borders-canvas-borders-canvas-border-base);
  position: relative;
  background-color: transparent;
}

.emotion-class:first-of-type {
  font-weight: 600;
  border-left: 1px solid var(--borders-canvas-borders-canvas-border-base);
  border-top-left-radius: var(--spacing-xs);
  border-bottom-left-radius: var(--spacing-xs);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #303757;
  display: inline-block;
  font-weight: 400;
  margin-right: 15px;
  padding-left: 25px;
  position: relative;
  padding-top: 2px;
  max-width: 400px;
}

.emotion-class svg {
  left: 0;
  top: 0;
  position: absolute;
}

.emotion-class a {
  color: #A6ABBF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class a:hover {
  color: #465AB6;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #303757;
  display: inline-block;
  font-weight: 400;
  margin-right: 15px;
  position: relative;
  padding-top: 2px;
  max-width: 400px;
}

.emotion-class svg {
  left: 0;
  top: 0;
  position: absolute;
}

.emotion-class a {
  color: #A6ABBF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class a:hover {
  color: #465AB6;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  color: #303757;
  display: inline-block;
  font-weight: 400;
  margin-right: 15px;
  padding-left: 25px;
  position: relative;
  padding-top: 2px;
}

.emotion-class svg {
  left: 0;
  top: 0;
  position: absolute;
}

.emotion-class a {
  color: #A6ABBF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-class a:hover {
  color: #465AB6;
}

.emotion-class>a,
.emotion-class>span {
  position: relative;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
  padding-right: 5px;
  margin-right: 5px;
}

.emotion-class>a span,
.emotion-class>span span {
  color: #A6ABBF;
  position: absolute;
  top: 0;
  right: 0;
}

.emotion-class {
  fill: #A6ABBF;
  width: 24px;
  height: 24px;
  fill-rule: evenodd;
  vertical-align: middle;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 35px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: 20px;
  height: 100%;
  overflow: hidden;
}

.emotion-class svg {
  margin: -5px -6px 0 -6px;
}

.emotion-class {
  fill: #E4E5EB;
  width: 29px;
  height: 29px;
  fill-rule: evenodd;
  vertical-align: middle;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-top: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: var(--spacing-m);
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-s);
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  background-color: var(--fills-canvas-fills-alerts-canvas-fill-neutral);
  border-radius: 0 0 var(--spacing-xs) var(--spacing-xs);
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: var(--spacing-m);
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  width: 100%;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-base);
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: var(--glyphs-basic-glyphs-subdued);
}

.emotion-class {
  height: 1px;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  border-radius: var(--spacing-s-plus);
  background-color: var(--borders-encapsulation-separator-divider);
}

.emotion-class {
  font-family: "Montserrat",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  font-size: 30px;
  line-height: 40px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 24px;
  line-height: 30px;
  color: var(--glyphs-basic-glyphs-base);
}

<div>
  <div
    style="margin: 40px;"
  >
    <div>
      <div
        class="emotion-class"
      >
        <table
          cellpadding="0"
          cellspacing="0"
          class="emotion-class"
          data-testid="LineItemsTableWithCf"
        >
          <tbody>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                style="text-align: left; width: 100%;"
              >
                <span
                  class="emotion-class"
                >
                  Payment_Line_Items_Item
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: left; white-space: nowrap; min-width: 128px;"
              >
                <span
                  class="emotion-class"
                >
                  Category
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 28px;"
              >
                <span
                  class="emotion-class"
                >
                  Qty
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 88px;"
              >
                <span
                  class="emotion-class"
                >
                  Price
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 80px;"
              >
                <span
                  class="emotion-class"
                >
                  Deduction
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; white-space: nowrap; min-width: 80px;"
              >
                <span
                  class="emotion-class"
                >
                  Tax
                </span>
              </td>
              <td
                class="emotion-class"
                style="text-align: right; min-width: 88px;"
              >
                <span
                  class="emotion-class"
                >
                  Amount
                </span>
              </td>
            </tr>
            <tr
              class="emotion-class"
              data-testid="LineItem"
            >
              <td
                class="emotion-class"
              >
                Line item #1
              </td>
              <td
                class="emotion-class"
              >
                <div
                  class="emotion-class"
                />
              </td>
              <td
                class="emotion-class"
              >
                1.1111
                <div
                  class="emotion-class"
                >
                  1.11110
                </div>
              </td>
              <td
                class="emotion-class"
              >
                122
                <div
                  class="emotion-class"
                >
                  122
                </div>
              </td>
              <td
                class="emotion-class"
              >
                50%
              </td>
              <td
                class="emotion-class"
              >
                23%
                <div
                  class="emotion-class"
                >
                  23%
                </div>
              </td>
              <td
                class="emotion-class"
              >
                68
                <div
                  class="emotion-class"
                >
                  68
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <span
                  class="emotion-class"
                  title="text line"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37%2019h11v22h37v19zm30.5%2025.5h11v28.5h30.5v25.5z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  text1
                </span>
                <span
                  class="emotion-class"
                />
                <span
                  class="emotion-class"
                  title="text area"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m11%2012.5h37v15.5h11v12.5zm37%2019h11v22h37v19zm37%2025.5h11v28.5h37v25.5zm30.5%2032h11v35h30.5v32z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin vehicula erat venenatis odio pellentesque dignissim vitae ac nunc. Donec molestie dignissim lorem, quis volutpat sapien pretium ut. Phasellus sit amet nulla finibus, hendrerit urna id, lacinia est. Donec vitae neque id odio laoreet venenatis quis quis est. Ut congue elementum scelerisque. Vivamus ullamcorper libero eu consectetur efficitur. Morbi eu lobortis mauris. Mauris molestie, dui in scelerisque egestas, ante elit faucibus risus, sit amet dictum massa sem eu dolor. Cras feugiat sagittis lorem et viverra.

Nam in est lectus. Integer nec elit nulla. Sed diam elit, lobortis quis tincidunt nec, convallis in mi. Phasellus finibus id justo et sodales. Maecenas molestie velit eget nisi commodo, id molestie mi iaculis. Nulla hendrerit mi purus, ultricies ultricies nisi convallis at. Donec non maximus justo. Donec auctor tellus id nisi vehicula gravida ac non justo. Praesent nec volutpat mauris. Vivamus vel felis ut lorem aliquam imperdiet ut eu risus. Donec et consequat dui, vulputate hendrerit justo. Aliquam erat volutpat. Mauris eu odio ante.

Duis sit amet mi at ligula rutrum eleifend id et risus. Quisque dignissim nec orci semper dictum. Etiam malesuada eu orci ut tincidunt. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Nam a ex augue. Aliquam erat volutpat. Quisque elit lorem, mollis eu metus iaculis, faucibus viverra mi. Curabitur egestas sem et lacinia elementum. Maecenas eu sollicitudin nunc.
                </span>
                <span
                  class="emotion-class"
                />
                <span
                  class="emotion-class"
                  title="dropdown"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m10%2035c10%2036.6569%2011.3431%2038%2013%2038h35c36.6569%2038%2038%2036.6569%2038%2035v13c38%2011.3431%2036.6569%2010%2035%2010h13c11.3431%2010%2010%2011.3431%2010%2013v35zm35%2013v35h13v13h35zm28%2025.5l24%2032.5l20%2025.5h28zm24%2015.5l28%2022.5h20l24%2015.5z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  Option F
                </span>
                <span
                  class="emotion-class"
                  title="Date"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m27%2010.5v8.5h31v10.5h34.5c36.1569%2010.5%2037.5%2011.8431%2037.5%2013.5v34.5c37.5%2036.1569%2036.1569%2037.5%2034.5%2037.5h13.5c11.8431%2037.5%2010.5%2036.1569%2010.5%2034.5v13.5c10.5%2011.8431%2011.8431%2010.5%2013.5%2010.5h17.5v8.5h21.5v10.5h27zm34.5%2034.5h13.5v18h34.5v34.5zm31.5%2025v32h22.5v25h31.5z'%20/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  01/26/2021
                </span>
                <span
                  class="emotion-class"
                  title="files"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                    class="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/1/download/?inline=0"
                    rel="noreferrer"
                  >
                    2.2.png
                    <span>
                      , 
                    </span>
                  </a>
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/2/download/?inline=0"
                    rel="noreferrer"
                  >
                    long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-2.2.png
                    <span>
                      , 
                    </span>
                  </a>
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/4/download/?inline=0"
                    rel="noreferrer"
                  >
                    9ZLqmuYwRIOCMJfG3vOk_33123.png
                  </a>
                </span>
                <span
                  class="emotion-class"
                  title="files"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                    class="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/3/download/?inline=0"
                    rel="noreferrer"
                  >
                    s.png
                  </a>
                </span>
                <div
                  class="emotion-class"
                >
                  <span
                    class="EmptyIconGrey emotion-class"
                  >
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m14%2027c12.3431%2027%2011%2025.6569%2011%2024c11%2022.3431%2012.3431%2021%2014%2021h34c35.6569%2021%2037%2022.3431%2037%2024c37%2025.6569%2035.6569%2027%2034%2027h14z'/%3e%3c/svg%3e
                      class="emotion-class"
                      fill="#E4E5EB"
                    />
                  </span>
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
              data-testid="LineItem"
            >
              <td
                class="emotion-class"
              >
                Line item #2
              </td>
              <td
                class="emotion-class"
              >
                Root Category 1
                <div
                  class="emotion-class"
                >
                  Root Category 1
                </div>
              </td>
              <td
                class="emotion-class"
              >
                2.11
                <div
                  class="emotion-class"
                >
                  2.11000
                </div>
              </td>
              <td
                class="emotion-class"
              >
                100
                <div
                  class="emotion-class"
                >
                  100
                </div>
              </td>
              <td
                class="emotion-class"
              >
                -
              </td>
              <td
                class="emotion-class"
              >
                -
                <div
                  class="emotion-class"
                >
                  -
                </div>
              </td>
              <td
                class="emotion-class"
              >
                211
                <div
                  class="emotion-class"
                >
                  211
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <span
                  class="emotion-class"
                  title="text line"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37%2019h11v22h37v19zm30.5%2025.5h11v28.5h30.5v25.5z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  text1
                </span>
                <span
                  class="emotion-class"
                />
                <span
                  class="emotion-class"
                  title="text area"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m11%2012.5h37v15.5h11v12.5zm37%2019h11v22h37v19zm37%2025.5h11v28.5h37v25.5zm30.5%2032h11v35h30.5v32z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin vehicula erat venenatis odio pellentesque dignissim vitae ac nunc. Donec molestie dignissim lorem, quis volutpat sapien pretium ut. Phasellus sit amet nulla finibus, hendrerit urna id, lacinia est. Donec vitae neque id odio laoreet venenatis quis quis est. Ut congue elementum scelerisque. Vivamus ullamcorper libero eu consectetur efficitur. Morbi eu lobortis mauris. Mauris molestie, dui in scelerisque egestas, ante elit faucibus risus, sit amet dictum massa sem eu dolor. Cras feugiat sagittis lorem et viverra.

Nam in est lectus. Integer nec elit nulla. Sed diam elit, lobortis quis tincidunt nec, convallis in mi. Phasellus finibus id justo et sodales. Maecenas molestie velit eget nisi commodo, id molestie mi iaculis. Nulla hendrerit mi purus, ultricies ultricies nisi convallis at. Donec non maximus justo. Donec auctor tellus id nisi vehicula gravida ac non justo. Praesent nec volutpat mauris. Vivamus vel felis ut lorem aliquam imperdiet ut eu risus. Donec et consequat dui, vulputate hendrerit justo. Aliquam erat volutpat. Mauris eu odio ante.

Duis sit amet mi at ligula rutrum eleifend id et risus. Quisque dignissim nec orci semper dictum. Etiam malesuada eu orci ut tincidunt. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Nam a ex augue. Aliquam erat volutpat. Quisque elit lorem, mollis eu metus iaculis, faucibus viverra mi. Curabitur egestas sem et lacinia elementum. Maecenas eu sollicitudin nunc.
                </span>
                <span
                  class="emotion-class"
                />
                <span
                  class="emotion-class"
                  title="dropdown"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m10%2035c10%2036.6569%2011.3431%2038%2013%2038h35c36.6569%2038%2038%2036.6569%2038%2035v13c38%2011.3431%2036.6569%2010%2035%2010h13c11.3431%2010%2010%2011.3431%2010%2013v35zm35%2013v35h13v13h35zm28%2025.5l24%2032.5l20%2025.5h28zm24%2015.5l28%2022.5h20l24%2015.5z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  Option F
                </span>
                <span
                  class="emotion-class"
                  title="Date"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m27%2010.5v8.5h31v10.5h34.5c36.1569%2010.5%2037.5%2011.8431%2037.5%2013.5v34.5c37.5%2036.1569%2036.1569%2037.5%2034.5%2037.5h13.5c11.8431%2037.5%2010.5%2036.1569%2010.5%2034.5v13.5c10.5%2011.8431%2011.8431%2010.5%2013.5%2010.5h17.5v8.5h21.5v10.5h27zm34.5%2034.5h13.5v18h34.5v34.5zm31.5%2025v32h22.5v25h31.5z'%20/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  01/26/2021
                </span>
                <span
                  class="emotion-class"
                  title="files"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                    class="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/1/download/?inline=0"
                    rel="noreferrer"
                  >
                    2.2.png
                    <span>
                      , 
                    </span>
                  </a>
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/2/download/?inline=0"
                    rel="noreferrer"
                  >
                    long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-2.2.png
                    <span>
                      , 
                    </span>
                  </a>
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/4/download/?inline=0"
                    rel="noreferrer"
                  >
                    9ZLqmuYwRIOCMJfG3vOk_33123.png
                  </a>
                </span>
                <span
                  class="emotion-class"
                  title="files"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                    class="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/3/download/?inline=0"
                    rel="noreferrer"
                  >
                    s.png
                  </a>
                </span>
                <div
                  class="emotion-class"
                >
                  <span
                    class="EmptyIconGrey emotion-class"
                  >
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m14%2027c12.3431%2027%2011%2025.6569%2011%2024c11%2022.3431%2012.3431%2021%2014%2021h34c35.6569%2021%2037%2022.3431%2037%2024c37%2025.6569%2035.6569%2027%2034%2027h14z'/%3e%3c/svg%3e
                      class="emotion-class"
                      fill="#E4E5EB"
                    />
                  </span>
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
              data-testid="LineItem"
            >
              <td
                class="emotion-class"
              >
                Line item #3
              </td>
              <td
                class="emotion-class"
              >
                Work
                <div
                  class="emotion-class"
                >
                  Work
                </div>
              </td>
              <td
                class="emotion-class"
              >
                10
                <div
                  class="emotion-class"
                >
                  10.00000
                </div>
              </td>
              <td
                class="emotion-class"
              >
                100
                <div
                  class="emotion-class"
                >
                  100
                </div>
              </td>
              <td
                class="emotion-class"
              >
                101
              </td>
              <td
                class="emotion-class"
              >
                -
                <div
                  class="emotion-class"
                >
                  -
                </div>
              </td>
              <td
                class="emotion-class"
              >
                900
                <div
                  class="emotion-class"
                >
                  900
                </div>
              </td>
            </tr>
            <tr
              class="emotion-class"
            >
              <td
                class="emotion-class"
                colspan="100"
              >
                <span
                  class="emotion-class"
                  title="text line"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m37%2019h11v22h37v19zm30.5%2025.5h11v28.5h30.5v25.5z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  text1
                </span>
                <span
                  class="emotion-class"
                />
                <span
                  class="emotion-class"
                  title="text area"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m11%2012.5h37v15.5h11v12.5zm37%2019h11v22h37v19zm37%2025.5h11v28.5h37v25.5zm30.5%2032h11v35h30.5v32z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin vehicula erat venenatis odio pellentesque dignissim vitae ac nunc. Donec molestie dignissim lorem, quis volutpat sapien pretium ut. Phasellus sit amet nulla finibus, hendrerit urna id, lacinia est. Donec vitae neque id odio laoreet venenatis quis quis est. Ut congue elementum scelerisque. Vivamus ullamcorper libero eu consectetur efficitur. Morbi eu lobortis mauris. Mauris molestie, dui in scelerisque egestas, ante elit faucibus risus, sit amet dictum massa sem eu dolor. Cras feugiat sagittis lorem et viverra.

Nam in est lectus. Integer nec elit nulla. Sed diam elit, lobortis quis tincidunt nec, convallis in mi. Phasellus finibus id justo et sodales. Maecenas molestie velit eget nisi commodo, id molestie mi iaculis. Nulla hendrerit mi purus, ultricies ultricies nisi convallis at. Donec non maximus justo. Donec auctor tellus id nisi vehicula gravida ac non justo. Praesent nec volutpat mauris. Vivamus vel felis ut lorem aliquam imperdiet ut eu risus. Donec et consequat dui, vulputate hendrerit justo. Aliquam erat volutpat. Mauris eu odio ante.

Duis sit amet mi at ligula rutrum eleifend id et risus. Quisque dignissim nec orci semper dictum. Etiam malesuada eu orci ut tincidunt. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Nam a ex augue. Aliquam erat volutpat. Quisque elit lorem, mollis eu metus iaculis, faucibus viverra mi. Curabitur egestas sem et lacinia elementum. Maecenas eu sollicitudin nunc.
                </span>
                <span
                  class="emotion-class"
                />
                <span
                  class="emotion-class"
                  title="dropdown"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m10%2035c10%2036.6569%2011.3431%2038%2013%2038h35c36.6569%2038%2038%2036.6569%2038%2035v13c38%2011.3431%2036.6569%2010%2035%2010h13c11.3431%2010%2010%2011.3431%2010%2013v35zm35%2013v35h13v13h35zm28%2025.5l24%2032.5l20%2025.5h28zm24%2015.5l28%2022.5h20l24%2015.5z'/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  Option F
                </span>
                <span
                  class="emotion-class"
                  title="Date"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='m27%2010.5v8.5h31v10.5h34.5c36.1569%2010.5%2037.5%2011.8431%2037.5%2013.5v34.5c37.5%2036.1569%2036.1569%2037.5%2034.5%2037.5h13.5c11.8431%2037.5%2010.5%2036.1569%2010.5%2034.5v13.5c10.5%2011.8431%2011.8431%2010.5%2013.5%2010.5h17.5v8.5h21.5v10.5h27zm34.5%2034.5h13.5v18h34.5v34.5zm31.5%2025v32h22.5v25h31.5z'%20/%3e%3c/svg%3e
                    classname="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  01/26/2021
                </span>
                <span
                  class="emotion-class"
                  title="files"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                    class="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/1/download/?inline=0"
                    rel="noreferrer"
                  >
                    2.2.png
                    <span>
                      , 
                    </span>
                  </a>
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/2/download/?inline=0"
                    rel="noreferrer"
                  >
                    long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-long--filename-2.2.png
                    <span>
                      , 
                    </span>
                  </a>
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/4/download/?inline=0"
                    rel="noreferrer"
                  >
                    9ZLqmuYwRIOCMJfG3vOk_33123.png
                  </a>
                </span>
                <span
                  class="emotion-class"
                  title="files"
                >
                  <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m12.8076%2025.391l23.4803%2014.7995c26.5719%2011.7315%2031.5852%2011.734%2034.6787%2014.804c37.7682%2017.8699%2037.7758%2022.848%2034.6893%2025.911l25.4943%2035.0359l23.7255%2033.2805l32.9204%2024.1556c35.0288%2022.0632%2035.0236%2018.6571%2032.9098%2016.5594c30.7928%2014.4585%2027.3634%2014.4568%2025.2492%2016.5549l14.5765%2027.1464c13.203%2028.5094%2013.1467%2030.6943%2014.4021%2032.1188c15.8418%2033.3695%2018.0306%2033.3148%2019.4014%2031.9544l27.8407%2023.5793c28.4198%2023.0046%2028.4508%2022.0849%2027.9418%2021.4883c27.3365%2020.9784%2026.4212%2021.0091%2025.8446%2021.5813l18.8906%2028.4824l17.1217%2026.727l24.0757%2019.8259c25.628%2018.2855%2028.1276%2018.2507%2029.7133%2019.731c31.1995%2021.2995%2031.1641%2023.792%2029.6096%2025.3347l21.1703%2033.7098c18.8219%2036.0403%2015.0493%2036.095%2012.6306%2033.8763c10.3998%2031.4806%2010.4571%2027.7236%2012.8076%2025.391z'/%3e%3c/svg%3e
                    class="emotion-class"
                    fill="#A6ABBF"
                    size="24"
                  />
                  <a
                    download=""
                    href="/api/payments/1/line_items_files/3/download/?inline=0"
                    rel="noreferrer"
                  >
                    s.png
                  </a>
                </span>
                <div
                  class="emotion-class"
                >
                  <span
                    class="EmptyIconGrey emotion-class"
                  >
                    <data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewbox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='m14%2027c12.3431%2027%2011%2025.6569%2011%2024c11%2022.3431%2012.3431%2021%2014%2021h34c35.6569%2021%2037%2022.3431%2037%2024c37%2025.6569%2035.6569%2027%2034%2027h14z'/%3e%3c/svg%3e
                      class="emotion-class"
                      fill="#E4E5EB"
                    />
                  </span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div
        class="emotion-class"
        data-testid="LineItemsTableWithCf_Footer"
      >
        <div
          class="emotion-class"
          data-testid="InvoiceDetails_Note"
        />
        <div
          class="emotion-class"
        >
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
            >
              Total Deductions
            </span>
            <p
              class="emotion-class"
              data-testid="LineItemsTable_TotalDeductions"
            >
              169
               
              YEN
            </p>
          </div>
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
            >
              Subtotal (excl. tax)
            </span>
            <p
              class="emotion-class"
              data-testid="LineItemsTable_Subtotal"
            >
              1,179
               
              YEN
            </p>
          </div>
          <div
            class="emotion-class"
          >
            <span
              class="emotion-class"
            >
              Total Tax
            </span>
            <p
              class="emotion-class"
              data-testid="LineItemsTable_TotalTax"
            >
              16
               
              YEN
            </p>
          </div>
          <div
            class="emotion-class"
          />
          <div
            class="emotion-class"
          >
            <p
              class="emotion-class"
            >
              Total
            </p>
            <p
              class="emotion-class"
              data-testid="LineItemsTable_Total"
            >
              1,195
               
              YEN
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
