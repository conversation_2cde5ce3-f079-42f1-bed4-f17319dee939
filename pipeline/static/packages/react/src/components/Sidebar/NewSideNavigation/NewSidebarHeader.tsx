import { Small } from '../../../assets/icons';
import {
  NavMenuIcon,
  NotificationIcon,
  RemoveIcon,
} from '../../../assets/icons/small';
import i18n from '../../../i18n/i18n';
import Api from '../../../services/Api';
import Notifications from '../../../services/Api/Notifications';
import { goToStateOrOpenInNewTab } from '../../../services/Reducers/AngularMigration.helpers';
import { noop } from '../../../utils';
import { makeCancelable } from '../../../utils/other';
import { generateTestId } from '../../../utils/test.utils';
import Button from '../../FormElements/Button/Button';
import NotificationList from '../../Lists/NotificationList/NotificationList';
import DropdownWrapper from '../../Utils/DropdownWrapper/DropdownWrapper';
import TenantLogo from '../../Utils/TenantLogo/TenantLogo';
import * as Styles from './NewSideNavigation.styles';
import React, { useCallback, useEffect, useState } from 'react';

const NewSidebarHeader = ({
  headerRef,
  isMobile,
  mobileMenuOpened,
  onSetMobileMenuOpened,
  menuMinimizedManually,
  searchOpened,
  onSetSearchOpened,
  menuExpandedOnHover,
  onSetMenuExpandedOnHover,
  onSetMenuMinimizedManually,
  notificationOpened,
  onSetNotificationOpened,
  showNotifications,
  showSearch,
  onSetIsMenuMinimizedInLocalStorage,
  isAppWideSearchEnabled,
}: {
  headerRef: React.Ref<HTMLDivElement>;
  isMobile: boolean;
  mobileMenuOpened: boolean;
  onSetMobileMenuOpened: (data: boolean) => void;
  menuMinimizedManually: boolean;
  searchOpened: boolean;
  onSetSearchOpened: (data: boolean) => void;
  menuExpandedOnHover: boolean;
  onSetMenuExpandedOnHover: (data: boolean) => void;
  onSetMenuMinimizedManually: (data: boolean) => void;
  notificationOpened: boolean;
  onSetNotificationOpened: (data: boolean) => void;
  showNotifications: boolean;
  showSearch: boolean;
  onSetIsMenuMinimizedInLocalStorage: (data: string) => void;
  isAppWideSearchEnabled: boolean;
}) => {
  const [unreadNotificationsCount, setUnreadNotificationsCount] = useState(0);
  const reloadNotificationList = useCallback(() => {
    const cancelablePromise = makeCancelable(Api.Notifications.unreadCount());
    cancelablePromise.promise
      .then((count) => {
        setUnreadNotificationsCount(count);
      })
      .catch(noop);
    return () => cancelablePromise.cancel();
  }, []);

  const showMinimizeButton =
    !isMobile &&
    !(searchOpened && !menuMinimizedManually && menuExpandedOnHover);

  useEffect(() => {
    window.addEventListener('focus', reloadNotificationList);
    return () => {
      window.removeEventListener('focus', reloadNotificationList);
    };
  }, []);

  useEffect(reloadNotificationList, []);

  return (
    <Styles.HeaderWrapper ref={headerRef} isMobile={isMobile}>
      {isMobile && (
        <Styles.HamburgerMenu
          {...generateTestId('Hamburger', 'SideNavigation')}
          onClick={() => onSetMobileMenuOpened(!mobileMenuOpened)}
        >
          {mobileMenuOpened ? (
            <RemoveIcon
              size={40}
              className={isMobile ? 'active' : 'inactive'}
            />
          ) : (
            <NavMenuIcon
              size={40}
              className={isMobile ? 'active' : 'inactive'}
            />
          )}
        </Styles.HamburgerMenu>
      )}

      <Styles.HeaderInner>
        {!menuMinimizedManually && (
          <Styles.HeaderLogo>
            <TenantLogo variant="new-side-navigation" />
          </Styles.HeaderLogo>
        )}

        {showMinimizeButton && (
          <Styles.MinimizeButton
            {...generateTestId(
              `MinimizeButton_${menuMinimizedManually ? 'Show' : 'Hide'}`,
              'SideNavigation'
            )}
            className="minimizeButton canvas-base"
            onClick={() => {
              if (menuMinimizedManually) {
                onSetMenuMinimizedManually(false);
                onSetIsMenuMinimizedInLocalStorage('');
              } else {
                onSetMenuMinimizedManually(true);
                onSetIsMenuMinimizedInLocalStorage('minimized');
              }
              onSetMenuExpandedOnHover(false);
            }}
          >
            {menuMinimizedManually ? (
              <Small.NavigationShownIcon size={24} />
            ) : (
              <Small.NavigationHiddenIcon size={24} />
            )}
          </Styles.MinimizeButton>
        )}

        <Styles.HeaderIcons className="smoothOpacityAnimation">
          {showNotifications && (
            <DropdownWrapper
              content={({ handleClose }) => (
                <>
                  {notificationOpened && (
                    <NotificationList
                      itemsLimit={5}
                      variant="widget"
                      hideDescription={true}
                    />
                  )}
                  <Styles.NotificationListSeeAll>
                    <Button
                      testId={
                        generateTestId('SeeAllNotifications', 'SideNavigation')[
                          'data-testid'
                        ]
                      }
                      label={i18n.t('SideNavigation:SeeAllNotifications')}
                      onClick={(event) => {
                        handleClose(event);
                        onSetMobileMenuOpened(false);
                        goToStateOrOpenInNewTab('app.notifications')(event);
                      }}
                      variant="linkSmall"
                    />
                  </Styles.NotificationListSeeAll>
                </>
              )}
              trigger={
                <Styles.NotificationIconWrapper>
                  <NotificationIcon
                    {...generateTestId('OpenNotifications', 'SideNavigation')}
                    size={30}
                    className={
                      isMobile || notificationOpened ? 'active' : 'inactive'
                    }
                    onClick={() => {
                      Notifications.markAsRead()
                        .then(() => {
                          setUnreadNotificationsCount(0);
                        })
                        .catch(noop);
                    }}
                  />
                  {unreadNotificationsCount > 0 && (
                    <Styles.NotificationUnreadIcon />
                  )}
                </Styles.NotificationIconWrapper>
              }
              options={{
                withMargins: false,
                navAppWideSearchEnabled: isAppWideSearchEnabled,
              }}
              popoverAnchor={{ vertical: 'top', horizontal: 'left' }}
              themeOptions={{
                customVariant: isMobile
                  ? 'sideNavNotificationsMobile'
                  : 'sideNavNotifications',
              }}
              onDropdownOpen={() => {
                onSetNotificationOpened(true);
                onSetSearchOpened(false);
              }}
              onDropdownClose={() => {
                setTimeout(() => onSetNotificationOpened(false), 500);
                onSetMenuExpandedOnHover(false);
              }}
            />
          )}

          {showSearch && (
            <Styles.StyledSearchIcon
              {...generateTestId('OpenSearch', 'SideNavigation')}
              size={30}
              className={isMobile ? 'active' : 'inactive'}
              onClick={() => {
                onSetSearchOpened(!searchOpened);
              }}
            />
          )}
        </Styles.HeaderIcons>
      </Styles.HeaderInner>
    </Styles.HeaderWrapper>
  );
};

export default NewSidebarHeader;
