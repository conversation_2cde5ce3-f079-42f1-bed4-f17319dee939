import * as stories from '../NewSideNavigation/NewSideNavigation.stories';
import { composeStories } from '@storybook/react';
import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';

class ResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
}
describe('NewSideNavigation - ', () => {
  beforeAll(() => {
    window.ResizeObserver = ResizeObserver;
  });

  test('snapshot for vendor should match', async () => {
    const { MenuForVendor: SideNavigation } = composeStories(stories);
    const { container } = render(<SideNavigation />);
    const element = await screen.findByText(
      'Link dla suppliera2',
      {},
      { timeout: 5000 }
    );
    expect(element).toBeVisible();
    expect(container).toMatchSnapshot();
  });

  test('snapshot for contracts soon should match', async () => {
    const { ContractsSoon: NewSideNavigation } = composeStories(stories);
    const { container } = render(<NewSideNavigation />);
    const element1 = await screen.findByText('koz2');
    expect(element1).toBeVisible();
    const element2 = await screen.findByText('Soon');
    expect(element2).toBeVisible();
    expect(container).toMatchSnapshot();
  });

  test('click on section should show subsection', async () => {
    const { Default: SideNavigation } = composeStories(stories);
    const { container } = render(<SideNavigation />);
    const element = await screen.findByText('koz2');
    expect(element).toBeVisible();
    fireEvent.click(
      screen.getByTestId('SideNavigation_SidebarItemRecruitment')
    );
    const element2 = await screen.findByText('All Job openings');
    expect(element2).toBeVisible();
    expect(container).toMatchSnapshot();
  });

  test('click on notification icon should open notification panel', async () => {
    const { Default: SideNavigation } = composeStories(stories);
    const { container } = render(<SideNavigation />);
    const element = await screen.findByText('koz2');
    expect(element).toBeVisible();
    fireEvent.click(screen.getByTestId('SideNavigation_OpenNotifications'));
    const element2 = await screen.findByText('a a has joined your Worksuite');
    expect(element2).toBeVisible();
    expect(container).toMatchSnapshot();
  });

  test('click on search icon should open search panel', async () => {
    const { Default: SideNavigation } = composeStories(stories);
    const { container } = render(<SideNavigation />);
    const element = await screen.findByText('koz2');
    expect(element).toBeVisible();
    const openSearch = await screen.findByTestId('SideNavigation_OpenSearch');
    fireEvent.click(openSearch);
    const placeholder = await screen.findByPlaceholderText(
      'Start typing to search...'
    );
    expect(placeholder).toBeVisible();
    expect(container).toMatchSnapshot();
  });

  test('clicking on minimize icon should toggle menu', async () => {
    const { Default: SideNavigation } = composeStories(stories);
    const { container } = render(<SideNavigation />);
    const element = await screen.findByText('koz2');
    expect(element).toBeVisible();
    fireEvent.click(screen.getByTestId('SideNavigation_MinimizeButtonHide'));
    const MinimizeButtonShowId = await screen.findByTestId(
      'SideNavigation_MinimizeButtonShow'
    );
    expect(MinimizeButtonShowId).toBeVisible();
    expect(
      screen.queryByTestId('SideNavigation_MinimizeButtonHide')
    ).toBeNull();
    expect(screen.queryByText('Tenant Name & Placeholder')).toBeNull();
    fireEvent.click(screen.getByTestId('SideNavigation_MinimizeButtonShow'));
    const element2 = await screen.findByText('Tenant Name & Placeholder');
    expect(element2).toBeVisible();
    expect(screen.getByText('koz2')).toBeVisible();
    expect(container).toMatchSnapshot();
  });

  test('click on settings icon should open settings panel', async () => {
    const { Default: SideNavigation } = composeStories(stories);
    const { container } = render(<SideNavigation />);
    const element = await screen.findByText('koz2');
    expect(element).toBeVisible();
    fireEvent.click(screen.getByTestId('SideNavigation_SettingsButton'));
    const element2 = await screen.findByText('Personal settings');
    expect(element2).toBeVisible();
    expect(container).toMatchSnapshot();
  });
});
