import { Effects, FontFamily } from '../../../../styles/global.styles';
import { SidebarBadge } from '../NewSidebarItem';
import styled from '@emotion/styled';

export const SubSectionItem = styled('div')(() => ({
  display: 'flex',
  position: 'relative',
  overflow: 'hidden',

  '.MuiLink-root': {
    width: '100%',
    boxSizing: 'border-box',
    textDecoration: 'none',
  },

  '&:hover .circleIcon, .active.circleIcon': {
    width: '20px',
  },
}));

export const SubSectionItemContent = styled('span')(
  ({ hasSavedSearch = false }: { hasSavedSearch: boolean }) => ({
    padding: `3px ${hasSavedSearch ? '55px' : '20px'} 3px 25px`,
    display: 'flex',
    textDecoration: 'none',
    fontSize: '14px',
    lineHeight: '20px',
    fontFamily: FontFamily.default,
    position: 'relative',
    gap: '25px',
    alignItems: 'center',
  })
);

export const SubSectionIconWrapper = styled('span')(() => ({
  display: 'flex',
  width: '30px',
  minWidth: '30px',
  height: '30px',
  justifyContent: 'center',
  alignItems: 'center',
}));

export const SubSectionName = styled('span')(() => ({
  ...Effects.Text.ellipsis,
}));

export const DropdownTrigger = styled('span')(() => ({
  width: '30px',
  height: '36px',
  padding: '3px 0',
  position: 'absolute',
  right: '25px',
  top: 0,
}));

export const SubSectionBadge = styled(SidebarBadge)(() => ({
  right: '30px',
  top: 'calc(50% - 9px)',
}));
