import PropTypes from 'prop-types';
import { useCallback, useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import bulkActionsTranslations from '../../i18n/en/components/BulkActions/BulkActions.json';
import i18n from '../../i18n/i18n';
import { LabelSmall } from '../../styles/typography';
import { makeCancelable } from '../../utils/other';
import { generateTestId } from '../../utils/test.utils';
import { HorizontalBar } from '../Utils/HorizontalBar/HorizontalBar';
import BulkActionItem from './BulkActionItem';
import { BulkActionsContext } from './BulkActions.context';
import {
  BulkActionListWrapper,
  BulkActionsWrapper,
  DismissLabel,
  ItemLabel,
  ItemSelectorsWrapper,
} from './BulkActions.styles';
import {
  BulkAction,
  BulkActionButtons,
  BulkActionsProps,
  ExecuteActionHandler,
} from './BulkActions.types';
import BulkActionsNoActionsAvailable from './BulkActionsNoActionsAvailable';

i18n.addResourceBundle('en', 'BulkActions', bulkActionsTranslations);

export const BulkActions = ({
  visibleItemsCount,
  allItemsCount,
  onSelectVisibleItems,
  getActionsForSelectedItems,
  getActionsForAllItems,
  translations: { itemTranslationKey, itemsTranslationKey },
  items,
  renderNoActionsAvailable,
  onSelect,
}: BulkActionsProps) => {
  const { t } = useTranslation('BulkActions');
  const [visibleButtons, setVisibleButtons] = useState([]);
  const [actions, setActions] = useState<BulkAction[]>([]);
  const {
    selectedItemIds,
    selectedItemCount,
    isAllItemsSelected,
    setSelectedItemIds,
    setSelectedAllItems,
    setLastClickedId,
  } = useContext(BulkActionsContext);

  useEffect(() => {
    onSelect && onSelect();
  }, [isAllItemsSelected, selectedItemCount]);

  useEffect(() => {
    const buttons = [];
    if (selectedItemCount < visibleItemsCount) {
      buttons.push(BulkActionButtons.SelectAllVisibleItems);
    } else if (
      !isAllItemsSelected &&
      getActionsForAllItems &&
      selectedItemCount < allItemsCount
    ) {
      buttons.push(BulkActionButtons.SelectAllItems);
    } else {
      buttons.push(BulkActionButtons.ClearSelection);
    }
    setVisibleButtons(buttons);
  }, [isAllItemsSelected, selectedItemCount]);

  useEffect(() => {
    if (Array.isArray(items) && items.length === 0) {
      setSelectedItemIds([]);
    }
  }, [items]);

  useEffect(() => {
    const cancelablePromise = makeCancelable(
      isAllItemsSelected
        ? getActionsForAllItems()
        : getActionsForSelectedItems(selectedItemIds)
    );
    cancelablePromise.promise
      .then((bulkActions: BulkAction[]) => {
        setActions(bulkActions);
      })
      .catch(() => {
        // catch
      });
    return () => cancelablePromise.cancel();
  }, [selectedItemIds, isAllItemsSelected]);

  const onDismiss = useCallback(() => {
    setLastClickedId(null);
    setSelectedAllItems(false);
    setSelectedItemIds([]);
  }, []);

  const handleExecuteAction: ExecuteActionHandler = useCallback(
    (action) => {
      action.execute().then(onDismiss);
    },
    [onDismiss]
  );

  return (
    <BulkActionsWrapper
      data-testid="BulkActions"
      visible={selectedItemCount > 0}
    >
      <div>
        <ItemSelectorsWrapper>
          <div className="info">
            <LabelSmall>
              {t('Selection.ItemsSelected', {
                count: isAllItemsSelected ? allItemsCount : selectedItemCount,
                itemTranslationKey,
                itemsTranslationKey,
              })}
            </LabelSmall>
          </div>
          <div className="selectors">
            {visibleButtons.includes(
              BulkActionButtons.SelectAllVisibleItems
            ) && (
              <div>
                <a onClick={() => setSelectedItemIds(onSelectVisibleItems())}>
                  <ItemLabel>
                    {t('Selection.SelectAllVisibleItems', {
                      count: visibleItemsCount,
                      itemsTranslationKey,
                    })}
                  </ItemLabel>
                </a>
              </div>
            )}
            {visibleButtons.includes(BulkActionButtons.SelectAllItems) && (
              <div>
                <a onClick={() => setSelectedAllItems(true)}>
                  <ItemLabel>
                    {t('Selection.SelectAllItems', {
                      count: allItemsCount,
                      itemsTranslationKey,
                    })}
                  </ItemLabel>
                </a>
              </div>
            )}
            {visibleButtons.includes(BulkActionButtons.ClearSelection) && (
              <div>
                <a onClick={onDismiss}>
                  <ItemLabel>{t('Selection.ClearSelection')}</ItemLabel>
                </a>
              </div>
            )}
          </div>
          <div className="dismiss">
            <a onClick={onDismiss}>
              <DismissLabel>{t('common:Form.Cancel')}</DismissLabel>
            </a>
          </div>
        </ItemSelectorsWrapper>
      </div>
      {actions.length === 0 && (
        <BulkActionListWrapper>
          {renderNoActionsAvailable ? (
            renderNoActionsAvailable({
              selectedItemCount,
            })
          ) : (
            <BulkActionsNoActionsAvailable
              selectedItemCount={selectedItemCount}
              translations={{ itemTranslationKey, itemsTranslationKey }}
            />
          )}
        </BulkActionListWrapper>
      )}
      {actions.length > 0 && (
        <BulkActionListWrapper>
          <HorizontalBar
            items={actions}
            visibleItem={(item) => (
              <BulkActionItem
                action={item}
                onExecuteAction={handleExecuteAction}
              />
            )}
            hiddenItem={(item) => ({
              id: item.id,
              label: item.title,
              actionPromise: () => handleExecuteAction(item),
              icon: item.icon,
              testId: generateTestId(item.title, 'BulkAction')['data-testid'],
            })}
          />
        </BulkActionListWrapper>
      )}
    </BulkActionsWrapper>
  );
};

BulkActions.propTypes = {
  visibleItemsCount: PropTypes.number,
  allItemsCount: PropTypes.number,
  onSelectVisibleItems: PropTypes.func,
  translations: PropTypes.shape({
    itemTranslationKey: PropTypes.string,
    itemsTranslationKey: PropTypes.string,
  }),
  onSelect: PropTypes.func,
};
