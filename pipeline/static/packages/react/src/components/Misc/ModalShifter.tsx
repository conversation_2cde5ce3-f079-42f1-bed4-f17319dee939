import { useLayoutEffect } from 'react';

const getFirstModal = () => {
  const modals = document.getElementsByClassName(
    'modal'
  ) as HTMLCollectionOf<HTMLElement>;
  return modals[0];
};

const ModalShifter = () => {
  useLayoutEffect(() => {
    const firstModal = getFirstModal();
    if (firstModal) {
      firstModal.style.right = '240px';
      return () => {
        firstModal.style.right = '0px';
      };
    }
  }, []);
  return null;
};

export default ModalShifter;
