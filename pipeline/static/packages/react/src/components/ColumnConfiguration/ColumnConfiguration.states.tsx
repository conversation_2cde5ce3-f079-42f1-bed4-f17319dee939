import { BackIcon } from '../../assets/icons/small';
import columnConfiguration from '../../i18n/en/components/DataTable/ColumnConfiguration.json';
import i18n from '../../i18n/i18n';
import { Colors } from '../../styles/global.styles';
import { Label } from '../../styles/typography';
import { searchNestedItemsByQuery } from '../../utils/other';
import { generateTestId } from '../../utils/test.utils';
import Button from '../FormElements/Button/Button';
import InputSearch from '../FormElements/InputSearch/InputSearch';
import InputText from '../FormElements/InputText/InputText';
import {
  ColumnList,
  IconWrapper,
  Link,
  Row,
  SearchStyled,
} from './ColumnConfiguration.styles';
import {
  ColumnConfigurationListViewType,
  ColumnConfigurationState,
} from './ColumnConfiguration.types';
import styled from '@emotion/styled';
import produce from 'immer';
import React, { useEffect } from 'react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

i18n.addResourceBundle('en', 'ColumnConfiguration', columnConfiguration);

const StyledGroupLabel = styled(Label)({
  display: 'block',
  marginBottom: 10,
  paddingTop: 10,
});

export const Header = ({
  text,
  onBackClick,
}: {
  text: string;
  onBackClick: () => void;
}) => (
  <div
    style={{
      padding: '12px 10px',
      borderBottom: '1px solid',
      borderBottomColor: Colors.grey3,
    }}
  >
    <IconWrapper style={{ verticalAlign: 'middle', paddingRight: 8 }}>
      <BackIcon
        onClick={(event) => {
          onBackClick();
          event.stopPropagation();
        }}
        size={22}
        fill={Colors.grey2}
      />
    </IconWrapper>
    <Label>{text}</Label>
  </div>
);

export const ColumnConfigurationAddFieldState = ({
  columns,
  setState,
  setColumns,
  availableColumns,
  onShow,
  showHeader = true,
  listView = 'list',
}: {
  columns: any[];
  setState: (state: ColumnConfigurationState) => void;
  setColumns: (state) => void;
  availableColumns: any[];
  onShow: (columns, subItem?) => void;
  showHeader?: boolean;
  listView?: ColumnConfigurationListViewType;
}) => {
  const { t } = useTranslation('ColumnConfiguration');
  const [filteredColumns, setFilteredColumns] = useState(availableColumns);
  const [searchedPhrase, setSearchedPhrase] = useState('');

  const renderColumnsByType = (fieldTypes: string[], header?: string) => {
    let firstMatched = false;
    return filteredColumns.map((column) => {
      let showHeader = false;
      if (
        [...fieldTypes].includes(column.fieldType) ||
        fieldTypes.length === 0
      ) {
        if (!firstMatched) {
          firstMatched = showHeader = true;
        }
        return (
          <div key={column.id}>
            {showHeader && header ? (
              <StyledGroupLabel>{header}</StyledGroupLabel>
            ) : null}
            {column.items ? (
              searchNestedItemsByQuery(
                column.items,
                searchedPhrase,
                (i: any) => i._translatedName
              )
                .filter((columnItem) => !columnItem.isVisible)
                .map((columnItem) => (
                  <Row key={columnItem.id}>
                    <Link
                      {...generateTestId('ColumnConfiguration_ColumnName')}
                      onClick={(event) => {
                        onShow(column, columnItem);
                        setColumns(
                          produce(columns, (draft) => {
                            const index = draft.findIndex(
                              (c) => c.id === column.id
                            );
                            const subIndex = draft[index].items.findIndex(
                              (c) => c.id === columnItem.id
                            );
                            if (index !== -1 && subIndex !== -1) {
                              draft[index].items[subIndex].isVisible = true;
                            }
                          })
                        );
                        event.stopPropagation();
                      }}
                    >
                      {columnItem._translatedName}
                    </Link>
                  </Row>
                ))
            ) : (
              <Row>
                <Link
                  {...generateTestId('ColumnConfiguration_ColumnName')}
                  onClick={(event) => {
                    onShow(column);
                    setColumns(
                      produce(columns, (draft) => {
                        const index = draft.findIndex(
                          (c) => c.id === column.id
                        );
                        if (index !== -1) {
                          draft[index].isVisible = true;
                        }
                      })
                    );
                    event.stopPropagation();
                  }}
                >
                  {column._translatedName}
                </Link>
              </Row>
            )}
          </div>
        );
      }
    });
  };

  useEffect(() => {
    setFilteredColumns(
      searchNestedItemsByQuery(availableColumns, searchedPhrase, (i: any) => {
        if (i.fieldType === 'availability_week') {
          return `${i18n.t('Search.Availability')} ${i._translatedName}`;
        } else {
          return i._translatedName;
        }
      })
    );
  }, [searchedPhrase]);

  return (
    <div>
      {showHeader && (
        <Header
          text={t(`Header.AddProfile.${listView}`)}
          onBackClick={() => setState(ColumnConfigurationState.List)}
        />
      )}
      {availableColumns.length > 10 && (
        <SearchStyled>
          <InputSearch
            placeholder={t(`Search.SearchProfile.${listView}`)}
            onChange={setSearchedPhrase}
            autoFocus
          />
        </SearchStyled>
      )}
      <ColumnList data-testid="ColumnConfiguration__AddField__ColumnList">
        {filteredColumns.some((column) => !column.fieldType) && (
          <div>{renderColumnsByType([undefined])}</div>
        )}
        {filteredColumns.some((column) =>
          ['core', 'custom_field'].includes(column.fieldType)
        ) && (
          <div>
            {renderColumnsByType(
              ['core', 'custom_field'],
              i18n.t('Search.CustomFields')
            )}
          </div>
        )}
        {filteredColumns.some(
          (column) => column.fieldType === 'availability_week'
        ) && (
          <div>
            {renderColumnsByType(
              ['availability_week'],
              i18n.t('Search.Availability')
            )}
          </div>
        )}
        {filteredColumns.some((column) => column.fieldType === 'list') && (
          <div>{renderColumnsByType(['list'], i18n.t('Search.Lists'))}</div>
        )}
        {filteredColumns.some(
          (column) => column.fieldType === 'requested_document'
        ) && (
          <div>
            {renderColumnsByType(
              ['requested_document'],
              i18n.t('Search.RequestedDocuments')
            )}
          </div>
        )}
      </ColumnList>
    </div>
  );
};

export const ColumnConfigurationEditState = ({
  setState,
  onAdd,
}: {
  setState: (state: ColumnConfigurationState) => void;
  onAdd: ({ type, name }: { type: string; name: string }) => void;
}) => {
  const [name, setName] = useState('');
  const [errors, setErrors] = useState([]);
  const { t } = useTranslation('ColumnConfiguration');
  return (
    <div>
      <Header
        text={t('Header.AddNewCustomField')}
        onBackClick={() => setState(ColumnConfigurationState.List)}
      />
      <div style={{ margin: 20 }}>
        <div style={{ marginBottom: 10 }}>
          <InputText
            label={t('Label.ColumnLabel')}
            autoFocus={true}
            errors={errors}
            maxLength={50}
            onChange={(text) => {
              setErrors([]);
              setName(String(text));
            }}
          />
        </div>
        <Button
          label={t('Button.AddField')}
          onClick={(event) => {
            if (name.trim().length === 0) {
              setErrors([t('Error.LabelNotValid')]);
            } else {
              onAdd({
                type: 'text_line',
                name: name.trim(),
              });
              setState(ColumnConfigurationState.List);
            }
            event.stopPropagation();
          }}
        />
      </div>
    </div>
  );
};
