import custom_fields_templates from '../../../fixtures/api/custom_fields_templates/global_fields_templates.json';
import { apiClient } from '../../../services/Api/base';
import { jestMockIntersectionObserver } from '../../../utils/jest/jest.helpers';
import {
  jestSetupSessionUser,
  jestSetupTenant,
} from '../../../utils/jest/jest.session';
import GlobalCustomFieldsTemplatesContainer from './GlobalCustomFieldsTemplatesContainer';
import '@testing-library/jest-dom';
import {
  fireEvent,
  render,
  screen,
  waitFor,
  within,
} from '@testing-library/react';
// eslint-disable-next-line no-restricted-imports
import MockAdapter from 'axios-mock-adapter';
import React from 'react';

const prepareContainer = async (props?: any) => {
  render(<GlobalCustomFieldsTemplatesContainer {...props} />);
  return await screen.findByTestId('GlobalCustomFieldsTemplateList');
};

const editTemplate = async (container, matcher) => {
  const jobOpenings = await within(container).findByText(matcher);
  fireEvent.click(jobOpenings);
  return await screen.findByTestId('RequestDataStageEditor');
};

const getFieldDropdown = (editor, matcher) => {
  const fieldDropdowns = within(editor).getAllByTestId('Dropdown_Label');
  return fieldDropdowns.find((o) => within(o).queryByText(matcher));
};

const getFieldOption = async (matcher) => {
  const fieldOptions = await screen.findAllByTestId(
    'DropdownSelectOption_OptionValue'
  );
  return fieldOptions.find((o) => within(o).queryByText(matcher));
};

const saveTemplate = (editor) => {
  const saveButton = within(editor)
    .getAllByRole('button')
    .find((o) => within(o).getByText('Save'));
  fireEvent.click(saveButton);
};

describe('GlobalCustomFieldsTemplatesContainer', () => {
  let mock;

  beforeEach(() => {
    jestMockIntersectionObserver();
    jestSetupTenant([
      'marketplace',
      'projects_and_tasks',
      'payments',
      'task_templates_in_job_openings',
    ]);

    jestSetupSessionUser('buyer-admin');

    mock = new MockAdapter(apiClient, { delayResponse: 0 });
    mock
      .onGet(/\/api\/global_fields_templates\//gm)
      .reply(200, custom_fields_templates);
    mock
      .onPost(/\/api\/global_fields_templates\//gm)
      .reply(200, custom_fields_templates);
    mock
      .onPut(/\/api\/global_fields_templates\/\d+\//gm)
      .reply(200, custom_fields_templates);

    mock.onGet(/\/api\/job_opening_templates\//gm).reply(200, []);
    mock.onGet(/\/api\/task_templates\//gm).reply(200, []);
    mock
      .onPost(/\/api\/search\/contracts\//gm)
      .reply(({ data }) => [
        200,
        require(
          `../../../fixtures/api/contracts/search/page${
            JSON.parse(data).page
          }.json`
        ),
      ]);
  });
  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should show all global custom field templates', async () => {
    const component = await prepareContainer();

    await waitFor(() => {
      expect(
        within(component)
          .queryAllByRole('row')
          .filter((row) =>
            within(row).queryByText(/^(Job opening)|(Invoice)|(Project)|(Task)/)
          )
      ).toHaveLength(5);
    });
    expect(within(component).queryByText(/^Job openings/)).toBeVisible();
    expect(within(component).queryByText(/^Projects/)).toBeVisible();
    expect(within(component).queryByText(/^Tasks/)).toBeVisible();
    expect(within(component).queryByText(/^Invoices/)).toBeVisible();
    expect(within(component).queryByText(/^Invoice /)).toBeVisible();
  });
  test('should not show global custom field template if related feature is disabled', async () => {
    jestSetupTenant([]);
    const component = await prepareContainer();

    await waitFor(() => {
      expect(
        within(component)
          .queryAllByRole('row')
          .filter((row) =>
            within(row).queryByText(/^(Job opening)|(Invoice)|(Project)|(Task)/)
          )
      ).toHaveLength(0);
    });
  });
  test('should show number of fields configured for the global custom fields template', async () => {
    const component = await prepareContainer();

    await waitFor(() => {
      expect(
        within(component)
          .queryAllByRole('row')
          .filter(
            (row) =>
              within(row).queryByText(/^Job opening/) &&
              within(row).queryByText(/^9 Fields/)
          )
      ).toHaveLength(1);
    });
  });
  test('should open custom fields editor when template is selected', async () => {
    const component = await prepareContainer();

    const editor = await editTemplate(component, /^Job openings/);

    expect(editor).toBeVisible();
    expect(
      within(editor).getByText('Custom fields for Job openings')
    ).toBeVisible();
  });
  test('should allow adding new global custom field to template', async () => {
    const onTemplateSaved = jest.fn();
    const component = await prepareContainer({
      onTemplateSaved,
    });

    const editor = await editTemplate(component, /^Job openings/);
    fireEvent.click(getFieldDropdown(editor, 'Add field'));
    fireEvent.click(await getFieldOption('Short answer'));
    saveTemplate(editor);

    await waitFor(() => {
      expect(onTemplateSaved).toHaveBeenCalledTimes(1);
    });
    expect(onTemplateSaved).toHaveBeenCalledWith(
      expect.objectContaining({
        name: 'Job openings',
        model: 'JobOpening',
        template_fields: expect.arrayContaining([
          expect.objectContaining({
            label: 'Question 12',
            type: 'text_line',
          }),
        ]),
      })
    );
  });
  test('should not display not allowed custom field types', async () => {
    const component = await prepareContainer();
    const editor = await editTemplate(component, /^Job openings/);
    await waitFor(() => {
      within(editor)
        .queryAllByText(/(^Test header$)|(^Test paragraph$)/)
        .forEach((element) => expect(element).not.toBeVisible());
    });
  });
  test('should preserve not allowed custom fields when saving', async () => {
    const onTemplateSaved = jest.fn();
    const component = await prepareContainer({
      onTemplateSaved,
    });

    const editor = await editTemplate(component, /^Job openings/);
    fireEvent.click(getFieldDropdown(editor, 'Add field'));
    fireEvent.click(await getFieldOption('Short answer'));
    saveTemplate(editor);

    await waitFor(() => {
      expect(onTemplateSaved).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Job openings',
          model: 'JobOpening',
          template_fields: expect.arrayContaining([
            expect.objectContaining({
              label: 'Test header',
              type: 'section',
            }),
            expect.objectContaining({
              description: 'Test paragraph',
              type: 'content_text_area',
            }),
          ]),
        })
      );
    });
  });
  test('should not allow buyer to add new global custom field to Job Opening if more than one job opening template exists', async () => {
    mock.onGet(/\/api\/job_opening_templates\//gm).reply(200, [{}, {}]);
    const component = await prepareContainer();

    const editor = await editTemplate(component, /^Job openings/);

    await waitFor(() => {
      expect(within(editor).queryByText('Add field')).not.toBeInTheDocument();
    });
  });
  test('should allow staff to add new global custom field to Job Opening if more than one job opening template exists', async () => {
    mock.onGet(/\/api\/job_opening_templates\//gm).reply(200, [{}, {}]);
    jestSetupSessionUser('staff');

    const component = await prepareContainer();

    const editor = await editTemplate(component, /^Job openings/);

    await waitFor(() => {
      expect(within(editor).getByText('Add field')).toBeInTheDocument();
    });
  });
  test('should not allow buyer to add new global custom field to Task if more than one task template exists', async () => {
    mock.onGet(/\/api\/task_templates\//gm).reply(200, [{}, {}]);

    const component = await prepareContainer();

    const editor = await editTemplate(component, /^Tasks/);

    await waitFor(() => {
      expect(within(editor).queryByText('Add field')).not.toBeInTheDocument();
    });
  });
  test('should allow staff to add new global custom field to Task if more than one task template exists', async () => {
    mock.onGet(/\/api\/task_templates\//gm).reply(200, [{}, {}]);
    jestSetupSessionUser('staff');

    const component = await prepareContainer();

    const editor = await editTemplate(component, /^Tasks/);

    await waitFor(() => {
      expect(within(editor).getByText('Add field')).toBeInTheDocument();
    });
  });
  test('should not allow buyer to duplicate global custom field if more than one template exists', async () => {
    mock.onGet(/\/api\/task_templates\//gm).reply(200, [{}, {}]);
    const component = await prepareContainer();

    const editor = await editTemplate(component, /^Tasks/);

    const fieldPreviews = within(editor).getAllByTestId(
      'CustomFieldConfigItemPreview'
    );
    fireEvent.click(fieldPreviews[0]);
    const fieldEditor = await within(editor).findByTestId(
      'CustomFieldConfigItem'
    );

    await waitFor(() => {
      expect(
        within(fieldEditor).queryByLabelText('Duplicate')
      ).not.toBeInTheDocument();
    });
  });
});
