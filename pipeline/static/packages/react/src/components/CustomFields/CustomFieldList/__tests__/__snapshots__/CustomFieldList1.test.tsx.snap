// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CustomFieldList snapshot  Default 1`] = `
.emotion-class:not(:last-of-type) {
  margin-bottom: 30px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  margin-bottom: 5px;
}

.emotion-class>a {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  padding: 0 10px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  visibility: hidden;
  max-height: 0;
  white-space: nowrap;
  overflow-x: hidden;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  box-sizing: border-box;
  width: 100%;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: row nowrap;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 150%;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  width: 100%;
  box-sizing: border-box;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  position: relative;
}

.emotion-class svg {
  fill: #465AB6;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class {
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
  font-size: 12px;
  color: #465AB6;
  vertical-align: middle;
}

.emotion-class {
  position: relative;
}

.emotion-class:hover fieldset {
  border-color: #B4BCE0;
}

.emotion-class fieldset {
  border-color: #D1D6ED;
}

.emotion-class input {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  padding-left: 9px;
  padding-right: 9px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
  vertical-align: top;
  width: 100%;
  margin: 0;
}

.emotion-class .clearButton {
  opacity: 1;
}

@media (pointer: fine) {
  .emotion-class .clearButton {
    opacity: 0;
  }

  .emotion-class:hover .clearButton,
  .emotion-class:focus-within .clearButton {
    opacity: 1;
  }
}

.emotion-class {
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.4375em;
  letter-spacing: 0.00938em;
  color: rgba(0, 0, 0, 0.87);
  box-sizing: border-box;
  position: relative;
  cursor: text;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  border-radius: 4px;
  padding-right: 14px;
  padding: 5px;
  height: 45px;
  background: #FFFFFF;
}

.emotion-class.Mui-disabled {
  color: rgba(0, 0, 0, 0.38);
  cursor: default;
}

.emotion-class:hover .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.87);
}

@media (hover: none) {
  .emotion-class:hover .MuiOutlinedInput-notchedOutline {
    border-color: rgba(0, 0, 0, 0.23);
  }
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-width: 2px;
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #1976d2;
}

.emotion-class.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: #d32f2f;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.26);
}

.emotion-class input {
  font-size: 14px;
  color: #303757;
  font-family: "Open Sans",sans-serif;
}

.emotion-class input::-webkit-input-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input::-moz-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input:-ms-input-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input::placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class .MuiOutlinedInput-notchedOutline {
  border-color: #D1D6ED;
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline,
.emotion-class:hover.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #465AB6;
}

.emotion-class:hover .MuiOutlinedInput-notchedOutline {
  border-color: #B4BCE0;
  outline: none;
}

.emotion-class.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: #FF7F8A;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: #E4E5EB;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::-webkit-input-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::-moz-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline:-ms-input-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::placeholder {
  color: blue;
}

.emotion-class {
  font: inherit;
  letter-spacing: inherit;
  color: currentColor;
  padding: 4px 0 5px;
  border: 0;
  box-sizing: content-box;
  background: none;
  height: 1.4375em;
  margin: 0;
  -webkit-tap-highlight-color: transparent;
  display: block;
  min-width: 0;
  width: 100%;
  -webkit-animation-name: mui-auto-fill-cancel;
  animation-name: mui-auto-fill-cancel;
  -webkit-animation-duration: 10ms;
  animation-duration: 10ms;
  padding: 16.5px 14px;
  padding-right: 0;
  cursor: pointer;
  padding: 9px 12px 10px;
  width: 100%;
}

.emotion-class::-webkit-input-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class::-moz-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class::-ms-input-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class:focus {
  outline: 0;
}

.emotion-class:invalid {
  box-shadow: none;
}

.emotion-class::-webkit-search-decoration {
  -webkit-appearance: none;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-webkit-input-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-moz-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-ms-input-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-webkit-input-placeholder {
  opacity: 0.42;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-moz-placeholder {
  opacity: 0.42;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-ms-input-placeholder {
  opacity: 0.42;
}

.emotion-class.Mui-disabled {
  opacity: 1;
  -webkit-text-fill-color: rgba(0, 0, 0, 0.38);
}

.emotion-class:-webkit-autofill {
  -webkit-animation-duration: 5000s;
  animation-duration: 5000s;
  -webkit-animation-name: mui-auto-fill;
  animation-name: mui-auto-fill;
}

.emotion-class:-webkit-autofill {
  border-radius: inherit;
}

.emotion-class.Mui-disabled {
  color: #A6ABBF;
  -webkit-text-fill-color: #A6ABBF;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  max-height: 2em;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.54);
  margin-left: 8px;
  position: absolute;
  right: 17px;
}

.emotion-class button {
  color: #465AB6;
  margin-right: -12px!important;
}

.emotion-class button.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class button:hover {
  background-color: transparent;
}

.emotion-class button svg {
  width: 20px;
  height: 20px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-right: -12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: 1.5rem;
}

.emotion-class {
  text-align: left;
  position: absolute;
  bottom: 0;
  right: 0;
  top: -5px;
  left: 0;
  margin: 0;
  padding: 0 8px;
  pointer-events: none;
  border-radius: inherit;
  border-style: solid;
  border-width: 1px;
  overflow: hidden;
  min-width: 0%;
  border-color: rgba(0, 0, 0, 0.23);
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 34px;
  min-height: auto;
  border-width: 2px;
  border-style: solid;
  outline: none;
  top: 0;
  -webkit-transition: border-color ease-in-out 0.15s,box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s,box-shadow ease-in-out 0.15s;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class legend {
  display: none;
}

.emotion-class {
  float: unset;
  width: auto;
  overflow: hidden;
  padding: 0;
  line-height: 11px;
  -webkit-transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
  transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
}

.emotion-class {
  z-index: 1300;
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  outline: 0;
  transform-origin: top center;
  transform-origin: bottom center;
}

.emotion-class {
  display: grid;
  grid-auto-columns: max-content auto max-content;
  grid-auto-rows: max-content auto max-content;
}

.emotion-class .MuiPickersLayout-actionBar {
  grid-column: 1/4;
  grid-row: 3;
}

.emotion-class .MuiPickersLayout-toolbar {
  grid-column: 2/4;
  grid-row: 1;
}

.emotion-class .MuiPickersLayout-shortcuts {
  grid-column: 1;
  grid-row: 2/3;
}

.emotion-class {
  grid-column: 2;
  grid-row: 2;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  overflow: hidden;
  width: 320px;
  max-height: 336px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: 0 auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 336px;
}

.emotion-class>div:first-of-type {
  position: absolute;
  width: calc(100% - 40px);
  box-sizing: border-box;
  background: #fff;
  min-height: initial;
  max-height: initial;
  height: 54px;
  padding: 16px 0 6px;
  margin: 0 20px;
}

.emotion-class>div:first-of-type+div {
  margin-top: 54px;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  font-size: 14px;
  color: #465AB6;
  font-weight: 600;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .MuiButtonBase-root {
  display: none;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .PrivatePickersFadeTransitionGroup-root {
  color: #465AB6;
  font-weight: 600;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .PrivatePickersFadeTransitionGroup-root:nth-of-type(2) div {
  margin-right: 0;
}

.emotion-class>div:first-of-type>div:nth-of-type(2) {
  width: 100%;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-top: 12px;
  margin-bottom: 4px;
  padding-left: 24px;
  padding-right: 12px;
  max-height: 40px;
  min-height: 40px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  margin-right: auto;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 500;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0.00938em;
}

.emotion-class {
  display: block;
  position: relative;
}

.emotion-class {
  margin-right: 6px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  padding: 5px;
  font-size: 1.125rem;
  margin-right: auto;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: 1.5rem;
  will-change: transform;
  -webkit-transition: -webkit-transform 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-right: -12px;
  color: #465AB6;
  padding: 5px;
  width: 34px;
  height: 34px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class svg {
  width: 24px;
  height: 24px;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: inherit;
}

.emotion-class {
  width: 24px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-left: -12px;
  color: #465AB6;
  padding: 5px;
  width: 34px;
  height: 34px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class svg {
  width: 24px;
  height: 24px;
}

.emotion-class {
  display: block;
  position: relative;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  margin: 0;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  font-weight: 600;
  font-size: 12px;
  width: 36px;
  height: 40px;
  margin: 0 2px;
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: rgba(0, 0, 0, 0.6);
}

.emotion-class {
  display: block;
  position: relative;
  overflow-x: hidden;
  min-height: 240px;
}

.emotion-class>* {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
}

.emotion-class .MuiPickersSlideTransition-slideEnter-left {
  will-change: transform;
  -webkit-transform: translate(100%);
  -moz-transform: translate(100%);
  -ms-transform: translate(100%);
  transform: translate(100%);
  z-index: 1;
}

.emotion-class .MuiPickersSlideTransition-slideEnter-right {
  will-change: transform;
  -webkit-transform: translate(-100%);
  -moz-transform: translate(-100%);
  -ms-transform: translate(-100%);
  transform: translate(-100%);
  z-index: 1;
}

.emotion-class .MuiPickersSlideTransition-slideEnterActive {
  -webkit-transform: translate(0%);
  -moz-transform: translate(0%);
  -ms-transform: translate(0%);
  transform: translate(0%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
}

.emotion-class .MuiPickersSlideTransition-slideExit {
  -webkit-transform: translate(0%);
  -moz-transform: translate(0%);
  -ms-transform: translate(0%);
  transform: translate(0%);
}

.emotion-class .MuiPickersSlideTransition-slideExitActiveLeft-left {
  will-change: transform;
  -webkit-transform: translate(-100%);
  -moz-transform: translate(-100%);
  -ms-transform: translate(-100%);
  transform: translate(-100%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  z-index: 0;
}

.emotion-class .MuiPickersSlideTransition-slideExitActiveLeft-right {
  will-change: transform;
  -webkit-transform: translate(100%);
  -moz-transform: translate(100%);
  -ms-transform: translate(100%);
  transform: translate(100%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  z-index: 0;
}

.emotion-class {
  overflow: hidden;
}

.emotion-class button {
  margin-right: 2px!important;
}

.emotion-class {
  margin: 2px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  color: rgba(0, 0, 0, 0.6);
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:not(.Mui-selected) {
  border: 1px solid rgba(0, 0, 0, 0.6);
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class:not(.Mui-selected) {
  -webkit-transition: 220ms all;
  transition: 220ms all;
}

.emotion-class:not(.Mui-selected):before {
  content: '';
  display: inline-block;
  border-left: 7px solid transparent;
  border-bottom: 7px solid #465AB6;
  border-top-color: rgba(0, 0, 0, 0.2);
  position: absolute;
  bottom: 4px;
  right: 4px;
}

.emotion-class:not(.Mui-selected):hover:before {
  display: none;
}

.emotion-class>:not(:last-child) {
  margin-bottom: 10px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  font-size: 12px;
  line-height: 20px;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  color: #465AB6;
  display: block;
  padding: 20px;
  text-align: center;
  background-color: #F5F6FA;
  cursor: pointer;
}

.emotion-class svg {
  vertical-align: bottom;
  margin-right: 5px;
}

<div>
  <div
    class="emotion-class"
  >
    <div
      class="emotion-class"
    >
      <div>
        <span
          class="emotion-class"
          data-testid="InputLabel_TextLine"
        >
          text line
        </span>
        <p
          class="emotion-class"
          style="color: rgb(48, 55, 87);"
        >
          Description (text line)
        </p>
      </div>
    </div>
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      />
      <input
        class="emotion-class"
        type="text"
        value=""
      />
    </div>
    <div
      class="emotion-class"
    >
      <div />
    </div>
  </div>
  <div
    class="emotion-class"
  >
    <div
      class="emotion-class"
    >
      <div>
        <span
          class="emotion-class"
          data-testid="InputLabel_TextLineWithoutValue"
        >
          text line (without value)
        </span>
      </div>
    </div>
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      />
      <input
        class="emotion-class"
        type="text"
        value=""
      />
    </div>
    <div
      class="emotion-class"
    >
      <div />
    </div>
  </div>
  <div
    class="emotion-class"
  >
    <div
      style="margin-bottom: 5px;"
    >
      <span
        class="emotion-class"
        data-testid="InputLabel_TextArea"
      >
        text area
      </span>
      <p
        class="emotion-class"
        style="color: rgb(48, 55, 87);"
      >
        Description (text area)
      </p>
    </div>
    <div
      class="emotion-class"
    >
      <textarea
        class="emotion-class"
        style="height: -104px;"
        variant="default"
      />
    </div>
    <div
      class="emotion-class"
    >
      <div />
    </div>
  </div>
  <div
    class="emotion-class"
  >
    <div
      style="margin-bottom: 5px;"
    >
      <span
        class="emotion-class"
        data-testid="InputLabel_TextAreaWithoutValue"
      >
        text area (without value)
      </span>
    </div>
    <div
      class="emotion-class"
    >
      <textarea
        class="emotion-class"
        style="height: -104px;"
        variant="default"
      />
    </div>
    <div
      class="emotion-class"
    >
      <div />
    </div>
  </div>
  <div
    class="emotion-class"
  >
    <div
      style="margin-bottom: 5px;"
    >
      <span
        class="emotion-class"
        data-testid="InputLabel_Dropdown"
      >
        dropdown
      </span>
      <p
        class="emotion-class"
        style="color: rgb(48, 55, 87);"
      >
        Description (dropdown)
      </p>
    </div>
    <div
      class="emotion-class"
    >
      <select
        class="emotion-class"
        data-testid="Select_Dropdown"
      >
        <option
          value=""
        >
          Select option
        </option>
        <option
          data-testid="SelectOption_OptionE"
          value="Option E"
        >
          Option E
        </option>
        <option
          data-testid="SelectOption_OptionF"
          value="Option F"
        >
          Option F
        </option>
        <option
          data-testid="SelectOption_Other"
          value=":-other-:"
        >
          Other option
        </option>
      </select>
      <icon-mock
        classname="css-1qbf8bs"
        disabled="false"
        size="28"
      />
    </div>
  </div>
  <div
    class="emotion-class"
  >
    <div
      class="emotion-class"
    >
      <div>
        <span
          class="emotion-class"
          data-testid="InputLabel_Date"
        >
          Date
        </span>
        <p
          class="emotion-class"
          style="color: rgb(48, 55, 87);"
        >
          Description (date)
        </p>
      </div>
      <a
        class=" emotion-class"
        data-testid="InputActionLabel_Button"
        style="text-align: right;"
      >
        <span
          class="emotion-class"
        >
          Reset
        </span>
      </a>
    </div>
    <div
      class="emotion-class"
    >
      <div
        class="MuiFormControl-root MuiTextField-root emotion-class"
      >
        <div
          class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd emotion-class"
        >
          <input
            aria-invalid="false"
            autocomplete="off"
            class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd emotion-class"
            id=":r0:"
            inputmode="text"
            placeholder="MM/DD/YYYY"
            type="text"
            value=""
          />
          <div
            class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium emotion-class"
          >
            <button
              aria-label="Choose date"
              class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium emotion-class"
              tabindex="0"
              type="button"
            >
              <svg
                aria-hidden="true"
                class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium emotion-class"
                data-testid="CalendarIcon"
                focusable="false"
                viewBox="0 0 24 24"
              >
                <path
                  d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
                />
              </svg>
            </button>
          </div>
          <fieldset
            aria-hidden="true"
            class="MuiOutlinedInput-notchedOutline emotion-class"
          >
            <legend
              class="emotion-class"
            >
              <span
                aria-hidden="true"
                class="notranslate"
              >
                ​
              </span>
            </legend>
          </fieldset>
        </div>
      </div>
    </div>
    <div
      class="MuiPopper-root MuiPickersPopper-root emotion-class"
      role="dialog"
      style="position: fixed; top: 0px; left: 0px; display: none;"
    >
      <div
        data-testid="sentinelStart"
        tabindex="-1"
      />
      <div
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation8 MuiPickersPopper-paper emotion-class"
        style="--Paper-shadow: 0px 5px 5px -3px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12); opacity: 0; transform: scale(0.75, 0.5625); visibility: hidden;"
        tabindex="-1"
      >
        <div
          class="MuiPickersLayout-root emotion-class"
        >
          <div
            class="MuiPickersLayout-contentWrapper emotion-class"
          >
            <div
              class="MuiDateCalendar-root emotion-class"
            >
              <div
                class="MuiPickersCalendarHeader-root emotion-class"
              >
                <div
                  aria-live="polite"
                  class="MuiPickersCalendarHeader-labelContainer emotion-class"
                  role="presentation"
                >
                  <div
                    class="MuiPickersFadeTransitionGroup-root emotion-class"
                  >
                    <div
                      class="MuiPickersCalendarHeader-label emotion-class"
                      id=":r3:-grid-label"
                      style="opacity: 1;"
                    >
                      November 2000
                    </div>
                  </div>
                  <button
                    aria-label="calendar view is open, switch to year view"
                    class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall MuiPickersCalendarHeader-switchViewButton emotion-class"
                    tabindex="0"
                    type="button"
                  >
                    <svg
                      aria-hidden="true"
                      class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiPickersCalendarHeader-switchViewIcon emotion-class"
                      data-testid="ArrowDropDownIcon"
                      focusable="false"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M7 10l5 5 5-5z"
                      />
                    </svg>
                  </button>
                </div>
                <div
                  class="MuiPickersArrowSwitcher-root emotion-class"
                  style="opacity: 1; webkit-transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
                >
                  <button
                    aria-label="Previous month"
                    class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-previousIconButton emotion-class"
                    tabindex="0"
                    title="Previous month"
                    type="button"
                  >
                    <svg
                      aria-hidden="true"
                      class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-leftArrowIcon emotion-class"
                      data-testid="ArrowLeftIcon"
                      focusable="false"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"
                      />
                    </svg>
                  </button>
                  <div
                    class="MuiPickersArrowSwitcher-spacer emotion-class"
                  />
                  <button
                    aria-label="Next month"
                    class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeStart MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-nextIconButton emotion-class"
                    tabindex="0"
                    title="Next month"
                    type="button"
                  >
                    <svg
                      aria-hidden="true"
                      class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-rightArrowIcon emotion-class"
                      data-testid="ArrowRightIcon"
                      focusable="false"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"
                      />
                    </svg>
                  </button>
                </div>
              </div>
              <div
                class="MuiPickersFadeTransitionGroup-root MuiDateCalendar-viewTransitionContainer emotion-class"
              >
                <div
                  style="opacity: 1;"
                >
                  <div
                    aria-labelledby=":r3:-grid-label"
                    class="MuiDayCalendar-root emotion-class"
                    role="grid"
                  >
                    <div
                      class="MuiDayCalendar-header emotion-class"
                      role="row"
                    >
                      <span
                        aria-label="Sunday"
                        class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                        role="columnheader"
                      >
                        S
                      </span>
                      <span
                        aria-label="Monday"
                        class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                        role="columnheader"
                      >
                        M
                      </span>
                      <span
                        aria-label="Tuesday"
                        class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                        role="columnheader"
                      >
                        T
                      </span>
                      <span
                        aria-label="Wednesday"
                        class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                        role="columnheader"
                      >
                        W
                      </span>
                      <span
                        aria-label="Thursday"
                        class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                        role="columnheader"
                      >
                        T
                      </span>
                      <span
                        aria-label="Friday"
                        class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                        role="columnheader"
                      >
                        F
                      </span>
                      <span
                        aria-label="Saturday"
                        class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                        role="columnheader"
                      >
                        S
                      </span>
                    </div>
                    <div
                      class="MuiPickersSlideTransition-root MuiDayCalendar-slideTransition emotion-class"
                      role="presentation"
                    >
                      <div
                        class="MuiDayCalendar-monthContainer emotion-class"
                        role="rowgroup"
                      >
                        <div
                          aria-rowindex="1"
                          class="MuiDayCalendar-weekContainer emotion-class"
                          role="row"
                        >
                          <button
                            aria-colindex="1"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                            data-timestamp="************"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            29
                          </button>
                          <button
                            aria-colindex="2"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                            data-timestamp="************"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            30
                          </button>
                          <button
                            aria-colindex="3"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                            data-timestamp="972950400000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            31
                          </button>
                          <button
                            aria-colindex="4"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="973036800000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            1
                          </button>
                          <button
                            aria-colindex="5"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="973123200000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            2
                          </button>
                          <button
                            aria-colindex="6"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="973209600000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            3
                          </button>
                          <button
                            aria-colindex="7"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="************"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            4
                          </button>
                        </div>
                        <div
                          aria-rowindex="2"
                          class="MuiDayCalendar-weekContainer emotion-class"
                          role="row"
                        >
                          <button
                            aria-colindex="1"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="************"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            5
                          </button>
                          <button
                            aria-colindex="2"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="************"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            6
                          </button>
                          <button
                            aria-colindex="3"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="973555200000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            7
                          </button>
                          <button
                            aria-colindex="4"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="973641600000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            8
                          </button>
                          <button
                            aria-colindex="5"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="973728000000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            9
                          </button>
                          <button
                            aria-colindex="6"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="973814400000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            10
                          </button>
                          <button
                            aria-colindex="7"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="************"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            11
                          </button>
                        </div>
                        <div
                          aria-rowindex="3"
                          class="MuiDayCalendar-weekContainer emotion-class"
                          role="row"
                        >
                          <button
                            aria-colindex="1"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="************"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            12
                          </button>
                          <button
                            aria-colindex="2"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="************"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            13
                          </button>
                          <button
                            aria-colindex="3"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="974160000000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            14
                          </button>
                          <button
                            aria-colindex="4"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="974246400000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            15
                          </button>
                          <button
                            aria-colindex="5"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="974332800000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            16
                          </button>
                          <button
                            aria-colindex="6"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="974419200000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            17
                          </button>
                          <button
                            aria-colindex="7"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="************"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            18
                          </button>
                        </div>
                        <div
                          aria-rowindex="4"
                          class="MuiDayCalendar-weekContainer emotion-class"
                          role="row"
                        >
                          <button
                            aria-colindex="1"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="************"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            19
                          </button>
                          <button
                            aria-colindex="2"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="************"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            20
                          </button>
                          <button
                            aria-colindex="3"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="974764800000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            21
                          </button>
                          <button
                            aria-colindex="4"
                            aria-current="date"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-today emotion-class"
                            data-timestamp="974851200000"
                            role="gridcell"
                            tabindex="0"
                            type="button"
                          >
                            22
                          </button>
                          <button
                            aria-colindex="5"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="974937600000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            23
                          </button>
                          <button
                            aria-colindex="6"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="975024000000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            24
                          </button>
                          <button
                            aria-colindex="7"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="************"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            25
                          </button>
                        </div>
                        <div
                          aria-rowindex="5"
                          class="MuiDayCalendar-weekContainer emotion-class"
                          role="row"
                        >
                          <button
                            aria-colindex="1"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="************"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            26
                          </button>
                          <button
                            aria-colindex="2"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="************"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            27
                          </button>
                          <button
                            aria-colindex="3"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="975369600000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            28
                          </button>
                          <button
                            aria-colindex="4"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="975456000000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            29
                          </button>
                          <button
                            aria-colindex="5"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="975542400000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            30
                          </button>
                          <button
                            aria-colindex="6"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                            data-timestamp="975628800000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            1
                          </button>
                          <button
                            aria-colindex="7"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                            data-timestamp="975715200000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            2
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        data-testid="sentinelEnd"
        tabindex="-1"
      />
    </div>
  </div>
  <div
    class="emotion-class"
  >
    <div
      style="margin-bottom: 5px;"
    >
      <span
        class="emotion-class"
        data-testid="InputLabel_Files"
      >
        files
      </span>
      <p
        class="emotion-class"
        style="color: rgb(48, 55, 87);"
      >
        Description (files)
      </p>
    </div>
    <div
      class="emotion-class"
    >
      <span
        class="emotion-class"
      >
        <icon-mock
          classname="css-l5mwpl"
          fill="#465AB6"
          size="24"
        />
        Click to upload file.
      </span>
    </div>
  </div>
  <div
    class="emotion-class"
  >
    <div
      style="margin-bottom: 5px;"
    >
      <span
        class="emotion-class"
        data-testid="InputLabel_Files"
      >
        files
      </span>
    </div>
    <div
      class="emotion-class"
    >
      <span
        class="emotion-class"
      >
        <icon-mock
          classname="css-l5mwpl"
          fill="#465AB6"
          size="24"
        />
        Click to upload file.
      </span>
    </div>
  </div>
  <div
    class="emotion-class"
  />
</div>
`;

exports[`CustomFieldList snapshot  WithValues 1`] = `
.emotion-class:not(:last-of-type) {
  margin-bottom: 30px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  margin-bottom: 5px;
}

.emotion-class>a {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  color: var(--text-color);
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
}

.emotion-class {
  position: relative;
}

.emotion-class {
  padding: 0 10px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  visibility: hidden;
  max-height: 0;
  white-space: nowrap;
  overflow-x: hidden;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  box-sizing: border-box;
  width: 100%;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: row nowrap;
  -webkit-flex-flow: row nowrap;
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 150%;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  width: 100%;
  box-sizing: border-box;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class:disabled {
  border-color: #E4E5EB;
  background-color: #FFFFFF;
  color: #777D96;
}

.emotion-class:disabled::-webkit-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::-moz-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled:-ms-input-placeholder {
  color: #C5C8D5;
}

.emotion-class:disabled::placeholder {
  color: #C5C8D5;
}

.emotion-class {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 25px 6px 12px!important;
  line-height: 34px;
  min-height: 48px;
  border-width: 2px;
  border-style: solid;
  border-color: #D1D6ED;
  outline: none;
  height: 48px;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  background-color: #FFFFFF;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: #FFFFFF;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class {
  position: relative;
}

.emotion-class svg {
  fill: #465AB6;
}

.emotion-class:hover,
.emotion-class:hover svg,
.emotion-class:visited,
.emotion-class:active {
  cursor: pointer;
}

.emotion-class {
  font-weight: 600;
  font-family: "Open Sans",sans-serif;
  font-size: 12px;
  color: #465AB6;
  vertical-align: middle;
}

.emotion-class {
  position: relative;
}

.emotion-class:hover fieldset {
  border-color: #B4BCE0;
}

.emotion-class fieldset {
  border-color: #D1D6ED;
}

.emotion-class input {
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  padding-left: 9px;
  padding-right: 9px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
  vertical-align: top;
  width: 100%;
  margin: 0;
}

.emotion-class .clearButton {
  opacity: 1;
}

@media (pointer: fine) {
  .emotion-class .clearButton {
    opacity: 0;
  }

  .emotion-class:hover .clearButton,
  .emotion-class:focus-within .clearButton {
    opacity: 1;
  }
}

.emotion-class {
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.4375em;
  letter-spacing: 0.00938em;
  color: rgba(0, 0, 0, 0.87);
  box-sizing: border-box;
  position: relative;
  cursor: text;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  border-radius: 4px;
  padding-right: 14px;
  padding: 5px;
  height: 45px;
  background: #FFFFFF;
}

.emotion-class.Mui-disabled {
  color: rgba(0, 0, 0, 0.38);
  cursor: default;
}

.emotion-class:hover .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.87);
}

@media (hover: none) {
  .emotion-class:hover .MuiOutlinedInput-notchedOutline {
    border-color: rgba(0, 0, 0, 0.23);
  }
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-width: 2px;
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #1976d2;
}

.emotion-class.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: #d32f2f;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.26);
}

.emotion-class input {
  font-size: 14px;
  color: #303757;
  font-family: "Open Sans",sans-serif;
}

.emotion-class input::-webkit-input-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input::-moz-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input:-ms-input-placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class input::placeholder {
  color: #A6ABBF;
  opacity: 1;
}

.emotion-class .MuiOutlinedInput-notchedOutline {
  border-color: #D1D6ED;
}

.emotion-class.Mui-focused .MuiOutlinedInput-notchedOutline,
.emotion-class:hover.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #465AB6;
}

.emotion-class:hover .MuiOutlinedInput-notchedOutline {
  border-color: #B4BCE0;
  outline: none;
}

.emotion-class.Mui-error .MuiOutlinedInput-notchedOutline {
  border-color: #FF7F8A;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline {
  border-color: #E4E5EB;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::-webkit-input-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::-moz-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline:-ms-input-placeholder {
  color: blue;
}

.emotion-class.Mui-disabled .MuiOutlinedInput-notchedOutline::placeholder {
  color: blue;
}

.emotion-class {
  font: inherit;
  letter-spacing: inherit;
  color: currentColor;
  padding: 4px 0 5px;
  border: 0;
  box-sizing: content-box;
  background: none;
  height: 1.4375em;
  margin: 0;
  -webkit-tap-highlight-color: transparent;
  display: block;
  min-width: 0;
  width: 100%;
  -webkit-animation-name: mui-auto-fill-cancel;
  animation-name: mui-auto-fill-cancel;
  -webkit-animation-duration: 10ms;
  animation-duration: 10ms;
  padding: 16.5px 14px;
  padding-right: 0;
  cursor: pointer;
  padding: 9px 12px 10px;
  width: 100%;
}

.emotion-class::-webkit-input-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class::-moz-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class::-ms-input-placeholder {
  color: currentColor;
  opacity: 0.42;
  -webkit-transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}

.emotion-class:focus {
  outline: 0;
}

.emotion-class:invalid {
  box-shadow: none;
}

.emotion-class::-webkit-search-decoration {
  -webkit-appearance: none;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-webkit-input-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-moz-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class::-ms-input-placeholder {
  opacity: 0!important;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-webkit-input-placeholder {
  opacity: 0.42;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-moz-placeholder {
  opacity: 0.42;
}

label[data-shrink=false]+.MuiInputBase-formControl .emotion-class:focus::-ms-input-placeholder {
  opacity: 0.42;
}

.emotion-class.Mui-disabled {
  opacity: 1;
  -webkit-text-fill-color: rgba(0, 0, 0, 0.38);
}

.emotion-class:-webkit-autofill {
  -webkit-animation-duration: 5000s;
  animation-duration: 5000s;
  -webkit-animation-name: mui-auto-fill;
  animation-name: mui-auto-fill;
}

.emotion-class:-webkit-autofill {
  border-radius: inherit;
}

.emotion-class.Mui-disabled {
  color: #A6ABBF;
  -webkit-text-fill-color: #A6ABBF;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  max-height: 2em;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.54);
  margin-left: 8px;
  position: absolute;
  right: 17px;
}

.emotion-class button {
  color: #465AB6;
  margin-right: -12px!important;
}

.emotion-class button.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class button:hover {
  background-color: transparent;
}

.emotion-class button svg {
  width: 20px;
  height: 20px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-right: -12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: 1.5rem;
}

.emotion-class {
  text-align: left;
  position: absolute;
  bottom: 0;
  right: 0;
  top: -5px;
  left: 0;
  margin: 0;
  padding: 0 8px;
  pointer-events: none;
  border-radius: inherit;
  border-style: solid;
  border-width: 1px;
  overflow: hidden;
  min-width: 0%;
  border-color: rgba(0, 0, 0, 0.23);
  font-family: "Open Sans",sans-serif;
  font-size: 14px;
  color: #303757;
  border: 1px solid #E4E5EB;
  border-radius: 4px;
  padding: 6px 12px;
  line-height: 34px;
  min-height: auto;
  border-width: 2px;
  border-style: solid;
  outline: none;
  top: 0;
  -webkit-transition: border-color ease-in-out 0.15s,box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s,box-shadow ease-in-out 0.15s;
}

.emotion-class:hover {
  border-color: #B4BCE0;
}

.emotion-class:focus {
  border-color: #465AB6;
}

.emotion-class::-webkit-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::-moz-placeholder {
  color: #A6ABBF;
}

.emotion-class:-ms-input-placeholder {
  color: #A6ABBF;
}

.emotion-class::placeholder {
  color: #A6ABBF;
}

.emotion-class legend {
  display: none;
}

.emotion-class {
  float: unset;
  width: auto;
  overflow: hidden;
  padding: 0;
  line-height: 11px;
  -webkit-transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
  transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
}

.emotion-class {
  z-index: 1300;
}

.emotion-class {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow: var(--Paper-shadow);
  background-image: var(--Paper-overlay);
  outline: 0;
  transform-origin: top center;
  transform-origin: bottom center;
}

.emotion-class {
  display: grid;
  grid-auto-columns: max-content auto max-content;
  grid-auto-rows: max-content auto max-content;
}

.emotion-class .MuiPickersLayout-actionBar {
  grid-column: 1/4;
  grid-row: 3;
}

.emotion-class .MuiPickersLayout-toolbar {
  grid-column: 2/4;
  grid-row: 1;
}

.emotion-class .MuiPickersLayout-shortcuts {
  grid-column: 1;
  grid-row: 2/3;
}

.emotion-class {
  grid-column: 2;
  grid-row: 2;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-class {
  overflow: hidden;
  width: 320px;
  max-height: 336px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: 0 auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 336px;
}

.emotion-class>div:first-of-type {
  position: absolute;
  width: calc(100% - 40px);
  box-sizing: border-box;
  background: #fff;
  min-height: initial;
  max-height: initial;
  height: 54px;
  padding: 16px 0 6px;
  margin: 0 20px;
}

.emotion-class>div:first-of-type+div {
  margin-top: 54px;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) {
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  font-size: 14px;
  color: #465AB6;
  font-weight: 600;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .MuiButtonBase-root {
  display: none;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .PrivatePickersFadeTransitionGroup-root {
  color: #465AB6;
  font-weight: 600;
}

.emotion-class>div:first-of-type>div:nth-of-type(1) .PrivatePickersFadeTransitionGroup-root:nth-of-type(2) div {
  margin-right: 0;
}

.emotion-class>div:first-of-type>div:nth-of-type(2) {
  width: 100%;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-top: 12px;
  margin-bottom: 4px;
  padding-left: 24px;
  padding-right: 12px;
  max-height: 40px;
  min-height: 40px;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  margin-right: auto;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 500;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0.00938em;
}

.emotion-class {
  display: block;
  position: relative;
}

.emotion-class {
  margin-right: 6px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  padding: 5px;
  font-size: 1.125rem;
  margin-right: auto;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: 1.5rem;
  will-change: transform;
  -webkit-transition: -webkit-transform 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-right: -12px;
  color: #465AB6;
  padding: 5px;
  width: 34px;
  height: 34px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class svg {
  width: 24px;
  height: 24px;
}

.emotion-class {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: inherit;
}

.emotion-class {
  width: 24px;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-left: -12px;
  color: #465AB6;
  padding: 5px;
  width: 34px;
  height: 34px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-class:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-class:hover {
    background-color: transparent;
  }
}

.emotion-class.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-class.MuiIconButton-loading {
  color: transparent;
}

.emotion-class.Mui-disabled {
  color: #C5C8D5;
}

.emotion-class svg {
  width: 24px;
  height: 24px;
}

.emotion-class {
  display: block;
  position: relative;
}

.emotion-class {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-class {
  margin: 0;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  font-weight: 600;
  font-size: 12px;
  width: 36px;
  height: 40px;
  margin: 0 2px;
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  color: rgba(0, 0, 0, 0.6);
}

.emotion-class {
  display: block;
  position: relative;
  overflow-x: hidden;
  min-height: 240px;
}

.emotion-class>* {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
}

.emotion-class .MuiPickersSlideTransition-slideEnter-left {
  will-change: transform;
  -webkit-transform: translate(100%);
  -moz-transform: translate(100%);
  -ms-transform: translate(100%);
  transform: translate(100%);
  z-index: 1;
}

.emotion-class .MuiPickersSlideTransition-slideEnter-right {
  will-change: transform;
  -webkit-transform: translate(-100%);
  -moz-transform: translate(-100%);
  -ms-transform: translate(-100%);
  transform: translate(-100%);
  z-index: 1;
}

.emotion-class .MuiPickersSlideTransition-slideEnterActive {
  -webkit-transform: translate(0%);
  -moz-transform: translate(0%);
  -ms-transform: translate(0%);
  transform: translate(0%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
}

.emotion-class .MuiPickersSlideTransition-slideExit {
  -webkit-transform: translate(0%);
  -moz-transform: translate(0%);
  -ms-transform: translate(0%);
  transform: translate(0%);
}

.emotion-class .MuiPickersSlideTransition-slideExitActiveLeft-left {
  will-change: transform;
  -webkit-transform: translate(-100%);
  -moz-transform: translate(-100%);
  -ms-transform: translate(-100%);
  transform: translate(-100%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  z-index: 0;
}

.emotion-class .MuiPickersSlideTransition-slideExitActiveLeft-right {
  will-change: transform;
  -webkit-transform: translate(100%);
  -moz-transform: translate(100%);
  -ms-transform: translate(100%);
  transform: translate(100%);
  -webkit-transition: -webkit-transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  transition: transform 375ms cubic-bezier(0.35, 0.8, 0.4, 1) 0ms;
  z-index: 0;
}

.emotion-class {
  overflow: hidden;
}

.emotion-class button {
  margin-right: 2px!important;
}

.emotion-class {
  margin: 2px 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  color: rgba(0, 0, 0, 0.6);
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

.emotion-class {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 0;
  background-color: transparent;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  margin: 0 2px;
  line-height: 36px;
  font-size: 12px;
}

.emotion-class::-moz-focus-inner {
  border-style: none;
}

.emotion-class.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-class {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

@media (pointer: fine) {
  .emotion-class:hover {
    background-color: rgba(25, 118, 210, 0.04);
  }
}

.emotion-class:focus {
  background-color: rgba(25, 118, 210, 0.12);
}

.emotion-class:focus.Mui-selected {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-selected {
  color: #fff;
  background-color: #1976d2;
  font-weight: 500;
}

.emotion-class.Mui-selected:hover {
  will-change: background-color;
  background-color: #1565c0;
}

.emotion-class.Mui-disabled:not(.Mui-selected) {
  color: rgba(0, 0, 0, 0.38);
}

.emotion-class.Mui-disabled.emotion-class.Mui-selected {
  opacity: 0.6;
}

.emotion-class:focus {
  background-color: transparent;
}

.emotion-class:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.emotion-class:not(.Mui-selected) {
  border: 0;
}

.emotion-class.Mui-selected {
  background-color: #465AB6!important;
}

<div>
  <div
    class="emotion-class"
  >
    <div
      class="emotion-class"
    >
      <div>
        <span
          class="emotion-class"
          data-testid="InputLabel_TextLine"
        >
          text line
        </span>
        <p
          class="emotion-class"
          style="color: rgb(48, 55, 87);"
        >
          Description (text line)
        </p>
      </div>
    </div>
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      >
        text1
      </div>
      <input
        class="emotion-class"
        type="text"
        value="text1"
      />
    </div>
    <div
      class="emotion-class"
    >
      <div />
    </div>
  </div>
  <div
    class="emotion-class"
  >
    <div
      class="emotion-class"
    >
      <div>
        <span
          class="emotion-class"
          data-testid="InputLabel_TextLineWithoutValue"
        >
          text line (without value)
        </span>
      </div>
    </div>
    <div
      class="emotion-class"
    >
      <div
        class="emotion-class"
      />
      <input
        class="emotion-class"
        type="text"
        value=""
      />
    </div>
    <div
      class="emotion-class"
    >
      <div />
    </div>
  </div>
  <div
    class="emotion-class"
  >
    <div
      style="margin-bottom: 5px;"
    >
      <span
        class="emotion-class"
        data-testid="InputLabel_TextArea"
      >
        text area
      </span>
      <p
        class="emotion-class"
        style="color: rgb(48, 55, 87);"
      >
        Description (text area)
      </p>
    </div>
    <div
      class="emotion-class"
    >
      <textarea
        class="emotion-class"
        style="height: -104px;"
        variant="default"
      >
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin vehicula erat venenatis odio pellentesque dignissim vitae ac nunc. Donec molestie dignissim lorem, quis volutpat sapien pretium ut. Phasellus sit amet nulla finibus, hendrerit urna id, lacinia est. Donec vitae neque id odio laoreet venenatis quis quis est. Ut congue elementum scelerisque. Vivamus ullamcorper libero eu consectetur efficitur. Morbi eu lobortis mauris. Mauris molestie, dui in scelerisque egestas, ante elit faucibus risus, sit amet dictum massa sem eu dolor. Cras feugiat sagittis lorem et viverra.

Nam in est lectus. Integer nec elit nulla. Sed diam elit, lobortis quis tincidunt nec, convallis in mi. Phasellus finibus id justo et sodales. Maecenas molestie velit eget nisi commodo, id molestie mi iaculis. Nulla hendrerit mi purus, ultricies ultricies nisi convallis at. Donec non maximus justo. Donec auctor tellus id nisi vehicula gravida ac non justo. Praesent nec volutpat mauris. Vivamus vel felis ut lorem aliquam imperdiet ut eu risus. Donec et consequat dui, vulputate hendrerit justo. Aliquam erat volutpat. Mauris eu odio ante.

Duis sit amet mi at ligula rutrum eleifend id et risus. Quisque dignissim nec orci semper dictum. Etiam malesuada eu orci ut tincidunt. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Nam a ex augue. Aliquam erat volutpat. Quisque elit lorem, mollis eu metus iaculis, faucibus viverra mi. Curabitur egestas sem et lacinia elementum. Maecenas eu sollicitudin nunc.
      </textarea>
    </div>
    <div
      class="emotion-class"
    >
      <div />
    </div>
  </div>
  <div
    class="emotion-class"
  >
    <div
      style="margin-bottom: 5px;"
    >
      <span
        class="emotion-class"
        data-testid="InputLabel_TextAreaWithoutValue"
      >
        text area (without value)
      </span>
    </div>
    <div
      class="emotion-class"
    >
      <textarea
        class="emotion-class"
        style="height: -104px;"
        variant="default"
      />
    </div>
    <div
      class="emotion-class"
    >
      <div />
    </div>
  </div>
  <div
    class="emotion-class"
  >
    <div
      style="margin-bottom: 5px;"
    >
      <span
        class="emotion-class"
        data-testid="InputLabel_Dropdown"
      >
        dropdown
      </span>
      <p
        class="emotion-class"
        style="color: rgb(48, 55, 87);"
      >
        Description (dropdown)
      </p>
    </div>
    <div
      class="emotion-class"
    >
      <select
        class="emotion-class"
        data-testid="Select_Dropdown"
      >
        <option
          value=""
        >
          Select option
        </option>
        <option
          data-testid="SelectOption_OptionE"
          value="Option E"
        >
          Option E
        </option>
        <option
          data-testid="SelectOption_OptionF"
          value="Option F"
        >
          Option F
        </option>
        <option
          data-testid="SelectOption_Other"
          value=":-other-:"
        >
          Other option
        </option>
      </select>
      <icon-mock
        classname="css-1qbf8bs"
        disabled="false"
        size="28"
      />
    </div>
  </div>
  <div
    class="emotion-class"
  >
    <div
      class="emotion-class"
    >
      <div>
        <span
          class="emotion-class"
          data-testid="InputLabel_Date"
        >
          Date
        </span>
        <p
          class="emotion-class"
          style="color: rgb(48, 55, 87);"
        >
          Description (date)
        </p>
      </div>
      <a
        class=" emotion-class"
        data-testid="InputActionLabel_Button"
        style="text-align: right;"
      >
        <span
          class="emotion-class"
        >
          Reset
        </span>
      </a>
    </div>
    <div
      class="emotion-class"
    >
      <div
        class="MuiFormControl-root MuiTextField-root emotion-class"
      >
        <div
          class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-formControl MuiInputBase-adornedEnd emotion-class"
        >
          <input
            aria-invalid="false"
            autocomplete="off"
            class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputAdornedEnd emotion-class"
            id=":r7:"
            inputmode="text"
            placeholder="MM/DD/YYYY"
            type="text"
            value=""
          />
          <div
            class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium emotion-class"
          >
            <button
              aria-label="Choose date, selected date is Jan 26, 2021"
              class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium emotion-class"
              tabindex="0"
              type="button"
            >
              <svg
                aria-hidden="true"
                class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium emotion-class"
                data-testid="CalendarIcon"
                focusable="false"
                viewBox="0 0 24 24"
              >
                <path
                  d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
                />
              </svg>
            </button>
          </div>
          <fieldset
            aria-hidden="true"
            class="MuiOutlinedInput-notchedOutline emotion-class"
          >
            <legend
              class="emotion-class"
            >
              <span
                aria-hidden="true"
                class="notranslate"
              >
                ​
              </span>
            </legend>
          </fieldset>
        </div>
      </div>
    </div>
    <div
      class="MuiPopper-root MuiPickersPopper-root emotion-class"
      role="dialog"
      style="position: fixed; top: 0px; left: 0px; display: none;"
    >
      <div
        data-testid="sentinelStart"
        tabindex="-1"
      />
      <div
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation8 MuiPickersPopper-paper emotion-class"
        style="--Paper-shadow: 0px 5px 5px -3px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12); opacity: 0; transform: scale(0.75, 0.5625); visibility: hidden;"
        tabindex="-1"
      >
        <div
          class="MuiPickersLayout-root emotion-class"
        >
          <div
            class="MuiPickersLayout-contentWrapper emotion-class"
          >
            <div
              class="MuiDateCalendar-root emotion-class"
            >
              <div
                class="MuiPickersCalendarHeader-root emotion-class"
              >
                <div
                  aria-live="polite"
                  class="MuiPickersCalendarHeader-labelContainer emotion-class"
                  role="presentation"
                >
                  <div
                    class="MuiPickersFadeTransitionGroup-root emotion-class"
                  >
                    <div
                      class="MuiPickersCalendarHeader-label emotion-class"
                      id=":ra:-grid-label"
                      style="opacity: 1;"
                    >
                      January 2021
                    </div>
                  </div>
                  <button
                    aria-label="calendar view is open, switch to year view"
                    class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall MuiPickersCalendarHeader-switchViewButton emotion-class"
                    tabindex="0"
                    type="button"
                  >
                    <svg
                      aria-hidden="true"
                      class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiPickersCalendarHeader-switchViewIcon emotion-class"
                      data-testid="ArrowDropDownIcon"
                      focusable="false"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M7 10l5 5 5-5z"
                      />
                    </svg>
                  </button>
                </div>
                <div
                  class="MuiPickersArrowSwitcher-root emotion-class"
                  style="opacity: 1; webkit-transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
                >
                  <button
                    aria-label="Previous month"
                    class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-previousIconButton emotion-class"
                    tabindex="0"
                    title="Previous month"
                    type="button"
                  >
                    <svg
                      aria-hidden="true"
                      class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-leftArrowIcon emotion-class"
                      data-testid="ArrowLeftIcon"
                      focusable="false"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M15.41 16.59L10.83 12l4.58-4.59L14 6l-6 6 6 6 1.41-1.41z"
                      />
                    </svg>
                  </button>
                  <div
                    class="MuiPickersArrowSwitcher-spacer emotion-class"
                  />
                  <button
                    aria-label="Next month"
                    class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeStart MuiIconButton-sizeMedium MuiPickersArrowSwitcher-button MuiPickersArrowSwitcher-nextIconButton emotion-class"
                    tabindex="0"
                    title="Next month"
                    type="button"
                  >
                    <svg
                      aria-hidden="true"
                      class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit MuiPickersArrowSwitcher-rightArrowIcon emotion-class"
                      data-testid="ArrowRightIcon"
                      focusable="false"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"
                      />
                    </svg>
                  </button>
                </div>
              </div>
              <div
                class="MuiPickersFadeTransitionGroup-root MuiDateCalendar-viewTransitionContainer emotion-class"
              >
                <div
                  style="opacity: 1;"
                >
                  <div
                    aria-labelledby=":ra:-grid-label"
                    class="MuiDayCalendar-root emotion-class"
                    role="grid"
                  >
                    <div
                      class="MuiDayCalendar-header emotion-class"
                      role="row"
                    >
                      <span
                        aria-label="Sunday"
                        class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                        role="columnheader"
                      >
                        S
                      </span>
                      <span
                        aria-label="Monday"
                        class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                        role="columnheader"
                      >
                        M
                      </span>
                      <span
                        aria-label="Tuesday"
                        class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                        role="columnheader"
                      >
                        T
                      </span>
                      <span
                        aria-label="Wednesday"
                        class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                        role="columnheader"
                      >
                        W
                      </span>
                      <span
                        aria-label="Thursday"
                        class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                        role="columnheader"
                      >
                        T
                      </span>
                      <span
                        aria-label="Friday"
                        class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                        role="columnheader"
                      >
                        F
                      </span>
                      <span
                        aria-label="Saturday"
                        class="MuiTypography-root MuiTypography-caption MuiDayCalendar-weekDayLabel emotion-class"
                        role="columnheader"
                      >
                        S
                      </span>
                    </div>
                    <div
                      class="MuiPickersSlideTransition-root MuiDayCalendar-slideTransition emotion-class"
                      role="presentation"
                    >
                      <div
                        class="MuiDayCalendar-monthContainer emotion-class"
                        role="rowgroup"
                      >
                        <div
                          aria-rowindex="1"
                          class="MuiDayCalendar-weekContainer emotion-class"
                          role="row"
                        >
                          <button
                            aria-colindex="1"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                            data-timestamp="1609027200000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            27
                          </button>
                          <button
                            aria-colindex="2"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                            data-timestamp="1609113600000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            28
                          </button>
                          <button
                            aria-colindex="3"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                            data-timestamp="1609200000000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            29
                          </button>
                          <button
                            aria-colindex="4"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                            data-timestamp="1609286400000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            30
                          </button>
                          <button
                            aria-colindex="5"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                            data-timestamp="1609372800000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            31
                          </button>
                          <button
                            aria-colindex="6"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1609459200000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            1
                          </button>
                          <button
                            aria-colindex="7"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1609545600000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            2
                          </button>
                        </div>
                        <div
                          aria-rowindex="2"
                          class="MuiDayCalendar-weekContainer emotion-class"
                          role="row"
                        >
                          <button
                            aria-colindex="1"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1609632000000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            3
                          </button>
                          <button
                            aria-colindex="2"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1609718400000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            4
                          </button>
                          <button
                            aria-colindex="3"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1609804800000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            5
                          </button>
                          <button
                            aria-colindex="4"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1609891200000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            6
                          </button>
                          <button
                            aria-colindex="5"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1609977600000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            7
                          </button>
                          <button
                            aria-colindex="6"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1610064000000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            8
                          </button>
                          <button
                            aria-colindex="7"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1610150400000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            9
                          </button>
                        </div>
                        <div
                          aria-rowindex="3"
                          class="MuiDayCalendar-weekContainer emotion-class"
                          role="row"
                        >
                          <button
                            aria-colindex="1"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1610236800000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            10
                          </button>
                          <button
                            aria-colindex="2"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1610323200000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            11
                          </button>
                          <button
                            aria-colindex="3"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1610409600000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            12
                          </button>
                          <button
                            aria-colindex="4"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1610496000000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            13
                          </button>
                          <button
                            aria-colindex="5"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1610582400000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            14
                          </button>
                          <button
                            aria-colindex="6"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1610668800000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            15
                          </button>
                          <button
                            aria-colindex="7"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1610755200000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            16
                          </button>
                        </div>
                        <div
                          aria-rowindex="4"
                          class="MuiDayCalendar-weekContainer emotion-class"
                          role="row"
                        >
                          <button
                            aria-colindex="1"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1610841600000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            17
                          </button>
                          <button
                            aria-colindex="2"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1610928000000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            18
                          </button>
                          <button
                            aria-colindex="3"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1611014400000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            19
                          </button>
                          <button
                            aria-colindex="4"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1611100800000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            20
                          </button>
                          <button
                            aria-colindex="5"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1611187200000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            21
                          </button>
                          <button
                            aria-colindex="6"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1611273600000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            22
                          </button>
                          <button
                            aria-colindex="7"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1611360000000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            23
                          </button>
                        </div>
                        <div
                          aria-rowindex="5"
                          class="MuiDayCalendar-weekContainer emotion-class"
                          role="row"
                        >
                          <button
                            aria-colindex="1"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1611446400000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            24
                          </button>
                          <button
                            aria-colindex="2"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1611532800000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            25
                          </button>
                          <button
                            aria-colindex="3"
                            aria-selected="true"
                            class="MuiButtonBase-root MuiPickersDay-root Mui-selected MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1611619200000"
                            role="gridcell"
                            tabindex="0"
                            type="button"
                          >
                            26
                          </button>
                          <button
                            aria-colindex="4"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1611705600000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            27
                          </button>
                          <button
                            aria-colindex="5"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1611792000000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            28
                          </button>
                          <button
                            aria-colindex="6"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1611878400000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            29
                          </button>
                          <button
                            aria-colindex="7"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1611964800000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            30
                          </button>
                        </div>
                        <div
                          aria-rowindex="6"
                          class="MuiDayCalendar-weekContainer emotion-class"
                          role="row"
                        >
                          <button
                            aria-colindex="1"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin emotion-class"
                            data-timestamp="1612051200000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            31
                          </button>
                          <button
                            aria-colindex="2"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                            data-timestamp="1612137600000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            1
                          </button>
                          <button
                            aria-colindex="3"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                            data-timestamp="1612224000000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            2
                          </button>
                          <button
                            aria-colindex="4"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                            data-timestamp="1612310400000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            3
                          </button>
                          <button
                            aria-colindex="5"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                            data-timestamp="1612396800000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            4
                          </button>
                          <button
                            aria-colindex="6"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                            data-timestamp="1612483200000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            5
                          </button>
                          <button
                            aria-colindex="7"
                            aria-selected="false"
                            class="MuiButtonBase-root MuiPickersDay-root MuiPickersDay-dayWithMargin MuiPickersDay-dayOutsideMonth emotion-class"
                            data-timestamp="1612569600000"
                            role="gridcell"
                            tabindex="-1"
                            type="button"
                          >
                            6
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        data-testid="sentinelEnd"
        tabindex="-1"
      />
    </div>
  </div>
  <div
    class="emotion-class"
  />
  <div
    class="emotion-class"
  />
  <div
    class="emotion-class"
  />
</div>
`;
