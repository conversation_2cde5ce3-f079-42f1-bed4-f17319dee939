import i18n from '../../../../i18n/i18n';
import { Validators } from '../../../../utils';
import InputNumber from '../../../FormElements/InputNumber/InputNumber';
import { numberMaskDefault } from '../../../LineItems/LineItems.helpers';
import { CustomField, CustomFieldValue } from '../../CustomFields';
import { useCustomFieldInputsErrorHooks } from '../CustomFieldInputs.hooks';
import PropTypes from 'prop-types';
import React from 'react';
import createNumberMask from 'text-mask-addons/dist/createNumberMask';

const customFieldNumberMask = createNumberMask({
  ...numberMaskDefault,
  integerLimit: 11,
});

const CustomFieldNumber = ({
  customField,
  defaultValue,
  onChange,
  errors = [],
  disabled = false,
}: {
  customField: CustomField;
  defaultValue: CustomFieldValue;
  onChange?: (valueObject: CustomFieldValue) => void;
  errors?: string[];
  disabled?: boolean;
}) => {
  const { customFieldErrors, broadcastChanges } =
    useCustomFieldInputsErrorHooks({
      customField,
      defaultValue,
      onChange,
      errors,
      validatorFn: Validators.CustomFields.text_line,
    });
  return (
    <InputNumber
      label={customField.label}
      labelTooltip={customField.tooltip}
      labelIsLocked={!customField.visible_to_vendors}
      labelLockText={i18n.t(
        'CustomFieldConfig:CustomFieldConfigItem.VisibleToVendors.Tooltip'
      )}
      additionalInfo={customField.description}
      defaultValue={defaultValue?.value}
      isOptional={!customField.mandatory}
      onChange={(value) => broadcastChanges(value ?? '')}
      errors={customFieldErrors}
      ghosted={true}
      mask={customFieldNumberMask}
      disabled={disabled}
    />
  );
};

CustomFieldNumber.propTypes = {
  customField: PropTypes.object,
  defaultValue: PropTypes.object,
  onChange: PropTypes.func,
  errors: PropTypes.array,
};

export default CustomFieldNumber;
