import { useTheme } from '@emotion/react';
import PropTypes from 'prop-types';
import React, { useState } from 'react';
import { CustomField, CustomFieldValue } from '../CustomFields.d';
import * as CustomFieldInputs from '../Inputs/CustomFieldInputs';
import * as CustomFieldPreviews from '../Preview/CustomFieldPreviews';
import { CustomFieldPreviewVariant } from '../Preview/CustomFieldPreviews.d';
import { isOtherCustomFieldValue } from '../custom-fields.helpers';

const CustomFieldWrapper = ({
  customField,
  value,
  errors = [],
  onChange = null,
  nestedFieldValues,
  nestedOnChange = null,
  nestedErrors = [],
  readOnly = false,
  autoFocus = false,
  variant: initVariant = 'default',
  readOnlyPlaceholder = '',
  disabled = false,
}: {
  customField: CustomField;
  value?: CustomFieldValue;
  errors?: string[];
  onChange?: (value: CustomFieldValue) => void;
  nestedFieldValues?: CustomFieldValue[];
  nestedOnChange?: (value: CustomFieldValue) => void;
  nestedErrors?: string[];
  readOnly?: boolean;
  autoFocus?: boolean;
  variant?: CustomFieldPreviewVariant;
  readOnlyPlaceholder?: React.ReactNode;
  disabled?: boolean;
}) => {
  const {
    CustomFieldPreview: { variant: themeCustomFieldPreviewVariant } = {
      variant: initVariant,
    },
  } = useTheme();

  // Variant order - theme > component prop > 'default'
  const [customFieldPreviewVariant] = useState(() =>
    initVariant !== 'default' ? initVariant : themeCustomFieldPreviewVariant
  );

  if (readOnly && CustomFieldPreviews[customField.type]) {
    const prepareFieldValue = (
      value: CustomFieldValue,
      nestedFieldValues: CustomFieldValue[]
    ) =>
      isOtherCustomFieldValue(value) && nestedFieldValues?.[0]?.value
        ? nestedFieldValues?.[0]
        : value;

    return React.createElement(CustomFieldPreviews[customField.type], {
      customField,
      defaultValue: prepareFieldValue(value, nestedFieldValues),
      variant: customFieldPreviewVariant,
      placeholder: readOnlyPlaceholder,
      disabled,
    });
  } else if (!readOnly && CustomFieldInputs[customField.type]) {
    return React.createElement(CustomFieldInputs[customField.type], {
      customField,
      defaultValue: value,
      errors,
      onChange,
      autoFocus,
      nestedFieldValues,
      nestedOnChange,
      nestedErrors,
      singleChoice: customField.max_len === 1,
      disabled,
    });
  }
  return <></>;
};

CustomFieldWrapper.propTypes = {
  customField: PropTypes.object,
  value: PropTypes.object,
  errors: PropTypes.array,
  onChange: PropTypes.func,
  nestedOnChange: PropTypes.func,
  nestedFieldValues: PropTypes.array,
  nestedErrors: PropTypes.array,
  readOnly: PropTypes.bool,
  readOnlyPlaceholder: PropTypes.oneOfType([
    PropTypes.object,
    PropTypes.string,
  ]),
  disabled: PropTypes.bool,
};

export default CustomFieldWrapper;
