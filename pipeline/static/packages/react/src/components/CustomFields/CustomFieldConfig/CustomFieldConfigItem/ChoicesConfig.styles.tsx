import { Colors, Fonts } from '../../../../styles/global.styles';
import styled from '@emotion/styled';
import { Grid } from '@mui/material';

export const DragHandler = styled('div')({
  position: 'absolute',
  top: 8,
  left: 5,
  visibility: 'hidden',
});

export const OptionWrapper = styled(Grid)({
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  paddingLeft: 5,
  input: {
    marginLeft: 10,
  },
});

export const ActionsOptionWrapper = styled(Grid)({
  paddingTop: 5,
  paddingLeft: 5,
});

export const ActionsOptionInner = styled('div')({
  paddingLeft: 10,
  span: {
    marginRight: 5,
  },
});

export const OptionInner = styled(Grid)({
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  flexWrap: 'wrap',
  ...Fonts.default,
  div: {
    flex: '1 0 90%',
  },
});

export const AddNestedFieldsLink = styled('span')({
  fontSize: 12,
  color: Colors.blue1,
  fontWeight: 600,
  position: 'absolute',
  right: 0,
  top: 12,
  visibility: 'hidden',
  cursor: 'pointer',
  zIndex: 100,
});

export const RemoveButtonWrapper = styled(Grid)({
  position: 'absolute',
  top: 5,
  right: 0,
  visibility: 'hidden',
});

export const OptionLink = styled('span')({
  color: Colors.blue1,
  cursor: 'pointer',
});

export const NestedFieldsEditButton = styled('div')({
  visibility: 'hidden',
  position: 'absolute',
  right: 10,
  top: 10,
});

export const NestedFieldsPreviewWrapper = styled('div')({
  position: 'relative',
  marginLeft: 30,
  '&:hover': {
    cursor: 'pointer',

    '.NestedFieldsEditButton': {
      visibility: 'visible',
      svg: {
        fill: Colors.blue1,
      },
    },
  },
});

export const OptionContainer = styled('div')({
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  paddingLeft: 30,
  marginLeft: -30,

  '&:hover': {
    '.NestedFieldsPreviewContainer': {
      backgroundColor: Colors.blue4,

      '.PreviewContainer, .PreviewContainer:hover': {
        backgroundColor: Colors.blue4,
      },
    },

    '.ChoicesConfig-OptionWrapper': {
      borderRadius: 4,
      backgroundColor: Colors.blue4,
    },

    '.ChoicesConfig-DragHandler': {
      visibility: 'visible',
    },

    '.ChoicesConfig-AddNestedFieldsLink': {
      visibility: 'visible',
    },

    '.ChoicesConfig-RemoveButtonWrapper': {
      visibility: 'visible',
    },
  },
});
