{
    "compilerOptions": {
        "declaration": false,
        "module": "esnext",
        "target": "es2017",
        "lib": [
            "dom",
            "es2022"
        ],
        "types": [
            "vitest/globals"
        ],
        "sourceMap": true,
        "jsx": "react-jsx",
        "moduleResolution": "node",
        "allowSyntheticDefaultImports": true,
        "esModuleInterop": true,
        "resolveJsonModule": true,
        "removeComments": true,
        "skipLibCheck": true,
    },
    "include": [
        "packages/*/src/**/*"
    ],
    "exclude": [
        "packages/*/node_modules",
        "packages/*/dist",
        "packages/*/src/**/*.stories.tsx",
        "packages/*/src/**/*.test.tsx",
        "packages/*/src/**/*.vitest.tsx"
    ]
}
