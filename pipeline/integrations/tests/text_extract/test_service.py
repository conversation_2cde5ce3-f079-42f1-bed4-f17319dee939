from unittest.mock import Mock, patch

from django.test import SimpleTestCase

from integrations.services.text_extract.api import TextExtractAPI
from integrations.services.text_extract.exceptions import TextExtractException
from integrations.services.text_extract.service import TextExtract, presign_file_url


class TextExtractServiceTestCase(SimpleTestCase):
    def setUp(self):
        self.api_mock = Mock(spec=TextExtractAPI)
        self.presign_func_mock = Mock()
        self.file_exists_func_mock = Mock()
        self.service = TextExtract(api=self.api_mock, presign_func=self.presign_func_mock, file_exists_func=self.file_exists_func_mock)

    @patch('sentry_sdk.capture_exception')
    def test_extract_text_success(self, mock_capture_exception):
        url = "https://example.com/document.pdf"
        expected_text = "This is the extracted text content."
        self.presign_func_mock.return_value = url
        self.api_mock.extract_text.return_value = expected_text
        self.file_exists_func_mock.return_value = True

        result = self.service.extract_from_file(bucket="test-bucket", key="test-key")

        self.assertEqual(result, expected_text)
        self.presign_func_mock.assert_called_once_with(bucket="test-bucket", key="test-key")
        self.api_mock.extract_text.assert_called_once_with(url=url)
        mock_capture_exception.assert_not_called()

    @patch('sentry_sdk.capture_exception')
    def test_extract_text_failure(self, mock_capture_exception):
        url = "https://example.com/document.pdf"
        self.presign_func_mock.return_value = url
        self.api_mock.extract_text.side_effect = TextExtractException("API Error")
        self.file_exists_func_mock.return_value = True

        result = self.service.extract_from_file(bucket="test-bucket", key="test-key")

        self.assertEqual(result, "")
        self.presign_func_mock.assert_called_once_with(bucket="test-bucket", key="test-key")
        self.api_mock.extract_text.assert_called_once_with(url=url)
        mock_capture_exception.assert_called_once()

    @patch('sentry_sdk.capture_exception')
    def test_extract_text_no_presigned_url(self, mock_capture_exception):
        self.presign_func_mock.return_value = None
        self.api_mock.extract_text.side_effect = TextExtractException("400 Bad Request")
        self.file_exists_func_mock.return_value = True

        result = self.service.extract_from_file(bucket="test-bucket", key="test-key")

        self.assertEqual(result, "")
        self.presign_func_mock.assert_called_once_with(bucket="test-bucket", key="test-key")
        self.api_mock.extract_text.assert_called_once_with(url=None)
        mock_capture_exception.assert_called_once()

    @patch('integrations.services.text_extract.service.s3_presigned_url')
    def test_presign_file_url(self, mock_s3_presigned_url):
        bucket = "test-bucket"
        key = "folder/test-file.pdf"
        expected_url = "https://example.com/presigned-url"
        mock_s3_presigned_url.return_value = expected_url
        self.file_exists_func_mock.return_value = True

        result = presign_file_url(bucket=bucket, key=key)

        self.assertEqual(result, expected_url)
        mock_s3_presigned_url.assert_called_once_with(file_name="test-file.pdf", bucket_name=bucket, key_name=key)

    def test_file_not_exists(self):
        self.file_exists_func_mock.return_value = False

        result = self.service.extract_from_file(bucket="test-bucket", key="test-key")

        self.assertEqual(result, "")
        self.file_exists_func_mock.assert_called_once_with("test-bucket", "test-key")
