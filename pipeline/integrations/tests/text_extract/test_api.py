import json

import responses
from django.test import SimpleTestCase

from integrations.services.text_extract.api import TextExtractAPI
from integrations.services.text_extract.exceptions import TextExtractException

TEST_URI = "https://fake-text-extract.org"
TEST_API_KEY = "text_extract_key_123"
TEST_URL = "https://example.com/document.pdf"
EXTRACTED_TEXT = "This is the extracted text content."


class TextExtractAPITestCase(SimpleTestCase):
    @responses.activate
    def test_posts_url_to_api(self):
        responses.post(f"{TEST_URI}/extract", body=json.dumps({"content": EXTRACTED_TEXT}))

        api = TextExtractAPI(uri=TEST_URI, api_key=TEST_API_KEY)
        result = api.extract_text(url=TEST_URL)

        self.assertEqual(result, EXTRACTED_TEXT)

        self.assertEqual(len(responses.calls), 1)
        request = responses.calls[0].request
        body = json.loads(request.body.decode())

        self.assertEqual(body, {"url": TEST_URL})

        self.assertIn("X-API-Key", request.headers)
        self.assertEqual(request.headers["X-API-Key"], TEST_API_KEY)

    @responses.activate
    def test_raises_failures(self):
        responses.post(TEST_URI, json={"message": "Unauthorized"}, status=401)

        api = TextExtractAPI(uri=TEST_URI, api_key=TEST_API_KEY)
        with self.assertRaises(TextExtractException) as cm:
            api.extract_text(url=TEST_URL)

    def test_initialization_with_missing_credentials(self):
        with self.assertRaises(AssertionError):
            TextExtractAPI(uri="", api_key="")
        with self.assertRaises(AssertionError):
            TextExtractAPI(uri=TEST_URI, api_key="")
        with self.assertRaises(AssertionError):
            TextExtractAPI(uri="", api_key=TEST_API_KEY)
