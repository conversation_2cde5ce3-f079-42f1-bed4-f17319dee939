from rest_framework import status

from integrations.models import Ten99PolicyConfig
from integrations.tests.factories.ten99policy import Ten99PConfigFactory
from shortlist.tests.helpers import APITestCaseMixin, TenantTestCase


def create_ten99_config() -> Ten99PolicyConfig:
    return Ten99PConfigFactory(
        enabled=True,
        job_category_codes_mapping={
            "Photography": "jc_SASjdXGrLK",
            "Spokesperson / Influencer": "jc_V9CcPzeTWr",
        },
    )


class Ten99ViewsTestCase(TenantTestCase):
    def assertTen99Config(self, config_data: dict, config: Ten99PolicyConfig):
        self.assertEqual(list(config_data.keys()), ['job_locations', 'job_categories'])
        self.assertIsInstance(config_data['job_locations'], dict)
        self.assertEqual(len(config_data['job_locations']), 50)
        self.assertEqual(
            config_data['job_categories'],
            {v: k for k, v in config.job_category_codes_mapping.items()},
        )


class Ten99BuyerViewsTestCase(APITestCaseMixin, Ten99ViewsTestCase):
    with_login_user = True

    ten99_config_api = '/api/ten99p/config/'

    def test_get_ten99_config(self):
        ten99_config = create_ten99_config()

        response_data = self.check_response(status.HTTP_200_OK, self.api_client.get(self.ten99_config_api)).json()

        self.assertTen99Config(response_data, ten99_config)


class Ten99VendorViewsTestCase(APITestCaseMixin, Ten99ViewsTestCase):
    with_vendor_user = True

    ten99_config_api = '/api/v/ten99p/config/'

    def test_get_ten99_config(self):
        ten99_config = create_ten99_config()

        response_data = self.check_response(status.HTTP_200_OK, self.api_client.get(self.ten99_config_api)).json()

        self.assertTen99Config(response_data, ten99_config)
