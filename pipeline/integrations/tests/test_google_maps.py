from django.test import TestCase
from unittest.mock import Mock

from integrations.google_maps import GoogleMaps


class GoogleMapsTest(TestCase):
    def test_maps_response_parsing(self):
        client = Mock()
        client.places_autocomplete = Mock(return_value=[{'place_id': '1'}])
        client.place = Mock(return_value={'result': {'id': '2'}})
        sut = GoogleMaps(client)

        result = sut.find("anything")

        self.assertEqual(result, {'query': 'anything', '_geoloc': {}, 'objectID': '2'})

    def test_no_place(self):
        client = Mock()
        client.places_autocomplete = Mock(return_value=[{'place_id': '1'}])
        client.place = Mock(return_value={})
        sut = GoogleMaps(client)

        result = sut.find("anything")

        self.assertEqual(result, {})

    def test_empty_responses(self):
        client = Mock()
        client.places_autocomplete = Mock(return_value=[])
        client.geocode = Mock(return_value=[])
        sut = GoogleMaps(client)

        result = sut.find("anything")

        self.assertEqual(result, {})

    def test_city_extraction(self):
        html = '<span class="locality">Test</span>'

        result = GoogleMaps._extract_city(html)

        self.assertEqual(result, "Test")

    def test_wrong_html(self):
        html = '<span class="sdf">Test</span>'

        result = GoogleMaps._extract_city(html)

        self.assertEqual(result, None)

    def test_city_extraction_empty_span(self):
        html = '<span class="locality"></span>'

        result = GoogleMaps._extract_city(html)

        self.assertEqual(result, None)

    def test_empty(self):
        result = GoogleMaps._extract_city("")

        self.assertEqual(result, None)

    def test_example_responses(self):
        client = Mock()
        client.places_autocomplete = Mock(
            return_value=[
                {
                    'terms': [{'value': 'Poznań', 'offset': 0}, {'value': 'Poland', 'offset': 8}],
                    'description': 'Poznań, Poland',
                    'reference': 'CjQnAAAAONtIFmfl2hIBD6ru1rNwCO621oS3nYLc0NzUYht1XADl1Pu_wrXpbIO5KVd08k_pEhCagU-zk4iM0EBssG3vj7k6GhTsGQyD6BtSFtMzOThZbDS2c6QoyQ',
                    'structured_formatting': {
                        'secondary_text': 'Poland',
                        'main_text_matched_substrings': [{'length': 6, 'offset': 0}],
                        'main_text': 'Poznań',
                    },
                    'matched_substrings': [{'length': 6, 'offset': 0}],
                    'place_id': 'ChIJtwrh7NJEBEcR0b80A5gx6qQ',
                    'id': '0730c3e35b6d5367031ab85ef2225d765089a90c',
                    'types': ['locality', 'political', 'geocode'],
                }
            ]
        )
        client.place = Mock(
            return_value={
                'status': 'OK',
                'html_attributions': [],
                'result': {
                    'geometry': {'location': {'lat': 52.406374, 'lng': 16.9251681}},
                    'place_id': 'ChIJtwrh7NJEBEcR0b80A5gx6qQ',
                    'formatted_address': 'Poznań, Poland',
                    'address_components': [
                        {'long_name': 'Poznań', 'types': ['locality', 'political'], 'short_name': 'Poznań'},
                        {'long_name': 'London', 'types': ['postal_town'], 'short_name': 'Luboń'},
                        {
                            'long_name': 'Poznań County',
                            'types': ['administrative_area_level_2', 'political'],
                            'short_name': 'Poznań County',
                        },
                        {
                            'long_name': 'Greater Poland Voivodeship',
                            'types': ['administrative_area_level_1', 'political'],
                            'short_name': 'Greater Poland Voivodeship',
                        },
                        {'long_name': 'Poland', 'types': ['country', 'political'], 'short_name': 'PL'},
                        {'long_name': '61', 'types': ['postal_code_prefix', 'postal_code'], 'short_name': '61'},
                    ],
                    'id': '0730c3e35b6d5367031ab85ef2225d765089a90c',
                },
            }
        )
        sut = GoogleMaps(client)

        result = sut.find("anything")

        self.assertEqual(
            result,
            {
                '_geoloc': {'lat': 52.406374, 'lng': 16.9251681},
                'administrative': 'Greater Poland Voivodeship',
                'city': 'Poznań',
                'country': 'Poland',
                'formatted_address': 'Poznań, Poland',
                'objectID': '0730c3e35b6d5367031ab85ef2225d765089a90c',
                'postal_code': '61',
                'query': 'anything',
                'state': 'Greater Poland Voivodeship',
            },
        )

    def test_missing_city_response(self):
        client = Mock()
        client.places_autocomplete = Mock(
            return_value=[
                {
                    'terms': [{'value': 'Poznań', 'offset': 0}, {'value': 'Poland', 'offset': 8}],
                    'description': 'Poznań, Poland',
                    'reference': 'CjQnAAAAONtIFmfl2hIBD6ru1rNwCO621oS3nYLc0NzUYht1XADl1Pu_wrXpbIO5KVd08k_pEhCagU-zk4iM0EBssG3vj7k6GhTsGQyD6BtSFtMzOThZbDS2c6QoyQ',
                    'structured_formatting': {
                        'secondary_text': 'Poland',
                        'main_text_matched_substrings': [{'length': 6, 'offset': 0}],
                        'main_text': 'Poznań',
                    },
                    'matched_substrings': [{'length': 6, 'offset': 0}],
                    'place_id': 'ChIJtwrh7NJEBEcR0b80A5gx6qQ',
                    'id': '0730c3e35b6d5367031ab85ef2225d765089a90c',
                    'types': ['locality', 'political', 'geocode'],
                }
            ]
        )
        client.place = Mock(
            return_value={
                'status': 'OK',
                'html_attributions': [],
                'result': {
                    'geometry': {'location': {'lat': 52.406374, 'lng': 16.9251681}},
                    'adr_address': '<span class="street-address">17 Rushcroft Rd</span>, <span class="locality">London</span> <span class="postal-code">E4 8SG</span>, <span class="country-name">UK</span>',
                    'place_id': 'ChIJtwrh7NJEBEcR0b80A5gx6qQ',
                    'formatted_address': '17 Rushcroft Rd, London E4 8SG, UK',
                    'address_components': [
                        {'long_name': '17', 'types': ['street_number'], 'short_name': '17'},
                        {'long_name': 'Rushcroft Road', 'types': ['route'], 'short_name': 'Rushcroft Rd'},
                        {'long_name': 'London', 'types': ['postal_town'], 'short_name': 'London'},
                        {
                            'long_name': 'Greater London',
                            'types': ['administrative_area_level_2', 'political'],
                            'short_name': 'Greater London',
                        },
                        {'long_name': 'England', 'types': ['administrative_area_level_1', 'political'], 'short_name': 'England'},
                        {'long_name': 'United Kingdom', 'types': ['country', 'political'], 'short_name': 'GB'},
                        {'long_name': 'E4 8SG', 'types': ['postal_code'], 'short_name': 'E4 8SG'},
                    ],
                    'id': '0730c3e35b6d5367031ab85ef2225d765089a90c',
                },
            }
        )
        sut = GoogleMaps(client)

        result = sut.find("anything")

        self.assertEqual(
            result,
            {
                'objectID': '0730c3e35b6d5367031ab85ef2225d765089a90c',
                '_geoloc': {'lat': 52.406374, 'lng': 16.9251681},
                'formatted_address': '17 Rushcroft Rd, London E4 8SG, UK',
                'street_number': '17',
                'street_address': 'Rushcroft Road',
                'state': 'England',
                'administrative': 'England',
                'country': 'United Kingdom',
                'postal_code': 'E4 8SG',
                'city': 'London',
                'query': 'anything',
            },
        )
