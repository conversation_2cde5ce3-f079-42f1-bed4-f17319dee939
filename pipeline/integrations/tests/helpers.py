import json
import os
from functools import cached_property
from unittest import mock

from payments.pay_integration.model import PayIntegration, PayOperator
from payments.tests.factories.pay import factory_payment_processor


class ShortlistPayMockMoneyCorp:
    payments = mock.MagicMock()
    recipients = mock.MagicMock()
    integration = PayIntegration(api_token="Any", account_reference="AXXTEST")  # noqa: S106

    def __init__(self, *, enabled=True):
        self.__enabled = enabled

    @cached_property
    def current_payment_processor(self):
        if self.__enabled:
            return factory_payment_processor(
                PayOperator.MONEYCORP,
                config={'account_id': 1},
            )

    def get_payment_processors(self):
        return [self.current_payment_processor] if self.current_payment_processor else []

    def get_moneycorp_payment_processor_for_processing(self):
        for processor in self.get_payment_processors():
            if processor.provider == PayOperator.MONEYCORP and processor.payments_processing_enabled:
                return processor


class FakeGoogleMapsClient:
    def __init__(self, *args, **kwargs):
        pass

    def geocode(self, *args, **kwargs):
        return self._load_json_data("geocode")

    def place(self, *args, **kwargs):
        return self._load_json_data("place")

    def places_autocomplete(self, *args, **kwargs):
        return self._load_json_data("places_autocomplete")

    def _load_json_data(self, filename):
        path = os.path.join(os.path.dirname(__file__), "fixtures", "GoogleMaps", f"{filename}.json")
        with open(path) as file:
            return json.load(file)
