import json

import celery.exceptions
import responses

from django.test import override_settings
from unittest.mock import patch

from integrations.tasks import global_send_to_zapier
from shortlist.tests.helpers import BaseTestCase

ZAPIER_HOOK_URL = "https://hooks.zapier.com/hooks/catch/000000/XXXXXXX/"


@override_settings(INTERNAL_ZAPIER_WEBHOOKS=dict(webhook=ZAPIER_HOOK_URL))
class TestGlobalSendToZapier(BaseTestCase):
    @responses.activate
    def test_global_send_to_zapier_successful(self):
        responses.add(responses.POST, ZAPIER_HOOK_URL, json={"status": "success"})

        message = {"some_arbitrary_keys": "and_their_values"}
        with patch("integrations.tasks.logger") as logger_mock:
            global_send_to_zapier("webhook", message)

        logger_mock.exception.assert_not_called()
        self.assertEqual(1, len(responses.calls))

        call = responses.calls[0]
        self.assertEqual(json.loads(call.request.body), message)

    @responses.activate
    def test_global_send_to_zapier_failure(self):
        responses.add(responses.POST, ZAPIER_HOOK_URL, json={"status": "fail"})

        message = {"some_arbitrary_keys": "and_their_values"}
        with self.assertRaises(celery.exceptions.Retry):
            global_send_to_zapier("webhook", message)

        self.assertEqual(1, len(responses.calls))
