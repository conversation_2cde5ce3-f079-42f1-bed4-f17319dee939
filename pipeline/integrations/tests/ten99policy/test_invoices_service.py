import datetime

from ten99policy.error import InvoiceUneditableAlreadyExistsError

from documents.models import WorkItem, WorkItemCategory, Payment
from documents.tests.factories import create_payment, create_work_type
from integrations.services.ten99policy import container as ten99p_container
from integrations.services.ten99policy.service.invoices import create_invoice_based_on_jo_task
from integrations.tests.factories.ten99policy import (
    Ten99PolicyRequestFactory,
    Ten99PolicyQuoteFactory,
    Ten99PolicyContractorFactory,
    Ten99PolicyJobFactory,
)
from integrations.tests.ten99policy.helper import Ten99PolicyTestHelper
from openings.tests.factories import JobOpeningFactory
from shortlist.tests.helpers import TenantTestCase
from tasks.tests.factories import TaskFactory
from vendors.tests.factories import VendorFactory


class TestInvoicesService(TenantTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.test_helper = Ten99PolicyTestHelper()

    def setUp(self):
        super().setUp()

        job_opening = JobOpeningFactory()
        self.vendor = VendorFactory()
        self.task = TaskFactory(job_opening=job_opening, date_start=datetime.date(2024, 8, 15), date_end=datetime.date(2024, 8, 20))

        self.ten99p_contractor = Ten99PolicyContractorFactory(vendor=self.vendor)
        self.ten99p_request = Ten99PolicyRequestFactory(onboarding_context_name=f"jobopening:{job_opening.pk}")
        self.ten99p_job = Ten99PolicyJobFactory(request=self.ten99p_request)
        Ten99PolicyQuoteFactory(request=self.ten99p_request, t99p_id="qt_TYU678", contractor=self.ten99p_contractor)

        self.work_type = create_work_type()
        self.work_item_category = WorkItemCategory.objects.create(name="Work item")
        self.expense_category_1 = WorkItemCategory.objects.create(name="Expense type 1")
        self.expense_category_2 = WorkItemCategory.objects.create(name="Expense type 2")

    def test_create_ten99p_invoice_for_task_with_single_payment(self):
        self._set_expense_categories_for_ten99p([self.expense_category_1])
        self._create_multi_item_payment(status=Payment.STATUS_PAID)

        api_mock = self.test_helper.get_api_mock()
        with ten99p_container.api.override(api_mock):
            create_invoice_based_on_jo_task(self.task)

        api_mock.create_invoice.assert_called_once_with(
            contractor=self.ten99p_contractor.t99p_id,
            job=self.ten99p_job.t99p_id,
            gross_pay=255000,
            paycycle_startdate=1723680000,
            paycycle_enddate=1724112000,
        )

        self.assertEqual(self.ten99p_request.invoice.t99p_id, "in_4RviYgc2Wt")
        self.assertEqual(self.ten99p_request.invoice.contractor, self.ten99p_contractor)
        self.assertEqual(self.ten99p_request.invoice.job, self.ten99p_job)
        self.assertEqual(self.ten99p_request.invoice.pay_cycle_start_date, datetime.date(2024, 8, 15))
        self.assertEqual(self.ten99p_request.invoice.pay_cycle_end_date, datetime.date(2024, 8, 20))

    def test_create_ten99p_invoice_for_task_with_multiple_payments(self):
        self._set_expense_categories_for_ten99p([self.expense_category_1, self.expense_category_2])
        self._create_multi_item_payment(status=Payment.STATUS_PAID)
        self._create_single_item_payment(status=Payment.STATUS_PAID)

        api_mock = self.test_helper.get_api_mock()
        with ten99p_container.api.override(api_mock):
            create_invoice_based_on_jo_task(self.task)

        api_mock.create_invoice.assert_called_once_with(
            contractor=self.ten99p_contractor.t99p_id,
            job=self.ten99p_job.t99p_id,
            gross_pay=350000,
            paycycle_startdate=1723680000,
            paycycle_enddate=1724112000,
        )

        self.assertEqual(self.ten99p_request.invoice.t99p_id, "in_4RviYgc2Wt")
        self.assertEqual(self.ten99p_request.invoice.contractor, self.ten99p_contractor)
        self.assertEqual(self.ten99p_request.invoice.job, self.ten99p_job)
        self.assertEqual(self.ten99p_request.invoice.pay_cycle_start_date, datetime.date(2024, 8, 15))
        self.assertEqual(self.ten99p_request.invoice.pay_cycle_end_date, datetime.date(2024, 8, 20))

    def test_create_ten99p_invoice_for_task_with_multiple_payments_and_mix_payment_statuses(self):
        self._set_expense_categories_for_ten99p([self.expense_category_1, self.expense_category_2])
        self._create_single_item_payment(status=Payment.STATUS_PAID)
        self._create_multi_item_payment(status=Payment.STATUS_NEW)

        api_mock = self.test_helper.get_api_mock()
        with ten99p_container.api.override(api_mock):
            create_invoice_based_on_jo_task(self.task)

        api_mock.create_invoice.assert_called_once_with(
            contractor=self.ten99p_contractor.t99p_id,
            job=self.ten99p_job.t99p_id,
            gross_pay=100000,
            paycycle_startdate=1723680000,
            paycycle_enddate=1724112000,
        )

        self.assertEqual(self.ten99p_request.invoice.t99p_id, "in_4RviYgc2Wt")
        self.assertEqual(self.ten99p_request.invoice.contractor, self.ten99p_contractor)
        self.assertEqual(self.ten99p_request.invoice.job, self.ten99p_job)
        self.assertEqual(self.ten99p_request.invoice.pay_cycle_start_date, datetime.date(2024, 8, 15))
        self.assertEqual(self.ten99p_request.invoice.pay_cycle_end_date, datetime.date(2024, 8, 20))

    def test_should_not_create_invoice_data_entry_on_already_existing_invoice(self):
        self._set_expense_categories_for_ten99p([self.expense_category_1])
        self._create_single_item_payment(status=Payment.STATUS_PAID)

        api_mock = self.test_helper.get_api_mock()
        api_mock.create_invoice.side_effect = InvoiceUneditableAlreadyExistsError()

        with ten99p_container.api.override(api_mock):
            create_invoice_based_on_jo_task(self.task)

        api_mock.create_invoice.assert_called_once_with(
            contractor=self.ten99p_contractor.t99p_id,
            job=self.ten99p_job.t99p_id,
            gross_pay=100000,
            paycycle_startdate=1723680000,
            paycycle_enddate=1724112000,
        )
        self.assertEqual(hasattr(self.ten99p_request, 'invoice'), False)

    def _set_expense_categories_for_ten99p(self, categories: list[WorkItemCategory]):
        self.test_helper.config.expense_work_item_categories.set(categories)

    def _create_single_item_payment(self, status: str):
        work_items = [
            WorkItem(
                vendor=self.vendor,
                work_type=self.work_type,
                display_name="Work item #1",
                quantity=1,
                unit_price=1000,
                category=self.work_item_category,
            ),
            WorkItem(
                vendor=self.vendor,
                work_type=self.work_type,
                display_name="Expense #1",
                quantity=1,
                unit_price=100,
                category=self.expense_category_1,
            ),
        ]
        create_payment(related_task=self.task, work_items=work_items, status=Payment.status_code(status))

    def _create_multi_item_payment(self, status: str):
        work_items = [
            WorkItem(
                vendor=self.vendor,
                work_type=self.work_type,
                display_name="Work item #1",
                quantity=1,
                unit_price=1000,
                category=self.work_item_category,
            ),
            WorkItem(
                vendor=self.vendor,
                work_type=self.work_type,
                display_name="Work item #2",
                quantity=1,
                unit_price=1500,
                category=self.work_item_category,
            ),
            WorkItem(
                vendor=self.vendor,
                work_type=self.work_type,
                display_name="Expense #1",
                quantity=1,
                unit_price=100,
                category=self.expense_category_1,
            ),
            WorkItem(
                vendor=self.vendor,
                work_type=self.work_type,
                display_name="Expense #2",
                quantity=1,
                unit_price=50,
                category=self.expense_category_2,
            ),
        ]
        create_payment(related_task=self.task, work_items=work_items, status=Payment.status_code(status))
