from freezegun import freeze_time

from documents.models import InsuranceSource, InsuranceStatus, InsuranceType
from documents.tests.factories import InsuranceFactory
from events.event_types import Ten99PProofOfInsuranceApproved, Ten99PProofOfInsuranceRejected
from integrations.models import Ten99PolicyCoverageType, Ten99PolicyCoverage, Ten99PolicyCoverageStatus
from integrations.services.ten99policy.data_model import BuyerDecisionData, BuyerApproval, BuyerRejection
from integrations.services.ten99policy.service.buyer import (
    onboarding_stage_get_details,
    onboarding_stage_allow_retry,
    onboarding_stage_save_decision,
)
from integrations.tests.factories.ten99policy import Ten99PolicyRequestFactory, Ten99PolicyCoverageFactory
from integrations.tests.ten99policy.helper import Ten99PolicyTestHelper
from shortlist.tests.helpers import TenantTestCase
from vendors.tests.factories import VendorFactory


class TestTen99PolicyBuyerService(TenantTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.test_helper = Ten99PolicyTestHelper()

    def test_onboarding_stage_get_details(self):
        vendor = VendorFactory()
        onboarding_context = self.test_helper.create_job_opening_and_context(
            custom_fields_data={
                "job_category_cf": "Photography",
                "location_cf": "TX",
                "required_coverage_type_cf": ["general liability", "workers' comp"],
            }
        )
        data_provider = self.test_helper.get_data_provider(vendor, onboarding_context)

        ten99p_request = Ten99PolicyRequestFactory(onboarding_context_name=f"{onboarding_context.name}")
        insurance_gl = InsuranceFactory(
            type=InsuranceType.general_liability,
            vendor=vendor,
            onboarding_context=onboarding_context,
        )
        insurance_wc = InsuranceFactory(
            type=InsuranceType.workers_compensation,
            vendor=vendor,
            onboarding_context=onboarding_context,
        )
        Ten99PolicyCoverageFactory(
            request=ten99p_request,
            type=Ten99PolicyCoverageType.general_liability,
            start_date="2024-05-01",
            end_date="2024-05-07",
            document=insurance_gl,
        )
        Ten99PolicyCoverageFactory(
            request=ten99p_request,
            type=Ten99PolicyCoverageType.workers_compensation,
            start_date="2024-05-01",
            end_date="2024-05-07",
            document=insurance_wc,
        )

        response = onboarding_stage_get_details(data_provider)

        expected_response = {
            "required_coverages": [
                {
                    "type": "general_liability",
                    "has_own_insurance": False,
                    "status": "not_started",
                    "purchase_not_allowed": False,
                    "purchase_not_allowed_reason": None,
                    "start_date": "2024-05-01",
                    "end_date": "2024-05-07",
                    "rejection_reason": None,
                    "expiration_date": None,
                    "ten99p_opt_in_url": None,
                    "document_id": insurance_gl.id,
                    "assignment_quote": None,
                },
                {
                    "type": "workers_compensation",
                    "has_own_insurance": False,
                    "status": "not_started",
                    "purchase_not_allowed": False,
                    "purchase_not_allowed_reason": None,
                    "start_date": "2024-05-01",
                    "end_date": "2024-05-07",
                    "rejection_reason": None,
                    "expiration_date": None,
                    "ten99p_opt_in_url": None,
                    "document_id": insurance_wc.id,
                    "assignment_quote": None,
                },
            ],
            "reupload_allowed": False,
        }
        self.assertDictEqual(response.model_dump(mode="json"), expected_response)

    def test_should_allow_proof_of_insurance_reupload(self):
        vendor = VendorFactory()
        onboarding_context = self.test_helper.create_job_opening_and_context(
            custom_fields_data={
                "job_category_cf": "Spokesperson / Influencer",
                "location_cf": "DC",
                "required_coverage_type_cf": ["general liability"],
            }
        )
        ten99p_request = Ten99PolicyRequestFactory(onboarding_context_name=f"{onboarding_context.name}")
        Ten99PolicyCoverageFactory(
            request=ten99p_request,
            type=Ten99PolicyCoverageType.general_liability,
            start_date="2024-05-01",
            end_date="2024-05-07",
        )
        data_provider = self.test_helper.get_data_provider(vendor, onboarding_context)

        with self.watch_event(Ten99PProofOfInsuranceRejected) as events:
            response = onboarding_stage_allow_retry(data_provider)

        expected_response = {
            "required_coverages": [
                {
                    "type": "general_liability",
                    "has_own_insurance": False,
                    "status": "not_started",
                    "purchase_not_allowed": False,
                    "purchase_not_allowed_reason": None,
                    "start_date": "2024-05-01",
                    "end_date": "2024-05-07",
                    "rejection_reason": None,
                    "expiration_date": None,
                    "ten99p_opt_in_url": None,
                    "document_id": None,
                    "assignment_quote": None,
                },
            ],
            "reupload_allowed": True,
        }
        self.assertDictEqual(response.model_dump(mode="json"), expected_response)
        self.assertEqual(len(events), 1)

    @freeze_time("2024-05-01")
    def test_should_save_onboarding_decision(self):
        vendor = VendorFactory()
        onboarding_context = self.test_helper.create_job_opening_and_context(
            custom_fields_data={
                "job_category_cf": "Spokesperson / Influencer",
                "location_cf": "LA",
                "required_coverage_type_cf": ["workers' compensation"],
            }
        )
        ten99p_request = Ten99PolicyRequestFactory(onboarding_context_name=f"{onboarding_context.name}")
        insurance = InsuranceFactory(
            type=InsuranceType.workers_compensation,
            vendor=vendor,
            onboarding_context=onboarding_context,
        )
        coverage = Ten99PolicyCoverageFactory(
            request=ten99p_request,
            type=Ten99PolicyCoverageType.workers_compensation,
            own_insurance=True,
            start_date="2024-05-02",
            end_date="2024-05-09",
            document=insurance,
        )

        data_provider = self.test_helper.get_data_provider(vendor, onboarding_context)
        buyer_decision = BuyerDecisionData(data={Ten99PolicyCoverageType.workers_compensation: BuyerApproval(end_date='2024-05-10')})

        with self.watch_event(Ten99PProofOfInsuranceApproved) as events:
            response = onboarding_stage_save_decision(data_provider, buyer_decision)

        self.assertEqual(len(events), 1)

        expected_response = {
            "required_coverages": [
                {
                    "type": "workers_compensation",
                    "has_own_insurance": True,
                    "status": "approved",
                    "purchase_not_allowed": False,
                    "purchase_not_allowed_reason": None,
                    "start_date": "2024-05-02",
                    "end_date": "2024-05-09",
                    "rejection_reason": None,
                    "expiration_date": '2024-05-10',
                    "ten99p_opt_in_url": None,
                    "document_id": insurance.id,
                    "assignment_quote": None,
                },
            ],
            "reupload_allowed": False,
        }
        self.assertDictEqual(response.model_dump(mode="json"), expected_response)

        current_coverage = Ten99PolicyCoverage.objects.filter(id=coverage.id).first()
        self.assertEqual(current_coverage.status, Ten99PolicyCoverageStatus.approved)
        self.assertEqual(current_coverage.document.source, InsuranceSource.proof_of_insurance)
        self.assertEqual(current_coverage.document.type, InsuranceType.workers_compensation)
        self.assertEqual(current_coverage.document.status, InsuranceStatus.approved)

    @freeze_time("2024-05-10")
    def test_should_save_onboarding_decision_for_coverage_without_insurance_document(self):
        vendor = VendorFactory()
        onboarding_context = self.test_helper.create_job_opening_and_context(
            custom_fields_data={
                "job_category_cf": "Photography",
                "location_cf": "LA",
                "required_coverage_type_cf": ["workers' compensation"],
            }
        )
        ten99p_request = Ten99PolicyRequestFactory(onboarding_context_name=f"{onboarding_context.name}")
        coverage = Ten99PolicyCoverageFactory(
            request=ten99p_request,
            type=Ten99PolicyCoverageType.workers_compensation,
            own_insurance=True,
            start_date="2024-05-15",
            end_date="2024-05-22",
        )

        data_provider = self.test_helper.get_data_provider(vendor, onboarding_context)
        buyer_decision = BuyerDecisionData(
            data={Ten99PolicyCoverageType.workers_compensation: BuyerRejection(rejection_reason='Expired insurance')}
        )

        response = onboarding_stage_save_decision(data_provider, buyer_decision)

        expected_response = {
            "required_coverages": [
                {
                    "type": "workers_compensation",
                    "has_own_insurance": True,
                    "status": "rejected",
                    "purchase_not_allowed": False,
                    "purchase_not_allowed_reason": None,
                    "start_date": "2024-05-15",
                    "end_date": "2024-05-22",
                    "rejection_reason": "Expired insurance",
                    "expiration_date": None,
                    "ten99p_opt_in_url": None,
                    "document_id": None,
                    "assignment_quote": None,
                },
            ],
            "reupload_allowed": False,
        }
        self.assertDictEqual(response.model_dump(mode="json"), expected_response)

        current_coverage = Ten99PolicyCoverage.objects.filter(id=coverage.id).first()
        self.assertEqual(current_coverage.status, Ten99PolicyCoverageStatus.rejected)
        self.assertEqual(current_coverage.document, None)
