import datetime
from datetime import date, timedelta
from decimal import Decimal
from unittest.mock import Mock

from django.test import SimpleTestCase
from freezegun import freeze_time

from integrations.models import (
    Ten99PolicyContractor,
    Ten99PolicyEntity,
    Ten99PolicyCoverageType,
)
from integrations.services.ten99policy.api_service import (
    Ten99PApiService,
    date_to_timestamp,
    get_earliest_start_date_for_coverage,
    T99PJobMetadata,
)
from integrations.services.ten99policy.data_provider.interface import WageTypes
from integrations.services.ten99policy.exceptions import (
    ContractorNotEligibleForAssignment,
)
from integrations.tests.factories.ten99policy import (
    Ten99PolicyEntityFactory,
    Ten99PolicyContractorFactory,
    Ten99PolicyJobFactory,
    Ten99PolicyQuoteFactory,
    Ten99PolicyRequestFactory,
    JOB_CATEGORY_CODES_MAPPING,
)
from integrations.tests.ten99policy.fakes import get_fake_api
from shortlist.tests.helpers import TenantTestCase
from vendors.tests.factories import VendorFactory


@freeze_time("2025-04-18")
class TestCoverageStartDate(SimpleTestCase):
    def test_start_date_past(self):
        today, tomorrow = self._get_dates()
        date_from_past = today - timedelta(days=5)

        result = get_earliest_start_date_for_coverage(date_from_past)

        self.assertEqual(result, tomorrow)

    def test_start_date_today(self):
        today, tomorrow = self._get_dates()

        result = get_earliest_start_date_for_coverage(today)

        self.assertEqual(result, tomorrow)

    def test_start_date_tomorrow(self):
        today, tomorrow = self._get_dates()

        result = get_earliest_start_date_for_coverage(tomorrow)

        self.assertEqual(result, tomorrow)

    def test_start_date_future(self):
        today, _ = self._get_dates()
        date_from_future = today + timedelta(days=5)

        result = get_earliest_start_date_for_coverage(date_from_future)

        self.assertEqual(result, date_from_future)

    @staticmethod
    def _get_dates():
        today = date.today()
        tomorrow = today + timedelta(days=1)
        return today, tomorrow


class TestTen99PApiService(TenantTestCase):
    def setUp(self):
        super().setUp()
        self.fake_api = get_fake_api()
        self.api_service = Ten99PApiService(job_category_codes_mapping=JOB_CATEGORY_CODES_MAPPING, api=self.fake_api)

    def test_date_to_timestamp(self):
        self.assertEqual(date_to_timestamp(date(2022, 10, 18)), 1666051200)

    def test_find_contractor_by_vendor_email(self):
        vendor = VendorFactory()
        contractor = self.api_service.find_contractor_by_vendor_email(vendor)

        self.assertIsInstance(contractor, Ten99PolicyContractor)
        self.assertEqual(contractor.vendor_id, vendor.id)
        self.assertEqual(contractor.t99p_id, "cn_ABCDE")

        self.fake_api.list_contractors.assert_called_once_with(email=vendor.email)

    def test_create_contractor(self):
        vendor = VendorFactory()
        contractor = self.api_service.create_contractor(
            vendor_id=vendor.id,
            company_name=vendor.taxinformation.company_name,
            first_name=vendor.taxinformation.first_name,
            middle_name=vendor.taxinformation.middle_name,
            last_name=vendor.taxinformation.last_name,
            email=vendor.email,
            phone_number=vendor.phone_number,
            address=vendor.taxinformation.address,
            city=vendor.taxinformation.city,
            postal_code=vendor.taxinformation.postal_code,
            state=vendor.taxinformation.state,
            tax_identification=vendor.taxinformation.tax_id,
        )

        self.assertIsInstance(contractor, Ten99PolicyContractor)
        self.assertEqual(contractor.vendor_id, vendor.id)
        self.assertEqual(contractor.t99p_id, "cn_ABCDE")

        self.fake_api.create_contractor.assert_called_once()

    def test_update_contractor(self):
        vendor = VendorFactory()
        contractor = Ten99PolicyContractorFactory(t99p_id="cn_ABCDE", vendor_id=vendor.id)

        updated_contractor = self.api_service.update_contractor(
            contractor=contractor,
            company_name="Updated Company",
            first_name="Updated First",
            middle_name="Updated Middle",
            last_name="Updated Last",
            email="<EMAIL>",
            phone_number="+48123123123",
            address="Updated Street 123",
            city="Updated City",
            postal_code="12345",
            state="TX",
            tax_identification="1234567890",
        )

        self.assertEqual(updated_contractor.t99p_id, "cn_ABCDE")

        self.fake_api.update_contractor.assert_called_once_with(
            "cn_ABCDE",
            company_name="Updated Company",
            first_name="Updated First",
            middle_name="Updated Middle",
            last_name="Updated Last",
            email="<EMAIL>",
            phone="+48123123123",
            address={
                "line1": "Updated Street 123",
                "line2": "",
                "locality": "Updated City",
                "postalcode": "12345",
                "region": "TX",
                "country": "USA",
            },
            tax_identification="1234567890",
        )

    def test_create_entity(self):
        entity = self.api_service.create_entity("Entity_name")

        self.assertIsInstance(entity, Ten99PolicyEntity)
        self.assertEqual(entity.t99p_id, "en_ABC123")
        self.assertEqual(entity.entity_name, "Entity_name")

        self.fake_api.create_entity.assert_called_once_with(name="Entity_name")

    def test_create_job(self):
        entity = Ten99PolicyEntityFactory(t99p_id="en_BCD12")
        request = Ten99PolicyRequestFactory()

        job = self.api_service.create_job(
            request=request,
            entity=entity,
            name="job_name",
            description="job_description",
            wage_total=10200,
            state="TX",
            wage_type=WageTypes.flatfee,
            category_name="Photography",
            custom_metadata=T99PJobMetadata(
                purchase_order_contact_name="John Doe",
                purchase_order_contact_email="<EMAIL>",
            ),
        )

        self.fake_api.create_job.assert_called_once_with(
            request_pk=request.pk,
            category_code="jc_SASjdXGrLW",
            description="job_description",
            entity="en_BCD12",
            name="job_name",
            wage=10200,
            wage_type="flatfee",
            address={"region": "TX", "country": "US"},
            custom_metadata={
                "purchase_order_contact_name": "John Doe",
                "purchase_order_contact_email": "<EMAIL>",
            },
        )

        self.assertEqual(job.t99p_id, "jb_ABC567")
        self.assertEqual(job.entity, entity)
        self.assertEqual(job.request, request)
        self.assertEqual(job.state, "TX")
        self.assertEqual(job.category_code, "jc_SASjdXGrLW")

    def test_create_job_with_empty_metadata(self):
        entity = Ten99PolicyEntityFactory(t99p_id="en_12345")
        request = Ten99PolicyRequestFactory()

        job = self.api_service.create_job(
            request=request,
            entity=entity,
            name="job_name",
            description="job_description",
            wage_total=200000,
            state="IL",
            wage_type=WageTypes.flatfee,
            category_name="Photography",
            custom_metadata=T99PJobMetadata(
                purchase_order_contact_name="John Doe",
                purchase_order_contact_email="<EMAIL>",
            ),
        )

        self.fake_api.create_job.assert_called_once_with(
            request_pk=request.pk,
            category_code="jc_SASjdXGrLW",
            description="job_description",
            entity="en_12345",
            name="job_name",
            wage=200000,
            wage_type="flatfee",
            address={"region": "IL", "country": "US"},
            custom_metadata={
                "purchase_order_contact_name": "John Doe",
                "purchase_order_contact_email": "<EMAIL>",
            },
        )

        self.assertEqual(job.t99p_id, "jb_ABC567")
        self.assertEqual(job.entity, entity)
        self.assertEqual(job.request, request)
        self.assertEqual(job.state, "IL")
        self.assertEqual(job.category_code, "jc_SASjdXGrLW")

    def test_update_job(self):
        request = Ten99PolicyRequestFactory()
        entity = Ten99PolicyEntityFactory(t99p_id="en_BCD12")
        job = Ten99PolicyJobFactory(t99p_id="jb_ABC567", request=request, entity=entity, state="TX")

        updated_job = self.api_service.update_job(
            job=job,
            entity=entity,
            name="Updated Job",
            description="Updated Description",
            wage_total=15000,
            wage_type=WageTypes.flatfee,
            category_name="Spokesperson / Influencer",
            state="CA",
        )

        self.fake_api.update_job.assert_called_once_with(
            "jb_ABC567",
            category_code="jc_V9CcPzeTWe",
            description="Updated Description",
            entity="en_BCD12",
            name="Updated Job",
            wage=15000,
            wage_type="flatfee",
            address={"region": "CA", "country": "US"},
        )

        self.assertEqual(updated_job.t99p_id, "jb_ABC567")
        self.assertEqual(updated_job.entity, entity)
        self.assertEqual(updated_job.request, request)
        self.assertEqual(updated_job.state, "CA")
        self.assertEqual(updated_job.category_code, "jc_V9CcPzeTWe")

    @freeze_time("2022-11-15")
    def test_create_quote(self):
        contractor = Ten99PolicyContractorFactory(t99p_id="cn_RTY123")
        job = Ten99PolicyJobFactory(t99p_id="jb_QWE567")

        quote = self.api_service.create_quote(
            request=job.request,
            contractor=contractor,
            job=job,
            start_date=date(2022, 12, 1),
            end_date=date(2022, 12, 10),
            coverage_types=[Ten99PolicyCoverageType.workers_compensation],
        )

        self.fake_api.create_quote.assert_called_once_with(
            coverage_type=["workers-comp"],
            contractor="cn_RTY123",
            job="jb_QWE567",
            effective_date=1669852800,
            end_date=1670630400,
        )

        self.assertEqual(quote.t99p_id, "qt_TYU678")
        self.assertEqual(quote.job, job)
        self.assertEqual(quote.request, job.request)
        self.assertEqual(quote.contractor, contractor)
        self.assertEqual(quote.effective_date, date(2022, 12, 1))
        self.assertEqual(quote.end_date, date(2022, 12, 10))

    @freeze_time("2022-12-5")
    def test_create_quote_start_date_is_always_in_future(self):
        contractor = Ten99PolicyContractorFactory(t99p_id="cn_RTY123")
        job = Ten99PolicyJobFactory(t99p_id="jb_QWE567")

        quote = self.api_service.create_quote(
            request=job.request,
            contractor=contractor,
            job=job,
            start_date=date(2022, 12, 1),
            end_date=date(2022, 12, 10),
            coverage_types=[
                Ten99PolicyCoverageType.workers_compensation,
                Ten99PolicyCoverageType.general_liability,
            ],
        )

        self.fake_api.create_quote.assert_called_once_with(
            coverage_type=["workers-comp", "general"],
            contractor="cn_RTY123",
            job="jb_QWE567",
            effective_date=1670284800,
            end_date=1670630400,
        )

        # even if the task start date is already past, send a future date
        self.assertEqual(quote.effective_date, date(2022, 12, 6))
        self.assertEqual(quote.end_date, date(2022, 12, 10))

    def test_create_session(self):
        quote = Ten99PolicyQuoteFactory(t99p_id="qt_TYU678")

        session = self.api_service.create_session(
            request=quote.request,
            quote=quote,
            success_url="https://example.com/success/",
            cancel_url="https://example.com/cancel/",
        )

        self.fake_api.create_session.assert_called_once_with(
            quote="qt_TYU678",
            success_url="https://example.com/success/",
            cancel_url="https://example.com/cancel/",
        )
        self.assertEqual(session.t99p_id, "ias_01FZCHXE7KNQHE1T3S8AXG2QZE")
        self.assertEqual(session.onboarding_url, "https://apply.1099t.com/ias_01")
        self.assertEqual(session.quote, quote)
        self.assertEqual(session.request, quote.request)

    @freeze_time("2023-03-01")
    def test_create_assignment_vendor_not_eligible(self):
        request = Ten99PolicyRequestFactory()
        contractor = Ten99PolicyContractorFactory(t99p_id="cn_ABCDE")
        job = Ten99PolicyJobFactory(t99p_id="jb_ABC567", request=request)

        with self.assertRaises(ContractorNotEligibleForAssignment):
            self.api_service.create_assignment(
                contractor=contractor,
                job=job,
                request=request,
                start_date=datetime.date(2024, 3, 11),
                end_date=datetime.date(2024, 3, 18),
                coverage_types=[
                    Ten99PolicyCoverageType.general_liability,
                    Ten99PolicyCoverageType.workers_compensation,
                ],
            )

        self.fake_api.create_assignment.assert_called_once_with(
            contractor="cn_ABCDE",
            job="jb_ABC567",
            effective_date=1710115200,
            end_date=1710720000,
            coverage_type=["general", "workers-comp"],
        )

    @freeze_time("2024-03-10")
    def test_create_assignment_with_single_coverage_type(self):
        request = Ten99PolicyRequestFactory()
        contractor = Ten99PolicyContractorFactory(t99p_id="cn_ABCDE")
        job = Ten99PolicyJobFactory(t99p_id="jb_ABC567", request=request)

        fake_api = get_fake_api()
        fake_api.create_assignment.return_value = Mock(
            **{
                "id": "an_7G9a2fg0as",
                "net_rate": 120,
                "eligible.result": True,
                "eligible.message": "Contractor is pre-approved for insurance coverage.",
            }
        )
        api_service = Ten99PApiService(job_category_codes_mapping=JOB_CATEGORY_CODES_MAPPING, api=fake_api)

        api_service.create_assignment(
            contractor=contractor,
            job=job,
            request=request,
            start_date=datetime.date(2024, 3, 11),
            end_date=datetime.date(2024, 3, 18),
            coverage_types=[Ten99PolicyCoverageType.workers_compensation],
        )

        fake_api.create_assignment.assert_called_once_with(
            contractor="cn_ABCDE",
            job="jb_ABC567",
            effective_date=1710115200,
            end_date=1710720000,
            coverage_type=["workers-comp"],
        )

    def test_create_invoice(self):
        contractor = Ten99PolicyContractorFactory(t99p_id="cn_RTY123")
        job = Ten99PolicyJobFactory(t99p_id="jb_QWE567")

        invoice = self.api_service.create_invoice(
            request=job.request,
            contractor=contractor,
            job=job,
            income_amount=Decimal(100.50),
            start_date=datetime.date(2024, 8, 15),
            end_date=datetime.date(2024, 8, 20),
        )

        self.fake_api.create_invoice.assert_called_once_with(
            contractor="cn_RTY123",
            job="jb_QWE567",
            gross_pay=10050,
            paycycle_startdate=1723680000,
            paycycle_enddate=1724112000,
        )

        self.assertEqual(invoice.t99p_id, "in_4RviYgc2Wt")
        self.assertEqual(invoice.job, job)
        self.assertEqual(invoice.contractor, contractor)
        self.assertEqual(invoice.gross_pay, 100000)
        self.assertEqual(invoice.pay_cycle_start_date, datetime.date(2024, 8, 15))
        self.assertEqual(invoice.pay_cycle_end_date, datetime.date(2024, 8, 20))
        self.assertEqual(invoice.premium_due, 200)
        self.assertEqual(invoice.request, job.request)

    def test_create_invoice_updates_existing(self):
        request = Ten99PolicyRequestFactory()
        contractor = Ten99PolicyContractorFactory(t99p_id="cn_SFJsdl")
        job = Ten99PolicyJobFactory(t99p_id="jb_KjUid8", request=request)
        start_date = date(2024, 1, 1)
        end_date = date(2024, 1, 31)

        # Create initial invoice
        self.api_service.create_invoice(
            request=request,
            contractor=contractor,
            job=job,
            income_amount=Decimal("1000.00"),
            start_date=start_date,
            end_date=end_date,
        )

        self.fake_api.create_invoice.reset_mock()  # Reset mock for the second call

        # Mock for the second API call returning a new ID and updated gross_pay
        new_api_invoice_id = "inv_NEW_4P1_ID"
        updated_api_gross_pay = 120000
        updated_api_premium_due = 14500

        self.fake_api.create_invoice.return_value = Mock(
            id=new_api_invoice_id,
            gross_pay=updated_api_gross_pay,
            premium_due=updated_api_premium_due,
        )

        # Update invoice
        updated_invoice = self.api_service.create_invoice(
            request=request,
            contractor=contractor,
            job=job,
            income_amount=Decimal("1200.00"),  # new income amount
            start_date=start_date,
            end_date=end_date,
        )

        self.assertEqual(self.fake_api.create_invoice.call_count, 1)
        self.fake_api.create_invoice.assert_called_once_with(
            contractor="cn_SFJsdl",
            job="jb_KjUid8",
            gross_pay=updated_api_gross_pay,
            paycycle_startdate=date_to_timestamp(start_date),
            paycycle_enddate=date_to_timestamp(end_date),
        )

        self.assertEqual(updated_invoice.t99p_id, new_api_invoice_id)
        self.assertEqual(updated_invoice.request, request)
        self.assertEqual(updated_invoice.contractor, contractor)
        self.assertEqual(updated_invoice.job, job)
        self.assertEqual(updated_invoice.gross_pay, updated_api_gross_pay)
        self.assertEqual(updated_invoice.pay_cycle_start_date, start_date)
        self.assertEqual(updated_invoice.pay_cycle_end_date, end_date)
        self.assertEqual(updated_invoice.premium_due, updated_api_premium_due)

        # Verify that there's only one invoice object for this request
        from integrations.models import Ten99PolicyInvoice

        self.assertEqual(Ten99PolicyInvoice.objects.filter(request=request).count(), 1)

        # Verify that the single DB object has the updated details from the second API call
        db_invoice = Ten99PolicyInvoice.objects.get(request=request)
        self.assertEqual(db_invoice.t99p_id, new_api_invoice_id)
        self.assertEqual(db_invoice.gross_pay, updated_api_gross_pay)
        self.assertEqual(db_invoice.pay_cycle_start_date, start_date)
        self.assertEqual(db_invoice.pay_cycle_end_date, end_date)
        self.assertEqual(db_invoice.premium_due, updated_api_premium_due)
