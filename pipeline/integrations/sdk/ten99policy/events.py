from events.event_types import (
    Ten99PProofOfInsuranceDecisionConfirmed,
    Ten99PProofOfInsuranceRejected,
    Ten99PVendorManualReview,
    Ten99PVendorObtainedNewInsurance,
    Ten99PVendorOptedInForInsurance,
    Ten99PVendorUploadedProofOfInsurance,
)

__all__ = [
    "Ten99PVendorOptedInForInsurance",  # Triggered when vendor started insurance purchase process in 1099Policy
    "Ten99PVendorUploadedProofOfInsurance",  # Triggered when vendor manually uploaded proof of insurance
    "Ten99PProofOfInsuranceDecisionConfirmed",  # Triggered when decision about vendor's insurance is confirmed by buyer
    "Ten99PProofOfInsuranceRejected",  # Triggered when vendor's insurance is rejected by buyer
    "Ten99PVendorObtainedNewInsurance",  # Triggered when vendor finished insurance purchase in 1099Policy
    "Ten99PVendorManualReview",  # Triggered when vendor is going under manual review (KYC)
]
