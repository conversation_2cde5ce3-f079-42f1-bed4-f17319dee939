from django.urls import re_path

from integrations import sso
from .views import (
    IntegrationsView,
    HelloSignCallback,
    SocialLogin,
    ReplytoView,
    SisenseView,
    ExcelGenerationView,
    PaymentProcessor,
    CheckrCallback,
    SendgridUnsubscribe,
    SendgridUnsubscribed,
    PaycassoEnrolmentView,
    PaycassoEnrolmentVerificationView,
    PaycassoVerificationCallback,
    PeroptyxResourceView,
    PaycassoEventViewForVendor,
    FlatfileView,
    VeriffVerificationView,
    VeriffDecisionCallback,
    VeriffEventCallback,
    Ten99PVendorView,
    Ten99PCallback,
    Ten99PBuyerView,
    TaxBanditsCallback,
    IntegrationsStatusView,
    DirectIntegrationVendorSyncEligibilityView,
)

urlpatterns = [
    re_path(r'^social/login_options/', SocialLogin.as_view({'get': 'login_options'})),
    re_path(r'^social/login/(?P<provider>[a-z]*)/(?P<pk>.*)/', SocialLogin.as_view({'get': 'login'})),
    re_path(r'^flatfile/(?P<module>[^/]+)/', FlatfileView.as_view({'get': 'get_config', 'post': 'import_data'})),
    re_path(r'^paycasso/enrolment/verification', PaycassoEnrolmentVerificationView.as_view()),
    re_path(r'^paycasso/enrolment/', PaycassoEnrolmentView.as_view()),
    re_path(r'^paycasso/events/', PaycassoEventViewForVendor.as_view()),
    re_path(r'^peroptyx/resources/(?P<resource_name>[^/]+)', PeroptyxResourceView.as_view()),
    re_path(r'^callbacks/paycasso/verification', PaycassoVerificationCallback.as_view(), name='paycasso-callback'),
    re_path(r'^callbacks/hellosign/', HelloSignCallback.as_view()),
    re_path(r'^callbacks/replyto/', ReplytoView.as_view()),
    re_path(r'^callbacks/unsubscribe/', SendgridUnsubscribed.as_view()),  # DEPRECATED, remove after updating SendGrid settings
    re_path(r'^sendgrid/unsubscribe/(?P<token>[^/]+)/', SendgridUnsubscribe.as_view()),
    re_path(r'^callbacks/sendgrid/unsubscribed/', SendgridUnsubscribed.as_view()),
    re_path(r'^callbacks/checkr/(?P<vendor_id>[^/]+)/(?P<package_id>[^/]+)/', CheckrCallback.as_view(), name='checkr-callback'),
    re_path(r'^callbacks/veriff/decision', VeriffDecisionCallback.as_view(), name='veriff-decision-callback'),
    re_path(r'^callbacks/veriff/event', VeriffEventCallback.as_view(), name='veriff-event-callback'),
    re_path(r'^callbacks/ten99p/', Ten99PCallback.as_view(), name="ten99p-callback"),
    re_path(r'^callbacks/taxbandits/', TaxBanditsCallback.as_view(), name="taxbandits-callback"),
    re_path(r'^shortlistpay/', PaymentProcessor.as_view()),
    re_path(r'^integrations/$', IntegrationsView.as_view()),
    re_path(r'^integrations/status/$', IntegrationsStatusView.as_view()),
    re_path(r'^sso/acs/$', sso.AssertionConsumerServiceView.as_view()),
    re_path(r'^sso/ls/$', sso.LogoutView.as_view()),
    re_path(r'^sso/ls/post/$', sso.LogoutView.as_view()),
    re_path(r'^sso/login/$', sso.LoginView.as_view()),
    re_path(r'^sso/metadata/$', sso.MetadataView.as_view()),
    re_path(r'^sso/logout/$', sso.LogoutInitView.as_view()),
    re_path(r'^tools/analytics/$', SisenseView.as_view({'get': 'config'})),
    re_path(r'^tools/analytics/embed/(?P<dashboard>.*)$', SisenseView.as_view({'get': 'embed'})),
    re_path(r'^tools/excel_from_template/(?P<template_id>\d+)/(?P<vendor_id>\d+)/$', ExcelGenerationView.as_view()),
    re_path(r'^veriff/verification/', VeriffVerificationView.as_view()),
    re_path(
        r'^ten99p/onboarding_stage/(?P<vendor_stage_id>[0-9]+)/return/(?P<return_token>[a-zA-Z0-9]{64})/(?P<result>(success|cancel))/$',
        Ten99PVendorView.as_view({'get': 'handle_return_from_ten99policy'}),
    ),
    re_path(
        r'^ten99p/onboarding_stage/(?P<vendor_stage_id>[0-9]+)/prevalidation/',
        Ten99PVendorView.as_view({'get': 'onboarding_stage_prevalidation'}),
    ),
    re_path(
        r'^ten99p/onboarding_stage/(?P<vendor_stage_id>[0-9]+)/quote/',
        Ten99PVendorView.as_view({'get': 'onboarding_stage_quote'}),
    ),
    re_path(
        r'^ten99p/onboarding_stage/(?P<vendor_stage_id>[0-9]+)/save_decision/',
        Ten99PVendorView.as_view({'post': 'onboarding_stage_save_decision'}),
    ),
    re_path(
        r'^ten99p/onboarding_stage/(?P<vendor_stage_id>[0-9]+)/continue/',
        Ten99PVendorView.as_view({'post': 'continue_on_ten99policy'}),
    ),
    re_path(r'^ten99p/onboarding_stage/upload_file_config/', Ten99PVendorView.as_view({'get': 'get_upload_file_config'})),
    re_path(
        r'^v/ten99p/config/',
        Ten99PVendorView.as_view({'get': 'config'}),
    ),
    re_path(
        r'^ten99p/onboarding_stage/(?P<vendor_stage_id>[0-9]+)/details/',
        Ten99PBuyerView.as_view({'get': 'onboarding_stage_get_details'}),
    ),
    re_path(
        r'^ten99p/onboarding_stage/(?P<vendor_stage_id>[0-9]+)/review/',
        Ten99PBuyerView.as_view({'post': 'onboarding_stage_save_decision'}),
    ),
    re_path(
        r'^ten99p/onboarding_stage/(?P<vendor_stage_id>[0-9]+)/retry/',
        Ten99PBuyerView.as_view({'post': 'onboarding_stage_allow_retry'}),
    ),
    re_path(
        r'^ten99p/config/',
        Ten99PBuyerView.as_view({'get': 'config'}),
    ),
    re_path(
        r'^direct_integrations/sync_eligibility/vendors/(?P<vendor_id>[0-9]+)',
        DirectIntegrationVendorSyncEligibilityView.as_view({'get': 'list'}),
    ),
    re_path(
        r'^direct_integrations/(?P<integration_id>[a-z0-9-]+)/sync_eligibility/vendors/(?P<vendor_id>[0-9]+)',
        DirectIntegrationVendorSyncEligibilityView.as_view({'put': 'create', 'delete': 'delete'}),
    ),
]
