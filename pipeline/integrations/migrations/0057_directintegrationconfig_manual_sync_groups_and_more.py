# Generated by Django 4.2.20 on 2025-04-15 11:33

import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('vendors', '0058_alter_vendordeleteaccountrequestrecipient_user'),
        ('integrations', '0056_ten99policyrequest_is_cancelled'),
    ]

    operations = [
        migrations.AddField(
            model_name='directintegrationconfig',
            name='manual_sync_groups',
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(choices=[('vendor', 'vendor')], max_length=50), blank=True, default=list, size=None
            ),
        ),
        migrations.CreateModel(
            name='DirectIntegrationVendorSyncEligibility',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('config', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='integrations.directintegrationconfig')),
                ('vendor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='+', to='vendors.vendor')),
            ],
        ),
        migrations.AddConstraint(
            model_name='directintegrationvendorsynceligibility',
            constraint=models.UniqueConstraint(fields=('config', 'vendor'), name='unique_config_vendor'),
        ),
    ]
