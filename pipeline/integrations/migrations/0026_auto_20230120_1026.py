# Generated by Django 3.1.14 on 2023-01-20 10:26

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('integrations', '0025_ten99policy_additional_models'),
    ]

    operations = [
        migrations.CreateModel(
            name='Ten99PolicyRequest',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('onboarding_context_name', models.CharField(db_index=True, max_length=110)),
                ('status', models.CharField(choices=[('in_progress', 'in_progress'), ('done', 'done')], default='in_progress', max_length=50)),
            ],
        ),
        migrations.AlterField(
            model_name='ten99policycontractor',
            name='coverage_status',
            field=models.CharField(choices=[('application_started', 'application_started'), ('application_submitted', 'application_submitted'), ('policy_active', 'policy_active'), ('policy_inactive', 'policy_inactive')], default='application_started', max_length=100),
        ),
        migrations.CreateModel(
            name='Ten99PolicyAssignment',
            fields=[
                ('assignment_id', models.CharField(max_length=100, primary_key=True, serialize=False)),
                ('contractor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assignments', to='integrations.ten99policycontractor')),
                ('job', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assignments', to='integrations.ten99policyjob')),
                ('request', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='assignments', to='integrations.ten99policyrequest')),
            ],
        ),
        migrations.AddField(
            model_name='ten99policyjob',
            name='request',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='jobs', to='integrations.ten99policyrequest'),
        ),
        migrations.AddField(
            model_name='ten99policyquote',
            name='request',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='quotes', to='integrations.ten99policyrequest'),
        ),
        migrations.AddField(
            model_name='ten99policysession',
            name='request',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to='integrations.ten99policyrequest'),
        ),
    ]
