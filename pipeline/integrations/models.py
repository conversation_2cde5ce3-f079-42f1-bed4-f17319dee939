import logging
import operator
import os.path
import time
import uuid
from datetime import datetime
from enum import auto
from urllib.parse import urlencode

import jwt
import pycountry
import requests
from django.conf import settings
from django.contrib.postgres.fields import ArrayField
from django.core.exceptions import ImproperlyConfigured, ValidationError
from django.db import connection, models
from django.db.models import Q, UniqueConstraint
from django.db.models.signals import post_delete, post_save
from django.utils import timezone
from django.utils.functional import cached_property
from django.utils.http import urlencode
from django.utils.text import normalize_newlines

from documents.models import WorkItemCategory
from events.event_types import DispatchTrackUpdateHook
from onboarding.tasks import recalculate_onboarding_completeness
from preferences.constants import NOT_ISO_3166_COUNTRIES
from preferences.models import CustomField, CustomFieldsTemplate, CustomVendorField
from shortlist.current_user import get_current_user
from shortlist.custom_fields import TYPE_CHOICE, TYPE_SELECT
from shortlist.db_fields import dumps
from shortlist.permissions import Permission
from shortlist.utils import MAX_IP_LENGTH, boto3_client, make_choices, make_choices_from_enums
from strenum import StrEnum
from users.models import User, UserRole
from vendors.field_mapping import get_mapped_vendors_value

from . import default_icons
from .managers import CheckrReportManager, PaycassoIntegrationManager, VeriffIntegrationManager, VeriffVerificationManager
from .services.direct_integrations import cache
from .services.direct_integrations.container import direct_integrations_reset_config_provider
from .services.direct_integrations.data_model import ManualSyncControlGroup
from .services.direct_integrations.mapping import SOURCE_VENDOR

logger = logging.getLogger()


class Slack(models.Model):
    """
    Configuration for Stack integration
    """

    NON_RECOVERABLE_ERRORS = {"account_inactive", "invalid_auth", "is_archived", "not_in_channel", "channel_not_found"}

    enabled = models.BooleanField(default=True)
    access_token = models.CharField(max_length=250)
    team_id = models.CharField(max_length=100)
    team_name = models.CharField(max_length=100)
    incoming_url = models.CharField(max_length=250)
    configuration_url = models.CharField(max_length=250)
    channel = models.CharField(max_length=100)
    scope = models.CharField(max_length=250)
    last_error = models.CharField(max_length=250, null=True)

    class SlackException(Exception):
        pass

    class RateLimited(Exception):
        pass

    class ConnectionError(Exception):
        pass

    def send_to_slack(self, message):
        if not self.enabled:
            return
        post_data = {
            'text': message,
            'unfurl_links': False,  # unfurled URL won't be accessible to Slack
            'unfurl_media': False,  # unfurled URL won't be accessible to Slack
        }
        try:
            r = requests.post(self.incoming_url, json=post_data)
        except Exception as e:
            raise Slack.ConnectionError("Cannot connect to Slack", e)
        try:
            response = r.text
            if response == 'ok':
                return
        except Exception as e:
            raise Slack.SlackException("Cannot parse response from Slack API", e, r.status_code, r.text)
        self.last_error = response
        if response in self.NON_RECOVERABLE_ERRORS or r.status_code == 404:
            self.enabled = False
        self.save()
        if response == "rate_limited":
            raise Slack.RateLimited()
        else:
            raise Slack.SlackException(response)


class HellosignOverride(models.Model):
    id = models.IntegerField(default=1, primary_key=True, choices=((1, 1),))
    api_key = models.CharField(max_length=200, null=True, blank=True)
    client_id = models.CharField(max_length=200, null=True, blank=True)
    test_mode = models.BooleanField(default=False)
    destroy_after_signing = models.BooleanField(default=False)


def limit_choices_for_hello_sign_agreement_too_long_value_recipient():
    roles = [role.name for role in UserRole.objects.all_roles_with_permissions(Permission.buyer('template', 'create'), object)]
    filters = {"is_active": True, "deleted": False}
    if roles:
        filters["role_grants__role__in"] = roles
    return filters


class HelloSignAgreementTooLongValueErrorRecipient(models.Model):
    user = models.OneToOneField(
        'users.User', on_delete=models.CASCADE, limit_choices_to=limit_choices_for_hello_sign_agreement_too_long_value_recipient
    )


class SAML(models.Model):
    """
    Configuration for SAML (eg. Okta) integration.
    """

    ALLOWED_RELAY_STATE_PARAM_NAMES = {'RelayState', 'TargetResource'}
    DEFAULT_RELAY_STATE_PARAM_NAME = 'RelayState'
    LOGIN_VIEW = '/api/sso/login/'

    enabled = models.BooleanField(default=True)
    login_url = models.CharField(max_length=250, null=False)
    provider_name = models.CharField(default="Okta", null=False, max_length=100)
    can_signup = models.BooleanField(default=True, verbose_name="Can users create accounts without invitation")
    new_signups_are_admins = models.BooleanField(default=True, verbose_name="New signups will be admins")
    metadata = models.TextField()
    icon = models.TextField(null=True)
    icon_hover = models.TextField(null=True)

    def __str__(self):
        return self.provider_name

    def _get_provider_from_metadata(self):
        if 'okta.com' in self.metadata:
            return 'okta'
        if 'microsoftonline.com' in self.metadata:
            return 'microsoft'
        if 'google.com' in self.metadata:
            return 'google'
        return None

    def get_login_url(self, redirect_to=None):
        url = self.login_url if self.login_url else self.LOGIN_VIEW
        if redirect_to:
            relay_state_param_name = self.DEFAULT_RELAY_STATE_PARAM_NAME
            for allowed_relay_state_param_name in self.ALLOWED_RELAY_STATE_PARAM_NAMES:
                endswith = f'{allowed_relay_state_param_name}='
                if url.endswith(endswith):
                    relay_state_param_name = allowed_relay_state_param_name
                    url = url.replace(endswith, '').rstrip('?&')
                    break
            url += ('&' if '?' in url else '?') + urlencode({relay_state_param_name: redirect_to})
        return url

    def get_icon(self):
        if self.icon:
            return self.icon
        return default_icons.icons.get(f'{self._get_provider_from_metadata()}_icon', '')

    def get_icon_hover(self):
        if self.icon_hover:
            return self.icon_hover
        return default_icons.icons.get(f'{self._get_provider_from_metadata()}_icon_hover', '')

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None):
        if self.metadata:
            self.metadata = normalize_newlines(self.metadata)
        return super().save(force_insert=force_insert, force_update=force_update, using=using, update_fields=update_fields)


class IntegrationMixin:
    """
    Assumes model will have a field 'enabled' and property is_valid
    """

    @classmethod
    def get_enabled(cls):
        instance = cls.objects.filter(enabled=True).first()
        if instance is not None and instance.is_valid:
            return instance

    @property
    def is_valid(self):
        raise NotImplementedError()


class EnabledOneIntegrationMixin(IntegrationMixin):
    @property
    def is_valid(self):
        raise NotImplementedError()

    def clean(self):
        self._validate_enabled_instances()

    def _validate_enabled_instances(self):
        if self.enabled:
            enabled_instances = self.__class__.objects.filter(enabled=True)
            if enabled_instances.count() > 0 and self.id != enabled_instances.get().id:
                raise ValidationError(f'Only one enabled {self.__class__.__name__} is allowed')


class VendorCountryForPayments(models.Model):
    """Store vendor's country based on their bank details"""

    # not OneToOne as we might support multiple countries per vendor in future
    vendor = models.ForeignKey('vendors.Vendor', related_name='country_for_payments', on_delete=models.CASCADE)
    country_code = models.CharField(max_length=10, help_text='Country ISO code')
    country_en = models.CharField(max_length=250, help_text='Country name in English')

    class Meta:
        unique_together = (('vendor', 'country_code'),)


def country_name_from_code(country_code):
    country = pycountry.countries.get(alpha_2=country_code)
    if country is not None:
        return country.name
    return NOT_ISO_3166_COUNTRIES.get(country_code)


class DispatchTrack(models.Model, IntegrationMixin):
    enabled = models.BooleanField(default=True)
    base_url = models.CharField(max_length=512)
    api_id = models.CharField(max_length=100)
    api_key = models.CharField(max_length=100)
    slack_webhook_url = models.CharField(max_length=255, null=True, default=None)
    email_recipients = ArrayField(models.TextField(), null=True, blank=True, help_text='Comma separated email addresses')

    @property
    def is_valid(self):
        return bool(self.base_url and self.api_key and self.api_id)

    def update_vendors(self, vendors):
        from .tasks import send_to_dispatchtrack

        for vendor in vendors:
            data = self.get_data_for_vendor(vendor)
            if data and data.get('_send'):
                send_to_dispatchtrack.delay(vendor.id, data)

    def get_data_for_vendor(self, vendor):
        if getattr(vendor, '_deleted', False) or vendor.has_requested_document_expired or vendor.archived:
            status = 0
        else:
            status = 1
        data = {
            '_send': True,
            'driver_number': vendor.id,
            'name': vendor.name,
            'description': vendor.introduction,
            # 'skill_level': '',
            # 'time_zone': '',
            'efficiency_factor': 1,
            'status': status,
        }
        external_id = next((x for x in vendor.external_ids if x.startswith('dispatch_track:')), None)
        if not external_id:  # vendor not in DispatchTrack yet
            data['login'] = vendor.email
            data['password'] = vendor.email
        DispatchTrackUpdateHook(vendor=vendor, data=data)  # allows Server Side Script to modify data in place
        return data


def add_test_ferguson_dispatchtrack(model_admin, request, queryset=None):
    DispatchTrack.objects.create(
        enabled=True,
        base_url='https://fergusontestdni.dispatchtrack.com/drivers/api/import',
        api_id='fergusontestdni',
        api_key='966418da25cab67b0',
    )


add_test_ferguson_dispatchtrack.short_description = 'Add test Fergon env'


class SisenseUserMappingError(RuntimeError):
    pass


class Sisense(models.Model):
    id = models.IntegerField(primary_key=True, choices=((1, 1),), default=1)
    enabled = models.BooleanField(null=False, default=True)
    user_email = models.CharField(max_length=100, verbose_name="Default Sisense account")

    def generate_jwt_token(self, user=None):
        if isinstance(user, str):
            email = user
        else:
            email = self.get_mapped_account(user)
        payload = {
            "typ": "JWT",
            "alg": "HS256",
            "iat": int(time.time()),
            "sub": email,
            "jti": str(uuid.uuid4()),
        }
        token = jwt.encode(payload=payload, key=settings.SISENSE_JWT_SECRET, algorithm='HS256')
        return token

    def get_signed_url(self, return_path, user=None):
        """Returns tuple of url, query params"""
        base_url = settings.SISENSE_BASE_URL.rstrip('/')
        jwt_url = base_url + "/jwt"
        query_params = [
            ('jwt', self.generate_jwt_token(user=user)),
            ('return_to', return_path),
        ]
        return jwt_url, query_params

    def get_dashboards(self):
        if not self.enabled:
            return ()
        return self.api_call("/api/v1/dashboards?fields=title,desc,oid")

    def get_embed_url(self, dashboard=None):
        if dashboard:
            assert '/' not in dashboard
            assert '?' not in dashboard
        return_path = f"/app/main#/dashboards/{dashboard}?embed=true" if dashboard else "/app/main"
        jwt_url, query_params = self.get_signed_url(return_path=return_path)
        return f"{jwt_url}?{urlencode(query_params)}"

    @cached_property
    def account_choices(self):
        result = []
        data = self.api_call("/api/v1/users?fields=email,userName,firstName,lastName", user=settings.SISENSE_ADMIN_EMAIL)
        for x in data:
            user = x['userName']
            name = "{} {}".format(x.get('firstName', ''), x.get('lastName', '')).strip() or x['userName']
            name = "{} <{}>".format(name, x['email'])
            result.append((user, name))
        result.sort(key=operator.itemgetter(1))
        return result

    def get_mapped_account(self, user):
        if user is None:
            user = get_current_user()
        for mapping in self.mappings.all():
            if mapping.user == user and mapping.account:
                return mapping.account
        raise SisenseUserMappingError(f"No sisense user mapped for user {user}")

    @cached_property
    def user_choices(self):
        from users.models import User

        qs = User.objects.all_users_with_permission(Permission.insights())
        result = [(user.id, f"{user.full_name} <{user.email}>") for user in qs]
        result.sort(key=operator.itemgetter(1))
        return result

    def api_call(self, path, user=None):
        jwt_url, query_params = self.get_signed_url(path, user=user)
        session = requests.Session()
        r = session.get(url=jwt_url, params=query_params)
        r.raise_for_status()
        return r.json()


class SisenseUserMapping(models.Model):
    sisense = models.ForeignKey(Sisense, on_delete=models.CASCADE, related_name='mappings')
    user = models.OneToOneField(to='users.User', on_delete=models.CASCADE)
    account = models.CharField(max_length=100, verbose_name='Sisense account')


class ExcelTemplate(models.Model):
    name = models.CharField(max_length=100, null=False, blank=False, unique=True)
    template_source_file = models.CharField(max_length=200, null=False, blank=False)

    @cached_property
    def lambda_client(self):
        return boto3_client('lambda', region_name='us-east-1')  # Lambda function is only in that region now

    def get_substitutions(self, vendor, context=None):
        result = {}
        for sub in self.substitutions.all():
            value = get_mapped_vendors_value(sub.field, vendor, context)
            if value is not None:
                result[sub.cell] = value
        return result

    def get_file(self, vendor, context=None):
        """Return file_context, file_name"""
        data = {'TemplateName': self.template_source_file, 'Substitutions': self.get_substitutions(vendor, context)}
        try:
            response = self.lambda_client.invoke(FunctionName='excel-templating', InvocationType='RequestResponse', Payload=dumps(data))
            content = response['Payload'].read()
            if response['StatusCode'] == 200:
                template_name = self.template_source_file.split('/')[-1]
                base_name, ext = os.path.splitext(template_name)
                file_name = f"{base_name} {vendor.name}{ext}"
                return content, file_name
            else:
                raise Exception('Error when generating PDF. Lambda response: %s', content)
        except Exception:
            logger.exception('PDF generation failed')
            raise


class ExcelTemplateSubstitution(models.Model):
    template = models.ForeignKey(ExcelTemplate, null=False, on_delete=models.CASCADE, related_name='substitutions')
    cell = models.CharField(max_length=100, null=False, blank=False)
    field = models.CharField(max_length=200, null=False, blank=False)


class ShortlistPayIntegration(models.Model):
    """Keeps data about Pay integration for a tenant.

    See more: https://shortlistco.atlassian.net/wiki/spaces/PAY/pages/**********/Data+models#ShortlistPayIntegration"""

    api_token = models.CharField(max_length=128)
    enabled = models.BooleanField(default=False)
    account_reference = models.CharField(max_length=50)


class CheckrIntegration(models.Model, IntegrationMixin):
    enabled = models.BooleanField(default=True)
    api_key = models.CharField(max_length=100)
    account_id = models.CharField(max_length=100, default='')
    default_hierarchy_node = models.CharField(max_length=100, default='')

    @cached_property
    def client(self):
        from integrations.checkr import CheckrClient

        return CheckrClient(self)

    @property
    def is_valid(self):
        return bool(self.api_key)


class CheckrInvitationReport(models.Model):
    NOT_STARTED = 'not_started'
    PENDING = 'pending'
    CLEAR = 'clear'
    CONSIDER = 'consider'
    COMPLETED = 'completed'
    SUSPENDED = 'suspended'
    DISPUTE = 'dispute'
    EXPIRED = 'expired'
    ENGAGED = 'engaged'
    PRE_ADVERSE_ACTION = 'pre_adverse_action'
    POST_ADVERSE_ACTION = 'post_adverse_action'
    REPORT_STATUSES = make_choices(NOT_STARTED, PENDING, CLEAR, CONSIDER, SUSPENDED, DISPUTE)
    INVITATION_STATUSES = make_choices(PENDING, COMPLETED, EXPIRED)
    ADJUDICATION_STATUSES = make_choices(ENGAGED, PRE_ADVERSE_ACTION, POST_ADVERSE_ACTION)

    VENDOR_NOT_STARTED = 'not_started'
    VENDOR_COMPLETED = 'completed'
    VENDOR_MAPPING = {
        None: VENDOR_NOT_STARTED,
        NOT_STARTED: VENDOR_NOT_STARTED,
        PENDING: VENDOR_COMPLETED,
        CLEAR: VENDOR_COMPLETED,
        CONSIDER: VENDOR_COMPLETED,
        SUSPENDED: VENDOR_COMPLETED,
        DISPUTE: VENDOR_COMPLETED,
    }

    vendor = models.ForeignKey('vendors.Vendor', on_delete=models.CASCADE, related_name='+')
    package = models.CharField(max_length=255, db_index=True)

    invitation_id = models.CharField(max_length=255, null=True, blank=True)
    invitation_url = models.URLField(null=True, blank=True)
    invitation_status = models.CharField(choices=INVITATION_STATUSES, max_length=255)

    report_id = models.CharField(max_length=255, null=True, blank=True, db_index=True)
    report_status = models.CharField(choices=REPORT_STATUSES, max_length=255, default=NOT_STARTED, blank=True, db_index=True)
    report_adjudication = models.CharField(choices=ADJUDICATION_STATUSES, max_length=255, null=True, blank=True)

    updated_at = models.DateTimeField(auto_now=True, db_index=True, null=True)

    class Meta:
        verbose_name = 'Checkr invitation report (deprecated)'
        verbose_name_plural = 'Checkr invitation reports (deprecated)'

    def save(self, *args, **kwargs):
        raise ValidationError("Checkr invitation report is deprecated.")


class CheckrInvitation(models.Model):
    NOT_STARTED = 'not_started'
    PENDING = 'pending'
    COMPLETED = 'completed'
    EXPIRED = 'expired'
    INVITATION_STATUSES = make_choices(NOT_STARTED, PENDING, COMPLETED, EXPIRED)

    vendor = models.ForeignKey('vendors.Vendor', on_delete=models.CASCADE, related_name='+')
    package = models.CharField(max_length=255, db_index=True)

    invitation_id = models.CharField(max_length=255, null=True, blank=True)
    url = models.URLField(null=True, blank=True)
    status = models.CharField(choices=INVITATION_STATUSES, default=NOT_STARTED, max_length=255)

    @property
    def completed(self):
        return self.status == self.COMPLETED


class CheckrReport(models.Model):
    NOT_STARTED = 'not_started'
    PENDING = 'pending'
    CLEAR = 'clear'
    CONSIDER = 'consider'
    SUSPENDED = 'suspended'
    DISPUTE = 'dispute'

    ENGAGED = 'engaged'
    PRE_ADVERSE_ACTION = 'pre_adverse_action'
    POST_ADVERSE_ACTION = 'post_adverse_action'

    REPORT_COMPLETED_STATUSES = (CLEAR, CONSIDER, DISPUTE, SUSPENDED)
    REPORT_STATUSES = make_choices(NOT_STARTED, PENDING, CLEAR, CONSIDER, SUSPENDED, DISPUTE)
    ADJUDICATION_STATUSES = make_choices(ENGAGED, PRE_ADVERSE_ACTION, POST_ADVERSE_ACTION)

    VENDOR_NOT_STARTED = 'not_started'
    VENDOR_COMPLETED = 'completed'
    VENDOR_MAPPING = {
        None: VENDOR_NOT_STARTED,
        NOT_STARTED: VENDOR_NOT_STARTED,
        PENDING: VENDOR_COMPLETED,
        CLEAR: VENDOR_COMPLETED,
        CONSIDER: VENDOR_COMPLETED,
        SUSPENDED: VENDOR_COMPLETED,
        DISPUTE: VENDOR_COMPLETED,
    }

    vendor = models.ForeignKey('vendors.Vendor', on_delete=models.CASCADE, related_name='+')
    package = models.CharField(max_length=255, db_index=True)

    report_id = models.CharField(max_length=255, null=True, blank=True, db_index=True)
    status = models.CharField(choices=REPORT_STATUSES, max_length=255, default=NOT_STARTED, blank=True, db_index=True)
    adjudication = models.CharField(choices=ADJUDICATION_STATUSES, max_length=255, null=True, blank=True)

    updated_at = models.DateTimeField(auto_now=True, db_index=True, null=True)

    objects = CheckrReportManager()

    @property
    def completed(self):
        return self.status in self.REPORT_COMPLETED_STATUSES

    @property
    def report_url(self):
        integration = CheckrIntegration.get_enabled()
        if integration and self.report_id:
            vendor_external_id = self.vendor.raw_external_ids.filter(external_id__startswith='checkr:').first()
            if vendor_external_id:
                dashboard_url = settings.CHECKR_BASE_URL.format('dashboard')
                return f'{dashboard_url}/candidates/{vendor_external_id.get_external_id()}/reports/{self.report_id}'
        return None

    @property
    def status_for_vendor(self):
        return self.VENDOR_MAPPING.get(self.status)


def checkr_report_post_change(sender, instance, **kwargs):
    if instance and instance.vendor:
        from preferences.models import recalculate_compliance

        recalculate_compliance([instance.vendor])


post_save.connect(checkr_report_post_change, CheckrReport, dispatch_uid="checkr_report_post_change.post_save")


def update_checkr_packages(model_admin, request, queryset=None):
    CheckrIntegration.get_enabled().client.update_packages()


update_checkr_packages.short_description = 'Update Checkr packages'


class CheckrContinuousCheck(models.Model):
    vendor = models.ForeignKey('vendors.Vendor', on_delete=models.CASCADE, related_name='+')
    package_type = models.CharField(max_length=255, db_index=True)

    continuous_check_id = models.CharField(max_length=255, null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)


class PaycassoIntegration(EnabledOneIntegrationMixin, models.Model):
    DEFAULT_FACE_SCAN_THRESHOLD = 0.95

    account_number = models.CharField(max_length=255, db_index=True)
    app_id = models.CharField(max_length=255, db_index=True)
    api_authorization = models.CharField(max_length=255)
    callback_authorization = models.CharField(max_length=255)
    privacy_policy_url = models.URLField()
    terms_and_condition_url = models.URLField()
    face_scan_threshold = models.FloatField(default=DEFAULT_FACE_SCAN_THRESHOLD)
    enabled = models.BooleanField(default=True)

    objects = PaycassoIntegrationManager()

    @cached_property
    def service(self):
        from integrations.paycasso import PaycassoService

        return PaycassoService(self)

    @property
    def is_valid(self):
        return True


class PaycassoEnrolment(models.Model):
    PENDING = 'pending'
    NOT_VERIFIED = 'not_verified'
    COMPLETED = 'completed'
    ENROLMENT_STATUSES = make_choices(PENDING, NOT_VERIFIED, COMPLETED)

    user = models.OneToOneField('users.User', primary_key=True, on_delete=models.CASCADE, related_name='paycasso_enrolment')
    consumer_ref = models.CharField(max_length=255, db_index=True, unique=True)
    verification_transaction_ref = models.CharField(max_length=255, null=True, blank=False)
    status = models.CharField(choices=ENROLMENT_STATUSES, max_length=255, default=PENDING)
    created_at = models.DateTimeField(auto_now_add=True)

    @property
    def verification(self):
        if not self.verification_transaction_ref:
            return None

        return PaycassoVerification.objects.filter(transaction_ref=self.verification_transaction_ref).first()


class PaycassoVerification(models.Model):
    PENDING = 'pending'
    COMPLETED = 'completed'
    USED = 'used'
    FAILED = 'failed'
    VERIFICATION_STATUSES = make_choices(PENDING, COMPLETED, USED, FAILED)

    user = models.ForeignKey('users.User', on_delete=models.CASCADE, related_name='+')
    transaction_ref = models.CharField(max_length=255, db_index=True)
    status = models.CharField(choices=VERIFICATION_STATUSES, max_length=255, default=PENDING)
    created_at = models.DateTimeField(auto_now_add=True)


def paycasso_verification_post_save(sender, instance, **kwargs):
    if instance.status == PaycassoVerification.COMPLETED:
        enrolment = PaycassoEnrolment.objects.filter(user=instance.user, verification_transaction_ref=instance.transaction_ref).first()
        if enrolment and enrolment.status == PaycassoEnrolment.NOT_VERIFIED:
            enrolment.status = PaycassoEnrolment.COMPLETED
            enrolment.save(update_fields=['status'])
            instance.status = PaycassoVerification.USED
            instance.save(update_fields=['status'])
            if enrolment.consumer_ref not in enrolment.user.vendor.external_ids:
                from vendors.models.vendor import VendorExternalId

                VendorExternalId.objects.create(vendor=enrolment.user.vendor, external_id=enrolment.consumer_ref)
            recalculate_onboarding_completeness.delay([enrolment.user.vendor_id], source=['paycasso_verification_post_save'])


post_save.connect(paycasso_verification_post_save, PaycassoVerification, dispatch_uid="paycasso_verification_post_save.post_save")


class PaycassoEvent(models.Model):
    event_name = models.CharField(max_length=250)
    event_date = models.DateTimeField(db_index=True, default=datetime.now)
    vendor = models.ForeignKey('vendors.Vendor', null=True, on_delete=models.CASCADE, related_name='+')
    ip_address = models.CharField(max_length=MAX_IP_LENGTH, db_index=True, null=True)

    class Meta:
        ordering = ['-event_date']


class VeriffMixin:
    VERIFF_TYPE_PASSPORT = 'PASSPORT'
    VERIFF_TYPE_ID_CARD = 'ID_CARD'
    VERIFF_TYPE_RESIDENCE_PERMIT = 'RESIDENCE_PERMIT'
    VERIFF_TYPE_DRIVERS_LICENSE = 'DRIVERS_LICENSE'
    VERIFF_TYPES = (
        (
            VERIFF_TYPE_PASSPORT,
            'Passport',
        ),
        (
            VERIFF_TYPE_ID_CARD,
            'ID Card',
        ),
        (
            VERIFF_TYPE_RESIDENCE_PERMIT,
            'Residence permit',
        ),
        (
            VERIFF_TYPE_DRIVERS_LICENSE,
            "Driver's License",
        ),
    )


class VeriffIntegration(EnabledOneIntegrationMixin, models.Model):
    enabled = models.BooleanField(default=True)

    objects = VeriffIntegrationManager()

    def get_service(self, veriff_type):
        integration_type = self.get_integration_type(veriff_type=veriff_type)
        return integration_type.service

    def get_integration_type(self, veriff_type):
        integration_type = self.veriffintegrationtype_set.filter(veriff_type=veriff_type).first()
        if integration_type is None:
            raise ImproperlyConfigured(f'Veriff Integration for {veriff_type} is not configured')

        return integration_type

    @property
    def available_integration_types(self):
        from integrations.veriff import VeriffService

        return list(VeriffService.get_available_integration_types())

    @property
    def is_valid(self):
        return self.veriffintegrationtype_set.exists()


class VeriffIntegrationType(VeriffMixin, models.Model):
    integration = models.ForeignKey(VeriffIntegration, on_delete=models.CASCADE)
    veriff_type = models.CharField(max_length=50, null=True, blank=True, choices=VeriffMixin.VERIFF_TYPES)
    api_public_key = models.CharField(max_length=255, verbose_name='API Publishable key')
    api_private_key = models.CharField(max_length=255, verbose_name='API Private key')

    class Meta:
        unique_together = (
            'integration',
            'veriff_type',
        )

    @cached_property
    def service(self):
        from integrations.veriff import VeriffService

        return VeriffService(self)


class VeriffVerification(VeriffMixin, models.Model):
    REMINDER_DELAY = 6 * 3600

    STATUS_CREATED = 'created'
    STATUS_STARTED = 'started'
    STATUS_SUBMITTED = 'submitted'
    STATUS_RESUBMISSION_REQUESTED = 'resubmission_requested'
    STATUS_APPROVED = 'approved'
    STATUS_DECLINED = 'declined'
    STATUS_REVIEW = 'review'
    STATUS_ABANDONED = 'abandoned'
    STATUS_EXPIRED = 'expired'

    STATUSES = make_choices(
        STATUS_CREATED,
        STATUS_STARTED,
        STATUS_SUBMITTED,
        STATUS_RESUBMISSION_REQUESTED,
        STATUS_APPROVED,
        STATUS_DECLINED,
        STATUS_REVIEW,
        STATUS_ABANDONED,
        STATUS_EXPIRED,
    )

    UNUSABLE_STATUSES = {STATUS_EXPIRED, STATUS_ABANDONED}
    DOABLE_STATUSES = {STATUS_CREATED, STATUS_STARTED, STATUS_SUBMITTED, STATUS_RESUBMISSION_REQUESTED}

    TRANSITIONS = {
        STATUS_CREATED: [STATUS_STARTED, STATUS_ABANDONED, STATUS_EXPIRED],
        STATUS_STARTED: [STATUS_SUBMITTED, STATUS_ABANDONED, STATUS_EXPIRED],
        STATUS_SUBMITTED: [STATUS_RESUBMISSION_REQUESTED, STATUS_APPROVED, STATUS_DECLINED, STATUS_REVIEW],
        STATUS_RESUBMISSION_REQUESTED: [STATUS_STARTED],
        STATUS_APPROVED: [],
        STATUS_DECLINED: [],
        STATUS_REVIEW: [],
        STATUS_ABANDONED: [],
        STATUS_EXPIRED: [],
    }

    vendor = models.ForeignKey('vendors.Vendor', on_delete=models.CASCADE, related_name='+')
    veriff_type = models.CharField(max_length=50, choices=VeriffMixin.VERIFF_TYPES, null=True, blank=True)
    status = models.CharField(max_length=50, choices=STATUSES, default=STATUS_CREATED)
    verification_id = models.CharField(max_length=50)
    verification_url = models.CharField(max_length=512)
    verification_session_token = models.CharField(max_length=512)
    verification_base_url = models.CharField(max_length=255)
    expired_at = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)

    objects = VeriffVerificationManager()

    def save(self, *args, **kwargs):
        self._set_expired_at()
        return super().save(*args, **kwargs)

    def set_status_and_save(self, status):
        if status in self.TRANSITIONS[self.status]:
            self.status = status
            self.save()
            self.on_status_changed()
            return True
        return False

    def on_status_changed(self):
        on_status_changed = f'on_status_changed_to_{self.status}'
        if hasattr(self, on_status_changed):
            getattr(self, on_status_changed)()

    def on_status_changed_to_resubmission_requested(self):
        from .tasks import veriff_remind_about_resubmission_requested

        veriff_remind_about_resubmission_requested.apply_async(args=[self.id], countdown=self.REMINDER_DELAY)

    def _set_expired_at(self):
        if self.expired_at is None:
            if self.verification_session_token:
                session_data = jwt.decode(
                    self.verification_session_token, algorithms=['HS256'], options={"verify_signature": False, "verify_exp": True}
                )
                now = timezone.datetime.fromtimestamp(session_data["iat"]).replace(tzinfo=connection.tenant.timezone)
            else:
                now = timezone.now()
            self.expired_at = now + timezone.timedelta(days=7)


class Ten99PolicyConfig(IntegrationMixin, models.Model):
    ENV_SANDBOX = "sandbox"
    ENV_PRODUCTION = "production"

    enabled = models.BooleanField(default=False)
    environment = models.CharField(max_length=50, choices=make_choices(ENV_SANDBOX, ENV_PRODUCTION), default=ENV_SANDBOX)
    app_secret = models.CharField(max_length=100)
    webhook_secret = models.CharField(max_length=100, null=True)
    job_category_codes_mapping = models.JSONField(default=dict)
    job_category_custom_field = models.ForeignKey(
        "preferences.CustomField",
        on_delete=models.SET_NULL,
        related_name="+",
        null=True,
        limit_choices_to=Q(template__model=CustomFieldsTemplate.JOB_OPENING_MODEL) & (Q(type=TYPE_CHOICE) | Q(type=TYPE_SELECT)),
    )
    location_custom_field = models.ForeignKey(
        "preferences.CustomField",
        on_delete=models.SET_NULL,
        related_name="+",
        null=True,
        limit_choices_to=Q(template__model=CustomFieldsTemplate.JOB_OPENING_MODEL) & (Q(type=TYPE_CHOICE) | Q(type=TYPE_SELECT)),
    )
    required_coverage_type_custom_field = models.ForeignKey(
        "preferences.CustomField",
        on_delete=models.SET_NULL,
        related_name="+",
        null=True,
        limit_choices_to=(Q(template__model=CustomFieldsTemplate.JOB_OPENING_MODEL) | Q(template__name__startswith="onboardingstage-"))
        & Q(type=TYPE_CHOICE),
    )
    expense_work_item_categories = models.ManyToManyField(WorkItemCategory, related_name='ten99_policy_configs')
    required_coverage_type_custom_field_id: int | None  # because PyCharm doesn't know this is an int...

    def clean(self):
        if self.enabled:
            self._validate_only_one_enabled()
            self._validate_required_fields_filled()

    def _validate_only_one_enabled(self):
        if Ten99PolicyConfig.objects.filter(enabled=True).exclude(id=self.id).exists():
            raise ValidationError("Only one Ten99PolicyConfig can be enabled at a time.")

    def _validate_required_fields_filled(self):
        if not self.is_fully_configured:
            raise ValidationError(
                "You can not enable this integration without this fields filled: "
                "app secret, job category custom field, location custom field, required coverage type custom field"
            )

    @property
    def is_fully_configured(self):
        return all(
            [self.app_secret, self.job_category_custom_field_id, self.location_custom_field_id, self.required_coverage_type_custom_field_id]
        )

    @property
    def is_valid(self):
        return self.enabled and self.is_fully_configured


class Ten99PolicyCoverageType(StrEnum):
    workers_compensation = auto()
    general_liability = auto()


class Ten99PolicyRequestStatus(StrEnum):
    not_started = auto()
    in_progress = auto()
    completed = auto()
    partially_completed = auto()
    not_completed = auto()
    cancelled = auto()


class Ten99PolicyRequest(models.Model):
    onboarding_context_name = models.CharField(max_length=110, db_index=True)
    reupload_allowed = models.BooleanField(default=False)
    is_cancelled = models.BooleanField(default=False)

    def __str__(self):
        return f"Request #{self.pk} ({self.onboarding_context_name})"

    @property
    def status(self) -> Ten99PolicyRequestStatus:
        if self.is_cancelled:
            return Ten99PolicyRequestStatus.cancelled

        coverages = self.coverages.all()
        if all(coverage.status == Ten99PolicyCoverageStatus.not_started for coverage in coverages):
            return Ten99PolicyRequestStatus.not_started
        elif any(coverage.status == Ten99PolicyCoverageStatus.processing for coverage in coverages):
            return Ten99PolicyRequestStatus.in_progress
        elif all(
            coverage.status in {Ten99PolicyCoverageStatus.approved, Ten99PolicyCoverageStatus.active, Ten99PolicyCoverageStatus.eligible}
            for coverage in coverages
        ):
            return Ten99PolicyRequestStatus.completed
        elif any(
            coverage.status in {Ten99PolicyCoverageStatus.approved, Ten99PolicyCoverageStatus.active} for coverage in coverages
        ) and any(
            coverage.status in {Ten99PolicyCoverageStatus.rejected, Ten99PolicyCoverageStatus.not_completed} for coverage in coverages
        ):
            return Ten99PolicyRequestStatus.partially_completed
        else:
            return Ten99PolicyRequestStatus.not_completed

    @property
    def external_coverage_status(self) -> Ten99PolicyRequestStatus:
        """
        Status of coverages purchased via 1099Policy, excluding manually uploaded coverages.
        """
        if self.is_cancelled:
            return Ten99PolicyRequestStatus.cancelled

        coverages = self.coverages.filter(own_insurance=False).all()
        if all(coverage.status == Ten99PolicyCoverageStatus.not_started for coverage in coverages):
            return Ten99PolicyRequestStatus.not_started
        if any(coverage.status == Ten99PolicyCoverageStatus.processing for coverage in coverages):
            return Ten99PolicyRequestStatus.in_progress
        if all(coverage.status in {Ten99PolicyCoverageStatus.active, Ten99PolicyCoverageStatus.eligible} for coverage in coverages):
            return Ten99PolicyRequestStatus.completed

        return Ten99PolicyRequestStatus.not_completed

    @property
    def contractor(self):
        if hasattr(self, "quote") and self.quote.contractor_id:
            return self.quote.contractor

        if hasattr(self, "assignment") and self.assignment.contractor_id:
            return self.assignment.contractor

        return None

    @staticmethod
    def get_onboarding_context_from_jo(job_opening_id: int | str) -> str:
        return f"jobopening:{job_opening_id}"

    def cancel(self) -> None:
        self.is_cancelled = True
        self.save()


class Ten99PolicyCoverageStatus(StrEnum):
    not_started = auto()
    processing = auto()
    approved = auto()
    rejected = auto()
    active = auto()
    eligible = auto()
    not_completed = auto()


class Ten99PolicyCoveragePurchaseNotAllowedReason(StrEnum):
    monopolistic_state = auto()
    contractor_already_has_matching_policy = auto()


class Ten99PolicyCoverage(models.Model):
    request = models.ForeignKey(Ten99PolicyRequest, on_delete=models.CASCADE, related_name="coverages")
    type = models.CharField(max_length=20, choices=make_choices_from_enums(Ten99PolicyCoverageType))
    own_insurance = models.BooleanField(null=True, default=None)
    status = models.CharField(
        max_length=20,
        choices=make_choices_from_enums(Ten99PolicyCoverageStatus),
        default=Ten99PolicyCoverageStatus.not_started,
    )
    purchase_not_allowed = models.BooleanField(default=False)
    purchase_not_allowed_reason = models.CharField(
        max_length=64,
        null=True,
        blank=True,
        choices=make_choices_from_enums(Ten99PolicyCoveragePurchaseNotAllowedReason),
    )
    document = models.OneToOneField("documents.Insurance", on_delete=models.CASCADE, null=True)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    rejection_reason = models.CharField(max_length=255, null=True, blank=True)
    expiration_date = models.DateField(null=True, blank=True)

    def __str__(self):
        return f"{self.type}"

    @classmethod
    def get_upload_path(cls, user=None, *args, **kwargs):
        current_user = user if isinstance(user, User) else get_current_user()
        return connection.tenant.storage_path('uploads', 'insurances', current_user.slug if current_user else 'system', *args)


class Ten99PolicyContractor(models.Model):
    t99p_id = models.CharField(max_length=100, primary_key=True)
    vendor = models.OneToOneField("vendors.Vendor", on_delete=models.CASCADE, related_name="ten99policy_contractor")

    def __str__(self):
        return f"{self.vendor.name} ({self.t99p_id})"


class Ten99PolicyEntity(models.Model):
    t99p_id = models.CharField(max_length=250, primary_key=True)
    entity_name = models.CharField(max_length=250)

    def __str__(self):
        return f"{self.entity_name} ({self.t99p_id})"


class Ten99PolicyJob(models.Model):
    t99p_id = models.CharField(max_length=100, primary_key=True)
    entity = models.ForeignKey(Ten99PolicyEntity, on_delete=models.CASCADE, related_name="jobs")
    state = models.CharField(max_length=2)
    category_code = models.CharField(max_length=20)
    request = models.OneToOneField(Ten99PolicyRequest, on_delete=models.CASCADE, related_name="job")

    def __str__(self):
        return f"{self.t99p_id}"


class Ten99PolicyQuote(models.Model):
    t99p_id = models.CharField(max_length=100, primary_key=True)
    job = models.OneToOneField(Ten99PolicyJob, on_delete=models.CASCADE, related_name="quote")
    contractor = models.ForeignKey(Ten99PolicyContractor, on_delete=models.CASCADE, related_name="quotes")
    effective_date = models.DateField()
    end_date = models.DateField()
    eligible = models.BooleanField(default=False)
    gl_net_rate = models.IntegerField(null=True)
    wc_net_rate = models.IntegerField(null=True)
    gl_service_fee = models.IntegerField(null=True)
    wc_service_fee = models.IntegerField(null=True)
    request = models.OneToOneField(Ten99PolicyRequest, on_delete=models.CASCADE, related_name="quote")

    def __str__(self):
        return f"{self.t99p_id}"


class Ten99PolicySession(models.Model):
    t99p_id = models.CharField(max_length=100, primary_key=True)
    quote = models.OneToOneField(Ten99PolicyQuote, on_delete=models.CASCADE, related_name="session")
    onboarding_url = models.CharField(max_length=512)
    request = models.OneToOneField(Ten99PolicyRequest, on_delete=models.CASCADE, related_name="session")

    def __str__(self):
        return f"{self.t99p_id}"


class Ten99PolicyAssignment(models.Model):
    t99p_id = models.CharField(max_length=100, primary_key=True)
    contractor = models.ForeignKey(Ten99PolicyContractor, on_delete=models.CASCADE, related_name="assignments")
    job = models.OneToOneField(Ten99PolicyJob, on_delete=models.CASCADE, related_name="assignment")
    gl_net_rate = models.IntegerField(null=True)
    wc_net_rate = models.IntegerField(null=True)
    gl_service_fee = models.IntegerField(null=True)
    wc_service_fee = models.IntegerField(null=True)
    request = models.OneToOneField(Ten99PolicyRequest, on_delete=models.CASCADE, related_name="assignment")

    def __str__(self):
        return f"{self.t99p_id}"


class Ten99PolicyInvoice(models.Model):
    t99p_id = models.CharField(max_length=100, primary_key=True)
    contractor = models.ForeignKey(Ten99PolicyContractor, on_delete=models.CASCADE, related_name="invoices")
    job = models.OneToOneField(Ten99PolicyJob, on_delete=models.CASCADE, related_name="invoices")
    gross_pay = models.IntegerField()
    pay_cycle_start_date = models.DateField()
    pay_cycle_end_date = models.DateField()
    premium_due = models.IntegerField()
    request = models.OneToOneField(Ten99PolicyRequest, on_delete=models.CASCADE, related_name="invoice")

    def __str__(self):
        return f"{self.t99p_id}"


class DirectIntegrationConfig(IntegrationMixin, models.Model):
    linked_account_id = models.CharField(max_length=100, primary_key=True)
    integration = models.CharField(max_length=100)
    manual_sync_groups = ArrayField(
        models.CharField(max_length=50, choices=make_choices_from_enums(ManualSyncControlGroup)),
        default=list,
        blank=True,
    )


class DirectIntegrationEnabledProcess(models.Model):
    config = models.ForeignKey(DirectIntegrationConfig, on_delete=models.CASCADE, related_name="enabled_processes")
    process_name = models.CharField(max_length=100)
    events = ArrayField(models.CharField(max_length=100))
    event_filter_handler = models.CharField(max_length=512, null=True, blank=True, default=None)


class DirectIntegrationMapping(models.Model):
    config = models.ForeignKey(DirectIntegrationConfig, on_delete=models.CASCADE, related_name="mappings")
    source_model = models.TextField()
    source_field = models.TextField()
    target_model = models.TextField()
    target_field = models.TextField()

    SOURCE_FIELD_FORMAT = 'custom_fields.{id} ("{name}")'

    class Meta:
        constraints = [
            UniqueConstraint(fields=["config", "target_model", "target_field"], name="unique_target_mapping"),
        ]

    def __str__(self):
        if self.source_field and self.source_field.startswith("custom_fields."):
            _, custom_field_id = self.source_field.split(".", maxsplit=1)
            source_field = self._get_formatted_source_field(custom_field_id)
        else:
            source_field = self.source_field

        source = f"{self.source_model}.{source_field}".rstrip(".") or "N/A"
        target = f"{self.target_model}.{self.target_field}".rstrip(".") or "N/A"
        return f"{source} -> {target}"

    def _get_formatted_source_field(self, custom_field_id: str):
        if self.source_model == SOURCE_VENDOR:
            custom_field = CustomVendorField.objects.get(pk=int(custom_field_id))
            return self.SOURCE_FIELD_FORMAT.format(id=custom_field.id, name=custom_field.name)

        custom_field = CustomField.objects.get(pk=int(custom_field_id))
        return self.SOURCE_FIELD_FORMAT.format(id=custom_field.id, name=custom_field.label)


class DirectIntegrationValueMapping(models.Model):
    mapping = models.ForeignKey(DirectIntegrationMapping, on_delete=models.CASCADE, related_name="value_mappings")
    source_value = models.TextField()
    target_value = models.TextField()

    class Meta:
        constraints = [
            UniqueConstraint(fields=["mapping", "source_value"], name="unique_source_value"),
        ]


class DirectIntegrationVendorSyncEligibility(models.Model):
    config = models.ForeignKey(DirectIntegrationConfig, on_delete=models.CASCADE)
    vendor = models.ForeignKey('vendors.Vendor', on_delete=models.CASCADE, related_name='+')

    class Meta:
        constraints = [models.UniqueConstraint(fields=['config', 'vendor'], name='unique_config_vendor')]


def direct_integrations_invalidate_cache(sender, instance, *args, **kwargs):
    # triggered by post_save/post_delete hooks
    cache.enabled_processes.invalidate()


post_save.connect(
    direct_integrations_invalidate_cache, DirectIntegrationEnabledProcess, dispatch_uid="direct_integrations_invalidate_cache.post_save"
)
post_save.connect(
    direct_integrations_invalidate_cache, DirectIntegrationConfig, dispatch_uid="direct_integrations_invalidate_cache.post_save"
)
post_delete.connect(
    direct_integrations_invalidate_cache, DirectIntegrationEnabledProcess, dispatch_uid="direct_integrations_invalidate_cache.post_delete"
)
post_delete.connect(
    direct_integrations_invalidate_cache, DirectIntegrationConfig, dispatch_uid="direct_integrations_invalidate_cache.post_delete"
)


class DirectIntegrationConstant(models.Model):
    config = models.ForeignKey(DirectIntegrationConfig, on_delete=models.CASCADE, related_name="constants")
    name = models.CharField(max_length=100)
    value = models.CharField(max_length=1024)

    class Meta:
        constraints = [
            UniqueConstraint(fields=["config", "name"], name="unique_config_name"),
        ]


class DirectIntegrationsOverwrites(EnabledOneIntegrationMixin, models.Model):
    enabled = models.BooleanField(default=False)
    uri = models.TextField()
    api_key = models.TextField()

    @property
    def is_valid(self):
        return self.uri and self.api_key

    def clean(self):
        if not self.is_valid:
            raise ValidationError("Both URI and API key must be set")
        super().clean()

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        direct_integrations_reset_config_provider()

    def delete(self, *args, **kwargs):
        super().delete(*args, **kwargs)
        direct_integrations_reset_config_provider()


class TaxBandits1099NecFormPostalRequestStatus(StrEnum):
    not_requested = auto()
    sent = auto()
    failed = auto()


class TaxBandits1099NecForm(models.Model):
    vendor_1099nec_form = models.OneToOneField("vendors.Vendor1099NecForm", on_delete=models.CASCADE)
    submission_id = models.CharField(max_length=100)
    record_id = models.CharField(max_length=100)
    postal_request_status = models.CharField(max_length=50, default=TaxBandits1099NecFormPostalRequestStatus.not_requested)
    postal_request_failed_reason = models.CharField(max_length=200, null=True)

    class Meta:
        verbose_name = "TaxBandits 1099-NEC form"
        verbose_name_plural = "TaxBandits 1099-NEC forms"

    def __str__(self):
        return f"{self.record_id}"


class TaxBandits1099NecPostalRequestStatus(StrEnum):
    pending = auto()
    finished = auto()


class TaxBandits1099NecPostalRequest(models.Model):
    vendor_1099_forms = models.ManyToManyField("integrations.TaxBandits1099NecForm", related_name="postal_requests")
    requested_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "TaxBandits 1099-NEC postal request"
        verbose_name_plural = "TaxBandits 1099-NEC postal requests"

    def __str__(self):
        return f"#{self.pk} {self.requested_at.strftime('%Y-%m-%d %H:%M')}"

    @property
    def total_forms(self):
        return self.vendor_1099_forms.count()

    @property
    def sent_forms(self):
        return self.vendor_1099_forms.filter(postal_request_status=TaxBandits1099NecFormPostalRequestStatus.sent).count()

    @property
    def failed_forms(self):
        return self.vendor_1099_forms.filter(postal_request_status=TaxBandits1099NecFormPostalRequestStatus.failed).count()

    @property
    def processed_forms(self):
        return self.sent_forms + self.failed_forms

    @property
    def status(self):
        if self.total_forms == self.sent_forms + self.failed_forms:
            return TaxBandits1099NecPostalRequestStatus.finished
        return TaxBandits1099NecPostalRequestStatus.pending
