import base64
import hmac
import json
import logging
from hashlib import sha256

import requests
import sentry_sdk
from botocore.exceptions import ClientError
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured, ValidationError, PermissionDenied, BadRequest
from django.db import connection
from django.db.models import Exists, OuterRef
from django.db.transaction import atomic
from django.http import HttpResponseRedirect, JsonResponse
from django.utils.crypto import salted_hmac
from django.utils.encoding import force_bytes
from django.utils.http import urlencode
from email_reply_parser import EmailReplyParser
from rest_framework import status, permissions, parsers, renderers, exceptions
from rest_framework.generics import get_object_or_404
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ViewSet

from bulk_import.helpers import BulkImportIdempotencyHelper
from clients import features
from deals.models import SignRequest
from events.event_types import Integration<PERSON>rror, Checkr<PERSON><PERSON>inuousCheckWebH<PERSON>, UserUnsubscribed, VendorSyncEligibilityEnabled
from integrations.checkr import add_checkr_integration, add_to_checkr_url
from notifications.base import all_classes
from onboarding.models import OnBoardingStagesForVendor
from onboarding.tasks import recalculate_onboarding_completeness
from payments.config import has_payment_processing_enabled
from integrations.services.ten99policy.service.continue_on_ten99policy import continue_on_ten99policy
from shortlist.current_user import get_current_user, as_user
from shortlist.mail import SendGridMailBackend
from shortlist.permissions import Permission, IsUser, IsBuyer, TenantFeaturesEnabled, IsStaff, Or
from shortlist.track_tasks import start_task_tracking
from shortlist.utils import serve_file_contents, serve_presigned_url, boto3_resource, filepicker_policy
from users.models import User, Actor, UserRestoreSubscription
from vendors.models import Vendor
from .dto import DirectIntegrationStatus, IntegrationsStatus, EligibilityStatus, ENABLED, DISABLED
from .models import (
    Slack,
    SAML,
    ExcelTemplate,
    CheckrIntegration,
    PaycassoEnrolment,
    PaycassoVerification,
    VeriffVerification,
    SisenseUserMappingError,
    Ten99PolicyConfig,
    Ten99PolicyCoverage,
    DirectIntegrationConfig,
    DirectIntegrationVendorSyncEligibility,
)
from .paycasso import PaycassoService, PaycassoEnrolmentError, PaycassoVerificationError, PaycassoLogger
from .sdk.ten99policy.config_values import get_ten99_config
from .sendgrid_unsubscribe import SendgridUnsubscribeClient, SendgridUnsubscribeInvalidToken
from .services.flatfile import Flatfile, FlatfileModuleNotSupportedException
from .services.flatfile_base import FlatfileInvalidContextException
from .services.taxbandits.exceptions import TaxBanditsException
from .services.taxbandits.webhooks.dispatcher import handle_e_file_status_change_webhook_request
from .services.taxbandits.webhooks.signature import verify_signature, SignatureError
from .services.ten99policy.data_model import InsuranceDecisionData, BuyerDecisionData
from .services.ten99policy.data_provider.impl import get_data_provider
from .services.ten99policy.data_provider.interface import Ten99PDataProvider
from .services.ten99policy.exception_handler import Ten99PExceptionHandler
from .services.ten99policy.exceptions import Ten99PWebhookException
from .services.ten99policy.service.buyer import (
    onboarding_stage_get_details,
    onboarding_stage_save_decision,
    onboarding_stage_allow_retry,
)
from .services.ten99policy.service.prevalidate import prevalidate
from .services.ten99policy.service.generate_quote import generate_quote
from .services.ten99policy.service.save_insurance_decision import save_insurance_decision
from .services.ten99policy.service.handle_return_from_ten99policy import handle_return_from_ten99policy
from .services.ten99policy.webhooks.dispatcher import handle_webhook_request
from .services.ten99policy.webhooks.verify_signature import verify_webhook_signature
from .tasks import flatfile_bulk_import
from .veriff import VeriffService, VeriffError

logger = logging.getLogger('integrations')

from . import events_to_slack  # noqa: F401  !!DO NOT REMOVE!!
from . import events_to_zapier  # noqa: F401  !!DO NOT REMOVE!!


def security_token(salt):
    return salted_hmac(salt, f"{connection.tenant.root_url}_{get_current_user().id}").hexdigest()


def slack_redirect_url():
    redirect_params = {"return_to": connection.tenant.root_url + "settings/integrations/slack/"}
    redirect_url = f"{settings.SLACK_AUTH_REDIRECT_URL}?{urlencode(redirect_params)}"
    return redirect_url


def add_slack_integration(data):
    request_url = b"https://slack.com/api/oauth.access"
    params = {
        'client_id': settings.SLACK_APP_ID,
        'client_secret': settings.SLACK_APP_SECRET,
        'code': data['code'],
        'redirect_uri': slack_redirect_url(),
    }
    response = requests.post(request_url, params)
    if response.status_code == status.HTTP_200_OK:
        data = response.json()
        if data['ok']:
            Slack.objects.create(
                access_token=data['access_token'],
                team_id=data['team_id'],
                team_name=data['team_name'],
                incoming_url=data['incoming_webhook']['url'],
                configuration_url=data['incoming_webhook']['configuration_url'],
                channel=data['incoming_webhook']['channel'],
                scope=data['scope'],
            )
            return True
    IntegrationError(response.json())
    return False


def add_to_slack_url():
    slack_params = {
        "scope": "incoming-webhook",
        "client_id": settings.SLACK_APP_ID,
        "redirect_uri": slack_redirect_url(),
        "state": security_token("slack"),
    }
    return f"{settings.SLACK_AUTH_ORIGINAL_URL}?{urlencode(slack_params)}"


class IntegrationsView(APIView):
    permission_classes = [Permission.buyer('integrations', 'create')]
    add_integration = {
        'slack': add_slack_integration,
    }
    if settings.CHECKR_CLIENT_ID and settings.CHECKR_CLIENT_SECRET:
        add_integration.update(
            {
                'checkr': add_checkr_integration,
            }
        )
    get_integration = {
        'slack': Slack,
        'checkr': CheckrIntegration,
    }

    def get_existing_types(self):
        return [type for (type, model) in self.get_integration.items() if model.objects.all().exists()]

    def get(self, request, *args, **kwargs):
        result = {"addToSlack": add_to_slack_url(), "addToCheckr": add_to_checkr_url(), "existing": self.get_existing_types()}
        return Response(result)

    def post(self, request, *args, **kwargs):
        data = request.data
        type = data.pop('type', None)
        state = data.pop('state', None)
        if not type:
            return Response("Missing integration type", status=status.HTTP_400_BAD_REQUEST)
        if type not in self.add_integration:
            return Response("Invalid integration type", status=status.HTTP_400_BAD_REQUEST)
        if not state:
            return Response("Missing security token", status=status.HTTP_400_BAD_REQUEST)
        if state != security_token(type):
            return Response("Invalid security token", status=status.HTTP_400_BAD_REQUEST)
        if self.add_integration[type](data):
            return Response(status=status.HTTP_201_CREATED)
        return Response("Integration error, please contact support", status=status.HTTP_400_BAD_REQUEST)


class IntegrationsStatusView(APIView):
    permission_classes = [Or(IsBuyer, IsStaff)]

    def get(self, request, *args, **kwargs):
        integrations = []
        for config in DirectIntegrationConfig.objects.all():
            integrations.append(
                DirectIntegrationStatus(
                    id=config.linked_account_id,
                    integration=config.integration,
                    enabled=True,  # all enabled by default
                    manual_sync_groups=config.manual_sync_groups,
                )
            )

        integration_status = IntegrationsStatus(
            direct_integrations=integrations,
        )

        return JsonResponse(integration_status.model_dump())


class DirectIntegrationVendorSyncEligibilityView(ViewSet):
    permission_classes = [Or(IsBuyer, IsStaff)]

    def list(self, request, *args, **kwargs):
        vendor_id = kwargs.get('vendor_id')
        configs = DirectIntegrationConfig.objects.annotate(
            is_enabled=Exists(DirectIntegrationVendorSyncEligibility.objects.filter(config=OuterRef('pk'), vendor_id=vendor_id))
        )

        response: dict[str, EligibilityStatus] = {
            config.linked_account_id: ENABLED if config.is_enabled else DISABLED for config in configs
        }

        return JsonResponse(response)

    def create(self, request, *args, **kwargs):
        vendor_id = kwargs.get('vendor_id')
        integration_id = kwargs.get('integration_id')

        vendor = Vendor.objects.get(id=vendor_id)
        if vendor.status == Vendor.STATUS_NOT_INVITED or vendor.status == Vendor.STATUS_INVITED:
            logger.info(f'Vendor sync eligibility disallowed for vendor_id: {vendor_id}, integration_id: {integration_id}')
            return Response(status=status.HTTP_400_BAD_REQUEST)

        sync_eligibility, _ = DirectIntegrationVendorSyncEligibility.objects.update_or_create(
            config_id=integration_id,
            vendor_id=vendor_id,
        )
        VendorSyncEligibilityEnabled(sync_eligibility)

        logger.info(f'Vendor sync eligibility enabled for vendor_id: {vendor_id}, integration_id: {integration_id}')

        return Response(status=status.HTTP_200_OK)

    def delete(self, request, *args, **kwargs):
        vendor_id = kwargs.get('vendor_id')
        integration_id = kwargs.get('integration_id')

        DirectIntegrationVendorSyncEligibility.objects.filter(
            config_id=integration_id,
            vendor_id=vendor_id,
        ).delete()

        logger.info(f'Vendor sync eligibility disabled for vendor_id: {vendor_id}, integration_id: {integration_id}')

        return Response(status=status.HTTP_200_OK)


class PaymentProcessor(APIView):
    permission_classes = [IsUser]

    def get(self, request, *args, **kwargs):
        return Response(has_payment_processing_enabled(connection.tenant))


class HelloSignCallback(APIView):
    permission_classes = []
    event_types = {'signature_request_signed', 'signature_request_all_signed'}

    def handle_sign_request(self, data):
        pk = data['signature_request']['metadata'].get('request_pk')
        external_id = data['signature_request']['signature_request_id']
        all_signed = data['event']['event_type'] == "signature_request_all_signed"

        obj = SignRequest.objects.filter(pk=pk).first()

        if self._is_sign_request_event_processable(data, obj):
            obj.signed_hellosign(all_signed=all_signed, external_id=external_id)

    def handle_form(self, data):
        pk = data['signature_request']['metadata'].get('form_pk')
        from documents.models import Document

        obj = Document.objects.filter(pk=pk).first()
        if obj is not None:
            from integrations.tasks import download_hellosign_form

            download_hellosign_form.delay(obj.pk, data['signature_request']['signature_request_id'])

    def post(self, request, *args, **kwargs):
        data = request.data
        # TODO: check secret value from our integrations proxy?
        try:
            if 'json' in data:  # raw callback, not sent through integrations proxy
                data = json.loads(data['json'])
            logger.info('hellosign callback', extra=dict(_data=data))

            event = data['event']
            if event['event_type'] in self.event_types:
                request_type = data['signature_request']['metadata'].get('request_type')
                if request_type == "document":
                    self.handle_sign_request(data)
                elif request_type == "form":
                    self.handle_form(data)
                else:
                    raise Exception('Missing metadata')
        except Exception:
            logging.exception(f"Hellosign error: {data}")
            return Response(status=status.HTTP_400_BAD_REQUEST)
        return Response(status=status.HTTP_200_OK, content_type="text/plain", data="Hello API Event Received")

    def _is_sign_request_event_processable(self, data: dict, sign_request: SignRequest | None) -> bool:
        if sign_request is None:
            return False

        # We should not process sign request events for users who requested / creates given Agreement without Template (ad-hoc Agreements).
        # We don't treat such users as signers in the Platform, thus we should skip those events and don't trigger send_to_next_signer
        if sign_request.template_id is None:
            latest_signer = self._get_latest_signed_signer_email(data)
            return latest_signer != sign_request.requested_by.email

        return True

    @staticmethod
    def _get_latest_signed_signer_email(data: dict) -> str | None:
        signatures = data['signature_request']['signatures']

        if data['event']['event_type'] != 'signature_request_signed' or not signatures:
            return None

        for signature in reversed(signatures):
            if signature['status_code'] == 'signed':
                return signature['signer_email_address']

        return None


class SocialLogin(ViewSet):
    permission_classes = []

    def login_options(self, request, *args, **kwargs):
        redirect_to = request.query_params.get('next')
        sso = []
        for saml in SAML.objects.filter(enabled=True):
            sso.append(
                {
                    'name': saml.provider_name,
                    'url': saml.get_login_url(redirect_to=redirect_to),
                    'icon': saml.get_icon(),
                    'icon_hover': saml.get_icon_hover(),
                }
            )

        response_params = {
            'password': connection.tenant.enable_standard_login,
            'password_for_buyers': connection.tenant.has_features(features.ENABLE_STANDARD_LOGIN_FOR_BUYERS),
            'sso': sso,
        }

        if connection.tenant.has_features(features.ENABLE_GOOGLE_OAUTH_LOGIN):
            response_params['google_oauth_url'] = '{}public.{}/signin/google_auth/?next={}'.format(
                settings.PROTOCOL_FOR_LINKS, settings.PUBLIC_SCHEMA_DOMAIN, (redirect_to or '')
            )

        if connection.tenant.has_features(features.ENABLE_GOOGLE_OAUTH_LOGIN_BUYERS):
            response_params['enable_google_oauth_for_buyers'] = True

        return Response(response_params)


class ReplytoView(APIView):
    def check_signature(self, body, given_signature):
        signature = hmac.new(force_bytes(settings.EMAIL_REPLYTO_KEY), force_bytes(body), digestmod=sha256).hexdigest()
        return hmac.compare_digest(signature, given_signature)

    def post(self, request, *args, **kwargs):
        signature = request.META['HTTP_X_REPLYTO_SIGNATURE']
        if not self.check_signature(request.body, signature):
            return Response(status=status.HTTP_400_BAD_REQUEST)

        event = request.data['event']
        email = request.data['email']
        try:
            reply = EmailReplyParser.parse_reply(email['text'])
            email_cls_name = event['email_class']
            email_cls = all_classes[email_cls_name]
            user = Actor.objects.for_any_email(email['envelope']['from']).user

            if user and email_cls.with_reply:
                with as_user(user):
                    email_cls.reply_callback(reply, event)
            else:
                logger.warning("Reply to took no action, event %s", event)
        except Exception:
            # log but confirm so webhook service does not retry
            logger.exception("Reply to error, event %s", event)
        return Response(status=status.HTTP_200_OK)


class SisenseView(ViewSet):
    """Embedded anaylytics configuration"""

    permission_classes = [Permission.insights()]

    def config(self, request, *args, **kwargs):
        sisense = connection.tenant.cache.sisense
        try:
            dashboards = sisense.get_dashboards() if sisense.enabled else []
        except SisenseUserMappingError:
            dashboards = []
        except Exception as e:
            sentry_sdk.capture_exception(e)
            dashboards = []
        return Response(
            {
                'sisense': {
                    'enabled': sisense.enabled,
                    'dashboards': dashboards,
                }
            }
        )

    def embed(self, request, *args, **kwargs):
        sisense = connection.tenant.cache.sisense
        if not sisense.enabled:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        dashboard_oid = kwargs.get('dashboard') or None
        if dashboard_oid:
            dashboard_oid = dashboard_oid.rstrip('/')
        try:
            url = sisense.get_embed_url(dashboard_oid)
        except SisenseUserMappingError:
            return Response(status=status.HTTP_403_FORBIDDEN)
        except Exception as e:
            sentry_sdk.capture_exception(e)
            return Response(status=status.HTTP_400_BAD_REQUEST)
        return HttpResponseRedirect(url)


class ExcelGenerationView(APIView):
    """Excel files generated from templates, based on vendor data"""

    def get(self, request, *args, **kwargs):
        try:
            vendor_id = int(kwargs['vendor_id'])
            template_id = int(kwargs['template_id'])
            template = ExcelTemplate.objects.get(id=template_id)
            vendor = Vendor.objects.get(id=vendor_id)
        except Exception:
            logger.exception('Invalid parameters for Excel generation view: %s', kwargs)
            return Response(status=status.HTTP_400_BAD_REQUEST)
        if not Permission.buyer('vendor', 'view').has_object_permission(request=request, view=self, obj=vendor):
            return Response(status=status.HTTP_403_FORBIDDEN)
        content, file_name = template.get_file(vendor, None)
        return serve_file_contents(
            file_name, content, content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', inline=True
        )


class CheckrCallback(APIView):
    """Checkr status update callback"""

    def post(self, request, vendor_id, package_id, *args, **kwargs):
        key = request.data.get('key')
        if key != settings.CHECKR_CALLBACK_KEY:
            return Response(status=status.HTTP_403_FORBIDDEN)
        checkr = CheckrIntegration.get_enabled()
        if checkr:
            report_id = request.data.get('report_id')
            if package_id == 'continuous_monitoring':
                CheckrContinuousCheckWebHook(vendor_id=vendor_id, package_id=package_id)
            elif report_id:
                from .tasks import update_checkr_report

                update_checkr_report.delay(vendor_id, package_id, report_id)
            return Response()
        return Response(status=status.HTTP_400_BAD_REQUEST)


class SendgridUnsubscribe(APIView):
    def post(self, request, token, *args, **kwargs):
        if not SendGridMailBackend.is_custom_unsubscribe_enabled():
            return JsonResponse({"error": "Operation not allowed"}, status=status.HTTP_403_FORBIDDEN)

        client = SendgridUnsubscribeClient()
        try:
            domain, email = client.decrypt_token(token)
        except SendgridUnsubscribeInvalidToken:
            return JsonResponse({"error": "Given unsubscribe token is invalid"}, status=status.HTTP_400_BAD_REQUEST)

        user = User.objects.filter(email=email).first()
        if not user:
            return JsonResponse({"error": "Given unsubscribe token is invalid"}, status=status.HTTP_400_BAD_REQUEST)

        unsubscribed = client.unsubscribe(email)
        if unsubscribed:
            restore = UserRestoreSubscription.objects.filter(user=user)
            if not restore:
                UserRestoreSubscription.objects.create(user=user)
                UserUnsubscribed(user)

        return Response(status=status.HTTP_202_ACCEPTED)


# This webhook event is fired when "Subscription Tracking" is enabled only
class SendgridUnsubscribed(APIView):
    def post(self, request, *args, **kwargs):
        token = request.META.get('HTTP_X_TOKEN')
        if not settings.SENDGRID_WEBHOOK_TOKEN or token != settings.SENDGRID_WEBHOOK_TOKEN:
            return Response(status=status.HTTP_403_FORBIDDEN)
        user_email = request.data.get('email')

        user = User.objects.filter(email=user_email).first()
        if user:
            restore = UserRestoreSubscription.objects.filter(user=user)
            if not restore:
                UserRestoreSubscription.objects.create(user=user)
                UserUnsubscribed(user)
        return Response(status=status.HTTP_200_OK)


class PaycassoEnrolmentView(APIView):
    permission_classes = [Permission.vendor('vendor', 'view')]

    def get(self, request):
        user = request.user
        paycasso_integration = PaycassoService.get_enabled_integration()
        enrolment = PaycassoService.get_enrolment(user)
        if not enrolment:
            return Response(status=status.HTTP_404_NOT_FOUND)

        previous_status = enrolment.status
        new_status = paycasso_integration.service.check_and_update_enrolment_status(enrolment)
        if new_status != previous_status:
            recalculate_onboarding_completeness.delay([user.vendor_id], source=['PaycassoEnrolmentView_get'])

        return JsonResponse({'status': new_status})

    def delete(self, request):
        user = request.user
        enrolment = PaycassoService.get_enrolment(user)
        if not enrolment:
            return Response(status=status.HTTP_404_NOT_FOUND)

        enrolment.delete()
        recalculate_onboarding_completeness.delay([user.vendor_id], source=['PaycassoEnrolmentView_delete'])
        return Response(status=status.HTTP_204_NO_CONTENT)

    def post(self, request):
        user = request.user
        paycasso_integration = PaycassoService.get_enabled_integration()
        if not PaycassoEnrolment.objects.filter(user=user).exists():
            try:
                paycasso_integration.service.enrol(user)
            except PaycassoEnrolmentError as error:
                return JsonResponse({'error': str(error)}, status=status.HTTP_400_BAD_REQUEST)
            recalculate_onboarding_completeness.delay([user.vendor_id], source=['PaycassoEnrolmentView_post'])
            return JsonResponse({}, status=status.HTTP_201_CREATED)
        return JsonResponse({'error': 'Enrolment already exists.'}, status=status.HTTP_400_BAD_REQUEST)


class PaycassoEnrolmentVerificationView(APIView):
    permission_classes = [Permission.vendor('vendor', 'view')]

    def get(self, request):
        user = request.user
        enrolment = PaycassoService.get_enrolment(user)
        if not enrolment or enrolment.status == PaycassoEnrolment.PENDING:
            return JsonResponse({'error': 'User is not enrolled.'}, status=status.HTTP_400_BAD_REQUEST)

        verification = enrolment.verification
        if not verification:
            return JsonResponse({'error': 'Enrolment verification is not started.'}, status=status.HTTP_400_BAD_REQUEST)

        return JsonResponse({'enrolment_status': enrolment.status, 'verification_status': verification.status})

    def post(self, request):
        user = request.user
        paycasso_integration = PaycassoService.get_enabled_integration()
        if not PaycassoService.has_not_verified_enrolment(user):
            return JsonResponse({'error': 'User is not enrolled.'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            paycasso_integration.service.verify(user, is_enrolment_verification=True)
        except PaycassoVerificationError as error:
            return JsonResponse({'error': str(error)}, status=status.HTTP_400_BAD_REQUEST)

        return JsonResponse({}, status=status.HTTP_201_CREATED)


class PaycassoVerificationCallback(APIView):
    authentication_classes = []
    permission_classes = []

    def post(self, request, *args, **kwargs):
        auth = request.META.get('HTTP_AUTHORIZATION', None)
        if auth is None:
            return Response(status=status.HTTP_403_FORBIDDEN)

        integration = PaycassoService.get_enabled_integration()
        if integration is None:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        expected_auth = f'Basic {base64.b64encode(force_bytes(integration.callback_authorization)).decode("utf-8")}'
        if auth != expected_auth:
            return Response(status=status.HTTP_403_FORBIDDEN)

        transaction_id = request.data.get('transactionId')
        transaction_ref = request.data.get('transactionRef')
        transaction_status = request.data.get('transactionStatus')

        if transaction_id is None or transaction_ref is None:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        verification = PaycassoService.get_verification(transaction_ref)
        if verification is None:
            return Response(status=status.HTTP_404_NOT_FOUND)
        user = verification.user

        if transaction_status == 'Completed':
            if integration.service.is_authenticated(user, transaction_id):
                verification.status = PaycassoVerification.COMPLETED
            else:
                verification.status = PaycassoVerification.FAILED
            verification.save(update_fields=['status'])

        return Response('acknowledgement', status=status.HTTP_200_OK)


class PeroptyxResourceView(APIView):
    permission_classes = [IsUser]

    def get(self, request, *args, **kwargs):
        resource_name = kwargs.get('resource_name')
        s3 = boto3_resource('s3')

        key_path = f'peroptyx/resources/{resource_name}'
        obj = s3.Object(settings.AWS_STORAGE_BUCKET_NAME, key_path)
        try:
            obj.load()
        except ClientError as e:
            if e.response['Error']['Code'] == "404":
                return Response(status=status.HTTP_404_NOT_FOUND)
            else:
                raise
        return serve_presigned_url(None, settings.AWS_STORAGE_BUCKET_NAME, key_path, content_type=obj.content_type, inline=True)


class PaycassoEventViewForVendor(APIView):
    permission_classes = []

    def post(self, request):
        vendor = None
        if request.user.is_authenticated:
            if request.user.is_vendor:
                vendor = request.user.vendor
        elif request.user.is_anonymous:
            transaction_ref = request.data.get('transaction_ref')
            if transaction_ref:
                verification = PaycassoService.get_verification(transaction_ref)
                if verification:
                    vendor = verification.user.vendor

        if not vendor:
            return Response(status=status.HTTP_403_FORBIDDEN)

        event_name = request.data.get('event_name')
        PaycassoLogger.log_event(event_name, vendor)
        return Response(status=status.HTTP_201_CREATED)


class FlatfileView(ViewSet):
    permission_classes = [IsUser]

    def check_permissions(self, request):
        super().check_permissions(request)
        user = get_current_user()
        module = self.kwargs.get("module", None)
        try:
            flatfile = Flatfile(module, user)
        except FlatfileModuleNotSupportedException as e:
            raise exceptions.ParseError(e.message)
        if not flatfile.has_permissions():
            raise exceptions.PermissionDenied(detail="Permission denied.")

    def get_config(self, request, module=None, *args, **kwargs):
        user = get_current_user()
        try:
            flatfile = Flatfile(module, user)
            context = Flatfile.get_context_from_request(module, request)
            fields = flatfile.get_fields_config(context)
        except FlatfileInvalidContextException as e:
            return JsonResponse({'error': e.message}, status=status.HTTP_400_BAD_REQUEST)

        return JsonResponse(
            {
                'licenseKey': settings.FLATFILE_LICENSE_KEY,
                'customer': {
                    'userId': user.pk,
                    'name': user.full_name,
                    'email': user.email,
                    'companyName': connection.tenant.name,
                    'companyId': connection.tenant.pk,
                },
                'settings': {
                    'type': flatfile.handler_type,
                    'title': flatfile.title,
                    'fields': fields,
                    'allowCustom': False,
                    'managed': True,
                    'devMode': settings.FLATFILE_DEV_MODE,
                },
            },
            safe=False,
            json_dumps_params={'ensure_ascii': False},
        )

    def import_data(self, request, module, *args, **kwargs):
        user = get_current_user()
        rows = request.data

        context = Flatfile.get_context_from_request(module, request)

        if not isinstance(rows, dict):
            return Response({"detail": "Invalid import data. Expected dictionary with rows data."}, status=status.HTTP_400_BAD_REQUEST)

        if rows:
            idempotency_helper = BulkImportIdempotencyHelper(f'flatfile_{module}', rows, user.pk)

            if idempotency_helper.is_data_already_submitted():
                return Response(
                    {"detail": "Import with the same data was already submitted.", "data_imported_already_error": True},
                    status=status.HTTP_409_CONFLICT,
                )

            if "validate_only" in request.GET:
                validation_errors = Flatfile(module, user).validate(rows, **context)
                if validation_errors:
                    return Response(validation_errors, status=status.HTTP_400_BAD_REQUEST)
                return Response(status=status.HTTP_204_NO_CONTENT)
            else:
                idempotency_helper.create_idempotency_key()

                if settings.FLATFILE_PROCESS_ASYNCHRONOUSLY:
                    start_task_tracking()
                    flatfile_bulk_import.delay(module, user.pk, rows, **context)
                else:
                    flatfile_bulk_import(module, user.pk, rows, **context)
                    return Response({"status": "OK"}, status=status.HTTP_200_OK)

        return Response({"status": "Accepted"}, status=status.HTTP_202_ACCEPTED)


class VeriffVerificationView(APIView):
    permission_classes = [Permission.vendor('vendor', 'view')]

    def get(self, request):
        veriff_type = self.request.query_params.get('veriff_type')
        service = self.get_service(veriff_type=veriff_type)
        if not service:
            return Response({'detail': 'Veriff integration not available for given document type'}, status=status.HTTP_403_FORBIDDEN)

        veriff_verification = service.get_usable_verification(vendor=request.user.vendor)
        if not veriff_verification:
            return Response(status=status.HTTP_404_NOT_FOUND)

        return JsonResponse(
            {
                'status': veriff_verification.status,
                'verification_id': veriff_verification.verification_id,
                'expired_at': veriff_verification.expired_at,
                'verification_url': veriff_verification.verification_url,
                'veriff_type': veriff_verification.veriff_type,
            }
        )

    def post(self, request):
        veriff_type = self.request.data.get('veriff_type')
        service = self.get_service(veriff_type=veriff_type)
        if not service:
            return Response({'detail': 'Veriff integration not available'}, status=status.HTTP_403_FORBIDDEN)

        if not service.get_usable_verification(vendor=request.user.vendor):
            try:
                veriff_verification = service.create_verification(vendor=request.user.vendor)
            except VeriffError as error:
                return JsonResponse({'error': str(error)}, status=status.HTTP_400_BAD_REQUEST)

            recalculate_onboarding_completeness.delay([request.user.vendor_id], source=['VeriffVerificationView_post'])
            return JsonResponse(
                {
                    'status': veriff_verification.status,
                    'verification_id': veriff_verification.verification_id,
                    'expired_at': veriff_verification.expired_at,
                    'verification_url': veriff_verification.verification_url,
                    'veriff_type': veriff_verification.veriff_type,
                },
                status=status.HTTP_201_CREATED,
            )

        return JsonResponse({'error': 'Veriff Session already exists.'}, status=status.HTTP_400_BAD_REQUEST)

    @staticmethod
    def get_service(veriff_type):
        service = None
        veriff_integration = VeriffService.get_enabled_integration()
        if veriff_integration:
            if not veriff_type:
                veriff_type = None

            try:
                service = veriff_integration.get_service(veriff_type=veriff_type)
            except ImproperlyConfigured as exc:
                sentry_sdk.capture_exception(exc)

        return service


class VeriffCallbackPermission(permissions.BasePermission):
    def has_permission(self, request, view):
        signature = request.META.get('HTTP_X_HMAC_SIGNATURE')
        api_public_key = request.META.get('HTTP_X_AUTH_CLIENT')
        veriff_integration_type = VeriffService.get_integration_type_by_api_key(api_public_key)
        # request.data is touched for set request.raw_body
        return (
            veriff_integration_type
            and request.data
            and veriff_integration_type.service.is_enabled()
            and veriff_integration_type.service.check_signature(signature, request.raw_body)
        )


class RawBodyJSONParser(parsers.BaseParser):
    media_type = 'application/json'
    renderer_class = renderers.JSONRenderer

    # to use the request.raw_body in the permission class, first touch request.data to run this parser
    def parse(self, stream, media_type=None, parser_context=None):
        parser_context = parser_context or {}
        encoding = parser_context.get('encoding', settings.DEFAULT_CHARSET)
        request = parser_context.get('request')
        try:
            data = stream.read().decode(encoding)
            request.raw_body = data
            return json.loads(data)
        except ValueError as exc:
            raise parsers.ParseError(f'JSON parse error - {exc}')


class VeriffCallbackMixin:
    authentication_classes = []
    permission_classes = [
        VeriffCallbackPermission,
    ]
    parser_classes = [
        RawBodyJSONParser,
    ]


class VeriffDecisionCallback(VeriffCallbackMixin, APIView):
    def post(self, request, *args, **kwargs):
        if not VeriffService.is_enabled():
            return Response(status=status.HTTP_404_NOT_FOUND)

        verification_data = request.data.get('verification')
        if not verification_data:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        verification_id = verification_data.get('id')
        verification_status = verification_data.get('status')
        verification = VeriffVerification.objects.filter(verification_id=verification_id).first()
        if verification is None:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        if verification.set_status_and_save(verification_status):
            recalculate_onboarding_completeness.delay([verification.vendor_id], source=['VeriffDecisionCallback'])
            return Response(status=status.HTTP_200_OK)

        return Response(status=status.HTTP_400_BAD_REQUEST)


class VeriffEventCallback(VeriffCallbackMixin, APIView):
    def post(self, request, *args, **kwargs):
        if not VeriffService.is_enabled():
            return Response(status=status.HTTP_404_NOT_FOUND)

        verification_id = request.data.get('id')
        verification_status = request.data.get('action')
        verification = VeriffVerification.objects.filter(verification_id=verification_id).first()
        if verification is None:
            return Response(status=status.HTTP_404_NOT_FOUND)

        if verification.set_status_and_save(verification_status):
            recalculate_onboarding_completeness.delay([verification.vendor_id], source=['VeriffEventCallback'])
            return Response(status=status.HTTP_200_OK)

        return Response(status=status.HTTP_400_BAD_REQUEST)


def assert_1099p_integration_enabled():
    if not Ten99PolicyConfig.get_enabled():
        raise BadRequest("Ten99P integration is not configured properly")


class Ten99PVendorView(ViewSet):
    permission_classes = [Permission.vendor('vendor', 'view')]

    def _get_vendor_stage_and_validate_access(self, request, kwargs):
        assert_1099p_integration_enabled()
        vendor_stage = get_object_or_404(
            OnBoardingStagesForVendor,
            pk=kwargs.get('vendor_stage_id', None),
        )
        if not request.user.is_vendor or vendor_stage.vendor != request.user.vendor:
            raise PermissionDenied('Only stage vendor has access to this data')
        return vendor_stage

    def onboarding_stage_prevalidation(self, request, *args, **kwargs):
        vendor_stage = self._get_vendor_stage_and_validate_access(request, kwargs)
        data_provider: Ten99PDataProvider = get_data_provider(vendor_stage)
        with Ten99PExceptionHandler(vendor_stage.context.name):
            prevalidation_response = prevalidate(data_provider)
            return JsonResponse(prevalidation_response.model_dump())

    def onboarding_stage_quote(self, request, *args, **kwargs):
        vendor_stage = self._get_vendor_stage_and_validate_access(request, kwargs)
        data_provider: Ten99PDataProvider = get_data_provider(vendor_stage)
        with Ten99PExceptionHandler(vendor_stage.context.name):
            quote_response = generate_quote(data_provider)
            return JsonResponse(quote_response.model_dump())

    def onboarding_stage_save_decision(self, request, *args, **kwargs):
        vendor_stage = self._get_vendor_stage_and_validate_access(request, kwargs)
        data_provider: Ten99PDataProvider = get_data_provider(vendor_stage)
        with Ten99PExceptionHandler(vendor_stage.context.name):
            insurance_decision = InsuranceDecisionData(data=self.request.data)
            current_domain = request.get_host()
            insurance_decision_response = save_insurance_decision(data_provider, insurance_decision, current_domain)
            if insurance_decision_response:
                return JsonResponse(insurance_decision_response.model_dump())
            else:
                return Response(status=status.HTTP_204_NO_CONTENT)

    def continue_on_ten99policy(self, request, *args, **kwargs):
        vendor_stage = self._get_vendor_stage_and_validate_access(request, kwargs)
        data_provider: Ten99PDataProvider = get_data_provider(vendor_stage)
        with Ten99PExceptionHandler(vendor_stage.context.name):
            current_domain = request.get_host()
            continue_on_ten99policy(data_provider, current_domain)
            return Response(status=status.HTTP_204_NO_CONTENT)

    def handle_return_from_ten99policy(self, request, *args, **kwargs):
        vendor_stage = self._get_vendor_stage_and_validate_access(request, kwargs)
        data_provider: Ten99PDataProvider = get_data_provider(vendor_stage)
        with Ten99PExceptionHandler(vendor_stage.context.name):
            return_token = kwargs.get('return_token')
            result = kwargs.get('result')
            if not return_token or not result:
                raise ValidationError('Incorrect url parameters')
            handle_return_from_ten99policy(data_provider, return_token, result)
            current_domain = request.get_host()
            url = f"{settings.PROTOCOL_FOR_LINKS}{current_domain}{data_provider.get_redirect_urls().vendor_return_url}"
            return HttpResponseRedirect(url)

    def get_upload_file_config(self, request):
        upload_path = Ten99PolicyCoverage.get_upload_path()
        return Response(filepicker_policy(upload_path))


class Ten99PBuyerView(ViewSet):
    permission_classes = [IsBuyer]

    def _get_vendor_stage(self, kwargs):
        assert_1099p_integration_enabled()
        return get_object_or_404(
            OnBoardingStagesForVendor,
            pk=kwargs.get('vendor_stage_id', None),
        )

    def config(self, request, *args, **kwargs):
        ten99_config = get_ten99_config()
        return JsonResponse(ten99_config.model_dump())

    def onboarding_stage_get_details(self, request, *args, **kwargs):
        vendor_stage = self._get_vendor_stage(kwargs)
        data_provider: Ten99PDataProvider = get_data_provider(vendor_stage)
        with Ten99PExceptionHandler(vendor_stage.context.name):
            details_response = onboarding_stage_get_details(data_provider)
            return JsonResponse(details_response.model_dump())

    def onboarding_stage_save_decision(self, request, *args, **kwargs):
        vendor_stage = self._get_vendor_stage(kwargs)
        data_provider: Ten99PDataProvider = get_data_provider(vendor_stage)
        with Ten99PExceptionHandler(vendor_stage.context.name):
            buyer_decision = BuyerDecisionData(data=self.request.data)
            buyer_decision_response = onboarding_stage_save_decision(data_provider, buyer_decision)
            return JsonResponse(buyer_decision_response.model_dump())

    def onboarding_stage_allow_retry(self, request, *args, **kwargs):
        vendor_stage = self._get_vendor_stage(kwargs)
        data_provider: Ten99PDataProvider = get_data_provider(vendor_stage)
        with Ten99PExceptionHandler(vendor_stage.context.name):
            allow_retry_response = onboarding_stage_allow_retry(data_provider)
            return JsonResponse(allow_retry_response.model_dump())


class Ten99PCallbackPermission(permissions.BasePermission):
    def has_permission(self, request, view):
        signature = request.headers.get('X-Convoy-Signature')
        timestamp = request.headers.get('Convoy-Timestamp')

        config = Ten99PolicyConfig.get_enabled()

        try:
            # accessing request.data will trigger the RawBodyJSONParser to run and set raw_body on request
            return config and request.data and verify_webhook_signature(request.raw_body, timestamp, signature, config.webhook_secret)
        except RuntimeError:
            sentry_sdk.capture_exception()
            return False


class Ten99PCallback(APIView):
    authentication_classes = []
    permission_classes = [Ten99PCallbackPermission]
    parser_classes = [RawBodyJSONParser]

    @atomic()
    def post(self, request, *args, **kwargs):
        try:
            handle_webhook_request(request.data)
        except Ten99PWebhookException:
            sentry_sdk.capture_exception()
        return Response(status=status.HTTP_204_NO_CONTENT)


class TaxBanditsCallbackPermission(permissions.BasePermission):
    def has_permission(self, request: Request, view) -> bool:
        signature = request.headers.get("signature", None)
        timestamp = request.headers.get("timestamp", None)
        try:
            return verify_signature(
                client_id=settings.TAXBANDITS_CLIENT_ID,
                client_secret=settings.TAXBANDITS_CLIENT_SECRET,
                timestamp=timestamp,
                signature=signature,
            )
        except SignatureError:
            sentry_sdk.capture_exception()
            return False


class TaxBanditsCallback(APIView):
    authentication_classes = []
    permission_classes = [TenantFeaturesEnabled(features.ENABLE_1099_TAX_FILING), TaxBanditsCallbackPermission]

    @atomic
    def post(self, request: Request, *args, **kwargs):
        try:
            handle_e_file_status_change_webhook_request(request.data)
        except TaxBanditsException:
            sentry_sdk.capture_exception()
            return Response(status=status.HTTP_400_BAD_REQUEST)

        return Response(status=status.HTTP_204_NO_CONTENT)
