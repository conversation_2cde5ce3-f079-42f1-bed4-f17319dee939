import hashlib
import logging

import requests
from django.conf import settings
from django.utils.encoding import force_bytes

from shortlist.celery import app
from . import path_to_cert

logger = logging.getLogger()

CERTFILE = path_to_cert('api.mailchimp.com')


def mailchimp_request(method, address, **kwargs):
    subscriber_hash = hashlib.md5(force_bytes(address)).hexdigest()
    url = f"{settings.MAILCHIMP_API_ADDRESS}/{subscriber_hash}"
    kwargs.setdefault('headers', {})["Authorization"] = f"apikey {settings.MAILCHIMP_API_KEY}"
    # Mailchimp won't pass verification when using current requests/urllib3, we have to switch SSL verification off :(
    # r = requests.request(method, url, verify=CERTFILE, **kwargs)
    r = requests.request(method, url, verify=False, **kwargs)
    return r


@app.task
def add_to_newsletter(email_info):
    data = {
        "email_address": email_info.address,
        "status": "subscribed",
        "merge_fields": {
            "FNAME": email_info.first_name or "",
            "LNAME": email_info.last_name or "",
            "MMERGE3": email_info.company or "",
            "MMERGE6": email_info.title or "",
        },
    }
    try:
        r = mailchimp_request("PUT", email_info.address, json=data)

        if r.status_code != 200:
            print(f"MAILCHIMP: Could not subscribe {email_info.address}: received {r.status_code}\n:{r.text}")
    except Exception:
        logger.exception('Could not change mailchimp subcription status for email %(email)s', email=email_info.address)


@app.task
def remove_from_newsletter(email_info):
    r = mailchimp_request("DELETE", email_info.address)
    if r.status_code not in (204, 404):
        print(f"MAILCHIMP: Could not unsubscribe {email_info.address}: received {r.status_code}\n:{r.text}")
