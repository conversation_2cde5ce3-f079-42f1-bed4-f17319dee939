from datetime import date, timed<PERSON><PERSON>
from decimal import Decimal

from pydantic import BaseModel
from ten99policy import Quotes
from ten99policy.error import Ten99PolicyError
from django.db import transaction

from integrations.models import (
    Ten99PolicyCoverageType,
    Ten99PolicyContractor,
    Ten99PolicyEntity,
    Ten99PolicyJob,
    Ten99PolicySession,
    Ten99PolicyQuote,
    Ten99PolicyRequest,
    Ten99PolicyAssignment,
    Ten99PolicyInvoice,
)
from integrations.services.ten99policy.api import Ten99PApi
from integrations.services.ten99policy.exceptions import (
    ConfigurationError,
    ContractorNotEligibleForAssignment,
    AssigmentRateFetchError,
)
from vendors.models import Vendor


class T99PJobMetadata(BaseModel):
    purchase_order_contact_name: str | None
    purchase_order_contact_email: str | None


def date_to_timestamp(date_: date) -> int:
    return int((date_ - date(1970, 1, 1)).total_seconds())


def get_earliest_start_date_for_coverage(start_date: date) -> date:
    return max(start_date, date.today() + timedelta(days=1))


COVERAGE_TYPE_MAPPING = {
    Ten99PolicyCoverageType.workers_compensation: "workers-comp",
    Ten99PolicyCoverageType.general_liability: "general",
}


class Ten99PApiService:
    def __init__(self, job_category_codes_mapping: dict, api: Ten99PApi):
        self.job_category_codes_mapping = job_category_codes_mapping
        self.api = api

    def find_contractor_by_vendor_email(self, vendor: Vendor) -> Ten99PolicyContractor | None:
        t99p_contractors = self.api.list_contractors(email=vendor.email)
        if not t99p_contractors:
            return None

        contractor: Ten99PolicyContractor = Ten99PolicyContractor.objects.filter(vendor_id=vendor.id).first()
        if contractor:
            contractor.t99p_contractor_id = t99p_contractors[0].id
            contractor.save()
        else:
            contractor: Ten99PolicyContractor = Ten99PolicyContractor.objects.create(vendor_id=vendor.id, t99p_id=t99p_contractors[0].id)
        return contractor

    def create_contractor(
        self,
        vendor_id: int,
        company_name: str,
        first_name: str,
        middle_name: str,
        last_name: str,
        email: str,
        phone_number: str,
        address: str,
        city: str,
        postal_code: str,
        state: str,
        tax_identification: str,
    ) -> Ten99PolicyContractor:
        contractor_data = {
            "company_name": company_name,
            "first_name": first_name,
            "middle_name": middle_name,
            "last_name": last_name,
            "email": email,
            "phone": phone_number,
            "address": {
                "line1": address,
                "line2": "",
                "locality": city,
                "postalcode": postal_code,
                "region": state,
                "country": "USA",
            },
            "tax_identification": tax_identification,
        }

        t99p_contractor = self.api.create_contractor(**contractor_data)

        contractor: Ten99PolicyContractor = Ten99PolicyContractor.objects.create(
            vendor_id=vendor_id,
            t99p_id=t99p_contractor.id,
        )
        return contractor

    def update_contractor(
        self,
        contractor: Ten99PolicyContractor,
        company_name: str,
        first_name: str,
        middle_name: str,
        last_name: str,
        email: str,
        phone_number: str,
        address: str,
        city: str,
        postal_code: str,
        state: str,
        tax_identification: str,
    ) -> Ten99PolicyContractor:
        contractor_data = {
            "company_name": company_name,
            "first_name": first_name,
            "middle_name": middle_name,
            "last_name": last_name,
            "email": email,
            "phone": phone_number,
            "address": {
                "line1": address,
                "line2": "",
                "locality": city,
                "postalcode": postal_code,
                "region": state,
                "country": "USA",
            },
            "tax_identification": tax_identification,
        }
        self.api.update_contractor(contractor.pk, **contractor_data)
        return contractor

    def create_entity(self, entity_name: str) -> Ten99PolicyEntity:
        t99p_entity = self.api.create_entity(name=entity_name)
        entity: Ten99PolicyEntity = Ten99PolicyEntity.objects.create(
            entity_name=entity_name,
            t99p_id=t99p_entity.id,
        )
        return entity

    def create_job(
        self,
        request: Ten99PolicyRequest,
        entity: Ten99PolicyEntity,
        name: str,
        description: str,
        wage_total: int,
        wage_type: str,
        category_name: str,
        state: str,
        custom_metadata: T99PJobMetadata,
    ) -> Ten99PolicyJob:
        category_code = self.job_category_codes_mapping.get(category_name, None)
        if not category_code:
            # Raise an error if the job category code is missing, ensuring data integrity
            raise ConfigurationError(f"Can't find a job category id for custom field value {category_name}")

        job_data = {
            "category_code": category_code,
            "description": description,
            "entity": entity.t99p_id,
            "name": name,
            "wage": wage_total,
            "wage_type": wage_type,
            "address": {"region": state, "country": "US"},
            "custom_metadata": custom_metadata.model_dump(exclude_none=True),
        }
        t99p_job = self.api.create_job(request_pk=request.pk, **job_data)

        job: Ten99PolicyJob = Ten99PolicyJob.objects.create(
            t99p_id=t99p_job.id,
            entity=entity,
            state=state,
            category_code=category_code,
            request=request,
        )
        return job

    def update_job(
        self,
        job: Ten99PolicyJob,
        entity: Ten99PolicyEntity,
        name: str,
        description: str,
        wage_total: int,
        wage_type: str,
        category_name: str,
        state: str,
    ) -> Ten99PolicyJob:
        category_code = self.job_category_codes_mapping.get(category_name, None)
        if not category_code:
            # Ensure the job category code is valid to maintain data integrity
            raise ConfigurationError(f"Can't find a job category id for custom field value {category_name}")

        job_data = {
            "category_code": category_code,
            "description": description,
            "entity": entity.t99p_id,
            "name": name,
            "wage": wage_total,
            "wage_type": wage_type,
            "address": {"region": state, "country": "US"},
        }
        self.api.update_job(job.pk, **job_data)

        job.entity = entity
        job.state = state
        job.category_code = category_code
        job.save()

        return job

    def create_quote(
        self,
        request: Ten99PolicyRequest,
        contractor: Ten99PolicyContractor,
        job: Ten99PolicyJob,
        start_date: date,
        end_date: date,
        coverage_types: list[Ten99PolicyCoverageType],
    ) -> Ten99PolicyQuote:
        effective_date = get_earliest_start_date_for_coverage(start_date)
        coverage_types = [COVERAGE_TYPE_MAPPING[ct] for ct in coverage_types]

        quote_data = {
            "coverage_type": coverage_types,
            "contractor": contractor.t99p_id,
            "job": job.t99p_id,
            "effective_date": date_to_timestamp(effective_date),
            "end_date": date_to_timestamp(end_date),
        }
        t99p_quote = self.api.create_quote(**quote_data)

        Ten99PolicyQuote.objects.filter(job=job).delete()

        gl_net_rate, wc_net_rate, gl_service_fee, wc_service_fee = self.extract_rates_from_t99p_quote(t99p_quote)

        quote: Ten99PolicyQuote = Ten99PolicyQuote.objects.create(
            t99p_id=t99p_quote.id,
            job=job,
            contractor=contractor,
            effective_date=effective_date,
            end_date=end_date,
            eligible=t99p_quote.eligible,
            gl_net_rate=t99p_quote.gl_net_rate,
            wc_net_rate=t99p_quote.wc_net_rate,
            gl_service_fee=gl_service_fee,
            wc_service_fee=wc_service_fee,
            request=request,
        )
        return quote

    def create_session(
        self,
        request: Ten99PolicyRequest,
        quote: Ten99PolicyQuote,
        success_url: str,
        cancel_url: str,
    ) -> Ten99PolicySession:
        session_data = {
            "quote": quote.t99p_id,
            "success_url": success_url,
            "cancel_url": cancel_url,
        }
        t99p_session = self.api.create_session(**session_data)
        session: Ten99PolicySession = Ten99PolicySession.objects.create(
            t99p_id=t99p_session.id,
            quote=quote,
            onboarding_url=t99p_session.url,
            request=request,
        )
        return session

    def update_session(
        self,
        session: Ten99PolicySession,
        success_url: str,
        cancel_url: str,
    ) -> Ten99PolicySession:
        session_data = {
            "success_url": success_url,
            "cancel_url": cancel_url,
        }
        self.api.update_session(session.t99p_id, **session_data)
        return session

    def expire_session(self, session: Ten99PolicySession):
        self.api.expire_session(session.t99p_id)

    def create_assignment(
        self,
        contractor: Ten99PolicyContractor,
        job: Ten99PolicyJob,
        request: Ten99PolicyRequest,
        start_date: date,
        end_date: date,
        coverage_types: list[Ten99PolicyCoverageType],
    ) -> Ten99PolicyAssignment:
        effective_date = get_earliest_start_date_for_coverage(start_date)
        assignment_data = {
            "contractor": contractor.t99p_id,
            "job": job.t99p_id,
            "effective_date": date_to_timestamp(effective_date),
            "end_date": date_to_timestamp(end_date),
            "coverage_type": [COVERAGE_TYPE_MAPPING[ct] for ct in coverage_types],
        }

        try:
            t99p_assignment = self.api.create_assignment(**assignment_data)
        except Ten99PolicyError:
            raise ContractorNotEligibleForAssignment(str(Ten99PolicyError))

        if str(t99p_assignment.eligible.result).lower() == "false":
            raise ContractorNotEligibleForAssignment(t99p_assignment.eligible.message)

        assignment = Ten99PolicyAssignment.objects.create(
            t99p_id=t99p_assignment.id,
            request=request,
            contractor=contractor,
            job=job,
        )

        return self.fetch_assignment_rates(assignment, t99p_assignment.policy)

    def cancel_assignment(self, job: Ten99PolicyJob):
        self.api.cancel_assignment(job.t99p_id)

    def fetch_assignment_rates(self, assignment: Ten99PolicyAssignment, t99p_policy_id: str) -> Ten99PolicyAssignment:
        # Try to fetch assignment rates
        try:
            t99p_policy = self.api.retrieve_policy(t99p_policy_id)

            # If we have a quote, use it to get the rates, if not fetch quote data from 1099P API
            quote = Ten99PolicyQuote.objects.filter(t99p_id=t99p_policy.quote).first()
            if quote:
                gl_net_rate = quote.gl_net_rate
                wc_net_rate = quote.wc_net_rate
                gl_service_fee = quote.gl_service_fee
                wc_service_fee = quote.wc_service_fee
            else:
                t99p_quote = self.api.retrieve_quote(t99p_policy.quote)
                gl_net_rate, wc_net_rate, gl_service_fee, wc_service_fee = self.extract_rates_from_t99p_quote(t99p_quote)

            assignment.gl_net_rate = gl_net_rate
            assignment.wc_net_rate = wc_net_rate
            assignment.gl_service_fee = gl_service_fee
            assignment.wc_service_fee = wc_service_fee
            assignment.save()

        except Ten99PolicyError:
            raise AssigmentRateFetchError(str(Ten99PolicyError))

        return assignment

    def extract_rates_from_t99p_quote(self, t99p_quote: Quotes):
        gl_net_rate = t99p_quote.gl_net_rate
        wc_net_rate = t99p_quote.wc_net_rate
        gl_service_fee = t99p_quote.quote_json.get("gl", {}).get("risk_purchasing_group_fee", None)
        wc_service_fee = t99p_quote.quote_json.get("wc", {}).get("risk_purchasing_group_fee", None)
        return gl_net_rate, wc_net_rate, gl_service_fee, wc_service_fee

    def create_invoice(
        self,
        request: Ten99PolicyRequest,
        contractor: Ten99PolicyContractor,
        job: Ten99PolicyJob,
        income_amount: Decimal,
        start_date: date,
        end_date: date,
    ) -> Ten99PolicyInvoice:
        invoice_data = {
            "contractor": contractor.t99p_id,
            "job": job.t99p_id,
            "gross_pay": int(income_amount * 100),  # Convert to cents
            "paycycle_startdate": date_to_timestamp(start_date),
            "paycycle_enddate": date_to_timestamp(end_date),
        }

        t99p_invoice = self.api.create_invoice(**invoice_data)

        existing_invoice = Ten99PolicyInvoice.objects.filter(request=request).first()

        with transaction.atomic():
            if existing_invoice:
                existing_invoice.delete()

            invoice = Ten99PolicyInvoice.objects.create(
                t99p_id=t99p_invoice.id,
                request=request,
                job=job,
                contractor=contractor,
                gross_pay=t99p_invoice.gross_pay,
                pay_cycle_start_date=start_date,
                pay_cycle_end_date=end_date,
                premium_due=t99p_invoice.premium_due,
            )
        return invoice
