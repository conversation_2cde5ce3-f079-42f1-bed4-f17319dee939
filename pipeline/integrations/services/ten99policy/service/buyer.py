from django.db.transaction import atomic

from documents.models import InsuranceStatus
from events.event_types import Ten99PProofOfInsuranceDecisionConfirmed, Ten99PProofOfInsuranceRejected
from integrations.models import (
    Ten99PolicyCoverage,
    Ten99PolicyCoverageStatus,
    Ten99PolicyCoverageType,
    Ten99PolicyRequest,
)
from integrations.services.ten99policy.data_model import (
    BuyerDecisionData,
    BuyerDecisionFormData,
    BuyerStageDetailsResponseData,
    Coverage,
)
from integrations.services.ten99policy.data_provider.interface import JobData, Ten99PDataProvider
from integrations.services.ten99policy.exceptions import Ten99PException
from integrations.services.ten99policy.logger import Ten99PolicyLogger
from integrations.services.ten99policy.service.common import (
    COVERAGE_STATUS_TO_INSURANCE_STATUS,
    get_and_validate_job_data,
    get_coverage_data_for_request,
    get_ten99p_request,
)
from onboarding.tasks import recalculate_onboarding_completeness

logger: Ten99PolicyLogger = Ten99PolicyLogger()


def onboarding_stage_get_details(data_provider: Ten99PDataProvider) -> BuyerStageDetailsResponseData:
    onboarding_context_name: str = data_provider.onboarding_context_name

    logger.set_onboarding_context_name(onboarding_context_name)
    logger.info("Starting flow - Get onboarding stage details for buyer")

    # Get data from onboarding context
    job_data: JobData = get_and_validate_job_data(data_provider)

    ten99p_request: Ten99PolicyRequest | None = get_ten99p_request(onboarding_context_name)
    if ten99p_request:
        return BuyerStageDetailsResponseData(
            required_coverages=get_coverage_data_for_request(ten99p_request), reupload_allowed=ten99p_request.reupload_allowed
        )

    required_coverages = []
    for coverage_type in job_data.required_coverage_types:
        required_coverages.append(Coverage(type=coverage_type))

    return BuyerStageDetailsResponseData(required_coverages=required_coverages, reupload_allowed=False)


def onboarding_stage_save_decision(data_provider: Ten99PDataProvider, buyer_decision: BuyerDecisionData) -> BuyerStageDetailsResponseData:
    onboarding_context_name: str = data_provider.onboarding_context_name

    logger.set_onboarding_context_name(onboarding_context_name)
    logger.info("Starting flow - Save buyer decision for onboarding stage")

    ten99p_request: Ten99PolicyRequest | None = get_ten99p_request(onboarding_context_name)
    if not ten99p_request:
        logger.error("Request not found for the onboarding context")
        raise Ten99PException("Request not found for the onboarding context")

    # Save buyer decision data for coverages
    with atomic():
        for coverage_type, decision_form_data in buyer_decision.data.items():
            _update_coverage_with_insurance_decision_form_data(ten99p_request, coverage_type, decision_form_data)

    recalculate_onboarding_completeness.delay(
        vendor_ids=[data_provider.vendor.pk], source=["ten99p.onboarding_stage_save_decision_by_buyer"]
    )

    # Fetch fresh request+coverages data after saving buyer decision
    ten99p_request: Ten99PolicyRequest | None = get_ten99p_request(onboarding_context_name)
    required_coverages = get_coverage_data_for_request(ten99p_request)
    reupload_allowed = ten99p_request.reupload_allowed

    _trigger_proof_of_insurance_decision_confirmed_event(data_provider, required_coverages)

    return BuyerStageDetailsResponseData(required_coverages=required_coverages, reupload_allowed=reupload_allowed)


def onboarding_stage_allow_retry(data_provider: Ten99PDataProvider) -> BuyerStageDetailsResponseData:
    onboarding_context_name: str = data_provider.onboarding_context_name

    logger.set_onboarding_context_name(onboarding_context_name)
    logger.info("Starting flow - Allow retry for onboarding stage")

    ten99p_request: Ten99PolicyRequest | None = get_ten99p_request(onboarding_context_name)
    if not ten99p_request:
        logger.error("Request not found for the onboarding context")
        raise Ten99PException("Request not found for the onboarding context")

    ten99p_request.reupload_allowed = True
    ten99p_request.save()
    recalculate_onboarding_completeness.delay(vendor_ids=[data_provider.vendor.pk], source=["ten99p.onboarding_stage_allow_retry_by_buyer"])
    logger.debug("Updated request object with reupload_allowed=True")

    required_coverages = get_coverage_data_for_request(ten99p_request)
    reupload_allowed = ten99p_request.reupload_allowed

    rejection_reasons = {
        coverage.type.value: coverage.rejection_reason
        for coverage in required_coverages
        if coverage.status == Ten99PolicyCoverageStatus.rejected
    }
    Ten99PProofOfInsuranceRejected(data_provider=data_provider, rejection_reasons=rejection_reasons)

    return BuyerStageDetailsResponseData(required_coverages=required_coverages, reupload_allowed=reupload_allowed)


def _update_coverage_with_insurance_decision_form_data(
    ten99p_request: Ten99PolicyRequest, coverage_type: Ten99PolicyCoverageType, buyer_decision_form_data: BuyerDecisionFormData
):
    logger.set_onboarding_context_name(ten99p_request.onboarding_context_name)
    coverage: Ten99PolicyCoverage = ten99p_request.coverages.filter(type=coverage_type).select_related('document').first()

    if not coverage:
        logger.error(
            f"Coverage object should already exists for the request (coverage_type={coverage_type}, t99p_request_id={ten99p_request.pk})"
        )
        raise Ten99PException(f"Coverage type {coverage_type} should exists for the request {ten99p_request.pk}")

    expiration_date = buyer_decision_form_data.end_date if buyer_decision_form_data.status == Ten99PolicyCoverageStatus.approved else None
    rejection_reason = (
        buyer_decision_form_data.rejection_reason if buyer_decision_form_data.status == Ten99PolicyCoverageStatus.rejected else None
    )

    coverage.status = buyer_decision_form_data.status
    coverage.rejection_reason = rejection_reason
    coverage.expiration_date = expiration_date

    # Coverages created in the past don't have reference to Insurance document
    # TODO(INTEG-1344): Prepare migrate script that will repopulate missing data
    if coverage.document is not None:
        coverage.document.status = COVERAGE_STATUS_TO_INSURANCE_STATUS.get(buyer_decision_form_data.status, InsuranceStatus.uploaded)
        coverage.document.expiration_date = expiration_date
        coverage.document.save()

    coverage.save()

    logger.debug(f"Updated coverage object with buyer decision form data (coverage_type={coverage_type})")


def _trigger_proof_of_insurance_decision_confirmed_event(data_provider: Ten99PDataProvider, updated_coverages: list[Coverage]) -> None:
    if any(coverage.has_own_insurance for coverage in updated_coverages):
        Ten99PProofOfInsuranceDecisionConfirmed(data_provider=data_provider)
