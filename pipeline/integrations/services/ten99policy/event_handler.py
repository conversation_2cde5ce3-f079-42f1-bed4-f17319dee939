from dependency_injector.wiring import Provide

from events.event_types import TaskStatusChanged, CustomFieldChanged
from integrations.models import Ten99PolicyConfig
from integrations.services.ten99policy import Ten99PContainer
from integrations.services.ten99policy.tasks import (
    create_invoice_in_ten99policy_based_on_jo_task,
    refresh_ten99policy_job_category_codes_mapping_for_current_config,
)
from tasks.workflow import TASK_STATUS_COMPLETED


def handle_task_status_changed(event):
    if not event.task.covered_by_1099p_insurance:
        return

    if event.task_status == TASK_STATUS_COMPLETED:
        create_invoice_in_ten99policy_based_on_jo_task.delay(event.task.pk)


def handle_custom_field_changed(event, config: Ten99PolicyConfig = Provide[Ten99PContainer.config]):
    if config and event.custom_field == config.job_category_custom_field:
        refresh_ten99policy_job_category_codes_mapping_for_current_config.delay()


def register_event_watchers():
    TaskStatusChanged.watch(handle_task_status_changed)
    CustomFieldChanged.watch(handle_custom_field_changed)
