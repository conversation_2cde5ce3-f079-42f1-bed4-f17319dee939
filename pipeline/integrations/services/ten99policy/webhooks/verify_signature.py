import hashlib
import hmac


def verify_webhook_signature(payload: str, timestamp: str, expected_signature: str, webhook_secret: str) -> bool:
    if not payload or not timestamp or not expected_signature:
        return False

    if not webhook_secret:
        raise RuntimeError("Invalid 1099P configuration - no webhook secret")

    request_data = f"{timestamp},{payload}".encode()
    secret = webhook_secret.encode()
    digest = hmac.new(secret, request_data, hashlib.sha512).hexdigest()
    result = hmac.compare_digest(expected_signature, digest)
    return result
