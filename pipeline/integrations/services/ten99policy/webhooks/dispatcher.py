import sentry_sdk
from pydantic import ValidationError

from integrations.services.ten99policy.exceptions import InvalidRequest
from integrations.services.ten99policy.webhooks.events import Ten99PWebhookActions, WebhookEvent
from integrations.services.ten99policy.webhooks.handlers import manul_review, policy_active, no_op, category_code_added, card_charge

handlers = {
    Ten99PWebhookActions.application_created: no_op.handle,
    Ten99PWebhookActions.application_started: no_op.handle,
    Ten99PWebhookActions.manual_review: manul_review.handle,
    Ten99PWebhookActions.assignment_active: no_op.handle,
    Ten99PWebhookActions.policy_active: policy_active.handle,
    Ten99PWebhookActions.category_code_added: category_code_added.handle,
    Ten99PWebhookActions.card_charge_succeeded: card_charge.handle,
    Ten99PWebhookActions.card_charge_failed: card_charge.handle,
}


def handle_webhook_request(request_data: dict):
    try:
        event = WebhookEvent.validate_python(request_data)
    except ValidationError as exception:
        sentry_sdk.capture_exception(exception)
        raise InvalidRequest("Invalid request data") from exception

    handler = handlers[event.type]
    handler(event)
