from typing import Literal, Annotated

from pydantic import BaseModel, Discriminator, TypeAdapter
from strenum import StrEnum


class Ten99PWebhookActions(StrEnum):
    application_created = "application.created"
    application_started = "application.started"
    manual_review = "application.manual_review"
    assignment_active = "assignment.active"
    policy_active = "policy.active"
    category_code_added = "category_code.added"
    card_charge_succeeded = "invoice.charge_card_succeeded"
    card_charge_failed = "invoice.charge_card_failed"


class EventBase(BaseModel):
    type: str


class PolicyActiveData(BaseModel):
    id: str
    quote: str


class PolicyActiveEvent(EventBase):
    type: Literal[Ten99PWebhookActions.policy_active]
    data: PolicyActiveData


class ManualReviewData(BaseModel):
    id: str
    job: str
    contractor: str


class ManualReviewEvent(BaseModel):
    type: Literal[Ten99PWebhookActions.manual_review]
    data: ManualReviewData


class CategoryCodeAddedEvent(BaseModel):
    type: Literal[Ten99PWebhookActions.category_code_added]


class CardChargeData(BaseModel):
    contractor: str


class CardChargeEvent(BaseModel):
    type: Literal[Ten99PWebhookActions.card_charge_succeeded, Ten99PWebhookActions.card_charge_failed]
    data: CardChargeData


class NoOpEvent(EventBase):
    type: Literal[
        Ten99PWebhookActions.application_created,
        Ten99PWebhookActions.application_started,
        Ten99PWebhookActions.assignment_active,
    ]


WebhookEvent = TypeAdapter(
    Annotated[PolicyActiveEvent | ManualReviewEvent | CategoryCodeAddedEvent | CardChargeEvent | NoOpEvent, Discriminator("type")]
)
