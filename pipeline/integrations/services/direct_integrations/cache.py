from collections import defaultdict
from typing import TYPE_CHECKING

from django.apps import apps

from shortlist.utils import cached, cache_func_tenant

if TYPE_CHECKING:
    from integrations.models import DirectIntegrationEnabledProcess


@cached(cache_func_tenant("enabled_processes"))
def enabled_processes() -> dict[str, list[dict[str, str]]]:
    enabled_processes_by_event_type = defaultdict(list)
    # indirect import to avoid circular import
    enabled_processes_model = apps.get_model("integrations", "DirectIntegrationEnabledProcess")

    for process in enabled_processes_model.objects.all():
        process: DirectIntegrationEnabledProcess
        process_data = {
            "event_filter_handler": process.event_filter_handler,
            "process_name": process.process_name,
            "config_integration": process.config.integration,
            "config_linked_account_id": process.config.linked_account_id,
            "config_manual_sync_groups": process.config.manual_sync_groups,
        }
        for event_type in process.events:
            enabled_processes_by_event_type[event_type].append(process_data)

    return enabled_processes_by_event_type
