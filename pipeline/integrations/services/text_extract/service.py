import sentry_sdk

from integrations.services.text_extract.api import TextExtractAPI
from shortlist.utils import s3_file_exists, s3_presigned_url


def presign_file_url(*, bucket: str, key: str) -> str:
    filename = key.rsplit("/", 1)[-1]
    return s3_presigned_url(file_name=filename, bucket_name=bucket, key_name=key)


class TextExtract:
    def __init__(self, api: TextExtractAPI, presign_func: callable = presign_file_url, file_exists_func: callable = s3_file_exists):
        self.api = api
        self.presign_func = presign_func
        self.file_exists_func = file_exists_func

    def extract_from_file(self, bucket: str, key: str) -> str:
        try:
            if not self.file_exists_func(bucket, key):
                return ""
            presigned_url = self.presign_func(bucket=bucket, key=key)
            return self.api.extract_text(url=presigned_url)
        except Exception as e:
            sentry_sdk.capture_exception(e)
        return ""
