# Generated by Django 3.1.14 on 2022-12-22 17:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0030_auto_20221117_1542'),
    ]

    operations = [
        migrations.RenameField(
            model_name='paymentprocessingproblem',
            old_name='failure_date',
            new_name='created_at',
        ),
        migrations.RemoveField(
            model_name='paymentprocessingproblem',
            name='failure_status',
        ),
        migrations.RemoveField(
            model_name='paymentprocessingproblem',
            name='provider',
        ),
        migrations.AddField(
            model_name='payment',
            name='blocked_processing_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='paymentprocessingproblem',
            name='payment_processor',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='paymentprocessingproblem',
            name='reason_group',
            field=models.CharField(blank=True, max_length=150, null=True),
        ),
        migrations.AddField(
            model_name='paymentprocessingproblem',
            name='resolved',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='paymentprocessingproblem',
            name='updated_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='paymentprocessingproblem',
            name='reason',
            field=models.CharField(blank=True, max_length=150, null=True),
        ),
    ]
