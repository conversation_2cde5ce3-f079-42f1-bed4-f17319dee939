# Generated by Django 3.2.18 on 2023-04-04 13:31

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('preferences', '0070_customfield_source_field'),
        ('documents', '0033_auto_20230322_1418'),
    ]

    operations = [
        migrations.CreateModel(
            name='WorkItemCategoryForWorkType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='documents.workitemcategory')),
                ('work_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='+', to='preferences.worktype')),
            ],
            options={
                'unique_together': {('category', 'work_type')},
            },
        ),
    ]
