from collections import defaultdict
from typing import TYPE_CHECKING, Optional

from django.db import transaction

from documents.models import RequestedDocument
from shortlist.current_user import get_current_user

if TYPE_CHECKING:
    from users.models import User


@transaction.atomic
def create_requested_documents(
    vendor_ids: list[int], template_ids: list[int], created_by: Optional['User'] = None
) -> list[RequestedDocument]:
    if created_by is None:
        created_by = get_current_user()

    existing_vendor_documents = RequestedDocument.objects.filter(vendor_id__in=vendor_ids, template_id__in=template_ids).values(
        'vendor_id', 'template_id'
    )
    existing_vendor_templates = defaultdict(set)
    for existing_document in existing_vendor_documents:
        existing_vendor_templates[existing_document['vendor_id']].add(existing_document['template_id'])

    documents = [
        RequestedDocument(template_id=template_id, vendor_id=vendor_id, created_by=created_by)
        for vendor_id in vendor_ids
        for template_id in template_ids
        if template_id not in existing_vendor_templates.get(vendor_id, [])
    ]
    return RequestedDocument.objects.bulk_create(documents)
