from decimal import Decimal

from django.test import TestCase

from documents.taxes import round_decimal, compute_amount, compute_tax


class RoundDecimalTestCase(TestCase):
    def test_correct_input(self):
        self.assertEqual(round_decimal(9), Decimal("9"))
        self.assertEqual(round_decimal("9.9949"), Decimal("9.99"))
        self.assertEqual(round_decimal("9.9951"), Decimal("10"))

    def test_incorrect_input(self):
        invalid_message = "round_decimal got invalid argument type for amount, got {}"
        with self.assertRaisesMessage(TypeError, invalid_message.format("<class 'float'>")):
            round_decimal(1.99)

        invalid_message = "round_decimal got invalid argument type for amount, got {}"
        with self.assertRaisesMessage(TypeError, invalid_message.format("<class 'bool'>")):
            round_decimal(True)

        invalid_message = "round_decimal got invalid argument type for amount, got {}"
        with self.assertRaisesMessage(TypeError, invalid_message.format("<class 'NoneType'>")):
            round_decimal(None)

    def test_rounding(self):
        self.assertEqual(round_decimal(Decimal("9.01")), Decimal("9.01"))
        self.assertEqual(round_decimal(Decimal("9.1")), Decimal("9.1"))
        self.assertEqual(round_decimal(Decimal("9.9")), Decimal("9.9"))
        self.assertEqual(round_decimal(Decimal("9.99")), Decimal("9.99"))
        self.assertEqual(round_decimal(Decimal("9.990")), Decimal("9.99"))
        self.assertEqual(round_decimal(Decimal("9.991")), Decimal("9.99"))
        self.assertEqual(round_decimal(Decimal("9.992")), Decimal("9.99"))
        self.assertEqual(round_decimal(Decimal("9.993")), Decimal("9.99"))
        self.assertEqual(round_decimal(Decimal("9.994")), Decimal("9.99"))
        self.assertEqual(round_decimal(Decimal("9.995")), Decimal("10"))
        self.assertEqual(round_decimal(Decimal("9.996")), Decimal("10"))
        self.assertEqual(round_decimal(Decimal("9.997")), Decimal("10"))
        self.assertEqual(round_decimal(Decimal("9.998")), Decimal("10"))
        self.assertEqual(round_decimal(Decimal("9.999")), Decimal("10"))
        self.assertEqual(round_decimal(Decimal("9.9949")), Decimal("9.99"))
        self.assertEqual(round_decimal(Decimal("9.9951")), Decimal("10"))

        self.assertEqual(round_decimal(Decimal("9.9949"), decimal_places=5), Decimal("9.9949"))
        self.assertEqual(round_decimal(Decimal("9.9951"), decimal_places=5), Decimal("9.9951"))
        self.assertEqual(round_decimal(Decimal("9.99999"), decimal_places=5), Decimal("9.99999"))
        self.assertEqual(round_decimal(Decimal("9.999994"), decimal_places=5), Decimal("9.99999"))
        self.assertEqual(round_decimal(Decimal("9.999995"), decimal_places=5), Decimal("10"))
        self.assertEqual(round_decimal(Decimal("9.999999"), decimal_places=5), Decimal("10"))


class ComputeAmountTestCase(TestCase):
    def test_correct_input(self):
        self.assertEqual(compute_amount(9, 9), Decimal("81"))
        self.assertEqual(compute_amount("9.99", "9.99"), Decimal("99.8"))
        self.assertEqual(compute_amount(Decimal("9.99"), Decimal("9.99")), Decimal("99.8"))

    def test_computations(self):
        self.assertEqual(compute_amount(Decimal("9.99"), Decimal("9.99")), Decimal("99.8"))
        self.assertEqual(compute_amount(Decimal("129.83333"), Decimal("55")), Decimal("7140.83"))


class ComputeTaxTestCase(TestCase):
    def test_computations(self):
        self.assertEqual(compute_tax(Decimal("7140.83"), None), Decimal("0"))
        self.assertEqual(compute_tax(Decimal("7140.83"), 0), Decimal("0"))
        self.assertEqual(compute_tax(Decimal("7140.83"), Decimal("0")), Decimal("0"))
        self.assertEqual(compute_tax(Decimal("7140.83"), Decimal("1")), Decimal("71.41"))
        self.assertEqual(compute_tax(Decimal("7140.83"), Decimal("10")), Decimal("714.08"))
        self.assertEqual(compute_tax(Decimal("7140.83"), Decimal("23")), Decimal("1642.39"))
        self.assertEqual(compute_tax(Decimal("7140.83"), Decimal("55.9")), Decimal("3991.72"))
        self.assertEqual(compute_tax(Decimal("7140.83"), Decimal("100")), Decimal("7140.83"))
