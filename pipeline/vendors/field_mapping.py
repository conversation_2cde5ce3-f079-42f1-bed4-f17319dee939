import datetime
from collections.abc import Iterable
from dataclasses import dataclass
from operator import attrgetter
from typing import Literal, Union, Any, TYPE_CHECKING

from django.core.cache import cache
from django.core.exceptions import ObjectDoesNotExist
from django.db import connection
from django.db.models import Prefetch
from django.utils.timezone import now

from preferences.models import OTHER_VALUE_IDENTIFIER, CustomField, CustomFieldsTemplate
from shortlist.helpers import HTMLText
from shortlist.utils import current_user_timezone
from users.models import User


if TYPE_CHECKING:
    from contracts.models import Contract
    from openings.models import JobOpening
    from tasks.models import Task
    from vendors.models import Vendor


GLOBAL_FIELD_MAPPING_CHOICES = [
    ("", ""),
    ("current date", "Current date"),
    ("name", "Business Name"),
    ("first_name", "First Name"),
    ("last_name", "Last Name"),
    ("phone_number", "Phone Number"),
    ("full_address", "Address"),
    ("email", "Email"),
    ("hourly_rate_currency", "Rate currency"),
    ("slug", "Internal Vendor Slug"),
    ("id", "Internal Vendor Id"),
    ("profile_url", "Profile URL"),
    ("vendor_type", "Vendor type"),
    ("managers", "Vendor relationship managers"),
    ("external_id", "External IDs"),
    ("archived", "Archived"),
    ("tags", "Tags"),
    ("rank_label", "Rank"),
    ("parent.name", "Staffing supplier"),
    ("first_contact.full_name", "Contact - Full name"),
    ("first_contact.first_name", "Contact - First name"),
    ("first_contact.last_name", "Contact - Last name"),
    ("taxinformation.address", "Tax Information - Address"),
    ("taxinformation.city", "Tax Information - City"),
    ("taxinformation.company_name", "Tax Information - Company Name"),
    ("taxinformation.country", "Tax Information - Country"),
    ("taxinformation.disregarded_entity_name", "Tax Information - Disregarded entity name"),
    ("taxinformation.employer_identyfication_number", "Tax Information - Employer Identification Number"),
    ("taxinformation.first_name", "Tax Information - Firstname"),
    ("taxinformation.last_name", "Tax Information - Lastname"),
    ("taxinformation.middle_name", "Tax Information - Middle name"),
    ("taxinformation.postal_code", "Tax Information - Postal code"),
    ("taxinformation.residential_country", "Tax Information - Residential country"),
    ("taxinformation.social_security_number", "Tax Information - SSN"),
    ("taxinformation.state", "Tax Information - State"),
    ("taxinformation.tax_classification", "Tax Information - Classification"),
    ("taxinformation.national_id", "Tax Information - National ID"),
    ("taxinformation.tax_identifier", "Tax Information - Tax Identifier"),
    ("taxinformation.tax_id", "Tax Information - Tax ID"),
    ("bank_details.country", "Bank Details - Country"),
    ("bank_details.operator_name", "Bank Details - Operator Name"),
    ("bank_details.external_id", "Bank Details - External ID"),
    ("bank_details.status", "Bank Details - Status"),
    ("bank_details.shortlist_pay_status", "Bank Details - ShortlistPay Status"),
    ("bank_details.fields.bank_name", "Bank Details - Bank name"),
    ("bank_details.fields.routing_number", "Bank Details - Routing number"),
    ("bank_details.fields.account_number", "Bank Details - Account number"),
    ("bank_details.fields.account_type", "Bank Details - Account type"),
    ("bank_details.fields.account_name", "Bank Details - Account name"),
    ("bank_details.fields.sort_code", "Bank Details - Sort code"),
    ("bank_details.fields.iban", "Bank Details - IBAN"),
    ("bank_details.fields.swift_bic_code", "Bank Details - SWIFT/BIC code"),
    ("bank_details.fields.bank_branch_address", "Bank Details - Bank branch address"),
    ("bank_details.fields.building_society_roll", "Bank Details - Building society roll number"),
    ("bank_details.fields.branch_key_code", "Bank Details - Branch key code"),
    ("bank_details.fields.address", "Bank Details - Bank address"),
    # Fields below aren"t GLOBAL
    ("task.task_group.name", "{terms[Task]} - {terms[Project]} name"),
    ("task.task_group.manager_names", "{terms[Task]} - {terms[Project]} managers"),
    ("task.task_group.frontend_url", "{terms[Task]} - {terms[Project]} URL"),
    ("task.name", "{terms[Task]} - name"),
    ("task.frontend_url", "{terms[Task]} - URL"),
    ("task.date_start", "{terms[Task]} - start date"),
    ("task.date_end", "{terms[Task]} - end date"),
    ("task.description", "{terms[Task]} - description"),
    ("task.currency", "{terms[Task]} - currency"),
    ("task.budget_rate_per_time_unit", "{terms[Task]} - rate"),
    ("task.budget_time_units_worked", "{terms[Task]} - time units worked"),
    ("task.budget_total", "{terms[Task]} - budget total"),
    ("task.budget_rate_type", "{terms[Task]} - budget rate type"),
    ("task.status", "{terms[Task]} - status"),
    ("task.manager_names", "{terms[Task]} - managers"),
    ("task.last_sign_request.status", "{terms[Task]} - last sign request status"),
    ("task.last_sign_request.created", "{terms[Task]} - last sign request sent date"),
    ("task.last_sign_request.signed_document.sign_date", "{terms[Task]} - last sign request signed date"),
    ("task_template.task_group.name", "{terms[Task]} template - {terms[Project]} name"),
    ("task_template.task_group.manager_names", "{terms[Task]} template - {terms[Project]} managers"),
    ("task_template.name", "{terms[Task]} template - name"),
    ("task_template.date_start", "{terms[Task]} template - start date"),
    ("task_template.date_end", "{terms[Task]} template - end date"),
    ("task_template.description", "{terms[Task]} template - description"),
    ("task_template.currency", "{terms[Task]} template - currency"),
    ("task_template.budget_rate_per_time_unit", "{terms[Task]} template - rate"),
    ("task_template.budget_time_units_worked", "{terms[Task]} template - time units worked"),
    ("task_template.budget_total", "{terms[Task]} template - budget total"),
    ("task_template.budget_rate_type", "{terms[Task]} template - budget rate type"),
    ("task_template.manager_names", "{terms[Task]} template - managers"),
    ("opening.reference_id", "{terms[Job opening]} - ID"),
    ("opening.name", "{terms[Job opening]} - name"),
    # ("opening.job_title", "{terms[Job opening]} - {terms[job title]}"),
    ("opening.description", "{terms[Job opening]} - description"),
    ("opening.location", "{terms[Job opening]} - location"),
    ("contract.name", "{terms[Contract]} name"),
    ("contract.description", "{terms[Contract]} description"),
    ("contract.start_date", "{terms[Contract]} start date"),
    ("contract.end_date", "{terms[Contract]} end date"),
    ("contract.payment_template.amount", "{terms[Contract]} rate"),
    ("contract.payment_template.currency", "{terms[Contract]} currency"),
    ("contract.payment_template.frequency_text", "{terms[Contract]} payment frequency"),
    ("contract.payment_template.total_amount", "{terms[Contract]} total payable"),
]

USER_FIELD_MAPPING_CHOICES = [
    ("task.managers", "{terms[Task]} - managers"),
    ("task_template.managers", "{terms[Task]} template - managers"),
]

CONTRACT_PREFIX = "contract."
CUSTOM_FIELD_P_PREFIX = "custom_p_field."
CUSTOM_FIELD_PREFIX = "custom_field."
CUSTOM_RATE_PREFIX = "custom_rate."
JOB_OPENING_PREFIX = "opening."
ONBOARDING_STAGE_PREFIX = "onboarding_stage."
PAYMENT_PREFIX = "payment."
TASK_GROUP_PREFIX = "task_group."
TASK_PREFIX = "task."
TASK_TEMPLATE_PREFIX = "task_template."
TEMPLATE_FIELD_PREFIX = "template_field."


@dataclass
class FieldMapping:
    """
    NOTE: Work in progress.
    This class represents possible types of field mapping.
    Consider using this class to simplify the code in get_raw_mapped_value.
    """

    key: (
        Literal["custom_p_field"]
        | Literal["custom_field"]
        | Literal["template_field"]
        | Literal["custom_rate"]
        | Literal["task_group"]
        | Literal["task_template"]
        | Literal["task"]
        | Literal["opening"]
        | Literal["contract"]
        | Literal["onboarding_stage"]
    )
    value: str | list[str]

    @property
    def is_custom_field(self):
        return self.key == "template_field"

    @staticmethod
    def from_mapping_str(mapping: str) -> "FieldMapping":
        split_mapping = mapping.split(".")
        if mapping.startswith(
            (CUSTOM_FIELD_P_PREFIX, CUSTOM_FIELD_PREFIX, TEMPLATE_FIELD_PREFIX, CUSTOM_RATE_PREFIX, ONBOARDING_STAGE_PREFIX)
        ):
            return FieldMapping(split_mapping[0], split_mapping[1])  # noqa

        if mapping.startswith((JOB_OPENING_PREFIX, CONTRACT_PREFIX)):
            return FieldMapping(split_mapping[0], split_mapping[1:])  # noqa

        if mapping.startswith((TASK_PREFIX, TASK_TEMPLATE_PREFIX)):
            if mapping.startswith("task_template.task_group."):
                return FieldMapping("task_group", split_mapping[2:])

            if mapping.startswith(TASK_TEMPLATE_PREFIX):
                return FieldMapping("task_template", split_mapping[1:])

            return FieldMapping("task", split_mapping[1:])

        raise ValueError(f'Invalid field mapping "{mapping}"')


def format_date(value):
    if not value:
        return ""
    if isinstance(value, datetime.datetime):
        value = value.astimezone(current_user_timezone())
    return value.strftime("%Y-%m-%d")


def create_getter(mapping):
    if mapping == "":
        return lambda x: None
    parts = mapping.split(".")
    if len(parts) > 1:
        if parts[0] == "bank_details":
            if parts[1] == "fields":
                key = parts[2]
                return lambda vendor: vendor.cached_unified_bank_details["fields"][key]
            else:
                key = parts[1]
                return lambda vendor: vendor.cached_unified_bank_details[key]
        if parts[0] in ("task", "task_template", "custom_field", "custom_rate", "template_field", "opening", "contract"):
            return None
    elif mapping == "current date":
        return lambda x: format_date(now())
    elif mapping == "managers":
        return lambda vendor: ";".join([manager.full_name for manager in vendor.managers])

    return attrgetter(mapping)


GLOBAL_MAPPING_TO_DESC = dict(GLOBAL_FIELD_MAPPING_CHOICES)
GLOBAL_ALLOWED_MAPPINGS = list(GLOBAL_MAPPING_TO_DESC.keys())
GLOBAL_MAPPING_TO_GETTER = {k: create_getter(k) for k in GLOBAL_ALLOWED_MAPPINGS}

USER_MAPPING_TO_DESC = dict(USER_FIELD_MAPPING_CHOICES)


def get_address_or_location(vendor):
    location = vendor.location_dict or {}
    return location.get("query") or vendor.full_address


GLOBAL_MAPPING_TO_GETTER["full_address"] = get_address_or_location  # fix for SHOR-11597, refactor after full_address is removed


def user_names_from_id(ids):
    if isinstance(ids, list):
        return [user.display_name for user in User.objects.filter(id__in=ids)]
    if ids.isdigit():
        try:
            user = User.objects.get(id=ids)
            if user:
                return user.display_name
        except User.DoesNotExist:
            pass
    return None


def get_raw_mapped_value_custom_vendor_field(key, vendor):
    cf = vendor.custom_fields or {}
    return cf.get(key)


def check_for_other(key, data):
    if data is None or data.get(key) is None:
        return False

    try:
        custom_fields_with_other = connection.tenant._custom_fields_with_other  # noqa
    except AttributeError:
        custom_fields_with_other = connection.tenant._custom_fields_with_other = list(
            CustomField.active.filter(type__in=[CustomField.TYPE_CHOICE, CustomField.TYPE_SELECT], other_label__isnull=False).values_list(
                "pk", flat=True
            )
        )

    value = data.get(key)
    if value:
        for cf_id in custom_fields_with_other:
            if str(cf_id) == key and isinstance(value, Iterable) and OTHER_VALUE_IDENTIFIER in data.get(key):
                return True

    return False


def get_raw_mapped_value_for_other(key, data):
    try:
        custom_fields_other_value = connection.tenant._fields_other  # noqa
    except AttributeError:
        custom_fields_other_value = connection.tenant._fields_other = CustomField.active.filter(
            field_group__startswith=OTHER_VALUE_IDENTIFIER
        )
    value = data.get(key)
    for cf in custom_fields_other_value:
        if cf.field_group == f"{OTHER_VALUE_IDENTIFIER}{key}":
            choice = isinstance(value, list)
            if choice:
                value.remove(OTHER_VALUE_IDENTIFIER)
                value.append(data.get(str(cf.id)))
            else:
                value = data.get(str(cf.id))
    return value


def get_raw_mapped_value(
    mapping: str, vendor: Union["Vendor", None], context: Union["Contract", "JobOpening", "Task", dict], for_email: bool = False
) -> Any | None:
    try:
        f = GLOBAL_MAPPING_TO_GETTER.get(mapping)

        if f and vendor is not None:
            return f(vendor)

        match mapping:
            case s if s.startswith(CUSTOM_FIELD_P_PREFIX):
                if vendor is not None:
                    if vendor.parent_id:
                        return get_raw_mapped_value_custom_vendor_field(mapping[len(CUSTOM_FIELD_P_PREFIX) :], vendor.parent)
                    return ""
            case s if s.startswith(CUSTOM_FIELD_PREFIX):
                if vendor is not None:
                    return get_raw_mapped_value_custom_vendor_field(mapping[len(CUSTOM_FIELD_PREFIX) :], vendor)
            case s if s.startswith(TEMPLATE_FIELD_PREFIX):
                return __map_template_field_value(mapping, context, for_email)
            case s if s.startswith(CUSTOM_RATE_PREFIX):
                if vendor is not None:
                    return __map_custom_rate_value(mapping, vendor)
            case s if s.startswith((TASK_PREFIX, TASK_TEMPLATE_PREFIX)):
                return __map_task_or_task_template_value(mapping, context)
            case s if s.startswith((JOB_OPENING_PREFIX, CONTRACT_PREFIX)):
                return __map_job_opening_or_contract_value(mapping, context)
            case s if s.startswith(ONBOARDING_STAGE_PREFIX):
                return __map_onboarding_stage_value(mapping, context, for_email)

    except (LookupError, AttributeError, ObjectDoesNotExist):
        return None


def __get_cached_user_type_custom_fields_ids() -> list[int]:
    try:
        return connection.tenant._fields_user_type  # noqa
    except AttributeError:
        connection.tenant._fields_user_type = CustomField.active.filter_user_type()
        return connection.tenant._fields_user_type  # noqa


def __map_onboarding_stage_value(mapping: str, context: dict, for_email: bool):
    field_id = mapping[len("onboarding_stage.") :]
    value = context.get(f"custom_{field_id}", "")
    if for_email and int(field_id) in __get_cached_user_type_custom_fields_ids():
        value = user_names_from_id(value)

    return value


def __map_job_opening_or_contract_value(mapping, context: Union["JobOpening", "Contract"]):
    value = context
    for key in mapping.split(".")[1:]:
        value = getattr(value, key)
        if key == "description":
            value = __format_description(value)

    if value is None:
        value = ""
    return value


def __map_template_field_value(mapping: str, context: Union["Task", "JobOpening", "Contract"], for_email: bool) -> Any:
    key = mapping[len(TEMPLATE_FIELD_PREFIX) :]
    cf = getattr(context, "custom_fields_data", None) or {}
    # handle task template fields for Job openings
    if key not in cf and hasattr(context, "task_template"):
        task_template = getattr(context, "task_template", None)
        cf = task_template.custom_fields_data or {}
        if key not in cf and task_template:  # handle task group custom fields in TaskTemplate connected to JobOpening
            task_group = getattr(task_template, "task_group", None)
            cf = task_group.custom_fields_data if task_group else {}
    if check_for_other(key, cf):
        value = get_raw_mapped_value_for_other(key, cf)
    else:
        value = cf.get(key)
    if for_email and int(key) in __get_cached_user_type_custom_fields_ids():
        value = user_names_from_id(value)
    return value


def __map_task_or_task_template_value(mapping: str, context: Union["Task", "JobOpening"]) -> Any:
    split_mapping = mapping.split(".")[1:]
    if mapping.startswith("task_template.task_group."):
        value = context.task_template.task_group
        split_mapping = mapping.split(".")[2:]
    elif mapping.startswith(TASK_TEMPLATE_PREFIX):
        value = context.task_template
    else:
        value = context

    for key in split_mapping:
        value = getattr(value, key)
        if key == "budget_rate_type":
            value = value.name if value else "total"
        elif key == "description":
            value = __format_description(value)
        elif key == "managers":
            value = list(value.all().values_list("pk", flat=True))

    if value is None:
        value = ""

    return value


def __map_custom_rate_value(mapping: str, vendor: "Vendor") -> Any:
    cf = vendor.filtered_custom_rates or {}
    pk = mapping[len(CUSTOM_RATE_PREFIX) :]
    return cf.get(int(pk)) if pk.isdigit() else ""


def __format_description(value: str | None) -> str:
    if value:
        # HelloSign documentation: "Only "text" and "checkbox" are currently supported."
        # https://app.hellosign.com/api/signature_request/send_with_template
        return HTMLText(value).to_text().rstrip("\n")

    return value


def get_mapped_vendors_value(mapping, vendor, context, for_email=False):
    value = get_raw_mapped_value(mapping, vendor, context, for_email)
    return force_unicode(value)


def force_unicode(value):
    if value is not None:
        # force everything to text
        try:
            if isinstance(value, list):
                return ",".join([force_unicode(x) for x in value])
            if isinstance(value, bool):
                return "yes" if value else "no"
            if isinstance(value, (datetime.date, datetime.datetime)):
                value = format_date(value)
            return str(value)
        except Exception:
            # TODO: logging?
            pass
    return ""


def is_mapping_valid(mapping):
    return (
        mapping in GLOBAL_ALLOWED_MAPPINGS
        or mapping.startswith("custom_field")
        or mapping.startswith("template_field")
        or mapping.startswith("custom_rate")
        or mapping.startswith("custom_p_field")
    )


def mapping_to_desc(mapping):
    try:
        return GLOBAL_MAPPING_TO_DESC[mapping]
    except KeyError:
        return dict(field_mapping_choices()).get(mapping)


def field_mapping_choices(
    include_global=True,
    include_custom_rates=True,
    include_custom_profile=True,
    include_task_fields=True,
    include_job_opening_fields=True,
    include_contract_fields=True,
    include_task_template_fields=True,
    include_payment_fields=True,
    include_templates=True,
    custom_fields_from_workflow=False,
    include_task_group_fields=True,
    include_work_items=False,
    include_custom_parent_profile=False,
    excluded_custom_field_types=(),
):
    cache_key = tuple(locals().values())
    cached = get_from_tenant_per_request_cache("_field_mapping_choices", cache_key)
    if cached is not None:
        return cached

    result = []
    if include_global:
        result.extend(global_field_mapping())
    if include_custom_rates:
        result.extend(field_mapping_custom_rates())
    if include_custom_profile:
        result.extend(field_mapping_custom_vendor_fields(excluded_custom_field_types))
    if include_custom_parent_profile:
        result.extend(field_mapping_custom_vendor_parent_fields(excluded_custom_field_types))
    if include_task_fields:
        result.extend(field_mapping_task_fields(excluded_custom_field_types))
    if include_task_group_fields:
        result.extend(field_mapping_task_group_fields(excluded_custom_field_types))
    if include_work_items:
        result.extend(field_mapping_work_item_fields(excluded_custom_field_types))
    if include_job_opening_fields:
        result.extend(field_mapping_job_opening_fields(excluded_custom_field_types))
    if include_contract_fields:
        result.extend(field_mapping_contract_fields(excluded_custom_field_types))
    if include_task_template_fields:
        result.extend(field_mapping_task_template_fields(excluded_custom_field_types))
    if include_payment_fields:
        result.extend(field_mapping_payment_fields(excluded_custom_field_types))
    if include_templates:
        result.extend(field_mapping_custom_fields_templates(excluded_custom_field_types))
    if custom_fields_from_workflow:
        result.extend(field_mapping_from_workflow(custom_fields_from_workflow, excluded_custom_field_types))

    set_tenant_per_request_cache("_field_mapping_choices", cache_key, result)

    return result


def global_field_mapping() -> list[tuple[str, str]]:
    result = []
    terms = connection.tenant.tenant_terms
    for key, label in GLOBAL_FIELD_MAPPING_CHOICES:
        if not key.startswith((TASK_TEMPLATE_PREFIX, TASK_PREFIX, JOB_OPENING_PREFIX, CONTRACT_PREFIX, PAYMENT_PREFIX)):
            result.append((key, label.format(terms=terms)))
    return result


def field_mapping_for(custom_fields_model, excluded_custom_field_types=(), for_vendors_only=False):
    from preferences.models import CustomFieldsTemplate

    default_field_template = "{{terms[{model_label}]}} - {label}"
    config = {
        CustomFieldsTemplate.CONTRACT_MODEL: {"label": "Contract", "prefix": CONTRACT_PREFIX},
        CustomFieldsTemplate.PAYMENT_MODEL: {"label": "Payment", "prefix": PAYMENT_PREFIX},
        CustomFieldsTemplate.JOB_OPENING_MODEL: {"label": "Job opening", "prefix": JOB_OPENING_PREFIX},
        CustomFieldsTemplate.TASK_GROUP_MODEL: {"label": "Project", "prefix": TASK_GROUP_PREFIX},
        CustomFieldsTemplate.TASK_MODEL: {"label": "Task", "prefix": TASK_PREFIX},
        CustomFieldsTemplate.WORK_ITEM_MODEL: {"label": "Payment_Line_Items_Item", "prefix": "work_item."},
        "TaskTemplate": {"label": "Task", "prefix": TASK_TEMPLATE_PREFIX, "field_template": "{{terms[{model_label}]}} template - {label}"},
    }
    prefix = config[custom_fields_model]["prefix"]
    model_label = config[custom_fields_model]["label"]
    field_template = config[custom_fields_model].get("field_template", default_field_template)
    global_custom_fields_model = CustomFieldsTemplate.TASK_MODEL if custom_fields_model == "TaskTemplate" else custom_fields_model
    terms = connection.tenant.tenant_terms
    fields = []
    for key, label in GLOBAL_FIELD_MAPPING_CHOICES:
        if key.startswith(prefix):
            fields.append((key, label.format(terms=terms)))
    global_custom_fields = CustomFieldsTemplate.objects.filter(model=global_custom_fields_model).first()
    if global_custom_fields:
        field_qs = global_custom_fields.template_fields.exclude(type__in=excluded_custom_field_types).exclude(
            field_group__startswith=OTHER_VALUE_IDENTIFIER
        )
        if for_vendors_only:
            field_qs = field_qs.exclude(visible_to_vendors=False)
        for field in field_qs:
            formatted_field = field_template.format(model_label=model_label, label=field.label)
            fields.append((f"template_field.{field.id}", formatted_field.format(terms=terms)))
    return fields


def field_mapping_custom_rates(mapper=None):
    from preferences.models import CustomRateType

    if mapper is None:

        def mapper(item, key):
            return key, item.label

    fields = []
    for rate in CustomRateType.objects.filter(enabled_for_tenant=True).order_by("name"):
        fields.append(mapper(rate, f"custom_rate.{rate.id}"))
    return fields


def field_mapping_custom_vendor_fields(excluded_custom_field_types=(), mapper=None):
    from preferences.models import CustomVendorField

    if mapper is None:

        def mapper(item, key):
            return key, f"Custom field - {item.name}"

    fields = []
    for field in CustomVendorField.objects.exclude(type__in=excluded_custom_field_types).order_by("name"):
        fields.append(mapper(field, f"custom_field.{field.id}"))
    return fields


def field_mapping_custom_vendor_parent_fields(excluded_custom_field_types=(), mapper=None):
    from preferences.models import CustomVendorField

    if mapper is None:

        def mapper(item, key):
            return key, f"(Parent Vendor) Custom field - {item.name}"

    fields = []
    for field in CustomVendorField.objects.exclude(type__in=excluded_custom_field_types).order_by("name"):
        fields.append(mapper(field, f"custom_p_field.{field.id}"))
    return fields


def field_mapping_task_fields(excluded_custom_field_types=(), for_vendors_only=False):
    from preferences.models import CustomFieldsTemplate

    return field_mapping_for(CustomFieldsTemplate.TASK_MODEL, excluded_custom_field_types, for_vendors_only=for_vendors_only)


def field_mapping_task_group_fields(excluded_custom_field_types=(), for_vendors_only=False):
    from preferences.models import CustomFieldsTemplate

    return field_mapping_for(CustomFieldsTemplate.TASK_GROUP_MODEL, excluded_custom_field_types, for_vendors_only=for_vendors_only)


def field_mapping_contract_fields(excluded_custom_field_types=(), for_vendors_only=False):
    from preferences.models import CustomFieldsTemplate

    return field_mapping_for(CustomFieldsTemplate.CONTRACT_MODEL, excluded_custom_field_types, for_vendors_only=for_vendors_only)


def field_mapping_work_item_fields(excluded_custom_field_types=(), for_vendors_only=False):
    from preferences.models import CustomFieldsTemplate

    return field_mapping_for(CustomFieldsTemplate.WORK_ITEM_MODEL, excluded_custom_field_types, for_vendors_only=for_vendors_only)


def field_mapping_job_opening_fields(excluded_custom_field_types=(), for_vendors_only=False):
    from preferences.models import CustomFieldsTemplate

    return field_mapping_for(CustomFieldsTemplate.JOB_OPENING_MODEL, excluded_custom_field_types, for_vendors_only=for_vendors_only)


def field_mapping_task_template_fields(excluded_custom_field_types=(), for_vendors_only=False):
    return field_mapping_for("TaskTemplate", excluded_custom_field_types, for_vendors_only=for_vendors_only)


def field_mapping_payment_fields(excluded_custom_field_types=(), for_vendors_only=False):
    from preferences.models import CustomFieldsTemplate

    return field_mapping_for(CustomFieldsTemplate.PAYMENT_MODEL, excluded_custom_field_types, for_vendors_only=for_vendors_only)


def field_mapping_custom_fields_templates(excluded_custom_field_types=(), mapper=None):
    # Templates created by buyer located in `Settings/Templates`
    from preferences.models import CustomFieldsTemplate

    if mapper is None:

        def mapper(item, template_instance, key):
            return key, f"{template_instance.name} - {item.label}"

    fields = []
    for template in (
        CustomFieldsTemplate.objects.exclude(model__isnull=False)
        .exclude_autogenerated_templates()
        .order_by("name")
        .prefetch_related(
            Prefetch("template_fields", queryset=CustomField.objects.exclude(type__in=excluded_custom_field_types), to_attr="temp_fields")
        )
    ):
        for field in template.temp_fields:
            fields.append(mapper(field, template, f"template_field.{field.id}"))
    return fields


def field_mapping_from_workflow(workflow, excluded_custom_field_types=()):
    from onboarding.models import OnBoardingStage
    from preferences.models import CustomFieldsTemplate

    fields = []
    profile_field_keys = OnBoardingStage.available_fields(with_vendor_hidden=True)
    for template in (
        CustomFieldsTemplate.objects.filter(onboarding_stage__workflow=workflow).order_by("name").prefetch_related("onboarding_stage")
    ):
        for field in template.template_fields.exclude(type__in=excluded_custom_field_types).select_related(
            "vendor_field_propagator", "vendor_field_propagator__custom_vendor_field"
        ):
            if not field.propagator or (
                not field.propagator.profile_field
                and (not field.propagator.custom_vendor_field or field.propagator.custom_vendor_field.field_key not in profile_field_keys)
            ):
                fields.append((f"onboarding_stage.{field.id}", f"{template.onboarding_stage.first().name} - {field.label}"))
    return fields


def field_mapping_choices_user_fields(
    include_job_opening_fields=True, include_task_fields=True, include_contract_fields=True
) -> list[tuple[str, str]]:
    cache_key = ("users fields",) + tuple(locals().values())
    cached = get_from_tenant_per_request_cache("_field_mapping_choices", cache_key)
    if cached is not None:
        return cached

    result = [
        ("", ""),  # empty choice
    ]

    custom_fields_template_models = []
    if include_task_fields:
        terms = connection.tenant.tenant_terms
        custom_fields_template_models.append(CustomFieldsTemplate.TASK_MODEL)
        for key in ("task.managers", "task_template.managers"):
            label = USER_MAPPING_TO_DESC[key]
            result.append((key, label.format(terms=terms)))

    if include_job_opening_fields:
        custom_fields_template_models.append(CustomFieldsTemplate.JOB_OPENING_MODEL)

    if include_contract_fields:
        custom_fields_template_models.append(CustomFieldsTemplate.CONTRACT_MODEL)

    if custom_fields_template_models:
        custom_fields = CustomField.active.filter(
            type=CustomField.TYPE_USER, template__model__in=custom_fields_template_models
        ).select_related("template")
        for field in custom_fields:
            result.append((f"template_field.{field.id}", f"{field.template.name} - {field.label}"))

    set_tenant_per_request_cache("_field_mapping_choices", cache_key, result)

    return result


def custom_vendor_field_choices():
    from preferences.models import CustomVendorField

    cache_key = "custom_vendor_field_choices_" + str(connection.tenant.pk)
    result = cache.get(cache_key)
    if not result:
        result = []
        for field in CustomVendorField.active.all().order_by("name"):
            result.append((field.id, f"Custom field - {field.name}"))
        for field in CustomVendorField.objects.filter(enabled=False).order_by("name"):
            result.append((field.id, f"Custom field - {field.name} #DISABLED in CVF"))
        cache.set(cache_key, result, 30)
    return result


def field_mapping_choices_for_content_rendering(workflow=None):
    from notifications.emails import STATIC_EMAIL_ATTRIBUTES
    from preferences.models import CustomField

    include_job_opening_fields = True
    if workflow and workflow.category == workflow.CATEGORY_CONTRACT:
        include_job_opening_fields = False

    result = [(item, STATIC_EMAIL_ATTRIBUTES[item]["label"]) for item in STATIC_EMAIL_ATTRIBUTES]
    not_supported_types = CustomField.CONTENT_TYPES + (CustomField.TYPE_FILES, CustomField.TYPE_NOTE)
    field_mapping = field_mapping_choices(
        custom_fields_from_workflow=workflow,
        include_job_opening_fields=include_job_opening_fields,
        include_payment_fields=False,
        include_task_fields=False,
        excluded_custom_field_types=not_supported_types,
    )[1:]
    for key, label in field_mapping:
        if key in ("id", "slug", "external_id"):
            continue
        else:
            result.append((key, label))
    return result


def get_mapped_field_values_for_content_rendering(vendor, workflow_for_vendor):
    from notifications.emails import STATIC_EMAIL_ATTRIBUTES

    values = {}
    workflow_context = workflow_for_vendor.context
    workflow_data = workflow_for_vendor.get_custom_fields_data_from_stages
    mapping_fields = field_mapping_choices_for_content_rendering(workflow_for_vendor.workflow)
    for field, label in mapping_fields:
        if field in STATIC_EMAIL_ATTRIBUTES:
            continue

        if field.startswith(ONBOARDING_STAGE_PREFIX):
            context_obj = workflow_data
        else:
            context_obj = workflow_context.context_object

        values[field] = get_mapped_vendors_value(field, vendor, context_obj, for_email=True) or r"N\A"

    return values


def get_from_tenant_per_request_cache(cache_attr: str, cache_key: Any) -> Any | None:
    if not hasattr(connection.tenant, cache_attr):
        setattr(connection.tenant, cache_attr, {})

    return getattr(connection.tenant, cache_attr).get(cache_key)


def set_tenant_per_request_cache(cache_attr: str, cache_key: Any | None, value: Any) -> None:
    cache_dict = getattr(connection.tenant, cache_attr, None) or {}
    cache_dict[cache_key] = value
    setattr(connection.tenant, cache_attr, cache_dict)
