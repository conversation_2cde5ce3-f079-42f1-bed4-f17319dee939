from datetime import timedelta
from django.db import models
from django.utils import timezone
from shortlist.utils import generate_key
from vendors.models import VendorType


class VendorPublicLinkManager(models.Manager):
    def generate(self, vendor, user):
        return self.create(vendor=vendor, public_key=generate_key(), created_by=user)

    def get_public(self, vendor_pk, public_key):
        try:
            time_threshold = timezone.now() - timedelta(days=VendorPublicLink.DAYS_VALID)
            return self.get(vendor__pk=vendor_pk, public_key=public_key, created_at__gt=time_threshold)
        except VendorPublicLink.DoesNotExist:
            return False


class VendorPublicLink(models.Model):
    DAYS_VALID = 7

    vendor = models.ForeignKey('vendors.Vendor', related_name='+', on_delete=models.CASCADE)
    public_key = models.CharField(max_length=8, null=False, blank=False)
    created_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey('users.User', related_name='+', on_delete=models.CASCADE)
    vendor_type = models.ForeignKey('vendors.VendorType', related_name='+', null=True, blank=True, on_delete=models.CASCADE)

    class Meta:
        app_label = 'vendors'

    objects = VendorPublicLinkManager()

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None):
        self.vendor_type = VendorType.objects.enabled_or_default(self.vendor_type)
        return super().save(force_insert=False, force_update=False, using=None, update_fields=None)
