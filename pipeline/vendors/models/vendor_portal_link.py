from django.db import models

from shortlist.db_fields import URL_FIELD_LENGTH
from vendors.models import VendorType


class PortalSection(models.TextChoices):
    DASHBOARD = 'dashboard'
    WORKERS = 'workers'
    MARKETPLACE = 'marketplace'
    TASKS = 'tasks'
    PAYMENTS = 'payments'
    ONBOARDING = 'onboarding'
    DOCUMENTS = 'documents'
    CONTRACTS = 'contracts'


class VendorPortalLink(models.Model):
    LINK_TYPE_STATIC = 'static_url'

    LINK_TYPE_CHOICES = ((LINK_TYPE_STATIC, LINK_TYPE_STATIC),)

    section = models.CharField(default=PortalSection.DASHBOARD, max_length=255, null=False, blank=False, choices=PortalSection.choices)
    label = models.CharField(max_length=100, null=False, blank=False)
    link_type = models.CharField(max_length=50, choices=LINK_TYPE_CHOICES, default=LINK_TYPE_STATIC)
    url = models.Char<PERSON>ield(max_length=URL_FIELD_LENGTH, null=False, blank=False)
    order = models.IntegerField(default=1, db_index=True)
    vendor_type = models.ForeignKey(VendorType, null=True, blank=True, on_delete=models.CASCADE, related_name="+")

    class Meta:
        ordering = ['order']
        app_label = 'vendors'
