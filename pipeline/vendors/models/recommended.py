from django.db import models
from django.db.models import Max


class Recommended(models.Model):
    public_id = models.CharField(max_length=100, null=True, blank=True, unique=True)
    order = models.IntegerField(default=0, db_index=True)

    class Meta:
        app_label = 'vendors'
        ordering = ('order', 'id')

    def save(self, *args, **kwargs):
        order = Recommended.objects.all().aggregate(Max('order')).get('order__max')
        self.order = 0 if order is None else order + 1
        super().save(*args, **kwargs)
