from contextlib import contextmanager

from django.contrib import auth
from django.utils import timezone
from parameterized import parameterized
from rest_framework import status

from clients import features
from shortlist.tests.helpers import TenantTestCase, features_enabled
from vendors.factories import VendorFactory, VendorSignUpFactory
from vendors.models import VendorSignUp, Vendor


class VendorEmailConfirmationApiTest(TenantTestCase):
    create_user = False
    api_url = '/api/public/confirm_email/'

    def setUp(self):
        super().setUp()
        self.vendor_1 = VendorFactory(email_verified=False)
        self.vendor_2 = VendorFactory(email_verified=False)
        self.other_vendor = VendorFactory(email_verified=False)
        self.archived_vendor = VendorFactory(archived=True, email_verified=False)
        VendorSignUpFactory(vendor=self.other_vendor)
        VendorSignUpFactory(vendor=self.archived_vendor)

    @contextmanager
    def not_confirmed_yet_vendor_signup(self, **vendor_signup_kwargs):
        yield VendorSignUpFactory(confirmed_at=None, **vendor_signup_kwargs).confirmation_key

    @contextmanager
    def already_confirmed_vendor_signup(self, **vendor_signup_kwargs):
        yield VendorSignUpFactory(confirmed_at=timezone.now(), **vendor_signup_kwargs).confirmation_key

    @parameterized.expand(
        [
            ({},),
            ({"vendor_email": "<EMAIL>"},),
            ({"code": "123"},),
        ]
    )
    def test_missing_params_responds_with_404(self, payload):
        self._test_get_request(payload, status.HTTP_404_NOT_FOUND)
        self._test_post_request(payload, status.HTTP_404_NOT_FOUND)

    def test_email_that_not_match_any_vendor_responds_with_404(self):
        payload = {"vendor_email": "<EMAIL>", "code": "123"}
        self._test_get_request(payload, status.HTTP_404_NOT_FOUND)
        self._test_post_request(payload, status.HTTP_404_NOT_FOUND)

    def test_email_belonging_to_archived_vendor_responds_with_404(self):
        payload = {"vendor_email": self.archived_vendor.email, "code": "123"}
        self._test_get_request(payload, status.HTTP_404_NOT_FOUND)
        self._test_post_request(payload, status.HTTP_404_NOT_FOUND)

    @features_enabled([], features_to_disable=[features.VENDOR_AUTO_LOGIN])
    def test_correct_payload_with_not_confirmed_yet_vendor_signup_activates_vendor_without_logging_him(self):
        with self.not_confirmed_yet_vendor_signup(vendor=self.vendor_1) as confirmation_key:
            payload = {"vendor_email": self.vendor_1.email, "code": confirmation_key}
            get_response = self._test_get_request(payload, status.HTTP_302_FOUND)
            self.assertEqual(get_response.url, "/login/")
            self.vendor_1.refresh_from_db()
            self.assertTrue(self.vendor_1.email_verified)
            self.assertNotEqual(auth.get_user(self.api_client), self.vendor_1.first_contact)

        with self.not_confirmed_yet_vendor_signup(vendor=self.vendor_2) as confirmation_key:
            payload = {"vendor_email": self.vendor_2.email, "code": confirmation_key}
            get_response = self._test_get_request(payload, status.HTTP_302_FOUND)
            self.assertEqual(get_response.url, "/login/")
            self.vendor_2.refresh_from_db()
            self.assertTrue(self.vendor_2.email_verified)
            self.assertNotEqual(auth.get_user(self.api_client), self.vendor_2.first_contact)

    @features_enabled([features.VENDOR_AUTO_LOGIN])
    def test_get_correct_payload_activates_and_auto_logins_vendor_if_auto_login_is_enabled(self):
        with self.not_confirmed_yet_vendor_signup(vendor=self.vendor_1) as confirmation_key:
            payload = {"vendor_email": self.vendor_1.email, "code": confirmation_key}
            self._test_get_request(payload)
            self.vendor_1.refresh_from_db()
            self.assertTrue(self.vendor_1.email_verified)
            self.assertEqual(auth.get_user(self.api_client), self.vendor_1.first_contact)

    @features_enabled([features.VENDOR_AUTO_LOGIN])
    def test_post_correct_payload_activates_and_auto_logins_vendor_if_auto_login_is_enabled(self):
        with self.not_confirmed_yet_vendor_signup(vendor=self.vendor_2) as confirmation_key:
            payload = {"vendor_email": self.vendor_2.email, "code": confirmation_key}
            self._test_post_request(payload)
            self.vendor_2.refresh_from_db()
            self.assertTrue(self.vendor_2.email_verified)
            self.assertEqual(auth.get_user(self.api_client), self.vendor_2.first_contact)

    def test_no_vendor_signups_redirects_to_root_uri(self):
        payload = {"vendor_email": self.vendor_1.email, "code": "123"}
        get_response = self._test_get_request(payload, status.HTTP_302_FOUND)
        self.assertEqual(get_response.url, "/")
        post_response = self._test_post_request(payload, status.HTTP_302_FOUND)
        self.assertEqual(post_response.url, "/")

    def test_correct_payload_with_already_confirmed_vendor_signup_redirects_to_root_uri(self):
        with self.already_confirmed_vendor_signup(vendor=self.vendor_1) as confirmation_key:
            payload = {"vendor_email": self.vendor_1.email, "code": confirmation_key}
            get_response = self._test_get_request(payload, status.HTTP_302_FOUND)
            self.assertEqual(get_response.url, "/")

        with self.already_confirmed_vendor_signup(vendor=self.vendor_2) as confirmation_key:
            payload = {"vendor_email": self.vendor_2.email, "code": confirmation_key}
            post_response = self._test_post_request(payload, status.HTTP_302_FOUND)
            self.assertEqual(post_response.url, "/")

    def test_invalid_code_increases_invalid_tries_and_redirects_to_root_uri(self):
        with self.not_confirmed_yet_vendor_signup(vendor=self.vendor_1) as confirmation_key:
            payload = {"vendor_email": self.vendor_1.email, "code": "INVALID"}
            get_response = self._test_get_request(payload, status.HTTP_302_FOUND)
            self.assertEqual(get_response.url, "/")
            vendor_signup = VendorSignUp.objects.get(vendor=self.vendor_1, confirmation_key=confirmation_key)
            self.assertEqual(vendor_signup.invalid_tries, 1)
            self.vendor_1.refresh_from_db()
            self.assertFalse(self.vendor_1.email_verified)

        with self.not_confirmed_yet_vendor_signup(vendor=self.vendor_2) as confirmation_key:
            payload = {"vendor_email": self.vendor_2.email, "code": "INVALID"}
            post_response = self._test_post_request(payload, status.HTTP_302_FOUND)
            self.assertEqual(post_response.url, "/")
            vendor_signup = VendorSignUp.objects.get(vendor=self.vendor_2, confirmation_key=confirmation_key)
            self.assertEqual(vendor_signup.invalid_tries, 1)
            self.vendor_2.refresh_from_db()
            self.assertFalse(self.vendor_2.email_verified)

    def test_invalid_code_used_too_many_times_removes_account_and_redirects_to_root_uri(self):
        with self.not_confirmed_yet_vendor_signup(vendor=self.vendor_1, invalid_tries=4):
            payload = {"vendor_email": self.vendor_1.email, "code": "INVALID"}
            get_response = self._test_get_request(payload, status.HTTP_302_FOUND)
            self.assertEqual(get_response.url, "/")
            self.assertFalse(Vendor.objects.filter(pk=self.vendor_1.pk).exists())
            self.assertFalse(VendorSignUp.objects.filter(vendor=self.vendor_1).exists())

        with self.not_confirmed_yet_vendor_signup(vendor=self.vendor_2, invalid_tries=4):
            payload = {"vendor_email": self.vendor_2.email, "code": "INVALID"}
            post_response = self._test_post_request(payload, status.HTTP_302_FOUND)
            self.assertEqual(post_response.url, "/")
            self.assertFalse(Vendor.objects.filter(pk=self.vendor_2.pk).exists())
            self.assertFalse(VendorSignUp.objects.filter(vendor=self.vendor_2).exists())

    def _test_get_request(self, payload, expected_status_code=status.HTTP_200_OK):
        api_url = f"{self.api_url}{payload.get('vendor_email', '')}/{payload.get('code', '')}/"
        return self.check_response(expected_status_code, self.api_client.get(api_url))

    def _test_post_request(self, payload, expected_status_code=status.HTTP_200_OK):
        return self.check_response(expected_status_code, self.api_client.post(self.api_url, payload))
