# Generated by Django 3.2.24 on 2024-03-08 11:40

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('vendors', '0043_vendorbankdetailsshortlistpay_blocked_at'),
    ]

    operations = [
        migrations.AddField(
            model_name='vendorbankdetailsshortlistpay',
            name='frozen_at',
            field=models.DateTimeField(
                blank=True,
                help_text='In frozen method details of method that were used for payments processing are immutable, even if method was changed on operator side or on Pay side.',
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name='vendorbankdetailsshortlistpay',
            name='blocked_at',
            field=models.DateTimeField(blank=True, help_text="Blocked method cannot be changed until it's unblocked", null=True),
        ),
    ]
