from django.utils import timezone

from clients import features
from shortlist.celery import app
from shortlist.permissions import check_permission, TenantFeaturesEnabled
from vendors.models import VendorBulkAction


def permission_check(request, view, obj=None):
    check_permission(TenantFeaturesEnabled(features.MARKETPLACE), request, view, obj)


def validate_input(**kwargs):
    errors = []
    return errors


@app.task()
def complete_job_opening_task(bulk_action_id):
    from onboarding.models import OnBoardingContext
    from openings.models import JobOpening

    bulk_action = VendorBulkAction.objects.get(pk=bulk_action_id)
    context_name = bulk_action.action_params.get('context_name')
    context = OnBoardingContext.objects.filter(name=context_name).first()
    if context.context_type == OnBoardingContext.CONTEXT_TYPE_JOB_OPENING:
        job_opening = context.context_object
        if job_opening.status in [JobOpening.DRAFT, JobOpening.PENDING, JobOpening.PUBLISHED]:
            job_opening.status = JobOpening.COMPLETED
            job_opening.save()
    bulk_action.ended_at = timezone.now()
    bulk_action.save()
