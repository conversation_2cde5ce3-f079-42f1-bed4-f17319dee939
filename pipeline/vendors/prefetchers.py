from django.apps import apps
from prefetch import Prefetcher


class VendorQuestionAnswersPrefetcher(Prefetcher):
    def filter(self, ids):
        return apps.get_model('vendors', 'VendorQuestion').objects.filter(parent__in=ids)

    def reverse_mapper(self, answer):
        return [answer.parent_id]

    def decorator(self, question, answers=()):
        answers = sorted(answers, key=lambda answer: answer.posted_at)
        question.answers_reverse = answers
