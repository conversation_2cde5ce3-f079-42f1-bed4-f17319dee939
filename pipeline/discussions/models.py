import re
from typing import Any

from pydantic_core import CoreSchema, core_schema

from django.apps import apps
from django.contrib.postgres.fields import ArrayField
from django.core.validators import RegexValidator
from django.db import models, transaction
from django.db.models.signals import post_save
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from discussions.tasks import recalculate_message_count, trigger_aggregated_notification_about_new_messages
from events.event_types import MessagePosted
from shortlist.current_user import get_actor_for_current_user, get_current_request, get_current_user
from shortlist.permissions import Permission
from users.models import Actor, User


"""
MessageBoards HOWTO:

First - you need to add new static values into BOARDS dictionary.
Second - model for which you want MessageBoard, should inherits from `Board` class (you can check there which methods
should be overwritten)

WARNING - reading messages by threads will update all messages in specific MessageBoard as read.

"""

INVALID_TYPE_ERROR_MESSAGE = _('Enter a valid message type.')
MESSAGE_TYPE_PATTERN = re.compile(r'\w+:\d+:?\w*', re.UNICODE)

BOARDS = {  # need to use lazy models to avoid problems with imports
    'task': ['tasks', 'Task'],
    'taskgroup': ['tasks', 'TaskGroup'],
    'jobopening': ['openings', 'JobOpening'],
    'vendorworkflow': ['onboarding', 'OnBoardingWorkflowsForVendor'],
    'vendor': ['vendors', 'Vendor'],
    'signrequest': ['deals', 'SignRequest'],
}


OBJECT_PERMISSIONS = {
    'task': {'': ['task']},
    'taskgroup': {'': ['task group']},
    'jobopening': {'': ['job opening']},
    'vendorworkflow': {'': ['onboarding template'], 'vendorboard': ['onboarding template']},
    'vendor': {'': ['vendor']},
    'signrequest': {'': 'agreement'},
}


OBJECT_TYPE_NAMES = {  # used in email aka: `<UserName> posted a message in <object_type_name>`
    # Variables are translated using tenant terms while email is prepared in emails.py
    'task': {'': '[task]'},
    'taskgroup': {'': '[project]'},
    'jobopening': {'': '[Job opening]'},
    'vendorworkflow': {'': '[Onboarding]', 'vendorboard': '[Onboarding]'},
    'vendor': {'': '[Partner] profile'},
    'signrequest': {'': 'Agreement'},
}

OBJECT_TYPES_WITH_AGGREGATED_NOTIFICATIONS = {'signrequest'}
AGGREGATED_NOTIFICATIONS_PERIOD_IN_MINUTES = 3

reg = re.compile(r'(?P<obj_type>\w+):(?P<obj_id>\d+):?(?P<board_name>\w*)')


def split_message_type(message_type):
    r = re.match(reg, message_type).groupdict()
    return r['obj_type'], int(r['obj_id']), r['board_name']


def object_from_message_type(message_type):
    obj_type, obj_id, _ = split_message_type(message_type)
    if obj_type not in BOARDS:
        raise NotImplementedError("Object related to this message is no longer supported or message_type is broken")
    else:
        return apps.get_model(*BOARDS[obj_type]).objects.filter(id=obj_id).first()


def object_type_name_from_message_type(message_type):
    obj_type, _, board_name = split_message_type(message_type)
    if obj_type not in OBJECT_TYPE_NAMES:
        raise NotImplementedError("Object related to this message is no longer supported or message_type is broken")
    else:
        return OBJECT_TYPE_NAMES[obj_type].get(board_name)


def get_unread_total_messages(message_type, vendor_id=None):
    unread_messages = 0

    with transaction.atomic():
        if vendor_id:
            object_message_counter, created = ObjectMessageCounterForVendor.objects.get_or_create(
                message_type=message_type, vendor_id=vendor_id
            )
        else:
            object_message_counter, created = ObjectMessageCounter.objects.get_or_create(message_type=message_type)

        if created:
            if vendor_id:
                message_count = Message.objects.filter(message_type=message_type, vendor_id=vendor_id).count()
            else:
                message_count = Message.objects.filter(message_type=message_type).count()

            if object_message_counter.message_count != message_count:
                object_message_counter.message_count = message_count
                object_message_counter.save()

        if object_message_counter.message_count > 0:
            personal_message_counter = (
                PersonalMessageCounter.objects.filter(message_type=message_type, actor=get_actor_for_current_user())
                .only("read_count")
                .first()
            )

            if personal_message_counter:
                unread_messages = object_message_counter.message_count - personal_message_counter.read_count

    return unread_messages, object_message_counter.message_count


def get_unread_total_messages_for_list_view(message_types, vendor_id=None):
    """
    Get counter of current user unread messages and message counters.

    Required param:
    message_types = ["task:10", "task:22", "task:45"]

    Returns two dicts:
    personal_message_counters = {"task:10": 0, "task:22": 2, "task:45": 1}
    message_counters = {"task:10": 0, "task:22": 10, "task:45": 1}
    """
    with transaction.atomic():
        message_counters = dict.fromkeys(message_types, 0)
        personal_message_counters = dict.fromkeys(message_types, 0)
        if vendor_id:
            counters = ObjectMessageCounterForVendor.objects.filter(
                message_type__in=message_types, vendor_id=vendor_id, message_count__gt=0
            )
        else:
            counters = ObjectMessageCounter.objects.filter(message_type__in=message_types, message_count__gt=0)
        for counter in counters:
            message_counters[counter.message_type] = counter.message_count

        for personal_counter in PersonalMessageCounter.objects.filter(
            message_type__in=message_types, actor=get_actor_for_current_user(), read_count__gt=0
        ):
            personal_message_counters[personal_counter.message_type] = personal_counter.read_count

        for key, value in message_counters.items():
            if value > 0 and value != personal_message_counters[key]:
                personal_message_counters[key] = value - personal_message_counters[key]

    return personal_message_counters, message_counters


class Board:
    """Class from which should inherit every model for which we want Message Board - helps avoid mistakes"""

    def message_recipients(self, board_name):
        raise NotImplementedError("Method message_recipients isn't declared")

    def message_board_url(self, board_name, user):
        raise NotImplementedError("Method message_board_url isn't declared")

    def new_message_allowed(self, board_name):
        raise NotImplementedError("Method new_message_allowed isn't declared")

    @property
    def can_edit(self):
        raise NotImplementedError("Property can_edit isn't declared")

    @property
    def notify_about_new_message_immediately(self):
        return self.__class__.__name__.lower() not in OBJECT_TYPES_WITH_AGGREGATED_NOTIFICATIONS


class Message(models.Model):
    class Meta:
        ordering = ('created_at',)  # when messages will need more levels, this need to be changed

    message_type = models.CharField(
        max_length=50,
        validators=[RegexValidator(MESSAGE_TYPE_PATTERN, INVALID_TYPE_ERROR_MESSAGE, 'invalid')],
        db_index=True,
    )
    vendor_id = models.IntegerField(null=True, blank=True, db_index=True)
    content = models.TextField()
    parent_message = models.ForeignKey('Message', null=True, blank=True, on_delete=models.CASCADE)
    quoted_text = models.TextField(blank=True)
    created_at = models.DateTimeField(editable=False, db_index=True)
    created_by = models.ForeignKey(
        Actor, null=True, default=get_actor_for_current_user, on_delete=models.SET_NULL, related_name='message_created_by'
    )
    updated_at = models.DateTimeField(editable=False)
    updated_by = models.ForeignKey(Actor, null=True, on_delete=models.SET_NULL, related_name='message_updated_by')
    deleted_at = models.DateTimeField(null=True, blank=True)
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolved_by = models.ForeignKey(Actor, null=True, on_delete=models.SET_NULL, related_name='message_resolved_by')
    secured = models.BooleanField(default=False)
    mention = ArrayField(models.TextField(), null=True, blank=True)

    def save(self, *args, **kwargs):
        if not self.id:
            self.created_at = timezone.now()
        self.updated_at = timezone.now()
        return super().save(*args, **kwargs)

    @property
    def object_for(self):
        return object_from_message_type(self.message_type)

    @property
    def object_type_name(self):
        return object_type_name_from_message_type(self.message_type)

    @property
    def can_edit(self):
        request = get_current_request()
        current_user = get_current_user()
        if self.secured and not Permission.buyer('message', 'edit secured').has_permission(request, None):
            return False
        if current_user.is_vendor:
            return (
                Permission.vendor('message', 'edit').has_object_permission(request, None, self) and self.created_by == current_user.actor
            )  # need to check as long as we have Vendor role 'shortlisted'
        else:
            return Permission.buyer('message', 'edit').has_object_permission(request, None, self)

    @property
    def message_content(self):
        result = self.content
        if self.mentioned_users and '@' in result:
            slug_to_name = {f'@{user.slug}': f'{user.full_name}' for user in self.mentioned_users}
            slugs = list(slug_to_name.keys())
            slugs.sort(key=len, reverse=True)
            for slug in slugs:
                result = result.replace(slug, slug_to_name.get(slug))
        return result

    @property
    def message_recipients(self):
        _, _, board_name = split_message_type(self.message_type)
        recipients = self.object_for.message_recipients(board_name)
        return recipients.union(self.mentioned_users)

    @property
    def mentioned_users(self):
        if self.mention:
            return set(User.objects.filter(slug__in=self.mention))
        return set()

    def message_board_url(self, user=None):
        _, _, board_name = split_message_type(self.message_type)
        user = user or get_current_user()
        board_url = self.object_for.message_board_url(board_name, user)
        delimiter = "&" if "?" in board_url else "?"
        return f"{board_url}{delimiter}message_id={self.id}"

    @property
    def get_created_by(self):
        return self.created_by.user

    @classmethod
    def __get_pydantic_core_schema__(cls, source_type: str, handler: Any) -> CoreSchema:
        return core_schema.any_schema()


def message_post_save(sender, instance, created, raw=False, **kwargs):
    if created:
        # because counting happens in async task (MessagePosted.watch) we want to improve user experience and blindly add +1 for
        # ObjectMessageCounter, ObjectMessageCounterForVendor and PersonalMessageCounter
        # in case if api asks for those counters BEFORE async task finish
        with transaction.atomic():
            current_user = get_current_user()
            if current_user and current_user.is_vendor:
                omc, _ = ObjectMessageCounterForVendor.objects.get_or_create(
                    message_type=instance.message_type, vendor_id=get_current_user().vendor.id
                )
            else:
                omc, _ = ObjectMessageCounter.objects.get_or_create(message_type=instance.message_type)
            omc.message_count += 1
            omc.save()
            pmc, _ = PersonalMessageCounter.objects.get_or_create(message_type=instance.message_type, actor=get_actor_for_current_user())
            pmc.read_count += 1
            pmc.save()

        object_for = instance.object_for
        if hasattr(object_for, 'vendor_id_for_messages'):
            Message.objects.filter(id=instance.id).update(vendor_id=object_for.vendor_id_for_messages)

        MessagePosted(
            message=instance,
            message_board_url=instance.message_board_url(),
            created_by_name=instance.get_created_by.full_name,
            object_name=object_for.name,
            object_type=instance.object_type_name,
        )
    else:
        sender.objects.filter(pk=instance.pk).update(updated_by=get_actor_for_current_user())
        if instance.deleted_at is not None:
            recalculate_message_count.delay(instance.pk)


post_save.connect(message_post_save, Message, dispatch_uid="message_post_save.post_save")


@MessagePosted.watch
def request_for_recalculate_message_count(event):
    recalculate_message_count.delay(event.message.pk)  # should be delayed?


@MessagePosted.watch
def schedule_aggregated_notification_about_new_messages(event: MessagePosted):
    if not event.message.object_for.notify_about_new_message_immediately:
        trigger_aggregated_notification_about_new_messages.apply_async(
            countdown=AGGREGATED_NOTIFICATIONS_PERIOD_IN_MINUTES * 60,
            kwargs={"message_id": event.message.pk},
        )


class PersonalMessageCounter(models.Model):
    message_type = models.CharField(
        max_length=50,
        validators=[RegexValidator(MESSAGE_TYPE_PATTERN, INVALID_TYPE_ERROR_MESSAGE, 'invalid')],
        db_index=True,
    )
    read_count = models.IntegerField(default=0)
    actor = models.ForeignKey(
        Actor, null=True, default=get_actor_for_current_user, on_delete=models.SET_NULL, related_name='personal_message_counter'
    )
    last_visit = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = (('message_type', 'actor'),)


class ObjectMessageCounter(models.Model):
    message_type = models.CharField(
        max_length=50,
        unique=True,
        validators=[RegexValidator(MESSAGE_TYPE_PATTERN, INVALID_TYPE_ERROR_MESSAGE, 'invalid')],
        db_index=True,
    )
    message_count = models.IntegerField(default=0)


class ObjectMessageCounterForVendor(models.Model):
    message_type = models.CharField(
        max_length=50,
        validators=[RegexValidator(MESSAGE_TYPE_PATTERN, INVALID_TYPE_ERROR_MESSAGE, 'invalid')],
        db_index=True,
    )
    vendor_id = models.IntegerField(db_index=True)
    message_count = models.IntegerField(default=0)

    class Meta:
        unique_together = (('message_type', 'vendor_id'),)
