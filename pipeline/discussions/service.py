from datetime import datetime

from django.db import models
from django.utils import timezone
from pydantic import BaseModel

from discussions.models import Message
from shortlist.pydantic_utils.dto_utils import ensure_dto, ensure_instance
from shortlist.result import Result
from users.models import User


class MessageDTO(BaseModel):
    message_type: str
    content: str
    parent_id: int | None = None
    quoted_text: str | None = None

    @classmethod
    def for_obj(cls, obj: models.Model, **kwargs):
        return cls(message_type=message_type_for_obj(obj), **kwargs)


def create_message(message_dto: MessageDTO | dict, user: "User") -> Result[Message]:
    message_dto = ensure_dto(message_dto, MessageDTO)

    if (
        message_dto.parent_id
        and not Message.objects.filter(message_type=message_dto.message_type, parent_message=message_dto.parent_id).exists()
    ):
        return Result.fail("Can not create message for object")

    message = Message.objects.create(
        content=message_dto.content,
        created_by=user.actor,
        message_type=message_dto.message_type,
        parent_message=message_dto.parent_id,
        vendor_id=user.vendor.pk if user.is_vendor else None,
        quoted_text=message_dto.quoted_text,
    )

    return Result.success(message)


def message_type_for_obj(obj: models.Model, board_name: str | None = None) -> str:
    message_type = f"{obj.__class__.__name__.lower()}:{obj.pk}"
    if board_name:
        message_type = f"{message_type}:{board_name}"

    return message_type


class ResolveMessageResultDTO(BaseModel):
    message_type: str
    id: int
    resolved_at: datetime | None


def resolve_message(message: Message | int, user: "User") -> Result[ResolveMessageResultDTO]:
    message = ensure_instance(message, Message).unwrap()

    if any((message.resolved_at, message.resolved_by)):
        return Result.fail("Message already resolved")

    message.resolved_at = timezone.now()
    message.resolved_by = user.actor
    message.save(update_fields=["resolved_at", "resolved_by"])

    return Result.success(ResolveMessageResultDTO(message_type=message.message_type, id=message.id, resolved_at=message.resolved_at))


def unresolve_message(message: Message | int) -> Result[ResolveMessageResultDTO]:
    message = ensure_instance(message, Message).unwrap()

    if not (message.resolved_at and message.resolved_by):
        return Result.fail("Message not resolved")

    message.resolved_at = message.resolved_by = None
    message.save(update_fields=["resolved_at", "resolved_by"])

    return Result.success(ResolveMessageResultDTO(message_type=message.message_type, id=message.id, resolved_at=message.resolved_at))


def get_discussion_participants(message_type: str) -> set:
    user_ids = set(Message.objects.filter(message_type=message_type).values_list("created_by__user_id", flat=True))
    return set(User.objects.active().filter(pk__in=user_ids))
