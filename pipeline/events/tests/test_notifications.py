from ..models import EventNotification
from .. import event_types

from rest_framework import status

from shortlist.tests.helpers import TenantTestCase


class TestModel(TenantTestCase):
    def test_triggering(self):
        self.assertEqual(EventNotification.objects.all().count(), 0)
        self.assertEqual(EventNotification.objects.latest_id(), 0)
        project = self._get_project()
        event_types.ProjectClosed(project=project, executor=None)
        self.assertEqual(EventNotification.objects.all().count(), 1)
        last = EventNotification.objects.latest()
        self.assertEqual(last.event_type, 'ProjectClosed')
        self.assertEqual(last.id, EventNotification.objects.latest_id())
        self.assertEqual(last.for_projects_team, project)
        self.assertEqual(last.for_all_buyers, False)
        self.assertEqual(last.for_user, None)


class TestAPI(TenantTestCase):
    with_login_user = True

    def setUp(self):
        super().setUp()
        self.user.last_seen_event = 0  # simulate login
        self.user.save()

    def test_counter(self):
        r = self.check_response(status.HTTP_200_OK, self.api_client.get('/api/notifications/unread/'))
        self.assertEqual(r.data, 0)
        project = self._get_project()
        event_types.ProjectClosed(project=project, executor=None)
        r = self.check_response(status.HTTP_200_OK, self.api_client.get('/api/notifications/unread/'))
        self.assertEqual(r.data, 1)
        r = self.check_response(status.HTTP_200_OK, self.api_client.post('/api/notifications/unread/'))
        r = self.check_response(status.HTTP_200_OK, self.api_client.get('/api/notifications/unread/'))
        self.assertEqual(r.data, 0)

    def test_paging(self):
        r = self.check_response(status.HTTP_200_OK, self.api_client.get('/api/notifications/'))
        self.assertEqual(r.data, [])
        project = self._get_project()
        event_types.ProjectClosed(project=project, executor=None)
        r = self.check_response(status.HTTP_200_OK, self.api_client.get('/api/notifications/'))
        self.assertEqual(len(r.data), 1)
        self.assertIsNotNone(r.data[0]['message'])

    def test_vendor_and_user_data(self):
        vendor = self._get_vendor()
        vendor.logo = 'some logo'
        vendor.save()
        event_types.VendorRegistrationCompleted(vendor=vendor, executor=vendor.first_contact)
        r = self.check_response(status.HTTP_200_OK, self.api_client.get('/api/notifications/'))
        self.assertEqual(len(r.data), 1)
        self.assertEqual(
            dict(r.data[0]['vendor']),
            {
                'name': vendor.name,
                'logo': vendor.logo,
                'is_company': vendor.is_company,
                'slug': vendor.slug,
                'email': vendor.email,
                'initials': vendor.first_contact.initials,
                'full_name': vendor.full_name,
                'avatar_color': vendor.first_contact.avatar_color,
            },
        )
        self.assertEqual(
            dict(r.data[0]['user']),
            {
                'avatar_color': vendor.first_contact.avatar_color,
                'deleted': False,
                'slug': vendor.first_contact.slug,
                'full_name': vendor.first_contact.full_name,
                'initials': vendor.first_contact.initials,
                'vendor_logo': vendor.logo,
                'vendor_slug': vendor.slug,
                'profile_picture_path': vendor.first_contact.profile_picture_path,
            },
        )

    def test_project_data(self):
        project = self._get_project()
        event_types.ProjectClosed(project=project, executor=None)
        r = self.check_response(status.HTTP_200_OK, self.api_client.get('/api/notifications/'))
        self.assertEqual(len(r.data), 1)
        self.assertEqual(
            dict(r.data[0]['project']),
            {
                'name': project.name,
                'status': project.status,
                'slug': project.slug,
            },
        )
