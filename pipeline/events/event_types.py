from events.base import EventBase


class AcceptContract(EventBase):
    def __init__(self, contract, accepting_user, **kwargs):
        EventBase.__init__(self, contract=contract, accepting_user=accepting_user, **kwargs)


class AcceptContractReminder(EventBase):
    def __init__(self, contract, accepting_user, **kwargs):
        EventBase.__init__(self, contract=contract, accepting_user=accepting_user, **kwargs)


class AgreementTemplateChanged(EventBase):
    def __init__(self, template, **kwargs):
        EventBase.__init__(self, template=template, **kwargs)


class AgreementTooLongValueError(EventBase):
    def __init__(self, template, vendor, field_name, **kwargs):
        EventBase.__init__(self, template=template, vendor=vendor, field_name=field_name, **kwargs)


class BankDetailsAdded(EventBase):
    def __init__(self, vendor, **kwargs):
        EventBase.__init__(self, vendor=vendor, **kwargs)


class BankDetailsChanged(EventBase):
    def __init__(self, vendor, **kwargs):
        EventBase.__init__(self, vendor=vendor, **kwargs)


class BankDetailsUpdateNotificationEmailTrigger(EventBase):
    def __init__(self, vendor, **kwargs):
        EventBase.__init__(self, vendor=vendor, **kwargs)


class BankDetailsSyncFailed(EventBase):
    def __init__(self, error_message, **kwargs):
        EventBase.__init__(self, error_message=error_message, **kwargs)


class BruteForceWarning(EventBase):
    def __init__(self, email, ip, module, **kwargs):
        EventBase.__init__(self, email=email, ip=ip, module=module, **kwargs)


class BulkActionFinished(EventBase):
    def __init__(self, bulk_action_data, **kwargs):
        super().__init__(bulk_action_data=bulk_action_data, **kwargs)


class BulkAddFailure(EventBase):
    def __init__(self, user, invalid_rows_errors, **kwargs):
        EventBase.__init__(self, user=user, invalid_rows_errors=invalid_rows_errors, **kwargs)


class BuyerAddedPaymentForTask(EventBase):
    def __init__(self, payment, vendor, task, **kwargs):
        EventBase.__init__(self, payment=payment, vendor=vendor, task=task, **kwargs)


class BuyerAdminSendsVendorInvitation(EventBase):
    def __init__(self, vendor, **kwargs):
        EventBase.__init__(self, vendor=vendor, **kwargs)


class BuyerAdminSendsVendorInvitationCustom(EventBase):
    def __init__(self, vendor, message_subject, message_content, reminder, **kwargs):
        EventBase.__init__(
            self, vendor=vendor, message_subject=message_subject, message_content=message_content, reminder=reminder, **kwargs
        )


class BuyerComment(EventBase):
    def __init__(self, project_reply, project, **kwargs):
        EventBase.__init__(self, project_reply=project_reply, project=project, **kwargs)


class BuyerLinkReset(EventBase):
    def __init__(self, user, **kwargs):
        EventBase.__init__(self, user=user, **kwargs)


class BuyerPostsNewPrivateMessage(EventBase):
    def __init__(self, message, vendor, **kwargs):
        EventBase.__init__(self, message=message, vendor=vendor, **kwargs)


class BuyerRemovesVendor(EventBase):
    def __init__(self, vendor, **kwargs):
        EventBase.__init__(self, vendor=vendor, **kwargs)


class BuyerRepliesPrivateMessage(EventBase):
    def __init__(self, message, vendor, **kwargs):
        EventBase.__init__(self, message=message, vendor=vendor, **kwargs)


class BuyerReply(EventBase):
    def __init__(self, project_reply, project, to_creator, **kwargs):
        EventBase.__init__(self, project_reply=project_reply, project=project, to_creator=to_creator, **kwargs)


class BuyerRequestsAvailabilityUpdate(EventBase):
    def __init__(self, vendors, message, **kwargs):
        EventBase.__init__(self, vendors=vendors, message=message, **kwargs)


class BuyerSendsADocument(EventBase):
    def __init__(self, document, vendor, **kwargs):
        EventBase.__init__(self, document=document, vendor=vendor, **kwargs)


class BuyerSentBulkMessage(EventBase):
    def __init__(self, message, vendor, subject, **kwargs):
        EventBase.__init__(self, message=message, vendor=vendor, subject=subject, **kwargs)


class BuyerSentDocumentForSignature(EventBase):
    def __init__(self, sign_request, vendor, document_name, custom_message, **kwargs):
        EventBase.__init__(
            self, sign_request=sign_request, vendor=vendor, document_name=document_name, custom_message=custom_message, **kwargs
        )


class BuyerSignup(EventBase):
    def __init__(self, new_user_email, new_business_name, signup_date, **kwargs):
        EventBase.__init__(self, new_user_email=new_user_email, new_business_name=new_business_name, signup_date=signup_date, **kwargs)


class CandidateApplied(EventBase):
    def __init__(self, candidate, job_opening, **kwargs):
        EventBase.__init__(self, candidate=candidate, job_opening=job_opening, **kwargs)


class CandidateHired(EventBase):
    def __init__(self, candidate, job_opening, **kwargs):
        EventBase.__init__(self, candidate=candidate, job_opening=job_opening, **kwargs)


class CandidateInvited(EventBase):
    def __init__(self, candidate, job_opening, **kwargs):
        EventBase.__init__(self, candidate=candidate, job_opening=job_opening, **kwargs)


class CandidateNotHired(EventBase):
    def __init__(self, candidate, job_opening, **kwargs):
        EventBase.__init__(self, candidate=candidate, job_opening=job_opening, **kwargs)


class CandidateStatusChanged(EventBase):
    def __init__(self, candidate, previous_status, **kwargs):
        EventBase.__init__(self, candidate=candidate, previous_status=previous_status, **kwargs)


class CheckrContinuousCheckWebHook(EventBase):
    def __init__(self, vendor_id, package_id, **kwargs):
        EventBase.__init__(self, vendor_id=vendor_id, package_id=package_id, **kwargs)


class CheckrInvitationDataHook(EventBase):
    def __init__(self, vendor, data, stage, continuous, **kwargs):
        EventBase.__init__(self, vendor=vendor, data=data, stage=stage, continuous=continuous, **kwargs)


class ComplianceClassificationCompleted(EventBase):
    def __init__(self, questionnaire_response, stage, **kwargs):
        EventBase.__init__(self, questionnaire_response=questionnaire_response, stage=stage, **kwargs)


class ComplianceClassificationOutcomeUpdated(EventBase):
    def __init__(self, questionnaire_response, **kwargs):
        EventBase.__init__(self, questionnaire_response=questionnaire_response, **kwargs)


class ContractAccepted(EventBase):
    def __init__(self, contract, accepting_user, **kwargs):
        EventBase.__init__(self, contract=contract, accepting_user=accepting_user, **kwargs)


class ContractApproved(EventBase):
    def __init__(self, contract, data, **kwargs):
        EventBase.__init__(self, contract=contract, data=data, **kwargs)


class ContractAutomationWorkflowEnded(EventBase):
    def __init__(self, contract, vendor_workflow, **kwargs):
        EventBase.__init__(self, contract=contract, vendor_workflow=vendor_workflow, **kwargs)


class ContractAutomationWorkflowStarted(EventBase):
    def __init__(self, contract, vendor_workflow, **kwargs):
        EventBase.__init__(self, contract=contract, vendor_workflow=vendor_workflow, **kwargs)


class ContractCanceled(EventBase):
    def __init__(self, contract, contract_cancellation_reason, **kwargs):
        EventBase.__init__(self, contract=contract, contract_cancellation_reason=contract_cancellation_reason, **kwargs)


class ContractComplianceClassificationOutcomeInconclusive(EventBase):
    def __init__(self, contract, questionnaire_response, stage, **kwargs):
        EventBase.__init__(self, contract=contract, questionnaire_response=questionnaire_response, stage=stage, **kwargs)


class ContractComplianceClassificationOutcomeConfirmed(EventBase):
    def __init__(self, contract, questionnaire_response, **kwargs):
        EventBase.__init__(self, contract=contract, questionnaire_response=questionnaire_response, **kwargs)


class ContractCreated(EventBase):
    def __init__(self, contract, **kwargs):
        EventBase.__init__(self, contract=contract, **kwargs)


class ContractCreationFailed(EventBase):
    def __init__(self, contract, **kwargs):
        EventBase.__init__(self, contract=contract, **kwargs)


class ContractEnded(EventBase):
    def __init__(self, contract, data, **kwargs):
        EventBase.__init__(self, contract=contract, data=data, **kwargs)


class ContractEndingSoon(EventBase):
    def __init__(self, contract, data, **kwargs):
        EventBase.__init__(self, contract=contract, data=data, **kwargs)


class ContractOwnerChanged(EventBase):
    def __init__(self, contract, current_contract_owner, previous_contract_owner, **kwargs):
        EventBase.__init__(
            self,
            contract=contract,
            current_contract_owner=current_contract_owner,
            previous_contract_owner=previous_contract_owner,
            **kwargs,
        )


class ContractPaymentAssignedBuyerChanged(EventBase):
    def __init__(self, contract, current_assigned_buyer, previous_assigned_buyer, **kwargs):
        EventBase.__init__(
            self,
            contract=contract,
            current_assigned_buyer=current_assigned_buyer,
            previous_assigned_buyer=previous_assigned_buyer,
            **kwargs,
        )


class ContractPaymentCreated(EventBase):
    def __init__(self, contract, data, **kwargs):
        EventBase.__init__(self, contract=contract, data=data, **kwargs)


class ContractRejected(EventBase):
    def __init__(self, contract, data, **kwargs):
        EventBase.__init__(self, contract=contract, data=data, **kwargs)


class ContractReviewWorkflowDisqualified(EventBase):
    def __init__(self, contract, vendor_workflow, **kwargs):
        EventBase.__init__(self, contract=contract, vendor_workflow=vendor_workflow, **kwargs)


class ContractReviewWorkflowEnded(EventBase):
    def __init__(self, contract, vendor_workflow, **kwargs):
        EventBase.__init__(self, contract=contract, vendor_workflow=vendor_workflow, **kwargs)


class ContractReviewWorkflowStarted(EventBase):
    def __init__(self, contract, vendor_workflow, **kwargs):
        EventBase.__init__(self, contract=contract, vendor_workflow=vendor_workflow, **kwargs)


class ContractSignerChanged(EventBase):
    def __init__(self, contract, current_contract_signer, previous_contract_signer, **kwargs):
        EventBase.__init__(
            self,
            contract=contract,
            current_contract_signer=current_contract_signer,
            previous_contract_signer=previous_contract_signer,
            **kwargs,
        )


class ContractStatusChanged(EventBase):
    def __init__(self, contract, contract_status, **kwargs):
        EventBase.__init__(self, contract=contract, contract_status=contract_status, **kwargs)


class CostpointVendorCreated(EventBase):
    def __init__(self, vendor_id: int, costpoint_id: str):
        EventBase.__init__(self, vendor_id=vendor_id, costpoint_id=costpoint_id)


class CostpointVendorBankDetailsUpdated(EventBase):
    def __init__(self, vendor_id: int, costpoint_id: str):
        EventBase.__init__(self, vendor_id=vendor_id, costpoint_id=costpoint_id)


class CostpointVendorDeleted(EventBase):
    def __init__(self, vendor_id: int, costpoint_id: str):
        EventBase.__init__(self, vendor_id=vendor_id, costpoint_id=costpoint_id)


class CostpointSyncFailed(EventBase):
    def __init__(self, sync_id: int, sync_type: str, sync_status: str, error_message: str, vendor_id: str | None = None):
        EventBase.__init__(
            self, sync_id=sync_id, sync_type=sync_type, sync_status=sync_status, error_message=error_message, vendor_id=vendor_id
        )


class CustomEvent(EventBase):
    pass


class CustomOnBoardingNotification(EventBase):
    def __init__(self, custom_notification, vendor, vendor_workflow_pk, stage, **kwargs):
        EventBase.__init__(
            self, custom_notification=custom_notification, vendor=vendor, vendor_workflow_pk=vendor_workflow_pk, stage=stage, **kwargs
        )


class CustomVendorFieldCreated(EventBase):
    def __init__(self, custom_vendor_field, **kwargs):
        EventBase.__init__(self, custom_vendor_field=custom_vendor_field, **kwargs)


class CustomVendorFieldDeleted(EventBase):
    def __init__(self, custom_vendor_field, **kwargs):
        EventBase.__init__(self, custom_vendor_field=custom_vendor_field, **kwargs)


class CustomVendorFieldSectionCreated(EventBase):
    def __init__(self, custom_vendor_field_section, **kwargs):
        EventBase.__init__(self, custom_vendor_field_section=custom_vendor_field_section, **kwargs)


class CustomVendorFieldSectionDeleted(EventBase):
    def __init__(self, custom_vendor_field_section, **kwargs):
        EventBase.__init__(self, custom_vendor_field_section=custom_vendor_field_section, **kwargs)


class CustomVendorFieldSectionUpdated(EventBase):
    def __init__(self, custom_vendor_field_section, **kwargs):
        EventBase.__init__(self, custom_vendor_field_section=custom_vendor_field_section, **kwargs)


class CustomVendorFieldUpdated(EventBase):
    def __init__(self, custom_vendor_field, **kwargs):
        EventBase.__init__(self, custom_vendor_field=custom_vendor_field, **kwargs)


class CustomFieldChanged(EventBase):
    def __init__(self, custom_field, **kwargs):
        EventBase.__init__(self, custom_field=custom_field, **kwargs)


class CustomScriptEmailTrigger(EventBase):
    def __init__(
        self, recipients, subject, content, button_1_text, button_1_url, button_2_text, button_2_url, button_3_text, button_3_url, **kwargs
    ):
        EventBase.__init__(
            self,
            recipients=recipients,
            subject=subject,
            content=content,
            button_1_text=button_1_text,
            button_1_url=button_1_url,
            button_2_text=button_2_text,
            button_2_url=button_2_url,
            button_3_text=button_3_text,
            button_3_url=button_3_url,
            **kwargs,
        )


class CustomScriptError(EventBase):
    def __init__(self, trigger, script, log_object, **kwargs):
        EventBase.__init__(self, trigger=trigger, script=script, log_object=log_object, **kwargs)


class DispatchTrackUpdateFailure(EventBase):
    def __init__(self, driver_name, driver_number, error_message, **kwargs):
        EventBase.__init__(self, driver_name=driver_name, driver_number=driver_number, error_message=error_message, **kwargs)


class DispatchTrackUpdateHook(EventBase):
    def __init__(self, vendor, data, **kwargs):
        EventBase.__init__(self, vendor=vendor, data=data, **kwargs)


class DocumentHasExpired(EventBase):
    def __init__(self, document, vendor, **kwargs):
        EventBase.__init__(self, document=document, vendor=vendor, **kwargs)


class DocumentIsGoingToExpire(EventBase):
    def __init__(self, document, vendor, **kwargs):
        EventBase.__init__(self, document=document, vendor=vendor, **kwargs)


class DocumentIsRejected(EventBase):
    def __init__(self, document, vendor, **kwargs):
        EventBase.__init__(self, document=document, vendor=vendor, **kwargs)


class DocumentReadyForDownload(EventBase):
    def __init__(self, vendor, document, **kwargs):
        EventBase.__init__(self, vendor=vendor, document=document, **kwargs)


class DocumentReadyForSignature(EventBase):
    def __init__(self, requested_by, sign_request, signer, document_name, custom_message, **kwargs):
        EventBase.__init__(
            self,
            requested_by=requested_by,
            sign_request=sign_request,
            signer=signer,
            document_name=document_name,
            custom_message=custom_message,
            **kwargs,
        )


class DocumentsReadyForReview(EventBase):
    def __init__(self, vendor_stage, **kwargs):
        EventBase.__init__(self, vendor_stage=vendor_stage, **kwargs)


class EarlyAccessRequest(EventBase):
    def __init__(self, username, email, feature, **kwargs):
        EventBase.__init__(self, username=username, email=email, feature=feature, **kwargs)


class EmailChangedByUser(EventBase):
    def __init__(self, old_email, new_email, **kwargs):
        EventBase.__init__(self, old_email=old_email, new_email=new_email, **kwargs)


class GroupCreated(EventBase):
    def __init__(self, group, **kwargs):
        EventBase.__init__(self, group=group, **kwargs)


class GroupRemoved(EventBase):
    def __init__(self, group, **kwargs):
        EventBase.__init__(self, group=group, **kwargs)


class IntegrationError(EventBase):
    def __init__(self, error_message, **kwargs):
        EventBase.__init__(self, error_message=error_message, **kwargs)


class InvitationDeadlineIsClosed(EventBase):
    def __init__(self, project, **kwargs):
        EventBase.__init__(self, project=project, **kwargs)


class InvitationDeadlineReminder(EventBase):
    def __init__(self, project, **kwargs):
        EventBase.__init__(self, project=project, **kwargs)


class InvitationReminder(EventBase):
    def __init__(self, vendor, automatic, **kwargs):
        EventBase.__init__(self, vendor=vendor, automatic=automatic, **kwargs)


class InviteLinkRegistration(EventBase):
    def __init__(self, email, invite_link_use, **kwargs):
        EventBase.__init__(self, email=email, invite_link_use=invite_link_use, **kwargs)


class InviteLinkSummary(EventBase):
    def __init__(self, manager, registered_vendors_number, **kwargs):
        EventBase.__init__(self, manager=manager, registered_vendors_number=registered_vendors_number, **kwargs)


class JobOpeningAwaitingApproval(EventBase):
    def __init__(self, job_opening, **kwargs):
        EventBase.__init__(self, job_opening=job_opening, **kwargs)


class JobOpeningCreated(EventBase):
    def __init__(self, job_opening, **kwargs):
        EventBase.__init__(self, job_opening=job_opening, **kwargs)


class JobOpeningPublished(EventBase):
    def __init__(self, job_opening, **kwargs):
        EventBase.__init__(self, job_opening=job_opening, **kwargs)


class JobOpeningPublishedToJobBoard(EventBase):
    def __init__(self, job_opening, **kwargs):
        EventBase.__init__(self, job_opening=job_opening, **kwargs)


class JobOpeningTaskCreated(EventBase):
    def __init__(self, job_opening, task, **kwargs):
        EventBase.__init__(self, job_opening=job_opening, task=task, **kwargs)


class ContractTaskCreated(EventBase):
    def __init__(self, contract, task, **kwargs):
        EventBase.__init__(self, contract=contract, task=task, **kwargs)


class JobOpeningUpdated(EventBase):
    def __init__(self, job_opening, **kwargs):
        EventBase.__init__(self, job_opening=job_opening, **kwargs)


class ManualProjectInvitationResent(EventBase):
    def __init__(self, project, project_vendor, **kwargs):
        EventBase.__init__(self, project=project, project_vendor=project_vendor, **kwargs)


class MessagePosted(EventBase):
    def __init__(self, message, message_board_url, created_by_name, object_name, object_type, **kwargs):
        EventBase.__init__(
            self,
            message=message,
            message_board_url=message_board_url,
            created_by_name=created_by_name,
            object_name=object_name,
            object_type=object_type,
            **kwargs,
        )


class MessagesAggregated(EventBase):
    def __init__(self, user_ids, messages, object_name, object_type, **kwargs):
        EventBase.__init__(self, user_ids=user_ids, messages=messages, object_name=object_name, object_type=object_type, **kwargs)


class RedlinesAggregated(EventBase):
    def __init__(self, user_ids, redlines, sign_request, **kwargs):
        EventBase.__init__(self, user_ids=user_ids, redlines=redlines, sign_request=sign_request, **kwargs)


class RedlinesReviewed(EventBase):
    def __init__(self, user_ids, redlines, sign_request, **kwargs) -> None:
        EventBase.__init__(self, user_ids=user_ids, redlines=redlines, sign_request=sign_request, **kwargs)


class MoneyCorpSFTPTestFailed(EventBase):
    def __init__(self, message, **kwargs):
        EventBase.__init__(self, message=message, **kwargs)


class NewsletterUserSubscribed(EventBase):
    def __init__(self, email_info, **kwargs):
        EventBase.__init__(self, email_info=email_info, **kwargs)


class NewsletterUserUnsubscribed(EventBase):
    def __init__(self, email_info, **kwargs):
        EventBase.__init__(self, email_info=email_info, **kwargs)


class OnBoardingCompletedByVendor(EventBase):
    def __init__(self, onboarding_workflows_for_vendor, **kwargs):
        EventBase.__init__(self, onboarding_workflows_for_vendor=onboarding_workflows_for_vendor, **kwargs)


class OnBoardingDisqualified(EventBase):
    def __init__(self, onboarding_workflows_for_vendor, vendor_workflow_pk, stage, vendor, **kwargs):
        EventBase.__init__(
            self,
            onboarding_workflows_for_vendor=onboarding_workflows_for_vendor,
            vendor_workflow_pk=vendor_workflow_pk,
            stage=stage,
            vendor=vendor,
            **kwargs,
        )


class OnBoardingStageDataUpdated(EventBase):
    def __init__(self, onboarding_workflows_for_vendor, current, updated, **kwargs):
        EventBase.__init__(
            self, onboarding_workflows_for_vendor=onboarding_workflows_for_vendor, current=current, updated=updated, **kwargs
        )


class OnBoardingStageEntryConditionEvaluated(EventBase):
    def __init__(
        self, onboarding_workflows_for_vendor, condition, condition_repr, condition_context, result_text, stage_full_name, **kwargs
    ):
        EventBase.__init__(
            self,
            onboarding_workflows_for_vendor=onboarding_workflows_for_vendor,
            condition=condition,
            condition_repr=condition_repr,
            condition_context=condition_context,
            result_text=result_text,
            stage_full_name=stage_full_name,
            **kwargs,
        )


class OnBoardingStageUnlocked(EventBase):
    def __init__(self, vendor_stage_pk, onboarding_workflows_for_vendor, vendor, stage, **kwargs):
        EventBase.__init__(
            self,
            vendor_stage_pk=vendor_stage_pk,
            onboarding_workflows_for_vendor=onboarding_workflows_for_vendor,
            vendor=vendor,
            stage=stage,
            **kwargs,
        )


class OnBoardingActionStageFailed(EventBase):
    def __init__(self, action_name, vendor_ids, stage_id, error, **kwargs):
        EventBase.__init__(self, action_name=action_name, vendor_ids=vendor_ids, stage_id=stage_id, error=error, **kwargs)


class OnBoardingActionStageRetrySucceeded(EventBase):
    def __init__(self, vendor_ids, stage_id, **kwargs):
        EventBase.__init__(self, vendor_ids=vendor_ids, stage_id=stage_id, **kwargs)


class OnBoardingTemplateEdited(EventBase):
    def __init__(self, vendor_ids, template_id, **kwargs):
        EventBase.__init__(self, vendor_ids=vendor_ids, template_id=template_id, **kwargs)


class PasswordChanged(EventBase):
    pass


class PasswordResetRequest(EventBase):
    def __init__(self, password_reset_url, **kwargs):
        EventBase.__init__(self, password_reset_url=password_reset_url, **kwargs)


class PaymentCreated(EventBase):
    def __init__(self, payment, **kwargs):
        EventBase.__init__(self, payment=payment, **kwargs)


class PaymentFlagAdded(EventBase):
    def __init__(self, payment, **kwargs):
        EventBase.__init__(self, payment=payment, **kwargs)


class PaymentFlagManuallyAdded(EventBase):
    def __init__(self, payment, reason, **kwargs):
        EventBase.__init__(self, payment=payment, reason=reason, **kwargs)


class PaymentFlagManuallyRemoved(EventBase):
    def __init__(self, payment, **kwargs):
        EventBase.__init__(self, payment=payment, **kwargs)


class PaymentFlagRemoved(EventBase):
    def __init__(self, payment, **kwargs):
        EventBase.__init__(self, payment=payment, **kwargs)


class PaymentStatusUpdated(EventBase):
    def __init__(self, payment, vendor, previous_status, **kwargs):
        EventBase.__init__(self, payment=payment, vendor=vendor, previous_status=previous_status, **kwargs)


class PaymentStatusUpdatedEmailTrigger(EventBase):
    def __init__(self, payment, vendor, previous_status, **kwargs):
        EventBase.__init__(self, payment=payment, vendor=vendor, previous_status=previous_status, **kwargs)


class PayrunPreparationFailed(EventBase):
    def __init__(self, payment_processor, trigger, reason, **kwargs):
        EventBase.__init__(self, provider=payment_processor, trigger=trigger, reason=reason, **kwargs)


class PayrunDeliveryToProcessorFailed(EventBase):
    def __init__(self, payrun_reference, provider, trigger, reason, **kwargs):
        EventBase.__init__(self, payrun_reference=payrun_reference, provider=provider, trigger=trigger, reason=reason, **kwargs)


class PaymentProcessingNotificationsSuccess(EventBase):
    def __init__(
        self,
        payment_processor_name,
        payment_processor_account_id,
        payment_count,
        vendor_count,
        success_count,
        failure_count,
        success_value,
        failure_value,
        success_payment_ids,
        success_income_ids,
        failure_payment_ids,
        failure_vendor_ids,
        trigger,
        **kwargs,
    ):
        EventBase.__init__(
            self,
            payment_processor_name=payment_processor_name,
            payment_processor_account_id=payment_processor_account_id,
            payment_count=payment_count,
            vendor_count=vendor_count,
            success_count=success_count,
            failure_count=failure_count,
            success_value=success_value,
            failure_value=failure_value,
            success_payment_ids=success_payment_ids,
            success_income_ids=success_income_ids,
            failure_payment_ids=failure_payment_ids,
            failure_vendor_ids=failure_vendor_ids,
            trigger=trigger,
            **kwargs,
        )


class PaymentsCreated(EventBase):
    def __init__(
        self,
        payment_processor_name,
        payment_processor_account_id,
        payment_count,
        vendor_count,
        success_count,
        failure_count,
        success_value,
        failure_value,
        success_payment_ids,
        success_income_ids,
        failure_payment_ids,
        failure_vendor_ids,
        trigger,
        **kwargs,
    ):
        EventBase.__init__(
            self,
            payment_processor_name=payment_processor_name,
            payment_processor_account_id=payment_processor_account_id,
            payment_count=payment_count,
            vendor_count=vendor_count,
            success_count=success_count,
            failure_count=failure_count,
            success_value=success_value,
            failure_value=failure_value,
            success_payment_ids=success_payment_ids,
            success_income_ids=success_income_ids,
            failure_payment_ids=failure_payment_ids,
            failure_vendor_ids=failure_vendor_ids,
            trigger=trigger,
            **kwargs,
        )


class PayrunExportToMoneyCorpSFTPFailed(EventBase):
    def __init__(self, payrun, file_name, **kwargs):
        EventBase.__init__(self, payrun=payrun, file_name=file_name, **kwargs)


class PayrunExportToMoneyCorpSFTPSuccessful(EventBase):
    def __init__(self, payrun, file_name, **kwargs):
        EventBase.__init__(self, payrun=payrun, file_name=file_name, **kwargs)


class PostBulkMessage(EventBase):
    def __init__(self, subject, content, vendors, **kwargs):
        EventBase.__init__(self, subject=subject, content=content, vendors=vendors, **kwargs)


class ProfileSettingsConfirmation(EventBase):
    def __init__(self, user, changes, confirm_link, **kwargs):
        EventBase.__init__(self, user=user, changes=changes, confirm_link=confirm_link, **kwargs)


class ProjectAwarded(EventBase):
    def __init__(self, project, vendor, **kwargs):
        EventBase.__init__(self, project=project, vendor=vendor, **kwargs)


class ProjectClosed(EventBase):
    def __init__(self, project, **kwargs):
        EventBase.__init__(self, project=project, **kwargs)


class ProjectClosedbyUser(EventBase):
    def __init__(self, project, **kwargs):
        EventBase.__init__(self, project=project, **kwargs)


class ProjectCreated(EventBase):
    def __init__(self, project, **kwargs):
        EventBase.__init__(self, project=project, **kwargs)


class ProjectDeleted(EventBase):
    def __init__(self, project, **kwargs):
        EventBase.__init__(self, project=project, **kwargs)


class ProjectMarkedAsCompleted(EventBase):
    def __init__(self, project, **kwargs):
        EventBase.__init__(self, project=project, **kwargs)


class ProjectPosted(EventBase):
    def __init__(self, project, **kwargs):
        EventBase.__init__(self, project=project, **kwargs)


class ProjectTeamInvitation(EventBase):
    def __init__(self, project, new_team_members, **kwargs):
        EventBase.__init__(self, project=project, new_team_members=new_team_members, **kwargs)


class ProjectUpdated(EventBase):
    def __init__(self, project, change_list, inform_partners, **kwargs):
        EventBase.__init__(self, project=project, change_list=change_list, inform_partners=inform_partners, **kwargs)


class ProjectAssistanceRequest(EventBase):
    def __init__(self, project, **kwargs):
        EventBase.__init__(self, project=project, **kwargs)


class ProposalBuyerComment(EventBase):
    def __init__(self, project_reply, project, proposal, **kwargs):
        EventBase.__init__(self, project_reply=project_reply, project=project, proposal=proposal, **kwargs)


class ProposalBuyerReply(EventBase):
    def __init__(self, project_reply, project, proposal, **kwargs):
        EventBase.__init__(self, project_reply=project_reply, project=project, proposal=proposal, **kwargs)


class ProposalVendorReply(EventBase):
    def __init__(self, project_reply, project, proposal, **kwargs):
        EventBase.__init__(self, project_reply=project_reply, project=project, proposal=proposal, **kwargs)


class PublicProjectConfirmedProposal(EventBase):
    def __init__(self, project, vendor, **kwargs):
        EventBase.__init__(self, project=project, vendor=vendor, **kwargs)


class PublicProjectUnconfirmedProposal(EventBase):
    def __init__(self, project, vendor_data, **kwargs):
        EventBase.__init__(self, project=project, vendor_data=vendor_data, **kwargs)


class RecalculatePaymentAmounts(EventBase):
    def __init__(self, statuses, **kwargs):
        EventBase.__init__(self, statuses=statuses, **kwargs)


class RegistrationCompletedByBuyer(EventBase):
    pass


class RequestProjectAccess(EventBase):
    def __init__(self, project, **kwargs):
        EventBase.__init__(self, project=project, **kwargs)


class RequestedDocumentHasExpired(EventBase):
    def __init__(self, document, vendor, **kwargs):
        EventBase.__init__(self, document=document, vendor=vendor, **kwargs)


class RequestedDocumentIsGoingToExpire(EventBase):
    def __init__(self, document, vendor, **kwargs):
        EventBase.__init__(self, document=document, vendor=vendor, **kwargs)


class RequestedDocumentRejected(EventBase):
    def __init__(self, document, vendor, **kwargs):
        EventBase.__init__(self, document=document, vendor=vendor, **kwargs)


class RequestedDocumentUpdated(EventBase):
    def __init__(self, document, vendor, **kwargs):
        EventBase.__init__(self, document=document, vendor=vendor, **kwargs)


class SchedulerError(EventBase):
    def __init__(self, scheduler, message, **kwargs):
        EventBase.__init__(self, scheduler=scheduler, message=message, **kwargs)


class SendManualCustomNotifications(EventBase):
    def __init__(self, vendor_workflow, stage, vendor, **kwargs):
        EventBase.__init__(self, vendor_workflow=vendor_workflow, stage=stage, vendor=vendor, **kwargs)


class SendOnBoardingReminderNotifications(EventBase):
    def __init__(self, vendor_workflow, stage, vendor, **kwargs):
        EventBase.__init__(self, vendor_workflow=vendor_workflow, stage=stage, vendor=vendor, **kwargs)


class SendSecureLinkRequest(EventBase):
    def __init__(self, email, login_links, **kwargs):
        EventBase.__init__(self, email=email, login_links=login_links, **kwargs)


class SharedListCustomFieldEdited(EventBase):
    def __init__(self, custom_field, vendor, vendor_group, **kwargs):
        EventBase.__init__(self, custom_field=custom_field, vendor=vendor, vendor_group=vendor_group, **kwargs)


class ShortlistPayCommunicationError(EventBase):
    def __init__(self, error_message, method, retry_number, slack_emoji, **kwargs):
        EventBase.__init__(self, error_message=error_message, method=method, retry_number=retry_number, slack_emoji=slack_emoji, **kwargs)


class ShortlistPayCommunicationRetrySuccessful(EventBase):
    def __init__(self, method, retry_number, slack_emoji, **kwargs):
        EventBase.__init__(self, method=method, retry_number=retry_number, slack_emoji=slack_emoji, **kwargs)


class SignatureAddedToDocument(EventBase):
    def __init__(self, signer, sign_request, **kwargs):
        EventBase.__init__(self, signer=signer, sign_request=sign_request, **kwargs)


class SubmissionDeadlineReminder(EventBase):
    def __init__(self, project, **kwargs):
        EventBase.__init__(self, project=project, **kwargs)


class SupplierAddedToJobOpening(EventBase):
    def __init__(self, job_opening, supplier_ids, **kwargs):
        EventBase.__init__(self, job_opening=job_opening, supplier_ids=supplier_ids, **kwargs)


class TaskAccepted(EventBase):
    def __init__(self, task, description, **kwargs):
        EventBase.__init__(self, task=task, description=description, **kwargs)


class TaskAdded(EventBase):
    def __init__(self, task, task_group, **kwargs):
        EventBase.__init__(self, task=task, task_group=task_group, **kwargs)


class TaskGroupEdited(EventBase):
    def __init__(self, task_group, description, **kwargs):
        EventBase.__init__(self, task_group=task_group, description=description, **kwargs)


class TaskGroupTeamInvitation(EventBase):
    def __init__(self, task_group, new_members, **kwargs):
        EventBase.__init__(self, task_group=task_group, new_members=new_members, **kwargs)


class TaskGroupUpdated(EventBase):
    def __init__(self, task_group, **kwargs):
        EventBase.__init__(self, task_group=task_group, **kwargs)


class TaskManagerInvitation(EventBase):
    def __init__(self, task, new_members, **kwargs):
        EventBase.__init__(self, task=task, new_members=new_members, **kwargs)


class TaskMilestoneStatusUpdated(EventBase):
    def __init__(self, task, description, milestone, **kwargs):
        EventBase.__init__(self, task=task, description=description, milestone=milestone, **kwargs)


class TaskMilestoneUpdated(EventBase):
    def __init__(self, task, description, milestone, **kwargs):
        EventBase.__init__(self, task=task, description=description, milestone=milestone, **kwargs)


class TaskRejected(EventBase):
    def __init__(self, task, description, rejection_reason, **kwargs):
        EventBase.__init__(self, task=task, description=description, rejection_reason=rejection_reason, **kwargs)


class TaskStatusChanged(EventBase):
    def __init__(self, task, **kwargs):
        EventBase.__init__(self, task=task, **kwargs)


class TaskTemplateMisconfigured(EventBase):
    def __init__(self, task_template, vendor, related_obj_model, related_obj_pk, error_message, **kwargs):
        EventBase.__init__(
            self,
            task_template=task_template,
            vendor=vendor,
            related_obj_model=related_obj_model,
            related_obj_pk=related_obj_pk,
            error_message=error_message,
            **kwargs,
        )


class TaskTimesheetApproved(EventBase):
    def __init__(self, task, timesheet, **kwargs):
        EventBase.__init__(self, task=task, timesheet=timesheet, **kwargs)


class TaskTimesheetRejected(EventBase):
    def __init__(self, task, timesheet, **kwargs):
        EventBase.__init__(self, task=task, timesheet=timesheet, **kwargs)


class TaskTimesheetReminder(EventBase):
    def __init__(self, task, **kwargs):
        EventBase.__init__(self, task=task, **kwargs)


class TaskTimesheetSubmitted(EventBase):
    def __init__(self, task, timesheet, **kwargs):
        EventBase.__init__(self, task=task, timesheet=timesheet, **kwargs)


class TaskUpdated(EventBase):
    def __init__(self, task, description, **kwargs):
        EventBase.__init__(self, task=task, description=description, **kwargs)


class TaskVendorMarkAsCompleted(EventBase):
    def __init__(self, task, description, **kwargs):
        EventBase.__init__(self, task=task, description=description, **kwargs)


class TaxInformationRequiredEmailTrigger(EventBase):
    def __init__(self, vendor, **kwargs):
        EventBase.__init__(self, vendor=vendor, **kwargs)


class TeamInvitation(EventBase):
    def __init__(self, user, **kwargs):
        EventBase.__init__(self, user=user, **kwargs)


class TeamRegistrationCompleted(EventBase):
    def __init__(self, invited_by, **kwargs):
        EventBase.__init__(self, invited_by=invited_by, **kwargs)


class Ten99PPrevalidationDateRangeInvalid(EventBase):
    def __init__(self, data_provider, **kwargs):
        EventBase.__init__(self, data_provider=data_provider, **kwargs)


class Ten99PPrevalidationTaxCountryNonUS(EventBase):
    def __init__(self, data_provider, **kwargs):
        EventBase.__init__(self, data_provider=data_provider, **kwargs)


class Ten99PPrevalidationJobCategoryNameInvalid(EventBase):
    def __init__(self, data_provider, **kwargs):
        EventBase.__init__(self, data_provider=data_provider, **kwargs)


class Ten99PVendorOptedInForInsurance(EventBase):
    def __init__(self, data_provider, **kwargs):
        EventBase.__init__(self, data_provider=data_provider, **kwargs)


class Ten99PVendorUploadedProofOfInsurance(EventBase):
    def __init__(self, data_provider, **kwargs):
        EventBase.__init__(self, data_provider=data_provider, **kwargs)


class Ten99PVendorObtainedNewInsurance(EventBase):
    def __init__(self, data_provider, **kwargs):
        EventBase.__init__(self, data_provider=data_provider, **kwargs)


class Ten99PProofOfInsuranceDecisionConfirmed(EventBase):
    def __init__(self, data_provider, **kwargs):
        EventBase.__init__(self, data_provider=data_provider, **kwargs)


class Ten99PProofOfInsuranceRejected(EventBase):
    def __init__(self, data_provider, rejection_reasons, **kwargs):
        EventBase.__init__(self, data_provider=data_provider, rejection_reasons=rejection_reasons, **kwargs)


class Ten99PStartedOnboardingReminder(EventBase):
    def __init__(self, vendor_id, external_onboarding_url, **kwargs):
        EventBase.__init__(self, vendor_id=vendor_id, external_onboarding_url=external_onboarding_url, **kwargs)


class Ten99PVendorManualReview(EventBase):
    def __init__(self, vendor_stage, **kwargs):
        EventBase.__init__(self, vendor_stage=vendor_stage, **kwargs)


class TimesheetApproved(EventBase):
    def __init__(self, timesheet, **kwargs):
        EventBase.__init__(self, timesheet=timesheet, **kwargs)


class TimesheetBilled(EventBase):
    def __init__(self, task, timesheet, **kwargs):
        EventBase.__init__(self, task=task, timesheet=timesheet, **kwargs)


class TimesheetRejected(EventBase):
    def __init__(self, timesheet, **kwargs):
        EventBase.__init__(self, timesheet=timesheet, **kwargs)


class TimesheetReminder(EventBase):
    def __init__(self, task, **kwargs):
        EventBase.__init__(self, task=task, **kwargs)


class TimesheetSubmitted(EventBase):
    def __init__(self, timesheet, **kwargs):
        EventBase.__init__(self, timesheet=timesheet, **kwargs)


class TimesheetUnBilled(EventBase):
    def __init__(self, task, timesheet, **kwargs):
        EventBase.__init__(self, task=task, timesheet=timesheet, **kwargs)


class TimesheetCurrentPeriodMissingEvent(EventBase):
    def __init__(self, task, **kwargs):
        EventBase.__init__(self, task=task, **kwargs)


class TimesheetPreviousPeriodMissingEvent(EventBase):
    def __init__(self, task, **kwargs):
        EventBase.__init__(self, task=task, **kwargs)


class TrialRegistrationCompleted(EventBase):
    def __init__(self, trial_template, **kwargs):
        EventBase.__init__(self, trial_template=trial_template, **kwargs)


class UpsellGlobalPayRequested(EventBase):
    def __init__(self, username, email, **kwargs):
        EventBase.__init__(self, username=username, email=email, **kwargs)


class UserPasswordExpiring(EventBase):
    def __init__(self, user, period, password_reset_url, **kwargs):
        EventBase.__init__(self, user=user, period=period, password_reset_url=password_reset_url, **kwargs)


class UserReusedEmailAddress(EventBase):
    def __init__(self, user, **kwargs):
        EventBase.__init__(self, user=user, **kwargs)


class UserUnsubscribed(EventBase):
    def __init__(self, user, **kwargs):
        EventBase.__init__(self, user=user, **kwargs)


class VendorAcceptsProjectInvitation(EventBase):
    def __init__(self, project, vendor, **kwargs):
        EventBase.__init__(self, project=project, vendor=vendor, **kwargs)


class VendorAddedPayment(EventBase):
    def __init__(self, payment, vendor, **kwargs):
        EventBase.__init__(self, payment=payment, vendor=vendor, **kwargs)


class VendorAddedToGroup(EventBase):
    def __init__(self, group, vendor, **kwargs):
        EventBase.__init__(self, group=group, vendor=vendor, **kwargs)


class VendorAddedToOnBoarding(EventBase):
    def __init__(self, onboarding_workflows_for_vendor, **kwargs):
        EventBase.__init__(self, onboarding_workflows_for_vendor=onboarding_workflows_for_vendor, **kwargs)


class VendorAvailabilityUpdated(EventBase):
    def __init__(self, old_availability, new_availability, vendor, **kwargs):
        EventBase.__init__(self, old_availability=old_availability, new_availability=new_availability, vendor=vendor, **kwargs)


class VendorComment(EventBase):
    def __init__(self, project_reply, project, vendor, **kwargs):
        EventBase.__init__(self, project_reply=project_reply, project=project, vendor=vendor, **kwargs)


class VendorCompletedStage(EventBase):
    def __init__(self, vendor_workflow_pk, stage, vendor, **kwargs):
        EventBase.__init__(self, vendor_workflow_pk=vendor_workflow_pk, stage=stage, vendor=vendor, **kwargs)


class VendorCreated(EventBase):
    def __init__(self, vendor, **kwargs):
        EventBase.__init__(self, vendor=vendor, **kwargs)


class VendorEnteredStage(EventBase):
    def __init__(self, vendor_workflow_pk, stage, vendor, **kwargs):
        EventBase.__init__(self, vendor_workflow_pk=vendor_workflow_pk, stage=stage, vendor=vendor, **kwargs)


class VendorIncidentCreated(EventBase):
    def __init__(self, incident, vendor, **kwargs):
        EventBase.__init__(self, incident=incident, vendor=vendor, **kwargs)


class VendorInvitationDeclined(EventBase):
    def __init__(self, project, vendor, **kwargs):
        EventBase.__init__(self, project=project, vendor=vendor, **kwargs)


class VendorLeftStage(EventBase):
    def __init__(self, vendor_workflow_pk, stage, vendor, **kwargs):
        EventBase.__init__(self, vendor_workflow_pk=vendor_workflow_pk, stage=stage, vendor=vendor, **kwargs)


class VendorLinkReset(EventBase):
    def __init__(self, user, vendor, **kwargs):
        EventBase.__init__(self, user=user, vendor=vendor, **kwargs)


class VendorMovedToStage(EventBase):
    def __init__(self, onboarding_workflows_for_vendor, **kwargs):
        EventBase.__init__(self, onboarding_workflows_for_vendor=onboarding_workflows_for_vendor, **kwargs)


class VendorMovedToStageManually(EventBase):
    def __init__(self, onboarding_workflows_for_vendor, **kwargs):
        EventBase.__init__(self, onboarding_workflows_for_vendor=onboarding_workflows_for_vendor, **kwargs)


class VendorNeedToReviewBankDetails(EventBase):
    def __init__(self, vendors, **kwargs):
        EventBase.__init__(self, vendors=vendors, **kwargs)


class VendorOnBoardingStatusUpdated(EventBase):
    def __init__(self, onboarding_workflows_for_vendor, **kwargs):
        EventBase.__init__(self, onboarding_workflows_for_vendor=onboarding_workflows_for_vendor, **kwargs)


class VendorPostsNewPrivateMessage(EventBase):
    def __init__(self, message, vendor, **kwargs):
        EventBase.__init__(self, message=message, vendor=vendor, **kwargs)


class VendorProfileInformationUpdated(EventBase):
    def __init__(self, vendor, **kwargs):
        EventBase.__init__(self, vendor=vendor, **kwargs)


class VendorProjectInvitationReminder(EventBase):
    def __init__(self, project, project_vendor, **kwargs):
        EventBase.__init__(self, project=project, project_vendor=project_vendor, **kwargs)


class VendorProjectInvitationSent(EventBase):
    def __init__(self, project, project_vendors, **kwargs):
        EventBase.__init__(self, project=project, project_vendors=project_vendors, **kwargs)


class VendorRegistrationCompleted(EventBase):
    def __init__(self, vendor, **kwargs):
        EventBase.__init__(self, vendor=vendor, **kwargs)


class VendorRejectsProjectInvitation(EventBase):
    def __init__(self, project, reject_feedback, vendor, **kwargs):
        EventBase.__init__(self, project=project, reject_feedback=reject_feedback, vendor=vendor, **kwargs)


class VendorRemovedfromProject(EventBase):
    def __init__(self, project, vendor, **kwargs):
        EventBase.__init__(self, project=project, vendor=vendor, **kwargs)


class VendorRemovedfromaGroup(EventBase):
    def __init__(self, group, vendor, **kwargs):
        EventBase.__init__(self, group=group, vendor=vendor, **kwargs)


class VendorRepliesPrivateMessage(EventBase):
    def __init__(self, message, vendor, **kwargs):
        EventBase.__init__(self, message=message, vendor=vendor, **kwargs)


class VendorReply(EventBase):
    def __init__(self, project_reply, project, vendor, to_creator, **kwargs):
        EventBase.__init__(self, project_reply=project_reply, project=project, vendor=vendor, to_creator=to_creator, **kwargs)


class VendorRequestedDeleteAccount(EventBase):
    def __init__(self, vendor, **kwargs):
        EventBase.__init__(self, vendor=vendor, **kwargs)


class VendorResubmitsProposal(EventBase):
    def __init__(self, project, proposal_description, vendor_bid, vendor, **kwargs):
        EventBase.__init__(self, project=project, proposal_description=proposal_description, vendor_bid=vendor_bid, vendor=vendor, **kwargs)


class VendorSendsDocument(EventBase):
    def __init__(self, vendor, document, **kwargs):
        EventBase.__init__(self, vendor=vendor, document=document, **kwargs)


class VendorSignUpWrongCode(EventBase):
    def __init__(self, vendor, **kwargs):
        EventBase.__init__(self, vendor=vendor, **kwargs)


class VendorSignedDocument(EventBase):
    def __init__(self, vendor, document, **kwargs):
        EventBase.__init__(self, vendor=vendor, document=document, **kwargs)


class VendorSignedUp(EventBase):
    def __init__(self, vendor_signup, **kwargs):
        EventBase.__init__(self, vendor_signup=vendor_signup, **kwargs)


class VendorStatusUpdated(EventBase):
    def __init__(self, old_status, new_status, vendor, **kwargs):
        EventBase.__init__(self, old_status=old_status, new_status=new_status, vendor=vendor, **kwargs)


class VendorSubmitsProposal(EventBase):
    def __init__(self, project, proposal_description, vendor_bid, vendor, **kwargs):
        EventBase.__init__(self, project=project, proposal_description=proposal_description, vendor_bid=vendor_bid, vendor=vendor, **kwargs)


class VendorTaskMilestoneUpdated(EventBase):
    def __init__(self, task, description, milestone, **kwargs):
        EventBase.__init__(self, task=task, description=description, milestone=milestone, **kwargs)


class VendorTaskUpdated(EventBase):
    def __init__(self, task, description, **kwargs):
        EventBase.__init__(self, task=task, description=description, **kwargs)


class VendorTypeChanged(EventBase):
    def __init__(self, vendor, old_vendor_type, vendor_type, **kwargs):
        EventBase.__init__(self, vendor=vendor, old_vendor_type=old_vendor_type, vendor_type=vendor_type, **kwargs)


class VendorVerificationCodeRegenerated(EventBase):
    def __init__(self, vendor_signup, **kwargs):
        EventBase.__init__(self, vendor_signup=vendor_signup, **kwargs)


class VendorFeedbackRequested(EventBase):
    def __init__(self, feedback_request, actor, **kwargs):
        EventBase.__init__(self, feedback_request=feedback_request, actor=actor, **kwargs)


class VendorPublicProjectProposalLink(EventBase):
    def __init__(self, project, vendor, **kwargs):
        EventBase.__init__(self, project=project, vendor=vendor, **kwargs)


class VendorSyncEligibilityEnabled(EventBase):
    def __init__(self, sync_eligibility, **kwargs):
        EventBase.__init__(self, sync_eligibility=sync_eligibility, **kwargs)


class VeriffResubmissionRequestedReminder(EventBase):
    def __init__(self, verification, **kwargs):
        EventBase.__init__(self, verification=verification, **kwargs)


class ZippedInvoicesReadyEmailTrigger(EventBase):
    def __init__(self, user, export_date, expiration_date, zipped_invoices_url, **kwargs):
        EventBase.__init__(
            self, user=user, export_date=export_date, expiration_date=expiration_date, zipped_invoices_url=zipped_invoices_url, **kwargs
        )


class FundingStatementReadyEmailTrigger(EventBase):
    def __init__(self, user, export_date, expiration_date, statement_pdf_url, **kwargs):
        EventBase.__init__(
            self, user=user, export_date=export_date, expiration_date=expiration_date, statement_pdf_url=statement_pdf_url, **kwargs
        )


class MissingBalanceForNextPayrunEmailTrigger(EventBase):
    def __init__(
        self,
        oper_account,
        tenant_name,
        operator_name,
        currency,
        missing_balance,
        vendors_assigned_to_invoices_count,
        scheduled_invoices_url,
        send_date,
        **kwargs,
    ):
        EventBase.__init__(
            self,
            oper_account=oper_account,
            tenant_name=tenant_name,
            operator_name=operator_name,
            currency=currency,
            missing_balance=missing_balance,
            vendors_assigned_to_invoices_count=vendors_assigned_to_invoices_count,
            scheduled_invoices_url=scheduled_invoices_url,
            send_date=send_date,
            **kwargs,
        )


class InsufficientBalanceOnPayrunEmailTrigger(EventBase):
    def __init__(
        self,
        tenant_name,
        operator_name,
        scheduled_invoices_url,
        all_invoices_url,
        scheduled_payments_count,
        processed_payments_count,
        **kwargs,
    ):
        EventBase.__init__(
            self,
            tenant_name=tenant_name,
            operator_name=operator_name,
            scheduled_invoices_url=scheduled_invoices_url,
            all_invoices_url=all_invoices_url,
            scheduled_payments_count=scheduled_payments_count,
            processed_payments_count=processed_payments_count,
            **kwargs,
        )


class Vendor1099NecFormAvailable(EventBase):
    def __init__(self, vendor, **kwargs):
        EventBase.__init__(self, vendor=vendor, **kwargs)


class MissingSignRequestSignature(EventBase):
    def __init__(self, sign_request, **kwargs):
        EventBase.__init__(self, sign_request=sign_request, **kwargs)


class TaxBandits1099NecFormsPostalRequestFinished(EventBase):
    def __init__(self, taxbandits_postal_request, notify_user_email, **kwargs):
        EventBase.__init__(self, taxbandits_postal_request=taxbandits_postal_request, notify_user_email=notify_user_email, **kwargs)


class PortfolioUpdated(EventBase):
    def __init__(self, vendor, updates, **kwargs):
        EventBase.__init__(self, vendor=vendor, updates=updates, **kwargs)


class SignRequestVendorGuestCreated(EventBase):
    """Event triggered when a new vendor guest is created or assigned to a sign request."""

    def __init__(self, sign_request, guest, **kwargs):
        self.sign_request = sign_request
        self.guest = guest
        super().__init__(**kwargs)
