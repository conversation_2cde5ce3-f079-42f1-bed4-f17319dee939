from django.contrib.auth.signals import user_logged_out
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from deals.services import locking

User = get_user_model()


@receiver(user_logged_out)
def release_all_locks_on_logout(sender, request, user, **kwargs):
    if user:
        for sign_request_id in locking.get_user_locks(user.id):
            locking.release_lock(sign_request_id, user, ignore_missing=True)
