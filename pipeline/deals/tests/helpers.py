import html
import re
from typing import TYPE_CHECKING, Any

import pytz
from bs4 import BeautifulSoup

from deals.models import Redline
from deals.services.fields.populating import SIGNATURE_DATE_FORMAT, SIGNATURE_TIMESTAMP_FORMAT
from deals.services.html_utils import base64_decode
from shortlist.tests.helpers import TenantTestCase

if TYPE_CHECKING:
    from deals.models import SignRequest


MENTION_PATTERN = r'<mention mention\-id="([a-zA-Z0-9_\-\.]+)"></mention>'


class SigningTenantTestCase(TenantTestCase):
    def assert_sign_request_is_signed(self, sign_request: "SignRequest"):
        self.assertIsNone(sign_request.current_signer)
        self.assertIsNone(sign_request.next_signer)
        self.assertEqual(sign_request.status, sign_request.STATUS_DONE)
        self.assertIsNotNone(sign_request.signed_document_id)
        self.assertEqual(sign_request.signed_document.text, sign_request.text)

    def assert_signatures_are_placed_in_text(self, sign_request: "SignRequest"):
        for signer in sign_request.signers.all():
            if signer.signed_at:
                self.assertIn(
                    f'<td id="signature-{signer.pk}">{signer.actor.name}<br/>'
                    f"{signer.signed_at.astimezone(signer.signed_at_timezone).strftime(SIGNATURE_DATE_FORMAT)}<br/>"
                    f"Generated at {signer.signed_at.astimezone(pytz.UTC).strftime(SIGNATURE_TIMESTAMP_FORMAT)}</td>",
                    sign_request.text,
                )
            else:
                self.assertIn(
                    f'<td id="signature-{signer.pk}"><signature signature-id="Legal">_____________ [Signature: {signer.role}]</signature></td>',
                    sign_request.text,
                )

    def assert_text_is_properly_populated(self, current_text: str, original_text: str, mapped_values: dict[str, str]):
        self.assertFalse(re.search(MENTION_PATTERN, current_text))

        original_soup = BeautifulSoup(original_text, "html.parser")
        original_mentions = original_soup.find_all("mention")

        for mention in original_mentions:
            mention_id = mention.get("mention-id")
            expected_value = mapped_values.get(mention_id, "")
            if expected_value:
                self.assertIn(expected_value, current_text)

    def assert_sign_request_is_properly_populated(self, sign_request: "SignRequest", original_text: str, mapped_values: dict[str, str]):
        self.assertEqual(sign_request.status, sign_request.STATUS_NEW)
        self.assert_text_is_properly_populated(sign_request.text, original_text, mapped_values)


class RedliningTestCase(TenantTestCase):
    @staticmethod
    def get_expected_text(text: str, new_id: str) -> str:
        return html.unescape(base64_decode(text)).replace('"new"', f'"{str(new_id)}"')

    def assert_redline_made(self, payload: dict[str, Any], result: dict[str, Any]) -> None:
        self.assertIn("text", result)

        comment_or_suggestion = payload.get("comment") or payload.get("suggestion")
        if comment_or_suggestion:
            self.assertIsNotNone(result["redline_id"])
            redline = Redline.objects.get(pk=result["redline_id"])
            self.assertEqual(base64_decode(redline.comment), base64_decode(comment_or_suggestion.get("comment", "")))
            self.assertEqual(base64_decode(redline.suggestion), base64_decode(comment_or_suggestion.get("suggestion", "")))
            self.assertEqual(base64_decode(redline.quoted_text), base64_decode(comment_or_suggestion.get("quoted_text", "")))
        else:
            self.assertIsNone(result["redline_id"])

        payload_text = payload.get("text")
        if payload_text is not None:
            if comment_or_suggestion:
                expected_text = self.get_expected_text(payload_text, result.get("redline_id"))
                self.assertEqual(base64_decode(result["text"]), expected_text)
            else:
                self.assertEqual(base64_decode(result["text"]), payload_text)
