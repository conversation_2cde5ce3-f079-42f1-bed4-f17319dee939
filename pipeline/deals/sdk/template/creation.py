from typing import Any

from django.db import connection

from deals import services as deals_services
from deals.models import Template
from deals.html import contains_unsafe_html
from deals.sdk.template.dto import TemplateDTO, FileTemplateDTO, TextTemplateDTO, TemplateResponseDTO
from deals.services.fields.populating import all_required_signature_tags_exists_in_text
from deals.services.redlining import normalize_html_content
from integrations.services.hellosign.hellosign_template import HelloSignTemplateHandler
from shortlist.pydantic_utils.dto_utils import ensure_dto
from shortlist.pydantic_utils.pydantic_drf import validation_error_as_fail
from shortlist.result import Result


@validation_error_as_fail
def create_template(template_dto: TemplateDTO | dict[str, Any]) -> Result[TemplateResponseDTO]:
    """
    Creates a HelloSign or Text Template depend on given data (file or text)

    :example:

    >>> from deals import sdk as deals_sdk
    >>> deals_sdk.template.creation.create_template({"name": "Template name", ...})
    """
    template_dto = ensure_dto(template_dto, TemplateDTO)

    if template_dto.file:
        file_template_dto = FileTemplateDTO.model_validate(template_dto.model_dump(exclude={"text"}))
        return create_hellosign_template(file_template_dto)

    elif template_dto.text:
        text_template_dto = TextTemplateDTO.model_validate(template_dto.model_dump(exclude={"file"}))
        return create_text_template(text_template_dto)


@validation_error_as_fail
def create_hellosign_template(template_dto: FileTemplateDTO | dict[str, Any]) -> Result[TemplateResponseDTO]:
    """
    Creates a HelloSign Template

    :example:

    >>> from deals import sdk as deals_sdk
    >>> deals_sdk.template.creation.create_hellosign_template({"name": "Template name", "file": {}, ...})
    """
    template_dto = ensure_dto(template_dto, FileTemplateDTO)

    result = HelloSignTemplateHandler().create(template_dto.model_dump())
    return Result.success(TemplateResponseDTO(**result))


@validation_error_as_fail
def create_text_template(template_dto: TextTemplateDTO | dict[str, Any]) -> Result[TemplateResponseDTO]:
    """
    Creates a Text Template

    :example:

    >>> from deals import sdk as deals_sdk
    >>> deals_sdk.template.creation.create_text_template({"name": "Template name", "text": "Foo", ...})
    """
    template_dto: TextTemplateDTO = ensure_dto(template_dto, TextTemplateDTO)
    available_signers = deals_services.signers.get_available_signers(template_dto.type)
    deals_services.signers.validate_signers(template_dto.signers, available_signers).unwrap(error_key="signers")

    if contains_unsafe_html(template_dto.text):
        return Result.fail("Text template contains unsafe HTML")

    if not (
        check_result := all_required_signature_tags_exists_in_text(template_dto.text, [signer.role for signer in template_dto.signers])
    ):
        return check_result

    template = Template.objects.create(
        name=template_dto.name,
        confidential=template_dto.confidential,
        status=Template.STATUS_READY,
        external_id=None,
        create_mode=Template.MODE_INTERNAL,
        partner_role=connection.tenant.tenant_terms["Partner"],
        file=None,
        text=normalize_html_content(template_dto.text),
        type=template_dto.type,
    )

    deals_services.signers.create_signers_mappings(template, template_dto.signers, available_signers)

    return Result.success(TemplateResponseDTO.model_validate(template))
