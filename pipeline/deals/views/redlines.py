import django_filters
from django_filters.rest_framework import FilterSet
from rest_framework import viewsets, serializers
from rest_framework.decorators import action
from rest_framework.response import Response

from clients import features
from deals.models import Redline
from deals.services.redlines import accept_redline, reject_redline
from shortlist.permissions import TenantFeaturesEnabled, TypedPermissionMixin
from shortlist.pydantic_utils.pydantic_drf import handle_validation_errors
from users.serializers import MinimalUserSeralizer


class RedlineFilter(FilterSet):
    since_id = django_filters.NumberFilter(field_name='id', lookup_expr='gt')
    since_date = django_filters.IsoDateTimeFilter(field_name='updated_at', lookup_expr='gt')

    class Meta:
        model = Redline
        fields = ("sign_request", "since_id", "since_date")


class RedlineSerializer(serializers.ModelSerializer):
    user = MinimalUserSeralizer(read_only=True)
    reviewed_by = MinimalUserSeralizer(read_only=True)

    class Meta:
        model = Redline
        fields = (
            "comment",
            "created_at",
            "id",
            "quoted_text",
            "reviewed_at",
            "reviewed_by",
            "sign_request",
            "status",
            "suggestion",
            "updated_at",
            "user",
            "parent_message",
        )
        read_only_fields = fields


class RedlineForBuyerViewSet(TypedPermissionMixin, viewsets.ReadOnlyModelViewSet):
    queryset = Redline.objects.all()
    serializer_class = RedlineSerializer
    permission_app = "buyer"
    permission_type = "agreement"
    permission_classes = [TenantFeaturesEnabled(features.REDLINING)]
    filterset_class = RedlineFilter

    @action(detail=True, methods=["POST"])
    @handle_validation_errors
    def accept(self, request, *args, **kwargs):
        redline = self.get_object()
        return Response(accept_redline(redline).unwrap())

    @action(detail=True, methods=["POST"])
    @handle_validation_errors
    def reject(self, request, *args, **kwargs):
        redline = self.get_object()
        return Response(reject_redline(redline).unwrap())


class RedlineForVendorViewSet(TypedPermissionMixin, viewsets.ReadOnlyModelViewSet):
    queryset = Redline.objects.all()
    serializer_class = RedlineSerializer
    permission_app = "vendor"
    permission_type = "agreement"
    permission_classes = [TenantFeaturesEnabled(features.REDLINING)]
    filterset_class = RedlineFilter

    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset.filter(sign_request__vendor=self.request.user.vendor)


class RedlineForGuestViewSet(TypedPermissionMixin, viewsets.ReadOnlyModelViewSet):
    queryset = Redline.objects.all()
    serializer_class = RedlineSerializer
    permission_app = "guest"
    permission_type = "agreement"
    permission_classes = [TenantFeaturesEnabled(features.REDLINING)]
    filterset_class = RedlineFilter

    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset.filter(sign_request__vendor_guests=self.request.user)
