from deals.models import Redline
from deals.notifications.utils import (
    group_by_recipients,
    should_skip_notification,
    collect_redlines_in_period_ended_by,
)
from events.event_types import RedlinesAggregated


def send_aggregated_notification_about_new_redlines(redline_id: int) -> None:
    """
    Triggers events which send email notifications with aggregated redlines.
    Called by Celery task after a delay to allow for aggregation.
    """
    try:
        redline = Redline.objects.get(pk=redline_id)
    except Redline.DoesNotExist:
        return

    queryset = Redline.objects.filter(sign_request=redline.sign_request_id)

    if should_skip_notification(redline, timestamp_field="created_at", base_queryset=queryset):
        return

    redlines = collect_redlines_in_period_ended_by(redline, timestamp_field="created_at", base_queryset=queryset)
    grouped_redlines = group_by_recipients(redlines, exclude_field="user_id")

    for user_ids, redlines in grouped_redlines.items():
        RedlinesAggregated(user_ids=user_ids, redlines=redlines, sign_request=redline.sign_request)
