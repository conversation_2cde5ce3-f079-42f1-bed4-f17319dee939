import inflection
from django.apps import apps
from django.core.exceptions import ObjectDoesNotExist
from django.db import models
from django.forms import model_to_dict
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from shortlist.current_user import get_actor_for_current_user
from shortlist.db_fields import <PERSON><PERSON><PERSON><PERSON>
from shortlist.formatter import DateFormatter

from audit.tenant.managers import ActivityLogManager

from .. import base_models


class LoginHistory(base_models.BaseLoginHistory):
    pass


ACTION_LOG_VERBOSE = {
    'contract_status_changed': 'Contract was set to: {contract_status}',
    'contract_created': 'Contract was created by {executor_name}',
    'contract_creation_failed': 'Contract has encountered an issue during the automated creation process.',
    'contract_ending_soon': 'Contract was set to: ending soon',
    'accept_contract_reminder': '{executor_name} sent a reminder to the Vendor',
    'contract_accepted': '{executor_name} accepted Contract',
    'contract_rejected': '{executor_name} rejected Contract with a reason: {data_rejection_reason}',
    'contract_approved': 'Contract approved',
    'contract_canceled': [
        (lambda event: event.get('contract_cancellation_reason') == 'user_requested', 'Contract was canceled by the user'),
        (
            lambda event: event.get('contract_cancellation_reason') == 'start_date_expired',
            'Contract was canceled due to the expiration of the contract start date',
        ),
        (
            lambda event: event.get('contract_cancellation_reason') == 'workflow_disqualified',
            'Contract was canceled due to disqualification of the contract workflow',
        ),
    ],
    'contract_review_workflow_started': 'Workflow {vendor_workflow_workflow_name} started',
    'contract_review_workflow_ended': 'Workflow {vendor_workflow_workflow_name} ended',
    'contract_review_workflow_disqualified': 'Workflow {vendor_workflow_workflow_name} disqualified',
    'contract_automation_workflow_started': 'Workflow {vendor_workflow_workflow_name} started',
    'contract_automation_workflow_ended': 'Workflow {vendor_workflow_workflow_name} ended',
    'contract_payment_created': 'New Payment was created',
    'contract_owner_changed': 'Contract owner {previous_contract_owner_name} changed to {current_contract_owner_name}',
    'contract_signer_changed': 'Contract signer {previous_contract_signer_name} changed to {current_contract_signer_name}',
    'contract_payment_assigned_buyer_changed': (
        'Contract payment assigned buyer {previous_assigned_buyer_name} changed to {current_assigned_buyer_name}'
    ),
    'contract_internal_settings_changed': '{changes}',
    'contract_ten99_insurance_purchased': '{vendor_name} bought the 1099 insurance: {purchased_insurance_coverages}',
    'contract_ten99_insurance_uploaded': '{vendor_name} uploaded the 1099 insurance: {uploaded_insurance_coverages}',
    'contract_ten99_insurance_under_review': '{vendor_name} 1099 insurance is under KYC review',
    'contract_ten99_insurance_approved': '{executor_name} approved 1099 insurance: {approved_insurance_coverages}',
    'contract_ten99_insurance_rejected': '{executor_name} rejected 1099 insurance. Rejection reason: {insurance_rejection_reasons}',
    'contract_ten99_insurance_canceled': '1099 insurance was canceled',
    'contract_ten99_invoice_created': '1099 invoice was created for an amount of USD {invoice_amount}',
    'vendor_added_to_on_boarding': 'Added by {executor_name} to {onboarding_workflows_for_vendor_workflow_name}',
    'vendor_moved_to_stage_manually': 'Moved by {executor_name} to {stage_for_vendor_full_name}',
    'vendor_moved_to_stage': 'Moved to {stage_for_vendor_full_name}',
    'on_boarding_completed_by_vendor': 'Onboarding finished',
    'on_boarding_disqualified': 'Disqualified by {executor_name} at the stage: {stage_name}',
    'on_boarding_stage_unlocked': 'Onboarding stage named: {stage_full_name}. Has been unlocked for editing.',
    'on_boarding_stage_data_updated': 'Edited stage, {summary} updated',
    'on_boarding_stage_entry_condition_evaluated': (
        'Evaluating entry condition for stage "{stage_full_name}" <br>'
        'Condition was {condition_repr} <br>'
        '{condition} <br>'
        'Context was {condition_context} <br>'
        'Result: {result_text}'
    ),
    'payment_created': [
        (lambda event: event.get('payment_reset'), '{executor_name} re-added payment'),
        (lambda event: not event.get('payment_reset'), '{executor_name} added payment'),
    ],
    'payment_status_updated': [
        (lambda event: event.get('deleted'), '{executor_name} deleted payment'),
        (lambda event: event.get('updated'), '{executor_name} updated payment'),
        (
            lambda event: event.get('payment_status') == 'scheduled',
            '{executor_name} scheduled payment for {payment_scheduled_for:dt%d %b %Y}',
        ),
        (lambda event: event.get('payment_status') == 'processing', 'Payment processing started'),
        (lambda event: event.get('payment_status') == 'paid', '{executor_name} marked payment as paid'),
        (lambda event: event.get('payment_status') == 'rejected', '{executor_name} rejected payment with reason: {payment_reason}'),
        (
            lambda event: event.get('previous_status') == 'paid' and event.get('payment_status') != 'paid',
            '{executor_name} marked payment as unpaid',
        ),
        (lambda event: event.get('payment_status') == 'in_flight', '{executor_name} moved payment to In-flight'),
        (lambda event: True, '{executor_name} {payment_status} payment'),
    ],
    'payment_flag_added': '{executor_name} added flag for Payment',
    'payment_flag_removed': '{executor_name} removed flag for Payment',
    'payment_flag_manually_added': '{executor_name} added flag for Payment. Issue description: {reason}',
    'payment_flag_manually_removed': '{executor_name} removed flag for Payment',
    'send_manual_custom_notifications': '{executor_name} sent manual notification for {stage_full_name}',
    'task_accepted': '{executor_name} accepted task',
    'task_rejected': '{executor_name} rejected task',
    'task_vendor_mark_as_completed': '{executor_name} marked task as completed',
    'task_added': 'Task was created by {executor_name}',
    'task_status_changed': [
        (lambda event: event.get('executor_name') == 'System', 'The status was changed to {task_status}'),
        (lambda event: event.get('task_status') == 'draft', '{executor_name} changed the status to draft'),
        (lambda event: event.get('task_status') == 'pending', '{executor_name} changed the status to pending'),
        (lambda event: event.get('task_status') == 'accepted', '{executor_name} accepted the task'),
        (lambda event: event.get('task_status') == 'completed', '{executor_name} changed the status to completed'),
        (lambda event: event.get('task_status') == 'rejected', '{executor_name} rejected the task'),
        (lambda event: event.get('task_status') == 'canceled', '{executor_name} cancelled the task'),
        (lambda event: event.get('task_status') == 'archived', '{executor_name} archived the task'),
        (lambda event: event.get('task_status') == 'under_review', 'The task was set as under review'),
    ],
    'task_updated': [
        (lambda event: len((event.get('object_diff') or {}).keys()) == 1, '{edited_fields} was edited by {executor_name}'),
        (lambda event: len((event.get('object_diff') or {}).keys()) > 1, '{edited_fields} were edited by {executor_name}'),
        (lambda event: len((event.get('object_diff') or {}).keys()) == 0, 'Task was edited by {executor_name}'),
    ],
    'buyer_added_payment_for_task': '{executor_name} added payment to task',
    'task_timesheet_approved': 'The timesheet was approved by {executor_name}',
    'task_timesheet_rejected': 'The timesheet was rejected by {executor_name}',
    'task_timesheet_submitted': 'The timesheet was submitted by {executor_name}',
    'timesheet_approved': 'The timesheet was approved by {executor_name}',
    'timesheet_rejected': 'The timesheet was rejected by {executor_name}',
    'timesheet_submitted': 'The timesheet was submitted by {executor_name}',
    'timesheet_billed': 'The timesheet was marked as billed by {executor_name}',
    'timesheet_un_billed': 'The timesheet was unmarked as billed by {executor_name}',
    'vendor_1099nec_file_enabled': 'File {file_name} was enabled',
    'vendor_1099nec_consent_changed': [
        (lambda event: event.get('digital_delivery_consent') is True, '{executor_name} gave consent for digital delivery'),
        (lambda event: event.get('digital_delivery_consent') is False, '{executor_name} declined consent for digital delivery'),
    ],
    'vendor_1099nec_form_downloaded': '{executor_name} downloaded the file {file_name}',
}


def calculate_request_data_summary(event):
    current = event.get('current') or {}
    updated = event.get('updated') or {}

    changed = [field for field, value in current.items() if field not in updated or field in updated and value != updated.get(field)]
    changed += [field for field in updated if field not in current and field not in changed]

    if changed:
        updated = len(changed)
        return {'summary': "{} {}".format(updated, "field" if updated == 1 else "fields")}
    return {'summary': 'no fields'}


def calculate_edited_fields(event):
    key_overwrite_map = {'managers': 'task_managers'}
    keys = list(event.get('object_diff', {}).keys())
    for old_key, new_key in key_overwrite_map.items():
        if old_key in keys:
            keys[keys.index(old_key)] = new_key
    return {'edited_fields': ", ".join(sorted(map(inflection.humanize, keys)))}


def contract_internal_settings_changes(event: dict):
    attribute_messages = {
        "name": "name changed to {value}",
        "payment_template.assigned_buyer": "payment approver changed to {value}",
        "invoicing_template.assigned_buyer": "invoicing approver changed to {value}",
        "budget.contracted_budget.allocated_amount": "allocated budget amount changed to {value}",
    }

    changes = []
    for attribute, message in attribute_messages.items():
        value = event.get(attribute)
        if value:
            changes.append(message.format(value=value))

    return {'changes': f"Contract settings were edited: {', '.join(changes)}"} if changes else {}


EXTENDED_CONTEXT = {
    'on_boarding_stage_data_updated': calculate_request_data_summary,
    'task_updated': calculate_edited_fields,
    'contract_internal_settings_changed': contract_internal_settings_changes,
}


class ActionLog(models.Model):
    app_label = models.CharField(max_length=100)
    model = models.CharField(_('python model class name'), max_length=100)
    object_id = models.CharField(max_length=250)
    serialized_object = JSONField()
    event_type = models.CharField(max_length=50, db_index=True)
    data = JSONField()
    actor = models.ForeignKey('users.Actor', default=get_actor_for_current_user, on_delete=models.CASCADE)
    created_at = models.DateTimeField(editable=False)
    # visible_for column indicates who can see action log entry besides admin users
    visible_for = models.ForeignKey(
        'users.User', null=True, blank=True, default=None, on_delete=models.CASCADE, related_name='visible_action_logs'
    )
    objects = ActivityLogManager()

    class Meta:
        verbose_name = _('Action Log')
        verbose_name_plural = _('Action Log')
        ordering = ('-created_at',)

    def save(self, *args, **kwargs):
        if self.pk is None:
            self.created_at = timezone.now()
            if self.serialized_object is None:
                self.serialized_object = self.serialize_object(self.content_object)
        super().save(*args, **kwargs)

    @property
    def content_object(self):
        try:
            return apps.get_model(self.app_label, self.model).objects.get(pk=self.object_id)
        except ObjectDoesNotExist:
            return None

    @classmethod
    def serialize_object(cls, content_object):
        if not content_object:
            return {}

        # Identify all FileField names in the model
        exclude_fields = [field.name for field in content_object._meta.fields if isinstance(field, models.FileField)]

        return model_to_dict(content_object, exclude=exclude_fields)

    def message(self):
        verbose = ACTION_LOG_VERBOSE.get(self.event_type, '')
        context = self.get_context_data()
        if isinstance(verbose, list):
            return DateFormatter(next(msg for (func, msg) in verbose if func(context)) or '').vformat(context)
        return DateFormatter(verbose).vformat(context)

    def get_context_data(self):
        context = self.data.copy()
        if not context.get('executor_name'):
            context['executor_name'] = self.actor.name
        extend = EXTENDED_CONTEXT.get(self.event_type)
        if extend:
            context.update(extend(self.data.copy()))
        return context
