# Assumptions

Assumptions:

No. 1
When Task is created its timesheet template is created and can't be changed.

- To use new template or template with changes, it'll be applied only for new tasks.
- To migrate template in existing task we need to ensure different procedure
  (if needed, there are many edge cases to be solved here, for example: while changing
  from week to month - how to treat already approved timesheets? etc.).

No. 2
In the first MVP notes column will not use custom fields, they will be added in future sprints. At start it'll be normal text field used in the system.

# API proposal

## Timesheet API

### Templates list & Task templates GET/PATCH

- `GET /api/timesheet/templates/` - get list of Timesheet Templates to be shown on a form
- `GET & PATCH /api/task_templates/{task_template_id}/` - save new field `timesheet_template` as a new field that can be edited for a template

### Config

W przypadku kiedy jest określone end date taska to niezależnie
od tego generujemy okresy do obecnie trwającego tygodnia.

TaskConfig is created together with Task.

`GET /api/timesheet/task_config/{task_id}/`

```json
{
    "description": "Please fill form below with breaks",
    "entry_types": [
        {"id": 1, "type": "work", "name": "Work work", "billable": true},
        {"id": 2, "type": "break", "name": "AM break", "billable": true},
        {"id": 3, "type": "break", "name": "PM break", "billable": false},
        {"id": 4, "type": "text", "name": "Notes", "billable": false}
    ],
    "periods": [
      {
        "start_date": "2021-01-01",
        "end_date": "2021-01-07"
      },
      {
        "start_date": "2021-01-08",
        "end_date": "2021-01-14"
      }
    ]
}
```

## List

Returns list of timesheet objects for given task.

Optional features in the future: pagination, more filtering & sorting options.

Possible filters:
- task
- status

`GET /api/timesheet/task_timesheets/?task=123`

```json
{
    "id": 666,
    "task_name": "...",
    "project_name": "...",
    "status": "unsubmitted",
    "status_comment": null,
    "start_date": "2021-01-01",
    "end_date": "2021-01-07",
    "description": "Instruction taken from template",
    "minutes_worked": 60,
    "minutes_break": 15,
    "entries": [
      {
          "id": 555,
          "entry_type": 1,
          "day": "2021-01-01",
          "start_time": "2021-01-01 08:00:00+00:00",
          "end_time": "2021-01-01 17:00:00+00:00",
          "declined": null,
          "data": null
      },
      {
          "id": 433,
          "entry_type": 2,
          "day": "2021-01-01",
          "start_time": "2021-01-01 10:00:00+00:00",
          "end_time": "2021-01-01 10:30:00+00:00",
          "declined": null,
          "data": null
      },
      {
          "id": 777,
          "day": "2021-01-01",
          "entry_type": 3,
          "start_time": null,
          "end_time": null,
          "declined": true,
          "data": null
      },
      {
          "id": 888,
          "day": "2021-01-01",
          "entry_type": 4,
          "start_time": null,
          "end_time": null,
          "declined": null,
          "data": "Some notes to row/day"
      }
    ]
}
```

### Details

`GET /api/timesheet/task_timesheets/{timesheet_id}/`

```json
{
    "id": 666,
    "task_name": "...",
    "project_name": "...",
    "status": "unsubmitted",
    "status_comment": null,
    "start_date": "2021-01-01",
    "end_date": "2021-01-07",
    "description": "Instruction taken from template",
    "minutes_worked": 60,
    "minutes_break": 15,
    "entries": [
      {
          "id": 555,
          "entry_type": 1,
          "day": "2021-01-01",
          "start_time": "2021-01-01 08:00:00+00:00",
          "end_time": "2021-01-01 17:00:00+00:00",
          "declined": null,
          "data": null
      },
      {
          "id": 433,
          "entry_type": 2,
          "day": "2021-01-01",
          "start_time": "2021-01-01 10:00:00+00:00",
          "end_time": "2021-01-01 10:30:00+00:00",
          "declined": null,
          "data": null
      },
      {
          "id": 777,
          "day": "2021-01-01",
          "entry_type": 3,
          "start_time": null,
          "end_time": null,
          "declined": true,
          "data": null
      },
      {
          "id": 888,
          "day": "2021-01-01",
          "entry_type": 4,
          "start_time": null,
          "end_time": null,
          "declined": null,
          "data": "Some notes to row/day"
      }
    ]
}
```

### Create

`POST /api/timesheet/task_timesheets/`

```json
{
    "task": 123,
    "start_date": "2021-01-01"
}
```

Response: same as for details.

### Update

`PATCH /api/timesheet/task_timesheets/{timesheet_id}/`

Important: every time the whole "rows" will be updated so client needs to be aware of that.

```json
{
    "status": "under_review",
    "entries": [
      {
          "entry_type": 1,
          "day": "2021-01-01",
          "start_time": "2021-01-01 17:00:00+00:00",
          "end_time": "2021-01-01 17:00:00+00:00",
          "declined": null,
          "data": null
      },
      {
          "entry_type": 2,
          "day": "2021-01-01",
          "start_time": "2021-01-01 17:00:00+00:00",
          "end_time": "2021-01-01 17:00:00+00:00",
          "declined": null,
          "data": null
      },
      {
          "entry_type": 3,
          "day": "2021-01-01",
          "start_time": null,
          "end_time": null,
          "declined": true,
          "data": null
      },
      {
          "entry_type": 4,
          "day": "2021-01-01",
          "start_time": null,
          "end_time": null,
          "declined": null,
          "data": "Lol lol"
      }
    ]
}
```

Possible basic response status to handle:
- 2xx - OK
- 4xx - client error
- 5xx - server error

#### Example error responses

First timesheet entry has errors in `day` field:

```
{
    "entries": [
        {
          "day": ["This day is out of range of this Timesheet"]
        }
    ]
}
```

User can't change status of timesheet to the one provided in data:

```
{
  "non_field_errors": ["Status not allowed to be changed"]
}
```

User can't change entries in timesheet:

```
{
  "non_field_errors": ["Entries not allowed to be changed in this status"]
}
```

User can't change status_comment field:

```
{
  "non_field_errors": ["Status comment not allowed to be changed"]
}
```

# API usage examples from FE perspective

## Timesheet list view

Used in:

- Task view
- Timesheet for approval view
- All timesheet view

- `GET /api/timesheet/task_config/{task_id}/` - get template and periods to be shown
- `GET /api/timesheet/task_timesheets/?task=123` fill shown periods with existing data

## View timesheet after receiving notifications

- `GET /api/timesheet/task_timesheets/{timesheet_id}/` get full information about timesheet to be edited

## Edit not existing timesheet view

- `POST /api/timesheet/task_timesheets/` create new timesheet

## Edit existing timesheet view

- `GET /api/timesheet/task_timesheets/{timesheet_id}/` to render
- `PATCH /api/timesheet/task_timesheets/{timesheet_id}/` to make changes (state & rows as an atomic operations).

## Generate payment

- `POST /api/timesheet/task_timesheets/{timesheet_id}/payment/`

## Export

- `GET /api/timesheet/task_timesheets/?task=123&start_date__gte=...&status=123&end_date__lte=...&format=csv`

# Example data models

```
class WorkEntryType(object):
    slug = "work"
    billable = True
    name = "Work work"


class AMBreakEntryType(object):
    slug = "am_break"
    billable = False
    name = "AM break"


class PMBreakEntryType(object):
    slug = "pm_break"
    billable = False
    name = "PM break"


class TimesheetTemplate(object):
    def __init___(self):
        self.mode = "week"
        self.mode_config = {"week_start": "monday"}
        self.instruction = "Some instruction for vendor"
        self.entry_types = [WorkEntryType(), AMBreakEntryType(), PMBreakEntryType()]


class TimesheetTaskConfig(object):
    def __init__(self):
        self.task = None
        # copy from Template while task is created
        self.mode = "week"
        self.mode_config = {"week_start": "monday"}
        self.instruction = "Some instruction how to fill timesheet for this task"
        self.entry_types = [WorkEntryType(), AMBreakEntryType(), PMBreakEntryType()]
        # synchronized with task dates:
        self.start_date = "2021-01-01"
        self.end_date = "2021-02-01"


class TimesheetPeriod(object):
    def __init__(self):
        self.uuid = None
        self.config = TimesheetTaskConfig()
        self.status = "unsubmitted"
        self.start_date = "2021-01-01"
        self.end_date = "2021-01-07"
        self.summary = {}
        self.comments = []
        self.entries = []
        self.logs = []
        self.external_approvers = []


class TimesheetPeriodEntry(object):
    def __init__(self):
        self.entry_type = WorkEntryType()
        self.billable = True
        self.declined = "2021-01-01 21:13:01"
        self.day = "2021-01-01"
        self.start = "06:00"
        self.end = "14:00"
        self.timezone = "US"
```
