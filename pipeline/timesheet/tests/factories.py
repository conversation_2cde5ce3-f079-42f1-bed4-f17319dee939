import factory

from django.db import connection

from clients import features
from shortlist.factories import ShortlistDjangoModelFactory
from tasks.models import TaskTemplate, TaskTimeSheetPeriod
from timesheet.calendar_utils import Period, WeekDay
from timesheet.models import EntryType, Timesheet, TimesheetEntry, TimesheetTemplate

# Imported here for backward compatibility.
# When all imports of timesheet.tests.factories.create_user will be changed,
# then this can be removed.
from users.tests.factories import create_user  # noqa: F401, isort:skip


def create_timesheet_template(with_extended_config=True, **kwargs):
    mode = kwargs.pop("mode", Period.WEEK.value)
    template = TimesheetTemplate.objects.create(
        name="Test",
        week_start_day=WeekDay.MONDAY.value,
        mode=mode,
        description="Instruction for workers how to fill timesheet",
        **kwargs,
    )
    if with_extended_config:
        EntryType.objects.create(template=template, type=EntryType.TYPE_WORK, name="Work", order=0)
        EntryType.objects.create(template=template, type=EntryType.TYPE_BREAK, name="AM break", order=1)
        EntryType.objects.create(template=template, type=EntryType.TYPE_BREAK, name="PM break", order=2)
        EntryType.objects.create(template=template, type=EntryType.TYPE_TEXT, name="Notes", order=3, billable=False)
        EntryType.objects.create(
            template=template,
            type=EntryType.TYPE_CHOICE,
            name="Choose",
            order=4,
            choices=[dict(value='1st_choice', label="First"), dict(value='2nd_choice', label="Second")],
        )
    return template


def create_task_template(timesheet_template):
    return TaskTemplate.objects.create(timesheet_template=timesheet_template)


def create_timesheets(task, entries=False):
    result = []
    for period in task.timesheettaskconfig.get_periods("UTC"):
        result.append(create_timesheet(task, period["start_date"], period["end_date"], entries=entries))
    return result


def create_timesheet(task, start_date=None, end_date=None, entries=False, **kwargs):
    if start_date is None:
        start_date = task.timesheettaskconfig.get_periods("UTC")[0]["start_date"]
    if end_date is None:
        end_date = task.timesheettaskconfig.get_periods("UTC")[0]["end_date"]
    timesheet = Timesheet.objects.create(
        task=task,
        start_date=start_date,
        end_date=end_date,
        **kwargs,
    )
    if entries:
        timesheet.timesheetentry_set.bulk_create(
            [
                TimesheetEntry(timesheet=timesheet, entry_type=entry_type["id"], day=start_date)
                for entry_type in task.timesheettaskconfig.entry_types
            ]
        )
    return timesheet


def create_weekly_timesheet(task, vendor, date_start, date_end, **kwargs):
    minutes_per_day = kwargs.pop("minutes_per_day", {})
    return TaskTimeSheetPeriod.objects.create(
        task=task, vendor=vendor, date_start=date_start, date_end=date_end, minutes_per_day=minutes_per_day, **kwargs
    )


def create_timesheet_depending_on_feature(task, vendor, start, end, **kwargs):
    if connection.tenant.has_features(features.TASKS_TIME_SHEET_EXTENDED):
        return create_timesheet(task, start, end, **kwargs)
    else:
        return create_weekly_timesheet(task, vendor, start, end, **kwargs)


class TimesheetTemplateFactory(ShortlistDjangoModelFactory):
    class Meta:
        model = TimesheetTemplate

    name = factory.Faker("text", max_nb_chars=50)
    week_start_day = WeekDay.MONDAY.value
    mode = Period.WEEK.value
    description = factory.Faker("paragraph")
