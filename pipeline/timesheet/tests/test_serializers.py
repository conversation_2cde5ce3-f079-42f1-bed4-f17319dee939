import copy
import datetime
import itertools

from unittest import mock

from parameterized import parameterized
import pytz
from django.test import TestCase
from rest_framework.exceptions import PermissionDenied

from shortlist.tests.helpers import TenantTestCase
from tasks.tests.factories import TaskFactory, TaskGroupFactory
from timesheet.models import Timesheet
from timesheet.serializers import TimesheetEntrySerializer, TimesheetUpdateSerializer, TimesheetCreateSerializer
from timesheet.tests.factories import create_task_template, create_timesheet_template, create_user
from timesheet.tests.utils import REQUIRED_FEATURE_FLAGS


class TimesheetSerializerTestCase(TenantTestCase):
    def setUp(self):
        super().setUp()
        project = TaskGroupFactory(name="Test project 1", currency="USD")
        timesheet_template = create_timesheet_template()
        task_template = create_task_template(timesheet_template=timesheet_template)
        with self.features_enabled(*REQUIRED_FEATURE_FLAGS):
            self.task = TaskFactory(
                task_template=task_template,
                name="Test task 1",
                task_group=project,
                vendor=create_user("vendor").vendor,
                date_start=datetime.date(2021, 1, 1),
                date_end=datetime.date(2021, 2, 1),
                live_at=datetime.datetime(2021, 1, 1, tzinfo=pytz.UTC),
            )
            self.periods = self.task.timesheettaskconfig.get_periods("UTC")
        self.all_transitions = set(
            itertools.combinations(
                [None, Timesheet.STATUS_UNSUBMITTED, Timesheet.STATUS_UNDER_REVIEW, Timesheet.STATUS_REJECTED, Timesheet.STATUS_APPROVED], 2
            )
        )

    def run_transition_test(self, context, from_state, to_state, allowed=True):
        data = {
            "start_date": self.periods[0]["start_date"],
            "end_date": self.periods[0]["end_date"],
            "task": self.task.id,
            "status": to_state,
        }
        instance = None
        if from_state:
            # it's a case for update, so first create Timesheet to be changed
            instance_data = copy.copy(data)
            instance_data["status"] = from_state
            instance_data["task"] = self.task
            instance = Timesheet.objects.create(**instance_data)
            serializer = TimesheetUpdateSerializer(data=data, instance=instance, context=context)
        else:
            serializer = TimesheetCreateSerializer(data=data, context=context)

        try:
            with mock.patch("shortlist.permissions.get_current_user", return_value=context["user"]):
                serializer.is_valid()
                errors = serializer.errors
        finally:
            if instance:
                # cleanup before next usage of run_transition_test
                instance.delete()

        expected_errors = {}
        if not allowed:
            expected_errors = {"non_field_errors": ["Status not allowed to be changed"]}

        self.assertEqual(
            errors,
            expected_errors,
            f"check failed on {from_state} -> {to_state}",
        )

    def test_vendor_status_transition_permissions(self):
        allowed = {
            (None, Timesheet.STATUS_UNSUBMITTED),
            (None, Timesheet.STATUS_UNDER_REVIEW),
            (Timesheet.STATUS_UNSUBMITTED, Timesheet.STATUS_UNSUBMITTED),
            (Timesheet.STATUS_UNSUBMITTED, Timesheet.STATUS_UNDER_REVIEW),
            (Timesheet.STATUS_REJECTED, Timesheet.STATUS_UNDER_REVIEW),
        }
        not_allowed = self.all_transitions - allowed
        context = {"user": create_user("vendor")}

        for from_state, to_state in allowed:
            self.run_transition_test(context, from_state, to_state, allowed=True)

        for from_state, to_state in not_allowed:
            self.run_transition_test(context, from_state, to_state, allowed=False)

    def test_buyer_status_transition_permissions(self):
        context = {"user": create_user("buyer_admin")}
        allowed = {
            (None, Timesheet.STATUS_UNSUBMITTED),
            (Timesheet.STATUS_UNDER_REVIEW, Timesheet.STATUS_APPROVED),
            (Timesheet.STATUS_UNDER_REVIEW, Timesheet.STATUS_REJECTED),
            (Timesheet.STATUS_APPROVED, Timesheet.STATUS_REJECTED),
            (Timesheet.STATUS_REJECTED, Timesheet.STATUS_APPROVED),
        }
        not_allowed = self.all_transitions - allowed

        for from_state, to_state in allowed:
            self.run_transition_test(context, from_state, to_state, allowed=True)

        for from_state, to_state in not_allowed:
            self.run_transition_test(context, from_state, to_state, allowed=False)

    def test_guest_edit_permissions(self):
        context = {"user": create_user("guest")}
        allowed = {
            (None, Timesheet.STATUS_UNSUBMITTED),
            (Timesheet.STATUS_UNDER_REVIEW, Timesheet.STATUS_APPROVED),
            (Timesheet.STATUS_UNDER_REVIEW, Timesheet.STATUS_REJECTED),
            (Timesheet.STATUS_APPROVED, Timesheet.STATUS_REJECTED),
            (Timesheet.STATUS_REJECTED, Timesheet.STATUS_APPROVED),
        }
        not_allowed = self.all_transitions - allowed

        for from_state, to_state in not_allowed:
            with self.assertRaises(PermissionDenied):
                self.run_transition_test(context, from_state, to_state, allowed=False)

        self.task.timesheets_approvers.add(context["user"])
        for from_state, to_state in allowed:
            self.run_transition_test(context, from_state, to_state, allowed=True)

    def test_task_without_timesheettaskconfig(self):
        self.task.timesheettaskconfig.delete()
        data = {
            "start_date": self.periods[0]["start_date"],
            "end_date": self.periods[0]["end_date"],
            "task": self.task.id,
        }
        context = {"user": create_user("vendor")}

        serializer = TimesheetCreateSerializer(data=data, context=context)

        self.assertFalse(serializer.is_valid())
        self.assertEqual(serializer.errors, {"task": ["Missing timesheet config, task was probably created without timesheet template"]})

    def run_status_transition_test(self, context, from_state, to_state):
        data = {
            "start_date": self.periods[0]["start_date"],
            "end_date": self.periods[0]["end_date"],
            "task": self.task.id,
            "status": to_state,
        }
        instance = None
        if from_state:
            instance_data = copy.copy(data)
            instance_data["status"] = from_state
            instance_data["task"] = self.task
            instance = Timesheet.objects.create(**instance_data)
            serializer = TimesheetUpdateSerializer(data=data, instance=instance, context=context)
        else:
            serializer = TimesheetCreateSerializer(data=data, context=context)

        try:
            with mock.patch("shortlist.permissions.get_current_user", return_value=context["user"]):
                serializer.is_valid()
                instance = serializer.save()

            self.assertEqual(instance.status_updated_at, instance.updated_at)
            self.assertEqual(instance.status_updated_by, context["user"])
        finally:
            instance.delete()

    def test_updating_status_fields_as_guest(self):
        context = {"user": create_user("guest")}
        self.task.timesheets_approvers.add(context["user"])
        transitions = {
            (Timesheet.STATUS_UNDER_REVIEW, Timesheet.STATUS_APPROVED),
            (Timesheet.STATUS_UNDER_REVIEW, Timesheet.STATUS_REJECTED),
            (Timesheet.STATUS_APPROVED, Timesheet.STATUS_REJECTED),
            (Timesheet.STATUS_REJECTED, Timesheet.STATUS_APPROVED),
        }

        for from_state, to_state in transitions:
            self.run_status_transition_test(context, from_state, to_state)

    def test_updating_status_fields_as_vendor(self):
        context = {"user": create_user("vendor")}
        transitions = {
            (None, Timesheet.STATUS_UNDER_REVIEW),
            (Timesheet.STATUS_UNSUBMITTED, Timesheet.STATUS_UNDER_REVIEW),
            (Timesheet.STATUS_REJECTED, Timesheet.STATUS_UNDER_REVIEW),
        }

        for from_state, to_state in transitions:
            self.run_status_transition_test(context, from_state, to_state)

    def test_updating_status_fields_as_buyer(self):
        context = {"user": create_user("buyer_admin")}
        self.task.timesheets_approvers.add(context["user"])
        transitions = {
            (Timesheet.STATUS_UNDER_REVIEW, Timesheet.STATUS_APPROVED),
            (Timesheet.STATUS_UNDER_REVIEW, Timesheet.STATUS_REJECTED),
            (Timesheet.STATUS_APPROVED, Timesheet.STATUS_REJECTED),
            (Timesheet.STATUS_REJECTED, Timesheet.STATUS_APPROVED),
        }

        for from_state, to_state in transitions:
            self.run_status_transition_test(context, from_state, to_state)

    @parameterized.expand(
        [
            ("buyer_regular", True),
            ("buyer_admin", True),
            ("vendor", False),
        ]
    )
    def test_updating_billed_field(self, user_role, allowed_change):
        default_value = False
        data = {
            "start_date": self.periods[0]["start_date"],
            "end_date": self.periods[0]["end_date"],
            "task": self.task.id,
            "billed": True,
        }
        context = {"user": create_user(user_role)}

        create_serializer = TimesheetCreateSerializer(data=data, context=context)
        create_serializer.is_valid()
        created_instance = create_serializer.save()
        excepted_billed = True if allowed_change else default_value
        self.assertEqual(created_instance.billed, excepted_billed)

        update_data = {**data, **{"billed": not created_instance.billed}}
        update_serializer = TimesheetUpdateSerializer(data=update_data, instance=created_instance, context=context)
        update_serializer.is_valid()
        updated_instance = update_serializer.save()
        excepted_billed = update_data["billed"] if allowed_change else created_instance.billed
        self.assertEqual(updated_instance.billed, excepted_billed)

        created_instance.delete()


class TimesheetEntrySerializerTestCase(TestCase):
    def test_valid(self):
        data = {
            "entry_type": 1,
            "day": "2021-07-15",
            "start_time": "2021-07-15 20:00:00+00:00",
            "end_time": "2021-07-15 23:59:59+00:00",
            "declined": None,
            "data": None,
        }

        serializer = TimesheetEntrySerializer(data=data)

        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data["entry_type"], 1)
        self.assertEqual(serializer.validated_data["day"], datetime.date(2021, 7, 15))
        self.assertEqual(serializer.validated_data["start_time"], datetime.datetime(2021, 7, 15, 20, tzinfo=pytz.UTC))
        self.assertEqual(serializer.validated_data["end_time"], datetime.datetime(2021, 7, 15, 23, 59, 59, tzinfo=pytz.UTC))
        self.assertEqual(serializer.validated_data["declined"], None)
        self.assertEqual(serializer.validated_data["data"], None)

    def test_valid_overnight(self):
        data = {
            "entry_type": 1,
            "day": "2021-07-15",
            "start_time": "2021-07-15 20:00:00+00:00",
            "end_time": "2021-07-15 04:00:00+00:00",
            "declined": None,
            "data": None,
        }

        serializer = TimesheetEntrySerializer(data=data)

        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.data["entry_type"], 1)
        self.assertEqual(serializer.validated_data["day"], datetime.date(2021, 7, 15))
        self.assertEqual(serializer.validated_data["start_time"], datetime.datetime(2021, 7, 15, 20, tzinfo=pytz.UTC))
        self.assertEqual(serializer.validated_data["end_time"], datetime.datetime(2021, 7, 16, 4, tzinfo=pytz.UTC))
        self.assertEqual(serializer.data["declined"], None)
        self.assertEqual(serializer.data["data"], None)

    def test_invalid_start_end_time_not_in_provided_day(self):
        data = {
            "entry_type": 1,
            "day": "2021-07-15",
            "start_time": "2021-07-16 20:00:00+00:00",
            "end_time": "2021-07-15 04:00:00+00:00",
            "declined": None,
            "data": None,
        }

        serializer = TimesheetEntrySerializer(data=data)

        self.assertFalse(serializer.is_valid())
        self.assertEqual(serializer.errors, {"start_time": ["Start time not in provided day"]})

        data["start_time"] = "2021-07-15 20:00:00+00:00"
        data["end_time"] = "2021-07-16 20:00:00+00:00"

        serializer = TimesheetEntrySerializer(data=data)

        self.assertFalse(serializer.is_valid())
        self.assertEqual(serializer.errors, {"end_time": ["End time not in provided day"]})

    def test_invalid_declined_cant_be_mixed_with_start_end_time(self):
        data = {
            "entry_type": 1,
            "day": "2021-07-15",
            "start_time": "2021-07-15 20:00:00+00:00",
            "end_time": "2021-07-15 22:00:00+00:00",
            "declined": True,
            "data": None,
        }

        serializer = TimesheetEntrySerializer(data=data)

        self.assertFalse(serializer.is_valid())
        self.assertEqual(serializer.errors, {"declined": ["Can't be declined if start/end time provided"]})
