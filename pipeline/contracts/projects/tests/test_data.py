from dateutil.relativedelta import relativedelta

from django.utils.timezone import now

from contracts.models import Contract
from contracts.projects.data import ContractTaskTemplates, TaskGroupKey
from contracts.projects.services import update_contract_tasks
from contracts.tests.factories import (
    ContractFactory,
    ContractTaskTemplateFactory,
    TaskDataFactory,
    contract_task_template_updates_factory,
)
from contracts.tests.utils import ContractTestCaseMixin
from shortlist.tests.helpers import TenantTestCase
from tasks.tests.factories import TaskFactory, TaskGroupFactory


class ContractTaskTemplateTestCase(ContractTestCaseMixin, TenantTestCase):
    def setUp(self):
        super().setUp()

        self.task_groups = [TaskGroupFactory(), TaskGroupFactory()]
        start_date = now().date()
        end_date = start_date + relativedelta(months=+6)
        self.tasks_data = [
            TaskDataFactory.json_dict(date_start=start_date, date_end=end_date),
            TaskDataFactory.json_dict(date_start=start_date + relativedelta(days=+10), date_end=end_date + relativedelta(days=-10)),
            TaskDataFactory.json_dict(date_start=start_date, date_end=end_date, task_group=dict(id=self.task_groups[0].id)),
            TaskDataFactory.json_dict(date_start=start_date, date_end=end_date, task_group=dict(id=self.task_groups[1].id)),
            TaskDataFactory.json_dict(date_start=start_date, date_end=end_date, task_group=dict(name="Test Project 1")),
            TaskDataFactory.json_dict(date_start=start_date, date_end=end_date, task_group=dict(name="Test Project 1")),
            TaskDataFactory.json_dict(date_start=start_date, date_end=end_date, task_group=dict(name="Test Project 2")),
        ]
        self.contract: Contract = ContractFactory(
            name="Test",
            start_date=start_date,
            end_date=end_date,
            currency='PLN',
            with_statement_of_work=True,
            create_task_templates=[{'task_data': task_data} for task_data in self.tasks_data],
            accepted=True,
        )

    def test_get_new_task_group_data(self):
        task_group_data = self.contract.get_task_templates().get_new_task_group_data({'name': "Test task group"})

        self.assertEqual(task_group_data['name'], "Test task group")
        self.assertEqual(task_group_data['date_start'], self.contract.start_date)
        self.assertEqual(task_group_data['date_end'], self.contract.end_date)
        self.assertEqual(task_group_data['currency'], 'PLN')

    def test_get_task_templates_to_create_task(self):
        new_tasks_data = self.contract.get_task_templates().get_task_templates_to_create_task()

        self.assertEqual(len(new_tasks_data.get_tasks_data()), 7)
        self.assertEqual(len(new_tasks_data.task_groups), 5)
        for _, task_data in new_tasks_data.get_tasks_data():
            self.assertEqual(task_data['contract']['id'], self.contract.id)
            self.assertEqual(task_data['vendor']['id'], self.contract.partner_id)

        default_task_group_key = TaskGroupKey.from_data({'name': f"{self.contract.name} - Project"})
        self.assertEqual(
            new_tasks_data.task_templates[default_task_group_key][0].task_data['task_group']['name'], f"{self.contract.name} - Project"
        )
        self.assertEqual(
            new_tasks_data.task_templates[default_task_group_key][1].task_data['task_group']['name'], f"{self.contract.name} - Project"
        )
        self.assertEqual(
            new_tasks_data.task_templates[TaskGroupKey.from_data({'id': self.task_groups[0].id})][0].task_data['task_group']['id'],
            self.task_groups[0].id,
        )
        self.assertEqual(
            new_tasks_data.task_templates[TaskGroupKey.from_data({'id': self.task_groups[1].id})][0].task_data['task_group']['id'],
            self.task_groups[1].id,
        )
        self.assertEqual(
            new_tasks_data.task_templates[TaskGroupKey.from_data({'name': "Test Project 1"})][0].task_data['task_group']['name'],
            "Test Project 1",
        )
        self.assertEqual(
            new_tasks_data.task_templates[TaskGroupKey.from_data({'name': "Test Project 1"})][1].task_data['task_group']['name'],
            "Test Project 1",
        )
        self.assertEqual(
            new_tasks_data.task_templates[TaskGroupKey.from_data({'name': "Test Project 2"})][0].task_data['task_group']['name'],
            "Test Project 2",
        )
        self.assertEqual(new_tasks_data.get_tasks_data()[0][1]['task_group']['name'], f"{self.contract.name} - Project")
        self.assertEqual(new_tasks_data.get_tasks_data()[1][1]['task_group']['name'], f"{self.contract.name} - Project")
        self.assertEqual(new_tasks_data.get_tasks_data()[2][1]['task_group']['id'], self.task_groups[0].id)
        self.assertEqual(new_tasks_data.get_tasks_data()[3][1]['task_group']['id'], self.task_groups[1].id)
        self.assertEqual(new_tasks_data.get_tasks_data()[4][1]['task_group']['name'], "Test Project 1")
        self.assertEqual(new_tasks_data.get_tasks_data()[5][1]['task_group']['name'], "Test Project 1")
        self.assertEqual(new_tasks_data.get_tasks_data()[6][1]['task_group']['name'], "Test Project 2")

    def test_get_task_templates_to_update_task(self):
        update_contract_tasks(self.contract)
        task_template_updates = contract_task_template_updates_factory(self.contract)

        updated_tasks = ContractTaskTemplates(
            task_templates=task_template_updates, contract=self.contract
        ).get_task_templates_to_update_task(self.contract.list_task_templates())

        self.assertTaskTemplatesEqual(updated_tasks, task_template_updates)

    def test_get_initial_tasks_data_to_duplicate(self):
        update_contract_tasks(self.contract)
        contract = Contract.objects.get(id=self.contract.id)

        initial_tasks_data = contract.get_task_templates().get_initial_tasks_data_to_duplicate()

        self.maxDiff = None
        self.assertEqual(
            [task_data for _, task_data in initial_tasks_data],
            [
                {
                    "name": self.tasks_data[0]['name'],
                    "date_end": self.tasks_data[0]['date_end'],
                    "date_start": self.tasks_data[0]['date_start'],
                    "task_group": {"name": "Test - Project"},
                    "description": self.tasks_data[0]['description'],
                    "budget_total": self.tasks_data[0]['budget_total'],
                    "budget_rate_type": self.tasks_data[0]['budget_rate_type'],
                    "budget_rate_per_time_unit": self.tasks_data[0]['budget_rate_per_time_unit'],
                    "budget_time_units_worked": self.tasks_data[0]['budget_time_units_worked'],
                    'vendor': {'id': contract.partner.id},
                },
                {
                    "name": self.tasks_data[1]['name'],
                    "date_end": self.tasks_data[1]['date_end'],
                    "date_start": self.tasks_data[1]['date_start'],
                    "task_group": {"name": "Test - Project"},
                    "description": self.tasks_data[1]['description'],
                    "budget_total": self.tasks_data[1]['budget_total'],
                    "budget_rate_type": self.tasks_data[1]['budget_rate_type'],
                    "budget_rate_per_time_unit": self.tasks_data[1]['budget_rate_per_time_unit'],
                    "budget_time_units_worked": self.tasks_data[1]['budget_time_units_worked'],
                    'vendor': {'id': contract.partner.id},
                },
                {
                    "name": self.tasks_data[2]['name'],
                    "date_end": self.tasks_data[2]['date_end'],
                    "date_start": self.tasks_data[2]['date_start'],
                    "task_group": {"id": self.task_groups[0].id},
                    "description": self.tasks_data[2]['description'],
                    "budget_total": self.tasks_data[2]['budget_total'],
                    "budget_rate_type": self.tasks_data[2]['budget_rate_type'],
                    "budget_rate_per_time_unit": self.tasks_data[2]['budget_rate_per_time_unit'],
                    "budget_time_units_worked": self.tasks_data[2]['budget_time_units_worked'],
                    'vendor': {'id': contract.partner.id},
                },
                {
                    "name": self.tasks_data[3]['name'],
                    "date_end": self.tasks_data[3]['date_end'],
                    "date_start": self.tasks_data[3]['date_start'],
                    "task_group": {"id": self.task_groups[1].id},
                    "description": self.tasks_data[3]['description'],
                    "budget_total": self.tasks_data[3]['budget_total'],
                    "budget_rate_type": self.tasks_data[3]['budget_rate_type'],
                    "budget_rate_per_time_unit": self.tasks_data[3]['budget_rate_per_time_unit'],
                    "budget_time_units_worked": self.tasks_data[3]['budget_time_units_worked'],
                    'vendor': {'id': contract.partner.id},
                },
                {
                    "name": self.tasks_data[4]['name'],
                    "date_end": self.tasks_data[4]['date_end'],
                    "date_start": self.tasks_data[4]['date_start'],
                    "task_group": {"name": "Test Project 1"},
                    "description": self.tasks_data[4]['description'],
                    "budget_total": self.tasks_data[4]['budget_total'],
                    "budget_rate_type": self.tasks_data[4]['budget_rate_type'],
                    "budget_rate_per_time_unit": self.tasks_data[4]['budget_rate_per_time_unit'],
                    "budget_time_units_worked": self.tasks_data[4]['budget_time_units_worked'],
                    'vendor': {'id': contract.partner.id},
                },
                {
                    "name": self.tasks_data[5]['name'],
                    "date_end": self.tasks_data[5]['date_end'],
                    "date_start": self.tasks_data[5]['date_start'],
                    "task_group": {"name": "Test Project 1"},
                    "description": self.tasks_data[5]['description'],
                    "budget_total": self.tasks_data[5]['budget_total'],
                    "budget_rate_type": self.tasks_data[5]['budget_rate_type'],
                    "budget_rate_per_time_unit": self.tasks_data[5]['budget_rate_per_time_unit'],
                    "budget_time_units_worked": self.tasks_data[5]['budget_time_units_worked'],
                    'vendor': {'id': contract.partner.id},
                },
                {
                    "name": self.tasks_data[6]['name'],
                    "date_end": self.tasks_data[6]['date_end'],
                    "date_start": self.tasks_data[6]['date_start'],
                    "task_group": {"name": "Test Project 2"},
                    "description": self.tasks_data[6]['description'],
                    "budget_total": self.tasks_data[6]['budget_total'],
                    "budget_rate_type": self.tasks_data[6]['budget_rate_type'],
                    "budget_rate_per_time_unit": self.tasks_data[6]['budget_rate_per_time_unit'],
                    "budget_time_units_worked": self.tasks_data[6]['budget_time_units_worked'],
                    'vendor': {'id': contract.partner.id},
                },
            ],
        )

    def test_get_task_templates_with_task(self):
        contract = ContractFactory(with_statement_of_work=True)
        tasks_data = [
            TaskDataFactory.json_dict(id=TaskFactory(draft=True).id, task_group=dict(id=1), vendor=dict(id=1), contract=dict(id=1)),
            TaskDataFactory.json_dict(id=TaskFactory(draft=True).id, task_group=dict(id=1), vendor=dict(id=1), contract=dict(id=1)),
            TaskDataFactory.json_dict(),
            TaskDataFactory.json_dict(),
        ]
        for task_data in tasks_data:
            ContractTaskTemplateFactory(contract=contract, task_data=task_data, task_id=task_data.get('id'))

        existing_task_templates = contract.get_task_templates().get_task_templates_with_task()

        self.assertEqual(len(existing_task_templates), 2)
        for existing_task_template in existing_task_templates:
            self.assertIsNotNone(existing_task_template.task_data['id'])
