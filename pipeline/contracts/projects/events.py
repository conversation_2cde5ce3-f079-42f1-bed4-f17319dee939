import logging

from contracts.projects.services import list_contract_tasks
from contracts.services.contract import end_contract
from events.event_types import TaskStatusChanged
from tasks.models import Task

logger = logging.getLogger(__name__)


@TaskStatusChanged.watch
def on_task_status_changed(event: dict):
    try:
        task: Task | None = event.get('task')
        if (
            task
            and task.status == Task.STATUS_COMPLETED
            and task.contract
            and not task.contract.is_ended
            and (task.contract.template.with_statement_of_work or task.contract.template.with_time_and_material)
        ):
            tasks = list_contract_tasks(task.contract_id)
            if all(t['status'] == Task.STATUS_COMPLETED for t in tasks):
                end_contract(task.contract_id)
    except Exception:
        logger.exception("Contract end failed")
