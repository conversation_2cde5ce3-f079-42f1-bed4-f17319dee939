from decimal import Decimal

from dateutil.relativedelta import relativedelta

from django.utils.timezone import now

from contracts.payments.tests.factories import invoice_factory
from contracts.ten99.insurance import cancel_contract_ten99_insurance
from contracts.ten99.invoice import create_contract_ten99_invoice
from contracts.ten99.tests.factories import CoverageDetailsFactory, CoverageFactory
from contracts.ten99.tests.mocks import ten99_mock
from contracts.tests.factories import ContractFactory
from contracts.tests.utils import ActionLogTestCaseMixin
from documents.tests.factories import WorkItemCategoryFactory
from integrations.models import Ten99PolicyCoverageStatus, Ten99PolicyCoverageType
from integrations.sdk.ten99policy.events import (
    Ten99PProofOfInsuranceDecisionConfirmed,
    Ten99PVendorManualReview,
    Ten99PVendorObtainedNewInsurance,
    Ten99PVendorUploadedProofOfInsurance,
)
from integrations.services.ten99policy.data_provider.contract import ContractDataProvider
from onboarding.tests.utils import OnBoardingTestCase
from payments.invoices.model import InvoiceStatus
from shortlist.result import Result
from shortlist.tests.helpers import TenantTestCase


class Contract1099AuditTests(OnBoardingTestCase, ActionLogTestCaseMixin, TenantTestCase):
    with_admin_user = True

    def setUp(self):
        super().setUp()

        self.contract = ContractFactory(
            start_date=now().date() + relativedelta(days=+30),
            with_ten99_insurance=True,
            initiated=True,
        )
        ten99_stage = self._get_stage(self.contract.template.workflow, 1)
        self.ten99_vendor_stage = self._get_vendor_stage(self.contract.vendor_workflow, ten99_stage)
        self.ten99_data_provider = ContractDataProvider(self.ten99_vendor_stage)

    @ten99_mock()
    def test_log_ten99p_vendor_obtained_new_insurance(self, **_):
        Ten99PVendorObtainedNewInsurance(data_provider=self.ten99_data_provider)

        self.assertActionLog(
            self.contract,
            'contract_ten99_insurance_purchased',
            f"{self.contract.partner.full_name} bought the 1099 insurance: General Liability, Workers Compensation",
        )

    @ten99_mock(
        get_coverages_return=Result.success(
            CoverageDetailsFactory(
                coverages=[
                    CoverageFactory(type=Ten99PolicyCoverageType.general_liability, has_own_insurance=True),
                    CoverageFactory(type=Ten99PolicyCoverageType.workers_compensation, has_own_insurance=True),
                ]
            )
        )
    )
    def test_log_ten99p_vendor_uploaded_proof_of_insurance(self, **_):
        Ten99PVendorUploadedProofOfInsurance(data_provider=self.ten99_data_provider)

        self.assertActionLog(
            self.contract,
            'contract_ten99_insurance_uploaded',
            f"{self.contract.partner.full_name} uploaded the 1099 insurance: General Liability, Workers Compensation",
        )

    def test_log_ten99p_vendor_manual_review(self):
        Ten99PVendorManualReview(vendor_stage=self.ten99_vendor_stage)

        self.assertActionLog(
            self.contract,
            'contract_ten99_insurance_under_review',
            f"{self.contract.partner.full_name} 1099 insurance is under KYC review",
        )

    @ten99_mock(
        get_coverages_return=Result.success(
            CoverageDetailsFactory(
                coverages=[
                    CoverageFactory(
                        type=Ten99PolicyCoverageType.general_liability,
                        status=Ten99PolicyCoverageStatus.approved,
                    ),
                    CoverageFactory(
                        type=Ten99PolicyCoverageType.workers_compensation,
                        status=Ten99PolicyCoverageStatus.rejected,
                        rejection_reason="Test reason",
                    ),
                ]
            )
        )
    )
    def test_log_ten99p_proof_of_insurance_decision_confirmed(self, **_):
        Ten99PProofOfInsuranceDecisionConfirmed(data_provider=self.ten99_data_provider)

        self.assertActionLog(
            self.contract,
            'contract_ten99_insurance_approved',
            f"{self.user.full_name} approved 1099 insurance: General Liability",
        )
        self.assertActionLog(
            self.contract,
            'contract_ten99_insurance_rejected',
            f"{self.user.full_name} rejected 1099 insurance. Rejection reason: Workers Compensation - Test reason",
        )

    @ten99_mock()
    def test_log_ten99_contract_insurance_canceled(self, **_):
        self.contract.canceled()

        cancel_contract_ten99_insurance(self.contract.id)

        self.assertActionLog(
            self.contract,
            'contract_ten99_insurance_canceled',
            "1099 insurance was canceled",
        )

    @ten99_mock()
    def test_log_ten99_contact_invoice_created(self, **_):
        self.contract.ended()
        invoice_factory(
            line_items=[
                {'unit_price': Decimal(100), 'category_id': WorkItemCategoryFactory(is_work=True).id},
            ],
            contract=self.contract,
            status=InvoiceStatus.PAID,
        )

        create_contract_ten99_invoice(self.contract.id)

        self.assertActionLog(
            self.contract,
            'contract_ten99_invoice_created',
            "1099 invoice was created for an amount of USD 100.00",
        )
