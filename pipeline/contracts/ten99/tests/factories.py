import factory
from us import STATES

from contracts.ten99.models import Contract1099InsuranceData, Contract1099InsuranceTemplateData
from integrations.models import Ten99PolicyCoverageType
from integrations.sdk.ten99policy.service import CoveragesDetails
from integrations.services.ten99policy.data_model import Coverage
from integrations.tests.factories.ten99policy import JOB_CATEGORY_CODES_MAPPING


class Contract1099InsuranceTemplateDataFactory(factory.Factory):
    class Meta:
        model = Contract1099InsuranceTemplateData

    coverage_general_liability = False
    coverage_workers_compensation = False


class Contract1099InsuranceDataFactory(factory.Factory):
    class Meta:
        model = Contract1099InsuranceData

    insurance_job_location = factory.Faker('random_element', elements=[state.abbr for state in STATES])
    insurance_job_category = factory.Faker('random_element', elements=JOB_CATEGORY_CODES_MAPPING.values())


class CoverageFactory(factory.Factory):
    class Meta:
        model = Coverage


class CoverageDetailsFactory(factory.Factory):
    class Meta:
        model = CoveragesDetails

    coverages = factory.LazyFunction(
        lambda: [
            CoverageFactory(type=Ten99PolicyCoverageType.general_liability),
            CoverageFactory(type=Ten99PolicyCoverageType.workers_compensation),
        ]
    )
    reupload_allowed = False
