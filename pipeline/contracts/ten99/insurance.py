from decimal import Decimal

from django.utils.timezone import now

from contracts.models import Contract
from contracts.payments.pay_periods import compute_pay_periods
from contracts.ten99.audit import log_ten99_contract_insurance_canceled
from contracts.ten99.errors import Contract1099Error
from integrations.sdk.ten99policy.service import cancel_insurance, get_coverages
from integrations.services.ten99policy.data_model import Coverage
from schedulers.api import run_schedule
from users.models import Actor

CANCEL_CONTRACT_1099_INSURANCE_FUNCTION = "contracts.ten99.insurance.cancel_contract_ten99_insurance"


def contract_ten99_cancel_insurance_schedule_name(contract_id: int) -> str:
    return f"contract-ten99-cancel-insurance-{contract_id}"


def should_cancel_contract_ten99_insurance(contract: Contract) -> bool:
    try:
        check_cancel_contract_ten99_insurance(contract)
        return True
    except Contract1099Error:
        return False


def cancel_contract_ten99_insurance(contract_id: int):
    contract = Contract.objects.get(id=contract_id)

    check_cancel_contract_ten99_insurance(contract)

    try:
        cancel_insurance_result = cancel_insurance(onboarding_context=contract.vendor_workflow.context)
    except Exception as ex:
        raise Contract1099Error("Failed to cancel 1099 insurance for contract", {'errors': [str(ex)]}) from ex

    if cancel_insurance_result.is_failure:
        raise Contract1099Error("Failed to cancel 1099 insurance for contract", {'errors': cancel_insurance_result.errors})

    log_ten99_contract_insurance_canceled(contract)


def check_cancel_contract_ten99_insurance(contract):
    if now().date() >= contract.start_date:
        raise Contract1099Error("Contract 1099 insurance cannot be canceled after the contract start date")

    if not contract.is_canceled:
        raise Contract1099Error("Contract 1099 insurance cannot be canceled if the contract is not canceled")

    if contract.vendor_workflow_id is None:
        raise Contract1099Error("Contract does not have a vendor workflow")

    if not contract.template.with_ten99_insurance:
        raise Contract1099Error("Contract does not have 1099 insurance")


def run_cancel_contract_ten99_insurance(contract_id: int):
    run_schedule(
        name=contract_ten99_cancel_insurance_schedule_name(contract_id),
        execute_function=CANCEL_CONTRACT_1099_INSURANCE_FUNCTION,
        execute_params={'contract_id': contract_id},
        created_by=Actor.objects.system_actor,
    )


def all_contract_ten99_insurance_coverages(contract: Contract) -> list[Coverage]:
    try:
        coverage_details = get_coverages(onboarding_context=contract.vendor_workflow.context)
    except Exception as ex:
        raise Contract1099Error("Failed to get 1099 insurance coverages", {'errors': [str(ex)]}) from ex

    if coverage_details.is_failure:
        raise Contract1099Error("Failed to get 1099 insurance coverages", {'errors': coverage_details.errors})

    return coverage_details.value.coverages


def calculate_contract_ten99_insurance_total_amount(contract: Contract) -> Decimal:
    if contract.template.with_statement_of_work or contract.template.with_time_and_material:
        return contract.get_task_templates().calculate_tasks_total_budget()

    if contract.template.with_fixed_rate_payments:
        return Decimal(sum([pay_period.amount for pay_period in compute_pay_periods(contract)], Decimal(0)))

    raise Contract1099Error("Contract 1099 insurance amount cannot be calculated for this type of contract")
