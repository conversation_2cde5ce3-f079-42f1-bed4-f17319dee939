import logging

from contracts.ten99.invoice import contract_ten99_invoice_update_required, should_create_contract_ten99_invoice
from documents.models import Payment
from events.event_types import PaymentStatusUpdated
from payments.invoices.model import InvoiceStatus
from payments.invoices.repositories.django_orm.repository import invoice_status_from_status_code

logger = logging.getLogger(__name__)


@PaymentStatusUpdated.watch
def on_payment_status_updated(event: dict):
    try:
        payment: Payment = event['payment']
        if (
            invoice_status_from_status_code(payment.status) == InvoiceStatus.PAID
            and payment.contract
            and should_create_contract_ten99_invoice(payment.contract)
        ):
            contract_ten99_invoice_update_required(payment.contract, required=True)
    except Exception:
        logger.exception("Contract 1099 invoice update failed")
