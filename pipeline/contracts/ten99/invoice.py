import logging
from collections.abc import Iterable
from decimal import Decimal

from pydantic import BaseModel

from django.db import transaction

from contracts.models import Contract
from contracts.models.contract import ContractStatus
from contracts.payments.invoice import all_contract_invoices
from contracts.ten99.audit import log_ten99_contact_invoice_created
from contracts.ten99.errors import Contract1099Error
from contracts.ten99.insurance import all_contract_ten99_insurance_coverages
from documents.models import WorkItemCategory
from integrations.sdk.ten99policy.service import create_invoice_in_1099policy
from payments.invoices.model import Invoice, InvoiceStatus
from schedulers.api import run_schedule
from shortlist.result import Result
from users.models import Actor

logger = logging.getLogger(__name__)


CREATE_CONTRACT_1099_INVOICE_FUNCTION = "contracts.ten99.invoice.create_contract_ten99_invoice"


class Contract1099InvoiceData(BaseModel):
    invoice_updated: bool
    invoice_amount: Decimal


def contract_ten99_create_invoice_schedule_name(contract_id: int):
    return f"contract-ten99-create-invoice-{contract_id}"


def should_create_contract_ten99_invoice(contract: Contract) -> bool:
    try:
        check_create_contract_ten99_invoice(contract)
        return True
    except Contract1099Error:
        return False


def can_create_contract_ten99_invoice(contract: Contract) -> bool:
    if not should_create_contract_ten99_invoice(contract):
        return False

    if not contract_ten99_insurance_eligible_for_invoice(contract):
        return False

    invoice_amount_result = calculate_contract_ten99_invoice_amount(contract.id)
    if invoice_amount_result.is_failure:
        return False

    if contract.insurance_invoice_updated_amount == invoice_amount_result.value:
        return False

    return True


def check_create_contract_ten99_invoice(contract: Contract):
    if not contract.is_ended:
        raise Contract1099Error(f"Invalid contract status: {contract.status}")

    if contract.vendor_workflow_id is None:
        raise Contract1099Error("Contract does not have a vendor workflow")

    if not contract.template.with_ten99_insurance:
        raise Contract1099Error("Contract does not have 1099 insurance")


@transaction.atomic
def create_contract_ten99_invoice(contract_id: int) -> Contract1099InvoiceData:
    contract = Contract.objects.get(id=contract_id)

    check_create_contract_ten99_invoice(contract)

    if not contract_ten99_insurance_eligible_for_invoice(contract):
        raise Contract1099Error("Contract 1099 insurance is not eligible for invoice")

    invoice_amount_result = calculate_contract_ten99_invoice_amount(contract.id)
    if invoice_amount_result.is_failure:
        raise Contract1099Error("Calculate contract 1099 invoice amount failed", details={'errors': invoice_amount_result.errors})

    if contract.insurance_invoice_updated_amount == invoice_amount_result.value:
        return Contract1099InvoiceData(invoice_updated=False, invoice_amount=invoice_amount_result.value)

    try:
        create_invoice_result = create_invoice_in_1099policy(
            onboarding_context=contract.vendor_workflow.context,
            gross_pay=invoice_amount_result.value,
            pay_cycle_start_date=contract.start_date,
            pay_cycle_end_date=contract.end_date,
        )
    except Exception as ex:
        raise Contract1099Error("Failed to create 1099 invoice for contract", {'errors': [str(ex)]}) from ex

    if create_invoice_result.is_failure:
        raise Contract1099Error("Failed to create 1099 invoice for contract", {'errors': create_invoice_result.errors})

    contract_ten99_invoice_updated(contract, invoice_amount_result.value)
    log_ten99_contact_invoice_created(contract, invoice_amount_result.value)

    return Contract1099InvoiceData(invoice_updated=True, invoice_amount=invoice_amount_result.value)


def contract_ten99_insurance_eligible_for_invoice(contract: Contract) -> bool:
    insurance_coverages = all_contract_ten99_insurance_coverages(contract)

    insurance_bought_by_vendor = any(not coverage.has_own_insurance for coverage in insurance_coverages)
    return insurance_bought_by_vendor


def calculate_contract_ten99_invoice_amount(contract_id: int) -> Result[Decimal]:
    invoices = list(all_contract_invoices(contract_id, status=InvoiceStatus.PAID))
    if len(invoices) == 0:
        return Result.fail("Contract does not have paid invoices")

    insurance_invoice_amount = calculate_ten99_invoice_amount(invoices)
    if insurance_invoice_amount == Decimal(0):
        return Result.fail("Contract 1099 insurance invoice amount is zero")

    return Result.success(insurance_invoice_amount)


def calculate_ten99_invoice_amount(invoices: Iterable[Invoice]) -> Decimal:
    expense_category_ids = {line_item.category_id for invoice in invoices for line_item in invoice.line_items}
    work_category_ids = list(WorkItemCategory.objects.filter(id__in=expense_category_ids, is_work=True).values_list("id", flat=True))
    return Decimal(
        sum(
            [
                line_item.amount_with_tax
                for invoice in invoices
                for line_item in invoice.line_items
                if line_item.category_id in work_category_ids
            ],
            Decimal(0),
        )
    )


def contract_ten99_invoice_update_required(contract: Contract, *, required: bool):
    contract.insurance_invoice_update_required = required
    contract.save(update_fields=['insurance_invoice_update_required'])


def contract_ten99_invoice_updated(contract: Contract, insurance_invoice_amount: Decimal):
    contract.insurance_invoice_updated_amount = insurance_invoice_amount
    contract.save(update_fields=['insurance_invoice_updated_amount'])


def all_contracts_with_ten99_invoice_update_required() -> Iterable[Contract]:
    return Contract.objects.filter(status=ContractStatus.ENDED).with_ten99_invoice_update_required()


def update_contract_ten99_invoices():
    contracts = all_contracts_with_ten99_invoice_update_required()
    for contract in contracts:
        try:
            with transaction.atomic():
                contract_ten99_invoice_update_required(contract, required=False)
                if can_create_contract_ten99_invoice(contract):
                    run_create_contract_ten99_invoice(contract.id)
        except Exception:
            logger.exception(
                "Contract 1099 invoice update failed",
                extra={'contract_id': contract.id},
            )


def run_create_contract_ten99_invoice(contract_id: int):
    run_schedule(
        name=contract_ten99_create_invoice_schedule_name(contract_id),
        execute_function=CREATE_CONTRACT_1099_INVOICE_FUNCTION,
        execute_params={'contract_id': contract_id},
        created_by=Actor.objects.system_actor,
    )
