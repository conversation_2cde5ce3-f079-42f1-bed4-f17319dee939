import functools
import logging
from decimal import Decimal

from audit.tenant.models import ActionLog
from contracts.models import Contract
from integrations.models import Ten99PolicyCoverageStatus
from integrations.sdk.ten99policy.events import (
    Ten99PProofOfInsuranceDecisionConfirmed,
    Ten99PVendorManualReview,
    Ten99PVendorObtainedNewInsurance,
    Ten99PVendorUploadedProofOfInsurance,
)
from integrations.services.ten99policy.data_model import Coverage
from integrations.services.ten99policy.data_provider.contract import ContractDataProvider
from integrations.services.ten99policy.data_provider.impl import get_data_provider
from integrations.services.ten99policy.data_provider.interface import Ten99PDataProvider
from onboarding.models import OnBoardingStagesForVendor

logger = logging.getLogger(__name__)


def audit_log_error_handler(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception:
            logger.exception("Contract audit log failed")

    return wrapper


@Ten99PVendorObtainedNewInsurance.watch
@audit_log_error_handler
def log_ten99p_vendor_obtained_new_insurance(event: dict):
    from contracts.ten99.insurance import all_contract_ten99_insurance_coverages

    data_provider: Ten99PDataProvider = event['data_provider']
    if not isinstance(data_provider, ContractDataProvider):
        return

    contract = data_provider.get_contract()
    insurance_coverages = all_contract_ten99_insurance_coverages(contract)
    purchased_insurance_coverages = [
        insurance_coverage for insurance_coverage in insurance_coverages if not insurance_coverage.has_own_insurance
    ]
    event_data = {
        'vendor_name': contract.partner.full_name,
        'purchased_insurance_coverages': format_insurance_coverage_names(purchased_insurance_coverages),
    }
    ActionLog.objects.create_log('contract_ten99_insurance_purchased', contract, data=event_data)


@Ten99PVendorUploadedProofOfInsurance.watch
@audit_log_error_handler
def log_ten99p_vendor_uploaded_proof_of_insurance(event: dict):
    from contracts.ten99.insurance import all_contract_ten99_insurance_coverages

    data_provider: Ten99PDataProvider = event['data_provider']
    if not isinstance(data_provider, ContractDataProvider):
        return

    contract = data_provider.get_contract()
    insurance_coverages = all_contract_ten99_insurance_coverages(contract)
    uploaded_insurance_coverages = [
        insurance_coverage for insurance_coverage in insurance_coverages if insurance_coverage.has_own_insurance
    ]
    event_data = {
        'vendor_name': contract.partner.full_name,
        'uploaded_insurance_coverages': format_insurance_coverage_names(uploaded_insurance_coverages),
    }
    ActionLog.objects.create_log('contract_ten99_insurance_uploaded', contract, data=event_data)


@Ten99PVendorManualReview.watch
@audit_log_error_handler
def log_ten99p_vendor_manual_review(event: dict):
    vendor_stage: OnBoardingStagesForVendor = event['vendor_stage']
    data_provider = get_data_provider(vendor_stage)
    if not isinstance(data_provider, ContractDataProvider):
        return

    contract = data_provider.get_contract()
    event_data = {
        'vendor_name': contract.partner.full_name,
    }
    ActionLog.objects.create_log('contract_ten99_insurance_under_review', contract, data=event_data)


@Ten99PProofOfInsuranceDecisionConfirmed.watch
@audit_log_error_handler
def log_ten99p_proof_of_insurance_decision_confirmed(event: dict):
    from contracts.ten99.insurance import all_contract_ten99_insurance_coverages

    data_provider: Ten99PDataProvider = event['data_provider']
    if not isinstance(data_provider, ContractDataProvider):
        return

    contract = data_provider.get_contract()
    insurance_coverages = all_contract_ten99_insurance_coverages(contract)
    approved_insurance_coverages = [
        insurance_coverage for insurance_coverage in insurance_coverages if insurance_coverage.status == Ten99PolicyCoverageStatus.approved
    ]
    rejected_insurance_coverages = [
        insurance_coverage for insurance_coverage in insurance_coverages if insurance_coverage.status == Ten99PolicyCoverageStatus.rejected
    ]
    event_data = {
        'vendor_name': contract.partner.full_name,
    }
    if approved_insurance_coverages:
        ActionLog.objects.create_log(
            'contract_ten99_insurance_approved',
            contract,
            data={**event_data, 'approved_insurance_coverages': format_insurance_coverage_names(approved_insurance_coverages)},
        )

    if rejected_insurance_coverages:
        ActionLog.objects.create_log(
            'contract_ten99_insurance_rejected',
            contract,
            data={**event_data, 'insurance_rejection_reasons': format_insurance_coverage_rejection_reasons(rejected_insurance_coverages)},
        )


def log_ten99_contract_insurance_canceled(contract: Contract):
    ActionLog.objects.create_log('contract_ten99_insurance_canceled', contract)


def log_ten99_contact_invoice_created(contract: Contract, invoice_amount: Decimal):
    ActionLog.objects.create_log('contract_ten99_invoice_created', contract, data={'invoice_amount': invoice_amount})


def format_insurance_coverage_names(insurance_coverage_names: list[Coverage]) -> str:
    insurance_coverage_names = [format_insurance_coverage_name(insurance_coverage) for insurance_coverage in insurance_coverage_names]
    return ', '.join(insurance_coverage_names)


def format_insurance_coverage_name(insurance_coverage: Coverage) -> str:
    return ' '.join([w.capitalize() for w in str(insurance_coverage.type.value).split('_')])


def format_insurance_coverage_rejection_reasons(insurance_coverages: list[Coverage]) -> str:
    insurance_coverage_rejection_reasons = {
        format_insurance_coverage_name(insurance_coverage): insurance_coverage.rejection_reason
        for insurance_coverage in insurance_coverages
    }
    return ', '.join(
        [
            f"{insurance_coverage_name} - {rejection_reason}"
            for insurance_coverage_name, rejection_reason in insurance_coverage_rejection_reasons.items()
        ]
    )
