import logging
import typing
from collections.abc import Iterable
from dataclasses import dataclass, field
from typing import Literal, Union

from django.db import transaction
from django.utils.timezone import now

from budgets.api import create_budget, delete_budget
from budgets.configuration.api import get_budget_configuration
from contracts.acceptances.models import ContractSigner
from contracts.acceptances.services import (
    ContractAcceptingService,
    reassign_acceptance_signers,
)
from contracts.agreements.services import ContractSigningService
from contracts.contract.events import (
    trigger_contract_canceled_event,
    trigger_contract_created_event,
    trigger_contract_ended_event,
    trigger_contract_ending_soon_event,
    trigger_contract_owner_changed_event,
    trigger_contract_status_changed_event,
)
from contracts.invoicing.models import ContractInvoicingTemplate
from contracts.invoicing.services import run_create_contract_task_payment, should_create_contract_task_payment
from contracts.models.contract import (
    Contract,
    ContractCancellationReason,
    ContractData,
    ContractPeriod,
)
from contracts.payments.models import ContractPaymentTemplate
from contracts.payments.schedules import schedule_contract_payments
from contracts.payments.services import (
    can_approve_payments,
    reassign_payment_assigned_buyers,
)
from contracts.projects.models import ContractTaskTemplate
from contracts.projects.services import update_contract_tasks
from contracts.revisions.services import cancel_contract_revision
from contracts.revisions.validators import validate_can_cancel_contract_revision
from contracts.services.automations import (
    trigger_automations_on_contract_end,
    trigger_automations_on_contract_ending_soon,
    trigger_automations_on_contract_live,
)
from contracts.services.errors import ContractError
from contracts.services.partner import (
    create_partner,
    filter_valid_partner,
    invite_partner,
    is_partner_invited,
)
from contracts.services.schedules import (
    disable_contract_automatic_cancel,
    disable_contract_ending_soon_schedule,
    disable_contract_schedules,
    run_contract_end,
    schedule_contract_automatic_cancel,
    schedule_contract_end,
    schedule_contract_start,
)
from contracts.services.workflow import (
    cancel_workflow,
    restart_review_workflow,
    start_review_workflow,
)
from contracts.ten99.insurance import run_cancel_contract_ten99_insurance, should_cancel_contract_ten99_insurance
from contracts.ten99.invoice import (
    run_create_contract_ten99_invoice,
    should_create_contract_ten99_invoice,
)
from contracts.validators.contract import (
    validate_can_accept_contract,
    validate_can_archive_contract,
    validate_can_cancel_contract,
    validate_can_delete_contract,
    validate_can_duplicate_contract,
    validate_can_end_contract,
    validate_can_end_contract_now,
    validate_can_end_contract_soon,
    validate_can_initiate_contract,
    validate_can_initiate_vendor_group_contract,
    validate_can_restore_contract,
    validate_can_schedule_contract,
    validate_can_start_contract,
    validate_can_start_review_workflow,
    validate_contract,
)
from contracts.validators.partner import validate_vendor_group_member
from contracts.validators.template import validate_contract_template
from onboarding.models import create_dynamic_managers_from_contract
from preferences.models import recalculate_compliance
from preferences.tasks import recalculate_vendors_compliance
from reference.service import generate_unique_reference
from vendors.models import Vendor

if typing.TYPE_CHECKING:
    from users.models import User


logger = logging.getLogger(__name__)

START_CONTRACT_FUNCTION = "contracts.services.contract.start_contract"
END_CONTRACT_SOON_FUNCTION = "contracts.services.contract.end_contract_soon"
END_CONTRACT_NOW_FUNCTION = "contracts.services.contract.end_contract_now"
CANCEL_CONTRACT_FUNCTION = "contracts.services.contract.cancel_contract"
INITIATE_VENDOR_GROUP_CONTRACTS_FUNCTION = "contracts.services.contract.initiate_vendor_group_contracts"


@dataclass
class InitiateContractResult:
    partner_id: int
    result: Literal['success', 'failure']
    contract_id: int | None = None

    @classmethod
    def success(cls, partner_id: int, contract_id: int):
        return cls(partner_id=partner_id, contract_id=contract_id, result='success')

    @classmethod
    def failure(cls, partner_id: int):
        return cls(partner_id=partner_id, result='failure')


@dataclass
class InitiateVendorGroupContractsResult:
    results: list[InitiateContractResult] = field(default_factory=list)


def get_contract_acceptance_service(contract: Contract):
    if contract.requires_accepting:
        return ContractAcceptingService()
    elif contract.requires_signing:
        return ContractSigningService()

    raise ContractError("Contract does not define a valid acceptance")


def create_contract_draft(contract_data: ContractData) -> Contract:
    validate_contract_template(contract_data['template']).raise_if_invalid()

    contract = Contract.objects.create(**{**contract_data, 'reference_id': generate_contract_reference_id()})
    contract.template.increment_number_of_contracts()
    if contract.partner:
        recalculate_compliance([contract.partner])

    return contract


@transaction.atomic
def initiate_contract(contract: Contract):
    validate_can_initiate_contract(contract).raise_if_invalid()

    if contract.external_partner:
        contract.set_partner(create_partner(contract.external_partner, contract.created_by))

    schedule_contract_automatic_cancel(contract=contract)
    get_contract_acceptance_service(contract).initiate_acceptance(contract)
    transaction.on_commit(lambda: trigger_contract_created_event(contract))

    if contract.requires_review:
        start_contract_review(contract)
    else:
        start_contract_acceptance(contract)


def initiate_vendor_group_contracts(contract: Contract | int) -> InitiateVendorGroupContractsResult:
    from contracts.tasks import update_number_of_contracts

    if isinstance(contract, int):
        contract = Contract.objects.get(id=contract)

    validate_can_initiate_vendor_group_contract(contract).raise_if_invalid()

    result = InitiateVendorGroupContractsResult()
    vendors_success = []
    for vendor in filter_valid_partner(contract.vendor_group.vendors):
        try:
            vendor_contract = _initiate_vendor_group_contract_for_vendor(contract=contract, vendor=vendor)
            vendors_success.append(vendor)
            result.results.append(InitiateContractResult.success(partner_id=vendor.id, contract_id=vendor_contract.id))
        except Exception:
            logger.exception("Failed to initiate vendor group contract for vendor")
            result.results.append(InitiateContractResult.failure(partner_id=vendor.id))

    transaction.on_commit(lambda: update_number_of_contracts.delay(contract_template_ids=[contract.template_id]))
    if vendors_success:
        transaction.on_commit(lambda: recalculate_vendors_compliance.delay(vendor_ids=[v.id for v in vendors_success]))

    try:
        delete_contract(contract)
    except Exception:
        # if deleting the original contract fails just report the error and continue
        logger.exception("Failed to delete vendor group contract")

    return result


def _initiate_vendor_group_contract_for_vendor(contract: Contract | int, vendor: Union['Vendor', int]) -> Contract:
    if isinstance(contract, int):
        contract = Contract.objects.get(id=contract)

    if isinstance(vendor, int):
        vendor = Vendor.objects.get(id=vendor)

    validate_can_initiate_vendor_group_contract(contract).raise_if_invalid()
    validate_vendor_group_member(contract.vendor_group, vendor).raise_if_invalid()

    contract_data = {
        'partner': vendor,
        'vendor_group': None,
        'reference_id': generate_contract_reference_id(),
        'created_by_id': contract.created_by_id,
    }
    with transaction.atomic():
        vendor_contract = _duplicate_contract(contract, contract_data)

    try:
        initiate_contract(vendor_contract)
    except Exception:
        # if initiating the contract fails just report the error and leave the contract in draft status
        logger.exception("Failed to initiate vendor group contract for vendor")

    return vendor_contract


@transaction.atomic
def start_contract_review(contract):
    validate_can_start_review_workflow(contract).raise_if_invalid()

    create_dynamic_managers_from_contract(contract=contract)
    vendor_workflow = start_review_workflow(contract)

    contract.under_review(vendor_workflow.id)
    transaction.on_commit(lambda status=contract.status: trigger_contract_status_changed_event(contract, status))


@transaction.atomic
def review_contract(contract: Contract):
    if not contract.is_under_review:
        # for now, ignore any changes to the review workflow if the contract is not under review
        return

    if not contract.vendor_workflow.completed:
        raise ContractError("Workflow has not been completed")

    start_contract_acceptance(contract=contract)


@transaction.atomic
def start_contract_acceptance(contract: Contract, *, dont_send_vendor_email=False):
    get_contract_acceptance_service(contract).start_acceptance(contract, dont_send_vendor_email=dont_send_vendor_email)

    transaction.on_commit(lambda status=contract.status: trigger_contract_status_changed_event(contract, status))


def continue_acceptance(contract: Contract):
    if not contract.is_rejected:
        raise ContractError("Contract has not been rejected")

    get_contract_acceptance_service(contract).continue_acceptance(contract)

    transaction.on_commit(lambda status=contract.status: trigger_contract_status_changed_event(contract, status))


@transaction.atomic
def approve_contract(contract: Contract):
    from contracts.revisions.services import accept_contract_revision

    validate_can_accept_contract(contract).raise_if_invalid()

    if contract.is_rejected:
        continue_acceptance(contract)

    if contract.is_revision:
        accept_contract_revision(contract)
    else:
        if contract.period == ContractPeriod.VARIABLE_DATES:
            contract.set_start_date(now().date())

        if contract.start_datetime <= now():
            start_contract(contract=contract)
        else:
            schedule_contract(contract=contract)


def reject_contract(contract: Contract):
    from contracts.revisions.services import reject_contract_revision

    if contract.is_revision:
        reject_contract_revision(contract)
    else:
        contract.rejected()
        transaction.on_commit(lambda status=contract.status: trigger_contract_status_changed_event(contract, status))


def make_contract_available_to_partner(contract: Contract):
    if not is_partner_invited(contract.partner):
        invite_partner(contract.partner, contract.created_by)

    contract.set_visible_to_partner()


@transaction.atomic
def start_contract(contract: Contract | int):
    if isinstance(contract, int):
        contract = Contract.objects.get(id=contract)

    validate_can_start_contract(contract).raise_if_invalid()

    contract.live()
    disable_contract_automatic_cancel(contract=contract)

    if contract.template.with_statement_of_work or contract.template.with_time_and_material:
        update_contract_tasks(contract=contract)

    if contract.template.with_fixed_rate_payments:
        schedule_contract_payments(contract=contract)

    if contract.end_date:
        schedule_contract_end(contract=contract)

    recalculate_compliance([contract.partner])
    trigger_automations_on_contract_live(contract=contract)
    transaction.on_commit(lambda status=contract.status: trigger_contract_status_changed_event(contract, status))


@transaction.atomic
def schedule_contract(contract: Contract):
    validate_can_schedule_contract(contract).raise_if_invalid()

    contract.scheduled()
    schedule_contract_start(contract=contract)
    disable_contract_automatic_cancel(contract=contract)
    recalculate_compliance([contract.partner])
    transaction.on_commit(lambda status=contract.status: trigger_contract_status_changed_event(contract, status))


@transaction.atomic
def update_contract_end(contract: Contract):
    if contract.is_scheduled:
        # The contract has not been started so no schedules have been created yet.
        return

    validate_can_end_contract(contract).raise_if_invalid()

    expiration_period = contract.get_expiration_period()
    expiration_period_start_date = expiration_period.calculate_expiration_period_start_date() if expiration_period else None
    if contract.ending_soon and expiration_period_start_date and expiration_period_start_date > now():
        contract.not_ending()

    schedule_contract_end(contract=contract)

    if contract.template.with_fixed_rate_payments:
        schedule_contract_payments(contract=contract)


@transaction.atomic
def restart_contract(contract: Contract):
    validate_contract(contract).raise_if_invalid()

    contract.draft()
    schedule_contract_automatic_cancel(contract=contract)
    get_contract_acceptance_service(contract).restart_acceptance(contract)
    if contract.requires_review:
        restart_review_workflow(contract)
        contract.under_review(contract.vendor_workflow_id)
        transaction.on_commit(lambda status=contract.status: trigger_contract_status_changed_event(contract, status))
    else:
        start_contract_acceptance(contract=contract)

    recalculate_compliance([contract.partner])


@transaction.atomic
def end_contract_soon(contract: Contract | int):
    if isinstance(contract, int):
        contract = Contract.objects.get(id=contract)

    validate_can_end_contract_soon(contract).raise_if_invalid()

    contract.ending()
    trigger_automations_on_contract_ending_soon(contract=contract)
    transaction.on_commit(lambda: trigger_contract_ending_soon_event(contract))


@transaction.atomic
def end_contract_now(contract: Contract | int):
    if isinstance(contract, int):
        contract = Contract.objects.get(id=contract)

    validate_can_end_contract_now(contract).raise_if_invalid()

    contract.ended()
    disable_contract_ending_soon_schedule(contract)

    if contract.under_revision:
        for contract_revision in contract.revisions.all():
            if validate_can_cancel_contract_revision(contract_revision).is_valid:
                cancel_contract_revision(contract_revision)

    if should_create_contract_task_payment(contract):
        run_create_contract_task_payment(contract.id)

    if should_create_contract_ten99_invoice(contract):
        run_create_contract_ten99_invoice(contract.id)

    recalculate_compliance([contract.partner])
    trigger_automations_on_contract_end(contract=contract)
    transaction.on_commit(lambda status=contract.status: trigger_contract_status_changed_event(contract, status))
    transaction.on_commit(lambda: trigger_contract_ended_event(contract))


def end_contract(contract: Contract | int):
    if isinstance(contract, int):
        contract = Contract.objects.get(id=contract)

    validate_can_end_contract_now(contract).raise_if_invalid()

    contract.ending()
    run_contract_end(contract.id)


@transaction.atomic
def cancel_contract(
    contract: Contract | int,
    *,
    cancellation_reason: ContractCancellationReason = ContractCancellationReason.USER_REQUESTED,
    automatic: bool = False,
):
    if isinstance(contract, int):
        contract = Contract.objects.get(id=contract)

    validate_can_cancel_contract(contract).raise_if_invalid()

    contract.canceled()
    if not automatic:
        disable_contract_automatic_cancel(contract)

    disable_contract_schedules(contract)
    if contract.vendor_workflow:
        cancel_workflow(contract.vendor_workflow)

    if should_cancel_contract_ten99_insurance(contract):
        run_cancel_contract_ten99_insurance(contract.id)

    get_contract_acceptance_service(contract).reset_acceptance(contract)
    recalculate_compliance([contract.partner])
    transaction.on_commit(lambda status=contract.status: trigger_contract_status_changed_event(contract, status))
    transaction.on_commit(lambda: trigger_contract_canceled_event(contract, cancellation_reason=cancellation_reason))


@transaction.atomic
def delete_contract(contract: Contract | int):
    from contracts.tasks import update_number_of_contracts

    if isinstance(contract, int):
        contract = Contract.objects.get(id=contract)

    validate_can_delete_contract(contract).raise_if_invalid()

    contract.delete()

    if contract.payment_template_id:
        ContractPaymentTemplate.objects.filter(id=contract.payment_template_id).delete()

    if contract.invoicing_template_id:
        ContractInvoicingTemplate.objects.filter(id=contract.invoicing_template_id).delete()

    if contract.budget_id:
        delete_budget(contract.budget_id)

    if contract.partner:
        recalculate_compliance([contract.partner])

    transaction.on_commit(lambda: update_number_of_contracts.delay(contract_template_ids=[contract.template_id]))


@transaction.atomic
def archive_contract(contract: Contract | int):
    if isinstance(contract, int):
        contract = Contract.objects.get(id=contract)

    validate_can_archive_contract(contract).raise_if_invalid()

    contract.archive()
    recalculate_compliance([contract.partner])


@transaction.atomic
def restore_contract(contract: Contract | int):
    if isinstance(contract, int):
        contract = Contract.objects.get(id=contract)

    validate_can_restore_contract(contract).raise_if_invalid()

    contract.restore()
    recalculate_compliance([contract.partner])


def has_contracts_to_reassign(user: 'User') -> bool:
    if Contract.objects.for_user(user).exists():
        return True

    if ContractSigner.objects.for_user(user).exists():
        return True

    if ContractPaymentTemplate.objects.filter(assigned_buyer=user).exists():
        return True

    return False


def can_be_assigned_to_contracts(user: 'User') -> bool:
    if not can_approve_payments(user) and ContractPaymentTemplate.objects.filter(assigned_buyer=user).exists():
        return False

    return True


@transaction.atomic
def reassign_contracts(user: 'User', assignee: 'User'):
    contracts = Contract.objects.for_user(user)
    for contract in contracts:
        contract.assign(assignee)
        transaction.on_commit(lambda c=contract: trigger_contract_owner_changed_event(c, user))

    reassign_acceptance_signers(user, assignee)
    reassign_payment_assigned_buyers(user, assignee)


def duplicate_contract(contract: Contract) -> Contract:
    validate_can_duplicate_contract(contract).raise_if_invalid()

    contract_data = {
        'name': f"Copy of {contract.name}",
        'reference_id': generate_contract_reference_id(),
    }
    with transaction.atomic():
        duplicated_contract = _duplicate_contract(contract, contract_data)
        duplicated_contract.template.increment_number_of_contracts()

    if contract.partner:
        recalculate_compliance([contract.partner])

    return duplicated_contract


def _duplicate_contract(contract: Contract, contract_data: dict) -> Contract:
    if contract.payment_template_id:
        contract_data['payment_template'] = ContractPaymentTemplate.objects.create_revision(contract.payment_template)

    if contract.budget_id:
        result = create_budget(get_budget_configuration(contract.budget_id))
        if result.is_failure:
            raise ContractError("Contract budget error", details={'budget': result.errors_dump()})

        contract_data['budget_id'] = result.value.id

    if contract.invoicing_template_id:
        contract_data['invoicing_template'] = ContractInvoicingTemplate.objects.create_revision(contract.invoicing_template)

    duplicated_contract = Contract.objects.create_duplicate(contract, contract_data)
    ContractTaskTemplate.objects.create_duplicates(duplicated_contract, contract.get_task_templates())
    return duplicated_contract


def generate_contract_reference_id() -> str:
    return generate_unique_reference("CNT", suffix_length=6)


def get_contract_recipients(contract: Contract, with_partner: bool = True) -> Iterable['User']:
    recipients = set()

    if contract.created_by is not None:
        recipients.add(contract.created_by)

    if with_partner and contract.partner.first_contact is not None:
        recipients.add(contract.partner.first_contact)

    accepting_users = get_contract_acceptance_service(contract).get_acceptance_users(contract)
    if accepting_users:
        recipients.update([user for user in accepting_users if user is not None])

    return recipients
