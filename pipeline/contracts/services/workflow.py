from django.db import transaction
from django.db.models import Q

from audit.tenant.models import ActionLog
from contracts.compliance.services import (
    start_compliance_assessment_questionnaire,
)
from contracts.models import Contract
from contracts.models.contract import ContractCancellationReason
from events.base import EventBase
from events.event_types import (
    ContractAutomationWorkflowEnded,
    ContractAutomationWorkflowStarted,
    ContractReviewWorkflowDisqualified,
    ContractReviewWorkflowEnded,
    ContractReviewWorkflowStarted,
    OnBoardingCompletedByVendor,
    OnBoardingDisqualified,
    VendorEnteredStage,
)
from onboarding.models import (
    OnBoardingContext,
    OnBoardingStage,
    OnBoardingWorkflow,
    OnBoardingWorkflowsForVendor,
    create_multi_onboarding_context,
)
from onboarding.vendor_workflow import disqualify_vendor_workflow, move_vendor_workflows


def trigger_contract_review_workflow_started_event(contract: Contract, vendor_workflow: OnBoardingWorkflowsForVendor):
    ContractReviewWorkflowStarted(contract=contract, vendor_workflow=vendor_workflow)


def trigger_contract_review_workflow_ended_event(contract: Contract, vendor_workflow: OnBoardingWorkflowsForVendor):
    ContractReviewWorkflowEnded(contract=contract, vendor_workflow=vendor_workflow)


def trigger_contract_review_workflow_disqualified_event(contract: Contract, vendor_workflow: OnBoardingWorkflowsForVendor):
    ContractReviewWorkflowDisqualified(contract=contract, vendor_workflow=vendor_workflow)


def trigger_contract_automation_workflow_started_event(contract: Contract, vendor_workflow: OnBoardingWorkflowsForVendor):
    ContractAutomationWorkflowStarted(contract=contract, vendor_workflow=vendor_workflow)


def trigger_contract_automation_workflow_ended_event(contract: Contract, vendor_workflow: OnBoardingWorkflowsForVendor):
    ContractAutomationWorkflowEnded(contract=contract, vendor_workflow=vendor_workflow)


@transaction.atomic
def start_review_workflow(contract: Contract) -> OnBoardingWorkflowsForVendor:
    from contracts.services.contract import make_contract_available_to_partner

    context = create_multi_onboarding_context(contract)

    if is_compliance_assessment_workflow(contract.template.workflow_id):
        start_compliance_assessment_questionnaire(contract, context.name)

    vendor_workflow = start_workflow(contract.partner_id, contract.template.workflow_id, context=context)

    if any(
        vendor_stage.onboarding_template.is_partner_facing
        for vendor_stage in vendor_workflow.vendor_stages.select_related('onboarding_template')
    ):
        # In case the partner has been moved to a partner facing stage and the contract has not been made visible by VendorEnteredStage
        #  handler on_vendor_entered_stage as there is no connection between the contract and the vendor workflow yet.
        make_contract_available_to_partner(contract)

    transaction.on_commit(lambda: trigger_contract_review_workflow_started_event(contract, vendor_workflow))
    return vendor_workflow


@transaction.atomic
def restart_review_workflow(contract: Contract):
    move_vendor_workflows(
        OnBoardingWorkflowsForVendor.objects.filter(id=contract.vendor_workflow.id), contract.vendor_workflow.workflow.get_first_stage()
    )


@transaction.atomic
def start_automation_workflow(contract: Contract, workflow_id: int) -> int:
    vendor_workflow = start_workflow(contract.partner_id, workflow_id, create_multi_onboarding_context(contract))
    transaction.on_commit(lambda: trigger_contract_automation_workflow_started_event(contract, vendor_workflow))
    return vendor_workflow.id


def start_workflow(partner_id: int, workflow_id: int, context: OnBoardingContext) -> OnBoardingWorkflowsForVendor:
    OnBoardingWorkflowsForVendor.objects.bulk_set([partner_id], [workflow_id], [], context=context)
    return context.workflows.last()


def cancel_workflow(vendor_workflow: OnBoardingWorkflowsForVendor):
    if not vendor_workflow.disqualified:
        disqualify_vendor_workflow(vendor_workflow.id)


@VendorEnteredStage.watch
def on_vendor_entered_stage(event: EventBase):
    from contracts.services.contract import make_contract_available_to_partner

    stage: OnBoardingStage = event.get('stage')
    vendor_workflow_pk = event.get('vendor_workflow_pk')
    if not stage.is_partner_facing:
        return

    try:
        vendor_workflow = (
            OnBoardingWorkflowsForVendor.objects.filter(is_contract_review_workflow_filter()).values('id').get(id=vendor_workflow_pk)
        )
    except OnBoardingWorkflowsForVendor.DoesNotExist:
        return

    try:
        contract = Contract.objects.get(vendor_workflow=vendor_workflow['id'])
    except Contract.DoesNotExist:
        # This can happen if the first stage is a partner facing stage: this event is fired before the function start_review_workflow
        # exits and transaction is commited. At that moment the created vendor workflow has not been assigned to the contract yet.
        return

    make_contract_available_to_partner(contract)


@OnBoardingCompletedByVendor.watch
def on_onboarding_completed_by_vendor(event: EventBase):
    from contracts.services.contract import review_contract

    vendor_workflow: OnBoardingWorkflowsForVendor = event['onboarding_workflows_for_vendor']
    if is_contract_review_workflow(vendor_workflow):
        contract = Contract.objects.get(vendor_workflow=vendor_workflow)
        trigger_contract_review_workflow_ended_event(contract, vendor_workflow)
        review_contract(contract=contract)
    elif is_contract_automation_workflow(vendor_workflow):
        contract = vendor_workflow.context.context_object
        trigger_contract_automation_workflow_ended_event(contract, vendor_workflow)


@OnBoardingDisqualified.watch
def on_onboarding_disqualified(event: EventBase):
    from contracts.services.contract import cancel_contract

    vendor_workflow: OnBoardingWorkflowsForVendor = event['onboarding_workflows_for_vendor']
    if is_contract_review_workflow(vendor_workflow):
        contract = Contract.objects.get(vendor_workflow=vendor_workflow)
        trigger_contract_review_workflow_disqualified_event(contract, vendor_workflow)
        if not contract.is_canceled:
            cancel_contract(contract, cancellation_reason=ContractCancellationReason.WORKFLOW_DISQUALIFIED)


def is_contract_review_workflow(vendor_workflow: OnBoardingWorkflowsForVendor) -> bool:
    return (
        vendor_workflow.context.context_type == OnBoardingContext.CONTEXT_TYPE_CONTRACT
        and vendor_workflow.workflow.category == OnBoardingWorkflow.CATEGORY_CONTRACT
    )


def is_contract_review_workflow_filter() -> Q:
    return Q(context__context_type=OnBoardingContext.CONTEXT_TYPE_CONTRACT) & Q(workflow__category=OnBoardingWorkflow.CATEGORY_CONTRACT)


def is_contract_automation_workflow(vendor_workflow: OnBoardingWorkflowsForVendor):
    return (
        vendor_workflow.context.context_type == OnBoardingContext.CONTEXT_TYPE_CONTRACT
        and vendor_workflow.workflow.category != OnBoardingWorkflow.CATEGORY_CONTRACT
    )


def is_contract_reviewed(contract: Contract):
    return contract.vendor_workflow.completed


def is_compliance_assessment_workflow(workflow_id: int) -> bool:
    return OnBoardingStage.objects.filter(workflow_id=workflow_id, stage_type=OnBoardingStage.STAGE_WORKER_CLASSIFICATION).exists()


def is_ten99_insurance_workflow(workflow_id: int) -> bool:
    return OnBoardingStage.objects.filter(workflow_id=workflow_id, stage_type=OnBoardingStage.STAGE_TEN99P).exists()


def list_workflows_containing_contract_template_in_action_stage(template_id: int) -> list[dict]:
    qs = (
        OnBoardingStage.objects.filter(actions__action_name='create_contract', actions__action_params__contract_data__template=template_id)
        .values('workflow_id', 'workflow__name')
        .distinct('workflow_id')
        .order_by()
    )
    return [{'id': result['workflow_id'], 'name': result['workflow__name']} for result in qs]


@ContractReviewWorkflowStarted.watch
def log_contract_review_workflow_started_event(event):
    ActionLog.objects.create_log_for_event(event, content_object=event['contract'])


@ContractReviewWorkflowEnded.watch
def log_contract_review_workflow_ended_event(event):
    ActionLog.objects.create_log_for_event(event, content_object=event['contract'])


@ContractReviewWorkflowDisqualified.watch
def log_contract_review_workflow_disqualified_event(event):
    ActionLog.objects.create_log_for_event(event, content_object=event['contract'])


@ContractAutomationWorkflowStarted.watch
def log_contract_automation_workflow_started_event(event):
    ActionLog.objects.create_log_for_event(event, content_object=event['contract'])


@ContractAutomationWorkflowEnded.watch
def log_contract_automation_workflow_ended_event(event):
    ActionLog.objects.create_log_for_event(event, content_object=event['contract'])
