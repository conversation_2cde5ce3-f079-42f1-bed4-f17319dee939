import abc
from abc import abstractmethod
from dataclasses import dataclass
from datetime import datetime, UTC

from dateutil.relativedelta import relativedelta
from django.utils.timezone import now

from contracts.models.expiration import (
    ExpirationPeriod,
    ExpirationPeriodCalculation,
    ExpirationPeriodOverduePolicy,
)


class ExpirationPeriodCalculationMethod(abc.ABC):
    @abstractmethod
    def calculate_expiration_period_start_date(self) -> datetime | None:
        raise NotImplementedError

    @abstractmethod
    def calculate_expiration_period_duration(self) -> relativedelta | None:
        raise NotImplementedError


@dataclass
class ExpirationPeriodCalculationAutomatic(ExpirationPeriodCalculationMethod):
    expiration_period: ExpirationPeriod
    start_date: datetime
    end_date: datetime

    def calculate_expiration_period_start_date(self) -> datetime | None:
        duration = self.calculate_expiration_period_duration()
        if duration:
            return self.end_date - duration

        return None

    def calculate_expiration_period_duration(self) -> relativedelta | None:
        days = (self.end_date - self.start_date).days
        if days > 365:
            return relativedelta(months=3)
        elif days >= 182:
            return relativedelta(months=1)
        elif days >= 90:
            return relativedelta(weeks=2)
        elif days >= 30:
            return relativedelta(weeks=1)
        elif days >= 7:
            return relativedelta(days=2)
        else:
            return None


@dataclass
class ExpirationPeriodCalculationManual(ExpirationPeriodCalculationMethod):
    expiration_period: ExpirationPeriod
    start_date: datetime
    end_date: datetime

    def calculate_expiration_period_start_date(self) -> datetime | None:
        return self.end_date - self.calculate_expiration_period_duration()

    def calculate_expiration_period_duration(self) -> relativedelta | None:
        return self.expiration_period.period.to_relativedelta()


EXPIRATION_PERIOD_CALCULATIONS = {
    ExpirationPeriodCalculation.AUTOMATIC: ExpirationPeriodCalculationAutomatic,
    ExpirationPeriodCalculation.MANUAL: ExpirationPeriodCalculationManual,
}


@dataclass
class ExpirationPeriodService:
    expiration_period: ExpirationPeriod
    start_date: datetime
    end_date: datetime

    def __post_init__(self):
        expiration_period_calculation_class = EXPIRATION_PERIOD_CALCULATIONS.get(self.expiration_period.period_calculation)
        if not expiration_period_calculation_class:
            raise RuntimeError(f"Invalid period calculation {self.expiration_period.period_calculation}")

        self.expiration_period_calculation = expiration_period_calculation_class(
            expiration_period=self.expiration_period, start_date=self.start_date, end_date=self.end_date
        )

    def calculate_expiration_period_start_date(self) -> datetime | None:
        start_date = self.expiration_period_calculation.calculate_expiration_period_start_date()
        if not start_date:
            return None

        start_date = datetime(
            start_date.year,
            start_date.month,
            start_date.day,
            datetime.max.hour,
            datetime.max.minute,
            datetime.max.second,
            tzinfo=UTC,
        )
        if now() > start_date:
            if self.expiration_period.overdue_policy == ExpirationPeriodOverduePolicy.START_IMMEDIATELY:
                return start_date

            if self.expiration_period.overdue_policy == ExpirationPeriodOverduePolicy.DO_NOT_START:
                return None

            raise RuntimeError(f"Invalid overdue policy {self.expiration_period.overdue_policy}")

        return start_date

    def format_expiration_period_duration_text(self) -> str | None:
        duration = self.expiration_period_calculation.calculate_expiration_period_duration()
        if duration:
            return self._format_expiration_period_duration_text(duration)

        return None

    @staticmethod
    def _format_expiration_period_duration_text(duration: relativedelta) -> str:
        months = duration.months
        days = duration.days
        weeks, days = divmod(days, 7)

        time_units = []

        if months:
            time_units.append(f"{months} month{'s' if months > 1 else ''}")
        if weeks:
            time_units.append(f"{weeks} week{'s' if weeks > 1 else ''}")
        if days:
            time_units.append(f"{days} day{'s' if days > 1 else ''}")

        return ', '.join(time_units)
