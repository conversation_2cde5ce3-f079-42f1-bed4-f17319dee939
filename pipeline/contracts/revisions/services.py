from dataclasses import dataclass, field

from django.db import transaction
from django.utils.timezone import now

from budgets.api import create_budget
from budgets.configuration.api import get_budget_configuration
from contracts.budgets.revision import (
    BudgetRevisionDiff,
    apply_contract_budget_revision,
    compare_contract_budget_revisions,
)
from contracts.invoicing.models import ContractInvoicingTemplate
from contracts.models import Contract, ContractPaymentTemplate
from contracts.models.contract import ContractCancellationReason, ContractData
from contracts.payments.revision import (
    CONTRACT_PAYMENT_TEMPLATE_REVISION_DELAYED_ATTRIBUTES,
    CONTRACT_PAYMENT_TEMPLATE_REVISION_IMMEDIATE_ATTRIBUTES,
    apply_contract_payment_template_revision,
    compare_contract_payment_template_revisions,
)
from contracts.projects.models import ContractTaskTemplate
from contracts.projects.revision import (
    apply_contract_task_templates_revision,
    compare_contract_task_templates_revisions,
)
from contracts.revisions.diff import RevisionDiff, compare_instances
from contracts.revisions.validators import (
    validate_can_accept_contract_revision,
    validate_can_cancel_contract_revision,
    validate_can_create_contract_revision,
    validate_contract_revision,
)
from contracts.services.errors import ContractError

CONTRACT_REVISION_ATTRIBUTES = (
    'description',
    'end_date',
    'custom_fields_data',
)


@dataclass
class ContractRevisionDiff:
    contract: RevisionDiff = field(default_factory=RevisionDiff)
    payment_template: RevisionDiff = field(default_factory=RevisionDiff)
    task_templates: RevisionDiff = field(default_factory=RevisionDiff)
    budget: BudgetRevisionDiff = field(default_factory=BudgetRevisionDiff)

    @property
    def has_changes(self) -> bool:
        return self.contract.has_changes or self.payment_template.has_changes or self.task_templates.has_changes or self.budget.has_changes


@transaction.atomic
def create_contract_revision(contract: Contract, contract_revision_data: ContractData | None = None) -> Contract:
    validate_can_create_contract_revision(contract).raise_if_invalid()

    revision_number = _get_or_create_contract_next_revision_number(contract)
    return Contract.objects.create_revision(contract, revision_number, contract_revision_data)


@transaction.atomic
def initiate_contract_revision(contract_revision: Contract):
    from contracts.contract.events import trigger_contract_created_event
    from contracts.contract.settings import contract_data_contains_external_attributes
    from contracts.services.contract import (
        get_contract_acceptance_service,
        start_contract_acceptance,
    )

    validate_contract_revision(contract_revision).raise_if_invalid()

    # Check if the revision contains external changes that require vendor notification
    contract_revision_diff = compare_contract_revisions(contract_revision.main_contract, contract_revision)
    has_external_changes = contract_data_contains_external_attributes(contract_revision_diff.contract.changed)

    contract_revision.main_contract.begin_revision()
    transaction.on_commit(lambda: trigger_contract_created_event(contract_revision, has_external_changes=has_external_changes))
    get_contract_acceptance_service(contract_revision).initiate_acceptance(contract_revision)
    start_contract_acceptance(contract_revision)


@transaction.atomic
def cancel_contract_revision(contract_revision: Contract):
    from contracts.contract.events import (
        trigger_contract_canceled_event,
        trigger_contract_status_changed_event,
    )
    from contracts.services.contract import get_contract_acceptance_service

    validate_can_cancel_contract_revision(contract_revision).raise_if_invalid()

    contract_revision.main_contract.finish_revision()
    contract_revision.canceled()
    transaction.on_commit(lambda status=contract_revision.status: trigger_contract_status_changed_event(contract_revision, status))
    get_contract_acceptance_service(contract_revision).reset_acceptance(contract_revision)
    transaction.on_commit(
        lambda: trigger_contract_canceled_event(contract_revision, cancellation_reason=ContractCancellationReason.USER_REQUESTED)
    )


@transaction.atomic
def accept_contract_revision(contract_revision: Contract):
    from contracts.contract.events import trigger_contract_approved_event
    from contracts.services.contract import update_contract_end

    validate_can_accept_contract_revision(contract_revision).raise_if_invalid()

    contract_revision_diff = compare_contract_revisions(contract_revision.main_contract, contract_revision)
    apply_contract_revision(contract_revision, contract_revision_diff.contract)

    if contract_revision_diff.payment_template.has_changes:
        apply_contract_payment_template_revision(
            contract_revision, contract_revision_diff.payment_template, CONTRACT_PAYMENT_TEMPLATE_REVISION_IMMEDIATE_ATTRIBUTES
        )
        if contract_revision.payment_template.pay_period_calculator.is_last_pay_period_pending(
            now().date(), contract_revision.start_date, contract_revision.end_date
        ):
            apply_contract_payment_template_revision(
                contract_revision, contract_revision_diff.payment_template, CONTRACT_PAYMENT_TEMPLATE_REVISION_DELAYED_ATTRIBUTES
            )

    if contract_revision_diff.budget.has_changes:
        apply_contract_budget_revision(contract_revision)

    if contract_revision_diff.task_templates.has_changes:
        apply_contract_task_templates_revision(contract_revision, contract_revision_diff.task_templates)

    if contract_revision_diff.contract.is_changed('end_date'):
        update_contract_end(contract_revision.main_contract)

    contract_revision.ended()
    contract_revision.revision_amendment.approve()
    contract_revision.main_contract.finish_revision()
    transaction.on_commit(lambda: trigger_contract_approved_event(contract_revision))


def reject_contract_revision(contract_revision: Contract):
    from contracts.contract.events import trigger_contract_status_changed_event

    contract_revision.main_contract.finish_revision()
    contract_revision.rejected()
    transaction.on_commit(lambda status=contract_revision.status: trigger_contract_status_changed_event(contract_revision, status))


def _get_or_create_contract_next_revision_number(contract):
    if contract.revision_number is None:
        _create_contract_original_revision(contract)
        revision_number = 0
    else:
        revision_number = Contract.objects.max_revision_number(contract.id)

    return revision_number + 1


def _get_contract_next_revision_number(contract):
    revision_number = Contract.objects.max_revision_number(contract.id)
    if revision_number is None:
        return 0
    else:
        return revision_number + 1


def _create_contract_original_revision(contract: Contract):
    if contract.revision_number is not None:
        raise AttributeError

    contract_data = {
        'status': contract.status,
        'created_by': contract.created_by,
        'vendor_workflow': contract.vendor_workflow,
        'visible_to_partner': contract.visible_to_partner,
    }
    if contract.payment_template_id:
        contract_data['payment_template'] = ContractPaymentTemplate.objects.create_revision(contract.payment_template)

    if contract.invoicing_template_id:
        contract_data['invoicing_template'] = ContractInvoicingTemplate.objects.create_revision(contract.invoicing_template)

    if contract.budget_id:
        result = create_budget(get_budget_configuration(contract.budget_id))
        if result.is_failure:
            raise ContractError("Contract budget error", details={'budget': result.errors_dump()})

        contract_data['budget_id'] = result.value.id

    contract_original_revision = Contract.objects.create_revision(contract, 0, contract_data)
    contract.revision_number = contract_original_revision.revision_number
    contract.save(update_fields=['revision_number'])
    ContractTaskTemplate.objects.create_revisions(contract_original_revision, contract.list_task_templates())
    return contract_original_revision


def apply_contract_revision(contract_revision: Contract, contract_diff: RevisionDiff):
    for attr, value in contract_diff.get_changes(CONTRACT_REVISION_ATTRIBUTES).items():
        setattr(contract_revision.main_contract, attr, value)

    changed_attributes = ['revision_number']
    changed_attributes.extend(contract_diff.get_changed(CONTRACT_REVISION_ATTRIBUTES))

    contract_revision.main_contract.revision_number = contract_revision.revision_number
    contract_revision.main_contract.save(update_fields=changed_attributes)


def compare_contract_revisions(contract: Contract, contract_revision: Contract) -> ContractRevisionDiff:
    contract_diff = ContractRevisionDiff()
    contract_diff.contract = compare_instances(contract, contract_revision, CONTRACT_REVISION_ATTRIBUTES)

    if contract.payment_template_id:
        contract_diff.payment_template = compare_contract_payment_template_revisions(
            contract.payment_template, contract_revision.payment_template
        )

    if contract.has_task_templates():
        contract_diff.task_templates = compare_contract_task_templates_revisions(
            contract.list_task_templates(), contract_revision.list_task_templates()
        )

    if contract.budget_id or contract_revision.budget_id:
        contract_diff.budget = compare_contract_budget_revisions(contract.budget_id, contract_revision.budget_id)

    return contract_diff
