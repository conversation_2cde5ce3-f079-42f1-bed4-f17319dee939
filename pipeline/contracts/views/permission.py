from django.utils.functional import cached_property

from contracts.models import Contract
from shortlist.permissions import TypedPermissionMixin


class ContractPermissionMixin(TypedPermissionMixin):
    @cached_property
    def contract(self):
        return Contract.objects.get(id=self.kwargs['contract_id'])

    def get_permissions_object(self):
        return self.contract

    def check_object_permissions(self, request, obj):
        if isinstance(obj, Contract):
            super().check_object_permissions(request, obj)
        else:
            super().check_object_permissions(request, obj.contract)
