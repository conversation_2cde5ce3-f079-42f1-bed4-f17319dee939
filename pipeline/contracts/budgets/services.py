from typing import TYPE_CHECKING, Union

from rest_framework import serializers

from budgets.api import create_budget, update_budget

if TYPE_CHECKING:
    from budgets.configuration.api import BudgetConfigurationRequest
    from budgets.configuration.data import BudgetConfiguration


def create_or_update_contract_budget(
    budget_configuration: Union['BudgetConfigurationRequest', 'BudgetConfiguration'],
    budget_id: int | None = None,
) -> int:
    if budget_id:
        result = update_budget(budget_id, budget_configuration)
    else:
        result = create_budget(budget_configuration)

    if result.is_failure:
        raise serializers.ValidationError(result.errors)

    return result.value.id
