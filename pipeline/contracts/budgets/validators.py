from typing import Union

from budgets.configuration.data import ContractedBudgetType, ReimbursementAllowanceType
from budgets.models import Budget
from budgets.validators import validate_budget_configuration
from contracts.validators.base import ValidationResult


def validate_contract_budget_configuration(
    budget: Budget, configuration: Union['BudgetConfigurationRequest', 'BudgetConfiguration']
) -> ValidationResult:
    result = ValidationResult()

    r = validate_budget_configuration(budget, configuration)
    if r.is_invalid:
        result.add_error(r.to_errors_dict())

    contracted_budget_validation_result = validate_contracted_budget_configuration(budget, configuration.contracted_budget)
    if contracted_budget_validation_result.is_invalid:
        result.add_error({'contracted_budget': contracted_budget_validation_result.to_dict()})

    reimbursement_allowance_validation_result = validate_reimbursement_allowance_budget_configuration(
        budget, configuration.reimbursement_allowance
    )
    if reimbursement_allowance_validation_result.is_invalid:
        result.add_error({'reimbursement_allowance': reimbursement_allowance_validation_result.to_dict()})

    return result


def validate_contracted_budget_configuration(
    budget: Budget, configuration: Union['ContractedBudgetConfigurationRequest', 'ContractedBudgetConfiguration'] | None
) -> ValidationResult:
    result = ValidationResult()

    if budget.contracted_budget_configuration.budget_type != ContractedBudgetType.NO_BUDGET and (
        not configuration or configuration.budget_type == ContractedBudgetType.NO_BUDGET
    ):
        result.add_error({'budget_type': ["Budget type is invalid"]})

    return result


def validate_reimbursement_allowance_budget_configuration(
    budget: Budget, configuration: Union['ReimbursementAllowanceConfigurationRequest', 'ReimbursementAllowanceConfiguration'] | None
) -> ValidationResult:
    result = ValidationResult()

    if budget.reimbursement_allowance_configuration.allowance_type != ReimbursementAllowanceType.NO_ALLOWANCE and (
        not configuration or configuration.allowance_type == ReimbursementAllowanceType.NO_ALLOWANCE
    ):
        result.add_error({'allowance_type': ["Allowance type is invalid"]})

    return result
