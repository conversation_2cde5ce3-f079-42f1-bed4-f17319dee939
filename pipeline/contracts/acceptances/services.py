import typing

from django.db import transaction
from django.db.models import Q
from django.utils.timezone import now

from audit.tenant.models import ActionLog
from contracts.acceptances.models import (
    ContractAcceptance,
    ContractAcceptanceStatus,
    ContractAcceptanceType,
    ContractSigner,
    ContractSignerType,
)
from contracts.acceptances.validators import can_accept, can_reject, can_send_reminder
from contracts.models import Contract
from contracts.services.errors import ContractError
from contracts.validators.contract import validate_can_start_accepting
from events.event_types import (
    AcceptContract,
    AcceptContractReminder,
    ContractAccepted,
    ContractRejected,
    ContractSignerChanged,
)

if typing.TYPE_CHECKING:
    from users.models import User


class ContractAcceptanceError(ContractError):
    pass


class ContractAcceptingService:
    def initiate_acceptance(self, contract: Contract):
        if contract.is_revision:
            signers = contract.template.amendment_signers.all()
            if not signers:
                # some contracts may not have amendment signers as they were created before the feature was introduced
                signer = ContractSigner.objects.create(
                    contract_template=contract.template,
                    type=ContractSignerType.PARTNER,
                    acceptance_type=ContractAcceptanceType.CONTRACT_AMENDMENT,
                )
                signers = [signer]
        else:
            signers = contract.template.signers.all()

        ContractAcceptance.objects.bulk_create(
            [ContractAcceptance(contract=contract, signer=signer, order=signer.order) for signer in signers]
        )

    def start_acceptance(self, contract: Contract, *, dont_send_vendor_email=False):
        validate_can_start_accepting(contract).raise_if_invalid()

        from contracts.services.contract import make_contract_available_to_partner

        if not ContractAcceptance.objects.for_contract(contract).exists():
            raise ContractAcceptanceError("Contract acceptances do not exist")

        if ContractAcceptance.objects.first_acceptance_is_vendor(contract):
            make_contract_available_to_partner(contract)

        transaction.on_commit(lambda: trigger_accept_contract_event(contract, dont_send_vendor_email=dont_send_vendor_email))

        contract.accepting()

    def restart_acceptance(self, contract: Contract):
        self.reset_acceptance(contract)
        self.initiate_acceptance(contract)

    def reset_acceptance(self, contract: Contract):
        ContractAcceptance.objects.for_contract(contract).delete()

    def continue_acceptance(self, contract: Contract):
        contract.accepting()

    def is_contract_accepted(self, contract: Contract):
        return not ContractAcceptance.objects.for_contract(contract.id).not_accepted().exists()

    def get_acceptance_users(self, contract: Contract):
        return {
            acceptance.signer.user
            for acceptance in ContractAcceptance.objects.buyer_acceptances(contract).select_related("signer", "signer__user")
        }


@transaction.atomic
def accept(acceptance: ContractAcceptance, user: 'User'):
    from contracts.services.contract import (
        approve_contract,
        continue_acceptance,
        make_contract_available_to_partner,
    )

    if not can_accept(acceptance, user):
        raise ContractAcceptanceError("User can't accept the contract")

    if ContractAcceptance.objects.awaiting_prior_acceptances(acceptance):
        raise ContractAcceptanceError("Contract is awaiting prior acceptance")

    acceptance.accept(user)
    if acceptance.contract.is_rejected:
        continue_acceptance(acceptance.contract)

    if ContractAcceptance.objects.next_acceptance_is_vendor(acceptance):
        make_contract_available_to_partner(acceptance.contract)

    transaction.on_commit(lambda: trigger_contract_accepted_event(acceptance))

    if ContractAcceptingService().is_contract_accepted(acceptance.contract):
        approve_contract(contract=acceptance.contract)
    else:
        # inform next signer
        transaction.on_commit(lambda: trigger_accept_contract_event(acceptance.contract))


@transaction.atomic
def reject(acceptance: ContractAcceptance, user: 'User', rejection_reason: str | None = None):
    from contracts.services.contract import reject_contract

    if not can_reject(acceptance, user):
        raise ContractAcceptanceError("User can't reject the contract")

    if ContractAcceptance.objects.awaiting_prior_acceptances(acceptance):
        raise ContractAcceptanceError("Contract is awaiting prior acceptance")

    acceptance.reject(user, rejection_reason)

    reject_contract(acceptance.contract)
    transaction.on_commit(lambda: trigger_contract_rejected_event(acceptance))


@transaction.atomic
def reassign_acceptance_signers(user: 'User', assignee: 'User'):
    signers = ContractSigner.objects.for_user(user.id)
    for signer in signers:
        signer.assign(assignee)
        contracts = Contract.objects.distinct().filter(
            Q(acceptances__status=ContractAcceptanceStatus.AWAITING) & Q(acceptances__signer=signer)
        )
        for contract in contracts:
            transaction.on_commit(lambda c=contract, s=signer: trigger_contract_signer_changed_event(c, s, user))


def trigger_accept_contract_event(contract: Contract, *, dont_send_vendor_email=False):
    contract_acceptance = ContractAcceptance.objects.next_acceptance(contract)
    if contract_acceptance:
        dont_send = ["AcceptContractEmail"] if dont_send_vendor_email else []
        AcceptContract(contract=contract, accepting_user=contract_acceptance.user, dont_send=dont_send)


def trigger_contract_accepted_event(contract_acceptance: ContractAcceptance):
    ContractAccepted(contract=contract_acceptance.contract, accepting_user=contract_acceptance.user)


def trigger_contract_rejected_event(contract_acceptance: ContractAcceptance):
    from contracts.services.contract import get_contract_recipients

    contract = contract_acceptance.contract
    data = {
        "recipients": get_contract_recipients(contract=contract, with_partner=False),
        "rejection_reason": contract_acceptance.rejection_reason,
    }
    ContractRejected(contract=contract, data=data)


def trigger_accept_contract_reminder_event(acceptance: ContractAcceptance):
    if can_send_reminder(acceptance):
        AcceptContractReminder(contract=acceptance.contract, accepting_user=acceptance.user)
        acceptance.last_reminder_sent = now()
        acceptance.save(update_fields=["last_reminder_sent"])


def trigger_contract_signer_changed_event(contract: Contract, signer: ContractSigner, user):
    ContractSignerChanged(contract=contract, current_contract_signer=signer.user, previous_contract_signer=user)


@AcceptContractReminder.watch
def log_accept_contract_reminder_event(event):
    ActionLog.objects.create_log_for_event(event, content_object=event['contract'])


@ContractAccepted.watch
def log_contract_accepted_event(event):
    ActionLog.objects.create_log_for_event(event, content_object=event['contract'])


@ContractRejected.watch
def log_contract_rejected_event(event):
    ActionLog.objects.create_log_for_event(event, content_object=event['contract'])


@ContractSignerChanged.watch
def log_contract_signer_changed(event):
    ActionLog.objects.create_log_for_event(event, content_object=event['contract'])
