import functools
from unittest import mock
from unittest.mock import MagicMock

from freezegun import freeze_time

from django.test import override_settings
from django.utils.timezone import now

from contracts.agreements.application import create_contract_agreement
from contracts.agreements.events import trigger_sign_contract_reminder_event
from contracts.agreements.models import ContractAgreementSignerStatus
from contracts.agreements.services import (
    ContractAgreementError,
    reject_agreement,
)
from contracts.models import ContractAgreementSigner
from contracts.models.contract import Contract, ContractStatus
from contracts.models.template import CONTRACT_DETAILS_TEMPLATE_NAME_PREFIX
from contracts.services.contract import initiate_contract, restart_contract
from contracts.tests.factories import ContractFactory
from contracts.tests.utils import ActionLogTestCaseMixin
from deals.models import SignRequest, Template
from deals.tests.factories import AgreementTemplateFactory, SignRequestFactory
from events.event_types import (
    AcceptContractReminder,
    ContractAccepted,
    ContractRejected,
    ContractStatusChanged,
)
from notifications.tests.test_email_notifications import mock_send_mail
from preferences.tests.factories import (
    CustomFieldsTemplateFactory,
    get_custom_fields_data,
)
from shortlist.custom_fields import TYPE_USER
from shortlist.tests.helpers import TenantTestCase
from users.tests.factories import UserFactory
from vendors.tests.factories import VendorFactory
from deals.services.locking import acquire_or_refresh_lock, release_lock


def hellosign_mock(template_config):
    def decorator(fn):
        @functools.wraps(fn)
        def wrapper(*args, **kwargs):
            with override_settings(HELLOSIGN_API_KEY="foo_api_key", HELLOSIGN_CLIENT_ID="client_id", HELLOSIGN_TEST_MODE=True):
                # with mock.patch('deals.models.Template.configuration', {'roles': [{'name': 'Partner'}, {'name': 'Buyer1'}, {'name': 'Buyer2'}]}):
                with mock.patch("integrations.services.hellosign.hellosign.get_hellosign_template_config") as get_hellosign_template_config:
                    with mock.patch(
                        "integrations.services.hellosign.hellosign.HellosignClient.get_signature_request"
                    ) as get_signature_request:
                        with mock.patch(
                            "integrations.services.hellosign.hellosign.HellosignClient.get_signature_request_file"
                        ) as get_signature_request_file:
                            get_hellosign_template_config.return_value = template_config
                            get_signature_request.return_value = mock.Mock(is_completed=True)
                            get_signature_request_file.return_value = b'foo'
                            return fn(*args, **kwargs)

        return wrapper

    return decorator


def hellosign_sign_current_signer(sign_request: SignRequest):
    sign_request.signed_callback()
    sign_request.signed_hellosign(all_signed=False, external_id=None)


def hellosign_sign_by_all_signers(sign_request: SignRequest):
    for _ in range(sign_request.signers.count()):
        hellosign_sign_current_signer(sign_request)


def hellosign_document_signed_and_ready_for_download(sign_request: SignRequest):
    sign_request.signed_hellosign(all_signed=True, external_id=None)


hellosign_config = hellosign_mock(template_config={'roles': [{'name': 'Partner'}, {'name': 'Buyer1'}, {'name': 'Buyer2'}]})


class ContractAgreementTestBase(TenantTestCase):
    def assertContractAgreement(self, contract, buyers):
        self.assertTrue(contract.is_signing)
        self.assertTrue(contract.visible_to_partner)
        self.assertIsNotNone(contract.agreement)
        self.assertIsNotNone(contract.agreement.sign_request)

        signers = list(contract.agreement.signers.all())
        self.assertEqual(len(signers), 3)

        self.assertIsNotNone(signers[0].sign_request_signer)
        self.assertEqual(signers[0].user, contract.partner.first_contact)
        self.assertEqual(signers[0].status, ContractAgreementSignerStatus.AWAITING)

        self.assertIsNotNone(signers[1].sign_request_signer)
        self.assertEqual(signers[1].user, buyers[0])
        self.assertEqual(signers[1].status, ContractAgreementSignerStatus.AWAITING)

        self.assertIsNotNone(signers[2].sign_request_signer)
        self.assertEqual(signers[2].user, buyers[1])
        self.assertEqual(signers[2].status, ContractAgreementSignerStatus.AWAITING)

    def assertSignRequestSigner(self, signer: ContractAgreementSigner, user, status):
        self.assertIsNotNone(signer.sign_request_signer)
        self.assertEqual(signer.user, user)
        self.assertEqual(signer.status, status)


class ContractAgreementTestCase(ActionLogTestCaseMixin, ContractAgreementTestBase):
    def get_contract(self) -> Contract:
        return Contract.objects.get(id=self.contract.id)

    def get_partner_signer(self, contract) -> ContractAgreementSigner:
        return list(contract.agreement.signers.all())[0]

    def get_buyer_1_signer(self, contract) -> ContractAgreementSigner:
        return list(contract.agreement.signers.all())[1]

    def get_buyer_2_signer(self, contract) -> ContractAgreementSigner:
        return list(contract.agreement.signers.all())[2]

    @hellosign_config
    def setUp(self):
        super().setUp()

        self.partner = VendorFactory()
        self.buyers = [UserFactory(), UserFactory()]
        self.signers = [
            dict(role="Buyer1", email=self.buyers[0].email),
            dict(role="Buyer2", email=self.buyers[1].email),
        ]
        self.contract = ContractFactory(
            partner=self.partner,
            initiated=True,
            template__with_agreement=True,
            template__agreement_template__signers=self.signers,
        )
        self.contract.refresh_from_db()

    def test_contract_status_signing(self):
        self.assertTrue(self.contract.is_signing)
        self.assertTrue(self.contract.visible_to_partner)
        self.assertIsNotNone(self.contract.agreement)
        self.assertIsNotNone(self.contract.agreement.sign_request)
        signers = list(self.contract.agreement.signers.all())
        self.assertEqual(len(signers), 3)
        self.assertSignRequestSigner(signers[0], self.contract.partner.first_contact, ContractAgreementSignerStatus.AWAITING)
        self.assertSignRequestSigner(signers[1], self.buyers[0], ContractAgreementSignerStatus.AWAITING)
        self.assertSignRequestSigner(signers[2], self.buyers[1], ContractAgreementSignerStatus.AWAITING)

    def test_partner_accepts(self):
        completed_at = now()
        with freeze_time(completed_at):
            hellosign_sign_current_signer(self.contract.agreement.sign_request)

        signers = list(self.contract.agreement.signers.all())
        self.assertEqual(self.get_contract().status, ContractStatus.SIGNING)
        self.assertEqual(signers[0].status, ContractAgreementSignerStatus.ACCEPTED)
        self.assertEqual(signers[0].completed_at, completed_at)
        self.assertEqual(signers[1].status, ContractAgreementSignerStatus.AWAITING)
        self.assertEqual(signers[2].status, ContractAgreementSignerStatus.AWAITING)

    def test_partner_rejects(self):
        completed_at = now()
        rejection_reason = "lorem ipsum"
        with freeze_time(completed_at):
            reject_agreement(self.get_partner_signer(self.contract), self.contract.partner.first_contact, rejection_reason)

        signer = self.get_partner_signer(self.contract)
        self.assertEqual(signer.status, ContractAgreementSignerStatus.REJECTED)
        self.assertEqual(signer.completed_at, completed_at)
        self.assertEqual(signer.rejection_reason, rejection_reason)
        self.assertEqual(self.get_contract().status, ContractStatus.REJECTED)

    def test_partner_rejects_then_accepts(self):
        reject_agreement(self.get_partner_signer(self.contract), self.contract.partner.first_contact, "lorem ipsum")
        self.assertEqual(self.get_contract().status, ContractStatus.REJECTED)

        completed_at = now()
        with freeze_time(completed_at):
            hellosign_sign_current_signer(self.contract.agreement.sign_request)

        signer = self.get_partner_signer(self.contract)
        self.assertEqual(self.get_contract().status, ContractStatus.SIGNING)
        self.assertEqual(signer.status, ContractAgreementSignerStatus.ACCEPTED)
        self.assertEqual(signer.completed_at, completed_at)

    def test_first_buyer_accepts(self):
        hellosign_sign_current_signer(self.contract.agreement.sign_request)

        completed_at = now()
        with freeze_time(completed_at):
            hellosign_sign_current_signer(self.contract.agreement.sign_request)

        signers = list(self.contract.agreement.signers.all())
        self.assertEqual(self.contract.status, ContractStatus.SIGNING)
        self.assertEqual(signers[0].status, ContractAgreementSignerStatus.ACCEPTED)
        self.assertEqual(signers[1].status, ContractAgreementSignerStatus.ACCEPTED)
        self.assertEqual(signers[1].completed_at, completed_at)
        self.assertEqual(signers[2].status, ContractAgreementSignerStatus.AWAITING)

    def test_second_buyer_accepts(self):
        hellosign_sign_current_signer(self.contract.agreement.sign_request)
        hellosign_sign_current_signer(self.contract.agreement.sign_request)

        completed_at = now()
        with freeze_time(completed_at):
            hellosign_sign_current_signer(self.contract.agreement.sign_request)

        signers = list(self.contract.agreement.signers.all())
        self.assertEqual(self.get_contract().status, ContractStatus.LIVE)
        self.assertEqual(signers[0].status, ContractAgreementSignerStatus.ACCEPTED)
        self.assertEqual(signers[1].status, ContractAgreementSignerStatus.ACCEPTED)
        self.assertEqual(signers[2].status, ContractAgreementSignerStatus.ACCEPTED)
        self.assertEqual(signers[2].completed_at, completed_at)

    @mock.patch("contracts.services.contract.approve_contract")
    def test_contract_accepted_after_all_signers_signed(self, approve_contract: MagicMock):
        hellosign_sign_by_all_signers(self.contract.agreement.sign_request)

        self.assertTrue(all(signer.status == ContractAgreementSignerStatus.ACCEPTED for signer in self.contract.agreement.signers.all()))
        approve_contract.assert_called_once_with(contract=self.contract)

    @hellosign_config
    def test_all_signers_accepted_and_document_ready_for_download(self):
        hellosign_sign_by_all_signers(self.contract.agreement.sign_request)
        hellosign_document_signed_and_ready_for_download(self.contract.agreement.sign_request)

        signers = list(self.contract.agreement.signers.all())
        self.assertEqual(self.get_contract().status, ContractStatus.LIVE)
        self.assertEqual(signers[0].status, ContractAgreementSignerStatus.ACCEPTED)
        self.assertEqual(signers[1].status, ContractAgreementSignerStatus.ACCEPTED)
        self.assertEqual(signers[2].status, ContractAgreementSignerStatus.ACCEPTED)
        self.assertEqual(self.get_contract().agreement.sign_request.status, SignRequest.STATUS_DONE)

    def test_partner_cant_reject_after_accepting(self):
        hellosign_sign_current_signer(self.contract.agreement.sign_request)
        self.assertEqual(self.get_partner_signer(self.contract).status, ContractAgreementSignerStatus.ACCEPTED)

        with self.assertRaises(ContractAgreementError) as e:
            reject_agreement(self.get_partner_signer(self.contract), self.contract.partner.first_contact, "lorem ipsum")

        self.assertEqual(str(e.exception), "User can't reject the contract")

    def test_buyer_cant_reject(self):
        with self.assertRaises(ContractAgreementError) as e:
            reject_agreement(self.get_buyer_1_signer(self.contract), self.buyers[0], "lorem ipsum")

        self.assertEqual(str(e.exception), "User can't reject the contract")

    def test_buyer_signature_reminder_notification(self):
        hellosign_sign_current_signer(self.contract.agreement.sign_request)
        signer = self.get_buyer_1_signer(self.contract)

        with self.watch_event(AcceptContractReminder) as events, mock_send_mail() as send_mail_mock:
            trigger_sign_contract_reminder_event(signer)

        emails = send_mail_mock.emails_for_event(AcceptContractReminder)
        self.assertEqual(len(events), 1)
        self.assertEqual(events[0]['accepting_user'], signer.user)
        self.assertEqual(len(emails), 1)
        self.assertEqual(emails[0], [signer.user.email])

        # try to send notification second time
        with self.watch_event(AcceptContractReminder) as events, mock_send_mail() as send_mail_mock:
            trigger_sign_contract_reminder_event(signer)

        emails = send_mail_mock.emails_for_event(AcceptContractReminder)
        self.assertEqual(len(events), 0)
        self.assertEqual(len(emails), 0)

    def test_partner_acceptance_reminder_notification(self):
        signer = self.get_partner_signer(self.contract)

        with self.watch_event(AcceptContractReminder) as events, mock_send_mail() as send_mail_mock:
            trigger_sign_contract_reminder_event(signer)

        emails = send_mail_mock.emails_for_event(AcceptContractReminder)
        self.assertEqual(len(events), 1)
        self.assertEqual(events[0]['accepting_user'], signer.user)
        self.assertEqual(len(emails), 1)
        self.assertEqual(emails[0], [signer.user.email])

        self.assertActionLog(self.contract, 'accept_contract_reminder', f'{self.user.full_name} sent a reminder to the Vendor')

        # try to send notification second time
        with self.watch_event(AcceptContractReminder) as events, mock_send_mail() as send_mail_mock:
            trigger_sign_contract_reminder_event(signer)

        emails = send_mail_mock.emails_for_event(AcceptContractReminder)
        self.assertEqual(len(events), 0)
        self.assertEqual(len(emails), 0)

    @hellosign_config
    def test_contract_signing_event(self):
        contract = ContractFactory(template__with_agreement=True, template__agreement_template__signers=self.signers)
        self.run_commit_hooks()

        with self.watch_event(ContractStatusChanged) as events:
            initiate_contract(contract)
            self.run_commit_hooks()

        self.assertStatusChangedEvent(contract, events, ContractStatus.SIGNING)
        self.assertActionLog(contract, 'contract_status_changed', "Contract was set to: signing")

    def test_partner_accepted_contract_event(self):
        with self.as_vendor(self.partner), self.watch_event(ContractAccepted) as events:
            hellosign_sign_current_signer(self.contract.agreement.sign_request)
            self.run_commit_hooks()

        self.assertEqual(len(events), 1)
        self.assertEqual(events[0]['accepting_user'], self.partner.first_contact)

        self.assertActionLog(self.contract, 'contract_accepted', f"{self.partner.first_contact.full_name} accepted Contract")

    def test_buyer_accepted_contract_event(self):
        hellosign_sign_current_signer(self.contract.agreement.sign_request)
        self.run_commit_hooks()

        with self.as_user(self.buyers[0]), self.watch_event(ContractAccepted) as events:
            hellosign_sign_current_signer(self.contract.agreement.sign_request)
            self.run_commit_hooks()

        self.assertEqual(len(events), 1)
        self.assertEqual(events[0]['accepting_user'], self.buyers[0])

        self.assertActionLog(self.contract, 'contract_accepted', f"{self.buyers[0].full_name} accepted Contract")

    def test_partner_rejected_contract_event(self):
        with (
            self.as_vendor(self.partner),
            self.watch_event(ContractRejected) as rejected_events,
            self.watch_event(ContractStatusChanged) as status_events,
        ):
            reject_agreement(self.get_partner_signer(self.contract), self.partner.first_contact, "lorem ipsum")
            self.run_commit_hooks()

        self.assertEqual(len(rejected_events), 1)
        self.assertEqual(rejected_events[0]["contract"], self.contract)

        self.assertActionLog(
            self.contract,
            'contract_rejected',
            f"{self.partner.first_contact.full_name} rejected Contract with a reason: lorem ipsum",
        )

        self.assertStatusChangedEvent(self.contract, status_events, ContractStatus.REJECTED)
        self.assertActionLog(self.contract, 'contract_status_changed', "Contract was set to: rejected")

    @hellosign_config
    def test_update_contract_restarts_signing(self):
        hellosign_sign_current_signer(self.contract.agreement.sign_request)
        hellosign_sign_current_signer(self.contract.agreement.sign_request)
        self.assertTrue(self.get_contract().is_signing)

        restart_contract(self.get_contract())

        self.assertContractAgreement(self.contract, self.buyers)

    @hellosign_config
    def test_sign_contract_with_external_partner(self):
        contract = ContractFactory(
            with_external_partner=True,
            template__workflow=None,
            template__with_agreement=True,
            template__agreement_template__signers=self.signers,
        )
        self.assertIsNone(contract.partner)
        self.assertIsNotNone(contract.external_partner)

        initiate_contract(contract)

        contract = Contract.objects.get(id=contract.id)
        self.assertIsNotNone(contract.partner.invited_at)
        self.assertIsNotNone(contract.partner.invited_by)
        self.assertEqual(contract.partner.invited_by, contract.created_by)

    @hellosign_config
    def test_create_contract_agreement_with_agreement_template(self):
        contract = ContractFactory(partner=self.partner, template__with_agreement_choose_before_signing=True, initiated=True)
        agreement_template = AgreementTemplateFactory(signers=self.signers, type=Template.TYPE_CONTRACT)
        self.assertTrue(contract.is_signing)
        self.assertFalse(contract.visible_to_partner)
        self.assertIsNone(contract.agreement)

        contract_agreement = create_contract_agreement(contract, dict(agreement_template=agreement_template.id))

        contract = Contract.objects.get(id=contract.id)
        self.assertTrue(contract.is_signing)
        self.assertTrue(contract.visible_to_partner)
        self.assertEqual(contract.agreement, contract_agreement)
        self.assertEqual(contract.agreement.agreement_template, agreement_template)

        signers = list(contract.agreement.signers.all())
        self.assertEqual(len(signers), 3)
        self.assertSignRequestSigner(signers[0], contract.partner.first_contact, ContractAgreementSignerStatus.AWAITING)
        self.assertSignRequestSigner(signers[1], self.buyers[0], ContractAgreementSignerStatus.AWAITING)
        self.assertSignRequestSigner(signers[2], self.buyers[1], ContractAgreementSignerStatus.AWAITING)

    @hellosign_config
    def test_create_contract_agreement_with_agreement_for_signing(self):
        contract = ContractFactory(partner=self.partner, template__with_agreement_choose_before_signing=True, initiated=True)
        sign_request = SignRequestFactory(for_signing=True, vendor=self.partner)
        self.assertTrue(contract.is_signing)
        self.assertFalse(contract.visible_to_partner)
        self.assertIsNone(contract.agreement)

        contract_agreement = create_contract_agreement(contract, dict(sign_request=sign_request.id))

        contract = Contract.objects.get(id=contract.id)
        self.assertTrue(contract.is_signing)
        self.assertTrue(contract.visible_to_partner)
        self.assertEqual(contract.agreement, contract_agreement)
        self.assertIsNone(contract.agreement.agreement_template)

        signers = list(contract.agreement.signers.all())
        self.assertEqual(len(signers), 1)
        self.assertSignRequestSigner(signers[0], contract.partner.first_contact, ContractAgreementSignerStatus.AWAITING)

    @hellosign_config
    def test_create_contract_agreement_with_signed_agreement(self):
        contract = ContractFactory(
            partner=self.partner, start_date=now().date(), template__with_agreement_choose_before_signing=True, initiated=True
        )
        sign_request = SignRequestFactory(signed=True, vendor=self.partner)
        self.assertTrue(contract.is_signing)
        self.assertFalse(contract.visible_to_partner)
        self.assertIsNone(contract.agreement)

        contract_agreement = create_contract_agreement(contract, dict(signed_document=sign_request.signed_document.id))

        contract = Contract.objects.get(id=contract.id)
        self.assertTrue(contract.is_live)
        self.assertTrue(contract.visible_to_partner)
        self.assertEqual(contract.agreement, contract_agreement)
        self.assertIsNone(contract.agreement.agreement_template)

        self.assertEqual(len(contract.agreement.signers.all()), 0)

    def test_partner_accepts_contract_agreement_with_agreement_for_signing(self):
        contract = ContractFactory(
            partner=self.partner, start_date=now().date(), template__with_agreement_choose_before_signing=True, initiated=True
        )
        sign_request = SignRequestFactory(for_signing=True, vendor=self.partner)
        create_contract_agreement(contract, dict(sign_request=sign_request.id))
        contract = Contract.objects.get(id=contract.id)

        hellosign_sign_current_signer(contract.agreement.sign_request)

        contract = Contract.objects.get(id=contract.id)
        self.assertTrue(contract.is_live)
        signers = list(contract.agreement.signers.all())
        self.assertSignRequestSigner(signers[0], contract.partner.first_contact, ContractAgreementSignerStatus.ACCEPTED)

    def test_partner_cant_reject_when_sign_request_locked(self):
        other_user = UserFactory()
        acquire_or_refresh_lock(self.contract.agreement.sign_request.id, other_user)

        with self.assertRaises(ContractAgreementError) as e:
            reject_agreement(self.get_partner_signer(self.contract), self.contract.partner.first_contact, "lorem ipsum")

        self.assertEqual(str(e.exception), "User can't reject the contract")

        # Clean up
        release_lock(self.contract.agreement.sign_request.id, other_user)


class ContractAgreementDynamicSignerTestCase(ActionLogTestCaseMixin, ContractAgreementTestBase):
    @hellosign_config
    def setUp(self):
        super().setUp()

        self.partner = VendorFactory()
        self.buyers = [UserFactory(), UserFactory()]
        contract_global_custom_fields_template = CustomFieldsTemplateFactory(
            model="Contract",
            post={"template_fields": [{"type": TYPE_USER}, {"type": TYPE_USER}]},
        )
        contract_global_custom_fields = list(contract_global_custom_fields_template.template_fields.all())
        self.contract = ContractFactory(
            partner=self.partner,
            initiated=True,
            template__with_contract_details_template=True,
            template__contract_details_template=CustomFieldsTemplateFactory(
                name_prefix=CONTRACT_DETAILS_TEMPLATE_NAME_PREFIX,
                post={
                    "template_fields": [
                        *[{'id': gcf.id} for gcf in contract_global_custom_fields],
                    ]
                },
            ),
            template__with_agreement=True,
            template__agreement_template__signers=[
                dict(role="Buyer1", map_to=f"template_field.{contract_global_custom_fields[0].id}"),
                dict(role="Buyer2", map_to=f"template_field.{contract_global_custom_fields[1].id}"),
            ],
            custom_fields_data=get_custom_fields_data(
                contract_global_custom_fields_template,
                {
                    contract_global_custom_fields[0].id: [self.buyers[0].id],
                    contract_global_custom_fields[1].id: [self.buyers[1].id],
                },
            ),
        )
        self.contract.refresh_from_db()

    def test_contract_status_signing(self):
        self.assertTrue(self.contract.is_signing)
        self.assertTrue(self.contract.visible_to_partner)
        self.assertIsNotNone(self.contract.agreement)
        self.assertIsNotNone(self.contract.agreement.sign_request)
        signers = list(self.contract.agreement.signers.all())
        self.assertEqual(len(signers), 3)
        self.assertSignRequestSigner(signers[0], self.contract.partner.first_contact, ContractAgreementSignerStatus.AWAITING)
        self.assertSignRequestSigner(signers[1], self.buyers[0], ContractAgreementSignerStatus.AWAITING)
        self.assertSignRequestSigner(signers[2], self.buyers[1], ContractAgreementSignerStatus.AWAITING)

    def test_partner_accepts(self):
        hellosign_sign_current_signer(self.contract.agreement.sign_request)

        contract = Contract.objects.get(id=self.contract.id)
        signers = list(contract.agreement.signers.all())
        self.assertEqual(contract.status, ContractStatus.SIGNING)
        self.assertEqual(signers[0].status, ContractAgreementSignerStatus.ACCEPTED)
        self.assertEqual(signers[1].status, ContractAgreementSignerStatus.AWAITING)
        self.assertEqual(signers[2].status, ContractAgreementSignerStatus.AWAITING)

    def test_first_buyer_accepts(self):
        hellosign_sign_current_signer(self.contract.agreement.sign_request)

        hellosign_sign_current_signer(self.contract.agreement.sign_request)

        contract = Contract.objects.get(id=self.contract.id)
        signers = list(contract.agreement.signers.all())
        self.assertEqual(contract.status, ContractStatus.SIGNING)
        self.assertEqual(signers[0].status, ContractAgreementSignerStatus.ACCEPTED)
        self.assertEqual(signers[1].status, ContractAgreementSignerStatus.ACCEPTED)
        self.assertEqual(signers[2].status, ContractAgreementSignerStatus.AWAITING)

    def test_second_buyer_accepts(self):
        hellosign_sign_current_signer(self.contract.agreement.sign_request)
        hellosign_sign_current_signer(self.contract.agreement.sign_request)

        hellosign_sign_current_signer(self.contract.agreement.sign_request)

        contract = Contract.objects.get(id=self.contract.id)
        signers = list(contract.agreement.signers.all())
        self.assertEqual(contract.status, ContractStatus.LIVE)
        self.assertEqual(signers[0].status, ContractAgreementSignerStatus.ACCEPTED)
        self.assertEqual(signers[1].status, ContractAgreementSignerStatus.ACCEPTED)
        self.assertEqual(signers[2].status, ContractAgreementSignerStatus.ACCEPTED)
