from django.utils.timezone import now
from freezegun import freeze_time
from rest_framework import status

from clients import features
from contracts.acceptances.models import (
    ContractAcceptanceStatus,
    ContractAcceptanceType,
    ContractSignerType,
)
from contracts.tests.test_contract_acceptance import ContractAcceptanceTestBase
from events.event_types import AcceptContractReminder
from shortlist.tests.helpers import APITestCaseMixin, features_enabled


class ContractAcceptanceAPITestCase(ContractAcceptanceTestBase, APITestCaseMixin):
    with_admin_user = True

    contract_api = "/api/contracts/{}/"
    contract_acceptance_accept_api = "/api/contracts/{}/acceptances/{}/accept/"
    contract_acceptance_reject_api = "/api/contracts/{}/acceptances/{}/reject/"
    contract_acceptance_remind_api = "/api/contracts/{}/acceptances/{}/remind/"

    contract_acceptance_accept_vendor_api = "/api/v/contracts/{}/acceptances/{}/accept/"
    contract_acceptance_reject_vendor_api = "/api/v/contracts/{}/acceptances/{}/reject/"

    @features_enabled([features.CONTRACTS])
    def test_buyer_acceptance(self):
        acceptance = self.get_buyer_acceptance(self.contract, self.user)

        self.api_post(self.contract_acceptance_accept_api.format(self.contract.id, acceptance.id), status=status.HTTP_200_OK)

        acceptance = self.get_buyer_acceptance(self.contract, self.user)
        self.assertEqual(acceptance.status, ContractAcceptanceStatus.ACCEPTED)

    @features_enabled([features.CONTRACTS])
    def test_buyer_not_signer_acceptance(self):
        acceptance = self.get_buyer_acceptance(self.contract, self.user)

        with self.as_user(self._get_user()), self.roles_enabled('buyer', ['buyer.contracts']):
            self.api_post(self.contract_acceptance_accept_api.format(self.contract.id, acceptance.id), status=status.HTTP_403_FORBIDDEN)

    @features_enabled([features.CONTRACTS])
    def test_partner_acceptance(self):
        self.accept_contract_by_buyer(self.contract, self.user)
        acceptance = self.get_partner_acceptance(self.contract)

        with self.as_vendor():
            self.api_post(self.contract_acceptance_accept_vendor_api.format(self.contract.id, acceptance.id), status=status.HTTP_200_OK)

        acceptance = self.get_partner_acceptance(self.contract)
        self.assertEqual(acceptance.status, ContractAcceptanceStatus.ACCEPTED)

    @features_enabled([features.CONTRACTS])
    def test_partner_rejection(self):
        self.accept_contract_by_buyer(self.contract, self.user)
        acceptance = self.get_partner_acceptance(self.contract)

        with self.as_vendor():
            self.api_post(self.contract_acceptance_reject_vendor_api.format(self.contract.id, acceptance.id), status=status.HTTP_200_OK)

        acceptance = self.get_partner_acceptance(self.contract)
        self.assertEqual(acceptance.status, ContractAcceptanceStatus.REJECTED)

    @features_enabled([features.CONTRACTS])
    def test_partner_not_signer_acceptance(self):
        self.accept_contract_by_buyer(self.contract, self.user)
        acceptance = self.get_partner_acceptance(self.contract)

        with self.as_vendor(self._new_vendor(able_to_login=True)):
            self.api_post(
                self.contract_acceptance_accept_vendor_api.format(self.contract.id, acceptance.id), status=status.HTTP_403_FORBIDDEN
            )

    @features_enabled([features.CONTRACTS])
    def test_retrieve_acceptances(self):
        completed_at = now()
        with freeze_time(completed_at):
            self.accept_contract_by_buyer(self.contract, self.user)

        response_data = self.api_get(self.contract_api.format(self.contract.id))

        self.assertEqual(
            response_data['acceptances'],
            [
                {
                    "id": self.get_buyer_acceptance(self.contract, self.user).id,
                    "signer": {
                        "id": self.get_buyer_acceptance(self.contract, self.user).signer.id,
                        "type": ContractSignerType.BUYER,
                        "user": self.user.id,
                        "acceptance_type": ContractAcceptanceType.CONTRACT,
                    },
                    "user": {"id": self.user.id, "first_name": self.user.first_name, "last_name": self.user.last_name},
                    "status": ContractAcceptanceStatus.ACCEPTED,
                    "completed_at": completed_at.isoformat()[:-6] + 'Z',
                    "rejection_reason": None,
                },
                {
                    "id": self.get_partner_acceptance(self.contract).id,
                    "signer": {
                        "id": self.get_partner_acceptance(self.contract).signer.id,
                        "type": ContractSignerType.PARTNER,
                        "user": None,
                        "acceptance_type": ContractAcceptanceType.CONTRACT,
                    },
                    "user": {
                        "id": self.vendor.first_contact.id,
                        "first_name": self.vendor.first_contact.first_name,
                        "last_name": self.vendor.first_contact.last_name,
                    },
                    "status": ContractAcceptanceStatus.AWAITING,
                    "completed_at": None,
                    "rejection_reason": None,
                },
            ],
        )

    @features_enabled([features.CONTRACTS])
    def test_buyer_acceptance_reminder(self):
        acceptance = self.get_buyer_acceptance(self.contract, self.user)

        with self.watch_event(AcceptContractReminder) as events:
            self.api_post(self.contract_acceptance_remind_api.format(self.contract.id, acceptance.id), status=status.HTTP_200_OK)

        self.assertEqual(len(events), 1)
        self.assertEqual(events[0]['accepting_user'], acceptance.user)

    @features_enabled([features.CONTRACTS])
    def test_partner_acceptance_reminder(self):
        self.accept_contract_by_buyer(self.contract, self.user)
        acceptance = self.get_partner_acceptance(self.contract)

        with self.watch_event(AcceptContractReminder) as events:
            self.api_post(self.contract_acceptance_remind_api.format(self.contract.id, acceptance.id), status=status.HTTP_200_OK)

        self.assertEqual(len(events), 1)
        self.assertEqual(events[0]['accepting_user'], acceptance.user)
