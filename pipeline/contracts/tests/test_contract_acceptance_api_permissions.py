from parameterized import parameterized
from rest_framework import status

from clients import features
from contracts.tests.test_contract_acceptance import ContractAcceptanceTestBase
from shortlist.tests.helpers import APITestCaseMixin, features_enabled


class ContractAcceptanceAPIPermissionsTestCase(ContractAcceptanceTestBase, APITestCaseMixin):
    with_login_user = True

    contract_acceptance_accept_api = "/api/contracts/{}/acceptances/{}/accept/"
    contract_acceptance_reject_api = "/api/contracts/{}/acceptances/{}/reject/"

    @parameterized.expand([('buyer.contracts',), ('buyer.contracts.participant',)])
    @features_enabled([features.CONTRACTS])
    def test_buyer_acceptance_allowed(self, role):
        acceptance = self.get_buyer_acceptance(self.contract, self.user)

        with self.roles_enabled('buyer', [role]):
            self.api_post(self.contract_acceptance_accept_api.format(self.contract.id, acceptance.id), status=status.HTTP_200_OK)

    @features_enabled([features.CONTRACTS])
    def test_buyer_acceptance_no_permission_forbidden(self):
        acceptance = self.get_buyer_acceptance(self.contract, self.user)

        self.api_post(self.contract_acceptance_accept_api.format(self.contract.id, acceptance.id), status=status.HTTP_403_FORBIDDEN)
