from copy import deepcopy
from datetime import UTC, date, datetime
from decimal import Decimal
from unittest import mock
from unittest.mock import MagicMock, call

from dateutil.relativedelta import relativedelta
from freezegun import freeze_time

from django.core.exceptions import ValidationError

from budgets.configuration.api import get_budget_configuration
from budgets.tests.factories import (
    BudgetFactory,
    ContractedBudgetConfigurationFactory,
    ExpenseReimbursementAllowanceConfigurationFactory,
    ReimbursementAllowanceConfigurationFactory,
)
from clients import features
from contracts.acceptances.models import ContractSignerType
from contracts.amendments.application import create_contract_amendment
from contracts.models.contract import Contract
from contracts.payments.models import PaymentAmountCalculation
from contracts.payments.revision import apply_contract_payment_template_pending_revision
from contracts.payments.schedules import (
    CREATE_CONTRACT_PAYMENT_FUNCTION,
    CREATE_FIRST_CONTRACT_PAYMENT_FUNCTION,
    CREATE_LAST_CONTRACT_PAYMENT_FUNCTION,
)
from contracts.revisions.services import (
    _create_contract_original_revision,
    accept_contract_revision,
    cancel_contract_revision,
    compare_contract_revisions,
    initiate_contract_revision,
)
from contracts.serializers.contract import ContractRetrieveSerializer
from contracts.services.contract import end_contract_now
from contracts.services.payment import create_contract_payment
from contracts.tests.factories import (
    ContractAmendmentFactory,
    ContractFactory,
    ContractPaymentTemplateFactory,
    ContractTaskTemplateFactory,
    PaymentTaxFactory,
    TaskDataFactory,
    contract_amendment_data_factory,
    contract_amendment_factory,
    contract_revision_factory,
    fake,
)
from contracts.tests.utils import ActionLogTestCaseMixin, ContractTestCaseMixin
from documents.tests.factories import WorkItemCategoryFactory
from events.event_types import AcceptContract, ContractApproved
from notifications.tests.test_email_notifications import mock_send_mail
from preferences.tests.factories import (
    CustomFieldsTemplateFactory,
    get_custom_fields_data,
)
from schedulers.scheduling.recurrence_rule import Frequency, RecurrenceRule
from shortlist.tests.helpers import TenantTestCase, features_enabled
from users.models import Actor
from users.tests.factories import UserFactory


class ContractAmendmentTestCase(ContractTestCaseMixin, ActionLogTestCaseMixin, TenantTestCase):
    def setUp(self):
        super().setUp()

        tax = PaymentTaxFactory()
        category = WorkItemCategoryFactory()

        with freeze_time(date(2024, 1, 5)):
            self.contract = ContractFactory(
                start_date=date(2024, 1, 5),
                end_date=date(2024, 6, 25),
                with_details_data=True,
                with_fixed_rate_payments=True,
                payment_template=ContractPaymentTemplateFactory(
                    amount=Decimal(1000),
                    tax=tax,
                    category=category,
                    pay_period=RecurrenceRule(frequency=Frequency.MONTHLY, month_days=[-1]),
                    first_pay_period_amount_calculation=PaymentAmountCalculation.CALCULATE_WORKING_DAYS,
                    last_pay_period_amount_calculation=PaymentAmountCalculation.CALCULATE_WORKING_DAYS,
                ),
                expiring=True,
                live=True,
            )

    def test_create_original_revision_with_fixed_rate_payments(self):
        contract_revision = _create_contract_original_revision(self.contract)

        self.assertEqual(contract_revision.revision_number, 0)
        self.assertEqual(contract_revision.main_contract_id, self.contract.id)
        self.assertContractEqual(contract_revision, self.contract)

    @features_enabled([features.PAYMENTS_ASSIGNED_BUYER])
    def test_create_original_revision_with_statement_of_work(self):
        with self.roles_enabled('buyer', includes=['buyer.contracts', 'buyer.payments.approve']):
            contract = ContractFactory(
                with_details_data=True,
                with_statement_of_work=True,
                create_task_templates__size=2,
                invoicing_template__automated_cumulative=True,
                expiring=True,
                live=True,
            )

        contract_revision = _create_contract_original_revision(contract)

        self.assertEqual(contract_revision.revision_number, 0)
        self.assertEqual(contract_revision.main_contract_id, contract.id)
        self.assertContractEqual(contract_revision, contract)
        for task_template in contract_revision.list_task_templates():
            self.assertIsNotNone(task_template.task_id)

    @freeze_time(date(2024, 2, 15))
    def test_compare_contract_revisions(self):
        revision_changes = dict(
            description=fake.sentence(),
            end_date=date(2024, 7, 25),
            custom_fields=get_custom_fields_data(self.contract.template.contract_details_template),
            payment_template=dict(
                amount=self.contract.payment_template.amount + Decimal(1000),
                last_pay_period_amount_calculation=PaymentAmountCalculation.CALCULATE_CALENDAR_DAYS,
            ),
        )
        contract_amendment = contract_amendment_factory(self.contract, revision_changes)

        contract_diff = compare_contract_revisions(self.contract, contract_amendment.contract_revision)

        self.assertTrue(contract_diff.has_changes)
        self.assertTrue(contract_diff.contract.has_changes)
        self.assertTrue(contract_diff.payment_template.has_changes)

        self.assertEqual(contract_diff.contract.original['description'], self.contract.description)
        self.assertEqual(contract_diff.contract.original['end_date'], self.contract.end_date)
        self.assertEqual(contract_diff.contract.original['custom_fields_data'], self.contract.custom_fields_data)

        self.assertEqual(contract_diff.contract.changed['description'], revision_changes['description'])
        self.assertEqual(contract_diff.contract.changed['end_date'], revision_changes['end_date'])
        self.assertEqual(contract_diff.contract.changed['custom_fields_data'], revision_changes['custom_fields'])

        self.assertEqual(contract_diff.payment_template.original['amount'], self.contract.payment_template.amount)
        self.assertEqual(
            contract_diff.payment_template.original['last_pay_period_amount_calculation'],
            self.contract.payment_template.last_pay_period_amount_calculation,
        )

        self.assertEqual(contract_diff.payment_template.changed['amount'], revision_changes['payment_template']['amount'])
        self.assertEqual(
            contract_diff.payment_template.changed['last_pay_period_amount_calculation'],
            revision_changes['payment_template']['last_pay_period_amount_calculation'],
        )

    @freeze_time(date(2024, 2, 15))
    def test_create_contract_amendment(self):
        revision_changes = dict(
            description=fake.sentence(),
            end_date=date(2024, 7, 25),
            custom_fields=get_custom_fields_data(self.contract.template.contract_details_template),
            payment_template=dict(
                amount=self.contract.payment_template.amount + Decimal(1000),
                last_pay_period_amount_calculation=PaymentAmountCalculation.CALCULATE_CALENDAR_DAYS,
            ),
        )

        with self.as_user(self.user), self.roles_enabled('buyer', includes=['buyer.contracts']):
            contract_amendment = create_contract_amendment(
                self.contract, contract_amendment_data_factory(self.contract, revision_changes), self.user
            )

        self.assertTrue(self.contract.under_revision)
        self.assertEqual(contract_amendment.contract, self.contract)
        self.assertEqual(contract_amendment.contract_revision.revision_number, 1)
        self.assertEqual(contract_amendment.contract_revision.main_contract, self.contract)
        self.assertEqual(contract_amendment.contract_revision.description, revision_changes['description'])
        self.assertEqual(contract_amendment.contract_revision.end_date, revision_changes['end_date'])
        self.assertEqual(contract_amendment.contract_revision.custom_fields_data, revision_changes['custom_fields'])
        self.assertEqual(contract_amendment.contract_revision.payment_template.amount, revision_changes['payment_template']['amount'])
        self.assertEqual(
            contract_amendment.contract_revision.payment_template.first_pay_period_amount_calculation,
            self.contract.payment_template.first_pay_period_amount_calculation,
        )
        self.assertEqual(
            contract_amendment.contract_revision.payment_template.last_pay_period_amount_calculation,
            revision_changes['payment_template']['last_pay_period_amount_calculation'],
        )

    @freeze_time(date(2024, 2, 15))
    def test_cancel_contract_amendment(self):
        contract_amendment = ContractAmendmentFactory(
            contract=self.contract,
            contract_revision=contract_revision_factory(self.contract, dict(description=fake.sentence())),
            initiated=True,
        )

        cancel_contract_revision(contract_amendment.contract_revision)

        self.assertFalse(self.contract.under_revision)
        self.assertTrue(contract_amendment.contract_revision.is_canceled)

    @freeze_time(date(2024, 2, 15))
    def test_create_contract_amendment_after_canceling_previous_amendment(self):
        contract_amendment = ContractAmendmentFactory(
            contract=self.contract,
            contract_revision=contract_revision_factory(self.contract, dict(description=fake.sentence())),
            initiated=True,
        )
        cancel_contract_revision(contract_amendment.contract_revision)
        self.assertFalse(self.contract.under_revision)

        contract_amendment = create_contract_amendment(
            self.contract, contract_amendment_data_factory(self.contract, dict(description=fake.sentence())), self.contract.created_by
        )

        self.assertTrue(self.contract.under_revision)
        self.assertEqual(contract_amendment.contract_revision.revision_number, 2)

    @freeze_time(date(2024, 2, 15))
    def test_accept_contract_revision(self):
        revision_changes = dict(
            description=fake.sentence(),
            end_date=date(2024, 8, 25),
            custom_fields=get_custom_fields_data(self.contract.template.contract_details_template),
        )
        contract_amendment = ContractAmendmentFactory(
            contract=self.contract, contract_revision=contract_revision_factory(self.contract, revision_changes), initiated=True
        )

        accept_contract_revision(contract_amendment.contract_revision)

        contract = Contract.objects.get(id=self.contract.id)
        contract_revision = Contract.objects.get(id=contract_amendment.contract_revision.id)
        self.assertFalse(contract.under_revision)
        self.assertEqual(contract.revision_number, 1)
        self.assertEqual(contract.description, revision_changes['description'])
        self.assertEqual(contract.end_date, revision_changes['end_date'])
        self.assertEqual(contract.custom_fields_data, revision_changes['custom_fields'])
        self.assertTrue(contract_revision.is_ended)

    @freeze_time(date(2024, 2, 15))
    @mock.patch("contracts.payments.schedules.create_or_update_recurring_schedule")
    @mock.patch("contracts.payments.schedules.create_or_update_one_time_schedule")
    def test_accept_contract_payment_template_revision_apply_on_last_pay_period(
        self,
        create_or_update_one_time_schedule: MagicMock,
        create_or_update_recurring_schedule: MagicMock,
    ):
        revision_changes = dict(
            end_date=date(2024, 3, 25),
            payment_template=dict(
                amount=self.contract.payment_template.amount + Decimal(1000),
                last_pay_period_amount_calculation=PaymentAmountCalculation.CALCULATE_CALENDAR_DAYS,
            ),
        )
        contract_amendment = ContractAmendmentFactory(
            contract=self.contract, contract_revision=contract_revision_factory(self.contract, revision_changes), initiated=True
        )

        accept_contract_revision(contract_amendment.contract_revision)

        contract = Contract.objects.get(id=self.contract.id)
        self.assertEqual(contract.revision_number, 1)
        self.assertEqual(contract.end_date, revision_changes['end_date'])
        self.assertEqual(
            contract.payment_template.last_pay_period_amount_calculation,
            revision_changes['payment_template']['last_pay_period_amount_calculation'],
        )
        self.assertEqual(contract.payment_template.amount, Decimal(1000))

        create_or_update_one_time_schedule.assert_has_calls(
            [
                call(
                    name=f"contract-first-payment-{self.contract.id}",
                    execute_date=datetime(2024, 1, 31, tzinfo=UTC),
                    execute_function=CREATE_FIRST_CONTRACT_PAYMENT_FUNCTION,
                    execute_params=dict(contract_id=self.contract.id),
                    created_by=Actor.objects.system_actor,
                ),
                call(
                    name=f"contract-last-payment-{self.contract.id}",
                    execute_date=datetime(2024, 3, 31, tzinfo=UTC),
                    execute_function=CREATE_LAST_CONTRACT_PAYMENT_FUNCTION,
                    execute_params=dict(contract_id=self.contract.id),
                    created_by=Actor.objects.system_actor,
                ),
            ],
            any_order=True,
        )
        create_or_update_recurring_schedule.assert_has_calls(
            [
                call(
                    name=f"contract-payment-{self.contract.id}",
                    recurrence_rule=contract_amendment.contract_revision.payment_template.pay_period,
                    start_date=date(2024, 2, 1),
                    end_date=date(2024, 2, 29),
                    execute_function=CREATE_CONTRACT_PAYMENT_FUNCTION,
                    execute_params=dict(contract_id=self.contract.id),
                    created_by=Actor.objects.system_actor,
                ),
            ]
        )

        with freeze_time(date(2024, 2, 29)):
            create_contract_payment(self.contract.id)

        contract = Contract.objects.get(id=self.contract.id)
        self.assertEqual(contract.payment_template.amount, revision_changes['payment_template']['amount'])

    @freeze_time(date(2024, 2, 15))
    @mock.patch("contracts.payments.schedules.create_or_update_recurring_schedule")
    @mock.patch("contracts.payments.schedules.create_or_update_one_time_schedule")
    def test_accept_contract_payment_template_revision_apply_on_next_recurring_pay_period(
        self,
        create_or_update_one_time_schedule: MagicMock,
        create_or_update_recurring_schedule: MagicMock,
    ):
        revision_changes = dict(
            end_date=date(2024, 8, 25),
            payment_template=dict(
                amount=self.contract.payment_template.amount + Decimal(1000),
                last_pay_period_amount_calculation=PaymentAmountCalculation.CALCULATE_CALENDAR_DAYS,
            ),
        )
        contract_amendment = ContractAmendmentFactory(
            contract=self.contract, contract_revision=contract_revision_factory(self.contract, revision_changes), initiated=True
        )

        accept_contract_revision(contract_amendment.contract_revision)

        contract = Contract.objects.get(id=self.contract.id)
        self.assertEqual(contract.revision_number, 1)
        self.assertEqual(contract.end_date, revision_changes['end_date'])
        self.assertEqual(
            contract.payment_template.last_pay_period_amount_calculation,
            revision_changes['payment_template']['last_pay_period_amount_calculation'],
        )
        self.assertEqual(contract.payment_template.amount, Decimal(1000))

        create_or_update_one_time_schedule.assert_has_calls(
            [
                call(
                    name=f"contract-first-payment-{self.contract.id}",
                    execute_date=datetime(2024, 1, 31, tzinfo=UTC),
                    execute_function=CREATE_FIRST_CONTRACT_PAYMENT_FUNCTION,
                    execute_params=dict(contract_id=self.contract.id),
                    created_by=Actor.objects.system_actor,
                ),
                call(
                    name=f"contract-last-payment-{self.contract.id}",
                    execute_date=datetime(2024, 8, 31, tzinfo=UTC),
                    execute_function=CREATE_LAST_CONTRACT_PAYMENT_FUNCTION,
                    execute_params=dict(contract_id=self.contract.id),
                    created_by=Actor.objects.system_actor,
                ),
            ],
            any_order=True,
        )
        create_or_update_recurring_schedule.assert_has_calls(
            [
                call(
                    name=f"contract-payment-{self.contract.id}",
                    recurrence_rule=contract_amendment.contract_revision.payment_template.pay_period,
                    start_date=date(2024, 2, 1),
                    end_date=date(2024, 7, 31),
                    execute_function=CREATE_CONTRACT_PAYMENT_FUNCTION,
                    execute_params=dict(contract_id=self.contract.id),
                    created_by=Actor.objects.system_actor,
                ),
            ]
        )

        with freeze_time(date(2024, 2, 29)):
            create_contract_payment(self.contract.id)

        contract = Contract.objects.get(id=self.contract.id)
        self.assertEqual(contract.payment_template.amount, revision_changes['payment_template']['amount'])

    @freeze_time(date(2024, 1, 15))
    @mock.patch("contracts.payments.schedules.disable_schedule")
    @mock.patch("contracts.payments.schedules.create_or_update_one_time_schedule")
    def test_accept_contract_payment_template_revision_apply_immediately_if_first_pay_period_is_the_only_period(
        self,
        create_or_update_one_time_schedule: MagicMock,
        disable_schedule: MagicMock,
    ):
        revision_changes = dict(
            end_date=date(2024, 1, 25),
            payment_template=dict(
                amount=self.contract.payment_template.amount + Decimal(1000),
                first_pay_period_amount_calculation=PaymentAmountCalculation.CALCULATE_CALENDAR_DAYS,
            ),
        )
        contract_amendment = ContractAmendmentFactory(
            contract=self.contract, contract_revision=contract_revision_factory(self.contract, revision_changes), initiated=True
        )

        accept_contract_revision(contract_amendment.contract_revision)

        contract = Contract.objects.get(id=self.contract.id)
        self.assertEqual(contract.revision_number, 1)
        self.assertEqual(contract.end_date, revision_changes['end_date'])
        self.assertEqual(contract.payment_template.amount, revision_changes['payment_template']['amount'])
        self.assertEqual(
            contract.payment_template.first_pay_period_amount_calculation,
            revision_changes['payment_template']['first_pay_period_amount_calculation'],
        )

        create_or_update_one_time_schedule.assert_has_calls(
            [
                call(
                    name=f"contract-first-payment-{self.contract.id}",
                    execute_date=datetime(2024, 1, 31, tzinfo=UTC),
                    execute_function=CREATE_FIRST_CONTRACT_PAYMENT_FUNCTION,
                    execute_params=dict(contract_id=self.contract.id),
                    created_by=Actor.objects.system_actor,
                ),
            ]
        )
        disable_schedule.assert_has_calls(
            [call(name=f"contract-last-payment-{self.contract.id}"), call(name=f"contract-payment-{self.contract.id}")]
        )

    @freeze_time(date(2024, 2, 15))
    @mock.patch("contracts.payments.schedules.disable_schedule")
    @mock.patch("contracts.payments.schedules.create_or_update_one_time_schedule")
    def test_accept_contract_payment_template_revision_apply_immediately_if_last_pay_period_is_the_only_period(
        self,
        create_or_update_one_time_schedule: MagicMock,
        disable_schedule: MagicMock,
    ):
        revision_changes = dict(
            end_date=date(2024, 2, 25),
            payment_template=dict(
                amount=self.contract.payment_template.amount + Decimal(1000),
                last_pay_period_amount_calculation=PaymentAmountCalculation.CALCULATE_CALENDAR_DAYS,
            ),
        )
        contract_amendment = ContractAmendmentFactory(
            contract=self.contract, contract_revision=contract_revision_factory(self.contract, revision_changes), initiated=True
        )

        accept_contract_revision(contract_amendment.contract_revision)

        contract = Contract.objects.get(id=self.contract.id)
        self.assertEqual(contract.revision_number, 1)
        self.assertEqual(contract.end_date, revision_changes['end_date'])
        self.assertEqual(contract.payment_template.amount, revision_changes['payment_template']['amount'])
        self.assertEqual(
            contract.payment_template.last_pay_period_amount_calculation,
            revision_changes['payment_template']['last_pay_period_amount_calculation'],
        )

        create_or_update_one_time_schedule.assert_has_calls(
            [
                call(
                    name=f"contract-first-payment-{self.contract.id}",
                    execute_date=datetime(2024, 1, 31, tzinfo=UTC),
                    execute_function=CREATE_FIRST_CONTRACT_PAYMENT_FUNCTION,
                    execute_params=dict(contract_id=self.contract.id),
                    created_by=Actor.objects.system_actor,
                ),
                call(
                    name=f"contract-last-payment-{self.contract.id}",
                    execute_date=datetime(2024, 2, 29, tzinfo=UTC),
                    execute_function=CREATE_LAST_CONTRACT_PAYMENT_FUNCTION,
                    execute_params=dict(contract_id=self.contract.id),
                    created_by=Actor.objects.system_actor,
                ),
            ],
            any_order=True,
        )
        disable_schedule.assert_has_calls([call(name=f"contract-payment-{self.contract.id}")])

    @freeze_time(date(2024, 1, 5))
    def test_accept_contract_task_template_revision(self):
        start_date = date(2024, 1, 5)
        end_date = date(2024, 5, 25)
        contract: Contract = ContractFactory(
            start_date=start_date,
            end_date=end_date,
            currency='PLN',
            with_statement_of_work=True,
            create_task_templates=[
                {'task_data': TaskDataFactory.json_dict(date_start=start_date, date_end=end_date)},
                {'task_data': TaskDataFactory.json_dict(date_start=start_date, date_end=end_date)},
            ],
            live=True,
        )

        contract = Contract.objects.get(id=contract.id)
        contract_data = dict(ContractRetrieveSerializer(instance=contract).data)
        task_templates: list = contract_data['task_templates']
        for task_template in task_templates:
            task_template['name'] = fake.text()

        task_templates.extend(
            [
                ContractTaskTemplateFactory.dict(
                    start_date=start_date,
                    end_date=end_date,
                ),
                ContractTaskTemplateFactory.dict(
                    start_date=start_date,
                    end_date=end_date,
                ),
            ]
        )

        revision_changes = {'task_templates': task_templates}
        contract_amendment = ContractAmendmentFactory(
            contract=contract,
            contract_revision=contract_revision_factory(contract, revision_changes),
            initiated=True,
        )

        accept_contract_revision(contract_amendment.contract_revision)

        contract = Contract.objects.get(id=contract.id)
        contract_revision = Contract.objects.get(id=contract_amendment.contract_revision.id)

        self.assertTaskTemplatesEqual(contract.list_task_templates(), contract_revision.list_task_templates())

        self.assertTaskTemplatesDataEqual(revision_changes['task_templates'], contract.list_task_templates())
        for task_template in contract.list_task_templates():
            self.assertIsNotNone(task_template.task_id)
            self.assertIsNotNone(task_template.task_data['task_group']['id'])

        self.assertTaskTemplatesDataEqual(revision_changes['task_templates'], contract_revision.list_task_templates())
        for task_template in contract_revision.list_task_templates():
            self.assertIsNotNone(task_template.task_id)
            self.assertIsNotNone(task_template.task_data['task_group']['id'])

    @freeze_time(date(2024, 1, 5))
    def test_accept_contract_revision_with_statement_of_work_and_budget(self):
        WorkItemCategoryFactory(is_work=True)
        budget = BudgetFactory(
            currency='PLN',
            contracted_budget_configuration=ContractedBudgetConfigurationFactory(
                general_budget=True,
                prevent_over_budget_expenses=False,
                allocated_amount=Decimal(1000),
            ),
            reimbursement_allowance_configuration=ReimbursementAllowanceConfigurationFactory(
                general_allowance=True,
                allocated_amount=Decimal(2000),
                expense_reimbursement_allowances=[
                    ExpenseReimbursementAllowanceConfigurationFactory(),
                    ExpenseReimbursementAllowanceConfigurationFactory(),
                ],
            ),
        )
        start_date = date(2024, 1, 5)
        end_date = date(2024, 5, 25)
        contract: Contract = ContractFactory(
            start_date=start_date,
            end_date=end_date,
            currency='PLN',
            with_statement_of_work=True,
            create_task_templates=[
                {
                    'task_data': TaskDataFactory.json_dict(
                        date_start=start_date,
                        date_end=end_date,
                        budget_total=Decimal(200),
                    )
                },
                {
                    'task_data': TaskDataFactory.json_dict(
                        date_start=start_date,
                        date_end=end_date,
                        budget_total=Decimal(300),
                    )
                },
            ],
            budget=budget,
            live=True,
        )

        contract = Contract.objects.get(id=contract.id)
        budget_data = get_budget_configuration(budget.id).model_dump()
        budget_data['contracted_budget']['allocated_amount'] = Decimal(2000)
        budget_data['contracted_budget']['prevent_over_budget_expenses'] = True
        budget_data['reimbursement_allowance']['allocated_amount'] = Decimal(3000)
        budget_data['reimbursement_allowance']['expense_reimbursement_allowances'].pop(1)
        budget_data['reimbursement_allowance']['expense_reimbursement_allowances'].append(
            {'expense_category': WorkItemCategoryFactory().id}
        )
        revision_changes = {'budget': budget_data}
        contract_amendment = ContractAmendmentFactory(
            contract=contract,
            contract_revision=contract_revision_factory(contract, revision_changes),
            initiated=True,
        )

        accept_contract_revision(contract_amendment.contract_revision)

        contract = Contract.objects.get(id=contract.id)
        self.assertEqual(contract.revision_number, 1)
        self.assertBudgetConfiguration(get_budget_configuration(contract.budget_id), budget_data)

    @freeze_time(date(2024, 1, 5))
    def test_accept_contract_revision_with_statement_of_work_update_existing_tasks(self):
        task_custom_fields_template = CustomFieldsTemplateFactory(model='Task')
        contract: Contract = ContractFactory(
            start_date=date(2024, 1, 5),
            end_date=date(2024, 5, 30),
            with_statement_of_work=True,
            create_task_templates=[
                {
                    'task_data': TaskDataFactory.json_dict(
                        date_start=date(2024, 1, 5),
                        date_end=date(2024, 3, 25),
                    ),
                    'custom_fields_data': get_custom_fields_data(custom_fields_template=task_custom_fields_template),
                },
                {
                    'task_data': TaskDataFactory.json_dict(
                        date_start=date(2024, 2, 10),
                        date_end=date(2024, 3, 10),
                    ),
                    'custom_fields_data': get_custom_fields_data(custom_fields_template=task_custom_fields_template),
                },
            ],
            live=True,
        )

        contract = Contract.objects.get(id=contract.id)
        changed_tasks_data = [
            TaskDataFactory.json_dict(
                date_start=date(2024, 3, 5),
                date_end=date(2024, 4, 25),
            ),
            TaskDataFactory.json_dict(
                date_start=date(2024, 4, 5),
                date_end=date(2024, 5, 25),
            ),
        ]
        task_templates = [
            {
                'id': task_template.id,
                'task_id': task_template.task_id,
                **deepcopy(task_template.task_data),
                'name': changed_task_data['name'],
                'description': changed_task_data['description'],
                'date_start': changed_task_data['date_start'],
                'date_end': changed_task_data['date_end'],
                'budget_total': changed_task_data['budget_total'],
                'custom_fields': get_custom_fields_data(custom_fields_template=task_custom_fields_template),
            }
            for task_template, changed_task_data in zip(contract.list_task_templates(), changed_tasks_data, strict=False)
        ]

        contract_amendment = ContractAmendmentFactory(
            contract=contract,
            contract_revision=contract_revision_factory(contract, {'task_templates': task_templates}),
            initiated=True,
        )

        accept_contract_revision(contract_amendment.contract_revision)

        contract = Contract.objects.get(id=contract.id)
        self.assertEqual(contract.revision_number, 1)
        self.assertTaskTemplatesEqual(contract_amendment.contract_revision.list_task_templates(), contract.list_task_templates())
        for task_template in contract_amendment.contract_revision.list_task_templates():
            self.assertNotEqual(task_template.custom_fields_data, {})

    @freeze_time(date(2024, 1, 15))
    def test_create_contract_amendment_fails_if_does_not_contain_changes(self):
        revision_changes = dict()

        with self.assertRaises(ValidationError) as e:
            with self.as_user(self.user), self.roles_enabled('buyer', includes=['buyer.contracts']):
                create_contract_amendment(self.contract, contract_amendment_data_factory(self.contract, revision_changes), self.user)

        self.assertIn("Contract revision does not contain changes", e.exception)

    @freeze_time(date(2024, 1, 15))
    def test_create_contract_amendment_fails_if_contract_revision_is_already_pending(self):
        revision_changes = dict(description=fake.sentence())
        ContractAmendmentFactory(
            contract=self.contract, contract_revision=contract_revision_factory(self.contract, revision_changes), initiated=True
        )

        with self.assertRaises(ValidationError) as e:
            create_contract_amendment(self.contract, contract_amendment_data_factory(self.contract, revision_changes), self.user)

        self.assertIn("Contract is already under revision", e.exception.messages)

    @freeze_time(date(2024, 1, 15))
    def test_accept_contract_payment_template_revision_fails_if_changed_pay_period_already_paid(self):
        revision_changes = dict(
            payment_template=dict(
                first_pay_period_amount_calculation=PaymentAmountCalculation.CALCULATE_CALENDAR_DAYS,
            ),
        )
        contract_amendment = ContractAmendmentFactory(
            contract=self.contract, contract_revision=contract_revision_factory(self.contract, revision_changes), initiated=True
        )

        with freeze_time(date(2024, 2, 5)), self.assertRaises(ValidationError) as e:
            accept_contract_revision(contract_amendment.contract_revision)

        self.assertIn("First pay period cannot be changed", e.exception.messages)

    @freeze_time(date(2024, 1, 15))
    def test_accept_contract_payment_template_revision_fails_if_contract_has_ended(self):
        revision_changes = dict(description=fake.sentence())
        contract_amendment = ContractAmendmentFactory(
            contract=self.contract, contract_revision=contract_revision_factory(self.contract, revision_changes), initiated=True
        )
        with freeze_time(self.contract.end_date + relativedelta(days=+1)):
            end_contract_now(self.contract)

        with freeze_time(date(2024, 8, 5)), self.assertRaises(ValidationError) as e:
            accept_contract_revision(contract_amendment.contract_revision)

        self.assertIn("Invalid contract status: ended", e.exception.messages)

    @freeze_time(date(2024, 2, 15))
    def test_accept_contract_payment_template_revision_overrides_previous_pending_changes(self):
        revision_changes = dict(payment_template=dict(amount=Decimal(2000)))
        ContractAmendmentFactory(
            contract=self.contract, contract_revision=contract_revision_factory(self.contract, revision_changes), accepted=True
        )
        contract = Contract.objects.get(id=self.contract.id)
        revision_changes = dict(payment_template=dict(amount=Decimal(3000)))
        contract_amendment = contract_amendment_factory(contract, revision_changes)

        accept_contract_revision(contract_amendment.contract_revision)
        apply_contract_payment_template_pending_revision(contract)

        contract = Contract.objects.get(id=self.contract.id)
        self.assertEqual(contract.payment_template.amount, Decimal(3000))

    def test_accept_contract_revision_events_and_notifications(self):
        buyer = UserFactory()
        contract = ContractFactory(
            template__post_amendment_signers=[
                dict(user=buyer, type=ContractSignerType.BUYER),
                dict(type=ContractSignerType.PARTNER),
            ],
            live=True,
        )
        contract_amendment = ContractAmendmentFactory(
            contract=contract,
            contract_revision=contract_revision_factory(contract, dict(description=fake.sentence())),
            initiated=True,
        )

        with self.watch_event(ContractApproved) as events, mock_send_mail() as send_mail_mock:
            accept_contract_revision(contract_amendment.contract_revision)
            self.run_commit_hooks()

        contract_revision = Contract.objects.get(id=contract_amendment.contract_revision.id)
        emails = send_mail_mock.emails_for_event(ContractApproved)
        self.assertEqual(len(events), 1)
        self.assertEqual(len(emails), 1)
        self.assertEqual(len(emails[0]), 3)
        self.assertCountEqual(emails[0], [buyer.email, contract.created_by.email, contract.partner.email])
        self.assertActionLog(contract_revision, 'contract_approved', "Contract approved")

    @freeze_time(date(2024, 3, 20))
    def test_ending_contract_cancels_pending_revision(self):
        revision_changes = dict(description=fake.sentence())
        contract_revision = ContractAmendmentFactory(
            contract=self.contract, contract_revision=contract_revision_factory(self.contract, revision_changes), initiated=True
        ).contract_revision

        end_contract_now(self.contract)

        contract = Contract.objects.get(id=self.contract.id)
        contract_revision = Contract.objects.get(id=contract_revision.id)
        self.assertTrue(contract.is_ended)
        self.assertFalse(contract.under_revision)
        self.assertTrue(contract_revision.is_canceled)

    def test_contract_revision_notification_external_field_change(self):
        """Test that changing external fields sends notification to vendor"""
        contract_amendment = ContractAmendmentFactory(
            contract=self.contract,
            contract_revision=contract_revision_factory(self.contract, dict(description=fake.sentence())),  # description is external
        )

        with self.watch_event(AcceptContract) as events, mock_send_mail() as send_mail_mock:
            initiate_contract_revision(contract_amendment.contract_revision)
            self.run_commit_hooks()

        emails = send_mail_mock.emails_for_event(AcceptContract)
        self.assertEqual(len(events), 1)
        self.assertEqual(len(emails), 1)
        self.assertIn(self.contract.partner.email, emails[0])

    def test_contract_revision_notification_internal_field_change(self):
        """Test that changing only internal fields does NOT send notification to vendor"""
        contract_amendment = ContractAmendmentFactory(
            contract=self.contract,
            contract_revision=contract_revision_factory(self.contract, dict(name=fake.sentence())),  # name is internal
        )

        with self.watch_event(AcceptContract) as events, mock_send_mail() as send_mail_mock:
            initiate_contract_revision(contract_amendment.contract_revision)
            self.run_commit_hooks()

        emails = send_mail_mock.emails_for_event(AcceptContract)
        self.assertEqual(len(events), 0)
        self.assertEqual(len(emails), 0)

    def test_contract_revision_notification_mixed_field_change(self):
        """Test that changing both external and internal fields sends notification to vendor"""
        contract_amendment = ContractAmendmentFactory(
            contract=self.contract,
            contract_revision=contract_revision_factory(
                self.contract,
                dict(
                    description=fake.sentence(),  # external field
                    name=fake.sentence()  # internal field
                )
            ),
        )

        with self.watch_event(AcceptContract) as events, mock_send_mail() as send_mail_mock:
            initiate_contract_revision(contract_amendment.contract_revision)
            self.run_commit_hooks()

        emails = send_mail_mock.emails_for_event(AcceptContract)
        self.assertEqual(len(events), 1)
        self.assertEqual(len(emails), 1)
        self.assertIn(self.contract.partner.email, emails[0])
