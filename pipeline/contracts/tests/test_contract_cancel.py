from datetime import date, datetime, UTC
from unittest import mock
from unittest.mock import MagicMock

from dateutil.relativedelta import relativedelta
from django.core.exceptions import ValidationError
from parameterized import parameterized

from clients.models import features
from contracts.models.contract import (
    ContractCancellationReason,
    ContractDuration,
    ContractPeriod,
    ContractStatus,
    calculate_contract_cancellation_datetime,
)
from contracts.services.contract import cancel_contract
from contracts.tests.factories import ContractFactory
from contracts.tests.utils import ActionLogTestCaseMixin
from events.event_types import ContractCanceled, ContractStatusChanged
from notifications.tests.test_email_notifications import mock_send_mail
from shortlist.tests.helpers import TenantTestCase, features_enabled


class CancelContractTestCase(TenantTestCase, ActionLogTestCaseMixin):
    @parameterized.expand(
        [
            (
                ContractPeriod.FIXED_DATES,
                None,
                date(2024, 2, 1),
                date(2024, 4, 15),
                datetime(2024, 4, 15, 0, 0, 0, tzinfo=UTC),
            ),
            (
                ContractPeriod.VARIABLE_DATES,
                ContractDuration.SPECIFIC_DATE,
                None,
                date(2024, 4, 15),
                datetime(2024, 4, 15, 0, 0, 0, tzinfo=UTC),
            ),
            (
                ContractPeriod.VARIABLE_DATES,
                ContractDuration.SIX_MONTHS,
                None,
                None,
                None,
            ),
        ]
    )
    def test_calculate_contract_cancellation_datetime(self, period, duration, start_date, end_date, cancellation_datetime):
        contract = ContractFactory(period=period, duration=duration, start_date=start_date, end_date=end_date)

        self.assertEqual(calculate_contract_cancellation_datetime(contract), cancellation_datetime)

    @parameterized.expand(
        [
            (ContractStatus.DRAFT,),
            (ContractStatus.LIVE,),
            (ContractStatus.ENDED,),
            (ContractStatus.CANCELED,),
        ]
    )
    @features_enabled([features.CONTRACTS])
    def test_cancel_contract_error(self, contract_status):
        contract = ContractFactory(
            partner=self._get_vendor(), start_date=date(2023, 6, 19), end_date=date(2023, 8, 18), status=contract_status
        )

        self.assertRaises(ValidationError, cancel_contract, contract=contract)

    @parameterized.expand(
        [
            (ContractStatus.UNDER_REVIEW, True),
            (ContractStatus.UNDER_REVIEW, False),
            (ContractStatus.SIGNING, True),
            (ContractStatus.SIGNING, False),
            (ContractStatus.ACCEPTING, True),
            (ContractStatus.ACCEPTING, False),
            (ContractStatus.REJECTED, True),
            (ContractStatus.REJECTED, False),
            (ContractStatus.SCHEDULED, True),
            (ContractStatus.SCHEDULED, False),
        ]
    )
    @mock.patch("contracts.services.contract.disable_contract_automatic_cancel")
    @mock.patch("contracts.services.contract.disable_contract_schedules")
    @features_enabled([features.CONTRACTS])
    def test_cancel_contract(
        self,
        contract_status,
        visible_to_partner,
        disable_contract_schedules: MagicMock,
        disable_contract_automatic_cancel: MagicMock,
    ):
        contract = ContractFactory(
            partner=self._get_vendor(),
            start_date=date.today(),
            status=contract_status,
            visible_to_partner=visible_to_partner,
        )

        cancel_contract(contract)

        self.assertEqual(contract.status, ContractStatus.CANCELED)
        disable_contract_schedules.assert_called_once_with(contract)
        disable_contract_automatic_cancel.assert_called_once_with(contract)

    @parameterized.expand([(True,), (False,)])
    @features_enabled([features.CONTRACTS])
    def test_contract_canceled_event(self, visible_to_partner):
        contract = ContractFactory(start_date=date.today() + relativedelta(days=+1), initiated=True)
        contract.visible_to_partner = visible_to_partner

        with (
            self.watch_event(ContractCanceled) as events,
            self.watch_event(ContractStatusChanged) as status_events,
            mock_send_mail() as send_mail_mock,
        ):
            cancel_contract(contract)
            self.run_commit_hooks()

        emails = send_mail_mock.emails_for_event(ContractCanceled)
        self.assertEqual(len(events), 1)
        self.assertEqual(events[0].contract, contract)
        self.assertEqual(events[0].contract_cancellation_reason, ContractCancellationReason.USER_REQUESTED)
        email_counter = 1 if visible_to_partner else 0
        self.assertEqual(len(emails), email_counter)

        self.assertStatusChangedEvent(contract, status_events, ContractStatus.CANCELED)
        self.assertActionLog(contract, 'contract_status_changed', "Contract was set to: canceled")
        self.assertActionLog(contract, 'contract_canceled', "Contract was canceled by the user")
