from parameterized import parameterized

from rest_framework import status

from clients import features
from contracts.tests.factories import ContractFactory, generate_contract_signers
from onboarding.models import OnBoardingTemplateMixIn
from shortlist.tests.helpers import APITestCaseMixin, TenantTestCase, features_enabled


class ContractSearchAPIPermissionsTestCase(TenantTestCase, APITestCaseMixin):
    with_login_user = True

    contract_search_api = "/api/contracts/search/"

    @parameterized.expand([('buyer.contracts',), ('buyer.contracts.participant',)])
    @features_enabled([features.CONTRACTS])
    def test_search_contracts_by_creator_allowed(self, role):
        contracts = [ContractFactory(), ContractFactory()]

        with self.roles_enabled('buyer', [role]):
            response_data = self.api_post(self.contract_search_api, data=dict(), status=status.HTTP_200_OK)

        self.assertEqual({d['id'] for d in response_data['hits']}, {c.id for c in contracts})

    @parameterized.expand([('buyer.contracts',), ('buyer.contracts.participant',)])
    @features_enabled([features.CONTRACTS])
    def test_search_draft_contracts_by_not_creator_forbidden(self, role):
        user = self._get_user()
        _ = [ContractFactory(created_by=user), ContractFactory(created_by=user)]

        with self.as_user(self._get_user()), self.roles_enabled('buyer', [role]):
            response_data = self.api_post(self.contract_search_api, data=dict(), status=status.HTTP_200_OK)

        self.assertEqual(len(response_data['hits']), 0)

    @features_enabled([features.CONTRACTS])
    def test_search_contracts_no_permission_forbidden(self):
        self.api_post(self.contract_search_api, data=dict(), status=status.HTTP_403_FORBIDDEN)

    @parameterized.expand([('buyer.contracts.participant',)])
    @features_enabled([features.CONTRACTS])
    def test_search_contracts_by_not_participant_forbidden(self, role):
        _ = [ContractFactory(), ContractFactory(live=True)]

        with self.as_user(self._get_user()), self.roles_enabled('buyer', [role]):
            response_data = self.api_post(self.contract_search_api, data=dict(), status=status.HTTP_200_OK)

        self.assertEqual(len(response_data['hits']), 0)

    @parameterized.expand([('buyer.contracts',)])
    @features_enabled([features.CONTRACTS])
    def test_search_contracts_by_not_participant_allowed(self, role):
        contracts = [ContractFactory(live=True), ContractFactory(live=True)]

        with self.as_user(self._get_user()), self.roles_enabled('buyer', [role]):
            response_data = self.api_post(self.contract_search_api, data=dict(), status=status.HTTP_200_OK)

        self.assertEqual({d['id'] for d in response_data['hits']}, {c.id for c in contracts})

    @parameterized.expand([('buyer.contracts.participant',)])
    @features_enabled([features.CONTRACTS])
    def test_search_contracts_by_signer_allowed(self, role):
        user = self._get_user()
        contracts = [
            ContractFactory(
                template__post_signers=generate_contract_signers([user]),
                live=True,
            )
        ]

        with self.as_user(user), self.roles_enabled('buyer', [role]):
            response_data = self.api_post(self.contract_search_api, data=dict(), status=status.HTTP_200_OK)

        self.assertEqual({d['id'] for d in response_data['hits']}, {c.id for c in contracts})

    @parameterized.expand([('buyer.contracts.participant',)])
    @features_enabled([features.CONTRACTS])
    def test_search_contracts_by_workflow_manager_allowed(self, role):
        user = self._get_user()
        contracts = [
            ContractFactory(
                template__with_workflow=True,
                template__workflow__stages=[
                    {'stage_type': OnBoardingTemplateMixIn.STAGE_REQUEST_DATA, 'is_internal': True, 'onboarding_managers': []},
                    {'stage_type': OnBoardingTemplateMixIn.STAGE_REQUEST_DATA, 'onboarding_managers': [user]},
                ],
                live=True,
            ),
        ]

        with self.as_user(user), self.roles_enabled('buyer', [role]):
            response_data = self.api_post(self.contract_search_api, data=dict(), status=status.HTTP_200_OK)

        self.assertEqual({d['id'] for d in response_data['hits']}, {c.id for c in contracts})
