from clients import features
from contracts.tests.factories import ContractFactory, ContractTemplateFactory
from onboarding.models import OnBoardingStage, OnBoardingTemplateMixIn
from preferences.models import TYPE_CHOICE, TYPE_TEXT_LINE
from preferences.tests.factories import CustomFieldsTemplateFactory
from shortlist.tests.helpers import APITestCaseMixin, TenantTestCase, features_enabled


class ContractForInformationStageTestCase(TenantTestCase, APITestCaseMixin):
    with_login_user = True
    contract_vendor_api = "/api/contract_for_information_stage/{}/?onboarding_stage={}"

    def setUp(self):
        super().setUp()
        contract_global_custom_fields = CustomFieldsTemplateFactory(
            model="Contract",
            post={"template_fields": [{"type": TYPE_CHOICE}, {"type": TYPE_TEXT_LINE, "visible_to_vendors": False}]},
        )
        self.global_custom_fields = list(contract_global_custom_fields.template_fields.all())

        contract_custom_fields = CustomFieldsTemplateFactory(
            post={
                "template_fields": [
                    {'id': self.global_custom_fields[0].id},
                    {'id': self.global_custom_fields[1].id},
                    {"type": TYPE_CHOICE},
                    {"type": TYPE_TEXT_LINE},
                ]
            },
        )

        contract_template = ContractTemplateFactory(
            contract_details_template=contract_custom_fields,
            with_workflow=True,
            workflow__stages=[
                {
                    "stage_type": OnBoardingTemplateMixIn.STAGE_INFORMATION,
                    "information_stage_config": [
                        {"name": "name"},
                        {"name": "description"},
                        {"name": f"custom_{self.global_custom_fields[0].id}"},
                        {"name": f"custom_{self.global_custom_fields[1].id}"},
                    ],
                }
            ],
        )
        self.contract = ContractFactory(template=contract_template, with_details_data=True, visible_to_partner=True)

    @features_enabled([features.CONTRACTS])
    def test_as_buyer(self):
        stage = OnBoardingStage.objects.filter(
            stage_type=OnBoardingTemplateMixIn.STAGE_INFORMATION, workflow_id=self.contract.template.workflow.id
        ).first()
        response_data = self.api_get(self.contract_vendor_api.format(self.contract.id, stage.id))
        self.assertFalse('start_date' in response_data)
        self.assertFalse('end_date' in response_data)
        self.assertEqual(response_data["name"], self.contract.name)
        self.assertEqual(response_data["description"], self.contract.description)
        self.assertEqual(len(response_data["custom_fields"]), 2)
        cf_data = {}
        for cf in response_data["custom_fields"]:
            cf_data[cf["id"]] = cf["value"]
        self.assertEqual(cf_data[self.global_custom_fields[0].id], self.contract.custom_fields_data[str(self.global_custom_fields[0].id)])
        self.assertEqual(cf_data[self.global_custom_fields[1].id], self.contract.custom_fields_data[str(self.global_custom_fields[1].id)])

    @features_enabled([features.CONTRACTS])
    def test_as_vendor(self):
        vendor = self._get_vendor(able_to_login=True)
        self.contract.partner = vendor
        self.contract.save()
        stage = OnBoardingStage.objects.filter(
            stage_type=OnBoardingTemplateMixIn.STAGE_INFORMATION, workflow_id=self.contract.template.workflow.id
        ).first()
        with self.as_vendor():
            response_data = self.api_get(self.contract_vendor_api.format(self.contract.id, stage.id))
        self.assertEqual(response_data["name"], self.contract.name)
        self.assertEqual(response_data["description"], self.contract.description)
        self.assertFalse('start_date' in response_data)
        self.assertFalse('end_date' in response_data)
        self.assertEqual(len(response_data["custom_fields"]), 1)
        self.assertEqual(response_data["custom_fields"][0]["value"], self.contract.custom_fields_data[str(self.global_custom_fields[0].id)])
