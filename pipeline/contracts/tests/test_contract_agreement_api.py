from django.utils.timezone import now
from freezegun import freeze_time
from rest_framework import status

from clients import features
from contracts.agreements.models import (
    ContractAgreementSignerStatus,
    contract_signer_type,
)
from contracts.models import Contract, ContractAgreementSigner
from contracts.models.template import CONTRACT_DETAILS_TEMPLATE_NAME_PREFIX
from contracts.tests.factories import ContractFactory, ContractTemplateFactory
from contracts.tests.test_contract_agreement import (
    hellosign_config,
    hellosign_sign_current_signer,
)
from deals.models import SignRequest, Template
from deals.tests.factories import AgreementTemplateFactory, SignRequestFactory
from events.event_types import AcceptContractReminder
from preferences.tests.factories import (
    CustomFieldsTemplateFactory,
    get_custom_fields_data,
)
from shortlist.custom_fields import TYPE_USER
from shortlist.tests.helpers import APITestCaseMixin, TenantTestCase, features_enabled
from users.tests.factories import UserFactory
from vendors.tests.factories import VendorFactory


class ContractAgreementAPITestCase(TenantTestCase, APITestCaseMixin):
    with_admin_user = True

    contract_api = "/api/contracts/{}/"
    contract_agreement_signer_remind_api = "/api/contracts/{}/agreements/{}/signers/{}/remind/"
    contract_agreement_signer_reject_vendor_api = "/api/v/contracts/{}/agreements/{}/signers/{}/reject/"
    contract_initiate_api = "/api/contracts/{}/initiate/"
    contract_agreements_api = "/api/contracts/{}/agreements/"

    def setUp(self):
        super().setUp()

        self.vendor = VendorFactory()
        self.buyers = [UserFactory(), UserFactory()]
        self.signers = [
            dict(role="Buyer1", email=self.buyers[0].email),
            dict(role="Buyer2", email=self.buyers[1].email),
        ]

    @features_enabled([features.CONTRACTS])
    @hellosign_config
    def test_initiate_contract_with_agreement_template(self):
        contract = ContractFactory(partner=self.vendor, template__with_agreement=True, template__agreement_template__signers=self.signers)

        response_data = self.api_post(self.contract_initiate_api.format(contract.id), status=status.HTTP_200_OK)

        contract = Contract.objects.get(id=response_data['id'])
        self.assertEqual(len(response_data['agreements']), 1)
        self.assertIsNotNone(response_data['agreements'][0]['sign_request']['id'])
        self.assertIsNone(response_data['agreements'][0]['sign_request']['signed_document'])
        self.assertEqual(response_data['agreements'][0]['sign_request']['status'], SignRequest.STATUS_NEW)
        self.assertTrue(contract.is_signing)
        self.assertContractAgreement(contract, response_data['agreements'][0], 3)

    @features_enabled([features.CONTRACTS])
    @hellosign_config
    def test_initiate_contract_without_agreement_template(self):
        contract = ContractFactory(partner=self.vendor, template__with_agreement_choose_before_signing=True)

        response_data = self.api_post(self.contract_initiate_api.format(contract.id), status=status.HTTP_200_OK)

        contract = Contract.objects.get(id=response_data['id'])
        self.assertEqual(len(response_data['agreements']), 0)
        self.assertTrue(contract.is_signing)
        self.assertIsNone(contract.agreement)

    @features_enabled([features.CONTRACTS])
    @hellosign_config
    def test_create_contract_agreement_with_agreement_template(self):
        contract = ContractFactory(partner=self.vendor, template__with_agreement_choose_before_signing=True, initiated=True)
        agreement_template = AgreementTemplateFactory(signers=self.signers, type=Template.TYPE_CONTRACT)

        response_data = self.api_post(
            self.contract_agreements_api.format(contract.id),
            status=status.HTTP_201_CREATED,
            data=dict(agreement_template=agreement_template.id),
        )

        contract = Contract.objects.get(id=contract.id)
        self.assertIsNotNone(response_data['sign_request']['id'])
        self.assertIsNone(response_data['sign_request']['signed_document'])
        self.assertEqual(response_data['sign_request']['status'], SignRequest.STATUS_NEW)
        self.assertTrue(contract.is_signing)
        self.assertContractAgreement(contract, response_data, 3)

        agreement_template.refresh_from_db()
        self.assertIsNotNone(agreement_template.last_used)

    @features_enabled([features.CONTRACTS])
    @hellosign_config
    def test_create_contract_agreement_with_agreement_template_with_dynamic_signers(self):
        contract_global_custom_fields_template = CustomFieldsTemplateFactory(
            model="Contract",
            post={"template_fields": [{"type": TYPE_USER}, {"type": TYPE_USER}]},
        )
        contract_global_custom_fields = list(contract_global_custom_fields_template.template_fields.all())
        contract_template = ContractTemplateFactory(
            with_contract_details_template=True,
            contract_details_template=CustomFieldsTemplateFactory(
                name_prefix=CONTRACT_DETAILS_TEMPLATE_NAME_PREFIX,
                post={
                    "template_fields": [
                        *[{'id': gcf.id} for gcf in contract_global_custom_fields],
                    ]
                },
            ),
            with_agreement_choose_before_signing=True,
        )
        contract = ContractFactory(
            template=contract_template,
            partner=self.vendor,
            initiated=True,
            with_details_data=True,
            custom_fields_data=get_custom_fields_data(
                contract_global_custom_fields_template,
                {
                    contract_global_custom_fields[0].id: [self.buyers[0].id],
                    contract_global_custom_fields[1].id: [self.buyers[1].id],
                },
            ),
        )
        agreement_template = AgreementTemplateFactory(
            type=Template.TYPE_CONTRACT,
            signers=[
                dict(role="Buyer1", map_to=f"template_field.{contract_global_custom_fields[0].id}"),
                dict(role="Buyer2", map_to=f"template_field.{contract_global_custom_fields[1].id}"),
            ],
        )

        response_data = self.api_post(
            self.contract_agreements_api.format(contract.id),
            status=status.HTTP_201_CREATED,
            data=dict(agreement_template=agreement_template.id),
        )

        contract = Contract.objects.get(id=contract.id)
        self.assertIsNotNone(response_data['sign_request']['id'])
        self.assertIsNone(response_data['sign_request']['signed_document'])
        self.assertEqual(response_data['sign_request']['status'], SignRequest.STATUS_NEW)
        self.assertTrue(contract.is_signing)
        self.assertContractAgreement(contract, response_data, 3)

    @features_enabled([features.CONTRACTS])
    @hellosign_config
    def test_create_contract_agreement_with_agreement_for_signing(self):
        contract = ContractFactory(partner=self.vendor, template__with_agreement_choose_before_signing=True, initiated=True)
        sign_request = SignRequestFactory(for_signing=True, vendor=self.vendor)

        response_data = self.api_post(
            self.contract_agreements_api.format(contract.id),
            status=status.HTTP_201_CREATED,
            data=dict(sign_request=sign_request.id),
        )

        contract = Contract.objects.get(id=contract.id)
        self.assertEqual(response_data['sign_request']['id'], sign_request.id)
        self.assertIsNone(response_data['sign_request']['signed_document'])
        self.assertEqual(response_data['sign_request']['status'], SignRequest.STATUS_NEW)
        self.assertTrue(contract.is_signing)
        self.assertContractAgreement(contract, response_data, 1)

    @features_enabled([features.CONTRACTS])
    @hellosign_config
    def test_create_contract_agreement_with_signed_agreement(self):
        contract = ContractFactory(
            partner=self.vendor, start_date=now().date(), template__with_agreement_choose_before_signing=True, initiated=True
        )
        sign_request = SignRequestFactory(signed=True, vendor=self.vendor)

        response_data = self.api_post(
            self.contract_agreements_api.format(contract.id),
            status=status.HTTP_201_CREATED,
            data=dict(signed_document=sign_request.signed_document.id),
        )

        contract = Contract.objects.get(id=contract.id)
        self.assertEqual(response_data['sign_request']['id'], sign_request.id)
        self.assertEqual(response_data['sign_request']['signed_document']['id'], sign_request.signed_document.id)
        self.assertEqual(response_data['sign_request']['status'], SignRequest.STATUS_DONE)
        self.assertTrue(contract.is_live)
        self.assertContractAgreement(contract, response_data, 0)

    @features_enabled([features.CONTRACTS])
    @hellosign_config
    def test_retrieve_contract_agreement(self):
        contract = ContractFactory(
            partner=self.vendor, initiated=True, template__with_agreement=True, template__agreement_template__signers=self.signers
        )
        hellosign_sign_current_signer(contract.agreement.sign_request)

        response_data = self.api_get(self.contract_api.format(contract.id))

        contract = Contract.objects.get(id=contract.id)
        self.assertEqual(len(response_data['agreements']), 1)
        self.assertContractAgreement(contract, response_data['agreements'][0], 3)

    @features_enabled([features.CONTRACTS])
    @hellosign_config
    def test_partner_rejects(self):
        contract = ContractFactory(
            partner=self.vendor, initiated=True, template__with_agreement=True, template__agreement_template__signers=self.signers
        )
        signer: ContractAgreementSigner = list(contract.agreement.signers.all())[0]

        completed_at = now()
        rejection_reason = "lorem ipsum"
        with freeze_time(completed_at), self.as_vendor(self.vendor):
            response_data = self.api_post(
                self.contract_agreement_signer_reject_vendor_api.format(contract.id, contract.agreement.id, signer.id),
                status=status.HTTP_200_OK,
                data=dict(rejection_reason=rejection_reason),
            )

        signer = ContractAgreementSigner.objects.get(id=signer.id)
        self.assertEqual(signer.status, ContractAgreementSignerStatus.REJECTED)
        self.assertSignerData(response_data, signer)

    @features_enabled([features.CONTRACTS])
    @hellosign_config
    def test_buyer_acceptance_reminder(self):
        contract = ContractFactory(
            partner=self.vendor, initiated=True, template__with_agreement=True, template__agreement_template__signers=self.signers
        )
        hellosign_sign_current_signer(contract.agreement.sign_request)
        signer = list(contract.agreement.signers.all())[1]

        with self.watch_event(AcceptContractReminder) as events:
            self.api_post(
                self.contract_agreement_signer_remind_api.format(contract.id, contract.agreement.id, signer.id), status=status.HTTP_200_OK
            )

        self.assertEqual(len(events), 1)
        self.assertEqual(events[0]['accepting_user'], signer.user)

    @features_enabled([features.CONTRACTS])
    @hellosign_config
    def test_partner_acceptance_reminder(self):
        contract = ContractFactory(
            partner=self.vendor, initiated=True, template__with_agreement=True, template__agreement_template__signers=self.signers
        )
        signer: ContractAgreementSigner = list(contract.agreement.signers.all())[0]

        with self.watch_event(AcceptContractReminder) as events:
            self.api_post(
                self.contract_agreement_signer_remind_api.format(contract.id, contract.agreement.id, signer.id), status=status.HTTP_200_OK
            )

        self.assertEqual(len(events), 1)
        self.assertEqual(events[0]['accepting_user'], signer.user)

    def assertContractAgreement(self, contract, agreement_data, signers_count):
        self.assertIsNotNone(contract.agreement)
        self.assertEqual(len(contract.agreements.all()), 1)
        signers = list(contract.agreement.signers.all())
        self.assertEqual(len(signers), signers_count)
        self.assertEqual(agreement_data['id'], contract.agreement.id)
        self.assertEqual(agreement_data['sign_request']['id'], contract.agreement.sign_request_id)
        self.assertEqual(len(agreement_data['signers']), signers_count)
        for signer_data, signer in zip(agreement_data['signers'], signers, strict=False):
            self.assertSignerData(signer_data, signer)

    def assertSignerData(self, signer_data, signer: ContractAgreementSigner):
        self.assertEqual(signer_data['status'], signer.status)
        self.assertEqual(
            signer_data['completed_at'], signer.completed_at.isoformat().replace("+00:00", "Z") if signer.completed_at else None
        )
        self.assertEqual(signer_data['rejection_reason'], signer.rejection_reason)
        self.assertEqual(signer_data['signer']['id'], signer.sign_request_signer.id)
        self.assertEqual(signer_data['signer']['type'], contract_signer_type(signer.sign_request_signer))
        self.assertEqual(signer_data['signer']['user'], signer.sign_request_signer.actor.user_id)
