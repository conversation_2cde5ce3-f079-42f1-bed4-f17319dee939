from dataclasses import asdict
from decimal import Decimal

from django.db import transaction

from contracts.invoicing.models import InvoiceIssuanceType
from contracts.models import Contract
from contracts.payments.events import trigger_contract_payment_created_event
from contracts.payments.invoice import (
    ContractPaymentData,
    ExpenseData,
    ExpenseItemData,
    create_contract_invoice,
)
from contracts.payments.services import extract_custom_fields_for_model
from contracts.projects.data import TaskData
from contracts.projects.services import list_contract_tasks
from contracts.services.errors import ContractError
from schedulers.api import run_schedule
from tasks.models import Task
from users.models import Actor

CREATE_CONTRACT_TASK_PAYMENT_FUNCTION = "contracts.invoicing.services.create_contract_task_payment"


def contract_task_payment_schedule_name(contract_id: int) -> str:
    return f"contract-task-payment-{contract_id}"


def list_contract_tasks_ready_for_payment(contract_id: int) -> list[TaskData]:
    tasks = list_contract_tasks(contract_id)
    if not tasks:
        return []

    return [task for task in tasks if is_contract_task_ready_for_payment(task)]


def is_contract_task_ready_for_payment(task: TaskData) -> bool:
    return task['status'] == Task.STATUS_COMPLETED and task['budget_total'] is not None


def should_create_contract_task_payment(contract: Contract) -> bool:
    if not contract.template.with_statement_of_work:
        return False

    if contract.invoicing_template.invoice_issuance_type != InvoiceIssuanceType.AUTOMATED_CUMULATIVE:
        return False

    return True


def run_create_contract_task_payment(contract_id: int):
    run_schedule(
        name=contract_task_payment_schedule_name(contract_id),
        execute_function=CREATE_CONTRACT_TASK_PAYMENT_FUNCTION,
        execute_params={'contract_id': contract_id},
        created_by=Actor.objects.system_actor,
    )


@transaction.atomic
def create_contract_task_payment(contract_id: int) -> dict:
    contract = Contract.objects.get(id=contract_id)
    tasks = list_contract_tasks_ready_for_payment(contract.id)
    if not tasks:
        raise ContractError("Contract tasks have not been completed yet")

    result = create_contract_payment_for_tasks(contract, tasks)
    return asdict(result)


def create_contract_payment_for_tasks(contract: Contract, tasks: list[TaskData]) -> ContractPaymentData | None:
    invoice_custom_fields_templates, invoice_custom_fields_data = extract_custom_fields_for_model(contract.invoicing_template, "Payment")
    line_item_custom_fields_templates, line_item_custom_fields_data = extract_custom_fields_for_model(
        contract.invoicing_template, "WorkItem"
    )

    line_items = []
    for task in tasks:
        line_items.append(
            ExpenseItemData(
                display_name=task['name'],
                category=contract.invoicing_template.category_id,
                quantity=1,
                unit_price=task['budget_total'],
                tax=contract.invoicing_template.tax_id,
                custom_fields_templates=line_item_custom_fields_templates,
                custom_fields=line_item_custom_fields_data,
            )
        )

    expense_data = ExpenseData(
        vendor=contract.partner_id,
        number=contract.invoicing_template.name,
        currency=contract.invoicing_template.currency,
        total_amount=Decimal(sum(line_item.unit_price for line_item in line_items)),
        custom_fields_templates=invoice_custom_fields_templates,
        custom_fields=invoice_custom_fields_data,
    )

    invoice_data = create_contract_invoice(
        contract.id,
        expense_data,
        line_items,
        approved=contract.invoicing_template.mark_as_approved,
        assigned_buyer=contract.invoicing_template.assigned_buyer,
    )

    contract_payment_data = ContractPaymentData(
        contract_id=contract.id,
        expense_data=expense_data,
        expense_items_data=line_items,
        approved=contract.invoicing_template.mark_as_approved,
        assigned_buyer=contract.invoicing_template.assigned_buyer_id,
        invoice_data=invoice_data,
    )
    transaction.on_commit(lambda: trigger_contract_payment_created_event(contract, asdict(contract_payment_data)))

    return contract_payment_data
