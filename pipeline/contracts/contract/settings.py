from contracts.budgets.services import create_or_update_contract_budget
from contracts.contract.audit import log_contract_internal_settings_changed
from contracts.invoicing.serializers import update_contract_invoicing_template_internal
from contracts.models.contract import Contract, ContractData
from contracts.payments.serializers import update_contract_payment_template_internal
from contracts.revisions.diff import flatten_dict_keys
from contracts.serializers.contract import ContractInternalUpdateSerializer

CONTRACT_INTERNAL_ATTRIBUTES = (
    "name",
    "payment_template.assigned_buyer",
    "payment_template.mark_as_approved",
    "invoicing_template.assigned_buyer",
    "invoicing_template.mark_as_approved",
    "budget.contracted_budget.allocated_amount",
    "custom_fields_data.internal_test_field",  # For testing purposes
)
"""
Contract internal attributes are the attributes that can be changed by an user without going through the change request process and does not require partner's acceptance.
Contract external attributes are the attributes that can be changed only by going through the change request process and require partner's acceptance.  
"""


def contract_data_contains_external_attributes(contract_data: ContractData) -> bool:
    updated_attributes = flatten_dict_keys(contract_data)
    return len(set(updated_attributes).difference(CONTRACT_INTERNAL_ATTRIBUTES)) > 0


def compare_contract_internal_attributes(contract: Contract, contract_data: ContractData) -> dict:
    changes = {}
    undefined = object()

    name = contract_data.get('name', undefined)
    if name is not undefined and name != contract.name:
        changes['name'] = name

    payment_template_assigned_buyer = contract_data.get('payment_template', {}).get('assigned_buyer', undefined)
    if payment_template_assigned_buyer is not undefined and payment_template_assigned_buyer != contract.payment_template.assigned_buyer:
        changes['payment_template.assigned_buyer'] = payment_template_assigned_buyer.full_name

    payment_template_mark_as_approved = contract_data.get('payment_template', {}).get('mark_as_approved', undefined)
    if (
        payment_template_mark_as_approved is not undefined
        and payment_template_mark_as_approved != contract.payment_template.mark_as_approved
    ):
        changes['payment_template.mark_as_approved'] = payment_template_mark_as_approved

    invoicing_template_assigned_buyer = contract_data.get('invoicing_template', {}).get('assigned_buyer', undefined)
    if (
        invoicing_template_assigned_buyer is not undefined
        and invoicing_template_assigned_buyer != contract.invoicing_template.assigned_buyer
    ):
        changes['invoicing_template.assigned_buyer'] = invoicing_template_assigned_buyer.full_name

    invoicing_template_mark_as_approved = contract_data.get('invoicing_template', {}).get('mark_as_approved', undefined)
    if (
        invoicing_template_mark_as_approved is not undefined
        and invoicing_template_mark_as_approved != contract.invoicing_template.mark_as_approved
    ):
        changes['invoicing_template.mark_as_approved'] = invoicing_template_mark_as_approved

    if 'budget' in contract_data:
        budget_contracted_budget_allocated_amount = contract_data.get('budget').contracted_budget.allocated_amount
        if (
            budget_contracted_budget_allocated_amount is not None
            and contract.budget.get_data().contracted_budget.allocated_amount != budget_contracted_budget_allocated_amount
        ):
            changes['budget.contracted_budget.allocated_amount'] = budget_contracted_budget_allocated_amount

    return changes


def update_contract_internal_attributes(contract: Contract, contract_data: ContractData) -> Contract:
    contract_serializer = ContractInternalUpdateSerializer(instance=contract, data=contract_data, partial=True)
    contract_serializer.is_valid(raise_exception=True)
    contract_validated_data = contract_serializer.validated_data.copy()

    changes = compare_contract_internal_attributes(contract, contract_validated_data)

    if contract_data.get('payment_template'):
        contract_validated_data['payment_template'] = update_contract_payment_template_internal(
            contract_data.get('payment_template'), contract.payment_template
        )

    if contract_data.get('invoicing_template'):
        contract_validated_data['invoicing_template'] = update_contract_invoicing_template_internal(
            contract_data.get('invoicing_template'), contract.invoicing_template
        )

    if contract_validated_data.get('budget'):
        contract_validated_data['budget_id'] = create_or_update_contract_budget(contract_validated_data.pop('budget'), contract.budget_id)

    if changes:
        log_contract_internal_settings_changed(contract, changes)

    return contract_serializer.update(contract, contract_validated_data)
