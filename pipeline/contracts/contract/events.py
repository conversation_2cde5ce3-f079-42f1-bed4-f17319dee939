from contracts.models import Contract
from contracts.models.contract import ContractCancellationReason, ContractStatus
from events.event_types import (
    ContractApproved,
    ContractCanceled,
    ContractCreated,
    ContractCreationFailed,
    ContractEnded,
    ContractEndingSoon,
    ContractOwnerChanged,
    ContractStatusChanged,
)


def trigger_contract_created_event(contract, *, has_external_changes=True):
    dont_send = ["AcceptContractEmail"] if contract.is_revision and not has_external_changes else []
    ContractCreated(contract=contract, dont_send=dont_send)


def trigger_contract_creation_failed_event(contract: Contract):
    ContractCreationFailed(contract=contract)


def trigger_contract_approved_event(contract):
    from contracts.services.contract import get_contract_recipients

    data = {"recipients": get_contract_recipients(contract, with_partner=True)}
    ContractApproved(contract=contract, data=data)


def trigger_contract_canceled_event(contract: Contract, *, cancellation_reason: ContractCancellationReason):
    dont_send = ["ContractCanceledEmail"] if not contract.visible_to_partner else []
    ContractCanceled(contract=contract, contract_cancellation_reason=cancellation_reason, dont_send=dont_send)


def trigger_contract_ended_event(contract: Contract):
    from contracts.services.contract import get_contract_recipients

    data = {"recipients": get_contract_recipients(contract)}
    ContractEnded(contract=contract, data=data)


def trigger_contract_ending_soon_event(contract: Contract):
    from contracts.services.contract import get_contract_recipients

    expiration_period = contract.get_expiration_period()
    period = expiration_period.format_expiration_period_duration_text() if expiration_period else None
    data = {"recipients": get_contract_recipients(contract), "period": period}
    ContractEndingSoon(contract=contract, data=data)


def trigger_contract_status_changed_event(contract: Contract, status: ContractStatus):
    ContractStatusChanged(contract=contract, contract_status=status)


def trigger_contract_owner_changed_event(contract: Contract, previous_owner: 'User'):
    ContractOwnerChanged(contract=contract, current_contract_owner=contract.created_by, previous_contract_owner=previous_owner)
