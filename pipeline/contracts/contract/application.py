from typing import TYPE_CHECKING

from django.core.exceptions import ValidationError
from django.db import transaction

from rest_framework import serializers

from contracts.budgets.services import create_or_update_contract_budget
from contracts.compliance.validators import validate_contract_compliance_data
from contracts.contract.events import trigger_contract_creation_failed_event
from contracts.contract.settings import contract_data_contains_external_attributes, update_contract_internal_attributes
from contracts.invoicing.serializers import create_or_update_contract_invoicing_template
from contracts.models import Contract
from contracts.models.contract import ContractData
from contracts.payments.serializers import create_or_update_contract_payment_template
from contracts.projects.serializers import create_or_update_contract_task_templates
from contracts.serializers.contract import ContractSerializer
from contracts.services.contract import create_contract_draft as create_contract_draft_service
from contracts.services.contract import initiate_contract, restart_contract
from contracts.services.permissions import check_contract_permission, contract_permission
from contracts.ten99.validators import validate_contract_ten99_insurance_data
from contracts.validators.contract import validate_can_update_contract
from preferences.tasks import recalculate_vendors_compliance
from shortlist.current_user import as_user

if TYPE_CHECKING:
    from users.models import User


@transaction.atomic
def create_contract(contract_data: ContractData, contract_creator: 'User') -> int:
    """
    Creates a contract and attempts to initiate it based on the provided contract data.

    Args:
        contract_data: Contract data used to create the contract
        contract_creator: User creating the contract

    Returns:
        int: ID of the created contract

    If contract data validation fails, creates a draft contract without validation and triggers a creation failed event.
    If validation succeeds, creates a validated draft contract and attempts to initiate it for non-fixed rate payment contracts.
    If initiation fails, the creation failed event is triggered and the contract remains in draft state.
    """

    with as_user(contract_creator):
        serializer = ContractSerializer(data=contract_data)
        if not serializer.is_valid():
            contract = create_contract_draft(contract_data, contract_creator, save_without_validation=True)
            transaction.on_commit(lambda: trigger_contract_creation_failed_event(contract))
            return contract.id

        contract = create_contract_draft(contract_data, contract_creator)
        if not contract.template.with_fixed_rate_payments:
            try:
                initiate_contract(contract)
            except ValidationError:
                transaction.on_commit(lambda: trigger_contract_creation_failed_event(contract))

        return contract.id


@transaction.atomic
def create_contract_draft(contract_data: ContractData, contract_creator: 'User', *, save_without_validation: bool = False) -> Contract:
    context = {'novalidate': save_without_validation}
    contract_serializer = ContractSerializer(data=contract_data, context=context)
    contract_serializer.is_valid(raise_exception=True)
    contract_validated_data = contract_serializer.validated_data.copy()

    contract_validated_data['created_by'] = contract_creator

    contract_template = contract_validated_data['template']
    if contract_template.with_compliance:
        result = validate_contract_compliance_data(contract_template, contract_validated_data)
        if result.is_invalid:
            raise serializers.ValidationError(result.to_dict())

    if contract_template.with_ten99_insurance:
        result = validate_contract_ten99_insurance_data(contract_template, contract_validated_data)
        if result.is_invalid:
            raise serializers.ValidationError(result.to_dict())

    if contract_data.get('payment_template'):
        contract_validated_data['payment_template'] = create_or_update_contract_payment_template(
            {
                **contract_data.get("payment_template"),
                'currency': contract_validated_data['payment_template'].get('currency'),
            },
            contract_validated_data.get('start_date'),
            contract_validated_data.get('end_date'),
            novalidate=save_without_validation,
        )

    if contract_data.get('invoicing_template'):
        contract_validated_data['invoicing_template'] = create_or_update_contract_invoicing_template(
            {
                **contract_data.get('invoicing_template'),
                'currency': contract_validated_data['invoicing_template'].get('currency'),
            },
        )

    task_templates_data: list[dict] | None = contract_validated_data.pop('task_templates', None)

    if contract_validated_data.get('budget'):
        contract_validated_data['budget_id'] = create_or_update_contract_budget(contract_validated_data.pop('budget'))

    contract = create_contract_draft_service(contract_validated_data)

    if task_templates_data:
        create_or_update_contract_task_templates(contract_data.get('task_templates'), contract)

    return contract


@transaction.atomic
def update_contract(
    contract: Contract, contract_data: ContractData, contract_editor: 'User', *, save_without_validation: bool = False
) -> Contract:
    validate_can_update_contract(contract).raise_if_invalid()

    is_updating_external_attributes = contract_data_contains_external_attributes(contract_data)
    if not contract.can_update_external_attributes and is_updating_external_attributes:
        raise serializers.ValidationError("Contract cannot be updated")

    contract.updated_by = contract_editor

    if contract.is_draft or is_updating_external_attributes:
        return _update_contract_draft_or_external(contract, contract_data, contract_editor, save_without_validation=save_without_validation)

    return update_contract_internal_attributes(contract, contract_data)


def _update_contract_draft_or_external(
    contract: Contract, contract_data: ContractData, contract_editor: 'User', *, save_without_validation: bool = False
):
    context = {'novalidate': save_without_validation}
    contract_serializer = ContractSerializer(instance=contract, data=contract_data, context=context)
    contract_serializer.is_valid(raise_exception=True)
    contract_validated_data = contract_serializer.validated_data.copy()

    check_contract_permission(
        contract_permission('edit', has_access_to_restricted_data=True),
        contract_editor,
        data=contract_validated_data,
        contract=contract,
    )

    if not contract.is_draft and "partner" in contract_validated_data and contract.partner != contract_validated_data["partner"]:
        raise serializers.ValidationError({'partner': ["Partner cannot be changed"]})

    if contract.template.with_compliance:
        result = validate_contract_compliance_data(contract.template, contract_validated_data, contract=contract)
        if result.is_invalid:
            raise serializers.ValidationError(result.to_dict())

    if contract.template.with_ten99_insurance:
        result = validate_contract_ten99_insurance_data(contract.template, contract_validated_data, contract=contract)
        if result.is_invalid:
            raise serializers.ValidationError(result.to_dict())

    if contract_data.get('payment_template'):
        contract_validated_data['payment_template'] = create_or_update_contract_payment_template(
            payment_template_data=contract_data.get('payment_template'),
            start_date=contract_validated_data.get('start_date', contract.start_date),
            end_date=contract_validated_data.get('end_date', contract.end_date),
            payment_template=contract.payment_template,
            novalidate=save_without_validation,
        )

    if contract_data.get('invoicing_template'):
        contract_validated_data['invoicing_template'] = create_or_update_contract_invoicing_template(
            invoicing_template_data={
                **contract_data.get('invoicing_template'),
                'currency': contract_validated_data['invoicing_template'].get('currency'),
            },
            invoicing_template=contract.invoicing_template,
        )

    task_templates_data: list[dict] | None = contract_validated_data.pop('task_templates', None)

    if contract_validated_data.get('budget'):
        contract_validated_data['budget_id'] = create_or_update_contract_budget(contract_validated_data.pop('budget'), contract.budget_id)

    previous_partner = contract.partner
    contract = contract_serializer.update(contract, contract_validated_data)

    if task_templates_data:
        create_or_update_contract_task_templates(contract_data.get('task_templates'), contract)

    if not contract.is_draft:
        restart_contract(contract)

    elif previous_partner != contract.partner:
        partners = []
        if previous_partner:
            partners.append(previous_partner.id)
        if contract.partner:
            partners.append(contract.partner.id)

        if partners:
            transaction.on_commit(lambda: recalculate_vendors_compliance.delay(vendor_ids=partners))

    return contract
