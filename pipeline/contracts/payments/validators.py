from typing import TYPE_CHECKING, Optional

from contracts.models.contract import ContractPeriod
from contracts.payments.models import ContractPaymentTemplate, PaymentAmountCalculation
from contracts.validators.base import VALIDATION_ERROR_INVALID, VALIDATION_ERROR_NOT_ALLOWED, VALIDATION_ERROR_REQUIRED, ValidationResult

if TYPE_CHECKING:
    from users.models import User


def validate_payment_template(payment_template: ContractPaymentTemplate, *, validate_assigned_buyer=True):
    from documents.models import PaymentTax, WorkItemCategory

    result = ValidationResult()

    if payment_template.contract.period == ContractPeriod.VARIABLE_DATES:
        result.add_error("Contract with variable dates can't contain payment template")

    if not payment_template.name:
        result.add_error({'name': [VALIDATION_ERROR_NOT_ALLOWED]})

    if payment_template.pay_period is None:
        result.add_error({'pay_period': [VALIDATION_ERROR_REQUIRED]})

    if not payment_template.amount:
        result.add_error({'amount': [VALIDATION_ERROR_REQUIRED]})

    if not payment_template.currency:
        result.add_error({'currency': [VALIDATION_ERROR_REQUIRED]})

    if not payment_template.tax and PaymentTax.objects.exists():
        result.add_error({'tax': [VALIDATION_ERROR_REQUIRED]})

    if not payment_template.category and WorkItemCategory.objects.exists():
        result.add_error({'category': [VALIDATION_ERROR_REQUIRED]})

    if not payment_template.first_pay_period_amount_calculation:
        result.add_error({'first_pay_period_amount_calculation': [VALIDATION_ERROR_REQUIRED]})

    if (
        payment_template.first_pay_period_amount_calculation
        and payment_template.first_pay_period_amount_calculation not in payment_template.first_pay_period_amount_calculations
    ):
        result.add_error({'first_pay_period_amount_calculation': [VALIDATION_ERROR_INVALID]})

    if (
        payment_template.first_pay_period_amount_calculation == PaymentAmountCalculation.CUSTOM_AMOUNT
        and payment_template.first_pay_period_custom_amount is None
    ):
        result.add_error({'first_pay_period_custom_amount': [VALIDATION_ERROR_REQUIRED]})

    if payment_template.last_pay_period_amount_calculations and payment_template.last_pay_period_amount_calculation is None:
        result.add_error({'last_pay_period_amount_calculation': [VALIDATION_ERROR_REQUIRED]})

    if (
        payment_template.last_pay_period_amount_calculations
        and payment_template.last_pay_period_amount_calculation
        and payment_template.last_pay_period_amount_calculation not in payment_template.last_pay_period_amount_calculations
    ):
        result.add_error({'last_pay_period_amount_calculation': [VALIDATION_ERROR_INVALID]})

    if (
        payment_template.last_pay_period_amount_calculation == PaymentAmountCalculation.CUSTOM_AMOUNT
        and payment_template.last_pay_period_custom_amount is None
    ):
        result.add_error({'last_pay_period_custom_amount': [VALIDATION_ERROR_REQUIRED]})

    if validate_assigned_buyer:
        result.add_error(validate_payment_assigned_buyer(payment_template.assigned_buyer))

    return result


def validate_payment_assigned_buyer(assigned_buyer: Optional['User']) -> ValidationResult:
    from contracts.payments.services import (
        can_approve_payments,
        payments_assigned_buyer_enabled,
    )

    result = ValidationResult()

    if payments_assigned_buyer_enabled():
        if not assigned_buyer:
            result.add_error(VALIDATION_ERROR_REQUIRED)
        elif not can_approve_payments(assigned_buyer):
            result.add_error(VALIDATION_ERROR_INVALID)
    elif assigned_buyer:
        result.add_error(VALIDATION_ERROR_NOT_ALLOWED)

    return result
