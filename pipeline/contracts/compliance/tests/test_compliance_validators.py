from datetime import date

from dateutil.relativedelta import relativedelta
from parameterized import parameterized

from django.utils.timezone import now

from contracts.compliance.models import ComplianceCheckLevel
from contracts.compliance.tests.factories import ContractComplianceTemplateDataFactory
from contracts.compliance.validators import (
    validate_contract_compliance_data,
    validate_contract_template_compliance_data,
)
from contracts.models.contract import ContractDuration
from contracts.models.template import CONTRACT_WORKFLOW_CATEGORY
from contracts.tests.factories import ContractFactory, ContractTemplateFactory
from contracts.validators.base import (
    VALIDATION_ERROR_INVALID,
    VALIDATION_ERROR_REQUIRED,
)
from onboarding.models import OnBoardingTemplateMixIn
from onboarding.tests.factories import OnBoardingWorkflowFactory
from shortlist.tests.helpers import TenantTestCase


class TestComplianceValidators(TenantTestCase):
    def test_validate_contract_template_compliance_agent_of_record(self):
        workflow = OnBoardingWorkflowFactory(
            category=CONTRACT_WORKFLOW_CATEGORY,
            stages=[
                {'stage_type': OnBoardingTemplateMixIn.STAGE_WORKER_CLASSIFICATION},
            ],
        )
        contract_template_data = ContractTemplateFactory.dict(
            workflow=workflow,
            is_end_date_mandatory=False,
            compliance_template=ContractComplianceTemplateDataFactory(compliance_check_level=ComplianceCheckLevel.AGENT_OF_RECORD),
        )

        result = validate_contract_template_compliance_data(contract_template_data)

        self.assertEqual(result.to_dict(), {'is_end_date_mandatory': [VALIDATION_ERROR_INVALID]})

    @parameterized.expand(
        [
            (now().date() + relativedelta(months=+3), {}),
            (None, {'end_date': [VALIDATION_ERROR_REQUIRED]}),
            (now().date() + relativedelta(months=+13), {'end_date': [VALIDATION_ERROR_INVALID]}),
        ]
    )
    def test_validate_contract_compliance_data_agent_of_record_fixed_dates(self, end_date: date | None, expected_errors: dict):
        contract_template = ContractTemplateFactory(
            with_compliance_assessment=True,
            compliance_template__compliance_check_level=ComplianceCheckLevel.AGENT_OF_RECORD,
        )
        contract_data = ContractFactory.dict(
            template=contract_template.id,
            start_date=now().date(),
            end_date=end_date,
        )

        result = validate_contract_compliance_data(contract_template, contract_data)

        self.assertEqual(result.to_dict(), expected_errors)

    @parameterized.expand(
        [
            (ContractDuration.ONE_MONTH, {}),
            (ContractDuration.THREE_MONTHS, {}),
            (ContractDuration.SIX_MONTHS, {}),
            (ContractDuration.ONE_YEAR, {}),
            (ContractDuration.TWO_YEARS, {'duration': [VALIDATION_ERROR_INVALID]}),
            (ContractDuration.INDEFINITE, {'duration': [VALIDATION_ERROR_INVALID]}),
            (ContractDuration.SPECIFIC_DATE, {'duration': [VALIDATION_ERROR_INVALID]}),
        ]
    )
    def test_validate_contract_compliance_data_agent_of_record_variable_dates(self, duration: ContractDuration, expected_errors: dict):
        contract_template = ContractTemplateFactory(
            with_compliance_assessment=True,
            compliance_template__compliance_check_level=ComplianceCheckLevel.AGENT_OF_RECORD,
        )
        contract_data = ContractFactory.dict(
            template=contract_template.id,
            with_variable_dates=True,
            duration=duration,
        )

        result = validate_contract_compliance_data(contract_template, contract_data)

        self.assertEqual(result.to_dict(), expected_errors)
