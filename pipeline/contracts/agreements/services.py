from django.db import transaction

from clients import features
from contracts.agreements.models import (
    ContractAgreement,
    ContractAgreementSigner,
    ContractAgreementSignerStatus,
)
from contracts.agreements.validators import can_reject
from contracts.models import Contract
from contracts.services.errors import ContractError
from contracts.validators.contract import validate_can_start_signing
from deals.exceptions import InvalidSignerConfiguration
from deals.models import SignedDocument, SignRequest, SignRequestSigner, Template
from users.models import Actor, User
from vendors.models import Vendor


class ContractAgreementError(ContractError):
    pass


class ContractSigningService:
    def initiate_acceptance(self, contract: Contract):
        if contract.template.agreement_choose_before_signing:
            return

        initiate_agreement_signing(contract, agreement_template=contract.template.agreement_template)

    def start_acceptance(self, contract: Contract, *, dont_send_vendor_email=False):
        validate_can_start_signing(contract).raise_if_invalid()

        contract.signing()

        try:
            contract_agreement = ContractAgreement.objects.get(contract_id=contract.id)
        except ContractAgreement.DoesNotExist:
            if contract.template.agreement_choose_before_signing:
                return

            raise

        start_agreement_signing(contract_agreement)

    def restart_acceptance(self, contract: Contract):
        self.reset_acceptance(contract)
        self.initiate_acceptance(contract)

    def reset_acceptance(self, contract: Contract):
        for contract_agreement in ContractAgreement.objects.filter(contract=contract).select_related("sign_request"):
            ContractAgreementSigner.objects.filter(agreement=contract_agreement).delete()
            contract_agreement.delete()
            if contract_agreement.sign_request:
                contract_agreement.sign_request.delete()

    def continue_acceptance(self, contract: Contract):
        contract.signing()

    def is_contract_accepted(self, contract: Contract):
        return not ContractAgreementSigner.objects.filter(
            agreement__contract=contract.id, status__in=[ContractAgreementSignerStatus.AWAITING, ContractAgreementSignerStatus.REJECTED]
        ).exists()

    def get_acceptance_users(self, contract: Contract):
        return {
            signer.user
            for signer in contract.agreement.signers.select_related(
                'sign_request_signer', 'sign_request_signer__actor', 'sign_request_signer__actor__user'
            )
            if signer.user.is_buyer
        }


def initiate_agreement_signing(
    contract: Contract,
    *,
    agreement_template: Template | None = None,
    sign_request: SignRequest | None = None,
    signed_document: SignedDocument | None = None,
) -> ContractAgreement:
    nonempty_arguments_count = len([arg for arg in [agreement_template, sign_request, signed_document] if arg])
    if nonempty_arguments_count > 1:
        raise ContractAgreementError("Agreement template, sign request and signed document cannot be provided at the same time")
    elif nonempty_arguments_count == 0:
        raise ContractAgreementError("Either agreement template, sign request or signed document must be provided")

    contract_agreement = ContractAgreement.objects.create(
        contract=contract,
        agreement_template=agreement_template,
        sign_request=(signed_document.sign_request if signed_document else sign_request),
    )

    if agreement_template:
        hellosign_enabled_for_vendor_type = (
            Vendor.objects.filter(id=contract.partner.id).get_vendors_with_feature(features.HELLOSIGN).exists()
        )
        if not hellosign_enabled_for_vendor_type:
            raise ContractAgreementError(
                f"Unable to initiate agreement signing for the contract agreement {contract_agreement.agreement_template.name}. "
                f"HelloSign feature is not enabled for vendor type {contract.partner.vendor_type.name}"
            )

        try:
            signers = agreement_template.validate_signers(custom_fields_object=contract)
        except InvalidSignerConfiguration as e:
            raise ContractAgreementError(
                f"Unable to initiate agreement signing for the contract agreement {contract_agreement.agreement_template.name}. "
                f"Invalid signer configuration: {e.message}"
            ) from e

        actors = []
        for signer in signers:
            if signer.get('actor') is None:
                actor = Actor.objects.for_user(contract.partner.first_contact)
            else:
                actor = signer.get('actor')

            actors.append(actor)

        for actor in actors:
            is_signer_duplicated = len([a for a in actors if a == actor]) > 1
            if is_signer_duplicated:
                raise ContractAgreementError(
                    f"Unable to initiate agreement signing for the contract agreement {contract_agreement.agreement_template.name}. "
                    f"User {actor.name} appears as a signer more than once."
                )

        ContractAgreementSigner.objects.bulk_create(
            [ContractAgreementSigner(agreement=contract_agreement, actor=actor) for actor in actors]
        )
    elif sign_request:
        sign_request.contract = contract
        sign_request.save(update_fields=['contract'])
        ContractAgreementSigner.objects.create(agreement=contract_agreement, actor=Actor.objects.for_user(contract.partner.first_contact))
    elif signed_document:
        signed_document.contract = contract
        signed_document.save(update_fields=['contract'])
        signed_document.sign_request.contract = contract
        signed_document.sign_request.save(update_fields=['contract'])

    return contract_agreement


def start_agreement_signing(contract_agreement: ContractAgreement):
    from contracts.services.contract import (
        approve_contract,
        make_contract_available_to_partner,
    )

    if contract_agreement.agreement_template_id:
        hellosign_enabled_for_vendor_type = (
            Vendor.objects.filter(id=contract_agreement.contract.partner.id).get_vendors_with_feature(features.HELLOSIGN).exists()
        )
        if not hellosign_enabled_for_vendor_type:
            raise ContractAgreementError(
                f"Unable to start agreement signing for the contract agreement {contract_agreement.agreement_template.name}. "
                f"HelloSign feature is not enabled for vendor type {contract_agreement.contract.partner.vendor_type.name}"
            )

        try:
            sign_requests = Template.objects.filter(pk=contract_agreement.agreement_template_id).create_sign_requests(
                vendors=[contract_agreement.contract.partner],
                requested_by=contract_agreement.contract.created_by,
                contract=contract_agreement.contract,
                wait_for_confirmation=contract_agreement.contract.template.agreement_preview_before_signing,
            )
        except InvalidSignerConfiguration as e:
            raise ContractAgreementError(
                f"Unable to create sign request for the contract agreement {contract_agreement.agreement_template.name}. "
                f"Invalid signer configuration: {e.message}"
            ) from e

        if not sign_requests:
            raise ContractAgreementError(
                f"Unable to create sign request for the contract agreement {contract_agreement.agreement_template.name}."
            )

        contract_agreement.sign_request = sign_requests[0]
        contract_agreement.save(update_fields=['sign_request'])

    for sign_request_signer in contract_agreement.sign_request.signers.all():
        try:
            signer = ContractAgreementSigner.objects.get(agreement=contract_agreement, actor=sign_request_signer.actor)
        except ContractAgreementSigner.MultipleObjectsReturned:
            raise ContractAgreementError(
                f"Unable to start agreement signing for the contract agreement {contract_agreement.agreement_template.name}. "
                f"User {sign_request_signer.actor.name} appears as a signer more than once."
            )

        signer.sign_request_signer = sign_request_signer
        signer.save(update_fields=['sign_request_signer'])

    make_contract_available_to_partner(contract_agreement.contract)

    if contract_agreement.sign_request.status == SignRequest.STATUS_DONE and contract_agreement.sign_request.signed_document_id:
        approve_contract(contract_agreement.contract)


@transaction.atomic
def accept_agreement(sign_request: SignRequest, sign_request_signer: SignRequestSigner):
    from contracts.agreements.events import trigger_contract_accepted_event
    from contracts.services.contract import approve_contract, continue_acceptance

    try:
        signer = ContractAgreementSigner.objects.select_related('agreement', 'agreement__contract').get(
            agreement__sign_request=sign_request, sign_request_signer=sign_request_signer
        )
    except ContractAgreementSigner.DoesNotExist:
        return

    signer.accept()
    if signer.agreement.contract.is_rejected:
        continue_acceptance(signer.agreement.contract)

    transaction.on_commit(lambda: trigger_contract_accepted_event(signer))

    if ContractSigningService().is_contract_accepted(signer.agreement.contract):
        approve_contract(contract=signer.agreement.contract)


@transaction.atomic
def reject_agreement(signer: ContractAgreementSigner, user: User, rejection_reason: str | None = None):
    from contracts.agreements.events import trigger_contract_rejected_event
    from contracts.services.contract import reject_contract

    if not can_reject(signer, user):
        raise ContractAgreementError("User can't reject the contract")

    signer.reject(rejection_reason)

    reject_contract(signer.agreement.contract)
    transaction.on_commit(lambda: trigger_contract_rejected_event(signer))


@transaction.atomic
def update_agreement_signing(contract_agreement: ContractAgreement):
    from contracts.agreements.events import trigger_contract_accepted_event
    from contracts.services.contract import approve_contract

    if not contract_agreement.contract.is_signing:
        raise ContractAgreementError("Contract is not in signing state")

    awaiting_signers = contract_agreement.signers.filter(status=ContractAgreementSignerStatus.AWAITING)
    for signer in awaiting_signers:
        if signer.sign_request_signer.signed_at:
            signer.accept()
            transaction.on_commit(lambda s=signer: trigger_contract_accepted_event(s))

    has_pending_signers = ContractAgreementSigner.objects.filter(
        agreement_id=contract_agreement.id, status__in=[ContractAgreementSignerStatus.AWAITING, ContractAgreementSignerStatus.REJECTED]
    ).exists()
    if not has_pending_signers:
        approve_contract(contract=contract_agreement.contract)


def get_contract_agreement_by_sign_request(sign_request: SignRequest) -> ContractAgreement | None:
    try:
        return ContractAgreement.objects.select_related('contract', 'sign_request').get(sign_request=sign_request)
    except ContractAgreement.DoesNotExist:
        return None
