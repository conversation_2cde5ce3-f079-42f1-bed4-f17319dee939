# Generated by Django 3.2.19 on 2023-11-14 13:08

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('onboarding', '0031_alter_informationstageconfiguration_type'),
        ('contracts', '0025_auto_20231113_1301'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='contractautomation',
            name='execute_function_data',
        ),
        migrations.AlterField(
            model_name='contracttemplate',
            name='enable_ending_soon_status',
            field=models.BooleanField(blank=True, default=True, null=True),
        ),
        migrations.CreateModel(
            name='ContractAutomationWorkflow',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('automation', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='+', to='contracts.contractautomation')),
                ('contract', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='automation_workflows', to='contracts.contract')),
                ('vendor_workflow', models.OneToOneField(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='onboarding.onboardingworkflowsforvendor')),
            ],
        ),
    ]
