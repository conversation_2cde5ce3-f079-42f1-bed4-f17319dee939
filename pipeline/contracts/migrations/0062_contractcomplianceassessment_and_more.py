# Generated by Django 4.2.16 on 2025-01-13 12:41

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('compliance', '0004_questionnaireresponse_last_synced_at'),
        ('contracts', '0061_migrate_payment_template_mark_as_approved'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContractComplianceAssessment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('compliance_assessment_id', models.CharField(max_length=255)),
                (
                    'compliance_outcome_result',
                    models.CharField(
                        blank=True,
                        choices=[('low_risk', 'Low Risk'), ('high_risk', 'High Risk'), ('inconclusive', 'Inconclusive')],
                        max_length=255,
                        null=True,
                    ),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.AddField(
            model_name='contract',
            name='compliance_assessment_outcome_result',
            field=models.CharField(
                blank=True,
                choices=[('low_risk', 'Low Risk'), ('high_risk', 'High Risk'), ('inconclusive', 'Inconclusive')],
                max_length=255,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name='contracttemplate',
            name='compliance_check_level',
            field=models.CharField(
                blank=True, choices=[('agent_of_record', 'Agent Of Record'), ('classify', 'Classify')], max_length=255, null=True
            ),
        ),
        migrations.AddIndex(
            model_name='contract',
            index=models.Index(fields=['compliance_assessment_outcome_result'], name='contracts_c_complia_8b9527_idx'),
        ),
        migrations.AddField(
            model_name='contractcomplianceassessment',
            name='compliance_questionnaire_response',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='compliance.questionnaireresponse'),
        ),
        migrations.AddField(
            model_name='contractcomplianceassessment',
            name='contract',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, related_name='compliance_assessments', to='contracts.contract'
            ),
        ),
        migrations.AddField(
            model_name='contract',
            name='compliance_assessment',
            field=models.ForeignKey(
                null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='contracts.contractcomplianceassessment'
            ),
        ),
        migrations.AddConstraint(
            model_name='contractcomplianceassessment',
            constraint=models.UniqueConstraint(
                fields=('contract', 'compliance_assessment_id'), name='unique_contract_compliance_assessment_id'
            ),
        ),
    ]
