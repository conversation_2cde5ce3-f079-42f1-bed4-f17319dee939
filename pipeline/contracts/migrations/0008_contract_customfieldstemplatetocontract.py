# Generated by Django 3.2.18 on 2023-03-28 14:34

from django.db import migrations, models
import django.db.models.deletion
import shortlist.db_fields


class Migration(migrations.Migration):

    dependencies = [
        ('preferences', '0070_customfield_source_field'),
        ('vendors', '0034_remove_vendoringroupdata_custom_fields_data_v2'),
        ('contracts', '0007_auto_20230322_0807'),
    ]

    operations = [
        migrations.CreateModel(
            name='Contract',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('custom_fields_data', shortlist.db_fields.JSONField(blank=True, decoder=shortlist.db_fields.PipelineJSONDecoder, encoder=shortlist.db_fields.PipelineJSONEncoder, null=True)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('start_date', models.DateField(null=True)),
                ('end_date', models.Date<PERSON>ield(null=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('under_review', 'Under Review'), ('signing', 'Signing'), ('accepting', 'Accepting'), ('rejected', 'Rejected'), ('scheduled', 'Scheduled'), ('live', 'Live'), ('ended', 'Ended')], default='draft', max_length=255)),
                ('ending_soon', models.BooleanField(default=False)),
                ('archived', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('partner', models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='+', to='vendors.vendor')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='contracts', to='contracts.contracttemplate')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CustomFieldsTemplateToContract',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('contract', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='raw_custom_fields_templates', to='contracts.contract')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='raw_contracts', to='preferences.customfieldstemplate')),
            ],
        ),
    ]
