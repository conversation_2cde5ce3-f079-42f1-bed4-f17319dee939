# Generated by Django 3.2.19 on 2023-05-18 20:22

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('contracts', '0011_contract_workflow'),
    ]

    operations = [
        migrations.CreateModel(
            name='ContractAcceptance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('awaiting', 'Awaiting'), ('accepted', 'Accepted'), ('rejected', 'Rejected')], default='awaiting', max_length=255)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('rejection_reason', models.CharField(blank=True, max_length=1000, null=True)),
                ('order', models.PositiveSmallIntegerField(default=0)),
                ('contract', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='acceptances', to='contracts.contract')),
                ('signer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='acceptances', to='contracts.contractsigner')),
            ],
            options={
                'ordering': ('order',),
                'unique_together': {('contract', 'signer')},
            },
        ),
    ]
