from django.apps import AppConfig


class ContractsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'contracts'

    def ready(self) -> None:
        # import packages to attach watchers to platform events
        import contracts.agreements.events  # noqa: F401 isort: skip
        import contracts.compliance.events  # noqa: F401 isort: skip
        import contracts.contract.audit  # noqa: F401 isort: skip
        import contracts.payments.audit  # noqa: F401 isort: skip
        import contracts.projects.events  # noqa: F401 isort: skip
        import contracts.ten99.audit  # noqa: F401 isort: skip
        import contracts.ten99.events  # noqa: F401 isort: skip

        # register contract actions
        import contracts.bulk_actions.contract_actions  # noqa: F401 isort: skip
        import contracts.bulk_actions.create_contract  # noqa: F401 isort: skip
