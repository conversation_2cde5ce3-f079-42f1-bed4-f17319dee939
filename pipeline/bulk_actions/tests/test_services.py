from unittest import mock
from unittest.mock import MagicMock

from bulk_actions.data import Action<PERSON>tatus, BulkActionResult, ObjectActionResult
from bulk_actions.models import AsyncActionStatus, BulkAction
from bulk_actions.services import (
    create_bulk_action,
    execute_action_for_objects,
    execute_bulk_action,
    load_bulk_action_module,
    run_bulk_action,
)
from bulk_actions.tests.factories import (
    TEST_ACTION_NAME,
    BulkActionCreateDataFactory,
    TestActionArgumentsFactory,
    TestBulkActionModule,
    TestModel,
    TestModelFactory,
    test_object_action,
)
from events.models import EventNotification
from shortlist.current_user import get_current_user
from shortlist.tests.helpers import TenantTestCase


class BulkActionServiceTestCase(TenantTestCase):
    def test_load_bulk_action_module(self):
        bulk_action_module = load_bulk_action_module(TEST_ACTION_NAME)

        self.assertIsInstance(bulk_action_module, TestBulkActionModule)

    def test_create_bulk_action(self):
        bulk_action_create_data = BulkActionCreateDataFactory(bulk_action_data__with_object_ids=True)

        bulk_action = create_bulk_action(bulk_action_create_data)

        self.assertEqual(bulk_action.name, TEST_ACTION_NAME)
        self.assertEqual(bulk_action.arguments, bulk_action_create_data.bulk_action_data.action.arguments)
        self.assertIsNone(bulk_action.object_search)
        self.assertEqual(bulk_action.object_ids, bulk_action_create_data.bulk_action_data.object_ids)
        self.assertEqual(bulk_action.status, AsyncActionStatus.WAITING)
        self.assertIsNotNone(bulk_action.created_at)
        self.assertIsNotNone(bulk_action.created_by)
        self.assertEqual(bulk_action.created_by, get_current_user())
        self.assertIsNone(bulk_action.started_at)
        self.assertIsNone(bulk_action.finished_at)

    def test_run_bulk_action(self):
        bulk_action = create_bulk_action(
            BulkActionCreateDataFactory(bulk_action_data__with_object_ids=True, bulk_action_data__object_ids=[1, 2, 3])
        )

        run_bulk_action(bulk_action)

        bulk_action = BulkAction.objects.get(id=bulk_action.id)
        self.assertEqual(bulk_action.status, AsyncActionStatus.COMPLETED)
        self.assertIsNotNone(bulk_action.task_id)
        self.assertIsNotNone(bulk_action.started_at)
        self.assertIsNotNone(bulk_action.finished_at)
        self.assertEqual(
            bulk_action.result,
            {
                'results': [
                    {'object_id': 1, 'result': 1, 'status': 'success', 'errors': None},
                    {'object_id': 2, 'result': 4, 'status': 'success', 'errors': None},
                    {'object_id': 3, 'result': 9, 'status': 'success', 'errors': None},
                ],
            },
        )

    def test_execute_bulk_action_for_object_ids(self):
        bulk_action = create_bulk_action(
            BulkActionCreateDataFactory(bulk_action_data__with_object_ids=True, bulk_action_data__object_ids=[1, 2, 3])
        )

        execute_bulk_action(bulk_action.id)

        bulk_action = BulkAction.objects.get(id=bulk_action.id)
        self.assertEqual(bulk_action.status, AsyncActionStatus.COMPLETED)
        self.assertIsNotNone(bulk_action.started_at)
        self.assertIsNotNone(bulk_action.finished_at)
        self.assertEqual(
            bulk_action.result,
            {
                'results': [
                    {'object_id': 1, 'result': 1, 'status': 'success', 'errors': None},
                    {'object_id': 2, 'result': 4, 'status': 'success', 'errors': None},
                    {'object_id': 3, 'result': 9, 'status': 'success', 'errors': None},
                ],
            },
        )

    def test_execute_bulk_action_for_object_search(self):
        bulk_action = create_bulk_action(
            BulkActionCreateDataFactory(
                bulk_action_data__with_object_search=True, bulk_action_data__object_search__filters=[{'id': [1, 2, 3]}]
            )
        )

        execute_bulk_action(bulk_action.id)

        bulk_action = BulkAction.objects.get(id=bulk_action.id)
        self.assertEqual(bulk_action.status, AsyncActionStatus.COMPLETED)
        self.assertIsNotNone(bulk_action.started_at)
        self.assertIsNotNone(bulk_action.finished_at)
        self.assertEqual(
            bulk_action.result,
            {
                'results': [
                    {'object_id': 1, 'result': 1, 'status': 'success', 'errors': None},
                    {'object_id': 2, 'result': 4, 'status': 'success', 'errors': None},
                    {'object_id': 3, 'result': 9, 'status': 'success', 'errors': None},
                ],
            },
        )

    def test_execute_bulk_action_completed_with_errors(self):
        bulk_action = create_bulk_action(
            BulkActionCreateDataFactory(
                bulk_action_data__with_object_ids=True,
                bulk_action_data__object_ids=[1, 2, 3, 4, 5],
                bulk_action_data__action__arguments={'faulted_ids': [1, 3, 5]},
            )
        )

        execute_bulk_action(bulk_action.id)

        bulk_action = BulkAction.objects.get(id=bulk_action.id)
        self.assertEqual(bulk_action.status, AsyncActionStatus.COMPLETED)
        self.assertIsNotNone(bulk_action.started_at)
        self.assertIsNotNone(bulk_action.finished_at)
        self.assertEqual(
            bulk_action.result,
            {
                'results': [
                    {'object_id': 1, 'result': None, 'status': 'error', 'errors': ["Failed by test case"]},
                    {'object_id': 2, 'result': 4, 'status': 'success', 'errors': None},
                    {'object_id': 3, 'result': None, 'status': 'error', 'errors': ["Failed by test case"]},
                    {'object_id': 4, 'result': 16, 'status': 'success', 'errors': None},
                    {'object_id': 5, 'result': None, 'status': 'error', 'errors': ["Failed by test case"]},
                ],
            },
        )

    @mock.patch("bulk_actions.tests.factories.TestBulkActionModule.execute_action")
    def test_execute_bulk_action_failed(self, execute_action: MagicMock):
        execute_action.side_effect = Exception("Failed by test case")

        bulk_action = create_bulk_action(
            BulkActionCreateDataFactory(bulk_action_data__with_object_ids=True, bulk_action_data__object_ids=[1, 2, 3])
        )

        execute_bulk_action(bulk_action.id)

        bulk_action = BulkAction.objects.get(id=bulk_action.id)
        self.assertEqual(bulk_action.status, AsyncActionStatus.FAILED)
        self.assertIsNotNone(bulk_action.started_at)
        self.assertIsNotNone(bulk_action.finished_at)
        self.assertIn("Failed by test case", bulk_action.error)

    def test_execute_action_for_objects(self):
        objects = TestModelFactory.create_batch(size=5)

        result = execute_action_for_objects(
            object_ids=[obj.id for obj in objects],
            object_qs=TestModel.objects.all(),
            action_function=test_object_action,
            action_arguments=TestActionArgumentsFactory(),
        )

        self.assertEqual(
            result,
            BulkActionResult(
                results=[
                    ObjectActionResult(object_id=1, status=ActionStatus.SUCCESS, result=None, errors=None),
                    ObjectActionResult(object_id=2, status=ActionStatus.SUCCESS, result=None, errors=None),
                    ObjectActionResult(object_id=3, status=ActionStatus.SUCCESS, result=None, errors=None),
                    ObjectActionResult(object_id=4, status=ActionStatus.SUCCESS, result=None, errors=None),
                    ObjectActionResult(object_id=5, status=ActionStatus.SUCCESS, result=None, errors=None),
                ]
            ),
        )

    def test_bulk_action_completed_event_notification(self):
        bulk_action = create_bulk_action(
            BulkActionCreateDataFactory(bulk_action_data__with_object_ids=True, bulk_action_data__object_ids=[1, 2, 3])
        )

        execute_bulk_action(bulk_action.id)

        bulk_action = BulkAction.objects.get(id=bulk_action.id)
        event_notification: EventNotification = EventNotification.objects.last()
        bulk_action_notification = "Test action bulk action has been finished"
        self.assertEqual(
            event_notification.params,
            {
                'bulk_action_data_id': bulk_action.id,
                'bulk_action_notification': bulk_action_notification,
            },
        )
        self.assertEqual(event_notification.message, bulk_action_notification)

    @mock.patch("bulk_actions.tests.factories.TestBulkActionModule.execute_action")
    def test_bulk_action_failed_event_notification(self, execute_action: MagicMock):
        execute_action.side_effect = Exception("Failed by test case")

        bulk_action = create_bulk_action(
            BulkActionCreateDataFactory(bulk_action_data__with_object_ids=True, bulk_action_data__object_ids=[1, 2, 3])
        )

        execute_bulk_action(bulk_action.id)

        bulk_action = BulkAction.objects.get(id=bulk_action.id)
        event_notification: EventNotification = EventNotification.objects.last()
        bulk_action_notification = "Test action bulk action has failed - not all selected items have been processed"
        self.assertEqual(
            event_notification.params,
            {
                'bulk_action_data_id': bulk_action.id,
                'bulk_action_notification': bulk_action_notification,
            },
        )
        self.assertEqual(event_notification.message, bulk_action_notification)
