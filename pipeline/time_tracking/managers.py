from django.db import models

from tasks.mixins import TaskPermissionMixin


class TimeEntryManager(TaskPermissionMixin, models.Manager):
    def for_user(self, user):
        if not user or not user.is_authenticated:
            return self.none()
        if user.is_vendor:
            qs = self.filter(vendor=user.vendor)
        else:
            qs = self.tasks_visible_to_user(user, "view", prefix="task__").exclude(task__isnull=True)
        qs = (
            qs.exclude(deleted_at__isnull=False)
            .select_related(
                "task",
                "task__vendor",
                "task__task_group",
                "vendor",
                "timesheet_period",
            )
            .prefetch_related(
                "task__timesheets_approvers",  # needed for permission checks to reduce queries
                "task__timeentry_set",  # needed to compute task time spent already (get_task_time_spent)
            )
            .order_by("-start_time", "-id")
        )
        return qs
