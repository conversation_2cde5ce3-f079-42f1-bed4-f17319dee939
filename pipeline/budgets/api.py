import budgets.data
import budgets.models
from budgets.configuration.api import (
    BudgetConfigurationRequest,
    create_budget_configuration,
    update_budget_configuration,
)
from budgets.configuration.data import BudgetConfiguration
from budgets.result import Result


def create_budget(configuration: BudgetConfigurationRequest | BudgetConfiguration | dict) -> Result[budgets.data.Budget]:
    result = create_budget_configuration(configuration)
    if result.is_failure:
        return Result.failure(errors=result.errors)

    budget = budgets.models.Budget.objects.get(id=result.value.id)
    return Result(value=budget.get_data())


def get_budget(budget_id: int) -> Result[budgets.data.Budget]:
    return Result(value=budgets.models.Budget.objects.get(id=budget_id).get_data())


def update_budget(budget_id: int, configuration: BudgetConfigurationRequest | BudgetConfiguration | dict) -> Result[budgets.data.Budget]:
    result = update_budget_configuration(budget_id, configuration)
    if result.is_failure:
        return Result.failure(errors=result.errors)

    budget = budgets.models.Budget.objects.get(id=result.value.id)
    return Result(value=budget.get_data())


def delete_budget(budget_id: int) -> Result:
    budgets.models.Budget.objects.filter(id=budget_id).delete()
    return Result.success()
