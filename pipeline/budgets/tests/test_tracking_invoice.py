from decimal import Decimal

from budgets.errors import (
    BudgetConfigurationInvalid,
    BudgetDoesNotExist,
    BudgetExceeded,
    BudgetSpendingAmountInvalid,
    ExpenseCategoryDoesNotExist,
    ExpenseCategoryInvalid,
    ExpenseCategoryRequired,
    ExpenseReimbursementAllowanceExceeded,
    InvoiceInvalid,
    ReimbursementAllowanceExceeded,
    ValueInvalid,
)
from budgets.tests.factories import invoice_factory
from budgets.tracking.audit.data import InvoiceSpending
from budgets.tracking.data import ExpenseSpending
from budgets.tracking.invoice import (
    compute_invoice_spending,
    compute_invoices_total_spending,
    prepare_invoice_errors,
)
from documents.tests.factories import WorkItemCategoryFactory
from shortlist.tests.helpers import TenantTestCase


class InvoiceTrackingTestCase(TenantTestCase):
    def test_compute_invoice_spending(self):
        work_category = WorkItemCategoryFactory(is_work=True)
        expense_category = WorkItemCategoryFactory()

        invoice = invoice_factory(
            items=[
                {
                    'unit_price': Decimal(100),
                    'category_id': work_category.id,
                },
                {
                    'unit_price': Decimal(200),
                    'category_id': work_category.id,
                },
                {
                    'unit_price': Decimal(300),
                    'category_id': expense_category.id,
                },
                {
                    'unit_price': Decimal(400),
                    'category_id': expense_category.id,
                },
            ],
        )
        invoice_spending = compute_invoice_spending(invoice)

        self.assertEqual(invoice_spending.amount, Decimal(1000))
        self.assertEqual(invoice_spending.currency, 'USD')
        self.assertEqual(invoice_spending.expenses_total_amount, Decimal(1000))
        self.assertEqual(len(invoice_spending.expenses), 2)
        work_category_spending = next(
            (expense for expense in invoice_spending.expenses if expense.expense_category == work_category.id), None
        )
        self.assertIsNotNone(work_category_spending)
        self.assertEqual(work_category_spending.amount, Decimal(300))
        expense_category_spending = next(
            (expense for expense in invoice_spending.expenses if expense.expense_category == expense_category.id), None
        )
        self.assertIsNotNone(expense_category_spending)
        self.assertEqual(expense_category_spending.amount, Decimal(700))

    def test_compute_invoices_total_spending(self):
        invoices_spending = [
            InvoiceSpending(
                invoice_id=1,
                amount=Decimal(300),
                currency='USD',
                expenses=[
                    ExpenseSpending(expense_category=1, amount=Decimal(100)),
                    ExpenseSpending(expense_category=2, amount=Decimal(200)),
                ],
            ),
            InvoiceSpending(
                invoice_id=2,
                amount=Decimal(700),
                currency='USD',
                expenses=[
                    ExpenseSpending(expense_category=1, amount=Decimal(300)),
                    ExpenseSpending(expense_category=2, amount=Decimal(400)),
                ],
            ),
        ]

        invoices_total_spending = compute_invoices_total_spending(invoices_spending, 'USD')

        self.assertEqual(invoices_total_spending.amount, Decimal(1000))
        self.assertEqual(invoices_total_spending.currency, 'USD')
        self.assertEqual(invoices_total_spending.expenses_total_amount, Decimal(1000))
        self.assertEqual(len(invoices_total_spending.expenses), 2)
        work_category_spending = next((expense for expense in invoices_total_spending.expenses if expense.expense_category == 1), None)
        self.assertIsNotNone(work_category_spending)
        self.assertEqual(work_category_spending.amount, Decimal(400))
        expense_category_spending = next((expense for expense in invoices_total_spending.expenses if expense.expense_category == 2), None)
        self.assertIsNotNone(expense_category_spending)
        self.assertEqual(expense_category_spending.amount, Decimal(600))

    def test_prepare_invoice_errors(self):
        invoice_errors = [
            ValueInvalid(),
            BudgetConfigurationInvalid(errors={'field1': 'error1', 'field2': 'error2'}),
            BudgetSpendingAmountInvalid(),
            BudgetDoesNotExist(budget_id=1),
            ExpenseCategoryDoesNotExist(),
            ExpenseCategoryInvalid(expense_category=2),
            ExpenseCategoryRequired(),
            BudgetExceeded(amount_exceeded=Decimal(100), currency='USD'),
            ReimbursementAllowanceExceeded(amount_exceeded=Decimal(100), currency='USD'),
            ExpenseReimbursementAllowanceExceeded(expense_category=3, amount_exceeded=Decimal(200), currency='USD'),
            InvoiceInvalid(errors={'field3': 'error3', 'field4': 'error4'}),
        ]

        errors = prepare_invoice_errors(
            budget_errors=[
                BudgetExceeded(amount_exceeded=Decimal(1000), currency='USD'),
                ReimbursementAllowanceExceeded(amount_exceeded=Decimal(1000), currency='USD'),
            ],
            invoice_errors=invoice_errors,
        )

        self.assertEqual(len(errors), 11)
        self.assertIn(BudgetExceeded(amount_exceeded=Decimal(1000), currency='USD'), errors)
        self.assertNotIn(BudgetExceeded(amount_exceeded=Decimal(100), currency='USD'), errors)
        self.assertIn(ReimbursementAllowanceExceeded(amount_exceeded=Decimal(1000), currency='USD'), errors)
        self.assertNotIn(ReimbursementAllowanceExceeded(amount_exceeded=Decimal(100), currency='USD'), errors)
