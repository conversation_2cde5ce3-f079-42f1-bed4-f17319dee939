from decimal import Decimal
from unittest import TestCase

from budgets.errors import (
    BudgetConfigurationInvalid,
    BudgetDoesNotExist,
    BudgetExceeded,
    BudgetSpendingAmountInvalid,
    ExpenseCategoryDoesNotExist,
    ExpenseCategoryInvalid,
    ExpenseCategoryRequired,
    ExpenseReimbursementAllowanceExceeded,
    InvoiceInvalid,
    ReimbursementAllowanceExceeded,
    ValueInvalid,
)
from budgets.result import Result


class ResultTestCase(TestCase):
    def setUp(self):
        super().setUp()
        self.errors = [
            ValueInvalid(),
            BudgetConfigurationInvalid(errors={'field1': 'error1', 'field2': 'error2'}),
            BudgetSpendingAmountInvalid(),
            BudgetDoesNotExist(budget_id=1),
            ExpenseCategoryDoesNotExist(),
            ExpenseCategoryInvalid(expense_category=2),
            ExpenseCategoryRequired(),
            BudgetExceeded(amount_exceeded=Decimal(100), currency='USD'),
            ReimbursementAllowanceExceeded(amount_exceeded=Decimal(100), currency='USD'),
            ExpenseReimbursementAllowanceExceeded(expense_category=3, amount_exceeded=Decimal(200), currency='USD'),
            InvoiceInvalid(errors={'field3': 'error3', 'field4': 'error4'}),
        ]

    def test_errors_dump(self):
        result = Result(errors=self.errors)

        errors = result.errors_dump()
        self.assertEqual(
            errors,
            [
                {'type': 'value_invalid'},
                {'type': 'budget_configuration_invalid', 'errors': {'field1': 'error1', 'field2': 'error2'}},
                {'type': 'budget_spending_amount_invalid'},
                {'type': 'budget_does_not_exist', 'budget_id': 1},
                {'type': 'expense_category_does_not_exist'},
                {'type': 'expense_category_invalid', 'expense_category': 2},
                {'type': 'expense_category_required'},
                {'type': 'budget_exceeded', 'amount_exceeded': Decimal('100'), 'currency': 'USD'},
                {'type': 'reimbursement_allowance_exceeded', 'amount_exceeded': Decimal('100'), 'currency': 'USD'},
                {
                    'type': 'expense_reimbursement_allowance_exceeded',
                    'expense_category': 3,
                    'amount_exceeded': Decimal('200'),
                    'currency': 'USD',
                },
                {'type': 'invoice_invalid', 'errors': {'field3': 'error3', 'field4': 'error4'}},
            ],
        )

    def test_remove_duplicated_errors(self):
        result = Result.remove_duplicated_errors(self.errors + self.errors)

        self.assertEqual(len(result), 11)
        for error in self.errors:
            self.assertIn(error, result)
