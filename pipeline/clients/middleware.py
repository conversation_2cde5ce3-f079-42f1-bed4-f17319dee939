import json

from django.conf import settings
from django.db import connection
from django.http import HttpResponseNotFound, HttpResponsePermanentRedirect, HttpResponse
from django.utils.deprecation import MiddlewareMixin
from rest_framework import status

from .models import Client


def get_tenant(**kwargs):
    return Client.tenants.filter(**kwargs).first()


class TenantMiddleware(MiddlewareMixin):
    """
    This middleware should be placed at the very top of the middleware stack.
    Selects the proper database schema using the request host. Can fail in
    various ways which is better than corrupting or revealing data...
    """

    def process_request(self, request):
        """
        Resets to public schema

        Some nasty weird bugs happened at the production environment without this call.
        connection.pg_thread.schema_name would already be set and then terrible errors
        would occur. Any idea why? My theory is django implements connection as some sort
        of threading local variable.
        """
        connection.set_schema_to_public()
        host = request.get_host().split(":")[0]
        tenant = None

        if host:
            if host == settings.PUBLIC_SCHEMA_DOMAIN:
                # temporary - redirect empty domain prefix to admin.<domain>
                return HttpResponsePermanentRedirect(f"{settings.PROTOCOL_FOR_LINKS}admin.{settings.PUBLIC_SCHEMA_DOMAIN}{request.path}")

            hostname_prefix, root_domain = None, host
            if "." in host:
                hostname_prefix, root_domain = host.split('.', 1)

            if hostname_prefix in settings.GLOBAL_HOSTNAMES:
                special_config = settings.GLOBAL_HOSTNAMES.get(hostname_prefix)
                if special_config:
                    request.urlconf = special_config
                    tenant = Client.get_public()

            if tenant is None and root_domain == settings.PUBLIC_SCHEMA_DOMAIN:
                tenant = get_tenant(domain_prefix=hostname_prefix)

            if tenant is None:
                if settings.FORCED_TENANT_PREFIX:
                    tenant = get_tenant(domain_prefix=settings.FORCED_TENANT_PREFIX)
                else:
                    # handle custom domains
                    tenant = get_tenant(custom_url=host)
                    if tenant is None:
                        tenant = get_tenant(client_domains__domain=host)

            if tenant is None:
                tenant = get_tenant(redirect_from_host=host)
                if tenant is not None:
                    if request.path == "/api/tenant/":
                        response = HttpResponse(json.dumps(dict(moved=tenant.root_url.rstrip("/"))))
                        response["Content-type"] = "application/json"
                    else:
                        response = HttpResponsePermanentRedirect(tenant.root_url + request.path.lstrip("/"))
                    response["Access-Control-Allow-Origin"] = settings.PROTOCOL_FOR_LINKS + host
                    return response

            if tenant is not None:
                request.tenant = tenant
                connection.set_tenant(tenant)

                if not tenant.public:
                    tenant.update_last_active()
                return

        return HttpResponseNotFound()


class TenantLockMiddleware(MiddlewareMixin):
    """
    Used for tenant locking for everyone except staff users.
    """

    excluded_paths = {"/api/tools/translations", "/api/tenant/", "/admin", "/api/callbacks/", "/api/users/login/"}

    def process_request(self, request):
        for path in self.excluded_paths:
            if request.path.startswith(path):
                return
        if connection.tenant and request.user is not None and not getattr(request.user, "is_staff", False):
            lock_status = connection.tenant.lock_status
            if lock_status is not None:
                return HttpResponse(json.dumps(dict(message=lock_status.message)), status=status.HTTP_402_PAYMENT_REQUIRED)
