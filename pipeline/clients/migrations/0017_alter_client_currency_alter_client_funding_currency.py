# Generated by Django 4.2.16 on 2025-03-07 07:36

from django.db import migrations, models
import shortlist.currencies


class Migration(migrations.Migration):

    dependencies = [
        ("clients", "0016_client_allow_staff_access"),
    ]

    operations = [
        migrations.AlterField(
            model_name="client",
            name="currency",
            field=models.CharField(
                choices=[
                    ("AED", "AED"),
                    ("ARS", "ARS"),
                    ("AUD", "AUD"),
                    ("BRL", "BRL"),
                    ("BYN", "BYN"),
                    ("CAD", "CAD"),
                    ("CHF", "CHF"),
                    ("CLP", "CLP"),
                    ("CNY", "CNY"),
                    ("CRC", "CRC"),
                    ("CZK", "CZK"),
                    ("DKK", "DKK"),
                    ("EUR", "EUR"),
                    ("GBP", "GBP"),
                    ("HKD", "HKD"),
                    ("HUF", "HUF"),
                    ("IDR", "IDR"),
                    ("ILS", "ILS"),
                    ("INR", "INR"),
                    ("JPY", "JPY"),
                    ("KRW", "KRW"),
                    ("MXN", "MXN"),
                    ("MYR", "MYR"),
                    ("NGN", "NGN"),
                    ("NOK", "NOK"),
                    ("NZD", "NZD"),
                    ("PAB", "PAB"),
                    ("PHP", "PHP"),
                    ("PKR", "PKR"),
                    ("PLN", "PLN"),
                    ("QAR", "QAR"),
                    ("RSD", "RSD"),
                    ("RUB", "RUB"),
                    ("SAR", "SAR"),
                    ("SEK", "SEK"),
                    ("SGD", "SGD"),
                    ("THB", "THB"),
                    ("TRY", "TRY"),
                    ("TWD", "TWD"),
                    ("USD", "USD"),
                    ("UYU", "UYU"),
                    ("VND", "VND"),
                    ("ZAR", "ZAR"),
                ],
                default="USD",
                max_length=3,
                validators=[shortlist.currencies.validate_currency],
                verbose_name="Currency",
            ),
        ),
        migrations.AlterField(
            model_name="client",
            name="funding_currency",
            field=models.CharField(
                blank=True,
                choices=[
                    ("AED", "AED"),
                    ("ARS", "ARS"),
                    ("AUD", "AUD"),
                    ("BRL", "BRL"),
                    ("BYN", "BYN"),
                    ("CAD", "CAD"),
                    ("CHF", "CHF"),
                    ("CLP", "CLP"),
                    ("CNY", "CNY"),
                    ("CRC", "CRC"),
                    ("CZK", "CZK"),
                    ("DKK", "DKK"),
                    ("EUR", "EUR"),
                    ("GBP", "GBP"),
                    ("HKD", "HKD"),
                    ("HUF", "HUF"),
                    ("IDR", "IDR"),
                    ("ILS", "ILS"),
                    ("INR", "INR"),
                    ("JPY", "JPY"),
                    ("KRW", "KRW"),
                    ("MXN", "MXN"),
                    ("MYR", "MYR"),
                    ("NGN", "NGN"),
                    ("NOK", "NOK"),
                    ("NZD", "NZD"),
                    ("PAB", "PAB"),
                    ("PHP", "PHP"),
                    ("PKR", "PKR"),
                    ("PLN", "PLN"),
                    ("QAR", "QAR"),
                    ("RSD", "RSD"),
                    ("RUB", "RUB"),
                    ("SAR", "SAR"),
                    ("SEK", "SEK"),
                    ("SGD", "SGD"),
                    ("THB", "THB"),
                    ("TRY", "TRY"),
                    ("TWD", "TWD"),
                    ("USD", "USD"),
                    ("UYU", "UYU"),
                    ("VND", "VND"),
                    ("ZAR", "ZAR"),
                ],
                max_length=3,
                null=True,
                validators=[shortlist.currencies.validate_currency],
                verbose_name="Funding currency",
            ),
        ),
    ]
