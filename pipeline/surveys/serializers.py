from rest_framework import serializers

from shortlist.serializers import DynamicSerializerFieldsMixin

from surveys import models
from projects.serializers import ProjectVendorSerializer


class FormTemplateSerializer(DynamicSerializerFieldsMixin, serializers.ModelSerializer):
    contents = serializers.JSONField(required=False)

    class Meta:
        model = models.FormTemplate
        fields = ('id', 'name', 'description', 'contents', 'question_count', 'imported')


class ProjectFormSerializer(serializers.ModelSerializer):
    contents = serializers.JSONField(required=False)

    class Meta:
        model = models.ProjectForm
        fields = ('id', 'contents')


class ProjectFormResponseSerializer(serializers.ModelSerializer):
    data = serializers.JSONField(required=False)

    class Meta:
        model = models.ProjectFormResponse
        read_only_fields = ('created_at', 'updated_at')
        fields = ('data', 'final', 'created_at', 'updated_at')


class ProjectFormCompareSerializer(serializers.ModelSerializer):
    vendor = ProjectVendorSerializer()
    proposal_amount = serializers.ReadOnly<PERSON>ield(source='vendor.proposal_amount')
    data = serializers.JSONField(required=False)

    class Meta:
        model = models.ProjectFormResponse
        fields = ('data', 'created_at', 'updated_at', 'vendor', 'proposal_amount')
