"""
API overview:

Buyer POV:

/api/insights/top_partners?sort=-spend                    -- top partners by spend
/api/insights/top_partners?sort=-projects                 -- top partners by projects won
/api/insights/winning_partners                            -- winning percentages for partners
/api/insights/spend_by_tags                               -- sum of awarded values grouped by tag
/api/insights/performing_to_budget                        -- compare budgets to awarded proposal

.csv at URL end or format=csv in the query params will return data in CSV format


Parameters common to all reports:
?date_from=YYYY-MM-DD&&date_to=YYYY-MM-DD                - custom awarded range
?tags=Name                                               - filter to projects having specific tags (comma separated)
?sort=field1&sort=field2,..                              - sort results (use -field for descending sort)
?currency=X                                              - use only projects with specified currency(ies)
?limit=n                                                 - return only n first results
?exclude=field1&exclude=field2,..                        - remove some columns

?range=month                                             - award date in this month
?range=quarter                                           - award date in this quarter
?range=year                                              - award date in this year
?range=month-1                                           - award date in previous month
?range=quarter-1                                         - award date in previous quarter
?range=year-1                                            - award date in previous year
"""

from decimal import Decimal
import logging

from django.db.models import Sum, Count, Avg, Q, <PERSON><PERSON><PERSON><PERSON><PERSON>, Float<PERSON>ield
from django.db.models.expressions import Func, <PERSON>, When, Value, F
from django.db.models.functions import Coalesce
from django.utils.timezone import now
from rest_framework import views, status
from rest_framework.response import Response

from reviews.models import Review
from shortlist.nulls_last import NullsLastQuerySet
from shortlist.permissions import Permission
from projects.models import Project, ProjectVendors
from vendors.models import Vendor
from vendors.serializers.vendor_serializers import VendorLocationSerializer
from .date_ranges import django_range, RANGE_FUNCTIONS
from shortlist.utils import HeaderedList

insights_logger = logging.getLogger('pipeline.insights')


def invalid_range_handler(range_):
    msg = f"Invalid range '{range_}'"
    insights_logger.exception(msg)
    raise InvalidParams(msg)


class InvalidParams(Exception):
    pass


class InsightView(views.APIView):
    permission_classes = [Permission.insights()]
    header = None
    sort_fields = {}
    fields_to_rename = {}
    serializers = {}
    statuses = (Project.STATUS_AWARDED, Project.STATUS_CLOSED, Project.STATUS_COMPLETED)

    def filtered_projects(self):
        result = Project.active.select_related('published_version').prefetch_related(None).filter(status__in=self.statuses)
        range = self.query_param('range')
        budget_from = self.query_param('budget_from')
        budget_to = self.query_param('budget_to')
        proposal_from = self.query_param('proposal_from')
        proposal_to = self.query_param('proposal_to')
        date_from = self.query_param('date_from')
        date_to = self.query_param('date_to')
        tags = self.query_params('tag')
        currencies = self.query_params('currency')

        if range:
            try:
                range, _, offset = range.partition('-')
                offset = int(offset, 10) if offset else 0
                filter_params = django_range('awarded_at', RANGE_FUNCTIONS[range](now(), offset))
                result = result.filter(**filter_params)
            except Exception:
                invalid_range_handler(range)
        if date_from is not None:
            result = result.filter(awarded_at__gte=date_from)
        if date_to is not None:
            result = result.filter(awarded_at__lte=date_to)
        if tags:
            result = result.filter(raw_tags__name__in=tags)
        if currencies:
            result = result.filter(published_version__currency__in=currencies)
        return result

    def query_params(self, name):
        return self.request.query_params.getlist(name, ())

    def query_param(self, name):
        return self.request.query_params.get(name, None)

    def apply_sort(self, data):
        fields = []
        for name in self.query_params('sort'):
            try:
                if name.startswith('-'):
                    field = '-' + self.sort_fields[name[1:]]
                else:
                    field = self.sort_fields[name]
            except KeyError:
                raise InvalidParams(f"Invalid sort field {name}")
            fields.append(field)
        if hasattr(data, 'order_by'):
            data = data.order_by(*fields)
        else:
            # TODO: sorting non-querysets
            pass
        return data

    def apply_limit(self, data):
        limit = self.query_param('limit')
        page = self.query_param('page')
        if limit:
            try:
                limit = int(limit, 10)
            except Exception:
                raise InvalidParams(f"Invalid limit '{limit}'")
            if page:
                try:
                    page = int(page, 10)
                except:
                    raise InvalidParams(f"Invalid page '{page}'")
                data = data[limit * (page - 1) : limit * page]
            else:
                data = data[:limit]
        return data

    def data_rows(self, projects):
        raise NotImplementedError()

    def apply_serializers(self, data):
        data = data[:]
        if self.serializers:
            for (object_id, target_name), (model, serializer) in self.serializers.items():
                objects = {object.id: object for object in model.objects.filter(id__in=(row[object_id] for row in data))}
                for row in data:
                    row[target_name] = serializer(instance=objects.get(row[object_id])).data
        return data

    def get(self, request, *args, **kwargs):
        try:
            data = self.data_rows(self.filtered_projects())
            data = self.apply_sort(data)
            data = self.apply_limit(data)
            data = self.apply_serializers(data)
            data = self.rename_fields(data)

        except InvalidParams as e:
            return Response(e.args, status=status.HTTP_400_BAD_REQUEST)
        return Response(HeaderedList(data, self.header))

    def rename_fields(self, rows):
        fields = (
            set(self.header)
            .union(target_name for object_id, target_name in self.serializers.keys())
            .difference(self.query_params('exclude'))
        )
        result = []
        for row in rows:
            row = dict(row)
            for k, v in self.fields_to_rename.items():
                row[v] = row.pop(k, None)
            new_row = {k: (int(v) if isinstance(v, Decimal) else v) for k, v in row.items() if k in fields}
            result.append(new_row)
        return result


class WinningPartners(InsightView):
    header = ["partner", "awarded_count", "awarded_sum", "accepted", "won_percent"]
    fields_to_rename = {
        'vendor__name': 'partner',
        'accepted_count': 'accepted',
    }
    sort_fields = {
        'partner': 'vendor__name',
        'awarded_count': 'awarded_count',
        'awarded_sum': 'awarded_sum',
        'accepted': 'accepted_count',
        'won_percent': 'won_percent',
    }

    awarded_count_sql = Sum(Case(When(awarded=True, then=1), default=0), output_field=IntegerField())
    awarded_sum_sql = Sum(Case(When(awarded=True, then=Coalesce('proposal_amount', 0)), default=0, output_field=IntegerField()))
    won_percent_sql = Value(100) * F('awarded_count') / F('accepted_count')

    serializers = {('vendor__id', 'vendor'): (Vendor, VendorLocationSerializer)}

    def data_rows(self, projects):
        project_ids = list(projects.values_list('id', flat=True))
        if project_ids:
            qs = ProjectVendors.objects.select_related(None).filter(accepted=True, project_id__in=project_ids)
            qs = (
                qs.order_by('vendor__name')
                .values('vendor__name', 'vendor__id')
                .annotate(
                    accepted_count=Count('id'),
                    awarded_count=self.awarded_count_sql,
                    awarded_sum=self.awarded_sum_sql,
                    won_percent=self.won_percent_sql,
                )
            )
            return qs
        return []


class VendorScores(InsightView):
    header = ["partner", "score_average", "score_quality", "score_delivery", "score_value_for_money", "satisfaction_ratio", "review_count"]

    fields_to_rename = {
        'name': 'partner',
    }

    serializers = {('id', 'vendor'): (Vendor, VendorLocationSerializer)}

    sort_fields = {
        'score_average': 'score_average',
        'score_quality': 'score_quality',
        'vendor': 'name',
        'score_delivery': 'score_delivery',
        'score_value_for_money': 'score_value_for_money',
        'satisfaction_ratio': 'satisfaction_ratio',
        'review_count': 'review_count',
    }

    satisfaction_ratio_sql = Avg(
        Case(When(reviews__project_id__isnull=True, then=None), When(reviews__recommended=True, then=1), default=0),
        output_field=FloatField(),
    )

    score_average_sql = Avg(
        (
            Coalesce('reviews__score_quality', Decimal(0))
            + Coalesce('reviews__score_delivery', Decimal(0))
            + Coalesce('reviews__score_value_for_money', Decimal(0))
        )
        / Func(
            Case(When(reviews__score_quality__isnull=True, then=0), default=1)
            + Case(When(reviews__score_delivery__isnull=True, then=0), default=1)
            + Case(When(reviews__score_value_for_money__isnull=True, then=0), default=1),
            0,
            function='NULLIF',
        )
    )

    def get_reviews(self):
        skills = self.query_params('skill')
        range = self.query_param('range')
        date_from = self.query_param('date_from')
        date_to = self.query_param('date_to')

        reviews = Review.objects.all()

        if skills:
            reviews = reviews.filter(raw_tags__tag__in=skills)
        if range:
            try:
                range, _, offset = range.partition('-')
                offset = int(offset, 10) if offset else 0
                filter_params = django_range('created', RANGE_FUNCTIONS[range](now(), offset))
                reviews = reviews.filter(**filter_params)
            except Exception:
                invalid_range_handler(range)
        if date_from is not None:
            reviews = reviews.filter(created__gte=date_from)
        if date_to is not None:
            reviews = reviews.filter(created__lte=date_to)
        return reviews

    def data_rows(self, projects):
        groups = [g for g in self.query_params('group') if g]
        qs = NullsLastQuerySet(Vendor).filter(reviews__in=self.get_reviews())
        if groups:
            qs = qs.filter(groups__id__in=groups)

        qs = (
            qs.order_by('id')
            .values('id', 'name')
            .annotate(
                review_count=Count('reviews__pk'),
                score_average=self.score_average_sql,
                score_quality=Avg('reviews__score_quality'),
                score_delivery=Avg('reviews__score_delivery'),
                score_value_for_money=Avg('reviews__score_value_for_money'),
                satisfaction_ratio=self.satisfaction_ratio_sql,
            )
        )
        return qs


class VendorProjectsMixin:
    PROJECT_STATUS_OPEN = (Project.STATUS_OPEN, Project.STATUS_EVALUATING, Project.STATUS_CLOSED)

    base_queryset = (
        ProjectVendors.objects.select_related('project__status')
        .exclude(project__status__in=[Project.STATUS_COMPLETED, Project.STATUS_DRAFT])
        .exclude(project_status=ProjectVendors.STATUS_REJECTED)
        .exclude(project__archived=True)
        .exclude(project__deleted=True)
    )

    @staticmethod
    def project_status_count(statuses):
        return Sum(
            Case(
                When(project__project_flag=Project.STATUS_AWARDED, awarded=True, then=0),
                When(project__status__in=statuses, then=1),
                default=0,
            )
        )

    @staticmethod
    def project_ongoing_count():
        return Sum(Case(When(project__project_flag=Project.STATUS_AWARDED, awarded=True, then=1), default=0))


class VendorProjects(InsightView, VendorProjectsMixin):
    header = ['partner', 'open_count', 'ongoing_count', 'total_count']
    serializers = {
        ('vendor__id', 'vendor'): (Vendor, VendorLocationSerializer),
    }
    fields_to_rename = {
        'vendor__name': 'partner',
    }
    sort_fields = {
        'open_count': 'open_count',
        'ongoing_count': 'ongoing_count',
        'total_count': 'total_count',
        'vendor': 'vendor__name',
    }

    def data_rows(self, projects):
        return (
            self.base_queryset.order_by('vendor__name')
            .values('vendor__id', 'vendor__name')
            .annotate(
                total_count=Count('id'),
                ongoing_count=VendorProjectsMixin.project_ongoing_count(),
                open_count=VendorProjectsMixin.project_status_count(self.PROJECT_STATUS_OPEN),
            )
        )


class VendorProjectsStats(views.APIView, VendorProjectsMixin):
    permission_classes = [Permission.insights()]

    def get(self, request, *args, **kwargs):
        stats = dict(
            ongoing_count=(
                self.base_queryset.filter(Q(project__project_flag=Project.STATUS_AWARDED) & Q(awarded=True)).aggregate(
                    count=Count('vendor', distinct=True)
                )
            )['count'],
            open_count=(
                self.base_queryset.exclude(Q(project__project_flag=Project.STATUS_AWARDED) & Q(awarded=True))
                .filter(Q(project__status__in=self.PROJECT_STATUS_OPEN))
                .aggregate(count=Count('vendor', distinct=True))
            )['count'],
            total_count=self.base_queryset.aggregate(count=Count('vendor', distinct=True))['count'],
        )

        return Response(stats)
