# Generated by Django 3.2.17 on 2023-05-30 11:31

from django.db import migrations, models
import django.db.models.deletion
import shortlist.current_user
import shortlist.db_fields


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0035_auto_20230113_0848'),
        ('schedulers', '0003_auto_20220111_1342'),
    ]

    operations = [
        migrations.CreateModel(
            name='Schedule',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.TextField(blank=True)),
                ('execute_date', models.DateTimeField()),
                ('start_date', models.DateTimeField(blank=True, null=True)),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('freq', models.CharField(blank=True, choices=[(3, 'daily'), (2, 'weekly'), (1, 'monthly'), (0, 'yearly')], max_length=255, null=True)),
                ('rrule_json', shortlist.db_fields.JSONField(blank=True, decoder=shortlist.db_fields.PipelineJSONDecoder, encoder=shortlist.db_fields.PipelineJSONEncoder, null=True)),
                ('params', shortlist.db_fields.JSONField(blank=True, decoder=shortlist.db_fields.PipelineJSONDecoder, encoder=shortlist.db_fields.PipelineJSONEncoder, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('active', models.BooleanField(default=True)),
                ('created_by', models.ForeignKey(default=shortlist.current_user.get_actor_for_current_user, on_delete=django.db.models.deletion.RESTRICT, related_name='+', to='users.actor')),
                ('updated_by', models.ForeignKey(default=shortlist.current_user.get_actor_for_current_user, on_delete=django.db.models.deletion.RESTRICT, related_name='+', to='users.actor')),
            ],
        ),
        migrations.CreateModel(
            name='HistoryRun',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('executed_at', models.DateTimeField()),
                ('scheduler_for', models.DateTimeField()),
                ('finished_at', models.DateTimeField(blank=True, null=True)),
                ('success', models.BooleanField(default=False)),
                ('result', models.TextField(blank=True)),
                ('messages', models.TextField()),
                ('schedule', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='schedulers.schedule')),
            ],
            options={
                'ordering': ['-executed_at'],
            },
        ),
    ]
