from __future__ import annotations

import logging
import traceback
from datetime import date, datetime, UTC
from importlib import import_module
from typing import Any

from django.db import transaction
from django.utils.timezone import now

from schedulers.models import Schedule, ScheduleError, ScheduleRun
from schedulers.scheduling.recurrence_rule import RecurrenceRule
from shortlist.celery import app
from users.models import Actor

logger = logging.getLogger(__name__)

MAX_SCHEDULE_RUN_RETRIES = 6
SCHEDULE_RUN_RETRY_DELAY = 3600  # in seconds


def create_recurring_schedule(
    start_date: date | datetime,
    recurrence_rule: RecurrenceRule,
    execute_function: Any,
    execute_params: Any,
    created_by: Actor,
    end_date: date | datetime = None,
    name: str | None = None,
) -> Schedule:
    schedule = Schedule(
        name=name,
        start_date=coerce_start_date(start_date),
        end_date=coerce_end_date(end_date) if end_date else None,
        created_by=created_by,
        updated_by=created_by,
        params=dict(execute_function=execute_function, execute_params=execute_params),
        recurrence_rule=recurrence_rule,
        run_id=1,
    )
    schedule.execute_date = schedule.get_first_execution_date()
    schedule.save()
    return schedule


def update_recurring_schedule(
    schedule: Schedule,
    recurrence_rule: RecurrenceRule | None = None,
    start_date: date | datetime | None = None,
    end_date: date | datetime | None = None,
    execute_function: Any | None = None,
    execute_params: Any | None = None,
    updated_by: Actor | None = None,
):
    if start_date is not None:
        schedule.start_date = coerce_start_date(start_date)

    if end_date is not None:
        schedule.end_date = coerce_end_date(end_date)

    if recurrence_rule is not None:
        schedule.update_recurrence_rule(recurrence_rule)

    if execute_function is not None or execute_params is not None:
        schedule.update_params(execute_function, execute_params)

    if updated_by:
        schedule.updated_by = updated_by

    schedule.save()


def create_one_time_schedule(
    start_date: date | datetime, execute_function: Any, execute_params: Any, created_by: Actor, name: str | None = None
) -> Schedule:
    start_date = coerce_start_date(start_date)
    schedule = Schedule.objects.create(
        name=name,
        start_date=start_date,
        execute_date=start_date,
        created_by=created_by,
        updated_by=created_by,
        params=dict(execute_function=execute_function, execute_params=execute_params),
        run_id=1,
    )
    return schedule


def update_one_time_schedule(
    schedule: Schedule,
    execute_date: date | datetime | None = None,
    execute_function: Any | None = None,
    execute_params: Any | None = None,
    updated_by: Actor | None = None,
):
    if execute_date is not None:
        schedule.update_execute_date(coerce_start_date(execute_date))

    if execute_function is not None or execute_params is not None:
        schedule.update_params(execute_function, execute_params)

    if updated_by:
        schedule.updated_by = updated_by

    schedule.activate_schedule()
    schedule.save()


@app.task()
def run_schedule(schedule_id: int, *, force_running: bool = False):
    try:
        with transaction.atomic():
            schedule = Schedule.objects.select_for_update(nowait=True).get(id=schedule_id)
            check_can_run_schedule(schedule, force_running=force_running)
            schedule.run()
    except Exception as ex:
        logger.exception(str(ex), extra={'error': repr(ex)})
        raise ex

    history = ScheduleRun.objects.create(schedule=schedule, run_id=schedule.run_id, executed_at=now(), scheduled_for=schedule.execute_date)
    try:
        result = execute_schedule(schedule)
    except Exception as ex:
        logger.exception(str(ex), extra={'error': repr(ex)})
        history.finish(False, {'stacktrace': traceback.format_exc(), 'error': repr(ex)})
        if ScheduleRun.objects.filter(schedule=schedule.id, run_id=schedule.run_id, success=False).count() >= MAX_SCHEDULE_RUN_RETRIES:
            schedule.deactivate()
        else:
            schedule.retry(SCHEDULE_RUN_RETRY_DELAY)
    else:
        history.finish(True, result)
        schedule.move_schedule()
    finally:
        schedule.finish()


def check_can_run_schedule(schedule: Schedule, *, force_running: bool = False):
    if not force_running and schedule.running:
        raise ScheduleError(f"Schedule {schedule.id} '{schedule.name}' is already running")

    if not schedule.active:
        raise ScheduleError(f"Schedule {schedule.id} '{schedule.name}' is inactive")

    if not force_running and ScheduleRun.objects.filter(schedule=schedule.id, run_id=schedule.run_id, success=True).exists():
        raise ScheduleError(f"Schedule {schedule.id} '{schedule.name}' has already run successfully")


def execute_schedule(schedule: Schedule) -> Any:
    function_path = schedule.params['execute_function']
    params = schedule.params.get('execute_params')

    module_path, function_name = function_path.rsplit('.', 1)
    module = import_module(module_path)
    func = getattr(module, function_name)
    if params:
        return func(**params)
    else:
        return func()


def coerce_start_date(start_date: date | datetime) -> datetime:
    if isinstance(start_date, datetime):
        return start_date

    return datetime(
        start_date.year,
        start_date.month,
        start_date.day,
        datetime.min.hour,
        datetime.min.minute,
        datetime.min.second,
        tzinfo=UTC,
    )


def coerce_end_date(end_date: date | datetime) -> datetime:
    if isinstance(end_date, datetime):
        return end_date

    return datetime(
        end_date.year,
        end_date.month,
        end_date.day,
        datetime.max.hour,
        datetime.max.minute,
        datetime.max.second,
        tzinfo=UTC,
    )
