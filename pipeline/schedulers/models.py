from __future__ import annotations

import logging
from dataclasses import asdict, is_dataclass
from datetime import datetime, timedelta, UTC
from typing import Any

from dateutil.relativedelta import relativedelta
from pydantic import BaseModel

from django.core.serializers.json import DjangoJSONEncoder
from django.db import models
from django.utils.timezone import now

from schedulers.scheduling.recurrence_rule import RecurrenceRule
from shortlist.current_user import get_actor_for_current_user
from shortlist.db_fields import <PERSON><PERSON><PERSON><PERSON>
from shortlist.utils import make_choices

logger = logging.getLogger(__name__)


class Scheduler(models.Model):
    INTERVAL_ONE_OFF = 'one_off'
    INTERVAL_DAY = 'day'
    INTERVAL_WEEK = 'week'
    INTERVAL_MONTH = 'month'
    INTERVAL_YEAR = 'year'
    INTERVAL_FIRST_DAY_OF_MONTH = 'first_day_of_month'
    INTERVAL_LAST_DAY_OF_MONTH = 'last_day_of_month'
    INTERVAL_TYPES = make_choices(
        INTERVAL_ONE_OFF,
        INTERVAL_DAY,
        INTERVAL_WEEK,
        INTERVAL_MONTH,
        INTERVAL_YEAR,
        INTERVAL_FIRST_DAY_OF_MONTH,
        INTERVAL_LAST_DAY_OF_MONTH,
    )

    JOB_OPENINGS_TYPE = 'jobopening'
    SCHED_TYPES = make_choices(
        JOB_OPENINGS_TYPE,
    )

    sched_type = models.CharField(choices=SCHED_TYPES, max_length=255)
    launch_date = models.DateField()
    interval_type = models.CharField(choices=INTERVAL_TYPES, default=INTERVAL_ONE_OFF, max_length=255)
    interval_number = models.IntegerField(blank=True, null=True)

    created_by = models.ForeignKey('users.Actor', default=get_actor_for_current_user, related_name='+', on_delete=models.CASCADE)
    updated_by = models.ForeignKey('users.Actor', default=get_actor_for_current_user, related_name='+', on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        if self.interval_type in (Scheduler.INTERVAL_ONE_OFF, Scheduler.INTERVAL_FIRST_DAY_OF_MONTH, Scheduler.INTERVAL_LAST_DAY_OF_MONTH):
            self.interval_number = None
            if self.interval_type == Scheduler.INTERVAL_FIRST_DAY_OF_MONTH and self.launch_date.day != 1:
                interval = relativedelta(months=+1, day=1)
                self.launch_date = self.launch_date + interval
            elif self.interval_type == Scheduler.INTERVAL_LAST_DAY_OF_MONTH:
                self.launch_date = self.launch_date + relativedelta(day=31)
        super().save(*args, **kwargs)

    @property
    def scheduled_for(self):
        return {'scheduled_for': self.launch_date, 'interval_type': self.interval_type, 'interval_number': self.interval_number}

    def move_schedule(self):
        if self.interval_type == self.INTERVAL_ONE_OFF:
            self.delete_me_after = True
        else:
            if self.interval_type == self.INTERVAL_FIRST_DAY_OF_MONTH:
                interval = relativedelta(months=+1, day=1)
            elif self.interval_type == self.INTERVAL_LAST_DAY_OF_MONTH:
                interval = relativedelta(months=+1, day=31)
            elif self.interval_number:
                interval = timedelta(days=+self.interval_number)
                if self.interval_type == self.INTERVAL_WEEK:
                    interval = timedelta(weeks=+self.interval_number)
                elif self.interval_type == self.INTERVAL_MONTH:
                    interval = relativedelta(months=+self.interval_number)
                elif self.interval_type == self.INTERVAL_YEAR:
                    interval = relativedelta(years=+self.interval_number)
            else:
                raise Exception('Incorrect scheduler')
            self.launch_date = self.launch_date + interval
            self.save()

    def create_history_run(self):
        h = SchedulerHistory.objects.create(
            sched_id=self.id,
            sched_type=self.sched_type,
            launch_date=self.launch_date,
            interval_type=self.interval_type,
            interval_number=self.interval_number,
            last_updated_by=self.updated_by,
            messages='',
        )
        return h


class SchedulerHistory(models.Model):
    sched_id = models.IntegerField()
    sched_type = models.CharField(max_length=255)
    launch_date = models.DateField()
    interval_type = models.CharField(max_length=255)
    interval_number = models.IntegerField(blank=True, null=True)

    last_updated_by = models.ForeignKey('users.Actor', related_name='+', on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    messages = models.TextField()

    def add_message(self, message):
        if self.messages:
            self.messages = f'{self.messages};{message}'
        else:
            self.messages = message
        self.save()


class ScheduleError(Exception):
    pass


class ScheduleQuerySet(models.QuerySet):
    def filter_ready_to_execute(self, execute_date: datetime):
        return self.filter(active=True, running=False, execute_date__lte=execute_date)


class Schedule(models.Model):
    name = models.CharField(max_length=255, unique=True, null=True, blank=True, db_index=True)
    active = models.BooleanField(default=True)
    start_date = models.DateTimeField(null=True, blank=True)
    end_date = models.DateTimeField(null=True, blank=True)
    execute_date = models.DateTimeField()
    recurrence_rule_data = models.JSONField(null=True)

    params = JSONField(null=True, blank=True)

    running = models.BooleanField(default=False)
    run_id = models.PositiveIntegerField(default=0)

    created_by = models.ForeignKey(
        'users.Actor', null=True, blank=True, default=get_actor_for_current_user, related_name='+', on_delete=models.RESTRICT
    )
    updated_by = models.ForeignKey(
        'users.Actor', null=True, blank=True, default=get_actor_for_current_user, related_name='+', on_delete=models.RESTRICT
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = models.Manager.from_queryset(ScheduleQuerySet)()

    class Meta:
        ordering = ('-id',)

    def __str__(self):
        return f"Schedule:{self.id}:{self.name}"

    @property
    def recurrence_rule(self) -> RecurrenceRule | None:
        if self.recurrence_rule_data is None:
            return None

        return RecurrenceRule.from_data(**self.recurrence_rule_data)

    @recurrence_rule.setter
    def recurrence_rule(self, value: RecurrenceRule | None):
        if value is None:
            self.recurrence_rule_data = None
        else:
            self.recurrence_rule_data = asdict(value.to_data())

    def update_recurrence_rule(self, recurrence_rule: RecurrenceRule):
        self.recurrence_rule = recurrence_rule
        execute_date = self.get_next_execution_date(now())
        if execute_date:
            self.execute_date = execute_date
        else:
            self.active = False

    def update_params(self, execute_function: Any | None = None, execute_params: Any | None = None):
        if execute_function is not None:
            self.params['execute_function'] = execute_function

        if execute_params is not None:
            self.params['execute_params'] = execute_params

    def update_execute_date(self, execute_date: datetime):
        if self.recurrence_rule:
            raise ScheduleError(f"Cannot change execute date for recurring schedule {self.id} '{self.name}'")

        self.execute_date = execute_date
        self.start_date = execute_date

    def get_first_execution_date(self) -> datetime | None:
        return self.get_next_execution_date(self.start_date, include_after_date=True)

    def get_next_execution_date(self, after_date, include_after_date=False) -> datetime | None:
        if self.recurrence_rule_data is None:
            return None

        next_execution_date = self.recurrence_rule.replace(start_date=self.start_date).next_recurrence(
            after_date, include_after_date, until=self.end_date
        )
        if not next_execution_date:
            return None

        return next_execution_date.replace(tzinfo=UTC)

    def move_schedule(self):
        if not self.active:
            raise ScheduleError(f"Schedule {self.id} '{self.name}' is inactive")

        if not self.recurrence_rule_data:
            self.active = False
        else:
            execute_date = self.get_next_execution_date(self.execute_date)
            if not execute_date:
                self.active = False
            else:
                self.execute_date = execute_date
                self.activate_schedule()

        self.save(update_fields=['active', 'execute_date', 'run_id'])

    def activate_schedule(self):
        self.active = True
        if ScheduleRun.objects.filter(schedule=self.id, run_id=self.run_id).exists():
            self._prepare_next_run()

    def _prepare_next_run(self):
        # whatever is needed for the next run
        self.run_id += 1

    def run(self):
        self.running = True
        self.save(update_fields=['running'])

    def finish(self):
        self.running = False
        self.save(update_fields=['running'])

    def deactivate(self):
        self.active = False
        self.save(update_fields=['active'])

    def retry(self, delay: int):
        self.execute_date = self.execute_date + relativedelta(seconds=+delay)
        self.save(update_fields=['execute_date'])


class ScheduleRun(models.Model):
    schedule = models.ForeignKey(Schedule, related_name="runs", on_delete=models.CASCADE)
    run_id = models.PositiveIntegerField(default=0)
    executed_at = models.DateTimeField()
    scheduled_for = models.DateTimeField()
    finished_at = models.DateTimeField(null=True, blank=True)
    success = models.BooleanField(default=False)
    result = models.JSONField(null=True, encoder=DjangoJSONEncoder)
    messages = models.TextField()

    class Meta:
        ordering = ['-executed_at']

    def __str__(self):
        return f"ScheduleRun:{self.id}"

    def add_message(self, message):
        if self.messages:
            self.messages = f'{self.messages};{message}'
        else:
            self.messages = message
        self.save()

    def finish(self, success: bool, result: Any):
        # As the result comes from an external source we must be sure it's serializable to prevent any errors when saving the data.
        # In case the serialization fails we just save the string representation of the result.
        try:
            if is_dataclass(result):
                result = asdict(result)
            elif isinstance(result, BaseModel):
                result = result.model_dump()

            DjangoJSONEncoder().encode(result)
        except TypeError as ex:
            logger.exception(str(ex))
            result = str(result)

        self.success = success
        self.finished_at = now()
        self.result = result
        self.save(update_fields=['success', 'finished_at', 'result'])
