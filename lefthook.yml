pre-commit:
  parallel: true
  commands:
    eslint:
      glob: "*.{ts,tsx}"
      root: "pipeline/static/"
      run: yarn eslint {staged_files} --fix --config .eslintrc-pre-commit.js
      stage_fixed: true
      tags:
        - frontend
    organize-imports:
      glob: "*.{ts,tsx}"
      root: "pipeline/static/"
      run: yarn biome check --write {staged_files}
      stage_fixed: true
      tags:
        - frontend
    prettier:
      glob: "*.{ts,tsx}"
      root: "pipeline/static/"
      run: yarn prettier {staged_files} --write
      stage_fixed: true
      tags:
        - frontend
    typescript-react:
      glob: "pipeline/static/packages/react/**/*.{ts,tsx}"
      root: "pipeline/static/"
      run: yarn tsc --noEmit -p packages/react/tsconfig.json
    check-yaml:
      glob: "*.{yaml,yml}"
      root: "pipeline/"
      run: |
        yamllint -d "{extends: default, rules: { line-length: { max: 400 } } }" {staged_files}
      stage_fixed: true
      tags:
        - backend
    ruff:
      glob: "*.py"
      exclude: "migrations/"
      run: poetry run ruff check --fix-only {staged_files}
      stage_fixed: true
      tags:
        - backend
    ruff-format:
      glob: "*.py"
      exclude: "migrations/"
      run: poetry run ruff format {staged_files}
      stage_fixed: true
      tags:
        - backend
